window.send_to_editor=function(a){var b,c="undefined"!=typeof tinymce,d="undefined"!=typeof QTags;if(wpActiveEditor)c&&(b=tinymce.get(wpActiveEditor));else if(c&&tinymce.activeEditor)b=tinymce.activeEditor,window.wpActiveEditor=b.id;else if(!d)return!1;if(b&&!b.isHidden()?b.execCommand("mceInsertContent",!1,a):d?QTags.insertContent(a):document.getElementById(wpActiveEditor).value+=a,window.tb_remove)try{window.tb_remove()}catch(e){}},function(a){window.tb_position=function(){var b=a("#TB_window"),c=a(window).width(),d=a(window).height(),e=833<c?833:c,f=0;return a("#wpadminbar").length&&(f=parseInt(a("#wpadminbar").css("height"),10)),b.length&&(b.width(e-50).height(d-45-f),a("#TB_iframeContent").width(e-50).height(d-75-f),b.css({"margin-left":"-"+parseInt((e-50)/2,10)+"px"}),"undefined"!=typeof document.body.style.maxWidth&&b.css({top:20+f+"px","margin-top":"0"})),a("a.thickbox").each(function(){var b=a(this).attr("href");b&&(b=b.replace(/&width=[0-9]+/g,""),b=b.replace(/&height=[0-9]+/g,""),a(this).attr("href",b+"&width="+(e-80)+"&height="+(d-85-f)))})},a(window).resize(function(){tb_position()})}(jQuery);