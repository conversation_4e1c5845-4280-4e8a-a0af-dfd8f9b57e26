jQuery(document).ready(function(a){var b,c,d,e=!1;a("#link_name").focus(),postboxes.add_postbox_toggles("link"),a("#category-tabs a").click(function(){var b=a(this).attr("href");return a(this).parent().addClass("tabs").siblings("li").removeClass("tabs"),a(".tabs-panel").hide(),a(b).show(),"#categories-all"==b?deleteUserSetting("cats"):setUserSetting("cats","pop"),!1}),getUserSetting("cats")&&a('#category-tabs a[href="#categories-pop"]').click(),b=a("#newcat").one("focus",function(){a(this).val("").removeClass("form-input-tip")}),a("#link-category-add-submit").click(function(){b.focus()}),c=function(){if(!e){e=!0;var b=a(this),c=b.is(":checked"),d=b.val().toString();a("#in-link-category-"+d+", #in-popular-link_category-"+d).prop("checked",c),e=!1}},d=function(b,d){a(d.what+" response_data",b).each(function(){var b=a(a(this).text());b.find("label").each(function(){var b,d=a(this),e=d.find("input").val(),f=d.find("input")[0].id,g=a.trim(d.text());a("#"+f).change(c),b=a('<option value="'+parseInt(e,10)+'"></option>').text(g)})})},a("#categorychecklist").wpList({alt:"",what:"link-category",response:"category-ajax-response",addAfter:d}),a('a[href="#categories-all"]').click(function(){deleteUserSetting("cats")}),a('a[href="#categories-pop"]').click(function(){setUserSetting("cats","pop")}),"pop"==getUserSetting("cats")&&a('a[href="#categories-pop"]').click(),a("#category-add-toggle").click(function(){return a(this).parents("div:first").toggleClass("wp-hidden-children"),a('#category-tabs a[href="#categories-all"]').click(),a("#newcategory").focus(),!1}),a(".categorychecklist :checkbox").change(c).filter(":checked").change()});