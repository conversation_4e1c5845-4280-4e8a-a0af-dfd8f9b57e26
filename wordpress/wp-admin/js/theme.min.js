window.wp=window.wp||{},function(a){function b(a,b){var c=this;Backbone.history._hasPushState&&Backbone.Router.prototype.navigate.call(c,a,b)}var c,d;c=wp.themes=wp.themes||{},c.data=_wpThemeSettings,d=c.data.l10n,c.isInstall=!!c.data.settings.isInstall,_.extend(c,{model:{},view:{},routes:{},router:{},template:wp.template}),c.Model=Backbone.Model.extend({initialize:function(){var a;_.indexOf(c.data.installedThemes,this.get("slug"))!==-1&&this.set({installed:!0}),this.set({id:this.get("slug")||this.get("id")}),this.has("sections")&&(a=this.get("sections").description,this.set({description:a}))}}),c.view.Appearance=wp.Backbone.View.extend({el:"#wpbody-content .wrap .theme-browser",window:a(window),page:0,initialize:function(a){_.bindAll(this,"scroller"),this.SearchView=a.SearchView?a.SearchView:c.view.Search,this.window.bind("scroll",_.throttle(this.scroller,300))},render:function(){this.view=new c.view.Themes({collection:this.collection,parent:this}),this.search(),this.$el.removeClass("search-loading"),this.view.render(),this.$el.empty().append(this.view.el).addClass("rendered")},searchContainer:a(".search-form"),search:function(){var b,e=this;1!==c.data.themes.length&&(b=new this.SearchView({collection:e.collection,parent:this}),e.SearchView=b,b.render(),this.searchContainer.append(a.parseHTML('<label class="screen-reader-text" for="wp-filter-search-input">'+d.search+"</label>")).append(b.el).on("submit",function(a){a.preventDefault()}))},scroller:function(){var a,b,c=this;a=this.window.scrollTop()+c.window.height(),b=c.$el.offset().top+c.$el.outerHeight(!1)-c.window.height(),b=Math.round(.9*b),a>b&&this.trigger("theme:scroll")}}),c.Collection=Backbone.Collection.extend({model:c.Model,terms:"",doSearch:function(b){this.terms!==b&&(this.terms=b,this.terms.length>0&&this.search(this.terms),""===this.terms&&(this.reset(c.data.themes),a("body").removeClass("no-results")),this.trigger("themes:update"))},search:function(b){var d,e,f,g,h,i;this.reset(c.data.themes,{silent:!0}),b=b.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),b=b.replace(/ /g,")(?=.*"),d=new RegExp("^(?=.*"+b+").+","i"),e=this.filter(function(a){return g=a.get("name").replace(/(<([^>]+)>)/gi,""),h=a.get("description").replace(/(<([^>]+)>)/gi,""),i=a.get("author").replace(/(<([^>]+)>)/gi,""),f=_.union([g,a.get("id"),h,i,a.get("tags")]),d.test(a.get("author"))&&b.length>2&&a.set("displayAuthor",!0),d.test(f)}),0===e.length?this.trigger("query:empty"):a("body").removeClass("no-results"),this.reset(e)},paginate:function(a){var b=this;return a=a||0,b=_(b.rest(20*a)),b=_(b.first(20))},count:!1,query:function(b){var c,d,e,f=this.queries,g=this;if(this.currentQuery.request=b,c=_.find(f,function(a){return _.isEqual(a.request,b)}),d=_.has(b,"page"),d||(this.currentQuery.page=1),c||d){if(d)return this.apiCall(b,d).done(function(a){g.add(a.themes),g.trigger("query:success"),g.loadingThemes=!1}).fail(function(){g.trigger("query:fail")});0===c.themes.length?g.trigger("query:empty"):a("body").removeClass("no-results"),_.isNumber(c.total)&&(this.count=c.total),this.reset(c.themes),c.total||(this.count=this.length),this.trigger("themes:update"),this.trigger("query:success",this.count)}else c=this.apiCall(b).done(function(a){a.themes&&(g.reset(a.themes),e=a.info.results,f.push({themes:a.themes,request:b,total:e})),g.trigger("themes:update"),g.trigger("query:success",e),a.themes&&0===a.themes.length&&g.trigger("query:empty")}).fail(function(){g.trigger("query:fail")})},queries:[],currentQuery:{page:1,request:{}},apiCall:function(b,c){return wp.ajax.send("query-themes",{data:{request:_.extend({per_page:100},b)},beforeSend:function(){c||a("body").addClass("loading-content").removeClass("no-results")}})},loadingThemes:!1}),c.view.Theme=wp.Backbone.View.extend({className:"theme",state:"grid",html:c.template("theme"),events:{click:c.isInstall?"preview":"expand",keydown:c.isInstall?"preview":"expand",touchend:c.isInstall?"preview":"expand",keyup:"addFocus",touchmove:"preventExpand","click .theme-install":"installTheme","click .update-message":"updateTheme"},touchDrag:!1,initialize:function(){this.model.on("change",this.render,this)},render:function(){var a=this.model.toJSON();this.$el.html(this.html(a)).attr({tabindex:0,"aria-describedby":a.id+"-action "+a.id+"-name","data-slug":a.id}),this.activeTheme(),this.model.get("displayAuthor")&&this.$el.addClass("display-author")},activeTheme:function(){this.model.get("active")&&this.$el.addClass("active")},addFocus:function(){var b=a(":focus").hasClass("theme")?a(":focus"):a(":focus").parents(".theme");a(".theme.focus").removeClass("focus"),b.addClass("focus")},expand:function(b){var d=this;if(b=b||window.event,"keydown"!==b.type||13===b.which||32===b.which)return this.touchDrag===!0?this.touchDrag=!1:void(a(b.target).is(".theme-actions a")||a(b.target).is(".theme-actions a, .update-message, .button-link, .notice-dismiss")||(c.focusedTheme=this.$el,this.trigger("theme:expand",d.model.cid)))},preventExpand:function(){this.touchDrag=!0},preview:function(b){var d,e,f=this;return b=b||window.event,this.touchDrag===!0?this.touchDrag=!1:void(a(b.target).not(".install-theme-preview").parents(".theme-actions").length||"keydown"===b.type&&13!==b.which&&32!==b.which||"keydown"===b.type&&13!==b.which&&a(":focus").hasClass("button")||(b.preventDefault(),b=b||window.event,c.focusedTheme=this.$el,c.preview=e=new c.view.Preview({model:this.model}),e.render(),this.setNavButtonsState(),1===this.model.collection.length?e.$el.addClass("no-navigation"):e.$el.removeClass("no-navigation"),a("div.wrap").append(e.el),this.listenTo(e,"theme:next",function(){return d=f.model,_.isUndefined(f.current)||(d=f.current),f.current=f.model.collection.at(f.model.collection.indexOf(d)+1),_.isUndefined(f.current)?(f.options.parent.parent.trigger("theme:end"),f.current=d):(e.model=f.current,e.render(),this.setNavButtonsState(),void a(".next-theme").focus())}).listenTo(e,"theme:previous",function(){d=f.model,0!==f.model.collection.indexOf(f.current)&&(_.isUndefined(f.current)||(d=f.current),f.current=f.model.collection.at(f.model.collection.indexOf(d)-1),_.isUndefined(f.current)||(e.model=f.current,e.render(),this.setNavButtonsState(),a(".previous-theme").focus()))}),this.listenTo(e,"preview:close",function(){f.current=f.model})))},setNavButtonsState:function(){var b=a(".theme-install-overlay"),c=_.isUndefined(this.current)?this.model:this.current,d=b.find(".previous-theme"),e=b.find(".next-theme");0===this.model.collection.indexOf(c)&&(d.addClass("disabled").prop("disabled",!0),e.focus()),_.isUndefined(this.model.collection.at(this.model.collection.indexOf(c)+1))&&(e.addClass("disabled").prop("disabled",!0),d.focus())},installTheme:function(b){var c=this;b.preventDefault(),wp.updates.maybeRequestFilesystemCredentials(b),a(document).on("wp-theme-install-success",function(a,b){c.model.get("id")===b.slug&&c.model.set({installed:!0})}),wp.updates.installTheme({slug:a(b.target).data("slug")})},updateTheme:function(b){var c=this;this.model.get("hasPackage")&&(b.preventDefault(),wp.updates.maybeRequestFilesystemCredentials(b),a(document).on("wp-theme-update-success",function(a,b){c.model.off("change",c.render,c),c.model.get("id")===b.slug&&c.model.set({hasUpdate:!1,version:b.newVersion}),c.model.on("change",c.render,c)}),wp.updates.updateTheme({slug:a(b.target).parents("div.theme").first().data("slug")}))}}),c.view.Details=wp.Backbone.View.extend({className:"theme-overlay",events:{click:"collapse","click .delete-theme":"deleteTheme","click .left":"previousTheme","click .right":"nextTheme","click #update-theme":"updateTheme"},html:c.template("theme-single"),render:function(){var a=this.model.toJSON();this.$el.html(this.html(a)),this.activeTheme(),this.navigation(),this.screenshotCheck(this.$el),this.containFocus(this.$el)},activeTheme:function(){this.$el.toggleClass("active",this.model.get("active"))},containFocus:function(b){_.delay(function(){a(".theme-overlay").focus()},100),b.on("keydown.wp-themes",function(a){var c=b.find(".theme-header button:not(.disabled)").first(),d=b.find(".theme-actions a:visible").last();9===a.which&&(c[0]===a.target&&a.shiftKey?(d.focus(),a.preventDefault()):d[0]!==a.target||a.shiftKey||(c.focus(),a.preventDefault()))})},collapse:function(b){var d,e=this;b=b||window.event,1!==c.data.themes.length&&(a(b.target).is(".theme-backdrop")||a(b.target).is(".close")||27===b.keyCode)&&(a("body").addClass("closing-overlay"),this.$el.fadeOut(130,function(){a("body").removeClass("closing-overlay"),e.closeOverlay(),d=document.body.scrollTop,c.router.navigate(c.router.baseUrl("")),document.body.scrollTop=d,c.focusedTheme&&c.focusedTheme.focus()}))},navigation:function(){this.model.cid===this.model.collection.at(0).cid&&this.$el.find(".left").addClass("disabled").prop("disabled",!0),this.model.cid===this.model.collection.at(this.model.collection.length-1).cid&&this.$el.find(".right").addClass("disabled").prop("disabled",!0)},closeOverlay:function(){a("body").removeClass("modal-open"),this.remove(),this.unbind(),this.trigger("theme:collapse")},updateTheme:function(b){var c=this;b.preventDefault(),wp.updates.maybeRequestFilesystemCredentials(b),a(document).on("wp-theme-update-success",function(a,b){c.model.get("id")===b.slug&&c.model.set({hasUpdate:!1,version:b.newVersion}),c.render()}),wp.updates.updateTheme({slug:a(b.target).data("slug")})},deleteTheme:function(b){var d=this,e=d.model.collection,f=c;b.preventDefault(),window.confirm(wp.themes.data.settings.confirmDelete)&&(wp.updates.maybeRequestFilesystemCredentials(b),a(document).one("wp-theme-delete-success",function(b,c){d.$el.find(".close").trigger("click"),a('[data-slug="'+c.slug+'"]').css({backgroundColor:"#faafaa"}).fadeOut(350,function(){a(this).remove(),f.data.themes=_.without(f.data.themes,_.findWhere(f.data.themes,{id:c.slug})),a(".wp-filter-search").val(""),e.doSearch(""),e.remove(d.model),e.trigger("themes:update")})}),wp.updates.deleteTheme({slug:this.model.get("id")}))},nextTheme:function(){var a=this;return a.trigger("theme:next",a.model.cid),!1},previousTheme:function(){var a=this;return a.trigger("theme:previous",a.model.cid),!1},screenshotCheck:function(a){var b,c;b=a.find(".screenshot img"),c=new Image,c.src=b.attr("src"),c.width&&c.width<=300&&a.addClass("small-screenshot")}}),c.view.Preview=c.view.Details.extend({className:"wp-full-overlay expanded",el:".theme-install-overlay",events:{"click .close-full-overlay":"close","click .collapse-sidebar":"collapse","click .devices button":"previewDevice","click .previous-theme":"previousTheme","click .next-theme":"nextTheme",keyup:"keyEvent","click .theme-install":"installTheme"},html:c.template("theme-preview"),render:function(){var b,d=this,e=this.model.toJSON(),f=a(document.body);f.attr("aria-busy","true"),this.$el.removeClass("iframe-ready").html(this.html(e)),b=this.$el.data("current-preview-device"),b&&d.tooglePreviewDeviceButtons(b),c.router.navigate(c.router.baseUrl(c.router.themePath+this.model.get("id")),{replace:!1}),this.$el.fadeIn(200,function(){f.addClass("theme-installer-active full-overlay-active")}),this.$el.find("iframe").one("load",function(){d.iframeLoaded()})},iframeLoaded:function(){this.$el.addClass("iframe-ready"),a(document.body).attr("aria-busy","false")},close:function(){return this.$el.fadeOut(200,function(){a("body").removeClass("theme-installer-active full-overlay-active"),c.focusedTheme&&c.focusedTheme.focus()}).removeClass("iframe-ready"),c.router.selectedTab?(c.router.navigate(c.router.baseUrl("?browse="+c.router.selectedTab)),c.router.selectedTab=!1):c.router.navigate(c.router.baseUrl("")),this.trigger("preview:close"),this.undelegateEvents(),this.unbind(),!1},collapse:function(b){var c=a(b.currentTarget);return"true"===c.attr("aria-expanded")?c.attr({"aria-expanded":"false","aria-label":d.expandSidebar}):c.attr({"aria-expanded":"true","aria-label":d.collapseSidebar}),this.$el.toggleClass("collapsed").toggleClass("expanded"),!1},previewDevice:function(b){var c=a(b.currentTarget).data("device");this.$el.removeClass("preview-desktop preview-tablet preview-mobile").addClass("preview-"+c).data("current-preview-device",c),this.tooglePreviewDeviceButtons(c)},tooglePreviewDeviceButtons:function(b){var c=a(".wp-full-overlay-footer .devices");c.find("button").removeClass("active").attr("aria-pressed",!1),c.find("button.preview-"+b).addClass("active").attr("aria-pressed",!0)},keyEvent:function(a){27===a.keyCode&&(this.undelegateEvents(),this.close()),39===a.keyCode&&_.once(this.nextTheme()),37===a.keyCode&&this.previousTheme()},installTheme:function(b){var c=this,d=a(b.target);b.preventDefault(),d.hasClass("disabled")||(wp.updates.maybeRequestFilesystemCredentials(b),a(document).on("wp-theme-install-success",function(){c.model.set({installed:!0})}),wp.updates.installTheme({slug:d.data("slug")}))}}),c.view.Themes=wp.Backbone.View.extend({className:"themes wp-clearfix",$overlay:a("div.theme-overlay"),index:0,count:a(".wrap .theme-count"),liveThemeCount:0,initialize:function(b){var c=this;this.parent=b.parent,this.setView("grid"),c.currentTheme(),this.listenTo(c.collection,"themes:update",function(){c.parent.page=0,c.currentTheme(),c.render(this)}),this.listenTo(c.collection,"query:success",function(a){_.isNumber(a)?(c.count.text(a),c.announceSearchResults(a)):(c.count.text(c.collection.length),c.announceSearchResults(c.collection.length))}),this.listenTo(c.collection,"query:empty",function(){a("body").addClass("no-results")}),this.listenTo(this.parent,"theme:scroll",function(){c.renderThemes(c.parent.page)}),this.listenTo(this.parent,"theme:close",function(){c.overlay&&c.overlay.closeOverlay()}),a("body").on("keyup",function(b){c.overlay&&(a("#request-filesystem-credentials-dialog").is(":visible")||(39===b.keyCode&&c.overlay.nextTheme(),37===b.keyCode&&c.overlay.previousTheme(),27===b.keyCode&&c.overlay.collapse(b)))})},render:function(){this.$el.empty(),1===c.data.themes.length&&(this.singleTheme=new c.view.Details({model:this.collection.models[0]}),this.singleTheme.render(),this.$el.addClass("single-theme"),this.$el.append(this.singleTheme.el)),this.options.collection.size()>0&&this.renderThemes(this.parent.page),this.liveThemeCount=this.collection.count?this.collection.count:this.collection.length,this.count.text(this.liveThemeCount),c.isInstall||this.announceSearchResults(this.liveThemeCount)},renderThemes:function(b){var e=this;return e.instance=e.collection.paginate(b),0===e.instance.size()?void this.parent.trigger("theme:end"):(!c.isInstall&&b>=1&&a(".add-new-theme").remove(),e.instance.each(function(a){e.theme=new c.view.Theme({model:a,parent:e}),e.theme.render(),e.$el.append(e.theme.el),e.listenTo(e.theme,"theme:expand",e.expand,e)}),!c.isInstall&&c.data.settings.canInstall&&this.$el.append('<div class="theme add-new-theme"><a href="'+c.data.settings.installURI+'"><div class="theme-screenshot"><span></span></div><h2 class="theme-name">'+d.addNew+"</h2></a></div>"),void this.parent.page++)},currentTheme:function(){var a,b=this;a=b.collection.findWhere({active:!0}),a&&(b.collection.remove(a),b.collection.add(a,{at:0}))},setView:function(a){return a},expand:function(b){var d,e,f=this;this.model=f.collection.get(b),c.router.navigate(c.router.baseUrl(c.router.themePath+this.model.id)),this.setView("detail"),a("body").addClass("modal-open"),this.overlay=new c.view.Details({model:f.model}),this.overlay.render(),this.model.get("hasUpdate")&&(d=a('[data-slug="'+this.model.id+'"]'),e=a(this.overlay.el),d.find(".updating-message").length?(e.find(".notice-warning h3").remove(),e.find(".notice-warning").removeClass("notice-large").addClass("updating-message").find("p").text(wp.updates.l10n.updating)):d.find(".notice-error").length&&e.find(".notice-warning").remove()),this.$overlay.html(this.overlay.el),this.listenTo(this.overlay,"theme:next",function(){f.next([f.model.cid])}).listenTo(this.overlay,"theme:previous",function(){f.previous([f.model.cid])})},next:function(a){var b,c,d=this;b=d.collection.get(a[0]),c=d.collection.at(d.collection.indexOf(b)+1),void 0!==c&&(this.overlay.closeOverlay(),d.theme.trigger("theme:expand",c.cid))},previous:function(a){var b,c,d=this;b=d.collection.get(a[0]),c=d.collection.at(d.collection.indexOf(b)-1),void 0!==c&&(this.overlay.closeOverlay(),d.theme.trigger("theme:expand",c.cid))},announceSearchResults:function(a){0===a?wp.a11y.speak(d.noThemesFound):wp.a11y.speak(d.themesFound.replace("%d",a))}}),c.view.Search=wp.Backbone.View.extend({tagName:"input",className:"wp-filter-search",id:"wp-filter-search-input",searching:!1,attributes:{placeholder:d.searchPlaceholder,type:"search","aria-describedby":"live-search-desc"},events:{input:"search",keyup:"search",blur:"pushState"},initialize:function(a){this.parent=a.parent,this.listenTo(this.parent,"theme:close",function(){this.searching=!1})},search:function(a){"keyup"===a.type&&27===a.which&&(a.target.value=""),this.doSearch(a)},doSearch:function(a){var b={};this.collection.doSearch(a.target.value.replace(/\+/g," ")),this.searching&&13!==a.which?b.replace=!0:this.searching=!0,a.target.value?c.router.navigate(c.router.baseUrl(c.router.searchPath+a.target.value),b):c.router.navigate(c.router.baseUrl(""))},pushState:function(a){var b=c.router.baseUrl("");a.target.value&&(b=c.router.baseUrl(c.router.searchPath+encodeURIComponent(a.target.value))),this.searching=!1,c.router.navigate(b)}}),c.Router=Backbone.Router.extend({routes:{"themes.php?theme=:slug":"theme","themes.php?search=:query":"search","themes.php?s=:query":"search","themes.php":"themes","":"themes"},baseUrl:function(a){return"themes.php"+a},themePath:"?theme=",searchPath:"?search=",search:function(b){a(".wp-filter-search").val(b.replace(/\+/g," "))},themes:function(){a(".wp-filter-search").val("")},navigate:b}),c.Run={init:function(){this.themes=new c.Collection(c.data.themes),this.view=new c.view.Appearance({collection:this.themes}),this.render(),this.view.SearchView.doSearch=_.debounce(this.view.SearchView.doSearch,500)},render:function(){this.view.render(),this.routes(),Backbone.History.started&&Backbone.history.stop(),Backbone.history.start({root:c.data.settings.adminUrl,pushState:!0,hashChange:!1})},routes:function(){var b=this;c.router=new c.Router,c.router.on("route:theme",function(a){b.view.view.expand(a)}),c.router.on("route:themes",function(){b.themes.doSearch(""),b.view.trigger("theme:close")}),c.router.on("route:search",function(){a(".wp-filter-search").trigger("keyup")}),this.extraRoutes()},extraRoutes:function(){return!1}},c.view.InstallerSearch=c.view.Search.extend({events:{input:"search",keyup:"search"},terms:"",search:function(a){("keyup"!==a.type||9!==a.which&&16!==a.which)&&(this.collection=this.options.parent.view.collection,"keyup"===a.type&&27===a.which&&(a.target.value=""),this.doSearch(a.target.value))},doSearch:function(b){var d={};this.terms!==b&&(this.terms=b,d.search=b,"author:"===b.substring(0,7)&&(d.search="",d.author=b.slice(7)),"tag:"===b.substring(0,4)&&(d.search="",d.tag=[b.slice(4)]),a(".filter-links li > a.current").removeClass("current").removeAttr("aria-current"),a("body").removeClass("show-filters filters-applied show-favorites-form"),a(".drawer-toggle").attr("aria-expanded","false"),this.collection.query(d),c.router.navigate(c.router.baseUrl(c.router.searchPath+encodeURIComponent(b)),{replace:!0}))}}),c.view.Installer=c.view.Appearance.extend({el:"#wpbody-content .wrap",events:{"click .filter-links li > a":"onSort","click .theme-filter":"onFilter","click .drawer-toggle":"moreFilters","click .filter-drawer .apply-filters":"applyFilters",'click .filter-group [type="checkbox"]':"addFilter","click .filter-drawer .clear-filters":"clearFilters","click .edit-filters":"backToFilters","click .favorites-form-submit":"saveUsername","keyup #wporg-username-input":"saveUsername"},render:function(){var b=this;this.search(),this.uploader(),this.collection=new c.Collection,this.listenTo(this,"theme:end",function(){b.collection.loadingThemes||(b.collection.loadingThemes=!0,b.collection.currentQuery.page++,_.extend(b.collection.currentQuery.request,{page:b.collection.currentQuery.page}),b.collection.query(b.collection.currentQuery.request))}),this.listenTo(this.collection,"query:success",function(){a("body").removeClass("loading-content"),a(".theme-browser").find("div.error").remove()}),this.listenTo(this.collection,"query:fail",function(){a("body").removeClass("loading-content"),a(".theme-browser").find("div.error").remove(),a(".theme-browser").find("div.themes").before('<div class="error"><p>'+d.error+'</p><p><button class="button try-again">'+d.tryAgain+"</button></p></div>"),a(".theme-browser .error .try-again").on("click",function(b){b.preventDefault(),a("input.wp-filter-search").trigger("input")})}),this.view&&this.view.remove(),this.view=new c.view.Themes({collection:this.collection,parent:this}),this.page=0,this.$el.find(".themes").remove(),this.view.render(),this.$el.find(".theme-browser").append(this.view.el).addClass("rendered")},browse:function(a){this.collection.query({browse:a})},onSort:function(b){var d=a(b.target),e=d.data("sort");b.preventDefault(),a("body").removeClass("filters-applied show-filters"),a(".drawer-toggle").attr("aria-expanded","false"),d.hasClass(this.activeClass)||(this.sort(e),c.router.navigate(c.router.baseUrl(c.router.browsePath+e)))},sort:function(b){this.clearSearch(),c.router.selectedTab=b,a(".filter-links li > a, .theme-filter").removeClass(this.activeClass).removeAttr("aria-current"),a('[data-sort="'+b+'"]').addClass(this.activeClass).attr("aria-current","page"),"favorites"===b?a("body").addClass("show-favorites-form"):a("body").removeClass("show-favorites-form"),this.browse(b)},onFilter:function(b){var c,d=a(b.target),e=d.data("filter");d.hasClass(this.activeClass)||(a(".filter-links li > a, .theme-section").removeClass(this.activeClass).removeAttr("aria-current"),d.addClass(this.activeClass).attr("aria-current","page"),e&&(e=_.union([e,this.filtersChecked()]),c={tag:[e]},this.collection.query(c)))},addFilter:function(){this.filtersChecked()},applyFilters:function(b){var c,e=this.filtersChecked(),f={tag:e},g=a(".filtered-by .tags");return b&&b.preventDefault(),e?(a("body").addClass("filters-applied"),a(".filter-links li > a.current").removeClass("current").removeAttr("aria-current"),g.empty(),_.each(e,function(b){c=a('label[for="filter-id-'+b+'"]').text(),g.append('<span class="tag">'+c+"</span>")}),void this.collection.query(f)):void wp.a11y.speak(d.selectFeatureFilter)},saveUsername:function(b){var c=a("#wporg-username-input").val(),d=a("#wporg-username-nonce").val(),e={browse:"favorites",user:c},f=this;if(b&&b.preventDefault(),"keyup"!==b.type||13===b.which)return wp.ajax.send("save-wporg-username",{data:{_wpnonce:d,username:c},success:function(){f.collection.query(e)}})},filtersChecked:function(){var b=a(".filter-group").find(":checkbox"),c=[];return _.each(b.filter(":checked"),function(b){c.push(a(b).prop("value"))}),0===c.length?(a(".filter-drawer .apply-filters").find("span").text(""),a(".filter-drawer .clear-filters").hide(),a("body").removeClass("filters-applied"),!1):(a(".filter-drawer .apply-filters").find("span").text(c.length),a(".filter-drawer .clear-filters").css("display","inline-block"),c)},activeClass:"current",uploader:function(){var b=a(".upload-view-toggle"),c=a(document.body);b.on("click",function(){c.toggleClass("show-upload-view"),b.attr("aria-expanded",c.hasClass("show-upload-view"))})},moreFilters:function(b){var d=a("body"),e=a(".drawer-toggle");return b.preventDefault(),d.hasClass("filters-applied")?this.backToFilters():(this.clearSearch(),c.router.navigate(c.router.baseUrl("")),d.toggleClass("show-filters"),void e.attr("aria-expanded",d.hasClass("show-filters")))},clearFilters:function(b){var c=a(".filter-group").find(":checkbox"),d=this;b.preventDefault(),_.each(c.filter(":checked"),function(b){return a(b).prop("checked",!1),d.filtersChecked()})},backToFilters:function(b){b&&b.preventDefault(),a("body").removeClass("filters-applied")},clearSearch:function(){a("#wp-filter-search-input").val("")}}),c.InstallerRouter=Backbone.Router.extend({routes:{"theme-install.php?theme=:slug":"preview","theme-install.php?browse=:sort":"sort","theme-install.php?search=:query":"search","theme-install.php":"sort"},baseUrl:function(a){return"theme-install.php"+a},themePath:"?theme=",browsePath:"?browse=",searchPath:"?search=",search:function(b){a(".wp-filter-search").val(b.replace(/\+/g," "))},navigate:b}),c.RunInstaller={init:function(){this.view=new c.view.Installer({section:"featured",SearchView:c.view.InstallerSearch}),this.render(),this.view.SearchView.doSearch=_.debounce(this.view.SearchView.doSearch,500)},render:function(){this.view.render(),this.routes(),Backbone.History.started&&Backbone.history.stop(),Backbone.history.start({root:c.data.settings.adminUrl,pushState:!0,hashChange:!1})},routes:function(){var b=this,d={};c.router=new c.InstallerRouter,c.router.on("route:preview",function(e){c.preview&&(c.preview.undelegateEvents(),c.preview.unbind()),b.view.view.theme&&b.view.view.theme.preview?(b.view.view.theme.model=b.view.collection.findWhere({slug:e}),b.view.view.theme.preview()):(d.theme=e,b.view.collection.query(d),b.view.collection.trigger("update"),b.view.collection.once("query:success",function(){a('div[data-slug="'+e+'"]').trigger("click")}))}),c.router.on("route:sort",function(a){a||(a="featured",c.router.navigate(c.router.baseUrl("?browse=featured"),{replace:!0})),b.view.sort(a),c.preview&&c.preview.close()}),c.router.on("route:search",function(){a(".wp-filter-search").focus().trigger("keyup")}),this.extraRoutes()},extraRoutes:function(){return!1}},a(document).ready(function(){c.isInstall?c.RunInstaller.init():c.Run.init(),a(document.body).on("click",".load-customize",function(){var b=a(this),c=document.createElement("a");c.href=b.prop("href"),c.search=a.param(_.extend(wp.customize.utils.parseQueryString(c.search.substr(1)),{"return":window.location.href})),b.prop("href",c.href)}),a(".broken-themes .delete-theme").on("click",function(){return confirm(_wpThemeSettings.settings.confirmDelete)})})}(jQuery),jQuery(document).ready(function(a){window.tb_position=function(){var b=a("#TB_window"),c=a(window).width(),d=a(window).height(),e=1040<c?1040:c,f=0;a("#wpadminbar").length&&(f=parseInt(a("#wpadminbar").css("height"),10)),b.size()&&(b.width(e-50).height(d-45-f),a("#TB_iframeContent").width(e-50).height(d-75-f),b.css({"margin-left":"-"+parseInt((e-50)/2,10)+"px"}),"undefined"!=typeof document.body.style.maxWidth&&b.css({top:20+f+"px","margin-top":"0"}))},a(window).resize(function(){tb_position()})});