jQuery(document).ready(function(a){a("#link_rel").prop("readonly",!0),a("#linkxfndiv input").bind("click keyup",function(){var b=a("#me").is(":checked"),c="";a("input.valinp").each(function(){b?a(this).prop("disabled",!0).parent().addClass("disabled"):(a(this).removeAttr("disabled").parent().removeClass("disabled"),a(this).is(":checked")&&""!==a(this).val()&&(c+=a(this).val()+" "))}),a("#link_rel").val(b?"me":c.substr(0,c.length-1))})}),jQuery(document).ready(function(a){function b(a,b){a.children().hide(),a.children("."+b).show()}function c(a){a.removeClass("has-request-results"),a.next().hasClass("request-results")&&a.next().remove()}function d(b,d,e,f){var g="",h="request-results";c(b),f.length&&(a.each(f,function(a,b){g=g+"<li>"+b+"</li>"}),g="<ul>"+g+"</ul>"),b.addClass("has-request-results"),b.hasClass("status-request-confirmed")&&(h+=" status-request-confirmed"),b.hasClass("status-request-failed")&&(h+=" status-request-failed"),b.after(function(){return'<tr class="'+h+'"><th colspan="5"><div class="notice inline notice-alt '+d+'"><p>'+e+"</p>"+g+"</div></td></tr>"})}var e=window.privacyToolsL10n||{};a(".export-personal-data-handle").click(function(f){function g(a){b(k,"export-personal-data-success"),"undefined"!=typeof a?window.location=a:p||h(e.noExportFile)}function h(a){b(k,"export-personal-data-failed"),a&&d(l,"notice-error",e.exportError,[a])}function i(b,c){a.ajax({url:window.ajaxurl,data:{action:"wp-privacy-export-personal-data",exporter:b,id:m,page:c,security:n,sendAsEmail:p},method:"post"}).done(function(a){var d=a.data;return a.success?void(d.done?b<o?setTimeout(i(b+1,1)):g(d.url):setTimeout(i(b,c+1))):void h(a.data)}).fail(function(a,b,c){h(c)})}var j=a(this),k=j.parents(".export-personal-data"),l=j.parents("tr"),m=k.data("request-id"),n=k.data("nonce"),o=k.data("exporters-count"),p=!!k.data("send-as-email");f.preventDefault(),f.stopPropagation(),k.blur(),c(l),b(k,"export-personal-data-processing"),i(1,1)}),a(".remove-personal-data-handle").click(function(f){function g(){var a=e.noDataFound,c="notice-success";b(k,"remove-personal-data-idle"),!1===p?!1===q?a=e.noDataFound:(a=e.noneRemoved,c="notice-warning"):!1===q?a=e.foundAndRemoved:(a=e.someNotRemoved,c="notice-warning"),d(l,"notice-success",a,r)}function h(){b(k,"remove-personal-data-failed"),d(l,"notice-error",e.removalError,[])}function i(b,c){a.ajax({url:window.ajaxurl,data:{action:"wp-privacy-erase-personal-data",eraser:b,id:m,page:c,security:n},method:"post"}).done(function(a){var d=a.data;return a.success?(d.items_removed&&(p=p||d.items_removed),d.items_retained&&(q=q||d.items_retained),d.messages&&(r=r.concat(d.messages)),void(d.done?b<o?setTimeout(i(b+1,1)):g():setTimeout(i(b,c+1)))):void h()}).fail(function(){h()})}var j=a(this),k=j.parents(".remove-personal-data"),l=j.parents("tr"),m=k.data("request-id"),n=k.data("nonce"),o=k.data("erasers-count"),p=!1,q=!1,r=[];f.stopPropagation(),k.blur(),c(l),b(k,"remove-personal-data-processing"),i(1,1)})}),function(a){a(document).on("click",function(b){var c,d,e,f=a(b.target);if(f.is("button.privacy-text-copy")&&(c=f.parent().parent(),d=c.find("div.wp-suggested-text"),d.length||(d=c.find("div.policy-text")),d.length))try{window.getSelection().removeAllRanges(),e=document.createRange(),d.addClass("hide-privacy-policy-tutorial"),e.selectNodeContents(d[0]),window.getSelection().addRange(e),document.execCommand("copy"),d.removeClass("hide-privacy-policy-tutorial"),window.getSelection().removeAllRanges()}catch(g){}})}(jQuery);