jQuery(document).ready(function(a){function b(){var b=e.find("#TB_iframeContent");f=b.contents().find("body"),c(),h.focus(),a("#plugin-information-tabs a",f).on("click",function(){c()}),f.on("keydown",function(a){27===a.which&&tb_remove()})}function c(){var b;g=a(":tabbable",f),h=e.find("#TB_closeWindowButton"),i=g.last(),b=h.add(i),b.off("keydown.wp-plugin-details"),b.on("keydown.wp-plugin-details",function(a){d(a)})}function d(a){9===a.which&&(i[0]!==a.target||a.shiftKey?h[0]===a.target&&a.shiftKey&&(a.preventDefault(),i.focus()):(a.preventDefault(),h.focus()))}var e,f,g,h,i,j=a(),k=a(".upload-view-toggle"),l=a(".wrap"),m=a(document.body);window.tb_position=function(){var b=a(window).width(),c=a(window).height()-(792<b?60:20),d=792<b?772:b-20;return e=a("#TB_window"),e.length&&(e.width(d).height(c),a("#TB_iframeContent").width(d).height(c),e.css({"margin-left":"-"+parseInt(d/2,10)+"px"}),"undefined"!=typeof document.body.style.maxWidth&&e.css({top:"30px","margin-top":"0"})),a("a.thickbox").each(function(){var b=a(this).attr("href");b&&(b=b.replace(/&width=[0-9]+/g,""),b=b.replace(/&height=[0-9]+/g,""),a(this).attr("href",b+"&width="+d+"&height="+c))})},a(window).resize(function(){tb_position()}),m.on("thickbox:iframe:loaded",e,function(){e.hasClass("plugin-details-modal")&&b()}).on("thickbox:removed",function(){j.focus()}),a(".wrap").on("click",".thickbox.open-plugin-details-modal",function(b){var c=a(this).data("title")?plugininstallL10n.plugin_information+" "+a(this).data("title"):plugininstallL10n.plugin_modal_label;b.preventDefault(),b.stopPropagation(),j=a(this),tb_click.call(this),e.attr({role:"dialog","aria-label":plugininstallL10n.plugin_modal_label}).addClass("plugin-details-modal"),e.find("#TB_iframeContent").attr("title",c)}),a("#plugin-information-tabs a").click(function(b){var c=a(this).attr("name");b.preventDefault(),a("#plugin-information-tabs a.current").removeClass("current"),a(this).addClass("current"),"description"!==c&&a(window).width()<772?a("#plugin-information-content").find(".fyi").hide():a("#plugin-information-content").find(".fyi").show(),a("#section-holder div.section").hide(),a("#section-"+c).show()}),l.hasClass("plugin-install-tab-upload")||k.attr({role:"button","aria-expanded":"false"}).on("click",function(a){a.preventDefault(),m.toggleClass("show-upload-view"),k.attr("aria-expanded",m.hasClass("show-upload-view"))})});