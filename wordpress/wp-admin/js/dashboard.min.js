window.wp=window.wp||{},jQ<PERSON>y(document).ready(function(a){function b(){if(!(document.documentMode&&document.documentMode<9)){a("body").append('<div class="quick-draft-textarea-clone" style="display: none;"></div>');var b=a(".quick-draft-textarea-clone"),c=a("#content"),d=c.height(),e=a(window).height()-100;b.css({"font-family":c.css("font-family"),"font-size":c.css("font-size"),"line-height":c.css("line-height"),"padding-bottom":c.css("paddingBottom"),"padding-left":c.css("paddingLeft"),"padding-right":c.css("paddingRight"),"padding-top":c.css("paddingTop"),"white-space":"pre-wrap","word-wrap":"break-word",display:"none"}),c.on("focus input propertychange",function(){var f=a(this),g=f.val()+"&nbsp;",h=b.css("width",f.css("width")).text(g).outerHeight()+2;c.css("overflow-y","auto"),h===d||h>=e&&d>=e||(d=h>e?e:h,c.css("overflow","hidden"),f.css("height",d+"px"))})}}var c,d=a("#welcome-panel"),e=a("#wp_welcome_panel-hide");c=function(b){a.post(ajaxurl,{action:"update-welcome-panel",visible:b,welcomepanelnonce:a("#welcomepanelnonce").val()})},d.hasClass("hidden")&&e.prop("checked")&&d.removeClass("hidden"),a(".welcome-panel-close, .welcome-panel-dismiss a",d).click(function(b){b.preventDefault(),d.addClass("hidden"),c(0),a("#wp_welcome_panel-hide").prop("checked",!1)}),e.click(function(){d.toggleClass("hidden",!this.checked),c(this.checked?1:0)}),window.ajaxWidgets=["dashboard_primary"],window.ajaxPopulateWidgets=function(b){function c(b,c){var d,e=a("#"+c+" div.inside:visible").find(".widget-loading");e.length&&(d=e.parent(),setTimeout(function(){d.load(ajaxurl+"?action=dashboard-widgets&widget="+c+"&pagenow="+pagenow,"",function(){d.hide().slideDown("normal",function(){a(this).css("display","")})})},500*b))}b?(b=b.toString(),a.inArray(b,ajaxWidgets)!==-1&&c(0,b)):a.each(ajaxWidgets,c)},ajaxPopulateWidgets(),postboxes.add_postbox_toggles(pagenow,{pbshow:ajaxPopulateWidgets}),window.quickPressLoad=function(){var c,d=a("#quickpost-action");a('#quick-press .submit input[type="submit"], #quick-press .submit input[type="reset"]').prop("disabled",!1),c=a("#quick-press").submit(function(b){function d(){var b=a(".drafts ul li").first();b.css("background","#fffbe5"),setTimeout(function(){b.css("background","none")},1e3)}b.preventDefault(),a("#dashboard_quick_press #publishing-action .spinner").show(),a('#quick-press .submit input[type="submit"], #quick-press .submit input[type="reset"]').prop("disabled",!0),a.post(c.attr("action"),c.serializeArray(),function(b){a("#dashboard_quick_press .inside").html(b),a("#quick-press").removeClass("initial-form"),quickPressLoad(),d(),a("#title").focus()})}),a("#publish").click(function(){d.val("post-quickpress-publish")}),a("#title, #tags-input, #content").each(function(){var b=a(this),c=a("#"+this.id+"-prompt-text");""===this.value&&c.removeClass("screen-reader-text"),c.click(function(){a(this).addClass("screen-reader-text"),b.focus()}),b.blur(function(){""===this.value&&c.removeClass("screen-reader-text")}),b.focus(function(){c.addClass("screen-reader-text")})}),a("#quick-press").on("click focusin",function(){wpActiveEditor="content"}),b()},window.quickPressLoad(),a(".meta-box-sortables").sortable("option","containment","#wpwrap")}),jQuery(function(a){"use strict";var b,c=window.communityEventsData||{};b=window.wp.communityEvents={initialized:!1,model:null,init:function(){if(!b.initialized){var d=a("#community-events");a(".community-events-errors").attr("aria-hidden","true").removeClass("hide-if-js"),d.on("click",".community-events-toggle-location, .community-events-cancel",b.toggleLocationForm),d.on("submit",".community-events-form",function(c){var d=a.trim(a("#community-events-location").val());c.preventDefault(),d&&b.getEvents({location:d})}),c&&c.cache&&c.cache.location&&c.cache.events?b.renderEventsTemplate(c.cache,"app"):b.getEvents(),b.initialized=!0}},toggleLocationForm:function(b){var c=a(".community-events-toggle-location"),d=a(".community-events-cancel"),e=a(".community-events-form"),f=a();"object"==typeof b&&(f=a(b.target),b="true"==c.attr("aria-expanded")?"hide":"show"),"hide"===b?(c.attr("aria-expanded","false"),d.attr("aria-expanded","false"),e.attr("aria-hidden","true"),f.hasClass("community-events-cancel")&&c.focus()):(c.attr("aria-expanded","true"),d.attr("aria-expanded","true"),e.attr("aria-hidden","false"))},getEvents:function(b){var d,e=this,f=a(".community-events-form").children(".spinner");b=b||{},b._wpnonce=c.nonce,b.timezone=window.Intl?window.Intl.DateTimeFormat().resolvedOptions().timeZone:"",d=b.location?"user":"app",f.addClass("is-active"),wp.ajax.post("get-community-events",b).always(function(){f.removeClass("is-active")}).done(function(a){"no_location_available"===a.error&&(b.location?a.unknownCity=b.location:delete a.error),e.renderEventsTemplate(a,d)}).fail(function(){e.renderEventsTemplate({location:!1,error:!0},d)})},renderEventsTemplate:function(d,e){var f,g,h=/%(?:\d\$)?s/g,i=a(".community-events-toggle-location"),j=a("#community-events-location-message"),k=a(".community-events-results");g={".community-events":!0,".community-events-loading":!1,".community-events-errors":!1,".community-events-error-occurred":!1,".community-events-could-not-locate":!1,"#community-events-location-message":!1,".community-events-toggle-location":!1,".community-events-results":!1},d.location.ip?(j.text(c.l10n.attend_event_near_generic),d.events.length?(f=wp.template("community-events-event-list"),k.html(f(d))):(f=wp.template("community-events-no-upcoming-events"),k.html(f(d))),g["#community-events-location-message"]=!0,g[".community-events-toggle-location"]=!0,g[".community-events-results"]=!0):d.location.description?(f=wp.template("community-events-attend-event-near"),j.html(f(d)),d.events.length?(f=wp.template("community-events-event-list"),k.html(f(d))):(f=wp.template("community-events-no-upcoming-events"),k.html(f(d))),"user"===e&&wp.a11y.speak(c.l10n.city_updated.replace(h,d.location.description),"assertive"),g["#community-events-location-message"]=!0,g[".community-events-toggle-location"]=!0,g[".community-events-results"]=!0):d.unknownCity?(f=wp.template("community-events-could-not-locate"),a(".community-events-could-not-locate").html(f(d)),wp.a11y.speak(c.l10n.could_not_locate_city.replace(h,d.unknownCity)),g[".community-events-errors"]=!0,g[".community-events-could-not-locate"]=!0):d.error&&"user"===e?(wp.a11y.speak(c.l10n.error_occurred_please_try_again),g[".community-events-errors"]=!0,g[".community-events-error-occurred"]=!0):(j.text(c.l10n.enter_closest_city),g["#community-events-location-message"]=!0,g[".community-events-toggle-location"]=!0),_.each(g,function(b,c){a(c).attr("aria-hidden",!b)}),i.attr("aria-expanded",g[".community-events-toggle-location"]),d.location&&(d.location.ip||d.location.latitude)?(b.toggleLocationForm("hide"),"user"===e&&i.focus()):b.toggleLocationForm("show")}},a("#dashboard_primary").is(":visible")?b.init():a(document).on("postbox-toggled",function(c,d){var e=a(d);"dashboard_primary"===e.attr("id")&&e.is(":visible")&&b.init()})});