!function(a){var b=window.tagsSuggestL10n&&window.tagsSuggestL10n.tagDelimiter||",";window.array_unique_noempty=function(b){var c=[];return a.each(b,function(b,d){d=a.trim(d),d&&a.inArray(d,c)===-1&&c.push(d)}),c},window.tagBox={clean:function(a){return","!==b&&(a=a.replace(new RegExp(b,"g"),",")),a=a.replace(/\s*,\s*/g,",").replace(/,+/g,",").replace(/[,\s]+$/,"").replace(/^[,\s]+/,""),","!==b&&(a=a.replace(/,/g,b)),a},parseTags:function(c){var d=c.id,e=d.split("-check-num-")[1],f=a(c).closest(".tagsdiv"),g=f.find(".the-tags"),h=g.val().split(b),i=[];return delete h[e],a.each(h,function(b,c){c=a.trim(c),c&&i.push(c)}),g.val(this.clean(i.join(b))),this.quickClicks(f),!1},quickClicks:function(c){var d,e,f=a(".the-tags",c),g=a(".tagchecklist",c),h=a(c).attr("id");f.length&&(e=f.prop("disabled"),d=f.val().split(b),g.empty(),a.each(d,function(b,c){var d,f;c=a.trim(c),c&&(d=a("<li />").text(c),e||(f=a('<button type="button" id="'+h+"-check-num-"+b+'" class="ntdelbutton"><span class="remove-tag-icon" aria-hidden="true"></span><span class="screen-reader-text">'+window.tagsSuggestL10n.removeTerm+" "+d.html()+"</span></button>"),f.on("click keypress",function(b){"click"!==b.type&&13!==b.keyCode&&32!==b.keyCode||(13!==b.keyCode&&32!==b.keyCode||a(this).closest(".tagsdiv").find("input.newtag").focus(),tagBox.userAction="remove",tagBox.parseTags(this))}),d.prepend("&nbsp;").prepend(f)),g.append(d))}),tagBox.screenReadersMessage())},flushTags:function(c,d,e){var f,g,h,i=a(".the-tags",c),j=a("input.newtag",c);return d=d||!1,h=d?a(d).text():j.val(),"undefined"!=typeof h&&""!==h&&(f=i.val(),g=f?f+b+h:h,g=this.clean(g),g=array_unique_noempty(g.split(b)).join(b),i.val(g),this.quickClicks(c),d||j.val(""),"undefined"==typeof e&&j.focus(),!1)},get:function(b){var c=b.substr(b.indexOf("-")+1);a.post(ajaxurl,{action:"get-tagcloud",tax:c},function(d,e){0!==d&&"success"==e&&(d=a('<div id="tagcloud-'+c+'" class="the-tagcloud">'+d+"</div>"),a("a",d).click(function(){return tagBox.userAction="add",tagBox.flushTags(a("#"+c),this),!1}),a("#"+b).after(d))})},userAction:"",screenReadersMessage:function(){var a;switch(this.userAction){case"remove":a=window.tagsSuggestL10n.termRemoved;break;case"add":a=window.tagsSuggestL10n.termAdded;break;default:return}window.wp.a11y.speak(a,"assertive")},init:function(){var b=a("div.ajaxtag");a(".tagsdiv").each(function(){tagBox.quickClicks(this)}),a(".tagadd",b).click(function(){tagBox.userAction="add",tagBox.flushTags(a(this).closest(".tagsdiv"))}),a("input.newtag",b).keypress(function(b){13==b.which&&(tagBox.userAction="add",tagBox.flushTags(a(this).closest(".tagsdiv")),b.preventDefault(),b.stopPropagation())}).keypress(function(a){13==a.which&&(a.preventDefault(),a.stopPropagation())}).each(function(b,c){a(c).wpTagsSuggest()}),a("#post").submit(function(){a("div.tagsdiv").each(function(){tagBox.flushTags(this,!1,1)})}),a(".tagcloud-link").click(function(){tagBox.get(a(this).attr("id")),a(this).attr("aria-expanded","true").unbind().click(function(){a(this).attr("aria-expanded","false"===a(this).attr("aria-expanded")?"true":"false").siblings(".the-tagcloud").toggle()})})}}}(jQuery);