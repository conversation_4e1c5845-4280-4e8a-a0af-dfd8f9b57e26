!function(a){a(document).ready(function(){var b,c=a("#custom-background-image");a("#background-color").wpColorPicker({change:function(a,b){c.css("background-color",b.color.toString())},clear:function(){c.css("background-color","")}}),a('select[name="background-size"]').change(function(){c.css("background-size",a(this).val())}),a('input[name="background-position"]').change(function(){c.css("background-position",a(this).val())}),a('input[name="background-repeat"]').change(function(){c.css("background-repeat",a(this).is(":checked")?"repeat":"no-repeat")}),a('input[name="background-attachment"]').change(function(){c.css("background-attachment",a(this).is(":checked")?"scroll":"fixed")}),a("#choose-from-library-link").click(function(c){var d=a(this);return c.preventDefault(),b?void b.open():(b=wp.media.frames.customBackground=wp.media({title:d.data("choose"),library:{type:"image"},button:{text:d.data("update"),close:!1}}),b.on("select",function(){var c=b.state().get("selection").first();a.post(ajaxurl,{action:"set-background-image",attachment_id:c.id,size:"full"}).done(function(){window.location.reload()})}),void b.open())})})}(jQuery);