window.wp||(window.wp={}),wp.themePluginEditor=function(a){"use strict";var b,c;b={l10n:{lintError:{singular:"",plural:""},saveAlert:"",saveError:""},codeEditor:{},instance:null,noticeElements:{},dirty:!1,lintErrors:[]},b.init=function(c,d){b.form=c,d&&a.extend(b,d),b.noticeTemplate=wp.template("wp-file-editor-notice"),b.noticesContainer=b.form.find(".editor-notices"),b.submitButton=b.form.find(":input[name=submit]"),b.spinner=b.form.find(".submit .spinner"),b.form.on("submit",b.submit),b.textarea=b.form.find("#newcontent"),b.textarea.on("change",b.onChange),b.warning=a(".file-editor-warning"),b.docsLookUpButton=b.form.find("#docs-lookup"),b.docsLookUpList=b.form.find("#docs-list"),b.warning.length>0&&b.showWarning(),!1!==b.codeEditor&&_.defer(function(){b.initCodeEditor()}),a(b.initFileBrowser),a(window).on("beforeunload",function(){if(b.dirty)return b.l10n.saveAlert}),b.docsLookUpList.on("change",function(){var c=a(this).val();""===c?b.docsLookUpButton.prop("disabled",!0):b.docsLookUpButton.prop("disabled",!1)})},b.showWarning=function(){var c=b.warning.find(".file-editor-warning-message").text();a("#wpwrap").attr("aria-hidden","true"),a(document.body).addClass("modal-open").append(b.warning.detach()),b.warning.removeClass("hidden").find(".file-editor-warning-go-back").focus(),b.warningTabbables=b.warning.find("a, button"),b.warningTabbables.on("keydown",b.constrainTabbing),b.warning.on("click",".file-editor-warning-dismiss",b.dismissWarning),setTimeout(function(){wp.a11y.speak(wp.sanitize.stripTags(c.replace(/\s+/g," ")),"assertive")},1e3)},b.constrainTabbing=function(a){var c,d;9===a.which&&(c=b.warningTabbables.first()[0],d=b.warningTabbables.last()[0],d!==a.target||a.shiftKey?c===a.target&&a.shiftKey&&(d.focus(),a.preventDefault()):(c.focus(),a.preventDefault()))},b.dismissWarning=function(){wp.ajax.post("dismiss-wp-pointer",{pointer:b.themeOrPlugin+"_editor_notice"}),b.warning.remove(),a("#wpwrap").removeAttr("aria-hidden"),a("body").removeClass("modal-open")},b.onChange=function(){b.dirty=!0,b.removeNotice("file_saved")},b.submit=function(c){var d,e={};if(c.preventDefault(),a.each(b.form.serializeArray(),function(){e[this.name]=this.value}),b.instance&&(e.newcontent=b.instance.codemirror.getValue()),!b.isSaving){if(b.lintErrors.length)return void b.instance.codemirror.setCursor(b.lintErrors[0].from.line);b.isSaving=!0,b.textarea.prop("readonly",!0),b.instance&&b.instance.codemirror.setOption("readOnly",!0),b.spinner.addClass("is-active"),d=wp.ajax.post("edit-theme-plugin-file",e),b.lastSaveNoticeCode&&b.removeNotice(b.lastSaveNoticeCode),d.done(function(a){b.lastSaveNoticeCode="file_saved",b.addNotice({code:b.lastSaveNoticeCode,type:"success",message:a.message,dismissible:!0}),b.dirty=!1}),d.fail(function(c){var d=a.extend({code:"save_error",message:b.l10n.saveError},c,{type:"error",dismissible:!0});b.lastSaveNoticeCode=d.code,b.addNotice(d)}),d.always(function(){b.spinner.removeClass("is-active"),b.isSaving=!1,b.textarea.prop("readonly",!1),b.instance&&b.instance.codemirror.setOption("readOnly",!1)})}},b.addNotice=function(c){var d;if(!c.code)throw new Error("Missing code.");return b.removeNotice(c.code),d=a(b.noticeTemplate(c)),d.hide(),d.find(".notice-dismiss").on("click",function(){b.removeNotice(c.code),c.onDismiss&&c.onDismiss(c)}),wp.a11y.speak(c.message),b.noticesContainer.append(d),d.slideDown("fast"),b.noticeElements[c.code]=d,d},b.removeNotice=function(c){return!!b.noticeElements[c]&&(b.noticeElements[c].slideUp("fast",function(){a(this).remove()}),delete b.noticeElements[c],!0)},b.initCodeEditor=function(){var c,d;c=a.extend({},b.codeEditor),c.onTabPrevious=function(){a("#templateside").find(":tabbable").last().focus()},c.onTabNext=function(){a("#template").find(":tabbable:not(.CodeMirror-code)").first().focus()},c.onChangeLintingErrors=function(a){b.lintErrors=a,0===a.length&&b.submitButton.toggleClass("disabled",!1)},c.onUpdateErrorNotice=function(a){var d,e;b.submitButton.toggleClass("disabled",a.length>0),0!==a.length?(d=1===a.length?b.l10n.lintError.singular.replace("%d","1"):b.l10n.lintError.plural.replace("%d",String(a.length)),e=b.addNotice({code:"lint_errors",type:"error",message:d,dismissible:!1}),e.find("input[type=checkbox]").on("click",function(){c.onChangeLintingErrors([]),b.removeNotice("lint_errors")})):b.removeNotice("lint_errors")},d=wp.codeEditor.initialize(a("#newcontent"),c),d.codemirror.on("change",b.onChange),a(d.codemirror.display.lineDiv).attr({role:"textbox","aria-multiline":"true","aria-labelledby":"theme-plugin-editor-label","aria-describedby":"editor-keyboard-trap-help-1 editor-keyboard-trap-help-2 editor-keyboard-trap-help-3 editor-keyboard-trap-help-4"}),a("#theme-plugin-editor-label").on("click",function(){d.codemirror.focus()}),b.instance=d},b.initFileBrowser=function(){var b=a("#templateside");b.find('[role="group"]').parent().attr("aria-expanded",!1),b.find(".notice").parents("[aria-expanded]").attr("aria-expanded",!0),b.find('[role="tree"]').each(function(){var a=new c(this);a.init()}),b.find(".current-file:first").each(function(){this.scrollIntoViewIfNeeded?this.scrollIntoViewIfNeeded():this.scrollIntoView(!1)})};var d=function(){var a=function(a,b,c){if("object"==typeof a){a.tabIndex=-1,this.tree=b,this.groupTreeitem=c,this.domNode=a,this.label=a.textContent.trim(),this.stopDefaultClick=!1,a.getAttribute("aria-label")&&(this.label=a.getAttribute("aria-label").trim()),this.isExpandable=!1,this.isVisible=!1,this.inGroup=!1,c&&(this.inGroup=!0);for(var d=a.firstElementChild;d;){if("ul"==d.tagName.toLowerCase()){d.setAttribute("role","group"),this.isExpandable=!0;break}d=d.nextElementSibling}this.keyCode=Object.freeze({RETURN:13,SPACE:32,PAGEUP:33,PAGEDOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40})}};return a.prototype.init=function(){this.domNode.tabIndex=-1,this.domNode.getAttribute("role")||this.domNode.setAttribute("role","treeitem"),this.domNode.addEventListener("keydown",this.handleKeydown.bind(this)),this.domNode.addEventListener("click",this.handleClick.bind(this)),this.domNode.addEventListener("focus",this.handleFocus.bind(this)),this.domNode.addEventListener("blur",this.handleBlur.bind(this)),this.isExpandable?(this.domNode.firstElementChild.addEventListener("mouseover",this.handleMouseOver.bind(this)),this.domNode.firstElementChild.addEventListener("mouseout",this.handleMouseOut.bind(this))):(this.domNode.addEventListener("mouseover",this.handleMouseOver.bind(this)),this.domNode.addEventListener("mouseout",this.handleMouseOut.bind(this)))},a.prototype.isExpanded=function(){return!!this.isExpandable&&"true"===this.domNode.getAttribute("aria-expanded")},a.prototype.handleKeydown=function(a){function b(a){return 1===a.length&&a.match(/\S/)}function c(a){"*"==e?(a.tree.expandAllSiblingItems(a),d=!0):b(e)&&(a.tree.setFocusByFirstCharacter(a,e),d=!0)}var d=(a.currentTarget,!1),e=a.key;if(this.stopDefaultClick=!1,!(a.altKey||a.ctrlKey||a.metaKey)){if(a.shift)a.keyCode==this.keyCode.SPACE||a.keyCode==this.keyCode.RETURN?(a.stopPropagation(),this.stopDefaultClick=!0):b(e)&&c(this);else switch(a.keyCode){case this.keyCode.SPACE:case this.keyCode.RETURN:this.isExpandable?(this.isExpanded()?this.tree.collapseTreeitem(this):this.tree.expandTreeitem(this),d=!0):(a.stopPropagation(),this.stopDefaultClick=!0);break;case this.keyCode.UP:this.tree.setFocusToPreviousItem(this),d=!0;break;case this.keyCode.DOWN:this.tree.setFocusToNextItem(this),d=!0;break;case this.keyCode.RIGHT:this.isExpandable&&(this.isExpanded()?this.tree.setFocusToNextItem(this):this.tree.expandTreeitem(this)),d=!0;break;case this.keyCode.LEFT:this.isExpandable&&this.isExpanded()?(this.tree.collapseTreeitem(this),d=!0):this.inGroup&&(this.tree.setFocusToParentItem(this),d=!0);break;case this.keyCode.HOME:this.tree.setFocusToFirstItem(),d=!0;break;case this.keyCode.END:this.tree.setFocusToLastItem(),d=!0;break;default:b(e)&&c(this)}d&&(a.stopPropagation(),a.preventDefault())}},a.prototype.handleClick=function(a){a.target!==this.domNode&&a.target!==this.domNode.firstElementChild||this.isExpandable&&(this.isExpanded()?this.tree.collapseTreeitem(this):this.tree.expandTreeitem(this),a.stopPropagation())},a.prototype.handleFocus=function(a){var b=this.domNode;this.isExpandable&&(b=b.firstElementChild),b.classList.add("focus")},a.prototype.handleBlur=function(a){var b=this.domNode;this.isExpandable&&(b=b.firstElementChild),b.classList.remove("focus")},a.prototype.handleMouseOver=function(a){a.currentTarget.classList.add("hover")},a.prototype.handleMouseOut=function(a){a.currentTarget.classList.remove("hover")},a}();return c=function(){var a=function(a){"object"==typeof a&&(this.domNode=a,this.treeitems=[],this.firstChars=[],this.firstTreeitem=null,this.lastTreeitem=null)};return a.prototype.init=function(){function a(b,c,e){for(var f=b.firstElementChild,g=e;f;)("li"===f.tagName.toLowerCase()&&"span"===f.firstElementChild.tagName.toLowerCase()||"a"===f.tagName.toLowerCase())&&(g=new d(f,c,e),g.init(),c.treeitems.push(g),c.firstChars.push(g.label.substring(0,1).toLowerCase())),f.firstElementChild&&a(f,c,g),f=f.nextElementSibling}this.domNode.getAttribute("role")||this.domNode.setAttribute("role","tree"),a(this.domNode,this,!1),this.updateVisibleTreeitems(),this.firstTreeitem.domNode.tabIndex=0},a.prototype.setFocusToItem=function(a){for(var b=0;b<this.treeitems.length;b++){var c=this.treeitems[b];c===a?(c.domNode.tabIndex=0,c.domNode.focus()):c.domNode.tabIndex=-1}},a.prototype.setFocusToNextItem=function(a){for(var b=!1,c=this.treeitems.length-1;c>=0;c--){var d=this.treeitems[c];if(d===a)break;d.isVisible&&(b=d)}b&&this.setFocusToItem(b)},a.prototype.setFocusToPreviousItem=function(a){for(var b=!1,c=0;c<this.treeitems.length;c++){var d=this.treeitems[c];if(d===a)break;d.isVisible&&(b=d)}b&&this.setFocusToItem(b)},a.prototype.setFocusToParentItem=function(a){a.groupTreeitem&&this.setFocusToItem(a.groupTreeitem)},a.prototype.setFocusToFirstItem=function(){this.setFocusToItem(this.firstTreeitem)},a.prototype.setFocusToLastItem=function(){this.setFocusToItem(this.lastTreeitem)},a.prototype.expandTreeitem=function(a){a.isExpandable&&(a.domNode.setAttribute("aria-expanded",!0),this.updateVisibleTreeitems())},a.prototype.expandAllSiblingItems=function(a){for(var b=0;b<this.treeitems.length;b++){var c=this.treeitems[b];c.groupTreeitem===a.groupTreeitem&&c.isExpandable&&this.expandTreeitem(c)}},a.prototype.collapseTreeitem=function(a){var b=!1;b=a.isExpanded()?a:a.groupTreeitem,b&&(b.domNode.setAttribute("aria-expanded",!1),this.updateVisibleTreeitems(),this.setFocusToItem(b))},a.prototype.updateVisibleTreeitems=function(){this.firstTreeitem=this.treeitems[0];for(var a=0;a<this.treeitems.length;a++){var b=this.treeitems[a],c=b.domNode.parentNode;for(b.isVisible=!0;c&&c!==this.domNode;)"false"==c.getAttribute("aria-expanded")&&(b.isVisible=!1),c=c.parentNode;b.isVisible&&(this.lastTreeitem=b)}},a.prototype.setFocusByFirstCharacter=function(a,b){var c,d;b=b.toLowerCase(),c=this.treeitems.indexOf(a)+1,c===this.treeitems.length&&(c=0),d=this.getIndexFirstChars(c,b),d===-1&&(d=this.getIndexFirstChars(0,b)),d>-1&&this.setFocusToItem(this.treeitems[d])},a.prototype.getIndexFirstChars=function(a,b){for(var c=a;c<this.firstChars.length;c++)if(this.treeitems[c].isVisible&&b===this.firstChars[c])return c;return-1},a}(),b}(jQuery);