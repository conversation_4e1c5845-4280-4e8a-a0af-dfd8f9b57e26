!function(a){var b=window.imageEdit={iasapi:{},hold:{},postid:"",_view:!1,handleCropToolClick:function(c,d,e){var f=a("#image-preview-"+c),g=this.iasapi.getSelection();isNaN(g.x1)&&(this.setCropSelection(c,{x1:0,y1:0,x2:f.innerWidth(),y2:f.innerHeight(),width:f.innerWidth(),height:f.innerHeight()}),g=this.iasapi.getSelection()),0===g.x1&&0===g.y1&&0===g.x2&&0===g.y2?(this.iasapi.setSelection(0,0,f.innerWidth(),f.innerHeight(),!0),this.iasapi.setOptions({show:!0}),this.iasapi.update()):b.crop(c,d,e)},intval:function(a){return 0|a},setDisabled:function(a,b){b?a.removeClass("disabled").prop("disabled",!1):a.addClass("disabled").prop("disabled",!0)},init:function(b){var c=this,d=a("#image-editor-"+c.postid),e=c.intval(a("#imgedit-x-"+b).val()),f=c.intval(a("#imgedit-y-"+b).val());c.postid!==b&&d.length&&c.close(c.postid),c.hold.w=c.hold.ow=e,c.hold.h=c.hold.oh=f,c.hold.xy_ratio=e/f,c.hold.sizer=parseFloat(a("#imgedit-sizer-"+b).val()),c.postid=b,a("#imgedit-response-"+b).empty(),a('input[type="text"]',"#imgedit-panel-"+b).keypress(function(b){var c=b.keyCode;if(36<c&&c<41&&a(this).blur(),13===c)return b.preventDefault(),b.stopPropagation(),!1})},toggleEditor:function(b,c){var d=a("#imgedit-wait-"+b);c?d.fadeIn("fast"):d.fadeOut("fast")},toggleHelp:function(b){var c=a(b);return c.attr("aria-expanded","false"===c.attr("aria-expanded")?"true":"false").parents(".imgedit-group-top").toggleClass("imgedit-help-toggled").find(".imgedit-help").slideToggle("fast"),!1},getTarget:function(b){return a('input[name="imgedit-target-'+b+'"]:checked',"#imgedit-save-target-"+b).val()||"full"},scaleChanged:function(b,c,d){var e=a("#imgedit-scale-width-"+b),f=a("#imgedit-scale-height-"+b),g=a("#imgedit-scale-warn-"+b),h="",i="";!1!==this.validateNumeric(d)&&(c?(i=""!==e.val()?Math.round(e.val()/this.hold.xy_ratio):"",f.val(i)):(h=""!==f.val()?Math.round(f.val()*this.hold.xy_ratio):"",e.val(h)),i&&i>this.hold.oh||h&&h>this.hold.ow?g.css("visibility","visible"):g.css("visibility","hidden"))},getSelRatio:function(b){var c=this.hold.w,d=this.hold.h,e=this.intval(a("#imgedit-crop-width-"+b).val()),f=this.intval(a("#imgedit-crop-height-"+b).val());return e&&f?e+":"+f:c&&d?c+":"+d:"1:1"},filterHistory:function(b,c){var d,e,f,g,h=a("#imgedit-history-"+b).val(),i=[];if(""!==h){if(h=JSON.parse(h),d=this.intval(a("#imgedit-undone-"+b).val()),d>0)for(;d>0;)h.pop(),d--;if(c){if(!h.length)return this.hold.w=this.hold.ow,this.hold.h=this.hold.oh,"";f=h[h.length-1],f=f.c||f.r||f.f||!1,f&&(this.hold.w=f.fw,this.hold.h=f.fh)}for(e in h)g=h[e],g.hasOwnProperty("c")?i[e]={c:{x:g.c.x,y:g.c.y,w:g.c.w,h:g.c.h}}:g.hasOwnProperty("r")?i[e]={r:g.r.r}:g.hasOwnProperty("f")&&(i[e]={f:g.f.f});return JSON.stringify(i)}return""},refreshEditor:function(c,d,e){var f,g,h=this;h.toggleEditor(c,1),f={action:"imgedit-preview",_ajax_nonce:d,postid:c,history:h.filterHistory(c,1),rand:h.intval(1e6*Math.random())},g=a('<img id="image-preview-'+c+'" alt="" />').on("load",{history:f.history},function(d){var f,h,i,j=a("#imgedit-crop-"+c),k=b;""!==d.data.history&&(i=JSON.parse(d.data.history),i[i.length-1].hasOwnProperty("c")&&(k.setDisabled(a("#image-undo-"+c),!0),a("#image-undo-"+c).focus())),j.empty().append(g),f=Math.max(k.hold.w,k.hold.h),h=Math.max(a(g).width(),a(g).height()),k.hold.sizer=f>h?h/f:1,k.initCrop(c,g,j),"undefined"!=typeof e&&null!==e&&e(),a("#imgedit-history-"+c).val()&&"0"===a("#imgedit-undone-"+c).val()?a("input.imgedit-submit-btn","#imgedit-panel-"+c).removeAttr("disabled"):a("input.imgedit-submit-btn","#imgedit-panel-"+c).prop("disabled",!0),k.toggleEditor(c,0)}).on("error",function(){a("#imgedit-crop-"+c).empty().append('<div class="error"><p>'+imageEditL10n.error+"</p></div>"),h.toggleEditor(c,0)}).attr("src",ajaxurl+"?"+a.param(f))},action:function(b,c,d){var e,f,g,h,i,j=this;if(j.notsaved(b))return!1;if(e={action:"image-editor",_ajax_nonce:c,postid:b},"scale"===d){if(f=a("#imgedit-scale-width-"+b),g=a("#imgedit-scale-height-"+b),h=j.intval(f.val()),i=j.intval(g.val()),h<1)return f.focus(),!1;if(i<1)return g.focus(),!1;if(h===j.hold.ow||i===j.hold.oh)return!1;e["do"]="scale",e.fwidth=h,e.fheight=i}else{if("restore"!==d)return!1;e["do"]="restore"}j.toggleEditor(b,1),a.post(ajaxurl,e,function(c){a("#image-editor-"+b).empty().append(c),j.toggleEditor(b,0),j._view&&j._view.refresh()})},save:function(c,d){var e,f=this.getTarget(c),g=this.filterHistory(c,0),h=this;return""!==g&&(this.toggleEditor(c,1),e={action:"image-editor",_ajax_nonce:d,postid:c,history:g,target:f,context:a("#image-edit-context").length?a("#image-edit-context").val():null,"do":"save"},void a.post(ajaxurl,e,function(d){var e=JSON.parse(d);return e.error?(a("#imgedit-response-"+c).html('<div class="error"><p>'+e.error+"</p></div>"),void b.close(c)):(e.fw&&e.fh&&a("#media-dims-"+c).html(e.fw+" &times; "+e.fh),e.thumbnail&&a(".thumbnail","#thumbnail-head-"+c).attr("src",""+e.thumbnail),e.msg&&a("#imgedit-response-"+c).html('<div class="updated"><p>'+e.msg+"</p></div>"),void(h._view?h._view.save():b.close(c)))}))},open:function(c,d,e){this._view=e;var f,g,h=a("#image-editor-"+c),i=a("#media-head-"+c),j=a("#imgedit-open-btn-"+c),k=j.siblings(".spinner");if(!j.hasClass("button-activated"))return k.addClass("is-active"),g={action:"image-editor",_ajax_nonce:d,postid:c,"do":"open"},f=a.ajax({url:ajaxurl,type:"post",data:g,beforeSend:function(){j.addClass("button-activated")}}).done(function(a){h.html(a),i.fadeOut("fast",function(){h.fadeIn("fast"),j.removeClass("button-activated"),k.removeClass("is-active")}),b.init(c)})},imgLoaded:function(b){var c=a("#image-preview-"+b),d=a("#imgedit-crop-"+b);"undefined"==typeof this.hold.sizer&&this.init(b),this.initCrop(b,c,d),this.setCropSelection(b,{x1:0,y1:0,x2:0,y2:0,width:c.innerWidth(),height:c.innerHeight()}),this.toggleEditor(b,0),a(".imgedit-wrap .imgedit-help-toggle").eq(0).focus()},initCrop:function(c,d,e){var f,g=this,h=a("#imgedit-sel-width-"+c),i=a("#imgedit-sel-height-"+c);g.iasapi=a(d).imgAreaSelect({parent:e,instance:!0,handles:!0,keys:!0,minWidth:3,minHeight:3,onInit:function(b){f=a(b),f.next().css("position","absolute").nextAll(".imgareaselect-outer").css("position","absolute"),e.children().on("mousedown, touchstart",function(a){var b,d,e=!1;a.shiftKey&&(b=g.iasapi.getSelection(),d=g.getSelRatio(c),e=b&&b.width&&b.height?b.width+":"+b.height:d),g.iasapi.setOptions({aspectRatio:e})})},onSelectStart:function(){b.setDisabled(a("#imgedit-crop-sel-"+c),1)},onSelectEnd:function(a,d){b.setCropSelection(c,d)},onSelectChange:function(a,c){var d=b.hold.sizer;h.val(b.round(c.width/d)),i.val(b.round(c.height/d))}})},setCropSelection:function(b,c){var d;return c=c||0,!c||c.width<3&&c.height<3?(this.setDisabled(a(".imgedit-crop","#imgedit-panel-"+b),1),this.setDisabled(a("#imgedit-crop-sel-"+b),1),a("#imgedit-sel-width-"+b).val(""),a("#imgedit-sel-height-"+b).val(""),a("#imgedit-selection-"+b).val(""),!1):(d={x:c.x1,y:c.y1,w:c.width,h:c.height},this.setDisabled(a(".imgedit-crop","#imgedit-panel-"+b),1),void a("#imgedit-selection-"+b).val(JSON.stringify(d)))},close:function(b,c){return c=c||!1,(!c||!this.notsaved(b))&&(this.iasapi={},this.hold={},void(this._view?this._view.back():a("#image-editor-"+b).fadeOut("fast",function(){a("#media-head-"+b).fadeIn("fast",function(){a("#imgedit-open-btn-"+b).focus()}),a(this).empty()})))},notsaved:function(b){var c=a("#imgedit-history-"+b).val(),d=""!==c?JSON.parse(c):[],e=this.intval(a("#imgedit-undone-"+b).val());return e<d.length&&!confirm(a("#imgedit-leaving-"+b).html())},addStep:function(b,c,d){for(var e=this,f=a("#imgedit-history-"+c),g=""!==f.val()?JSON.parse(f.val()):[],h=a("#imgedit-undone-"+c),i=e.intval(h.val());i>0;)g.pop(),i--;h.val(0),g.push(b),f.val(JSON.stringify(g)),e.refreshEditor(c,d,function(){e.setDisabled(a("#image-undo-"+c),!0),e.setDisabled(a("#image-redo-"+c),!1)})},rotate:function(b,c,d,e){return!a(e).hasClass("disabled")&&void this.addStep({r:{r:b,fw:this.hold.h,fh:this.hold.w}},c,d)},flip:function(b,c,d,e){return!a(e).hasClass("disabled")&&void this.addStep({f:{f:b,fw:this.hold.w,fh:this.hold.h}},c,d)},crop:function(b,c,d){var e=a("#imgedit-selection-"+b).val(),f=this.intval(a("#imgedit-sel-width-"+b).val()),g=this.intval(a("#imgedit-sel-height-"+b).val());return!a(d).hasClass("disabled")&&""!==e&&(e=JSON.parse(e),void(e.w>0&&e.h>0&&f>0&&g>0&&(e.fw=f,e.fh=g,this.addStep({c:e},b,c))))},undo:function(b,c){var d=this,e=a("#image-undo-"+b),f=a("#imgedit-undone-"+b),g=d.intval(f.val())+1;e.hasClass("disabled")||(f.val(g),d.refreshEditor(b,c,function(){var c=a("#imgedit-history-"+b),f=""!==c.val()?JSON.parse(c.val()):[];d.setDisabled(a("#image-redo-"+b),!0),d.setDisabled(e,g<f.length),f.length===g&&a("#image-redo-"+b).focus()}))},redo:function(b,c){var d=this,e=a("#image-redo-"+b),f=a("#imgedit-undone-"+b),g=d.intval(f.val())-1;e.hasClass("disabled")||(f.val(g),d.refreshEditor(b,c,function(){d.setDisabled(a("#image-undo-"+b),!0),d.setDisabled(e,g>0),0===g&&a("#image-undo-"+b).focus()}))},setNumSelection:function(b,c){var d,e,f,g,h,i=a("#imgedit-sel-width-"+b),j=a("#imgedit-sel-height-"+b),k=this.intval(i.val()),l=this.intval(j.val()),m=a("#image-preview-"+b),n=m.height(),o=m.width(),p=this.hold.sizer,q=this.iasapi;if(!1!==this.validateNumeric(c))return k<1?(i.val(""),!1):l<1?(j.val(""),!1):void(k&&l&&(d=q.getSelection())&&(g=d.x1+Math.round(k*p),h=d.y1+Math.round(l*p),e=d.x1,f=d.y1,g>o&&(e=0,g=o,i.val(Math.round(g/p))),h>n&&(f=0,h=n,j.val(Math.round(h/p))),q.setSelection(e,f,g,h),q.update(),this.setCropSelection(b,q.getSelection())))},round:function(a){var b;return a=Math.round(a),this.hold.sizer>.6?a:(b=a.toString().slice(-1),"1"===b?a-1:"9"===b?a+1:a)},setRatioSelection:function(b,c,d){var e,f,g=this.intval(a("#imgedit-crop-width-"+b).val()),h=this.intval(a("#imgedit-crop-height-"+b).val()),i=a("#image-preview-"+b).height();!1!==this.validateNumeric(d)&&g&&h&&(this.iasapi.setOptions({aspectRatio:g+":"+h}),(e=this.iasapi.getSelection(!0))&&(f=Math.ceil(e.y1+(e.x2-e.x1)/(g/h)),f>i&&(f=i,c?a("#imgedit-crop-height-"+b).val(""):a("#imgedit-crop-width-"+b).val("")),this.iasapi.setSelection(e.x1,e.y1,e.x2,f),this.iasapi.update()))},validateNumeric:function(b){if(!this.intval(a(b).val()))return a(b).val(""),!1}}}(jQuery);