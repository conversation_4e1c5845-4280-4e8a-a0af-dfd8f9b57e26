wp.textWidgets=function(a){"use strict";var b={dismissedPointers:[],idBases:["text"]};return b.TextWidgetControl=Backbone.View.extend({events:{},initialize:function(b){var c=this;if(!b.el)throw new Error("Missing options.el");if(!b.syncContainer)throw new Error("Missing options.syncContainer");Backbone.View.prototype.initialize.call(c,b),c.syncContainer=b.syncContainer,c.$el.addClass("text-widget-fields"),c.$el.html(wp.template("widget-text-control-fields")),c.customHtmlWidgetPointer=c.$el.find(".wp-pointer.custom-html-widget-pointer"),c.customHtmlWidgetPointer.length&&(c.customHtmlWidgetPointer.find(".close").on("click",function(b){b.preventDefault(),c.customHtmlWidgetPointer.hide(),a("#"+c.fields.text.attr("id")+"-html").focus(),c.dismissPointers(["text_widget_custom_html"])}),c.customHtmlWidgetPointer.find(".add-widget").on("click",function(a){a.preventDefault(),c.customHtmlWidgetPointer.hide(),c.openAvailableWidgetsPanel()})),c.pasteHtmlPointer=c.$el.find(".wp-pointer.paste-html-pointer"),c.pasteHtmlPointer.length&&c.pasteHtmlPointer.find(".close").on("click",function(a){a.preventDefault(),c.pasteHtmlPointer.hide(),c.editor.focus(),c.dismissPointers(["text_widget_custom_html","text_widget_paste_html"])}),c.fields={title:c.$el.find(".title"),text:c.$el.find(".text")},_.each(c.fields,function(a,b){a.on("input change",function(){var d=c.syncContainer.find(".sync-input."+b);d.val()!==a.val()&&(d.val(a.val()),d.trigger("change"))}),a.val(c.syncContainer.find(".sync-input."+b).val())})},dismissPointers:function(a){_.each(a,function(a){wp.ajax.post("dismiss-wp-pointer",{pointer:a}),b.dismissedPointers.push(a)})},openAvailableWidgetsPanel:function(){var a;wp.customize.section.each(function(b){b.extended(wp.customize.Widgets.SidebarSection)&&b.expanded()&&(a=wp.customize.control("sidebars_widgets["+b.params.sidebarId+"]"))}),a&&setTimeout(function(){wp.customize.Widgets.availableWidgetsPanel.open(a),wp.customize.Widgets.availableWidgetsPanel.$search.val("HTML").trigger("keyup")})},updateFields:function(){var a,b=this;b.fields.title.is(document.activeElement)||(a=b.syncContainer.find(".sync-input.title"),b.fields.title.val(a.val())),a=b.syncContainer.find(".sync-input.text"),b.fields.text.is(":visible")?b.fields.text.is(document.activeElement)||b.fields.text.val(a.val()):b.editor&&!b.editorFocused&&a.val()!==b.fields.text.val()&&b.editor.setContent(wp.editor.autop(a.val()))},initializeEditor:function(){function c(){var e,g,l;if(document.getElementById(d)){if("undefined"==typeof window.tinymce)return void wp.editor.initialize(d,{quicktags:!0,mediaButtons:!0});if(tinymce.get(d)&&(j=tinymce.get(d).isHidden(),wp.editor.remove(d)),a(document).one("wp-before-tinymce-init.text-widget-init",function(a,b){b.plugins&&(/\bwpview\b/.test(b.plugins)||(b.plugins+=",wpview"))}),wp.editor.initialize(d,{tinymce:{wpautop:!0},quicktags:!0,mediaButtons:!0}),l=function(b){b.show(),b.find(".close").focus(),wp.a11y.speak(b.find("h3, p").map(function(){return a(this).text()}).get().join("\n\n"))},e=window.tinymce.get(d),!e)throw new Error("Failed to initialize editor");g=function(){a(e.getWin()).on("unload",function(){_.defer(c)}),j&&switchEditors.go(d,"html"),a("#"+d+"-html").on("click",function(){h.pasteHtmlPointer.hide(),-1===b.dismissedPointers.indexOf("text_widget_custom_html")&&l(h.customHtmlWidgetPointer)}),a("#"+d+"-tmce").on("click",function(){h.customHtmlWidgetPointer.hide()}),e.on("pastepreprocess",function(a){var c=a.content;-1===b.dismissedPointers.indexOf("text_widget_paste_html")&&c&&/&lt;\w+.*?&gt;/.test(c)&&_.delay(function(){l(h.pasteHtmlPointer)},250)})},e.initialized?g():e.on("init",g),h.editorFocused=!1,e.on("focus",function(){h.editorFocused=!0}),e.on("paste",function(){e.setDirty(!0),f()}),e.on("NodeChange",function(){k=!0}),e.on("NodeChange",_.debounce(f,i)),e.on("blur hide",function(){h.editorFocused=!1,f()}),h.editor=e}}var d,e,f,g,h=this,i=1e3,j=!1,k=!1;e=h.fields.text,d=e.attr("id"),g=e.val(),f=function(){var a=300;h.editor.isDirty()&&(wp.customize&&wp.customize.state&&(wp.customize.state("processing").set(wp.customize.state("processing").get()+1),_.delay(function(){wp.customize.state("processing").set(wp.customize.state("processing").get()-1)},a)),h.editor.isHidden()||h.editor.save()),k&&g!==e.val()&&(e.trigger("change"),k=!1,g=e.val())},h.syncContainer.closest(".widget").find("[name=savewidget]:first").on("click",function(){f()}),c()}}),b.widgetControls={},b.handleWidgetAdded=function(c,d){var e,f,g,h,i,j,k,l=50;e=d.find("> .widget-inside > .form, > .widget-inside > form"),f=e.find("> .id_base").val(),-1!==b.idBases.indexOf(f)&&(h=e.find(".widget-id").val(),b.widgetControls[h]||e.find(".visual").val()&&(j=a("<div></div>"),k=d.find(".widget-content:first"),k.before(j),g=new b.TextWidgetControl({el:j,syncContainer:k}),b.widgetControls[h]=g,(i=function(){d.hasClass("open")?g.initializeEditor():setTimeout(i,l)})()))},b.setupAccessibleMode=function(){var c,d,e,f,g;c=a(".editwidget > form"),0!==c.length&&(d=c.find("> .widget-control-actions > .id_base").val(),-1!==b.idBases.indexOf(d)&&c.find(".visual").val()&&(f=a("<div></div>"),g=c.find("> .widget-inside"),g.before(f),e=new b.TextWidgetControl({el:f,syncContainer:g}),e.initializeEditor()))},b.handleWidgetUpdated=function(a,c){var d,e,f,g;d=c.find("> .widget-inside > .form, > .widget-inside > form"),g=d.find("> .id_base").val(),-1!==b.idBases.indexOf(g)&&(e=d.find("> .widget-id").val(),f=b.widgetControls[e],f&&f.updateFields())},b.init=function(){var c=a(document);c.on("widget-added",b.handleWidgetAdded),c.on("widget-synced widget-updated",b.handleWidgetUpdated),a(function(){var c;"widgets"===window.pagenow&&(c=a(".widgets-holder-wrap:not(#available-widgets)").find("div.widget"),c.one("click.toggle-widget-expanded",function(){var c=a(this);b.handleWidgetAdded(new jQuery.Event("widget-added"),c)}),a(window).on("load",function(){b.setupAccessibleMode()}))})},b}(jQuery);