wp.customHtmlWidgets=function(a){"use strict";var b={idBases:["custom_html"],codeEditorSettings:{},l10n:{errorNotice:{singular:"",plural:""}}};return b.CustomHtmlWidgetControl=Backbone.View.extend({events:{},initialize:function(a){var c=this;if(!a.el)throw new Error("Missing options.el");if(!a.syncContainer)throw new Error("Missing options.syncContainer");Backbone.View.prototype.initialize.call(c,a),c.syncContainer=a.syncContainer,c.widgetIdBase=c.syncContainer.parent().find(".id_base").val(),c.widgetNumber=c.syncContainer.parent().find(".widget_number").val(),c.customizeSettingId="widget_"+c.widgetIdBase+"["+String(c.widgetNumber)+"]",c.$el.addClass("custom-html-widget-fields"),c.$el.html(wp.template("widget-custom-html-control-fields")({codeEditorDisabled:b.codeEditorSettings.disabled})),c.errorNoticeContainer=c.$el.find(".code-editor-error-container"),c.currentErrorAnnotations=[],c.saveButton=c.syncContainer.add(c.syncContainer.parent().find(".widget-control-actions")).find(".widget-control-save, #savewidget"),c.saveButton.addClass("custom-html-widget-save-button"),c.fields={title:c.$el.find(".title"),content:c.$el.find(".content")},_.each(c.fields,function(a,b){a.on("input change",function(){var d=c.syncContainer.find(".sync-input."+b);d.val()!==a.val()&&(d.val(a.val()),d.trigger("change"))}),a.val(c.syncContainer.find(".sync-input."+b).val())})},updateFields:function(){var a,b=this;b.fields.title.is(document.activeElement)||(a=b.syncContainer.find(".sync-input.title"),b.fields.title.val(a.val())),b.contentUpdateBypassed=b.fields.content.is(document.activeElement)||b.editor&&b.editor.codemirror.state.focused||0!==b.currentErrorAnnotations.length,b.contentUpdateBypassed||(a=b.syncContainer.find(".sync-input.content"),b.fields.content.val(a.val()))},updateErrorNotice:function(c){var d,e,f=this,g="";1===c.length?g=b.l10n.errorNotice.singular.replace("%d","1"):c.length>1&&(g=b.l10n.errorNotice.plural.replace("%d",String(c.length))),f.fields.content[0].setCustomValidity&&f.fields.content[0].setCustomValidity(g),wp.customize&&wp.customize.has(f.customizeSettingId)?(e=wp.customize(f.customizeSettingId),e.notifications.remove("htmlhint_error"),0!==c.length&&e.notifications.add("htmlhint_error",new wp.customize.Notification("htmlhint_error",{message:g,type:"error"}))):0!==c.length?(d=a('<div class="inline notice notice-error notice-alt"></div>'),d.append(a("<p></p>",{text:g})),f.errorNoticeContainer.empty(),f.errorNoticeContainer.append(d),f.errorNoticeContainer.slideDown("fast"),wp.a11y.speak(g)):f.errorNoticeContainer.slideUp("fast")},initializeEditor:function(){var c,d=this;b.codeEditorSettings.disabled||(c=_.extend({},b.codeEditorSettings,{onTabPrevious:function(){d.fields.title.focus()},onTabNext:function(){var a=d.syncContainer.add(d.syncContainer.parent().find(".widget-position, .widget-control-actions")).find(":tabbable");a.first().focus()},onChangeLintingErrors:function(a){d.currentErrorAnnotations=a},onUpdateErrorNotice:function(a){d.saveButton.toggleClass("validation-blocked disabled",a.length>0),d.updateErrorNotice(a)}}),d.editor=wp.codeEditor.initialize(d.fields.content,c),a(d.editor.codemirror.display.lineDiv).attr({role:"textbox","aria-multiline":"true","aria-labelledby":d.fields.content[0].id+"-label","aria-describedby":"editor-keyboard-trap-help-1 editor-keyboard-trap-help-2 editor-keyboard-trap-help-3 editor-keyboard-trap-help-4"}),a("#"+d.fields.content[0].id+"-label").on("click",function(){d.editor.codemirror.focus()}),d.fields.content.on("change",function(){this.value!==d.editor.codemirror.getValue()&&d.editor.codemirror.setValue(this.value)}),d.editor.codemirror.on("change",function(){var a=d.editor.codemirror.getValue();a!==d.fields.content.val()&&d.fields.content.val(a).trigger("change")}),d.editor.codemirror.on("blur",function(){d.contentUpdateBypassed&&d.syncContainer.find(".sync-input.content").trigger("change")}),wp.customize&&d.editor.codemirror.on("keydown",function(a,b){var c=27;c===b.keyCode&&b.stopPropagation()}))}}),b.widgetControls={},b.handleWidgetAdded=function(c,d){var e,f,g,h,i,j,k,l=50;e=d.find("> .widget-inside > .form, > .widget-inside > form"),f=e.find("> .id_base").val(),-1!==b.idBases.indexOf(f)&&(h=e.find(".widget-id").val(),b.widgetControls[h]||(j=a("<div></div>"),k=d.find(".widget-content:first"),k.before(j),g=new b.CustomHtmlWidgetControl({el:j,syncContainer:k}),b.widgetControls[h]=g,(i=function(){(wp.customize?d.parent().hasClass("expanded"):d.hasClass("open"))?g.initializeEditor():setTimeout(i,l)})()))},b.setupAccessibleMode=function(){var c,d,e,f,g;c=a(".editwidget > form"),0!==c.length&&(d=c.find("> .widget-control-actions > .id_base").val(),-1!==b.idBases.indexOf(d)&&(f=a("<div></div>"),g=c.find("> .widget-inside"),g.before(f),e=new b.CustomHtmlWidgetControl({el:f,syncContainer:g}),e.initializeEditor()))},b.handleWidgetUpdated=function(a,c){var d,e,f,g;d=c.find("> .widget-inside > .form, > .widget-inside > form"),g=d.find("> .id_base").val(),-1!==b.idBases.indexOf(g)&&(e=d.find("> .widget-id").val(),f=b.widgetControls[e],f&&f.updateFields())},b.init=function(c){var d=a(document);_.extend(b.codeEditorSettings,c),d.on("widget-added",b.handleWidgetAdded),d.on("widget-synced widget-updated",b.handleWidgetUpdated),a(function(){var c;"widgets"===window.pagenow&&(c=a(".widgets-holder-wrap:not(#available-widgets)").find("div.widget"),c.one("click.toggle-widget-expanded",function(){var c=a(this);b.handleWidgetAdded(new jQuery.Event("widget-added"),c)}),a(window).on("load",function(){b.setupAccessibleMode()}))})},b}(jQuery);