!function(a){"use strict";var b,c,d;d=wp.media.view.MediaFrame.AudioDetails.extend({createStates:function(){this.states.add([new wp.media.controller.AudioDetails({media:this.media}),new wp.media.controller.MediaLibrary({type:"audio",id:"add-audio-source",title:wp.media.view.l10n.audioAddSourceTitle,toolbar:"add-audio-source",media:this.media,menu:!1})])}}),b=a.MediaWidgetModel.extend({}),c=a.MediaWidgetControl.extend({showDisplaySettings:!1,mapModelToMediaFrameProps:function(b){var c,d=this;return c=a.MediaWidgetControl.prototype.mapModelToMediaFrameProps.call(d,b),c.link="embed",c},renderPreview:function(){var a,b,c,d,e=this;c=e.model.get("attachment_id"),d=e.model.get("url"),(c||d)&&(a=e.$el.find(".media-widget-preview"),b=wp.template("wp-media-widget-audio-preview"),a.html(b({model:{attachment_id:e.model.get("attachment_id"),src:d},error:e.model.get("error")})),wp.mediaelement.initialize())},editMedia:function(){var a,b,c,e=this;b=e.mapModelToMediaFrameProps(e.model.toJSON()),a=new d({frame:"audio",state:"audio-details",metadata:b}),wp.media.frame=a,a.$el.addClass("media-widget"),c=function(a){e.selectedAttachment.set(a),e.model.set(_.extend(e.model.defaults(),e.mapMediaToModelProps(a),{error:!1}))},a.state("audio-details").on("update",c),a.state("replace-audio").on("replace",c),a.on("close",function(){a.detach()}),a.open()}}),a.controlConstructors.media_audio=c,a.modelConstructors.media_audio=b}(wp.mediaWidgets);