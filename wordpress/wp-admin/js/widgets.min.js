!function(a){var b=a(document);window.wpWidgets={hoveredSidebar:null,l10n:{save:"{save}",saved:"{saved}",saveAlert:"{saveAlert}",widgetAdded:"{widgetAdded}"},dirtyWidgets:{},init:function(){var c,d,e=this,f=a(".widgets-chooser"),g=f.find(".widgets-chooser-sidebars"),h=a("div.widgets-sortables"),i=!("undefined"==typeof isRtl||!isRtl);a("#widgets-right .sidebar-name").click(function(){var c=a(this),d=c.closest(".widgets-holder-wrap "),e=c.find(".handlediv");d.hasClass("closed")?(d.removeClass("closed"),e.attr("aria-expanded","true"),c.parent().sortable("refresh")):(d.addClass("closed"),e.attr("aria-expanded","false")),b.trigger<PERSON><PERSON><PERSON>("wp-pin-menu")}).find(".handlediv").each(function(b){0!==b&&a(this).attr("aria-expanded","false")}),a(window).on("beforeunload.widgets",function(b){var c,d=[];if(a.each(e.dirtyWidgets,function(a,b){b&&d.push(a)}),0!==d.length)return c=a("#widgets-right").find(".widget").filter(function(){return-1!==d.indexOf(a(this).prop("id").replace(/^widget-\d+_/,""))}),c.each(function(){a(this).hasClass("open")||a(this).find(".widget-title-action:first").click()}),c.first().each(function(){this.scrollIntoViewIfNeeded?this.scrollIntoViewIfNeeded():this.scrollIntoView(),a(this).find(".widget-inside :tabbable:first").focus()}),b.returnValue=wpWidgets.l10n.saveAlert,b.returnValue}),a("#widgets-left .sidebar-name").click(function(){var c=a(this).closest(".widgets-holder-wrap");c.toggleClass("closed").find(".handlediv").attr("aria-expanded",!c.hasClass("closed")),b.triggerHandler("wp-pin-menu")}),a(document.body).bind("click.widgets-toggle",function(b){var c,d,f,g,h,j,k,l=a(b.target),m={"z-index":100},n=l.closest(".widget").find(".widget-top button.widget-action");l.parents(".widget-top").length&&!l.parents("#available-widgets").length?(c=l.closest("div.widget"),d=c.children(".widget-inside"),f=parseInt(c.find("input.widget-width").val(),10),g=c.parent().width(),k=d.find(".widget-id").val(),c.data("dirty-state-initialized")||(j=d.find(".widget-control-save"),j.prop("disabled",!0).val(wpWidgets.l10n.saved),d.on("input change",function(){e.dirtyWidgets[k]=!0,c.addClass("widget-dirty"),j.prop("disabled",!1).val(wpWidgets.l10n.save)}),c.data("dirty-state-initialized",!0)),d.is(":hidden")?(f>250&&f+30>g&&c.closest("div.widgets-sortables").length&&(h=c.closest("div.widget-liquid-right").length?i?"margin-right":"margin-left":i?"margin-left":"margin-right",m[h]=g-(f+30)+"px",c.css(m)),n.attr("aria-expanded","true"),d.slideDown("fast",function(){c.addClass("open")})):(n.attr("aria-expanded","false"),d.slideUp("fast",function(){c.attr("style",""),c.removeClass("open")}))):l.hasClass("widget-control-save")?(wpWidgets.save(l.closest("div.widget"),0,1,0),b.preventDefault()):l.hasClass("widget-control-remove")?wpWidgets.save(l.closest("div.widget"),1,1,0):l.hasClass("widget-control-close")?(c=l.closest("div.widget"),c.removeClass("open"),n.attr("aria-expanded","false"),wpWidgets.close(c)):"inactive-widgets-control-remove"===l.attr("id")&&(wpWidgets.removeInactiveWidgets(),b.preventDefault())}),h.children(".widget").each(function(){var b=a(this);wpWidgets.appendTitle(this),b.find("p.widget-error").length&&b.find(".widget-action").trigger("click").attr("aria-expanded","true")}),a("#widget-list").children(".widget").draggable({connectToSortable:"div.widgets-sortables",handle:"> .widget-top > .widget-title",distance:2,helper:"clone",zIndex:100,containment:"#wpwrap",refreshPositions:!0,start:function(b,c){var f=a(this).find(".widgets-chooser");c.helper.find("div.widget-description").hide(),d=this.id,f.length&&(a("#wpbody-content").append(f.hide()),c.helper.find(".widgets-chooser").remove(),e.clearWidgetSelection())},stop:function(){c&&a(c).hide(),c=""}}),h.droppable({tolerance:"intersect",over:function(b){var c=a(b.target).parent();wpWidgets.hoveredSidebar&&!c.is(wpWidgets.hoveredSidebar)&&wpWidgets.closeSidebar(b),c.hasClass("closed")&&(wpWidgets.hoveredSidebar=c,c.removeClass("closed").find(".handlediv").attr("aria-expanded","true")),a(this).sortable("refresh")},out:function(a){wpWidgets.hoveredSidebar&&wpWidgets.closeSidebar(a)}}),h.sortable({placeholder:"widget-placeholder",items:"> .widget",handle:"> .widget-top > .widget-title",cursor:"move",distance:2,containment:"#wpwrap",tolerance:"pointer",refreshPositions:!0,start:function(b,c){var d,e=a(this),f=e.parent(),g=c.item.children(".widget-inside");"block"===g.css("display")&&(c.item.removeClass("open"),c.item.find(".widget-top button.widget-action").attr("aria-expanded","false"),g.hide(),a(this).sortable("refreshPositions")),f.hasClass("closed")||(d=c.item.hasClass("ui-draggable")?e.height():1+e.height(),e.css("min-height",d+"px"))},stop:function(e,f){var g,h,i,j,k,l,m=f.item,n=d;return wpWidgets.hoveredSidebar=null,m.hasClass("deleting")?(wpWidgets.save(m,1,0,1),void m.remove()):(g=m.find("input.add_new").val(),h=m.find("input.multi_number").val(),m.attr("style","").removeClass("ui-draggable"),d="",g&&("multi"===g?(m.html(m.html().replace(/<[^<>]+>/g,function(a){return a.replace(/__i__|%i%/g,h)})),m.attr("id",n.replace("__i__",h)),h++,a("div#"+n).find("input.multi_number").val(h)):"single"===g&&(m.attr("id","new-"+n),c="div#"+n),wpWidgets.save(m,0,0,1),m.find("input.add_new").val(""),b.trigger("widget-added",[m])),i=m.parent(),i.parent().hasClass("closed")&&(i.parent().removeClass("closed").find(".handlediv").attr("aria-expanded","true"),j=i.children(".widget"),j.length>1&&(k=j.get(0),l=m.get(0),k.id&&l.id&&k.id!==l.id&&a(k).before(m))),void(g?m.find(".widget-action").trigger("click"):wpWidgets.saveOrder(i.attr("id"))))},activate:function(){a(this).parent().addClass("widget-hover")},deactivate:function(){a(this).css("min-height","").parent().removeClass("widget-hover")},receive:function(b,c){var d=a(c.sender);return this.id.indexOf("orphaned_widgets")>-1?void d.sortable("cancel"):void(d.attr("id").indexOf("orphaned_widgets")>-1&&!d.children(".widget").length&&d.parents(".orphan-sidebar").slideUp(400,function(){a(this).remove()}))}}).sortable("option","connectWith","div.widgets-sortables"),a("#available-widgets").droppable({tolerance:"pointer",accept:function(b){return"widget-list"!==a(b).parent().attr("id")},drop:function(b,c){c.draggable.addClass("deleting"),a("#removing-widget").hide().children("span").empty()},over:function(b,c){c.draggable.addClass("deleting"),a("div.widget-placeholder").hide(),c.draggable.hasClass("ui-sortable-helper")&&a("#removing-widget").show().children("span").html(c.draggable.find("div.widget-title").children("h3").html())},out:function(b,c){c.draggable.removeClass("deleting"),a("div.widget-placeholder").show(),a("#removing-widget").hide().children("span").empty()}}),a("#widgets-right .widgets-holder-wrap").each(function(b,c){var d=a(c),e=d.find(".sidebar-name h2").text(),f=d.find(".sidebar-name").data("add-to"),h=d.find(".widgets-sortables").attr("id"),i=a("<li>"),j=a("<button>",{type:"button","aria-pressed":"false","class":"widgets-chooser-button","aria-label":f}).text(a.trim(e));i.append(j),0===b&&(i.addClass("widgets-chooser-selected"),j.attr("aria-pressed","true")),g.append(i),i.data("sidebarId",h)}),a("#available-widgets .widget .widget-top").on("click.widgets-chooser",function(){var b=a(this).closest(".widget"),c=a(this).find(".widget-action"),d=g.find(".widgets-chooser-button");b.hasClass("widget-in-question")||a("#widgets-left").hasClass("chooser")?(c.attr("aria-expanded","false"),e.closeChooser()):(e.clearWidgetSelection(),a("#widgets-left").addClass("chooser"),b.addClass("widget-in-question").children(".widget-description").after(f),f.slideDown(300,function(){c.attr("aria-expanded","true")}),d.on("click.widgets-chooser",function(){g.find(".widgets-chooser-selected").removeClass("widgets-chooser-selected"),d.attr("aria-pressed","false"),a(this).attr("aria-pressed","true").closest("li").addClass("widgets-chooser-selected")}))}),f.on("click.widgets-chooser",function(b){var c=a(b.target);c.hasClass("button-primary")?(e.addWidget(f),e.closeChooser()):c.hasClass("widgets-chooser-cancel")&&e.closeChooser()}).on("keyup.widgets-chooser",function(b){b.which===a.ui.keyCode.ESCAPE&&e.closeChooser()})},saveOrder:function(b){var c={action:"widgets-order",savewidgets:a("#_wpnonce_widgets").val(),sidebars:[]};b&&a("#"+b).find(".spinner:first").addClass("is-active"),a("div.widgets-sortables").each(function(){a(this).sortable&&(c["sidebars["+a(this).attr("id")+"]"]=a(this).sortable("toArray").join(","))}),a.post(ajaxurl,c,function(){a("#inactive-widgets-control-remove").prop("disabled",!a("#wp_inactive_widgets .widget").length),a(".spinner").removeClass("is-active")})},save:function(c,d,e,f){var g,h,i=this,j=c.closest("div.widgets-sortables").attr("id"),k=c.find("form"),l=c.find("input.add_new").val();(d||l||!k.prop("checkValidity")||k[0].checkValidity())&&(g=k.serialize(),c=a(c),a(".spinner",c).addClass("is-active"),h={action:"save-widget",savewidgets:a("#_wpnonce_widgets").val(),sidebar:j},d&&(h.delete_widget=1),g+="&"+a.param(h),a.post(ajaxurl,g,function(g){var h=a("input.widget-id",c).val();d?(a("input.widget_number",c).val()||a("#available-widgets").find("input.widget-id").each(function(){a(this).val()===h&&a(this).closest("div.widget").show()}),e?(f=0,c.slideUp("fast",function(){a(this).remove(),wpWidgets.saveOrder(),delete i.dirtyWidgets[h]})):(c.remove(),delete i.dirtyWidgets[h],"wp_inactive_widgets"===j&&a("#inactive-widgets-control-remove").prop("disabled",!a("#wp_inactive_widgets .widget").length))):(a(".spinner").removeClass("is-active"),g&&g.length>2&&(a("div.widget-content",c).html(g),wpWidgets.appendTitle(c),c.find(".widget-control-save").prop("disabled",!0).val(wpWidgets.l10n.saved),c.removeClass("widget-dirty"),delete i.dirtyWidgets[h],b.trigger("widget-updated",[c]),"wp_inactive_widgets"===j&&a("#inactive-widgets-control-remove").prop("disabled",!a("#wp_inactive_widgets .widget").length))),f&&wpWidgets.saveOrder()}))},removeInactiveWidgets:function(){var b,c,d=a(".remove-inactive-widgets"),e=this;a(".spinner",d).addClass("is-active"),b={action:"delete-inactive-widgets",removeinactivewidgets:a("#_wpnonce_remove_inactive_widgets").val()},c=a.param(b),a.post(ajaxurl,c,function(){a("#wp_inactive_widgets .widget").each(function(){var b=a(this);delete e.dirtyWidgets[b.find("input.widget-id").val()],b.remove()}),a("#inactive-widgets-control-remove").prop("disabled",!0),a(".spinner",d).removeClass("is-active")})},appendTitle:function(b){var c=a('input[id*="-title"]',b).val()||"";c&&(c=": "+c.replace(/<[^<>]+>/g,"").replace(/</g,"&lt;").replace(/>/g,"&gt;")),a(b).children(".widget-top").children(".widget-title").children().children(".in-widget-title").html(c)},close:function(a){a.children(".widget-inside").slideUp("fast",function(){a.attr("style","").find(".widget-top button.widget-action").attr("aria-expanded","false").focus()})},addWidget:function(c){var d,e,f,g,h,i,j,k=c.find(".widgets-chooser-selected").data("sidebarId"),l=a("#"+k);d=a("#available-widgets").find(".widget-in-question").clone(),e=d.attr("id"),f=d.find("input.add_new").val(),g=d.find("input.multi_number").val(),d.find(".widgets-chooser").remove(),"multi"===f?(d.html(d.html().replace(/<[^<>]+>/g,function(a){return a.replace(/__i__|%i%/g,g)})),d.attr("id",e.replace("__i__",g)),g++,a("#"+e).find("input.multi_number").val(g)):"single"===f&&(d.attr("id","new-"+e),a("#"+e).hide()),l.closest(".widgets-holder-wrap").removeClass("closed").find(".handlediv").attr("aria-expanded","true"),l.append(d),l.sortable("refresh"),wpWidgets.save(d,0,0,1),d.find("input.add_new").val(""),b.trigger("widget-added",[d]),h=a(window).scrollTop(),i=h+a(window).height(),j=l.offset(),j.bottom=j.top+l.outerHeight(),(h>j.bottom||i<j.top)&&a("html, body").animate({scrollTop:j.top-130},200),window.setTimeout(function(){d.find(".widget-title").trigger("click"),window.wp.a11y.speak(wpWidgets.l10n.widgetAdded,"assertive")},250)},closeChooser:function(){var b=this,c=a("#available-widgets .widget-in-question");a(".widgets-chooser").slideUp(200,function(){a("#wpbody-content").append(this),b.clearWidgetSelection(),c.find(".widget-action").attr("aria-expanded","false").focus()})},clearWidgetSelection:function(){a("#widgets-left").removeClass("chooser"),a(".widget-in-question").removeClass("widget-in-question")},closeSidebar:function(b){this.hoveredSidebar.addClass("closed").find(".handlediv").attr("aria-expanded","false"),a(b.target).css("min-height",""),this.hoveredSidebar=null}},b.ready(function(){wpWidgets.init()})}(jQuery);