!function(a,b){var c,d,e,f=wp.customize;f.OverlayNotification=f.Notification.extend({loading:!1,initialize:function(a,b){var c=this;f.Notification.prototype.initialize.call(c,a,b),c.containerClasses+=" notification-overlay",c.loading&&(c.containerClasses+=" notification-loading")},render:function(){var a=f.Notification.prototype.render.call(this);return a.on("keydown",_.bind(this.handleEscape,this)),a},handleEscape:function(a){var b=this;27===a.which&&(a.stopPropagation(),b.dismissible&&b.parent&&b.parent.remove(b.code))}}),f.Notifications=f.Values.extend({alt:!1,defaultConstructor:f.Notification,initialize:function(a){var b=this;f.Values.prototype.initialize.call(b,a),_.bindAll(b,"constrainFocus"),b._addedIncrement=0,b._addedOrder={},b.bind("add",function(a){b.trigger("change",a)}),b.bind("removed",function(a){b.trigger("change",a)})},count:function(){return _.size(this._value)},add:function(a,b){var c,d,e=this;return"string"==typeof a?(c=a,d=b):(c=a.code,d=a),e.has(c)||(e._addedIncrement+=1,e._addedOrder[c]=e._addedIncrement),f.Values.prototype.add.call(e,c,d)},remove:function(a){var b=this;return delete b._addedOrder[a],f.Values.prototype.remove.call(this,a)},get:function(a){var b,c,d,e=this;return b=_.values(e._value),d=_.extend({sort:!1},a),d.sort&&(c={error:4,warning:3,success:2,info:1},b.sort(function(a,b){var d=0,f=0;return _.isUndefined(c[a.type])||(d=c[a.type]),_.isUndefined(c[b.type])||(f=c[b.type]),d!==f?f-d:e._addedOrder[b.code]-e._addedOrder[a.code]})),b},render:function(){var a,c,d,e,g=this,h=!1,i=[],j={};g.container&&g.container.length&&(a=g.get({sort:!0}),g.container.toggle(0!==a.length),g.container.is(g.previousContainer)&&_.isEqual(a,g.previousNotifications)||(d=g.container.children("ul").first(),d.length||(d=b("<ul></ul>"),g.container.append(d)),d.find("> [data-code]").remove(),_.each(g.previousNotifications,function(a){j[a.code]=a}),_.each(a,function(a){var c;!wp.a11y||j[a.code]&&_.isEqual(a.message,j[a.code].message)||wp.a11y.speak(a.message,"assertive"),c=b(a.render()),a.container=c,d.append(c),a.extended(f.OverlayNotification)&&i.push(a)}),c=Boolean(i.length),g.previousNotifications&&(h=Boolean(_.find(g.previousNotifications,function(a){return a.extended(f.OverlayNotification)}))),c!==h&&(b(document.body).toggleClass("customize-loading",c),g.container.toggleClass("has-overlay-notifications",c),c?(g.previousActiveElement=document.activeElement,b(document).on("keydown",g.constrainFocus)):b(document).off("keydown",g.constrainFocus)),c?(g.focusContainer=i[i.length-1].container,g.focusContainer.prop("tabIndex",-1),e=g.focusContainer.find(":focusable"),e.length?e.first().focus():g.focusContainer.focus()):g.previousActiveElement&&(b(g.previousActiveElement).focus(),g.previousActiveElement=null),g.previousNotifications=a,g.previousContainer=g.container,g.trigger("rendered")))},constrainFocus:function(a){var c,d=this;a.stopPropagation(),9===a.which&&(c=d.focusContainer.find(":focusable"),0===c.length&&(c=d.focusContainer),b.contains(d.focusContainer[0],a.target)&&b.contains(d.focusContainer[0],document.activeElement)?c.last().is(a.target)&&!a.shiftKey?(a.preventDefault(),c.first().focus()):c.first().is(a.target)&&a.shiftKey&&(a.preventDefault(),c.last().focus()):(a.preventDefault(),c.first().focus()))}}),f.Setting=f.Value.extend({defaults:{transport:"refresh",dirty:!1},initialize:function(a,b,c){var d,e=this;d=_.extend({previewer:f.previewer},e.defaults,c||{}),f.Value.prototype.initialize.call(e,b,d),e.id=a,e._dirty=d.dirty,e.notifications=new f.Notifications,e.bind(e.preview)},preview:function(){var a,b=this;a=b.transport,"postMessage"!==a||f.state("previewerAlive").get()||(a="refresh"),"postMessage"===a?b.previewer.send("setting",[b.id,b()]):"refresh"===a&&b.previewer.refresh()},findControls:function(){var a=this,b=[];return f.control.each(function(c){_.each(c.settings,function(d){d.id===a.id&&b.push(c)})}),b}}),f._latestRevision=0,f._lastSavedRevision=0,f._latestSettingRevisions={},f.bind("change",function(a){f._latestRevision+=1,f._latestSettingRevisions[a.id]=f._latestRevision}),f.bind("ready",function(){f.bind("add",function(a){a._dirty&&(f._latestRevision+=1,f._latestSettingRevisions[a.id]=f._latestRevision)})}),f.dirtyValues=function(a){var b={};return f.each(function(c){var d;c._dirty&&(d=f._latestSettingRevisions[c.id],f.state("changesetStatus").get()&&a&&a.unsaved&&(_.isUndefined(d)||d<=f._lastSavedRevision)||(b[c.id]=c.get()))}),b},f.requestChangesetUpdate=function(a,c){var d,e,g,h,i={};return d=new b.Deferred,0!==f.state("processing").get()?(d.reject("already_processing"),d.promise()):(h=_.extend({title:null,date:null,autosave:!1,force:!1},c),a&&_.extend(i,a),_.each(f.dirtyValues({unsaved:!0}),function(b,c){a&&null===a[c]||(i[c]=_.extend({},i[c]||{},{value:b}))}),f.trigger("changeset-save",i,h),!h.force&&_.isEmpty(i)&&null===h.title&&null===h.date?(d.resolve({}),d.promise()):h.status?d.reject({code:"illegal_status_in_changeset_update"}).promise():h.date&&h.autosave?d.reject({code:"illegal_autosave_with_date_gmt"}).promise():(f.state("processing").set(f.state("processing").get()+1),d.always(function(){f.state("processing").set(f.state("processing").get()-1)}),g=f.previewer.query({excludeCustomizedSaved:!0}),delete g.customized,_.extend(g,{nonce:f.settings.nonce.save,customize_theme:f.settings.theme.stylesheet,customize_changeset_data:JSON.stringify(i)}),null!==h.title&&(g.customize_changeset_title=h.title),null!==h.date&&(g.customize_changeset_date=h.date),!1!==h.autosave&&(g.customize_changeset_autosave="true"),f.trigger("save-request-params",g),e=wp.ajax.post("customize_save",g),e.done(function(a){var b={};f._lastSavedRevision=Math.max(f._latestRevision,f._lastSavedRevision),f.state("changesetStatus").set(a.changeset_status),a.changeset_date&&f.state("changesetDate").set(a.changeset_date),d.resolve(a),f.trigger("changeset-saved",a),a.setting_validities&&_.each(a.setting_validities,function(a,c){!0===a&&_.isObject(i[c])&&!_.isUndefined(i[c].value)&&(b[c]=i[c].value)}),f.previewer.send("changeset-saved",_.extend({},a,{saved_changeset_values:b}))}),e.fail(function(a){d.reject(a),f.trigger("changeset-error",a)}),e.always(function(a){a.setting_validities&&f._handleSettingValidities({settingValidities:a.setting_validities})}),d.promise()))},f.utils.bubbleChildValueChanges=function(a,c){b.each(c,function(b,c){a[c].bind(function(b,c){a.parent&&b!==c&&a.parent.trigger("change",a)})})},d=function(a){var b,c,d,e;b=this,a=a||{},d=function(){var a;a=(b.extended(f.Panel)||b.extended(f.Section))&&b.expanded&&b.expanded()?b.contentContainer:b.container,e=a.find(".control-focus:first"),0===e.length&&(e=a.find("input, select, textarea, button, object, a[href], [tabindex]").filter(":visible").first()),e.focus()},a.completeCallback?(c=a.completeCallback,a.completeCallback=function(){d(),c()}):a.completeCallback=d,f.state("paneVisible").set(!0),b.expand?b.expand(a):a.completeCallback()},f.utils.prioritySort=function(a,b){return a.priority()===b.priority()&&"number"==typeof a.params.instanceNumber&&"number"==typeof b.params.instanceNumber?a.params.instanceNumber-b.params.instanceNumber:a.priority()-b.priority()},f.utils.isKeydownButNotEnterEvent=function(a){return"keydown"===a.type&&13!==a.which},f.utils.areElementListsEqual=function(a,c){var d=a.length===c.length&&-1===_.indexOf(_.map(_.zip(a,c),function(a){return b(a[0]).is(a[1])}),!1);return d},f.utils.highlightButton=function(a,b){function c(){f=!0}var d,e="button-see-me",f=!1;return d=_.extend({delay:0,focusTarget:a},b),d.focusTarget.on("focusin",c),setTimeout(function(){d.focusTarget.off("focusin",c),f||(a.addClass(e),a.one("animationend",function(){a.removeClass(e)}))},d.delay),c},f.utils.getCurrentTimestamp=function(){var a,b,c;return b=_.now(),a=new Date(f.settings.initialServerDate.replace(/-/g,"/")),c=b-f.settings.initialClientTimestamp,c+=f.settings.initialClientTimestamp-f.settings.initialServerTimestamp,a.setTime(a.getTime()+c),a.getTime()},f.utils.getRemainingTime=function(a){var b,c,d=1e3;return c=a instanceof Date?a.getTime():"string"==typeof a?new Date(a.replace(/-/g,"/")).getTime():a,b=c-f.utils.getCurrentTimestamp(),b=Math.ceil(b/d)},e=function(){var a,b,c;return a=document.createElement("div"),b={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"},c=_.find(_.keys(b),function(b){return!_.isUndefined(a.style[b])}),c?b[c]:null}(),c=f.Class.extend({defaultActiveArguments:{duration:"fast",completeCallback:b.noop},defaultExpandedArguments:{duration:"fast",completeCallback:b.noop},containerType:"container",defaults:{title:"",description:"",priority:100,type:"default",content:null,active:!0,instanceNumber:null},initialize:function(a,d){var e=this;e.id=a,c.instanceCounter||(c.instanceCounter=0),c.instanceCounter++,b.extend(e,{params:_.defaults(d.params||d,e.defaults)}),e.params.instanceNumber||(e.params.instanceNumber=c.instanceCounter),e.notifications=new f.Notifications,e.templateSelector=e.params.templateId||"customize-"+e.containerType+"-"+e.params.type,e.container=b(e.params.content),0===e.container.length&&(e.container=b(e.getContainer())),e.headContainer=e.container,e.contentContainer=e.getContent(),e.container=e.container.add(e.contentContainer),e.deferred={embedded:new b.Deferred},e.priority=new f.Value,e.active=new f.Value,e.activeArgumentsQueue=[],e.expanded=new f.Value,e.expandedArgumentsQueue=[],e.active.bind(function(a){var c=e.activeArgumentsQueue.shift();c=b.extend({},e.defaultActiveArguments,c),a=a&&e.isContextuallyActive(),e.onChangeActive(a,c)}),e.expanded.bind(function(a){var c=e.expandedArgumentsQueue.shift();c=b.extend({},e.defaultExpandedArguments,c),e.onChangeExpanded(a,c)}),e.deferred.embedded.done(function(){e.setupNotifications(),e.attachEvents()}),f.utils.bubbleChildValueChanges(e,["priority","active"]),e.priority.set(e.params.priority),e.active.set(e.params.active),e.expanded.set(!1)},getNotificationsContainerElement:function(){var a=this;return a.contentContainer.find(".customize-control-notifications-container:first")},setupNotifications:function(){var a,b=this;b.notifications.container=b.getNotificationsContainerElement(),a=function(){b.expanded.get()&&b.notifications.render()},b.expanded.bind(a),a(),b.notifications.bind("change",_.debounce(a))},ready:function(){},_children:function(a,b){var c=this,d=[];return f[b].each(function(b){b[a].get()===c.id&&d.push(b)}),d.sort(f.utils.prioritySort),d},isContextuallyActive:function(){throw new Error("Container.isContextuallyActive() must be overridden in a subclass.")},onChangeActive:function(a,c){var d,e,g=this,h=g.headContainer;return c.unchanged?void(c.completeCallback&&c.completeCallback()):(d="resolved"===f.previewer.deferred.active.state()?c.duration:0,g.extended(f.Panel)&&(f.panel.each(function(a){a!==g&&a.expanded()&&(e=a,d=0)}),a||_.each(g.sections(),function(a){a.collapse({duration:0})})),void(b.contains(document,h.get(0))?a?h.slideDown(d,c.completeCallback):g.expanded()?g.collapse({duration:d,completeCallback:function(){h.slideUp(d,c.completeCallback)}}):h.slideUp(d,c.completeCallback):(h.toggle(a),c.completeCallback&&c.completeCallback())))},_toggleActive:function(a,b){var c=this;return b=b||{},a&&this.active.get()||!a&&!this.active.get()?(b.unchanged=!0,c.onChangeActive(c.active.get(),b),!1):(b.unchanged=!1,this.activeArgumentsQueue.push(b),this.active.set(a),!0)},activate:function(a){return this._toggleActive(!0,a)},deactivate:function(a){return this._toggleActive(!1,a)},onChangeExpanded:function(){throw new Error("Must override with subclass.")},_toggleExpanded:function(a,b){var c,d=this;return b=b||{},c=b.completeCallback,!(a&&!d.active())&&(f.state("paneVisible").set(!0),b.completeCallback=function(){c&&c.apply(d,arguments),a?d.container.trigger("expanded"):d.container.trigger("collapsed")},a&&d.expanded.get()||!a&&!d.expanded.get()?(b.unchanged=!0,d.onChangeExpanded(d.expanded.get(),b),!1):(b.unchanged=!1,d.expandedArgumentsQueue.push(b),d.expanded.set(a),!0))},expand:function(a){return this._toggleExpanded(!0,a)},collapse:function(a){return this._toggleExpanded(!1,a)},_animateChangeExpanded:function(a){if(!e)return void(a&&a());var c,d,g,h=this,i=h.contentContainer,j=i.closest(".wp-full-overlay");c=j.add(i),g=!h.panel||""===h.panel()||!!f.panel(h.panel()).contentContainer.hasClass("skip-transition"),g&&(c=c.add("#customize-info, .customize-pane-parent")),d=function(f){2===f.eventPhase&&b(f.target).is(i)&&(i.off(e,d),c.removeClass("busy"),a&&a())},i.on(e,d),c.addClass("busy"),_.defer(function(){var a=i.closest(".wp-full-overlay-sidebar-content"),b=a.scrollTop(),c=i.data("previous-scrollTop")||0,d=h.expanded();d&&0<b?(i.css("top",b+"px"),i.data("previous-scrollTop",b)):!d&&0<b+c&&(i.css("top",c-b+"px"),a.scrollTop(c))})},focus:d,getContainer:function(){var a,c=this;return a=0!==b("#tmpl-"+c.templateSelector).length?wp.template(c.templateSelector):wp.template("customize-"+c.containerType+"-default"),a&&c.container?b.trim(a(_.extend({id:c.id},c.params))):"<li></li>"},getContent:function(){var a=this,b=a.container,c=b.find(".accordion-section-content, .control-panel-content").first(),d="sub-"+b.attr("id"),e=d,f=b.attr("aria-owns");return f&&(e=e+" "+f),b.attr("aria-owns",e),c.detach().attr({id:d,"class":"customize-pane-child "+c.attr("class")+" "+b.attr("class")})}}),f.Section=c.extend({containerType:"section",containerParent:"#customize-theme-controls",containerPaneParent:".customize-pane-parent",defaults:{title:"",description:"",priority:100,type:"default",content:null,active:!0,instanceNumber:null,panel:null,customizeAction:""},initialize:function(a,d){var e,g=this;e=d.params||d,e.type||_.find(f.sectionConstructor,function(a,b){return a===g.constructor&&(e.type=b,!0)}),c.prototype.initialize.call(g,a,e),g.id=a,g.panel=new f.Value,g.panel.bind(function(a){b(g.headContainer).toggleClass("control-subsection",!!a)}),g.panel.set(g.params.panel||""),f.utils.bubbleChildValueChanges(g,["panel"]),g.embed(),g.deferred.embedded.done(function(){g.ready()})},embed:function(){var a,b=this;b.containerParent=f.ensure(b.containerParent),a=function(a){var c;a?f.panel(a,function(a){a.deferred.embedded.done(function(){c=a.contentContainer,b.headContainer.parent().is(c)||c.append(b.headContainer),b.contentContainer.parent().is(b.headContainer)||b.containerParent.append(b.contentContainer),b.deferred.embedded.resolve()})}):(c=f.ensure(b.containerPaneParent),b.headContainer.parent().is(c)||c.append(b.headContainer),b.contentContainer.parent().is(b.headContainer)||b.containerParent.append(b.contentContainer),b.deferred.embedded.resolve())},b.panel.bind(a),a(b.panel.get())},attachEvents:function(){var a,c,d=this;d.container.hasClass("cannot-expand")||(d.container.find(".accordion-section-title, .customize-section-back").on("click keydown",function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),d.expanded()?d.collapse():d.expand())}),d.container.find(".customize-section-title .customize-help-toggle").on("click",function(){a=d.container.find(".section-meta"),a.hasClass("cannot-expand")||(c=a.find(".customize-section-description:first"),c.toggleClass("open"),c.slideToggle(d.defaultExpandedArguments.duration,function(){c.trigger("toggled")}),b(this).attr("aria-expanded",function(a,b){return"true"===b?"false":"true"}))}))},isContextuallyActive:function(){var a=this,b=a.controls(),c=0;return _(b).each(function(a){a.active()&&(c+=1)}),0!==c},controls:function(){return this._children("section","control")},onChangeExpanded:function(a,c){var d,e,g=this,h=g.headContainer.closest(".wp-full-overlay-sidebar-content"),i=g.contentContainer,j=g.headContainer.closest(".wp-full-overlay"),k=i.find(".customize-section-back"),l=g.headContainer.find(".accordion-section-title").first();a&&!i.hasClass("open")?(d=c.unchanged?c.completeCallback:b.proxy(function(){g._animateChangeExpanded(function(){l.attr("tabindex","-1"),k.attr("tabindex","0"),k.focus(),i.css("top",""),h.scrollTop(0),c.completeCallback&&c.completeCallback()}),i.addClass("open"),j.addClass("section-open"),f.state("expandedSection").set(g)},this),c.allowMultiple||f.section.each(function(a){a!==g&&a.collapse({duration:c.duration})}),g.panel()?f.panel(g.panel()).expand({duration:c.duration,completeCallback:d}):(c.allowMultiple||f.panel.each(function(a){a.collapse()}),d())):!a&&i.hasClass("open")?(g.panel()&&(e=f.panel(g.panel()),e.contentContainer.hasClass("skip-transition")&&e.collapse()),g._animateChangeExpanded(function(){k.attr("tabindex","-1"),l.attr("tabindex","0"),l.focus(),i.css("top",""),c.completeCallback&&c.completeCallback()}),i.removeClass("open"),j.removeClass("section-open"),g===f.state("expandedSection").get()&&f.state("expandedSection").set(!1)):c.completeCallback&&c.completeCallback()}}),f.ThemesSection=f.Section.extend({currentTheme:"",overlay:"",template:"",screenshotQueue:null,$window:null,$body:null,loaded:0,loading:!1,fullyLoaded:!1,term:"",tags:"",nextTerm:"",nextTags:"",filtersHeight:0,headerContainer:null,updateCountDebounced:null,initialize:function(a,c){var d=this;d.headerContainer=b(),d.$window=b(window),d.$body=b(document.body),f.Section.prototype.initialize.call(d,a,c),d.updateCountDebounced=_.debounce(d.updateCount,500)},embed:function(){var a,b=this;a=function(a){var c;f.panel(a,function(a){a.deferred.embedded.done(function(){c=a.contentContainer,b.headContainer.parent().is(c)||c.find(".customize-themes-full-container-container").before(b.headContainer),b.contentContainer.parent().is(b.headContainer)||b.containerParent.append(b.contentContainer),b.deferred.embedded.resolve()})})},b.panel.bind(a),a(b.panel.get())},ready:function(){var a=this;a.overlay=a.container.find(".theme-overlay"),a.template=wp.template("customize-themes-details-view"),a.container.on("keydown",function(b){a.overlay.find(".theme-wrap").is(":visible")&&(39===b.keyCode&&a.nextTheme(),37===b.keyCode&&a.previousTheme(),27===b.keyCode&&(a.$body.hasClass("modal-open")?a.closeDetails():a.headerContainer.find(".customize-themes-section-title").focus(),b.stopPropagation()))}),a.renderScreenshots=_.throttle(a.renderScreenshots,100),_.bindAll(a,"renderScreenshots","loadMore","checkTerm","filtersChecked")},isContextuallyActive:function(){return this.active()},attachEvents:function(){function a(){var a=d.headerContainer.find(".customize-themes-section-title");a.toggleClass("selected",d.expanded()),a.attr("aria-expanded",d.expanded()?"true":"false"),d.expanded()||a.removeClass("details-open")}var c,d=this;d.container.find(".customize-section-back").on("click keydown",function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),d.collapse())}),d.headerContainer=b("#accordion-section-"+d.id),d.headerContainer.on("click",".customize-themes-section-title",function(){d.headerContainer.find(".filter-details").length&&(d.headerContainer.find(".customize-themes-section-title").toggleClass("details-open").attr("aria-expanded",function(a,b){return"true"===b?"false":"true"}),d.headerContainer.find(".filter-details").slideToggle(180)),d.expanded()||d.expand()}),d.container.on("click",".theme-actions .preview-theme",function(){f.panel("themes").loadThemePreview(b(this).data("slug"))}),d.container.on("click",".left",function(){d.previousTheme()}),d.container.on("click",".right",function(){d.nextTheme()}),d.container.on("click",".theme-backdrop, .close",function(){d.closeDetails()}),"local"===d.params.filter_type?d.container.on("input",".wp-filter-search-themes",function(a){d.filterSearch(a.currentTarget.value)}):"remote"===d.params.filter_type&&(c=_.debounce(d.checkTerm,500),d.contentContainer.on("input",".wp-filter-search",function(){f.panel("themes").expanded()&&(c(d),d.expanded()||d.expand())}),d.contentContainer.on("click",".filter-group input",function(){d.filtersChecked(),d.checkTerm(d)})),d.contentContainer.on("click",".feature-filter-toggle",function(a){var c=b(".customize-themes-full-container"),e=b(a.currentTarget);if(d.filtersHeight=e.parent().next(".filter-drawer").height(),!(0<c.scrollTop()&&(c.animate({scrollTop:0},400),e.hasClass("open"))))if(e.toggleClass("open").attr("aria-expanded",function(a,b){return"true"===b?"false":"true"}).parent().next(".filter-drawer").slideToggle(180,"linear"),e.hasClass("open")){var f=1018<window.innerWidth?50:76;d.contentContainer.find(".themes").css("margin-top",d.filtersHeight+f)}else d.contentContainer.find(".themes").css("margin-top",0)}),d.contentContainer.on("click",".no-themes-local .search-dotorg-themes",function(){f.section("wporg_themes").focus()}),d.expanded.bind(a),a(),f.bind("ready",function(){d.contentContainer=d.container.find(".customize-themes-section"),d.contentContainer.appendTo(b(".customize-themes-full-container")),d.container.add(d.headerContainer)})},onChangeExpanded:function(a,b){function c(){0===d.loaded&&d.loadThemes(),f.section.each(function(a){var c;a!==d&&"themes"===a.params.type&&(c=a.contentContainer.find(".wp-filter-search").val(),d.contentContainer.find(".wp-filter-search").val(c),""===c&&""!==d.term&&"local"!==d.params.filter_type?(d.term="",d.initializeNewQuery(d.term,d.tags)):"remote"===d.params.filter_type?d.checkTerm(d):"local"===d.params.filter_type&&d.filterSearch(c),a.collapse({duration:b.duration}))}),d.contentContainer.addClass("current-section"),e.scrollTop(),e.on("scroll",_.throttle(d.renderScreenshots,300)),e.on("scroll",_.throttle(d.loadMore,300)),b.completeCallback&&b.completeCallback(),d.updateCount()}var d=this,e=d.contentContainer.closest(".customize-themes-full-container");return b.unchanged?void(b.completeCallback&&b.completeCallback()):void(a?d.panel()&&f.panel.has(d.panel())?f.panel(d.panel()).expand({duration:b.duration,completeCallback:c}):c():(d.contentContainer.removeClass("current-section"),d.headerContainer.find(".filter-details").slideUp(180),e.off("scroll"),b.completeCallback&&b.completeCallback()))},getContent:function(){return this.container.find(".control-section-content")},loadThemes:function(){var a,b,c,d=this;d.loading||(b=Math.ceil(d.loaded/100)+1,a={nonce:f.settings.nonce.switch_themes,wp_customize:"on",theme_action:d.params.action,customized_theme:f.settings.theme.stylesheet,page:b},"remote"===d.params.filter_type&&(a.search=d.term,a.tags=d.tags),d.headContainer.closest(".wp-full-overlay").addClass("loading"),d.loading=!0,d.container.find(".no-themes").hide(),c=wp.ajax.post("customize_load_themes",a),c.done(function(a){var c=a.themes;return""!==d.nextTerm||""!==d.nextTags?(d.nextTerm&&(d.term=d.nextTerm),d.nextTags&&(d.tags=d.nextTags),d.nextTerm="",d.nextTags="",d.loading=!1,void d.loadThemes()):(0!==c.length?(d.loadControls(c,b),1===b&&(_.each(d.controls().slice(0,3),function(a){var b,c=a.params.theme.screenshot[0];c&&(b=new Image,b.src=c)}),"local"!==d.params.filter_type&&wp.a11y.speak(f.settings.l10n.themeSearchResults.replace("%d",a.info.results))),_.delay(d.renderScreenshots,100),("local"===d.params.filter_type||100>c.length)&&(d.fullyLoaded=!0)):0===d.loaded?(d.container.find(".no-themes").show(),wp.a11y.speak(d.container.find(".no-themes").text())):d.fullyLoaded=!0,"local"===d.params.filter_type?d.updateCount():d.updateCount(a.info.results),d.container.find(".unexpected-error").hide(),d.headContainer.closest(".wp-full-overlay").removeClass("loading"),void(d.loading=!1))}),c.fail(function(a){"undefined"==typeof a?(d.container.find(".unexpected-error").show(),wp.a11y.speak(d.container.find(".unexpected-error").text())):"undefined"!=typeof console&&console.error&&console.error(a),d.headContainer.closest(".wp-full-overlay").removeClass("loading"),d.loading=!1}))},loadControls:function(a,b){var c=[],d=this;_.each(a,function(a){var b=new f.controlConstructor.theme(d.params.action+"_theme_"+a.id,{type:"theme",section:d.params.id,theme:a,priority:d.loaded+1});f.control.add(b),c.push(b),d.loaded=d.loaded+1}),1!==b&&Array.prototype.push.apply(d.screenshotQueue,c)},loadMore:function(){var a,b,c,d=this;d.fullyLoaded||d.loading||(a=d.container.closest(".customize-themes-full-container"),b=a.scrollTop()+a.height(),c=a.prop("scrollHeight")-3e3,b>c&&d.loadThemes())},filterSearch:function(a){var b,c=0,d=!1,e=this,g=f.section.has("wporg_themes")&&"remote"!==e.params.filter_type?".no-themes-local":".no-themes",h=e.controls();e.loading||(b=a.toLowerCase().trim().replace(/-/g," ").split(" "),_.each(h,function(a){d=a.filter(b),d&&(c+=1)}),0===c?(e.container.find(g).show(),wp.a11y.speak(e.container.find(g).text())):e.container.find(g).hide(),e.renderScreenshots(),f.reflowPaneContents(),e.updateCountDebounced(c))},checkTerm:function(a){var b;"remote"===a.params.filter_type&&(b=a.contentContainer.find(".wp-filter-search").val(),a.term!==b.trim()&&a.initializeNewQuery(b,a.tags))},filtersChecked:function(){var a=this,c=a.container.find(".filter-group").find(":checkbox"),d=[];_.each(c.filter(":checked"),function(a){d.push(b(a).prop("value"))}),0===d.length?(d="",a.contentContainer.find(".feature-filter-toggle .filter-count-0").show(),a.contentContainer.find(".feature-filter-toggle .filter-count-filters").hide()):(a.contentContainer.find(".feature-filter-toggle .theme-filter-count").text(d.length),a.contentContainer.find(".feature-filter-toggle .filter-count-0").hide(),a.contentContainer.find(".feature-filter-toggle .filter-count-filters").show()),_.isEqual(a.tags,d)||(a.loading?a.nextTags=d:"remote"===a.params.filter_type?a.initializeNewQuery(a.term,d):"local"===a.params.filter_type&&a.filterSearch(d.join(" ")))},initializeNewQuery:function(a,b){var c=this;_.each(c.controls(),function(a){a.container.remove(),f.control.remove(a.id)}),c.loaded=0,c.fullyLoaded=!1,c.screenshotQueue=null,c.loading?(c.nextTerm=a,c.nextTags=b):(c.term=a,c.tags=b,c.loadThemes()),c.expanded()||c.expand()},renderScreenshots:function(){var a=this;null!==a.screenshotQueue&&0!==a.screenshotQueue.length||(a.screenshotQueue=_.filter(a.controls(),function(a){return!a.screenshotRendered})),a.screenshotQueue.length&&(a.screenshotQueue=_.filter(a.screenshotQueue,function(b){var c=b.container.find(".theme-screenshot"),d=c.find("img");if(!d.length)return!1;if(d.is(":hidden"))return!0;var e=a.$window.scrollTop(),f=e+a.$window.height(),g=d.offset().top,h=c.height(),i=g+h,j=3*h,k=i>=e-j&&g<=f+j;return k&&b.container.trigger("render-screenshot"),!k}))},getVisibleCount:function(){return this.contentContainer.find("li.customize-control:visible").length},updateCount:function(a){var b,c,d=this;a||0===a||(a=d.getVisibleCount()),c=d.contentContainer.find(".themes-displayed"),b=d.contentContainer.find(".theme-count"),0===a?b.text("0"):(c.fadeOut(180,function(){b.text(a),c.fadeIn(180)}),wp.a11y.speak(f.settings.l10n.announceThemeCount.replace("%d",a)))},nextTheme:function(){var a=this;a.getNextTheme()&&a.showDetails(a.getNextTheme(),function(){a.overlay.find(".right").focus()})},getNextTheme:function(){var a,b,c,d,e=this;return a=f.control(e.params.action+"_theme_"+e.currentTheme),c=e.controls(),d=_.indexOf(c,a),-1!==d&&(b=c[d+1],!!b&&b.params.theme)},previousTheme:function(){var a=this;a.getPreviousTheme()&&a.showDetails(a.getPreviousTheme(),function(){a.overlay.find(".left").focus()})},getPreviousTheme:function(){var a,b,c,d,e=this;return a=f.control(e.params.action+"_theme_"+e.currentTheme),c=e.controls(),d=_.indexOf(c,a),-1!==d&&(b=c[d-1],!!b&&b.params.theme)},updateLimits:function(){this.getNextTheme()||this.overlay.find(".right").addClass("disabled"),this.getPreviousTheme()||this.overlay.find(".left").addClass("disabled")},loadThemePreview:function(a){return f.ThemesPanel.prototype.loadThemePreview.call(this,a)},showDetails:function(a,b){function c(){return!g.canSwitchTheme(a.id)}function d(){return c()||!1===f.settings.theme._canInstall||!0===f.settings.theme._filesystemCredentialsNeeded}var e=this,g=f.panel("themes");e.currentTheme=a.id,e.overlay.html(e.template(a)).fadeIn("fast").focus(),e.overlay.find("button.preview, button.preview-theme").toggleClass("disabled",c()),e.overlay.find("button.theme-install").toggleClass("disabled",d()),e.$body.addClass("modal-open"),e.containFocus(e.overlay),e.updateLimits(),wp.a11y.speak(f.settings.l10n.announceThemeDetails.replace("%s",a.name)),b&&b()},closeDetails:function(){var a=this;a.$body.removeClass("modal-open"),a.overlay.fadeOut("fast"),f.control(a.params.action+"_theme_"+a.currentTheme).container.find(".theme").focus()},containFocus:function(a){var c;a.on("keydown",function(d){if(9===d.keyCode)return c=b(":tabbable",a),c.last()[0]!==d.target||d.shiftKey?c.first()[0]===d.target&&d.shiftKey?(c.last().focus(),!1):void 0:(c.first().focus(),!1)})}}),f.OuterSection=f.Section.extend({initialize:function(){var a=this;a.containerParent="#customize-outer-theme-controls",a.containerPaneParent=".customize-outer-pane-parent",f.Section.prototype.initialize.apply(a,arguments)},onChangeExpanded:function(a,c){var d,e,g=this,h=g.headContainer.closest(".wp-full-overlay-sidebar-content"),i=g.contentContainer,j=i.find(".customize-section-back"),k=g.headContainer.find(".accordion-section-title").first(),l=b(document.body);l.toggleClass("outer-section-open",a),g.container.toggleClass("open",a),g.container.removeClass("busy"),f.section.each(function(a){"outer"===a.params.type&&a.id!==g.id&&a.container.removeClass("open")}),a&&!i.hasClass("open")?(d=c.unchanged?c.completeCallback:b.proxy(function(){g._animateChangeExpanded(function(){k.attr("tabindex","-1"),j.attr("tabindex","0"),j.focus(),i.css("top",""),h.scrollTop(0),c.completeCallback&&c.completeCallback()}),i.addClass("open")},this),g.panel()?f.panel(g.panel()).expand({duration:c.duration,completeCallback:d}):d()):!a&&i.hasClass("open")?(g.panel()&&(e=f.panel(g.panel()),e.contentContainer.hasClass("skip-transition")&&e.collapse()),g._animateChangeExpanded(function(){j.attr("tabindex","-1"),k.attr("tabindex","0"),k.focus(),i.css("top",""),c.completeCallback&&c.completeCallback()}),i.removeClass("open")):c.completeCallback&&c.completeCallback()}}),f.Panel=c.extend({containerType:"panel",initialize:function(a,b){var d,e=this;d=b.params||b,d.type||_.find(f.panelConstructor,function(a,b){return a===e.constructor&&(d.type=b,!0)}),c.prototype.initialize.call(e,a,d),e.embed(),e.deferred.embedded.done(function(){e.ready()})},embed:function(){var a=this,c=b("#customize-theme-controls"),d=b(".customize-pane-parent");a.headContainer.parent().is(d)||d.append(a.headContainer),a.contentContainer.parent().is(a.headContainer)||c.append(a.contentContainer),a.renderContent(),a.deferred.embedded.resolve()},attachEvents:function(){var a,c=this;c.headContainer.find(".accordion-section-title").on("click keydown",function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),c.expanded()||c.expand())}),c.container.find(".customize-panel-back").on("click keydown",function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),c.expanded()&&c.collapse())}),a=c.container.find(".panel-meta:first"),a.find("> .accordion-section-title .customize-help-toggle").on("click",function(){if(!a.hasClass("cannot-expand")){var d=a.find(".customize-panel-description:first");a.hasClass("open")?(a.toggleClass("open"),d.slideUp(c.defaultExpandedArguments.duration,function(){d.trigger("toggled")}),b(this).attr("aria-expanded",!1)):(d.slideDown(c.defaultExpandedArguments.duration,function(){d.trigger("toggled")}),a.toggleClass("open"),b(this).attr("aria-expanded",!0))}})},sections:function(){return this._children("panel","section")},isContextuallyActive:function(){var a=this,b=a.sections(),c=0;return _(b).each(function(a){a.active()&&a.isContextuallyActive()&&(c+=1)}),0!==c},onChangeExpanded:function(a,b){if(b.unchanged)return void(b.completeCallback&&b.completeCallback());var c,d=this,e=d.contentContainer,g=e.closest(".wp-full-overlay"),h=e.closest(".wp-full-overlay-sidebar-content"),i=d.headContainer.find(".accordion-section-title"),j=e.find(".customize-panel-back"),k=d.sections();a&&!e.hasClass("current-panel")?(f.section.each(function(a){d.id!==a.panel()&&a.collapse({duration:0})}),f.panel.each(function(a){d!==a&&a.collapse({duration:0});
}),d.params.autoExpandSoleSection&&1===k.length&&k[0].active.get()?(e.addClass("current-panel skip-transition"),g.addClass("in-sub-panel"),k[0].expand({completeCallback:b.completeCallback})):(d._animateChangeExpanded(function(){i.attr("tabindex","-1"),j.attr("tabindex","0"),j.focus(),e.css("top",""),h.scrollTop(0),b.completeCallback&&b.completeCallback()}),e.addClass("current-panel"),g.addClass("in-sub-panel")),f.state("expandedPanel").set(d)):!a&&e.hasClass("current-panel")&&(c=e.hasClass("skip-transition"),c?e.removeClass("skip-transition"):d._animateChangeExpanded(function(){i.attr("tabindex","0"),j.attr("tabindex","-1"),i.focus(),e.css("top",""),b.completeCallback&&b.completeCallback()}),g.removeClass("in-sub-panel"),e.removeClass("current-panel"),d===f.state("expandedPanel").get()&&f.state("expandedPanel").set(!1))},renderContent:function(){var a,c=this;a=0!==b("#tmpl-"+c.templateSelector+"-content").length?wp.template(c.templateSelector+"-content"):wp.template("customize-panel-default-content"),a&&c.headContainer&&c.contentContainer.html(a(_.extend({id:c.id},c.params)))}}),f.ThemesPanel=f.Panel.extend({initialize:function(a,b){var c=this;c.installingThemes=[],f.Panel.prototype.initialize.call(c,a,b)},canSwitchTheme:function(a){return!(!a||a!==f.settings.theme.stylesheet)||"publish"===f.state("selectedChangesetStatus").get()&&(""===f.state("changesetStatus").get()||"auto-draft"===f.state("changesetStatus").get())},attachEvents:function(){function a(){c.canSwitchTheme()?c.notifications.remove("theme_switch_unavailable"):c.notifications.add(new f.Notification("theme_switch_unavailable",{message:f.l10n.themePreviewUnavailable,type:"warning"}))}var c=this;f.Panel.prototype.attachEvents.apply(c),f.settings.theme._canInstall&&f.settings.theme._filesystemCredentialsNeeded&&c.notifications.add(new f.Notification("theme_install_unavailable",{message:f.l10n.themeInstallUnavailable,type:"info",dismissible:!0})),a(),f.state("selectedChangesetStatus").bind(a),f.state("changesetStatus").bind(a),c.contentContainer.on("click",".customize-theme",function(){c.collapse()}),c.contentContainer.on("click",".customize-themes-section-title, .customize-themes-mobile-back",function(){b(".wp-full-overlay").toggleClass("showing-themes")}),c.contentContainer.on("click",".theme-install",function(a){c.installTheme(a)}),c.contentContainer.on("click",".update-theme, #update-theme",function(a){a.preventDefault(),a.stopPropagation(),c.updateTheme(a)}),c.contentContainer.on("click",".delete-theme",function(a){c.deleteTheme(a)}),_.bindAll(c,"installTheme","updateTheme")},onChangeExpanded:function(a,b){var c,d,e=this,g=!1;return f.Panel.prototype.onChangeExpanded.apply(this,[a,b]),b.unchanged?void(b.completeCallback&&b.completeCallback()):(c=e.headContainer.closest(".wp-full-overlay"),void(a?(c.addClass("in-themes-panel").delay(200).find(".customize-themes-full-container").addClass("animate"),_.delay(function(){c.addClass("themes-panel-expanded")},200),600<window.innerWidth&&(d=e.sections(),_.each(d,function(a){a.expanded()&&(g=!0)}),!g&&d.length>0&&d[0].expand())):c.removeClass("in-themes-panel themes-panel-expanded").find(".customize-themes-full-container").removeClass("animate")))},installTheme:function(a){var c,d,e,g=this,h=b(a.target).data("slug"),i=b.Deferred();return c=b(a.target).hasClass("preview"),f.settings.theme._filesystemCredentialsNeeded?(i.reject({errorCode:"theme_install_unavailable"}),i.promise()):g.canSwitchTheme(h)?_.contains(g.installingThemes,h)?(i.reject({errorCode:"theme_already_installing"}),i.promise()):(wp.updates.maybeRequestFilesystemCredentials(a),d=function(a){var b,d=!1;if(c)f.notifications.remove("theme_installing"),g.loadThemePreview(h);else{if(f.control.each(function(b){"theme"===b.params.type&&b.params.theme.id===a.slug&&(d=b.params.theme,b.rerenderAsInstalled(!0))}),!d||f.control.has("installed_theme_"+d.id))return void i.resolve(a);d.type="installed",b=new f.controlConstructor.theme("installed_theme_"+d.id,{type:"theme",section:"installed_themes",theme:d,priority:0}),f.control.add(b),f.control(b.id).container.trigger("render-screenshot"),f.section.each(function(a){"themes"===a.params.type&&d.id===a.currentTheme&&a.closeDetails()})}i.resolve(a)},g.installingThemes.push(h),e=wp.updates.installTheme({slug:h}),c&&f.notifications.add(new f.OverlayNotification("theme_installing",{message:f.l10n.themeDownloading,type:"info",loading:!0})),e.done(d),e.fail(function(){f.notifications.remove("theme_installing")}),i.promise()):(i.reject({errorCode:"theme_switch_unavailable"}),i.promise())},loadThemePreview:function(a){var c,d,e,g=this,h=b.Deferred();return g.canSwitchTheme(a)?(d=document.createElement("a"),d.href=location.href,e=_.extend(f.utils.parseQueryString(d.search.substr(1)),{theme:a,changeset_uuid:f.settings.changeset.uuid,"return":f.settings.url["return"]}),f.state("saved").get()||(e.customize_autosaved="on"),d.search=b.param(e),f.notifications.add(new f.OverlayNotification("theme_previewing",{message:f.l10n.themePreviewWait,type:"info",loading:!0})),c=function(){var a;f.state("processing").get()>0||(f.state("processing").unbind(c),a=f.requestChangesetUpdate({},{autosave:!0}),a.done(function(){h.resolve(),b(window).off("beforeunload.customize-confirm"),location.replace(d.href)}),a.fail(function(){f.notifications.remove("theme_previewing"),h.reject()}))},0===f.state("processing").get()?c():f.state("processing").bind(c),h.promise()):(h.reject({errorCode:"theme_switch_unavailable"}),h.promise())},updateTheme:function(a){wp.updates.maybeRequestFilesystemCredentials(a),b(document).one("wp-theme-update-success",function(a,b){f.control.each(function(a){"theme"===a.params.type&&a.params.theme.id===b.slug&&(a.params.theme.hasUpdate=!1,a.params.theme.version=b.newVersion,setTimeout(function(){a.rerenderAsInstalled(!0)},2e3))})}),wp.updates.updateTheme({slug:b(a.target).closest(".notice").data("slug")})},deleteTheme:function(a){var c,d;c=b(a.target).data("slug"),d=f.section("installed_themes"),a.preventDefault(),f.settings.theme._filesystemCredentialsNeeded||window.confirm(f.settings.l10n.confirmDeleteTheme)&&(wp.updates.maybeRequestFilesystemCredentials(a),b(document).one("wp-theme-delete-success",function(){var a=f.control("installed_theme_"+c);a.container.remove(),f.control.remove(a.id),d.loaded=d.loaded-1,d.updateCount(),f.control.each(function(a){"theme"===a.params.type&&a.params.theme.id===c&&a.rerenderAsInstalled(!1)})}),wp.updates.deleteTheme({slug:c}),d.closeDetails(),d.focus())}}),f.Control=f.Class.extend({defaultActiveArguments:{duration:"fast",completeCallback:b.noop},defaults:{label:"",description:"",active:!0,priority:10},initialize:function(a,c){var d,e,g=this,h=[];g.params=_.extend({},g.defaults,g.params||{},c.params||c||{}),f.Control.instanceCounter||(f.Control.instanceCounter=0),f.Control.instanceCounter++,g.params.instanceNumber||(g.params.instanceNumber=f.Control.instanceCounter),g.params.type||_.find(f.controlConstructor,function(a,b){return a===g.constructor&&(g.params.type=b,!0)}),g.params.content||(g.params.content=b("<li></li>",{id:"customize-control-"+a.replace(/]/g,"").replace(/\[/g,"-"),"class":"customize-control customize-control-"+g.params.type})),g.id=a,g.selector="#customize-control-"+a.replace(/\]/g,"").replace(/\[/g,"-"),g.params.content?g.container=b(g.params.content):g.container=b(g.selector),g.params.templateId?g.templateSelector=g.params.templateId:g.templateSelector="customize-control-"+g.params.type+"-content",g.deferred=_.extend(g.deferred||{},{embedded:new b.Deferred}),g.section=new f.Value,g.priority=new f.Value,g.active=new f.Value,g.activeArgumentsQueue=[],g.notifications=new f.Notifications({alt:g.altNotice}),g.elements=[],g.active.bind(function(a){var c=g.activeArgumentsQueue.shift();c=b.extend({},g.defaultActiveArguments,c),g.onChangeActive(a,c)}),g.section.set(g.params.section),g.priority.set(isNaN(g.params.priority)?10:g.params.priority),g.active.set(g.params.active),f.utils.bubbleChildValueChanges(g,["section","priority","active"]),g.settings={},d={},g.params.setting&&(d["default"]=g.params.setting),_.extend(d,g.params.settings),_.each(d,function(a,b){var c;_.isObject(a)&&_.isFunction(a.extended)&&a.extended(f.Value)?g.settings[b]=a:_.isString(a)&&(c=f(a),c?g.settings[b]=c:h.push(a))}),e=function(){_.each(d,function(a,b){!g.settings[b]&&_.isString(a)&&(g.settings[b]=f(a))}),g.settings[0]&&!g.settings["default"]&&(g.settings["default"]=g.settings[0]),g.setting=g.settings["default"]||null,g.linkElements(),g.embed()},0===h.length?e():f.apply(f,h.concat(e)),g.deferred.embedded.done(function(){g.linkElements(),g.setupNotifications(),g.ready()})},linkElements:function(){var a,c,d,e=this;a=e.container.find("[data-customize-setting-link], [data-customize-setting-key-link]"),c={},a.each(function(){var g,h,i=b(this);if(!i.data("customizeSettingLinked")){if(i.data("customizeSettingLinked",!0),i.is(":radio")){if(g=i.prop("name"),c[g])return;c[g]=!0,i=a.filter('[name="'+g+'"]')}i.data("customizeSettingLink")?h=f(i.data("customizeSettingLink")):i.data("customizeSettingKeyLink")&&(h=e.settings[i.data("customizeSettingKeyLink")]),h&&(d=new f.Element(i),e.elements.push(d),d.sync(h),d.set(h()))}})},embed:function(){var a,b=this;a=function(a){var c;a&&f.section(a,function(a){a.deferred.embedded.done(function(){c=a.contentContainer.is("ul")?a.contentContainer:a.contentContainer.find("ul:first"),b.container.parent().is(c)||(c.append(b.container),b.renderContent()),b.deferred.embedded.resolve()})})},b.section.bind(a),a(b.section.get())},ready:function(){var a,c=this;"dropdown-pages"===c.params.type&&c.params.allow_addition&&(a=c.container.find(".new-content-item"),a.hide(),c.container.on("click",".add-new-toggle",function(c){b(c.currentTarget).slideUp(180),a.slideDown(180),a.find(".create-item-input").focus()}),c.container.on("click",".add-content",function(){c.addNewPage()}),c.container.on("keydown",".create-item-input",function(a){13===a.which&&c.addNewPage()}))},getNotificationsContainerElement:function(){var a,c,d=this;return c=d.container.find(".customize-control-notifications-container:first"),c.length?c:(c=b('<div class="customize-control-notifications-container"></div>'),d.container.hasClass("customize-control-nav_menu_item")?d.container.find(".menu-item-settings:first").prepend(c):d.container.hasClass("customize-control-widget_form")?d.container.find(".widget-inside:first").prepend(c):(a=d.container.find(".customize-control-title"),a.length?a.after(c):d.container.prepend(c)),c)},setupNotifications:function(){var a,b,c=this;_.each(c.settings,function(a){a.notifications&&(a.notifications.bind("add",function(b){var d=_.extend({},b,{setting:a.id});c.notifications.add(new f.Notification(a.id+":"+b.code,d))}),a.notifications.bind("remove",function(b){c.notifications.remove(a.id+":"+b.code)}))}),a=function(){var a=c.section();(!a||f.section.has(a)&&f.section(a).expanded())&&c.notifications.render()},c.notifications.bind("rendered",function(){var a=c.notifications.get();c.container.toggleClass("has-notifications",0!==a.length),c.container.toggleClass("has-error",0!==_.where(a,{type:"error"}).length)}),b=function(b,c){c&&f.section.has(c)&&f.section(c).expanded.unbind(a),b&&f.section(b,function(b){b.expanded.bind(a),a()})},c.section.bind(b),b(c.section.get()),c.notifications.bind("change",_.debounce(a))},renderNotifications:function(){var a,c,d=this,e=!1;"undefined"!=typeof console&&console.warn&&console.warn("[DEPRECATED] wp.customize.Control.prototype.renderNotifications() is deprecated in favor of instantating a wp.customize.Notifications and calling its render() method."),a=d.getNotificationsContainerElement(),a&&a.length&&(c=[],d.notifications.each(function(a){c.push(a),"error"===a.type&&(e=!0)}),0===c.length?a.stop().slideUp("fast"):a.stop().slideDown("fast",null,function(){b(this).css("height","auto")}),d.notificationsTemplate||(d.notificationsTemplate=wp.template("customize-control-notifications")),d.container.toggleClass("has-notifications",0!==c.length),d.container.toggleClass("has-error",e),a.empty().append(b.trim(d.notificationsTemplate({notifications:c,altNotice:Boolean(d.altNotice)}))))},expand:function(a){f.section(this.section()).expand(a)},focus:d,onChangeActive:function(a,c){return c.unchanged?void(c.completeCallback&&c.completeCallback()):void(b.contains(document,this.container[0])?a?this.container.slideDown(c.duration,c.completeCallback):this.container.slideUp(c.duration,c.completeCallback):(this.container.toggle(a),c.completeCallback&&c.completeCallback()))},toggle:function(a){return this.onChangeActive(a,this.defaultActiveArguments)},activate:c.prototype.activate,deactivate:c.prototype.deactivate,_toggleActive:c.prototype._toggleActive,dropdownInit:function(){var a=this,b=this.container.find(".dropdown-status"),c=this.params,d=!1,e=function(a){"string"==typeof a&&c.statuses&&c.statuses[a]?b.html(c.statuses[a]).show():b.hide()};this.container.on("click keydown",".dropdown",function(b){f.utils.isKeydownButNotEnterEvent(b)||(b.preventDefault(),d||a.container.toggleClass("open"),a.container.hasClass("open")&&a.container.parent().parent().find("li.library-selected").focus(),d=!0,setTimeout(function(){d=!1},400))}),this.setting.bind(e),e(this.setting())},renderContent:function(){var a,b,c,d,e=this;b=["button","checkbox","date","datetime-local","email","month","number","password","radio","range","search","select","tel","time","text","textarea","week","url"],c=e.templateSelector,c==="customize-control-"+e.params.type+"-content"&&_.contains(b,e.params.type)&&!document.getElementById("tmpl-"+c)&&0===e.container.children().length&&(c="customize-control-default-content"),document.getElementById("tmpl-"+c)&&(a=wp.template(c),a&&e.container&&e.container.html(a(e.params))),e.notifications.container=e.getNotificationsContainerElement(),d=e.section(),(!d||f.section.has(d)&&f.section(d).expanded())&&e.notifications.render()},addNewPage:function(){var a,c,d,e,g,h,i=this;if("dropdown-pages"===i.params.type&&i.params.allow_addition&&f.Menus){if(c=i.container.find(".add-new-toggle"),d=i.container.find(".new-content-item"),e=i.container.find(".create-item-input"),g=e.val(),h=i.container.find("select"),!g)return void e.addClass("invalid");e.removeClass("invalid"),e.attr("disabled","disabled"),a=f.Menus.insertAutoDraftPost({post_title:g,post_type:"page"}),a.done(function(a){var e,j,k;e=new f.Menus.AvailableItemModel({id:"post-"+a.post_id,title:g,type:"post_type",type_label:f.Menus.data.l10n.page_label,object:"page",object_id:a.post_id,url:a.url}),f.Menus.availableMenuItemsPanel.collection.add(e),j=b("#available-menu-items-post_type-page").find(".available-menu-items-list"),k=wp.template("available-menu-item"),j.prepend(k(e.attributes)),h.focus(),i.setting.set(String(a.post_id)),d.slideUp(180),c.slideDown(180)}),a.always(function(){e.val("").removeAttr("disabled")})}}}),f.ColorControl=f.Control.extend({ready:function(){var a,b=this,c="hue"===this.params.mode,d=!1;c?(a=this.container.find(".color-picker-hue"),a.val(b.setting()).wpColorPicker({change:function(a,c){d=!0,b.setting(c.color.h()),d=!1}})):(a=this.container.find(".color-picker-hex"),a.val(b.setting()).wpColorPicker({change:function(){d=!0,b.setting.set(a.wpColorPicker("color")),d=!1},clear:function(){d=!0,b.setting.set(""),d=!1}})),b.setting.bind(function(b){d||(a.val(b),a.wpColorPicker("color",b))}),b.container.on("keydown",function(c){var d;27===c.which&&(d=b.container.find(".wp-picker-container"),d.hasClass("wp-picker-active")&&(a.wpColorPicker("close"),b.container.find(".wp-color-result").focus(),c.stopPropagation()))})}}),f.MediaControl=f.Control.extend({ready:function(){function a(a){var d=b.Deferred();c.extended(f.UploadControl)?d.resolve():(a=parseInt(a,10),_.isNaN(a)||a<=0?(delete c.params.attachment,d.resolve()):c.params.attachment&&c.params.attachment.id===a&&d.resolve()),"pending"===d.state()&&wp.media.attachment(a).fetch().done(function(){c.params.attachment=this.attributes,d.resolve(),wp.customize.previewer.send(c.setting.id+"-attachment-data",this.attributes)}),d.done(function(){c.renderContent()})}var c=this;_.bindAll(c,"restoreDefault","removeFile","openFrame","select","pausePlayer"),c.container.on("click keydown",".upload-button",c.openFrame),c.container.on("click keydown",".upload-button",c.pausePlayer),c.container.on("click keydown",".thumbnail-image img",c.openFrame),c.container.on("click keydown",".default-button",c.restoreDefault),c.container.on("click keydown",".remove-button",c.pausePlayer),c.container.on("click keydown",".remove-button",c.removeFile),c.container.on("click keydown",".remove-button",c.cleanupPlayer),f.section(c.section()).container.on("expanded",function(){c.player&&c.player.setControlsSize()}).on("collapsed",function(){c.pausePlayer()}),a(c.setting()),c.setting.bind(a)},pausePlayer:function(){this.player&&this.player.pause()},cleanupPlayer:function(){this.player&&wp.media.mixin.removePlayer(this.player)},openFrame:function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),this.frame||this.initFrame(),this.frame.open())},initFrame:function(){this.frame=wp.media({button:{text:this.params.button_labels.frame_button},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:this.params.mime_type}),multiple:!1,date:!1})]}),this.frame.on("select",this.select)},select:function(){var a,b=this.frame.state().get("selection").first().toJSON(),c=window._wpmejsSettings||{};this.params.attachment=b,this.setting(b.id),a=this.container.find("audio, video").get(0),a?this.player=new MediaElementPlayer(a,c):this.cleanupPlayer()},restoreDefault:function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),this.params.attachment=this.params.defaultAttachment,this.setting(this.params.defaultAttachment.url))},removeFile:function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),this.params.attachment={},this.setting(""),this.renderContent())}}),f.UploadControl=f.MediaControl.extend({select:function(){var a,b=this.frame.state().get("selection").first().toJSON(),c=window._wpmejsSettings||{};this.params.attachment=b,this.setting(b.url),a=this.container.find("audio, video").get(0),a?this.player=new MediaElementPlayer(a,c):this.cleanupPlayer()},success:function(){},removerVisibility:function(){}}),f.ImageControl=f.UploadControl.extend({thumbnailSrc:function(){}}),f.BackgroundControl=f.UploadControl.extend({ready:function(){f.UploadControl.prototype.ready.apply(this,arguments)},select:function(){f.UploadControl.prototype.select.apply(this,arguments),wp.ajax.post("custom-background-add",{nonce:_wpCustomizeBackground.nonces.add,wp_customize:"on",customize_theme:f.settings.theme.stylesheet,attachment_id:this.params.attachment.id})}}),f.BackgroundPositionControl=f.Control.extend({ready:function(){var a,c=this;c.container.on("change",'input[name="background-position"]',function(){var a=b(this).val().split(" ");c.settings.x(a[0]),c.settings.y(a[1])}),a=_.debounce(function(){var a,b,d,e;a=c.settings.x.get(),b=c.settings.y.get(),e=String(a)+" "+String(b),d=c.container.find('input[name="background-position"][value="'+e+'"]'),d.click()}),c.settings.x.bind(a),c.settings.y.bind(a),a()}}),f.CroppedImageControl=f.MediaControl.extend({openFrame:function(a){f.utils.isKeydownButNotEnterEvent(a)||(this.initFrame(),this.frame.setState("library").open())},initFrame:function(){var a=_wpMediaViewsL10n;this.frame=wp.media({button:{text:a.select,close:!1},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:this.params.width,suggestedHeight:this.params.height}),new wp.media.controller.CustomizeImageCropper({imgSelectOptions:this.calculateImageSelectOptions,control:this})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this)},onSelect:function(){var a=this.frame.state().get("selection").first().toJSON();this.params.width!==a.width||this.params.height!==a.height||this.params.flex_width||this.params.flex_height?this.frame.setState("cropper"):(this.setImageFromAttachment(a),this.frame.close())},onCropped:function(a){this.setImageFromAttachment(a)},calculateImageSelectOptions:function(a,b){var c,d,e,f=b.get("control"),g=!!parseInt(f.params.flex_width,10),h=!!parseInt(f.params.flex_height,10),i=a.get("width"),j=a.get("height"),k=parseInt(f.params.width,10),l=parseInt(f.params.height,10),m=k/l,n=k,o=l;return b.set("canSkipCrop",!f.mustBeCropped(g,h,k,l,i,j)),i/j>m?(l=j,k=l*m):(k=i,l=k/m),c=(i-k)/2,d=(j-l)/2,e={handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:i,imageHeight:j,minWidth:n>k?k:n,minHeight:o>l?l:o,x1:c,y1:d,x2:k+c,y2:l+d},h===!1&&g===!1&&(e.aspectRatio=k+":"+l),!0===h&&(delete e.minHeight,e.maxWidth=i),!0===g&&(delete e.minWidth,e.maxHeight=j),e},mustBeCropped:function(a,b,c,d,e,f){return(!0!==a||!0!==b)&&((!0!==a||d!==f)&&((!0!==b||c!==e)&&((c!==e||d!==f)&&!(e<=c))))},onSkippedCrop:function(){var a=this.frame.state().get("selection").first().toJSON();this.setImageFromAttachment(a)},setImageFromAttachment:function(a){this.params.attachment=a,this.setting(a.id)}}),f.SiteIconControl=f.CroppedImageControl.extend({initFrame:function(){var a=_wpMediaViewsL10n;this.frame=wp.media({button:{text:a.select,close:!1},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:this.params.width,suggestedHeight:this.params.height}),new wp.media.controller.SiteIconCropper({imgSelectOptions:this.calculateImageSelectOptions,control:this})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this)},onSelect:function(){var a=this.frame.state().get("selection").first().toJSON(),b=this;this.params.width!==a.width||this.params.height!==a.height||this.params.flex_width||this.params.flex_height?this.frame.setState("cropper"):wp.ajax.post("crop-image",{nonce:a.nonces.edit,id:a.id,context:"site-icon",cropDetails:{x1:0,y1:0,width:this.params.width,height:this.params.height,dst_width:this.params.width,dst_height:this.params.height}}).done(function(a){b.setImageFromAttachment(a),b.frame.close()}).fail(function(){b.frame.trigger("content:error:crop")})},setImageFromAttachment:function(a){var c,d,e=["site_icon-32","thumbnail","full"];_.each(e,function(b){d||_.isUndefined(a.sizes[b])||(d=a.sizes[b])}),this.params.attachment=a,this.setting(a.id),d&&(c=b('link[rel="icon"][sizes="32x32"]'),c.attr("href",d.url))},removeFile:function(a){f.utils.isKeydownButNotEnterEvent(a)||(a.preventDefault(),this.params.attachment={},this.setting(""),this.renderContent(),b('link[rel="icon"][sizes="32x32"]').attr("href","/favicon.ico"))}}),f.HeaderControl=f.Control.extend({ready:function(){this.btnRemove=b("#customize-control-header_image .actions .remove"),this.btnNew=b("#customize-control-header_image .actions .new"),_.bindAll(this,"openMedia","removeImage"),this.btnNew.on("click",this.openMedia),this.btnRemove.on("click",this.removeImage),f.HeaderTool.currentHeader=this.getInitialHeaderImage(),new f.HeaderTool.CurrentView({model:f.HeaderTool.currentHeader,el:"#customize-control-header_image .current .container"}),new f.HeaderTool.ChoiceListView({collection:f.HeaderTool.UploadsList=new f.HeaderTool.ChoiceList,el:"#customize-control-header_image .choices .uploaded .list"}),new f.HeaderTool.ChoiceListView({collection:f.HeaderTool.DefaultsList=new f.HeaderTool.DefaultsList,el:"#customize-control-header_image .choices .default .list"}),f.HeaderTool.combinedList=f.HeaderTool.CombinedList=new f.HeaderTool.CombinedList([f.HeaderTool.UploadsList,f.HeaderTool.DefaultsList]),wp.media.controller.Cropper.prototype.defaults.doCropArgs.wp_customize="on",wp.media.controller.Cropper.prototype.defaults.doCropArgs.customize_theme=f.settings.theme.stylesheet},getInitialHeaderImage:function(){if(!f.get().header_image||!f.get().header_image_data||_.contains(["remove-header","random-default-image","random-uploaded-image"],f.get().header_image))return new f.HeaderTool.ImageModel;var a=_.find(_wpCustomizeHeader.uploads,function(a){return a.attachment_id===f.get().header_image_data.attachment_id});return a||(a={url:f.get().header_image,thumbnail_url:f.get().header_image,attachment_id:f.get().header_image_data.attachment_id}),new f.HeaderTool.ImageModel({header:a,choice:a.url.split("/").pop()})},calculateImageSelectOptions:function(a,b){var c,d,e,g,h,i,j=parseInt(_wpCustomizeHeader.data.width,10),k=parseInt(_wpCustomizeHeader.data.height,10),l=!!parseInt(_wpCustomizeHeader.data["flex-width"],10),m=!!parseInt(_wpCustomizeHeader.data["flex-height"],10);return h=a.get("width"),g=a.get("height"),this.headerImage=new f.HeaderTool.ImageModel,this.headerImage.set({themeWidth:j,themeHeight:k,themeFlexWidth:l,themeFlexHeight:m,imageWidth:h,imageHeight:g}),b.set("canSkipCrop",!this.headerImage.shouldBeCropped()),c=j/k,d=h,e=g,d/e>c?(k=e,j=k*c):(j=d,k=j/c),i={handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:h,imageHeight:g,x1:0,y1:0,x2:j,y2:k},m===!1&&l===!1&&(i.aspectRatio=j+":"+k),m===!1&&(i.maxHeight=k),l===!1&&(i.maxWidth=j),i},openMedia:function(a){var b=_wpMediaViewsL10n;a.preventDefault(),this.frame=wp.media({button:{text:b.selectAndCrop,close:!1},states:[new wp.media.controller.Library({title:b.chooseImage,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:_wpCustomizeHeader.data.width,suggestedHeight:_wpCustomizeHeader.data.height}),new wp.media.controller.Cropper({imgSelectOptions:this.calculateImageSelectOptions})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this),this.frame.open()},onSelect:function(){this.frame.setState("cropper")},onCropped:function(a){var b=a.url,c=a.attachment_id,d=a.width,e=a.height;this.setImageFromURL(b,c,d,e)},onSkippedCrop:function(a){var b=a.get("url"),c=a.get("width"),d=a.get("height");this.setImageFromURL(b,a.id,c,d)},setImageFromURL:function(a,b,c,d){var e,g={};g.url=a,g.thumbnail_url=a,g.timestamp=_.now(),b&&(g.attachment_id=b),c&&(g.width=c),d&&(g.height=d),e=new f.HeaderTool.ImageModel({header:g,choice:a.split("/").pop()}),f.HeaderTool.UploadsList.add(e),f.HeaderTool.currentHeader.set(e.toJSON()),e.save(),e.importImage()},removeImage:function(){f.HeaderTool.currentHeader.trigger("hide"),f.HeaderTool.CombinedList.trigger("control:removeImage")}}),f.ThemeControl=f.Control.extend({touchDrag:!1,screenshotRendered:!1,ready:function(){function a(){return!g.canSwitchTheme(e.params.theme.id)}function c(){return a()||!1===f.settings.theme._canInstall||!0===f.settings.theme._filesystemCredentialsNeeded}function d(){e.container.find("button.preview, button.preview-theme").toggleClass("disabled",a()),e.container.find("button.theme-install").toggleClass("disabled",c())}var e=this,g=f.panel("themes");f.state("selectedChangesetStatus").bind(d),f.state("changesetStatus").bind(d),d(),e.container.on("touchmove",".theme",function(){e.touchDrag=!0}),e.container.on("click keydown touchend",".theme",function(a){var c;if(!f.utils.isKeydownButNotEnterEvent(a))return e.touchDrag===!0?e.touchDrag=!1:void(b(a.target).is(".theme-actions .button, .update-theme")||(a.preventDefault(),c=f.section(e.section()),c.showDetails(e.params.theme,function(){f.settings.theme._filesystemCredentialsNeeded&&c.overlay.find(".theme-actions .delete-theme").remove()})))}),e.container.on("render-screenshot",function(){var a=b(this).find("img"),c=a.data("src");c&&a.attr("src",c),e.screenshotRendered=!0})},filter:function(a){var b=this,c=0,d=b.params.theme.name+" "+b.params.theme.description+" "+b.params.theme.tags+" "+b.params.theme.author+" ";return d=d.toLowerCase().replace("-"," "),_.isArray(a)||(a=[a]),b.params.theme.name.toLowerCase()===a.join(" ")?c=100:(c+=10*(d.split(a.join(" ")).length-1),_.each(a,function(a){c+=2*(d.split(a+" ").length-1),c=c+d.split(a).length-1}),c>99&&(c=99)),0!==c?(b.activate(),b.params.priority=101-c,!0):(b.deactivate(),b.params.priority=101,!1)},rerenderAsInstalled:function(a){var b,c=this;a?c.params.theme.type="installed":(b=f.section(c.params.section),c.params.theme.type=b.params.action),c.renderContent(),c.container.trigger("render-screenshot")}}),f.CodeEditorControl=f.Control.extend({initialize:function(a,c){var d=this;d.deferred=_.extend(d.deferred||{},{codemirror:b.Deferred()}),f.Control.prototype.initialize.call(d,a,c),d.notifications.bind("add",function(a){a.code===d.setting.id+":csslint_error"&&(a.templateId="customize-code-editor-lint-error-notification",a.render=function(a){return function(){var b=a.call(this);return b.find("input[type=checkbox]").on("click",function(){d.setting.notifications.remove("csslint_error")}),b}}(a.render))})},ready:function(){var a=this;return a.section()?void f.section(a.section(),function(b){b.deferred.embedded.done(function(){var c;b.expanded()?a.initEditor():(c=function(d){d&&(a.initEditor(),b.expanded.unbind(c))},b.expanded.bind(c))})}):void a.initEditor()},initEditor:function(){var a,b=this,c=!1;wp.codeEditor&&(_.isUndefined(b.params.editor_settings)||!1!==b.params.editor_settings)&&(c=wp.codeEditor.defaultSettings?_.clone(wp.codeEditor.defaultSettings):{},c.codemirror=_.extend({},c.codemirror,{indentUnit:2,tabSize:2}),_.isObject(b.params.editor_settings)&&_.each(b.params.editor_settings,function(a,b){_.isObject(a)&&(c[b]=_.extend({},c[b],a))})),a=new f.Element(b.container.find("textarea")),b.elements.push(a),a.sync(b.setting),a.set(b.setting()),c?b.initSyntaxHighlightingEditor(c):b.initPlainTextareaEditor()},focus:function(a){var b,c=this,d=_.extend({},a);b=d.completeCallback,d.completeCallback=function(){b&&b(),c.editor&&c.editor.codemirror.focus()},f.Control.prototype.focus.call(c,d)},initSyntaxHighlightingEditor:function(a){var c,d=this,e=d.container.find("textarea"),f=!1;c=_.extend({},a,{onTabNext:_.bind(d.onTabNext,d),onTabPrevious:_.bind(d.onTabPrevious,d),onUpdateErrorNotice:_.bind(d.onUpdateErrorNotice,d)}),d.editor=wp.codeEditor.initialize(e,c),b(d.editor.codemirror.display.lineDiv).attr({role:"textbox","aria-multiline":"true","aria-label":d.params.label,"aria-describedby":"editor-keyboard-trap-help-1 editor-keyboard-trap-help-2 editor-keyboard-trap-help-3 editor-keyboard-trap-help-4"}),d.container.find("label").on("click",function(){d.editor.codemirror.focus()}),d.editor.codemirror.on("change",function(a){f=!0,e.val(a.getValue()).trigger("change"),f=!1}),d.setting.bind(function(a){f||d.editor.codemirror.setValue(a)}),d.editor.codemirror.on("keydown",function(a,b){var c=27;c===b.keyCode&&b.stopPropagation()}),d.deferred.codemirror.resolveWith(d,[d.editor.codemirror])},onTabNext:function(){var a,c,d,e=this;d=f.section(e.section()),a=d.controls(),c=a.indexOf(e),a.length===c+1?b("#customize-footer-actions .collapse-sidebar").focus():a[c+1].container.find(":focusable:first").focus()},onTabPrevious:function(){var a,b,c,d=this;c=f.section(d.section()),a=c.controls(),b=a.indexOf(d),0===b?c.contentContainer.find(".customize-section-title .customize-help-toggle, .customize-section-title .customize-section-description.open .section-description-close").last().focus():a[b-1].contentContainer.find(":focusable:first").focus()},onUpdateErrorNotice:function(a){var b,c=this;c.setting.notifications.remove("csslint_error"),0!==a.length&&(b=1===a.length?f.l10n.customCssError.singular.replace("%d","1"):f.l10n.customCssError.plural.replace("%d",String(a.length)),c.setting.notifications.add(new f.Notification("csslint_error",{message:b,type:"error"})))},initPlainTextareaEditor:function(){var a=this,b=a.container.find("textarea"),c=b[0];b.on("blur",function(){b.data("next-tab-blurs",!1)}),b.on("keydown",function(a){var d,e,f,g=9,h=27;return h===a.keyCode?void(b.data("next-tab-blurs")||(b.data("next-tab-blurs",!0),a.stopPropagation())):void(g!==a.keyCode||a.ctrlKey||a.altKey||a.shiftKey||b.data("next-tab-blurs")||(d=c.selectionStart,
e=c.selectionEnd,f=c.value,d>=0&&(c.value=f.substring(0,d).concat("\t",f.substring(e)),b.selectionStart=c.selectionEnd=d+1),a.stopPropagation(),a.preventDefault()))}),a.deferred.codemirror.rejectWith(a)}}),f.DateTimeControl=f.Control.extend({ready:function(){var a=this;if(a.inputElements={},a.invalidDate=!1,_.bindAll(a,"populateSetting","updateDaysForMonth","populateDateInputs"),!a.setting)throw new Error("Missing setting");a.container.find(".date-input").each(function(){var c,d,e=b(this);c=e.data("component"),d=new f.Element(e),a.inputElements[c]=d,a.elements.push(d),e.on("change",function(){a.invalidDate&&a.notifications.add(new f.Notification("invalid_date",{message:f.l10n.invalidDate}))}),e.on("input",_.debounce(function(){a.invalidDate||a.notifications.remove("invalid_date")})),e.on("blur",_.debounce(function(){a.invalidDate||a.populateDateInputs()}))}),a.inputElements.month.bind(a.updateDaysForMonth),a.inputElements.year.bind(a.updateDaysForMonth),a.populateDateInputs(),a.setting.bind(a.populateDateInputs),_.each(a.inputElements,function(b){b.bind(a.populateSetting)})},parseDateTime:function(a){var b,c,d=this,e=12;return a&&(b=a.match(/^(\d\d\d\d)-(\d\d)-(\d\d)(?: (\d\d):(\d\d)(?::(\d\d))?)?$/)),b?(b.shift(),c={year:b.shift(),month:b.shift(),day:b.shift(),hour:b.shift()||"00",minute:b.shift()||"00",second:b.shift()||"00"},d.params.includeTime&&d.params.twelveHourFormat&&(c.hour=parseInt(c.hour,10),c.meridian=c.hour>=e?"pm":"am",c.hour=c.hour%e?String(c.hour%e):String(e),delete c.second),c):null},validateInputs:function(){var a,b,c=this;return c.invalidDate=!1,a=["year","day"],c.params.includeTime&&a.push("hour","minute"),_.find(a,function(a){var d,e,f,g;return d=c.inputElements[a],b=d.element.get(0),e=parseInt(d.element.attr("max"),10),f=parseInt(d.element.attr("min"),10),g=parseInt(d(),10),c.invalidDate=isNaN(g)||g>e||g<f,c.invalidDate||b.setCustomValidity(""),c.invalidDate}),c.inputElements.meridian&&!c.invalidDate&&(b=c.inputElements.meridian.element.get(0),"am"!==c.inputElements.meridian.get()&&"pm"!==c.inputElements.meridian.get()?c.invalidDate=!0:b.setCustomValidity("")),c.invalidDate?b.setCustomValidity(f.l10n.invalidValue):b.setCustomValidity(""),(!c.section()||f.section.has(c.section())&&f.section(c.section()).expanded())&&_.result(b,"reportValidity"),c.invalidDate},updateDaysForMonth:function(){var a,b,c,d,e=this;c=parseInt(e.inputElements.month(),10),b=parseInt(e.inputElements.year(),10),d=parseInt(e.inputElements.day(),10),c&&b&&(a=new Date(b,c,0).getDate(),e.inputElements.day.element.attr("max",a),d>a&&e.inputElements.day(String(a)))},populateSetting:function(){var a,b=this;return!(b.validateInputs()||!b.params.allowPastDate&&!b.isFutureDate())&&(a=b.convertInputDateToString(),b.setting.set(a),!0)},convertInputDateToString:function(){var a,b,c,d,e=this,f="";return d=function(a,b){var c;return String(a).length<b&&(c=b-String(a).length,a=Math.pow(10,c).toString().substr(1)+String(a)),a},c=function(a){var b=parseInt(e.inputElements[a].get(),10);return _.contains(["month","day","hour","minute"],a)?b=d(b,2):"year"===a&&(b=d(b,4)),b},a=["year","-","month","-","day"],e.params.includeTime&&(b=e.inputElements.meridian?e.convertHourToTwentyFourHourFormat(e.inputElements.hour(),e.inputElements.meridian()):e.inputElements.hour(),a=a.concat([" ",d(b,2),":","minute",":","00"])),_.each(a,function(a){f+=e.inputElements[a]?c(a):a}),f},isFutureDate:function(){var a=this;return 0<f.utils.getRemainingTime(a.convertInputDateToString())},convertHourToTwentyFourHourFormat:function(a,b){var c,d,e=12;return d=parseInt(a,10),isNaN(d)?"":(c="pm"===b&&d<e?d+e:"am"===b&&e===d?d-e:d,String(c))},populateDateInputs:function(){var a,b=this;return!!(a=b.parseDateTime(b.setting.get()))&&(_.each(b.inputElements,function(b,c){var d=a[c];"month"===c||"meridian"===c?(d=d.replace(/^0/,""),b.set(d)):(d=parseInt(d,10),b.element.is(document.activeElement)?d!==parseInt(b(),10)&&b.set(String(d)):b.set(a[c]))}),!0)},toggleFutureDateNotification:function(a){var b,c,d=this;return b="not_future_date",a?(c=new f.Notification(b,{type:"error",message:f.l10n.futureDateError}),d.notifications.add(c)):d.notifications.remove(b),d}}),f.PreviewLinkControl=f.Control.extend({defaults:_.extend({},f.Control.prototype.defaults,{templateId:"customize-preview-link-control"}),ready:function(){var a,c,d,e,g,h,i=this;_.bindAll(i,"updatePreviewLink"),i.setting||(i.setting=new f.Value),i.previewElements={},i.container.find(".preview-control-element").each(function(){d=b(this),c=d.data("component"),a=new f.Element(d),i.previewElements[c]=a,i.elements.push(a)}),e=i.previewElements.url,g=i.previewElements.input,h=i.previewElements.button,g.link(i.setting),e.link(i.setting),e.bind(function(a){e.element.parent().attr({href:a,target:f.settings.changeset.uuid})}),f.bind("ready",i.updatePreviewLink),f.state("saved").bind(i.updatePreviewLink),f.state("changesetStatus").bind(i.updatePreviewLink),f.state("activated").bind(i.updatePreviewLink),f.previewer.previewUrl.bind(i.updatePreviewLink),h.element.on("click",function(a){a.preventDefault(),i.setting()&&(g.element.select(),document.execCommand("copy"),h(h.element.data("copied-text")))}),e.element.parent().on("click",function(a){b(this).hasClass("disabled")&&a.preventDefault()}),h.element.on("mouseenter",function(){i.setting()&&h(h.element.data("copy-text"))})},updatePreviewLink:function(){var a,b=this;a=!f.state("saved").get()||""===f.state("changesetStatus").get()||"auto-draft"===f.state("changesetStatus").get(),b.toggleSaveNotification(a),b.previewElements.url.element.parent().toggleClass("disabled",a),b.previewElements.button.element.prop("disabled",a),b.setting.set(f.previewer.getFrontendPreviewUrl())},toggleSaveNotification:function(a){var b,c,d=this;b="changes_not_saved",a?(c=new f.Notification(b,{type:"info",message:f.l10n.saveBeforeShare}),d.notifications.add(c)):d.notifications.remove(b)}}),f.defaultConstructor=f.Setting,f.control=new f.Values({defaultConstructor:f.Control}),f.section=new f.Values({defaultConstructor:f.Section}),f.panel=new f.Values({defaultConstructor:f.Panel}),f.notifications=new f.Notifications,f.PreviewFrame=f.Messenger.extend({sensitivity:null,initialize:function(a,c){var d=b.Deferred();d.promise(this),this.container=a.container,b.extend(a,{channel:f.PreviewFrame.uuid()}),f.Messenger.prototype.initialize.call(this,a,c),this.add("previewUrl",a.previewUrl),this.query=b.extend(a.query||{},{customize_messenger_channel:this.channel()}),this.run(d)},run:function(a){var c,d,e,g=this,h=!1,i=!1,j=null,k="{}"!==g.query.customized;g._ready&&g.unbind("ready",g._ready),g._ready=function(b){i=!0,j=b,g.container.addClass("iframe-ready"),b&&h&&a.resolveWith(g,[b])},g.bind("ready",g._ready),c=document.createElement("a"),c.href=g.previewUrl(),d=_.extend(f.utils.parseQueryString(c.search.substr(1)),{customize_changeset_uuid:g.query.customize_changeset_uuid,customize_theme:g.query.customize_theme,customize_messenger_channel:g.query.customize_messenger_channel}),!f.settings.changeset.autosaved&&f.state("saved").get()||(d.customize_autosaved="on"),c.search=b.param(d),g.iframe=b("<iframe />",{title:f.l10n.previewIframeTitle,name:"customize-"+g.channel()}),g.iframe.attr("onmousewheel",""),g.iframe.attr("sandbox","allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-popups allow-popups-to-escape-sandbox allow-presentation allow-same-origin allow-scripts"),k?g.iframe.attr("data-src",c.href):g.iframe.attr("src",c.href),g.iframe.appendTo(g.container),g.targetWindow(g.iframe[0].contentWindow),k&&(e=b("<form>",{action:c.href,target:g.iframe.attr("name"),method:"post",hidden:"hidden"}),e.append(b("<input>",{type:"hidden",name:"_method",value:"GET"})),_.each(g.query,function(a,c){e.append(b("<input>",{type:"hidden",name:c,value:a}))}),g.container.append(e),e.submit(),e.remove()),g.bind("iframe-loading-error",function(b){return g.iframe.remove(),0===b?void g.login(a):-1===b?void a.rejectWith(g,["cheatin"]):void a.rejectWith(g,["request failure"])}),g.iframe.one("load",function(){h=!0,i?a.resolveWith(g,[j]):setTimeout(function(){a.rejectWith(g,["ready timeout"])},g.sensitivity)})},login:function(a){var c,d=this;return c=function(){a.rejectWith(d,["logged out"])},this.triedLogin?c():void b.get(f.settings.url.ajax,{action:"logged-in"}).fail(c).done(function(e){var g;"1"!==e&&c(),g=b("<iframe />",{src:d.previewUrl(),title:f.l10n.previewIframeTitle}).hide(),g.appendTo(d.container),g.on("load",function(){d.triedLogin=!0,g.remove(),d.run(a)})})},destroy:function(){f.Messenger.prototype.destroy.call(this),this.iframe&&this.iframe.remove(),delete this.iframe,delete this.targetWindow}}),function(){var a=0;f.PreviewFrame.uuid=function(){return"preview-"+String(a++)}}(),f.setDocumentTitle=function(a){var b,c;b=f.settings.documentTitleTmpl,c=b.replace("%s",a),document.title=c,f.trigger("title",c)},f.Previewer=f.Messenger.extend({refreshBuffer:null,initialize:function(a,c){var d=this,e=document.createElement("a");b.extend(d,c||{}),d.deferred={active:b.Deferred()},d.refresh=_.debounce(function(a){return function(){var b,c;b=function(){return 0===f.state("processing").get()},b()?a.call(d):(c=function(){b()&&(a.call(d),f.state("processing").unbind(c))},f.state("processing").bind(c))}}(d.refresh),d.refreshBuffer),d.container=f.ensure(a.container),d.allowedUrls=a.allowedUrls,a.url=window.location.href,f.Messenger.prototype.initialize.call(d,a),e.href=d.origin(),d.add("scheme",e.protocol.replace(/:$/,"")),d.add("previewUrl",a.previewUrl).setter(function(a){var c,e,g,h=null,i=[];return c=document.createElement("a"),c.href=a,/\/wp-(admin|includes|content)(\/|$)/.test(c.pathname)?null:(c.search.length>1&&(e=f.utils.parseQueryString(c.search.substr(1)),delete e.customize_changeset_uuid,delete e.customize_theme,delete e.customize_messenger_channel,delete e.customize_autosaved,_.isEmpty(e)?c.search="":c.search=b.param(e)),i.push(c),d.scheme.get()+":"!==c.protocol&&(c=document.createElement("a"),c.href=i[0].href,c.protocol=d.scheme.get()+":",i.unshift(c)),g=document.createElement("a"),_.find(i,function(a){return!_.isUndefined(_.find(d.allowedUrls,function(b){if(g.href=b,c.protocol===g.protocol&&c.host===g.host&&0===c.pathname.indexOf(g.pathname.replace(/\/$/,"")))return h=a.href,!0}))}),h)}),d.bind("ready",d.ready),d.deferred.active.done(_.bind(d.keepPreviewAlive,d)),d.bind("synced",function(){d.send("active")}),d.previewUrl.bind(d.refresh),d.scroll=0,d.bind("scroll",function(a){d.scroll=a}),d.bind("url",function(a){var b,c=!1;d.scroll=0,b=function(){c=!0},d.previewUrl.bind(b),d.previewUrl.set(a),d.previewUrl.unbind(b),c||d.refresh()}),d.bind("documentTitle",function(a){f.setDocumentTitle(a)})},ready:function(a){var b,c=this,d={};d.settings=f.get(),d["settings-modified-while-loading"]=c.settingsModifiedWhileLoading,("resolved"!==c.deferred.active.state()||c.loading)&&(d.scroll=c.scroll),d["edit-shortcut-visibility"]=f.state("editShortcutVisibility").get(),c.send("sync",d),a.currentUrl&&(c.previewUrl.unbind(c.refresh),c.previewUrl.set(a.currentUrl),c.previewUrl.bind(c.refresh)),b={panel:a.activePanels,section:a.activeSections,control:a.activeControls},_(b).each(function(a,b){f[b].each(function(c,d){var e=_.isUndefined(f.settings[b+"s"][d]);e&&_.isUndefined(a[d])||(a[d]?c.activate():c.deactivate())})}),a.settingValidities&&f._handleSettingValidities({settingValidities:a.settingValidities,focusInvalidControl:!1})},keepPreviewAlive:function(){var a,b,c,d,e=this;d=function(){b=setTimeout(c,f.settings.timeouts.keepAliveCheck)},a=function(){f.state("previewerAlive").set(!0),clearTimeout(b),d()},c=function(){f.state("previewerAlive").set(!1)},d(),e.bind("ready",a),e.bind("keep-alive",a)},query:function(){},abort:function(){this.loading&&(this.loading.destroy(),delete this.loading)},refresh:function(){var a,b=this;b.send("loading-initiated"),b.abort(),b.loading=new f.PreviewFrame({url:b.url(),previewUrl:b.previewUrl(),query:b.query({excludeCustomizedSaved:!0})||{},container:b.container}),b.settingsModifiedWhileLoading={},a=function(a){b.settingsModifiedWhileLoading[a.id]=!0},f.bind("change",a),b.loading.always(function(){f.unbind("change",a)}),b.loading.done(function(a){var c,d=this;b.preview=d,b.targetWindow(d.targetWindow()),b.channel(d.channel()),c=function(){d.unbind("synced",c),b._previousPreview&&b._previousPreview.destroy(),b._previousPreview=b.preview,b.deferred.active.resolve(),delete b.loading},d.bind("synced",c),b.trigger("ready",a)}),b.loading.fail(function(a){b.send("loading-failed"),"logged out"===a&&(b.preview&&(b.preview.destroy(),delete b.preview),b.login().done(b.refresh)),"cheatin"===a&&b.cheatin()})},login:function(){var a,c,d,e=this;return this._login?this._login:(a=b.Deferred(),this._login=a.promise(),c=new f.Messenger({channel:"login",url:f.settings.url.login}),d=b("<iframe />",{src:f.settings.url.login,title:f.l10n.loginIframeTitle}).appendTo(this.container),c.targetWindow(d[0].contentWindow),c.bind("login",function(){var b=e.refreshNonces();b.always(function(){d.remove(),c.destroy(),delete e._login}),b.done(function(){a.resolve()}),b.fail(function(){e.cheatin(),a.reject()})}),this._login)},cheatin:function(){b(document.body).empty().addClass("cheatin").append("<h1>"+f.l10n.notAllowedHeading+"</h1><p>"+f.l10n.notAllowed+"</p>")},refreshNonces:function(){var a,c=b.Deferred();return c.promise(),a=wp.ajax.post("customize_refresh_nonces",{wp_customize:"on",customize_theme:f.settings.theme.stylesheet}),a.done(function(a){f.trigger("nonce-refresh",a),c.resolve()}),a.fail(function(){c.reject()}),c}}),f.settingConstructor={},f.controlConstructor={color:f.ColorControl,media:f.MediaControl,upload:f.UploadControl,image:f.ImageControl,cropped_image:f.CroppedImageControl,site_icon:f.SiteIconControl,header:f.HeaderControl,background:f.BackgroundControl,background_position:f.BackgroundPositionControl,theme:f.ThemeControl,date_time:f.DateTimeControl,code_editor:f.CodeEditorControl},f.panelConstructor={themes:f.ThemesPanel},f.sectionConstructor={themes:f.ThemesSection,outer:f.OuterSection},f._handleSettingValidities=function(a){var b,c=[],d=!1;_.each(a.settingValidities,function(a,b){var d=f(b);d&&(_.isObject(a)&&_.each(a,function(a,b){var e,g,h=!1;e=new f.Notification(b,_.extend({fromServer:!0},a)),g=d.notifications(e.code),g&&(h=e.type!==g.type||e.message!==g.message||!_.isEqual(e.data,g.data)),h&&d.notifications.remove(b),d.notifications.has(e.code)||d.notifications.add(e),c.push(d.id)}),d.notifications.each(function(b){!b.fromServer||"error"!==b.type||!0!==a&&a[b.code]||d.notifications.remove(b.code)}))}),a.focusInvalidControl&&(b=f.findControlsForSettings(c),_(_.values(b)).find(function(a){return _(a).find(function(a){var b=a.section()&&f.section.has(a.section())&&f.section(a.section()).expanded();return b&&a.expanded&&(b=a.expanded()),b&&(a.focus(),d=!0),d})}),d||_.isEmpty(b)||_.values(b)[0][0].focus())},f.findControlsForSettings=function(a){var b,c={};return _.each(_.unique(a),function(a){var d=f(a);d&&(b=d.findControls(),b&&b.length>0&&(c[a]=b))}),c},f.reflowPaneContents=_.bind(function(){var a,c,d,e=[],g=!1;document.activeElement&&(c=b(document.activeElement)),f.panel.each(function(b){if("themes"!==b.id){var c=b.sections(),d=_.pluck(c,"headContainer");e.push(b),a=b.contentContainer.is("ul")?b.contentContainer:b.contentContainer.find("ul:first"),f.utils.areElementListsEqual(d,a.children("[id]"))||(_(c).each(function(b){a.append(b.headContainer)}),g=!0)}}),f.section.each(function(b){var c=b.controls(),d=_.pluck(c,"container");b.panel()||e.push(b),a=b.contentContainer.is("ul")?b.contentContainer:b.contentContainer.find("ul:first"),f.utils.areElementListsEqual(d,a.children("[id]"))||(_(c).each(function(b){a.append(b.container)}),g=!0)}),e.sort(f.utils.prioritySort),d=_.pluck(e,"headContainer"),a=b("#customize-theme-controls .customize-pane-parent"),f.utils.areElementListsEqual(d,a.children())||(_(e).each(function(b){a.append(b.headContainer)}),g=!0),f.panel.each(function(a){var b=a.active();a.active.callbacks.fireWith(a.active,[b,b])}),f.section.each(function(a){var b=a.active();a.active.callbacks.fireWith(a.active,[b,b])}),g&&c&&c.focus(),f.trigger("pane-contents-reflowed")},f),f.state=new f.Values,_.each(["saved","saving","trashing","activated","processing","paneVisible","expandedPanel","expandedSection","changesetDate","selectedChangesetDate","changesetStatus","selectedChangesetStatus","remainingTimeToPublish","previewerAlive","editShortcutVisibility","changesetLocked","previewedDevice"],function(a){f.state.create(a)}),b(function(){function a(){function c(a){a||f.settings.changeset.autosaved||(f.settings.changeset.autosaved=!0,f.previewer.send("autosaving"))}var d,e,g,h=!1;f.unbind("change",a),f.state("saved").bind(c),c(f.state("saved").get()),e=function(){h||(h=!0,f.requestChangesetUpdate({},{autosave:!0}).always(function(){h=!1})),g()},g=function(){clearTimeout(d),d=setTimeout(function(){e()},f.settings.timeouts.changesetAutoSave)},g(),b(document).on("visibilitychange.wp-customize-changeset-update",function(){document.hidden&&e()}),b(window).on("beforeunload.wp-customize-changeset-update",function(){e()})}if(f.settings=window._wpCustomizeSettings,f.l10n=window._wpCustomizeControlsL10n,f.settings&&b.support.postMessage&&(b.support.cors||!f.settings.isCrossDomain)){null===f.PreviewFrame.prototype.sensitivity&&(f.PreviewFrame.prototype.sensitivity=f.settings.timeouts.previewFrameSensitivity),null===f.Previewer.prototype.refreshBuffer&&(f.Previewer.prototype.refreshBuffer=f.settings.timeouts.windowRefresh);var c,d=b(document.body),e=d.children(".wp-full-overlay"),g=b("#customize-info .panel-title.site-title"),h=b(".customize-controls-close"),i=b("#save"),j=b("#customize-save-button-wrapper"),k=b("#publish-settings"),l=b("#customize-footer-actions");f.bind("ready",function(){f.section.add(new f.OuterSection("publish_settings",{title:f.l10n.publishSettings,priority:0,active:f.settings.theme.active}))}),f.section("publish_settings",function(a){function b(){r||(r=f.utils.highlightButton(j,{delay:1e3,focusTarget:i}))}function c(){r&&(r(),r=null)}var d,e,g,h,l,m,n,o,p,q,r,s=1e3;e=new f.Control("trash_changeset",{type:"button",section:a.id,priority:30,input_attrs:{"class":"button-link button-link-delete",value:f.l10n.discardChanges}}),f.control.add(e),e.deferred.embedded.done(function(){e.container.find(".button-link").on("click",function(){confirm(f.l10n.trashConfirm)&&wp.customize.previewer.trash()})}),f.control.add(new f.PreviewLinkControl("changeset_preview_link",{section:a.id,priority:100})),h=function(){return!!f.state("activated").get()&&(!f.state("trashing").get()&&"trash"!==f.state("changesetStatus").get()&&(""!==f.state("changesetStatus").get()||!f.state("saved").get()))},a.active.validate=h,g=function(){a.active.set(h())},f.state("activated").bind(g),f.state("trashing").bind(g),f.state("saved").bind(g),f.state("changesetStatus").bind(g),g(),d=function(){k.toggle(a.active.get()),i.toggleClass("has-next-sibling",a.active.get())},d(),a.active.bind(d),f.state("selectedChangesetStatus").bind(c),a.contentContainer.find(".customize-action").text(f.l10n.updating),a.contentContainer.find(".customize-section-back").removeAttr("tabindex"),k.prop("disabled",!1),k.on("click",function(b){b.preventDefault(),a.expanded.set(!a.expanded.get())}),a.expanded.bind(function(a){var d;return k.attr("aria-expanded",String(a)),k.toggleClass("active",a),a?void c():(d=f.state("changesetStatus").get(),""!==d&&"auto-draft"!==d||(d="publish"),void(f.state("selectedChangesetStatus").get()!==d?b():"future"===f.state("selectedChangesetStatus").get()&&f.state("selectedChangesetDate").get()!==f.state("changesetDate").get()&&b()))}),l=new f.Control("changeset_status",{priority:10,type:"radio",section:"publish_settings",setting:f.state("selectedChangesetStatus"),templateId:"customize-selected-changeset-status-control",label:f.l10n.action,choices:f.settings.changeset.statusChoices}),f.control.add(l),m=new f.DateTimeControl("changeset_scheduled_date",{priority:20,section:"publish_settings",setting:f.state("selectedChangesetDate"),minYear:(new Date).getFullYear(),allowPastDate:!1,includeTime:!0,twelveHourFormat:/a/i.test(f.settings.timeFormat),description:f.l10n.scheduleDescription}),m.notifications.alt=!0,f.control.add(m),o=function(){f.state("selectedChangesetStatus").set("publish"),f.previewer.save()},q=function(){var a="future"===f.state("changesetStatus").get()&&"future"===f.state("selectedChangesetStatus").get()&&f.state("changesetDate").get()&&f.state("selectedChangesetDate").get()===f.state("changesetDate").get()&&f.utils.getRemainingTime(f.state("changesetDate").get())>=0;a&&!p?p=setInterval(function(){var a=f.utils.getRemainingTime(f.state("changesetDate").get());f.state("remainingTimeToPublish").set(a),a<=0&&(clearInterval(p),p=0,o())},s):!a&&p&&(clearInterval(p),p=0)},f.state("changesetDate").bind(q),f.state("selectedChangesetDate").bind(q),f.state("changesetStatus").bind(q),f.state("selectedChangesetStatus").bind(q),q(),m.active.validate=function(){return"future"===f.state("selectedChangesetStatus").get()},n=function(a){m.active.set("future"===a)},n(f.state("selectedChangesetStatus").get()),f.state("selectedChangesetStatus").bind(n),f.state("saving").bind(function(a){a&&"future"===f.state("selectedChangesetStatus").get()&&m.toggleFutureDateNotification(!m.isFutureDate())})}),b("#customize-controls").on("keydown",function(a){var c=13===a.which,d=b(a.target);c&&(d.is("input:not([type=button])")||d.is("select"))&&a.preventDefault()}),b(".customize-info").find("> .accordion-section-title .customize-help-toggle").on("click",function(){var a=b(this).closest(".accordion-section"),c=a.find(".customize-panel-description:first");a.hasClass("cannot-expand")||(a.hasClass("open")?(a.toggleClass("open"),c.slideUp(f.Panel.prototype.defaultExpandedArguments.duration,function(){c.trigger("toggled")}),b(this).attr("aria-expanded",!1)):(c.slideDown(f.Panel.prototype.defaultExpandedArguments.duration,function(){c.trigger("toggled")}),a.toggleClass("open"),b(this).attr("aria-expanded",!0)))}),f.previewer=new f.Previewer({container:"#customize-preview",form:"#customize-controls",previewUrl:f.settings.url.preview,allowedUrls:f.settings.url.allowed},{nonce:f.settings.nonce,query:function(a){var b={wp_customize:"on",customize_theme:f.settings.theme.stylesheet,nonce:this.nonce.preview,customize_changeset_uuid:f.settings.changeset.uuid};return!f.settings.changeset.autosaved&&f.state("saved").get()||(b.customize_autosaved="on"),b.customized=JSON.stringify(f.dirtyValues({unsaved:a&&a.excludeCustomizedSaved})),b},save:function(a){function d(a){m[a.id]=!0}var e,g,h=this,i=b.Deferred(),j=f.state("selectedChangesetStatus").get(),k=f.state("selectedChangesetDate").get(),l=f.state("processing"),m={},n=[],o=[],p=[];return a&&a.status&&(j=a.status),f.state("saving").get()&&(i.reject("already_saving"),i.promise()),f.state("saving").set(!0),g=function(){var e,g,l={},q=f._latestRevision,r="client_side_error";return f.bind("change",d),f.notifications.remove(r),f.each(function(a){a.notifications.each(function(b){"error"!==b.type||b.fromServer||(n.push(a.id),l[a.id]||(l[a.id]={}),l[a.id][b.code]=b)})}),f.control.each(function(a){(!a.setting||!a.setting.id&&a.active.get())&&a.notifications.each(function(b){"error"===b.type&&p.push([a])})}),o=_.union(p,_.values(f.findControlsForSettings(n))),_.isEmpty(o)?(g=b.extend(h.query({excludeCustomizedSaved:!1}),{nonce:h.nonce.save,customize_changeset_status:j}),a&&a.date?g.customize_changeset_date=a.date:"future"===j&&k&&(g.customize_changeset_date=k),a&&a.title&&(g.customize_changeset_title=a.title),f.trigger("save-request-params",g),e=wp.ajax.post("customize_save",g),f.state("processing").set(f.state("processing").get()+1),f.trigger("save",e),e.always(function(){f.state("processing").set(f.state("processing").get()-1),f.state("saving").set(!1),f.unbind("change",d)}),f.notifications.each(function(a){a.saveFailure&&f.notifications.remove(a.code)}),e.fail(function(a){var b,d;d={type:"error",dismissible:!0,fromServer:!0,saveFailure:!0},"0"===a?a="not_logged_in":"-1"===a&&(a="invalid_nonce"),"invalid_nonce"===a?h.cheatin():"not_logged_in"===a?(h.preview.iframe.hide(),h.login().done(function(){h.save(),h.preview.iframe.show()})):a.code?"not_future_date"===a.code&&f.section.has("publish_settings")&&f.section("publish_settings").active.get()&&f.control.has("changeset_scheduled_date")?f.control("changeset_scheduled_date").toggleFutureDateNotification(!0).focus():"changeset_locked"!==a.code&&(b=new f.Notification(a.code,_.extend(d,{message:a.message}))):b=new f.Notification("unknown_error",_.extend(d,{message:f.l10n.unknownRequestFail})),b&&f.notifications.add(b),a.setting_validities&&f._handleSettingValidities({settingValidities:a.setting_validities,focusInvalidControl:!0}),i.rejectWith(h,[a]),f.trigger("error",a),"changeset_already_published"===a.code&&a.next_changeset_uuid&&(f.settings.changeset.uuid=a.next_changeset_uuid,f.state("changesetStatus").set(""),f.settings.changeset.branching&&c.send("changeset-uuid",f.settings.changeset.uuid),f.previewer.send("changeset-uuid",f.settings.changeset.uuid))}),void e.done(function(a){h.send("saved",a),f.state("changesetStatus").set(a.changeset_status),a.changeset_date&&f.state("changesetDate").set(a.changeset_date),"publish"===a.changeset_status&&(f.each(function(a){a._dirty&&(_.isUndefined(f._latestSettingRevisions[a.id])||f._latestSettingRevisions[a.id]<=q)&&(a._dirty=!1)}),f.state("changesetStatus").set(""),f.settings.changeset.uuid=a.next_changeset_uuid,f.settings.changeset.branching&&c.send("changeset-uuid",f.settings.changeset.uuid)),f._lastSavedRevision=Math.max(q,f._lastSavedRevision),a.setting_validities&&f._handleSettingValidities({settingValidities:a.setting_validities,focusInvalidControl:!0}),i.resolveWith(h,[a]),f.trigger("saved",a),_.isEmpty(m)||f.state("saved").set(!1)})):(o[0][0].focus(),f.unbind("change",d),n.length&&f.notifications.add(new f.Notification(r,{message:(1===n.length?f.l10n.saveBlockedError.singular:f.l10n.saveBlockedError.plural).replace(/%s/g,String(n.length)),type:"error",dismissible:!0,saveFailure:!0})),i.rejectWith(h,[{setting_invalidities:l}]),f.state("saving").set(!1),i.promise())},0===l()?g():(e=function(){0===l()&&(f.state.unbind("change",e),g())},f.state.bind("change",e)),i.promise()},trash:function(){var a,c,d;f.state("trashing").set(!0),f.state("processing").set(f.state("processing").get()+1),a=wp.ajax.post("customize_trash",{customize_changeset_uuid:f.settings.changeset.uuid,nonce:f.settings.nonce.trash}),f.notifications.add(new f.OverlayNotification("changeset_trashing",{type:"info",message:f.l10n.revertingChanges,loading:!0})),c=function(){var a,c=document.createElement("a");f.state("changesetStatus").set("trash"),f.each(function(a){a._dirty=!1}),f.state("saved").set(!0),c.href=location.href,a=f.utils.parseQueryString(c.search.substr(1)),delete a.changeset_uuid,a["return"]=f.settings.url["return"],c.search=b.param(a),location.replace(c.href)},d=function(a,b){var c=a||"unknown_error";f.state("processing").set(f.state("processing").get()-1),f.state("trashing").set(!1),f.notifications.remove("changeset_trashing"),f.notifications.add(new f.Notification(c,{message:b||f.l10n.unknownError,dismissible:!0,type:"error"}))},a.done(function(a){c(a.message)}),a.fail(function(a){var b=a.code||"trashing_failed";a.success||"non_existent_changeset"===b||"changeset_already_trashed"===b?c(a.message):d(b,a.message)})},getFrontendPreviewUrl:function(){var a,c,d=this;return c=document.createElement("a"),c.href=d.previewUrl.get(),a=f.utils.parseQueryString(c.search.substr(1)),f.state("changesetStatus").get()&&"publish"!==f.state("changesetStatus").get()&&(a.customize_changeset_uuid=f.settings.changeset.uuid),f.state("activated").get()||(a.customize_theme=f.settings.theme.stylesheet),c.search=b.param(a),c.href}}),b.ajaxPrefilter(function(a){/wp_customize=on/.test(a.data)&&(a.data+="&"+b.param({customize_preview_nonce:f.settings.nonce.preview}))}),f.previewer.bind("nonce",function(a){b.extend(this.nonce,a)}),f.bind("nonce-refresh",function(a){b.extend(f.settings.nonce,a),b.extend(f.previewer.nonce,a),f.previewer.send("nonce-refresh",a)}),b.each(f.settings.settings,function(a,b){var c=f.settingConstructor[b.type]||f.Setting;f.add(new c(a,b.value,{transport:b.transport,previewer:f.previewer,dirty:!!b.dirty}))}),b.each(f.settings.panels,function(a,b){var c,d=f.panelConstructor[b.type]||f.Panel;c=_.extend({params:b},b),f.panel.add(new d(a,c))}),b.each(f.settings.sections,function(a,b){var c,d=f.sectionConstructor[b.type]||f.Section;c=_.extend({params:b},b),f.section.add(new d(a,c))}),b.each(f.settings.controls,function(a,b){var c,d=f.controlConstructor[b.type]||f.Control;c=_.extend({params:b},b),f.control.add(new d(a,c))}),_.each(["panel","section","control"],function(a){var b=f.settings.autofocus[a];b&&f[a](b,function(a){a.deferred.embedded.done(function(){f.previewer.deferred.active.done(function(){a.focus()})})})}),f.bind("ready",f.reflowPaneContents),b([f.panel,f.section,f.control]).each(function(a,b){var c=_.debounce(f.reflowPaneContents,f.settings.timeouts.reflowPaneContents);b.bind("add",c),b.bind("change",c),b.bind("remove",c)}),f.bind("ready",function(){var a,c,d;f.notifications.container=b("#customize-notifications-area"),f.notifications.bind("change",_.debounce(function(){f.notifications.render()})),a=b(".wp-full-overlay-sidebar-content"),f.notifications.bind("rendered",function(){a.css("top",""),0!==f.notifications.count()&&(c=f.notifications.container.outerHeight()+1,d=parseInt(a.css("top"),10),a.css("top",d+c+"px")),f.notifications.trigger("sidebarTopUpdated")}),f.notifications.render()}),function(a){var c,e,g=a.instance("saved"),j=a.instance("saving"),k=a.instance("trashing"),l=a.instance("activated"),m=a.instance("processing"),n=a.instance("paneVisible"),o=a.instance("expandedPanel"),p=a.instance("expandedSection"),q=a.instance("changesetStatus"),r=a.instance("selectedChangesetStatus"),s=a.instance("changesetDate"),t=a.instance("selectedChangesetDate"),u=a.instance("previewerAlive"),v=a.instance("editShortcutVisibility"),w=a.instance("changesetLocked");a.bind("change",function(){var a;l()?""===q.get()&&g()?(f.settings.changeset.currentUserCanPublish?i.val(f.l10n.published):i.val(f.l10n.saved),h.find(".screen-reader-text").text(f.l10n.close)):("draft"===r()?g()&&r()===q()?i.val(f.l10n.draftSaved):i.val(f.l10n.saveDraft):"future"===r()?g()&&r()===q()?s.get()!==t.get()?i.val(f.l10n.schedule):i.val(f.l10n.scheduled):i.val(f.l10n.schedule):f.settings.changeset.currentUserCanPublish&&i.val(f.l10n.publish),h.find(".screen-reader-text").text(f.l10n.cancel)):(i.val(f.l10n.activate),h.find(".screen-reader-text").text(f.l10n.cancel)),a=!j()&&!k()&&!w()&&(!l()||!g()||q()!==r()&&""!==q()||"future"===r()&&s.get()!==t.get()),i.prop("disabled",!a)}),r.validate=function(a){return""===a||"auto-draft"===a?null:a},e=f.settings.changeset.currentUserCanPublish?"publish":"draft",q(f.settings.changeset.status),w(Boolean(f.settings.changeset.lockUser)),s(f.settings.changeset.publishDate),t(f.settings.changeset.publishDate),r(""===f.settings.changeset.status||"auto-draft"===f.settings.changeset.status?e:f.settings.changeset.status),r.link(q),g(!0),""===q()&&f.each(function(a){a._dirty&&g(!1)}),j(!1),l(f.settings.theme.active),m(0),n(!0),o(!1),p(!1),u(!0),v("visible"),f.bind("change",function(){a("saved").get()&&a("saved").set(!1)}),f.settings.changeset.branching&&g.bind(function(a){a||c(!0)}),j.bind(function(a){d.toggleClass("saving",a)}),k.bind(function(a){d.toggleClass("trashing",a)}),f.bind("saved",function(b){a("saved").set(!0),"publish"===b.changeset_status&&a("activated").set(!0)}),l.bind(function(a){a&&f.trigger("activated")}),c=function(a){var c,d;if(history.replaceState){if(c=document.createElement("a"),c.href=location.href,d=f.utils.parseQueryString(c.search.substr(1)),a){if(d.changeset_uuid===f.settings.changeset.uuid)return;d.changeset_uuid=f.settings.changeset.uuid}else{
if(!d.changeset_uuid)return;delete d.changeset_uuid}c.search=b.param(d),history.replaceState({},document.title,c.href)}},f.settings.changeset.branching&&q.bind(function(a){c(""!==a&&"publish"!==a&&"trash"!==a)})}(f.state),function(){function a(a){a&&a.lockUser&&(f.settings.changeset.lockUser=a.lockUser),f.state("changesetLocked").set(!0),f.notifications.add(new c("changeset_locked",{lockUser:f.settings.changeset.lockUser,allowOverride:Boolean(a&&a.allowOverride)}))}var c=f.OverlayNotification.extend({templateId:"customize-changeset-locked-notification",lockUser:null,initialize:function(a,b){var c,d,e=this;c=a||"changeset_locked",d=_.extend({type:"warning",containerClasses:"",lockUser:{}},b),d.containerClasses+=" notification-changeset-locked",f.OverlayNotification.prototype.initialize.call(e,c,d)},render:function(){var a,b,c,d,e=this;return b=_.extend({allowOverride:!1,returnUrl:f.settings.url["return"],previewUrl:f.previewer.previewUrl.get(),frontendPreviewUrl:f.previewer.getFrontendPreviewUrl()},this),a=f.OverlayNotification.prototype.render.call(b),f.requestChangesetUpdate({},{autosave:!0}).fail(function(b){b.autosaved||a.find(".notice-error").prop("hidden",!1).text(b.message||f.l10n.unknownRequestFail)}),c=a.find(".customize-notice-take-over-button"),c.on("click",function(b){b.preventDefault(),d||(c.addClass("disabled"),d=wp.ajax.post("customize_override_changeset_lock",{wp_customize:"on",customize_theme:f.settings.theme.stylesheet,customize_changeset_uuid:f.settings.changeset.uuid,nonce:f.settings.nonce.override_lock}),d.done(function(){f.notifications.remove(e.code),f.state("changesetLocked").set(!1)}),d.fail(function(b){var e=b.message||f.l10n.unknownRequestFail;a.find(".notice-error").prop("hidden",!1).text(e),d.always(function(){c.removeClass("disabled")})}),d.always(function(){d=null}))}),a}});f.settings.changeset.lockUser&&a({allowOverride:!0}),b(document).on("heartbeat-send.update_lock_notice",function(a,b){b.check_changeset_lock=!0,b.changeset_uuid=f.settings.changeset.uuid}),b(document).on("heartbeat-tick.update_lock_notice",function(b,c){var d,e="changeset_locked";c.customize_changeset_lock_user&&(d=f.notifications(e),d&&d.lockUser.id!==f.settings.changeset.lockUser.id&&f.notifications.remove(e),a({lockUser:c.customize_changeset_lock_user}))}),f.bind("error",function(b){"changeset_locked"===b.code&&b.lock_user&&a({lockUser:b.lock_user})})}(),function(){function a(){var a,c;return a=document.createElement("a"),a.href=location.href,c=f.utils.parseQueryString(a.search.substr(1)),f.settings.changeset.latestAutoDraftUuid?c.changeset_uuid=f.settings.changeset.latestAutoDraftUuid:c.customize_autosaved="on",c["return"]=f.settings.url["return"],a.search=b.param(c),a.href}function c(a){var c,d=document.createElement("a"),e=0;d.href=location.href,c=f.utils.parseQueryString(d.search.substr(1)),_.each(a,function(a){"undefined"!=typeof c[a]&&(e+=1,delete c[a])}),0!==e&&(d.search=b.param(c),history.replaceState({},document.title,d.href))}function d(){h||(wp.ajax.post("customize_dismiss_autosave_or_lock",{wp_customize:"on",customize_theme:f.settings.theme.stylesheet,customize_changeset_uuid:f.settings.changeset.uuid,nonce:f.settings.nonce.dismiss_autosave_or_lock,dismiss_autosave:!0}),h=!0)}function e(){var b,c="autosave_available";f.notifications.add(new f.Notification(c,{message:f.l10n.autosaveNotice,type:"warning",dismissible:!0,render:function(){var b,c=f.Notification.prototype.render.call(this);return b=c.find("a"),b.prop("href",a()),b.on("click",function(b){b.preventDefault(),location.replace(a())}),c.find(".notice-dismiss").on("click",d),c}})),b=function(){d(),f.notifications.remove(c),f.unbind("change",b),f.state("changesetStatus").unbind(b)},f.bind("change",b),f.state("changesetStatus").bind(b)}var g=[],h=!1;f.settings.changeset.autosaved&&(f.state("saved").set(!1),g.push("customize_autosaved")),f.settings.changeset.branching||f.settings.changeset.status&&"auto-draft"!==f.settings.changeset.status||g.push("changeset_uuid"),g.length>0&&c(g),(f.settings.changeset.latestAutoDraftUuid||f.settings.changeset.hasAutosaveRevision)&&e()}(),f.previewer.previewUrl()?f.previewer.refresh():f.previewer.previewUrl(f.settings.url.home),i.click(function(a){f.previewer.save(),a.preventDefault()}).keydown(function(a){9!==a.which&&(13===a.which&&f.previewer.save(),a.preventDefault())}),h.keydown(function(a){9!==a.which&&(13===a.which&&this.click(),a.preventDefault())}),b(".collapse-sidebar").on("click",function(){f.state("paneVisible").set(!f.state("paneVisible").get())}),f.state("paneVisible").bind(function(a){e.toggleClass("preview-only",!a),e.toggleClass("expanded",a),e.toggleClass("collapsed",!a),a?b(".collapse-sidebar").attr({"aria-expanded":"true","aria-label":f.l10n.collapseSidebar}):b(".collapse-sidebar").attr({"aria-expanded":"false","aria-label":f.l10n.expandSidebar})}),d.on("keydown",function(a){var c,e=[],g=[],h=[];if(27===a.which&&(b(a.target).is("body")||b.contains(b("#customize-controls")[0],a.target))&&(f.control.each(function(a){a.expanded&&a.expanded()&&_.isFunction(a.collapse)&&e.push(a)}),f.section.each(function(a){a.expanded()&&g.push(a)}),f.panel.each(function(a){a.expanded()&&h.push(a)}),e.length>0&&0===g.length&&(e.length=0),c=e[0]||g[0]||h[0])){if("themes"===c.params.type)return void(d.hasClass("modal-open")?c.closeDetails():f.panel.has("themes")&&f.panel("themes").collapse());c.collapse(),a.preventDefault()}}),b(".customize-controls-preview-toggle").on("click",function(){f.state("paneVisible").set(!f.state("paneVisible").get())}),function(){var a,c,d,e,g,h,i,j=b(".wp-full-overlay-sidebar-content");a=function(a){var b,g=a,i=f.state("expandedSection").get(),j=f.state("expandedPanel").get();if(h&&h.element&&(d(h.element),h.element.find(".description").off("toggled",c)),!g)if(!i&&j&&j.contentContainer)g=j;else{if(j||!i||!i.contentContainer)return void(h=!1);g=i}b=g.contentContainer.find(".customize-section-title, .panel-meta").first(),b.length?(h={instance:g,element:b,parent:b.closest(".customize-pane-child"),height:b.outerHeight()},h.element.find(".description").on("toggled",c),i&&e(h.element,h.parent)):h=!1},f.state("expandedSection").bind(a),f.state("expandedPanel").bind(a),j.on("scroll",_.throttle(function(){if(h){var a,b=j.scrollTop();a=i?b===i?0:b>i?1:-1:1,i=b,0!==a&&g(h,b,a)}},8)),f.notifications.bind("sidebarTopUpdated",function(){h&&h.element.hasClass("is-sticky")&&h.element.css("top",j.css("top"))}),d=function(a){a.hasClass("is-sticky")&&a.removeClass("is-sticky").addClass("maybe-sticky is-in-view").css("top",j.scrollTop()+"px")},e=function(a,b){a.hasClass("is-in-view")&&(a.removeClass("maybe-sticky is-in-view").css({width:"",top:""}),b.css("padding-top",""))},c=function(){h.height=h.element.outerHeight()},g=function(a,b,c){var d=a.element,e=a.parent,f=a.height,g=parseInt(d.css("top"),10),h=d.hasClass("maybe-sticky"),i=d.hasClass("is-sticky"),k=d.hasClass("is-in-view"),l=-1===c;if(!l)return i&&(g=b,d.removeClass("is-sticky").css({top:g+"px",width:""})),void(k&&b>g+f&&(d.removeClass("is-in-view"),e.css("padding-top","")));if(!h&&b>=f)h=!0,d.addClass("maybe-sticky");else if(0===b)return d.removeClass("maybe-sticky is-in-view is-sticky").css({top:"",width:""}),void e.css("padding-top","");k&&!i?g>=b&&d.addClass("is-sticky").css({top:j.css("top"),width:e.outerWidth()+"px"}):h&&!k&&(d.addClass("is-in-view").css("top",b-f+"px"),e.css("padding-top",f+"px"))}}(),f.previewedDevice=f.state("previewedDevice"),f.bind("ready",function(){_.find(f.settings.previewableDevices,function(a,b){if(!0===a["default"])return f.previewedDevice.set(b),!0})}),l.find(".devices button").on("click",function(a){f.previewedDevice.set(b(a.currentTarget).data("device"))}),f.previewedDevice.bind(function(a){var c=b(".wp-full-overlay"),d="";l.find(".devices button").removeClass("active").attr("aria-pressed",!1),l.find(".devices .preview-"+a).addClass("active").attr("aria-pressed",!0),b.each(f.settings.previewableDevices,function(a){d+=" preview-"+a}),c.removeClass(d).addClass("preview-"+a)}),g.length&&f("blogname",function(a){var c=function(){g.text(b.trim(a())||f.l10n.untitledBlogName)};a.bind(c),c()}),c=new f.Messenger({url:f.settings.url.parent,channel:"loader"}),function(){function a(){var a;return f.state("activated").get()?(a=f.state("changesetStatus").get(),""!==a&&"auto-draft"!==a||(a="publish"),f.state("selectedChangesetStatus").get()===a&&(("future"!==f.state("selectedChangesetStatus").get()||f.state("selectedChangesetDate").get()===f.state("changesetDate").get())&&(f.state("saved").get()&&"auto-draft"!==f.state("changesetStatus").get()))):0===f._latestRevision}function d(){f.unbind("change",d),f.state("selectedChangesetStatus").unbind(d),f.state("selectedChangesetDate").unbind(d),b(window).on("beforeunload.customize-confirm",function(){if(!a()&&!f.state("changesetLocked").get())return setTimeout(function(){e.removeClass("customize-loading")},1),f.l10n.saveAlert})}function g(){var c=b.Deferred(),d=!1,e=!1;return a()?e=!0:confirm(f.l10n.saveAlert)?(e=!0,f.each(function(a){a._dirty=!1}),b(document).off("visibilitychange.wp-customize-changeset-update"),b(window).off("beforeunload.wp-customize-changeset-update"),h.css("cursor","progress"),""!==f.state("changesetStatus").get()&&(d=!0)):c.reject(),(e||d)&&wp.ajax.send("customize_dismiss_autosave_or_lock",{timeout:500,data:{wp_customize:"on",customize_theme:f.settings.theme.stylesheet,customize_changeset_uuid:f.settings.changeset.uuid,nonce:f.settings.nonce.dismiss_autosave_or_lock,dismiss_autosave:d,dismiss_lock:e}}).always(function(){c.resolve()}),c.promise()}var i=!1;c.bind("back",function(){i=!0}),f.bind("change",d),f.state("selectedChangesetStatus").bind(d),f.state("selectedChangesetDate").bind(d),c.bind("confirm-close",function(){g().done(function(){c.send("confirmed-close",!0)}).fail(function(){c.send("confirmed-close",!1)})}),h.on("click.customize-controls-close",function(a){a.preventDefault(),i?c.send("close"):g().done(function(){b(window).off("beforeunload.customize-confirm"),window.location.href=h.prop("href")})})}(),b.each(["saved","change"],function(a,b){f.bind(b,function(){c.send(b)})}),f.bind("title",function(a){c.send("title",a)}),f.settings.changeset.branching&&c.send("changeset-uuid",f.settings.changeset.uuid),c.send("ready"),b.each({background_image:{controls:["background_preset","background_position","background_size","background_repeat","background_attachment"],callback:function(a){return!!a}},show_on_front:{controls:["page_on_front","page_for_posts"],callback:function(a){return"page"===a}},header_textcolor:{controls:["header_textcolor"],callback:function(a){return"blank"!==a}}},function(a,c){f(a,function(a){b.each(c.controls,function(b,d){f.control(d,function(b){var d=function(a){b.container.toggle(c.callback(a))};d(a.get()),a.bind(d)})})})}),f.control("background_preset",function(a){var b,c,d,e,g,h;b={"default":[!1,!1,!1,!1],fill:[!0,!1,!1,!1],fit:[!0,!1,!0,!1],repeat:[!0,!1,!1,!0],custom:[!0,!0,!0,!0]},c=[_wpCustomizeBackground.defaults["default-position-x"],_wpCustomizeBackground.defaults["default-position-y"],_wpCustomizeBackground.defaults["default-size"],_wpCustomizeBackground.defaults["default-repeat"],_wpCustomizeBackground.defaults["default-attachment"]],d={"default":c,fill:["left","top","cover","no-repeat","fixed"],fit:["left","top","contain","no-repeat","fixed"],repeat:["left","top","auto","repeat","scroll"]},e=function(a){_.each(["background_position","background_size","background_repeat","background_attachment"],function(c,d){var e=f.control(c);e&&e.container.toggle(b[a][d])})},g=function(a){_.each(["background_position_x","background_position_y","background_size","background_repeat","background_attachment"],function(b,c){var e=f(b);e&&e.set(d[a][c])})},h=a.setting.get(),e(h),a.setting.bind("change",function(a){e(a),"custom"!==a&&g(a)})}),f.control("background_repeat",function(a){a.elements[0].unsync(f("background_repeat")),a.element=new f.Element(a.container.find("input")),a.element.set("no-repeat"!==a.setting()),a.element.bind(function(b){a.setting.set(b?"repeat":"no-repeat")}),a.setting.bind(function(b){a.element.set("no-repeat"!==b)})}),f.control("background_attachment",function(a){a.elements[0].unsync(f("background_attachment")),a.element=new f.Element(a.container.find("input")),a.element.set("fixed"!==a.setting()),a.element.bind(function(b){a.setting.set(b?"scroll":"fixed")}),a.setting.bind(function(b){a.element.set("fixed"!==b)})}),f.control("display_header_text",function(a){var b="";a.elements[0].unsync(f("header_textcolor")),a.element=new f.Element(a.container.find("input")),a.element.set("blank"!==a.setting()),a.element.bind(function(c){c||(b=f("header_textcolor").get()),a.setting.set(c?b:"blank")}),a.setting.bind(function(b){a.element.set("blank"!==b)})}),f("show_on_front","page_on_front","page_for_posts",function(a,b,c){var d=function(){var d,e,g=this,h="show_on_front_page_collision";d=parseInt(b(),10),e=parseInt(c(),10),"page"===a()&&(g===b&&d>0&&f.previewer.previewUrl.set(f.settings.url.home),g===c&&e>0&&f.previewer.previewUrl.set(f.settings.url.home+"?page_id="+e)),"page"===a()&&d&&e&&d===e?a.notifications.add(new f.Notification(h,{type:"error",message:f.l10n.pageOnFrontError})):a.notifications.remove(h)};a.bind(d),b.bind(d),c.bind(d),d.call(a,a()),f.control("show_on_front",function(a){a.deferred.embedded.done(function(){a.container.append(a.getNotificationsContainerElement())})})}),function(){var a=b.Deferred();f.section("custom_css",function(b){b.deferred.embedded.done(function(){b.expanded()?a.resolve(b):b.expanded.bind(function(c){c&&a.resolve(b)})})}),a.done(function(a){var b=f.control("custom_css");b.container.find(".customize-control-title:first").addClass("screen-reader-text"),a.container.find(".section-description-buttons .section-description-close").on("click",function(){a.container.find(".section-meta .customize-section-description:first").removeClass("open").slideUp(),a.container.find(".customize-help-toggle").attr("aria-expanded","false").focus()}),b&&!b.setting.get()&&(a.container.find(".section-meta .customize-section-description:first").addClass("open").show().trigger("toggled"),a.container.find(".customize-help-toggle").attr("aria-expanded","true"))})}(),f.control("header_video",function(a){a.deferred.embedded.done(function(){var b=function(){var b=f.section(a.section()),c="video_header_not_available";b&&(a.active.get()?b.notifications.remove(c):b.notifications.add(new f.Notification(c,{type:"info",message:f.l10n.videoHeaderNotice})))};b(),a.active.bind(b)})}),f.previewer.bind("selective-refresh-setting-validities",function(a){f._handleSettingValidities({settingValidities:a,focusInvalidControl:!1})}),f.previewer.bind("focus-control-for-setting",function(a){var b=[];f.control.each(function(c){var d=_.pluck(c.settings,"id");-1!==_.indexOf(d,a)&&b.push(c)}),b.length&&(b.sort(function(a,b){return a.priority()-b.priority()}),b[0].focus())}),f.previewer.bind("refresh",function(){f.previewer.refresh()}),f.state("paneVisible").bind(function(a){var c;c=window.matchMedia?window.matchMedia("screen and ( max-width: 640px )").matches:b(window).width()<=640,f.state("editShortcutVisibility").set(a||c?"visible":"hidden")}),window.matchMedia&&window.matchMedia("screen and ( max-width: 640px )").addListener(function(){var a=f.state("paneVisible");a.callbacks.fireWith(a,[a.get(),a.get()])}),f.previewer.bind("edit-shortcut-visibility",function(a){f.state("editShortcutVisibility").set(a)}),f.state("editShortcutVisibility").bind(function(a){f.previewer.send("edit-shortcut-visibility",a)}),f.bind("change",a),b(document).one("tinymce-editor-setup",function(){window.tinymce.ui.FloatPanel&&(!window.tinymce.ui.FloatPanel.zIndex||window.tinymce.ui.FloatPanel.zIndex<500001)&&(window.tinymce.ui.FloatPanel.zIndex=500001)}),d.addClass("ready"),f.trigger("ready")}})}(wp,jQuery);