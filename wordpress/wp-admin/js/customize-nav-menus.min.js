!function(a,b,c){"use strict";function d(a){return"nav_menu_item["+a+"]"}function e(b){return b=b||"",b=c("<div>").text(b).html(),b=c.trim(b),b||a.Menus.data.l10n.unnamed}wpNavMenu.originalInit=wpNavMenu.init,wpNavMenu.options.menuItemDepthPerLevel=20,wpNavMenu.options.sortableItems="> .customize-control-nav_menu_item",wpNavMenu.options.targetTolerance=10,wpNavMenu.init=function(){this.jQueryExtensions()},a.Menus=a.Menus||{},a.Menus.data={itemTypes:[],l10n:{},settingTransport:"refresh",phpIntMax:0,defaultSettingValues:{nav_menu:{},nav_menu_item:{}},locationSlugMappedToName:{}},"undefined"!=typeof _wpCustomizeNavMenusSettings&&c.extend(a.Menus.data,_wpCustomizeNavMenusSettings),a.Menus.generatePlaceholderAutoIncrementId=function(){return-Math.ceil(a.Menus.data.phpIntMax*Math.random())},a.Menus.AvailableItemModel=Backbone.Model.extend(c.extend({id:null},a.Menus.data.defaultSettingValues.nav_menu_item)),a.Menus.AvailableItemCollection=Backbone.Collection.extend({model:a.Menus.AvailableItemModel,sort_key:"order",comparator:function(a){return-a.get(this.sort_key)},sortByField:function(a){this.sort_key=a,this.sort()}}),a.Menus.availableMenuItems=new a.Menus.AvailableItemCollection(a.Menus.data.availableMenuItems),a.Menus.insertAutoDraftPost=function(d){var e,f=c.Deferred();return e=b.ajax.post("customize-nav-menus-insert-auto-draft",{"customize-menus-nonce":a.settings.nonce["customize-menus"],wp_customize:"on",customize_changeset_uuid:a.settings.changeset.uuid,params:d}),e.done(function(b){b.post_id&&(a("nav_menus_created_posts").set(a("nav_menus_created_posts").get().concat([b.post_id])),"page"===d.post_type&&(a.section.has("static_front_page")&&a.section("static_front_page").activate(),a.control.each(function(a){var c;"dropdown-pages"===a.params.type&&(c=a.container.find('select[name^="_customize-dropdown-pages-"]'),c.append(new Option(d.post_title,b.post_id)))})),f.resolve(b))}),e.fail(function(a){var b=a||"";"undefined"!=typeof a.message&&(b=a.message),console.error(b),f.rejectWith(b)}),f.promise()},a.Menus.AvailableMenuItemsPanelView=b.Backbone.View.extend({el:"#available-menu-items",events:{"input #menu-items-search":"debounceSearch","focus .menu-item-tpl":"focus","click .menu-item-tpl":"_submit","click #custom-menu-item-submit":"_submitLink","keypress #custom-menu-item-name":"_submitLink","click .new-content-item .add-content":"_submitNew","keypress .create-item-input":"_submitNew",keydown:"keyboardAccessible"},selected:null,currentMenuControl:null,debounceSearch:null,$search:null,$clearResults:null,searchTerm:"",rendered:!1,pages:{},sectionContent:"",loading:!1,addingNew:!1,initialize:function(){var b=this;a.panel.has("nav_menus")&&(this.$search=c("#menu-items-search"),this.$clearResults=this.$el.find(".clear-results"),this.sectionContent=this.$el.find(".available-menu-items-list"),this.debounceSearch=_.debounce(b.search,500),_.bindAll(this,"close"),c("#customize-controls, .customize-section-back").on("click keydown",function(a){var d=c(a.target).is(".item-delete, .item-delete *"),e=c(a.target).is(".add-new-menu-item, .add-new-menu-item *");!c("body").hasClass("adding-menu-items")||d||e||b.close()}),this.$clearResults.on("click",function(){b.$search.val("").focus().trigger("keyup")}),this.$el.on("input","#custom-menu-item-name.invalid, #custom-menu-item-url.invalid",function(){c(this).removeClass("invalid")}),a.panel("nav_menus").container.bind("expanded",function(){b.rendered||(b.initList(),b.rendered=!0)}),this.sectionContent.scroll(function(){var a=b.$el.find(".accordion-section.open .available-menu-items-list").prop("scrollHeight"),d=b.$el.find(".accordion-section.open").height();if(!b.loading&&c(this).scrollTop()>.75*a-d){var e=c(this).data("type"),f=c(this).data("object");"search"===e?b.searchTerm&&b.doSearch(b.pages.search):b.loadItems([{type:e,object:f}])}}),a.previewer.bind("url",this.close),b.delegateEvents())},search:function(a){var b=c("#available-menu-items-search"),d=c("#available-menu-items .accordion-section").not(b);a&&this.searchTerm!==a.target.value&&(""===a.target.value||b.hasClass("open")?""===a.target.value&&(b.removeClass("open"),d.show(),this.$clearResults.removeClass("is-visible")):(d.fadeOut(100),b.find(".accordion-section-content").slideDown("fast"),b.addClass("open"),this.$clearResults.addClass("is-visible")),this.searchTerm=a.target.value,this.pages.search=1,this.doSearch(1))},doSearch:function(d){var e,f=this,g=c("#available-menu-items-search"),h=g.find(".accordion-section-content"),i=b.template("available-menu-item");if(f.currentRequest&&f.currentRequest.abort(),!(d<0)){if(d>1)g.addClass("loading-more"),h.attr("aria-busy","true"),b.a11y.speak(a.Menus.data.l10n.itemsLoadingMore);else if(""===f.searchTerm)return h.html(""),void b.a11y.speak("");g.addClass("loading"),f.loading=!0,e=a.previewer.query({excludeCustomizedSaved:!0}),_.extend(e,{"customize-menus-nonce":a.settings.nonce["customize-menus"],wp_customize:"on",search:f.searchTerm,page:d}),f.currentRequest=b.ajax.post("search-available-menu-items-customizer",e),f.currentRequest.done(function(c){var e;1===d&&h.empty(),g.removeClass("loading loading-more"),h.attr("aria-busy","false"),g.addClass("open"),f.loading=!1,e=new a.Menus.AvailableItemCollection(c.items),f.collection.add(e.models),e.each(function(a){h.append(i(a.attributes))}),20>e.length?f.pages.search=-1:f.pages.search=f.pages.search+1,e&&d>1?b.a11y.speak(a.Menus.data.l10n.itemsFoundMore.replace("%d",e.length)):e&&1===d&&b.a11y.speak(a.Menus.data.l10n.itemsFound.replace("%d",e.length))}),f.currentRequest.fail(function(a){a.message&&(h.empty().append(c('<li class="nothing-found"></li>').text(a.message)),b.a11y.speak(a.message)),f.pages.search=-1}),f.currentRequest.always(function(){g.removeClass("loading loading-more"),h.attr("aria-busy","false"),f.loading=!1,f.currentRequest=null})}},initList:function(){var b=this;_.each(a.Menus.data.itemTypes,function(a){b.pages[a.type+":"+a.object]=0}),b.loadItems(a.Menus.data.itemTypes)},loadItems:function(d,e){var f,g,h,i,j=this,k=[],l={};i=b.template("available-menu-item"),f=_.isString(d)&&_.isString(e)?[{type:d,object:e}]:d,_.each(f,function(a){var b,d=a.type+":"+a.object;-1!==j.pages[d]&&(b=c("#available-menu-items-"+a.type+"-"+a.object),b.find(".accordion-section-title").addClass("loading"),l[d]=b,k.push({object:a.object,type:a.type,page:j.pages[d]}))}),0!==k.length&&(j.loading=!0,g=a.previewer.query({excludeCustomizedSaved:!0}),_.extend(g,{"customize-menus-nonce":a.settings.nonce["customize-menus"],wp_customize:"on",item_types:k}),h=b.ajax.post("load-available-menu-items-customizer",g),h.done(function(b){var c;_.each(b.items,function(b,d){return 0===b.length?(0===j.pages[d]&&l[d].find(".accordion-section-title").addClass("cannot-expand").removeClass("loading").find(".accordion-section-title > button").prop("tabIndex",-1),void(j.pages[d]=-1)):("post_type:page"!==d||l[d].hasClass("open")||l[d].find(".accordion-section-title > button").click(),b=new a.Menus.AvailableItemCollection(b),j.collection.add(b.models),c=l[d].find(".available-menu-items-list"),b.each(function(a){c.append(i(a.attributes))}),void(j.pages[d]+=1))})}),h.fail(function(a){"undefined"!=typeof console&&console.error&&console.error(a)}),h.always(function(){_.each(l,function(a){a.find(".accordion-section-title").removeClass("loading")}),j.loading=!1}))},itemSectionHeight:function(){var a,b,c,d,e;c=window.innerHeight,a=this.$el.find(".accordion-section:not( #available-menu-items-search ) .accordion-section-content"),b=this.$el.find('.accordion-section:not( #available-menu-items-search ) .available-menu-items-list:not(":only-child")'),d=46*(1+a.length)+14,e=c-d,120<e&&290>e&&(a.css("max-height",e),b.css("max-height",e-60))},select:function(a){this.selected=c(a),this.selected.siblings(".menu-item-tpl").removeClass("selected"),this.selected.addClass("selected")},focus:function(a){this.select(c(a.currentTarget))},_submit:function(a){"keypress"===a.type&&13!==a.which&&32!==a.which||this.submit(c(a.currentTarget))},submit:function(a){var b,d;a||(a=this.selected),a&&this.currentMenuControl&&(this.select(a),b=c(this.selected).data("menu-item-id"),d=this.collection.findWhere({id:b}),d&&(this.currentMenuControl.addItemToMenu(d.attributes),c(a).find(".menu-item-handle").addClass("item-added")))},_submitLink:function(a){"keypress"===a.type&&13!==a.which||this.submitLink()},submitLink:function(){var b,d,e=c("#custom-menu-item-name"),f=c("#custom-menu-item-url");if(this.currentMenuControl){if(d=/^((\w+:)?\/\/\w.*|\w+:(?!\/\/$)|\/|\?|#)/,""===e.val())return void e.addClass("invalid");if(!d.test(f.val()))return void f.addClass("invalid");b={title:e.val(),url:f.val(),type:"custom",type_label:a.Menus.data.l10n.custom_label,object:"custom"},this.currentMenuControl.addItemToMenu(b),f.val("http://"),e.val("")}},_submitNew:function(a){var b;"keypress"===a.type&&13!==a.which||this.addingNew||(b=c(a.target).closest(".accordion-section"),this.submitNew(b))},submitNew:function(d){var e,f=this,g=d.find(".create-item-input"),h=g.val(),i=d.find(".available-menu-items-list"),j=i.data("type"),k=i.data("object"),l=i.data("type_label");if(this.currentMenuControl&&"post_type"===j){if(""===c.trim(g.val()))return g.addClass("invalid"),void g.focus();g.removeClass("invalid"),d.find(".accordion-section-title").addClass("loading"),f.addingNew=!0,g.attr("disabled","disabled"),e=a.Menus.insertAutoDraftPost({post_title:h,post_type:k}),e.done(function(e){var h,i,m;h=new a.Menus.AvailableItemModel({id:"post-"+e.post_id,title:g.val(),type:j,type_label:l,object:k,object_id:e.post_id,url:e.url}),f.currentMenuControl.addItemToMenu(h.attributes),a.Menus.availableMenuItemsPanel.collection.add(h),i=d.find(".available-menu-items-list"),m=c(b.template("available-menu-item")(h.attributes)),m.find(".menu-item-handle:first").addClass("item-added"),i.prepend(m),i.scrollTop(),g.val("").removeAttr("disabled"),f.addingNew=!1,d.find(".accordion-section-title").removeClass("loading")})}},open:function(b){var d,e=this;this.currentMenuControl=b,this.itemSectionHeight(),a.section.has("publish_settings")&&a.section("publish_settings").collapse(),c("body").addClass("adding-menu-items"),d=function(){e.close(),c(this).off("click",d)},c("#customize-preview").on("click",d),_(this.currentMenuControl.getMenuItemControls()).each(function(a){a.collapseForm()}),this.$el.find(".selected").removeClass("selected"),this.$search.focus()},close:function(a){a=a||{},a.returnFocus&&this.currentMenuControl&&this.currentMenuControl.container.find(".add-new-menu-item").focus(),this.currentMenuControl=null,this.selected=null,c("body").removeClass("adding-menu-items"),c("#available-menu-items .menu-item-handle.item-added").removeClass("item-added"),this.$search.val("").trigger("keyup")},keyboardAccessible:function(a){var b=13===a.which,d=27===a.which,e=9===a.which&&a.shiftKey,f=c(a.target).is(this.$search);b&&!this.$search.val()||(f&&e?(this.currentMenuControl.container.find(".add-new-menu-item").focus(),a.preventDefault()):d&&this.close({returnFocus:!0}))}}),a.Menus.MenusPanel=a.Panel.extend({attachEvents:function(){a.Panel.prototype.attachEvents.call(this);var b=this,d=b.container.find(".panel-meta"),e=d.find(".customize-help-toggle"),f=d.find(".customize-panel-description"),g=c("#screen-options-wrap"),h=d.find(".customize-screen-options-toggle");h.on("click keydown",function(b){if(!a.utils.isKeydownButNotEnterEvent(b))return b.preventDefault(),f.not(":hidden")&&(f.slideUp("fast"),e.attr("aria-expanded","false")),"true"===h.attr("aria-expanded")?(h.attr("aria-expanded","false"),d.removeClass("open"),d.removeClass("active-menu-screen-options"),g.slideUp("fast")):(h.attr("aria-expanded","true"),d.addClass("open"),d.addClass("active-menu-screen-options"),g.slideDown("fast")),!1}),e.on("click keydown",function(b){a.utils.isKeydownButNotEnterEvent(b)||(b.preventDefault(),"true"===h.attr("aria-expanded")&&(h.attr("aria-expanded","false"),e.attr("aria-expanded","true"),d.addClass("open"),d.removeClass("active-menu-screen-options"),g.slideUp("fast"),f.slideDown("fast")))})},ready:function(){var c=this;c.container.find(".hide-column-tog").click(function(){c.saveManageColumnsState()}),a.section("menu_locations",function(c){c.headContainer.prepend(b.template("nav-menu-locations-header")(a.Menus.data))})},saveManageColumnsState:_.debounce(function(){var a=this;a._updateHiddenColumnsRequest&&a._updateHiddenColumnsRequest.abort(),a._updateHiddenColumnsRequest=b.ajax.post("hidden-columns",{hidden:a.hidden(),screenoptionnonce:c("#screenoptionnonce").val(),page:"nav-menus"}),a._updateHiddenColumnsRequest.always(function(){a._updateHiddenColumnsRequest=null})},2e3),checked:function(){},unchecked:function(){},hidden:function(){return c(".hide-column-tog").not(":checked").map(function(){var a=this.id;return a.substring(0,a.length-5)}).get().join(",")}}),a.Menus.MenuSection=a.Section.extend({initialize:function(b,d){var e=this;a.Section.prototype.initialize.call(e,b,d),e.deferred.initSortables=c.Deferred()},ready:function(){var b,d,e=this;if("undefined"==typeof e.params.menu_id)throw new Error("params.menu_id was not defined");e.active.validate=function(){return!!a.has(e.id)&&!!a(e.id).get()},e.populateControls(),e.navMenuLocationSettings={},e.assignedLocations=new a.Value([]),a.each(function(a,b){var c=b.match(/^nav_menu_locations\[(.+?)]/);c&&(e.navMenuLocationSettings[c[1]]=a,a.bind(function(){e.refreshAssignedLocations()}))}),e.assignedLocations.bind(function(a){e.updateAssignedLocationsInSectionTitle(a)}),e.refreshAssignedLocations(),a.bind("pane-contents-reflowed",function(){e.contentContainer.parent().length&&(e.container.find(".menu-item .menu-item-reorder-nav button").attr({tabindex:"0","aria-hidden":"false"}),e.container.find(".menu-item.move-up-disabled .menus-move-up").attr({tabindex:"-1","aria-hidden":"true"}),e.container.find(".menu-item.move-down-disabled .menus-move-down").attr({tabindex:"-1","aria-hidden":"true"}),e.container.find(".menu-item.move-left-disabled .menus-move-left").attr({tabindex:"-1","aria-hidden":"true"}),e.container.find(".menu-item.move-right-disabled .menus-move-right").attr({tabindex:"-1","aria-hidden":"true"}))}),d=function(){var a="field-"+c(this).val()+"-active";e.contentContainer.toggleClass(a,c(this).prop("checked"))},b=a.panel("nav_menus").contentContainer.find(".metabox-prefs:first").find(".hide-column-tog"),b.each(d),b.on("click",d)},populateControls:function(){var b,c,d,e,f,g,h,i,j,k=this;b=k.id+"[name]",g=a.control(b),g||(g=new a.controlConstructor.nav_menu_name(b,{type:"nav_menu_name",label:a.Menus.data.l10n.menuNameLabel,section:k.id,priority:0,settings:{"default":k.id}}),a.control.add(g),g.active.set(!0)),f=a.control(k.id),f||(f=new a.controlConstructor.nav_menu(k.id,{type:"nav_menu",section:k.id,priority:998,settings:{"default":k.id},menu_id:k.params.menu_id}),a.control.add(f),f.active.set(!0)),c=k.id+"[locations]",h=a.control(c),h||(h=new a.controlConstructor.nav_menu_locations(c,{section:k.id,priority:999,settings:{"default":k.id},menu_id:k.params.menu_id}),a.control.add(h.id,h),f.active.set(!0)),d=k.id+"[auto_add]",i=a.control(d),i||(i=new a.controlConstructor.nav_menu_auto_add(d,{type:"nav_menu_auto_add",label:"",section:k.id,priority:1e3,settings:{"default":k.id}}),a.control.add(i),i.active.set(!0)),e=k.id+"[delete]",j=a.control(e),j||(j=new a.Control(e,{section:k.id,priority:1001,templateId:"nav-menu-delete-button"}),a.control.add(j.id,j),j.active.set(!0),j.deferred.embedded.done(function(){j.container.find("button").on("click",function(){var b=k.params.menu_id,c=a.Menus.getMenuControl(b);c.setting.set(!1)})}))},refreshAssignedLocations:function(){var a=this,b=a.params.menu_id,c=[];_.each(a.navMenuLocationSettings,function(a,d){a()===b&&c.push(d)}),a.assignedLocations.set(c)},updateAssignedLocationsInSectionTitle:function(b){var d,e=this;d=e.container.find(".accordion-section-title:first"),d.find(".menu-in-location").remove(),_.each(b,function(b){var e,f;e=c('<span class="menu-in-location"></span>'),f=a.Menus.data.locationSlugMappedToName[b],e.text(a.Menus.data.l10n.menuLocation.replace("%s",f)),d.append(e)}),e.container.toggleClass("assigned-to-menu-location",0!==b.length)},onChangeExpanded:function(b,d){var e,f=this;b&&(wpNavMenu.menuList=f.contentContainer,wpNavMenu.targetList=wpNavMenu.menuList,c("#menu-to-edit").removeAttr("id"),wpNavMenu.menuList.attr("id","menu-to-edit").addClass("menu"),_.each(a.section(f.id).controls(),function(a){"nav_menu_item"===a.params.type&&a.actuallyEmbed()}),d.completeCallback&&(e=d.completeCallback),d.completeCallback=function(){"resolved"!==f.deferred.initSortables.state()&&(wpNavMenu.initSortables(),f.deferred.initSortables.resolve(wpNavMenu.menuList),a.control("nav_menu["+String(f.params.menu_id)+"]").reflowMenuItems()),_.isFunction(e)&&e()}),a.Section.prototype.onChangeExpanded.call(f,b,d)},highlightNewItemButton:function(){a.utils.highlightButton(this.contentContainer.find(".add-new-menu-item"),{delay:2e3})}}),a.Menus.createNavMenu=function(b){var d,f,g;return f=a.Menus.generatePlaceholderAutoIncrementId(),d="nav_menu["+String(f)+"]",g=a.create(d,d,{},{type:"nav_menu",transport:a.Menus.data.settingTransport,previewer:a.previewer}),g.set(c.extend({},a.Menus.data.defaultSettingValues.nav_menu,{name:b||""})),a.section.add(new a.Menus.MenuSection(d,{panel:"nav_menus",title:e(b),customizeAction:a.Menus.data.l10n.customizingMenus,priority:10,menu_id:f}))},a.Menus.NewMenuSection=a.Section.extend({attachEvents:function(){function c(){var b=0;return a.each(function(a){j.test(a.id)&&!1!==a.get()&&(b+=1)}),b}function d(){h.find(".add-new-menu-notice").prop("hidden",c()>0)}function e(a){j.test(a.id)&&(a.bind(d),d())}function f(a){j.test(a.id)&&(a.unbind(d),d())}var g=this,h=g.container,i=g.contentContainer,j=/^nav_menu\[/;g.headContainer.find(".accordion-section-title").replaceWith(b.template("nav-menu-create-menu-section-title")),h.on("click",".customize-add-menu-button",function(){g.expand()}),i.on("keydown",".menu-name-field",function(a){13===a.which&&g.submit()}),i.on("click","#customize-new-menu-submit",function(a){g.submit(),a.stopPropagation(),a.preventDefault()}),a.each(e),a.bind("add",e),a.bind("removed",f),d(),a.Section.prototype.attachEvents.apply(g,arguments)},ready:function(){this.populateControls()},populateControls:function(){var b,c,d,e,f,g,h=this;b=h.id+"[name]",e=a.control(b),e||(e=new a.controlConstructor.nav_menu_name(b,{label:a.Menus.data.l10n.menuNameLabel,description:a.Menus.data.l10n.newMenuNameDescription,section:h.id,priority:0}),a.control.add(e.id,e),e.active.set(!0)),c=h.id+"[locations]",f=a.control(c),f||(f=new a.controlConstructor.nav_menu_locations(c,{section:h.id,priority:1,menu_id:"",isCreating:!0}),a.control.add(c,f),f.active.set(!0)),d=h.id+"[submit]",g=a.control(d),g||(g=new a.Control(d,{section:h.id,priority:1,templateId:"nav-menu-submit-new-button"}),a.control.add(d,g),g.active.set(!0))},submit:function(){var d,e=this,f=e.contentContainer,g=f.find(".menu-name-field").first(),h=g.val();return h?(d=a.Menus.createNavMenu(h),g.val(""),g.removeClass("invalid"),f.find(".assigned-menu-location input[type=checkbox]").each(function(){var b,e=c(this);e.prop("checked")&&(b=a("nav_menu_locations["+e.data("location-id")+"]"),b.set(d.params.menu_id),e.prop("checked",!1))}),b.a11y.speak(a.Menus.data.l10n.menuAdded),void d.focus({completeCallback:function(){d.highlightNewItemButton()}})):(g.addClass("invalid"),void g.focus())},selectDefaultLocation:function(b){var c=a.control(this.id+"[locations]"),d={};null!==b&&(d[b]=!0),c.setSelections(d)}}),a.Menus.MenuLocationControl=a.Control.extend({initialize:function(b,c){var d=this,e=b.match(/^nav_menu_locations\[(.+?)]/);d.themeLocation=e[1],a.Control.prototype.initialize.call(d,b,c)},ready:function(){var b=this,c=/^nav_menu\[(-?\d+)]/;b.setting.validate=function(a){return""===a?0:parseInt(a,10)},b.container.find(".create-menu").on("click",function(){var b=a.section("add_menu");b.selectDefaultLocation(this.dataset.locationId),b.focus()}),b.container.find(".edit-menu").on("click",function(){var c=b.setting();a.section("nav_menu["+c+"]").focus()}),b.setting.bind("change",function(){var a=0!==b.setting();b.container.find(".create-menu").toggleClass("hidden",a),b.container.find(".edit-menu").toggleClass("hidden",!a)}),a.bind("add",function(a){var d,f,g=a.id.match(c);g&&!1!==a()&&(f=g[1],d=new Option(e(a().name),f),b.container.find("select").append(d))}),a.bind("remove",function(a){var d,e=a.id.match(c);e&&(d=parseInt(e[1],10),b.setting()===d&&b.setting.set(""),b.container.find("option[value="+d+"]").remove())}),a.bind("change",function(a){var d,f=a.id.match(c);f&&(d=parseInt(f[1],10),!1===a()?(b.setting()===d&&b.setting.set(""),b.container.find("option[value="+d+"]").remove()):b.container.find("option[value="+d+"]").text(e(a().name)))})}}),a.Menus.MenuItemControl=a.Control.extend({initialize:function(b,d){var e=this;e.expanded=new a.Value(!1),e.expandedArgumentsQueue=[],e.expanded.bind(function(a){var b=e.expandedArgumentsQueue.shift();b=c.extend({},e.defaultExpandedArguments,b),e.onChangeExpanded(a,b)}),a.Control.prototype.initialize.call(e,b,d),e.active.validate=function(){var b,c=a.section(e.section());return b=!!c&&c.active()}},embed:function(){var b,c=this,d=c.section();d&&(b=a.section(d),(b&&b.expanded()||a.settings.autofocus.control===c.id)&&c.actuallyEmbed())},actuallyEmbed:function(){var a=this;"resolved"!==a.deferred.embedded.state()&&(a.renderContent(),a.deferred.embedded.resolve())},ready:function(){if("undefined"==typeof this.params.menu_item_id)throw new Error("params.menu_item_id was not defined");this._setupControlToggle(),this._setupReorderUI(),this._setupUpdateUI(),this._setupRemoveUI(),this._setupLinksUI(),this._setupTitleUI()},_setupControlToggle:function(){var b=this;this.container.find(".menu-item-handle").on("click",function(d){d.preventDefault(),d.stopPropagation();var e=b.getMenuControl(),f=c(d.target).is(".item-delete, .item-delete *"),g=c(d.target).is(".add-new-menu-item, .add-new-menu-item *");!c("body").hasClass("adding-menu-items")||f||g||a.Menus.availableMenuItemsPanel.close(),e.isReordering||e.isSorting||b.toggleForm()})},_setupReorderUI:function(){var a,d,e=this;a=b.template("menu-item-reorder-nav"),e.container.find(".item-controls").after(a),d=e.container.find(".menu-item-reorder-nav"),d.find(".menus-move-up, .menus-move-down, .menus-move-left, .menus-move-right").on("click",function(){var a=c(this);a.focus();var b=a.is(".menus-move-up"),d=a.is(".menus-move-down"),f=a.is(".menus-move-left"),g=a.is(".menus-move-right");b?e.moveUp():d?e.moveDown():f?e.moveLeft():g&&e.moveRight(),a.focus()})},_setupUpdateUI:function(){var b,c=this,d=c.setting();c.elements={},c.elements.url=new a.Element(c.container.find(".edit-menu-item-url")),c.elements.title=new a.Element(c.container.find(".edit-menu-item-title")),c.elements.attr_title=new a.Element(c.container.find(".edit-menu-item-attr-title")),c.elements.target=new a.Element(c.container.find(".edit-menu-item-target")),c.elements.classes=new a.Element(c.container.find(".edit-menu-item-classes")),c.elements.xfn=new a.Element(c.container.find(".edit-menu-item-xfn")),c.elements.description=new a.Element(c.container.find(".edit-menu-item-description")),_.each(c.elements,function(a,b){a.bind(function(d){a.element.is("input[type=checkbox]")&&(d=d?a.element.val():"");var e=c.setting();e&&e[b]!==d&&(e=_.clone(e),e[b]=d,c.setting.set(e))}),d&&("classes"!==b&&"xfn"!==b||!_.isArray(d[b])?a.set(d[b]):a.set(d[b].join(" ")))}),c.setting.bind(function(b,d){var e,f=c.params.menu_item_id,g=[],h=[];!1===b?(e=a.control("nav_menu["+String(d.nav_menu_term_id)+"]"),c.container.remove(),_.each(e.getMenuItemControls(),function(a){d.menu_item_parent===a.setting().menu_item_parent&&a.setting().position>d.position?g.push(a):a.setting().menu_item_parent===f&&h.push(a)}),_.each(g,function(a){var b=_.clone(a.setting());b.position+=h.length,a.setting.set(b)}),_.each(h,function(a,b){var c=_.clone(a.setting());c.position=d.position+b,c.menu_item_parent=d.menu_item_parent,a.setting.set(c)}),e.debouncedReflowMenuItems()):(_.each(b,function(a,d){c.elements[d]&&c.elements[d].set(b[d])}),c.container.find(".menu-item-data-parent-id").val(b.menu_item_parent),b.position===d.position&&b.menu_item_parent===d.menu_item_parent||c.getMenuControl().debouncedReflowMenuItems())}),b=function(){c.elements.url.element.toggleClass("invalid",c.setting.notifications.has("invalid_url"))},c.setting.notifications.bind("add",b),c.setting.notifications.bind("removed",b)},_setupRemoveUI:function(){var d,e=this;d=e.container.find(".item-delete"),d.on("click",function(){var d,f,g,h=!0;c("body").hasClass("adding-menu-items")||(h=!1),f=e.container.nextAll(".customize-control-nav_menu_item:visible").first(),g=e.container.prevAll(".customize-control-nav_menu_item:visible").first(),d=f.length?f.find(!1===h?".item-edit":".item-delete").first():g.length?g.find(!1===h?".item-edit":".item-delete").first():e.container.nextAll(".customize-control-nav_menu").find(".add-new-menu-item").first(),e.container.slideUp(function(){e.setting.set(!1),b.a11y.speak(a.Menus.data.l10n.itemDeleted),d.focus()}),e.setting.set(!1)})},_setupLinksUI:function(){var b;b=this.container.find("a.original-link"),b.on("click",function(b){b.preventDefault(),a.previewer.previewUrl(b.target.toString())})},_setupTitleUI:function(){var b,d=this;d.container.find(".edit-menu-item-title").on("blur",function(){c(this).val(c.trim(c(this).val()))}),b=d.container.find(".menu-item-title"),d.setting.bind(function(d){var e,f;d&&(e=c.trim(d.title),f=e||d.original_title||a.Menus.data.l10n.untitled,d._invalid&&(f=a.Menus.data.l10n.invalidTitleTpl.replace("%s",f)),e||d.original_title?b.text(f).removeClass("no-title"):b.text(f).addClass("no-title"))})},getDepth:function(){var b=this,c=b.setting(),d=0;if(!c)return 0;for(;c&&c.menu_item_parent&&(d+=1,b=a.control("nav_menu_item["+c.menu_item_parent+"]"));)c=b.setting();return d},renderContent:function(){var b,c=this,d=c.setting();c.params.title=d.title||"",c.params.depth=c.getDepth(),c.container.data("item-depth",c.params.depth),b=["menu-item","menu-item-depth-"+String(c.params.depth),"menu-item-"+d.object,"menu-item-edit-inactive"],d._invalid?(b.push("menu-item-invalid"),c.params.title=a.Menus.data.l10n.invalidTitleTpl.replace("%s",c.params.title)):"draft"===d.status&&(b.push("pending"),c.params.title=a.Menus.data.pendingTitleTpl.replace("%s",c.params.title)),c.params.el_classes=b.join(" "),c.params.item_type_label=d.type_label,c.params.item_type=d.type,c.params.url=d.url,c.params.target=d.target,c.params.attr_title=d.attr_title,c.params.classes=_.isArray(d.classes)?d.classes.join(" "):d.classes,c.params.attr_title=d.attr_title,c.params.xfn=d.xfn,c.params.description=d.description,c.params.parent=d.menu_item_parent,c.params.original_title=d.original_title||"",c.container.addClass(c.params.el_classes),a.Control.prototype.renderContent.call(c)},getMenuControl:function(){var b=this,c=b.setting();return c&&c.nav_menu_term_id?a.control("nav_menu["+c.nav_menu_term_id+"]"):null},expandControlSection:function(){var a=this.container.closest(".accordion-section");a.hasClass("open")||a.find(".accordion-section-title:first").trigger("click")},_toggleExpanded:a.Section.prototype._toggleExpanded,expand:a.Section.prototype.expand,expandForm:function(a){this.expand(a)},collapse:a.Section.prototype.collapse,collapseForm:function(a){this.collapse(a)},toggleForm:function(a,b){"undefined"==typeof a&&(a=!this.expanded()),a?this.expand(b):this.collapse(b)},onChangeExpanded:function(b,c){var d,e,f,g=this;return d=this.container,e=d.find(".menu-item-settings:first"),"undefined"==typeof b&&(b=!e.is(":visible")),e.is(":visible")===b?void(c&&c.completeCallback&&c.completeCallback()):void(b?(a.control.each(function(a){g.params.type===a.params.type&&g!==a&&a.collapseForm()}),f=function(){d.removeClass("menu-item-edit-inactive").addClass("menu-item-edit-active"),g.container.trigger("expanded"),c&&c.completeCallback&&c.completeCallback()},d.find(".item-edit").attr("aria-expanded","true"),e.slideDown("fast",f),g.container.trigger("expand")):(f=function(){d.addClass("menu-item-edit-inactive").removeClass("menu-item-edit-active"),g.container.trigger("collapsed"),c&&c.completeCallback&&c.completeCallback()},g.container.trigger("collapse"),d.find(".item-edit").attr("aria-expanded","false"),e.slideUp("fast",f)))},focus:function(b){b=b||{};var c,d=this,e=b.completeCallback;c=function(){d.expandControlSection(),b.completeCallback=function(){var a;a=d.container.find(".menu-item-settings").find("input, select, textarea, button, object, a[href], [tabindex]").filter(":visible"),a.first().focus(),e&&e()},d.expandForm(b)},a.section.has(d.section())?a.section(d.section()).expand({completeCallback:c}):c()},moveUp:function(){this._changePosition(-1),b.a11y.speak(a.Menus.data.l10n.movedUp)},moveDown:function(){this._changePosition(1),b.a11y.speak(a.Menus.data.l10n.movedDown)},moveLeft:function(){this._changeDepth(-1),b.a11y.speak(a.Menus.data.l10n.movedLeft)},moveRight:function(){this._changeDepth(1),b.a11y.speak(a.Menus.data.l10n.movedRight)},_changePosition:function(a){var b,d,e=this,f=_.clone(e.setting()),g=[];if(1!==a&&-1!==a)throw new Error("Offset changes by 1 are only supported.");if(e.setting()){if(_(e.getMenuControl().getMenuItemControls()).each(function(a){a.setting().menu_item_parent===f.menu_item_parent&&g.push(a.setting)}),g.sort(function(a,b){return a().position-b().position}),d=_.indexOf(g,e.setting),-1===d)throw new Error("Expected setting to be among siblings.");0===d&&a<0||d===g.length-1&&a>0||(b=g[d+a],b&&b.set(c.extend(_.clone(b()),{position:f.position})),f.position+=a,e.setting.set(f))}},_changeDepth:function(b){if(1!==b&&-1!==b)throw new Error("Offset changes by 1 are only supported.");var d,e,f,g=this,h=_.clone(g.setting()),i=[];if(_(g.getMenuControl().getMenuItemControls()).each(function(a){a.setting().menu_item_parent===h.menu_item_parent&&i.push(a)}),i.sort(function(a,b){return a.setting().position-b.setting().position}),d=_.indexOf(i,g),-1===d)throw new Error("Expected control to be among siblings.");if(-1===b){if(!h.menu_item_parent)return;f=a.control("nav_menu_item["+h.menu_item_parent+"]"),_(i).chain().slice(d).each(function(a,b){a.setting.set(c.extend({},a.setting(),{menu_item_parent:g.params.menu_item_id,position:b}))}),_(g.getMenuControl().getMenuItemControls()).each(function(a){var b,d;d=a.setting().menu_item_parent===f.setting().menu_item_parent&&a.setting().position>f.setting().position,d&&(b=_.clone(a.setting()),a.setting.set(c.extend(b,{position:b.position+1})))}),h.position=f.setting().position+1,h.menu_item_parent=f.setting().menu_item_parent,g.setting.set(h)}else if(1===b){if(0===d)return;e=i[d-1],h.menu_item_parent=e.params.menu_item_id,h.position=0,_(g.getMenuControl().getMenuItemControls()).each(function(a){a.setting().menu_item_parent===h.menu_item_parent&&(h.position=Math.max(h.position,a.setting().position))}),h.position+=1,g.setting.set(h)}}}),a.Menus.MenuNameControl=a.Control.extend({ready:function(){var b=this;if(b.setting){var c=b.setting();b.nameElement=new a.Element(b.container.find(".menu-name-field")),b.nameElement.bind(function(a){var c=b.setting();c&&c.name!==a&&(c=_.clone(c),c.name=a,b.setting.set(c))}),c&&b.nameElement.set(c.name),b.setting.bind(function(a){a&&b.nameElement.set(a.name)})}}}),a.Menus.MenuLocationsControl=a.Control.extend({ready:function(){var b=this;b.container.find(".assigned-menu-location").each(function(){var d=c(this),f=d.find("input[type=checkbox]"),g=new a.Element(f),h=a("nav_menu_locations["+f.data("location-id")+"]"),i=""===b.params.menu_id,j=i?_.noop:function(a){g.set(a)},k=i?_.noop:function(a){h.set(a?b.params.menu_id:0)},l=function(b){var c=a("nav_menu["+String(b)+"]");b&&c&&c()?d.find(".theme-location-set").show().find("span").text(e(c().name)):d.find(".theme-location-set").hide()};j(h.get()===b.params.menu_id),f.on("change",function(){k(this.checked)}),h.bind(function(a){
j(a===b.params.menu_id),l(a)}),l(h.get())})},setSelections:function(a){this.container.find(".menu-location").each(function(b,c){var d=c.dataset.locationId;c.checked=d in a&&a[d]})}}),a.Menus.MenuAutoAddControl=a.Control.extend({ready:function(){var b=this,c=b.setting();b.active.validate=function(){var c,d=a.section(b.section());return c=!!d&&d.active()},b.autoAddElement=new a.Element(b.container.find("input[type=checkbox].auto_add")),b.autoAddElement.bind(function(a){var c=b.setting();c&&c.name!==a&&(c=_.clone(c),c.auto_add=a,b.setting.set(c))}),c&&b.autoAddElement.set(c.auto_add),b.setting.bind(function(a){a&&b.autoAddElement.set(a.auto_add)})}}),a.Menus.MenuControl=a.Control.extend({ready:function(){var b,d,f,g=this,h=a.section(g.section()),i=g.params.menu_id,j=g.setting();if("undefined"==typeof this.params.menu_id)throw new Error("params.menu_id was not defined");g.active.validate=function(){var a;return a=!!h&&h.active()},g.$controlSection=h.headContainer,g.$sectionContent=g.container.closest(".accordion-section-content"),this._setupModel(),a.section(g.section(),function(a){a.deferred.initSortables.done(function(a){g._setupSortable(a)})}),this._setupAddition(),this._setupTitle(),j&&(b=e(j.name),a.control.each(function(c){c.extended(a.controlConstructor.widget_form)&&"nav_menu"===c.params.widget_id_base&&(c.container.find(".nav-menu-widget-form-controls:first").show(),c.container.find(".nav-menu-widget-no-menus-message:first").hide(),f=c.container.find("select"),0===f.find("option[value="+String(i)+"]").length&&f.append(new Option(b,i)))}),d=c("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )"),d.find(".nav-menu-widget-form-controls:first").show(),d.find(".nav-menu-widget-no-menus-message:first").hide(),f=d.find(".widget-inside select:first"),0===f.find("option[value="+String(i)+"]").length&&f.append(new Option(b,i))),_.defer(function(){g.updateInvitationVisibility()})},_setupModel:function(){var b=this,c=b.params.menu_id;b.setting.bind(function(d){var f;!1===d?b._handleDeletion():(f=e(d.name),a.control.each(function(b){if(b.extended(a.controlConstructor.widget_form)&&"nav_menu"===b.params.widget_id_base){var d=b.container.find("select");d.find("option[value="+String(c)+"]").text(f)}}))})},_setupSortable:function(b){var c=this;if(!b.is(c.$sectionContent))throw new Error("Unexpected menuList.");b.on("sortstart",function(){c.isSorting=!0}),b.on("sortstop",function(){setTimeout(function(){var b=c.$sectionContent.sortable("toArray"),d=[],e=0,f=10;c.isSorting=!1,c.$sectionContent.scrollLeft(0),_.each(b,function(b){var c,e,f;f=b.match(/^customize-control-nav_menu_item-(-?\d+)$/,""),f&&(c=parseInt(f[1],10),e=a.control("nav_menu_item["+String(c)+"]"),e&&d.push(e))}),_.each(d,function(a){if(!1!==a.setting()){var b=_.clone(a.setting());e+=1,f+=1,b.position=e,a.priority(f),b.menu_item_parent=parseInt(a.container.find(".menu-item-data-parent-id").val(),10),b.menu_item_parent||(b.menu_item_parent=0),a.setting.set(b)}})})}),c.isReordering=!1,this.container.find(".reorder-toggle").on("click",function(){c.toggleReordering(!c.isReordering)})},_setupAddition:function(){var b=this;this.container.find(".add-new-menu-item").on("click",function(d){b.$sectionContent.hasClass("reordering")||(c("body").hasClass("adding-menu-items")?(c(this).attr("aria-expanded","false"),a.Menus.availableMenuItemsPanel.close(),d.stopPropagation()):(c(this).attr("aria-expanded","true"),a.Menus.availableMenuItemsPanel.open(b)))})},_handleDeletion:function(){var d,e,f,g=this,h=g.params.menu_id,i=0;d=a.section(g.section()),e=function(){d.container.remove(),a.section.remove(d.id)},d&&d.expanded()?d.collapse({completeCallback:function(){e(),b.a11y.speak(a.Menus.data.l10n.menuDeleted),a.panel("nav_menus").focus()}}):e(),a.each(function(a){/^nav_menu\[/.test(a.id)&&!1!==a()&&(i+=1)}),a.control.each(function(b){if(b.extended(a.controlConstructor.widget_form)&&"nav_menu"===b.params.widget_id_base){var c=b.container.find("select");c.val()===String(h)&&c.prop("selectedIndex",0).trigger("change"),b.container.find(".nav-menu-widget-form-controls:first").toggle(0!==i),b.container.find(".nav-menu-widget-no-menus-message:first").toggle(0===i),b.container.find("option[value="+String(h)+"]").remove()}}),f=c("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )"),f.find(".nav-menu-widget-form-controls:first").toggle(0!==i),f.find(".nav-menu-widget-no-menus-message:first").toggle(0===i),f.find("option[value="+String(h)+"]").remove()},_setupTitle:function(){var b=this;b.setting.bind(function(d){if(d){var f=a.section(b.section()),g=b.params.menu_id,h=f.headContainer.find(".accordion-section-title"),i=f.contentContainer.find(".customize-section-title h3"),j=f.headContainer.find(".menu-in-location"),k=i.find(".customize-action"),l=e(d.name);h.text(l),j.length&&j.appendTo(h),i.text(l),k.length&&k.prependTo(i),a.control.each(function(a){/^nav_menu_locations\[/.test(a.id)&&a.container.find("option[value="+g+"]").text(l)}),f.contentContainer.find(".customize-control-checkbox input").each(function(){c(this).prop("checked")&&c(".current-menu-location-name-"+c(this).data("location-id")).text(l)})}})},toggleReordering:function(c){var d=this.container.find(".add-new-menu-item"),e=this.container.find(".reorder-toggle"),f=this.$sectionContent.find(".item-title");c=Boolean(c),c!==this.$sectionContent.hasClass("reordering")&&(this.isReordering=c,this.$sectionContent.toggleClass("reordering",c),this.$sectionContent.sortable(this.isReordering?"disable":"enable"),this.isReordering?(d.attr({tabindex:"-1","aria-hidden":"true"}),e.attr("aria-label",a.Menus.data.l10n.reorderLabelOff),b.a11y.speak(a.Menus.data.l10n.reorderModeOn),f.attr("aria-hidden","false")):(d.removeAttr("tabindex aria-hidden"),e.attr("aria-label",a.Menus.data.l10n.reorderLabelOn),b.a11y.speak(a.Menus.data.l10n.reorderModeOff),f.attr("aria-hidden","true")),c&&_(this.getMenuItemControls()).each(function(a){a.collapseForm()}))},getMenuItemControls:function(){var b=this,c=[],d=b.params.menu_id;return a.control.each(function(a){"nav_menu_item"===a.params.type&&a.setting()&&d===a.setting().nav_menu_term_id&&c.push(a)}),c},reflowMenuItems:function(){var a,b=this,c=b.getMenuItemControls();a=function(b){var c=[],d=b.currentParent;_.each(b.menuItemControls,function(a){d===a.setting().menu_item_parent&&c.push(a)}),c.sort(function(a,b){return a.setting().position-b.setting().position}),_.each(c,function(c){b.currentAbsolutePosition+=1,c.priority.set(b.currentAbsolutePosition),c.container.hasClass("menu-item-depth-"+String(b.currentDepth))||(_.each(c.container.prop("className").match(/menu-item-depth-\d+/g),function(a){c.container.removeClass(a)}),c.container.addClass("menu-item-depth-"+String(b.currentDepth))),c.container.data("item-depth",b.currentDepth),b.currentDepth+=1,b.currentParent=c.params.menu_item_id,a(b),b.currentDepth-=1,b.currentParent=d}),c.length&&(_(c).each(function(a){a.container.removeClass("move-up-disabled move-down-disabled move-left-disabled move-right-disabled"),0===b.currentDepth?a.container.addClass("move-left-disabled"):10===b.currentDepth&&a.container.addClass("move-right-disabled")}),c[0].container.addClass("move-up-disabled").addClass("move-right-disabled").toggleClass("move-down-disabled",1===c.length),c[c.length-1].container.addClass("move-down-disabled").toggleClass("move-up-disabled",1===c.length))},a({menuItemControls:c,currentParent:0,currentDepth:0,currentAbsolutePosition:0}),b.updateInvitationVisibility(c),b.container.find(".reorder-toggle").toggle(c.length>1)},debouncedReflowMenuItems:_.debounce(function(){this.reflowMenuItems.apply(this,arguments)},0),addItemToMenu:function(d){var e,f,g,h,i,j=this,k=0,l=10;return _.each(j.getMenuItemControls(),function(a){!1!==a.setting()&&(l=Math.max(l,a.priority()),0===a.setting().menu_item_parent&&(k=Math.max(k,a.setting().position)))}),k+=1,l+=1,d=c.extend({},a.Menus.data.defaultSettingValues.nav_menu_item,d,{nav_menu_term_id:j.params.menu_id,original_title:d.title,position:k}),delete d.id,i=a.Menus.generatePlaceholderAutoIncrementId(),e="nav_menu_item["+String(i)+"]",f={type:"nav_menu_item",transport:a.Menus.data.settingTransport,previewer:a.previewer},g=a.create(e,e,{},f),g.set(d),h=new a.controlConstructor.nav_menu_item(e,{type:"nav_menu_item",section:j.id,priority:l,settings:{"default":e},menu_item_id:i}),a.control.add(h),g.preview(),j.debouncedReflowMenuItems(),b.a11y.speak(a.Menus.data.l10n.itemAdded),h},updateInvitationVisibility:function(a){var b=a||this.getMenuItemControls();this.container.find(".new-menu-item-invitation").toggle(0===b.length)}}),a.Menus.NewMenuControl=a.Control.extend({initialize:function(){"undefined"!=typeof console&&console.warn&&console.warn("[DEPRECATED] wp.customize.NewMenuControl will be removed. Please use wp.customize.Menus.createNavMenu() instead."),a.Control.prototype.initialize.apply(this,arguments)},ready:function(){this._bindHandlers()},_bindHandlers:function(){var a=this,b=c("#customize-control-new_menu_name input"),d=c("#create-new-menu-submit");b.on("keydown",function(b){13===b.which&&a.submit()}),d.on("click",function(b){a.submit(),b.stopPropagation(),b.preventDefault()})},submit:function(){var c,d=this,e=d.container.closest(".accordion-section-new-menu"),f=e.find(".menu-name-field").first(),g=f.val();return g?(c=a.Menus.createNavMenu(g),f.val(""),f.removeClass("invalid"),b.a11y.speak(a.Menus.data.l10n.menuAdded),void c.focus()):(f.addClass("invalid"),void f.focus())}}),c.extend(a.controlConstructor,{nav_menu_location:a.Menus.MenuLocationControl,nav_menu_item:a.Menus.MenuItemControl,nav_menu:a.Menus.MenuControl,nav_menu_name:a.Menus.MenuNameControl,new_menu:a.Menus.NewMenuControl,nav_menu_locations:a.Menus.MenuLocationsControl,nav_menu_auto_add:a.Menus.MenuAutoAddControl}),c.extend(a.panelConstructor,{nav_menus:a.Menus.MenusPanel}),c.extend(a.sectionConstructor,{nav_menu:a.Menus.MenuSection,new_menu:a.Menus.NewMenuSection}),a.bind("ready",function(){a.Menus.availableMenuItemsPanel=new a.Menus.AvailableMenuItemsPanelView({collection:a.Menus.availableMenuItems}),a.bind("saved",function(b){(b.nav_menu_updates||b.nav_menu_item_updates)&&a.Menus.applySavedData(b)}),a.state("changesetStatus").bind(function(b){"publish"===b&&(a("nav_menus_created_posts")._value=[])}),a.previewer.bind("focus-nav-menu-item-control",a.Menus.focusMenuItemControl)}),a.Menus.applySavedData=function(d){var e={},f={};_(d.nav_menu_updates).each(function(d){var f,g,h,i,j,k,l,m,n,o,p,q,r;if("inserted"===d.status){if(!d.previous_term_id)throw new Error("Expected previous_term_id");if(!d.term_id)throw new Error("Expected term_id");if(f="nav_menu["+String(d.previous_term_id)+"]",!a.has(f))throw new Error("Expected setting to exist: "+f);if(i=a(f),!a.section.has(f))throw new Error("Expected control to exist: "+f);if(m=a.section(f),l=i.get(),!l)throw new Error("Did not expect setting to be empty (deleted).");l=c.extend(_.clone(l),d.saved_value),e[d.previous_term_id]=d.term_id,g="nav_menu["+String(d.term_id)+"]",j=a.create(g,g,l,{type:"nav_menu",transport:a.Menus.data.settingTransport,previewer:a.previewer}),r=m.expanded(),r&&m.collapse(),n=new a.Menus.MenuSection(g,{panel:"nav_menus",title:l.name,customizeAction:a.Menus.data.l10n.customizingMenus,type:"nav_menu",priority:m.priority.get(),menu_id:d.term_id}),a.section.add(n),a.control.each(function(b){if(b.extended(a.controlConstructor.widget_form)&&"nav_menu"===b.params.widget_id_base){var c,e,f;c=b.container.find("select"),e=c.find("option[value="+String(d.previous_term_id)+"]"),f=c.find("option[value="+String(d.term_id)+"]"),f.prop("selected",e.prop("selected")),e.remove()}}),i.callbacks.disable(),i.set(!1),i.preview(),j.preview(),i._dirty=!1,m.container.remove(),a.section.remove(f),q=0,a.each(function(a){/^nav_menu\[/.test(a.id)&&!1!==a()&&(q+=1)}),p=c("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )"),p.find(".nav-menu-widget-form-controls:first").toggle(0!==q),p.find(".nav-menu-widget-no-menus-message:first").toggle(0===q),p.find("option[value="+String(d.previous_term_id)+"]").remove(),b.customize.control.each(function(a){/^nav_menu_locations\[/.test(a.id)&&a.container.find("option[value="+String(d.previous_term_id)+"]").remove()}),a.each(function(b){var c=a.state("saved").get();/^nav_menu_locations\[/.test(b.id)&&b.get()===d.previous_term_id&&(b.set(d.term_id),b._dirty=!1,a.state("saved").set(c),b.preview())}),r&&n.expand()}else if("updated"===d.status){if(h="nav_menu["+String(d.term_id)+"]",!a.has(h))throw new Error("Expected setting to exist: "+h);k=a(h),_.isEqual(d.saved_value,k.get())||(o=a.state("saved").get(),k.set(d.saved_value),k._dirty=!1,a.state("saved").set(o))}}),_(d.nav_menu_item_updates).each(function(a){a.previous_post_id&&(f[a.previous_post_id]=a.post_id)}),_(d.nav_menu_item_updates).each(function(b){var c,d,g,h,i,j,k;if("inserted"===b.status){if(!b.previous_post_id)throw new Error("Expected previous_post_id");if(!b.post_id)throw new Error("Expected post_id");if(c="nav_menu_item["+String(b.previous_post_id)+"]",!a.has(c))throw new Error("Expected setting to exist: "+c);if(g=a(c),!a.control.has(c))throw new Error("Expected control to exist: "+c);if(j=a.control(c),i=g.get(),!i)throw new Error("Did not expect setting to be empty (deleted).");if(i=_.clone(i),i.menu_item_parent<0){if(!f[i.menu_item_parent])throw new Error("inserted ID for menu_item_parent not available");i.menu_item_parent=f[i.menu_item_parent]}e[i.nav_menu_term_id]&&(i.nav_menu_term_id=e[i.nav_menu_term_id]),d="nav_menu_item["+String(b.post_id)+"]",h=a.create(d,d,i,{type:"nav_menu_item",transport:a.Menus.data.settingTransport,previewer:a.previewer}),k=new a.controlConstructor.nav_menu_item(d,{type:"nav_menu_item",menu_id:b.post_id,section:"nav_menu["+String(i.nav_menu_term_id)+"]",priority:j.priority.get(),settings:{"default":d},menu_item_id:b.post_id}),j.container.remove(),a.control.remove(c),a.control.add(k),g.callbacks.disable(),g.set(!1),g.preview(),h.preview(),g._dirty=!1,k.container.toggleClass("menu-item-edit-inactive",j.container.hasClass("menu-item-edit-inactive"))}}),_.each(d.widget_nav_menu_updates,function(b,c){var d=a(c);d&&(d._value=b,d.preview())})},a.Menus.focusMenuItemControl=function(b){var c=a.Menus.getMenuItemControl(b);c&&c.focus()},a.Menus.getMenuControl=function(b){return a.control("nav_menu["+b+"]")},a.Menus.getMenuItemControl=function(b){return a.control(d(b))}}(wp.customize,wp,jQuery);