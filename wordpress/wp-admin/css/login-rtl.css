@import url(forms-rtl.css);
@import url(l10n-rtl.css);

html,
body {
	height: 100%;
	margin: 0;
	padding: 0;
}

body {
	background: #f1f1f1;
	min-width: 0;
	color: #444;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 13px;
	line-height: 1.4em;
}

a {
	color: #0073aa;
	transition-property: border, background, color;
	transition-duration: .05s;
	transition-timing-function: ease-in-out;
}

a {
	outline: 0;
}

a:hover,
a:active {
	color: #00a0d2;
}

a:focus {
	color: #124964;
	box-shadow:
		0 0 0 1px #5b9dd9,
		0 0 2px 1px rgba(30, 140, 190, .8);
}

.ie8 a:focus {
	outline: #5b9dd9 solid 1px;
}

p {
	line-height: 1.5;
}

.login .message,
.login .success,
.login #login_error {
	border-right: 4px solid #00a0d2;
	padding: 12px;
	margin-right: 0;
	margin-bottom: 20px;
	background-color: #fff;
	box-shadow: 0 1px 1px 0 rgba(0,0,0,0.1);
}

.login .success {
	border-right-color: #46b450;
}

.login #login_error {
	border-right-color: #dc3232;
}

#loginform p.submit,
.login-action-lostpassword p.submit {
	border: none;
	margin: -10px 0 20px; /* May want to revisit this */
}

.login * {
	margin: 0;
	padding: 0;
}

.login .password-input-wrapper {
	display: table;
}

.login .input.password-input {
	display: table-cell;
	margin: 0;
}

.login .pw-weak {
	margin-bottom: 15px;
}

.login .button.button-secondary {
	display: table-cell;
	border-radius: 0;
	vertical-align: middle;
}

.login form {
	margin-top: 20px;
	margin-right: 0;
	padding: 26px 24px 46px;
	font-weight: 400;
	overflow: hidden;
	background: #fff;
	box-shadow: 0 1px 3px rgba(0,0,0,0.13);
}

.login form .forgetmenot {
	font-weight: 400;
	float: right;
	margin-bottom: 0;
}

.login .button-primary {
	float: left;
}

#login form p {
	margin-bottom: 0;
}

#login form p.submit {
	margin: 0;
	padding: 0;
}

.login label {
	color: #72777c;
	font-size: 14px;
}

.login form .forgetmenot label {
	font-size: 12px;
	line-height: 19px;
}

.login h1 {
	text-align: center;
}

.login h1 a {
	background-image: url(../images/w-logo-blue.png?ver=20131202);
	background-image: none, url(../images/wordpress-logo.svg?ver=20131107);
	background-size: 84px;
	background-position: center top;
	background-repeat: no-repeat;
	color: #444;
	height: 84px;
	font-size: 20px;
	font-weight: 400;
	line-height: 1.3em;
	margin: 0 auto 25px;
	padding: 0;
	text-decoration: none;
	width: 84px;
	text-indent: -9999px;
	outline: none;
	overflow: hidden;
	display: block;
}

#login {
	width: 320px;
	padding: 8% 0 0;
	margin: auto;
}

.login #nav,
.login #backtoblog {
	font-size: 13px;
	padding: 0 24px 0;
}

.login #nav {
	margin: 24px 0 0 0;
}

#backtoblog {
	margin: 16px 0;
}

.login #nav a,
.login #backtoblog a {
	text-decoration: none;
	color: #555d66;
}

.login #nav a:hover,
.login #backtoblog a:hover,
.login h1 a:hover {
	color: #00a0d2;
}

.login #nav a:focus,
.login #backtoblog a:focus,
.login h1 a:focus {
	color: #124964;
}

.login .privacy-policy-page-link {
	text-align: center;
	width: 100%;
	margin: 5em 0 2em;
}

.login form .input,
.login input[type="text"] {
	font-size: 24px;
	width: 100%;
	padding: 3px;
	margin: 2px 0 16px 6px;
}

.login form .input,
.login input[type="text"],
.login form input[type="checkbox"] {
	background: #fbfbfb;
}

.ie7 .login form .input,
.ie8 .login form .input {
	font-family: sans-serif;
}

.login-action-rp input[type="text"] {
	box-shadow: none;
	margin: 0;
}

.login #pass-strength-result {
	font-weight: 600;
	margin: -1px 0 16px 5px;
	padding: 6px 5px;
	text-align: center;
	width: 100%;
}

body.interim-login {
	height: auto;
}

.interim-login #login {
	padding: 0;
	margin: 5px auto 20px;
}

.interim-login.login h1 a {
	width: auto;
}

.interim-login #login_error,
.interim-login.login .message {
	margin: 0 0 16px;
}

.interim-login.login form {
	margin: 0;
}

@-ms-viewport {
	width: device-width;
}

@media screen and (max-height: 550px) {
	#login {
		padding: 20px 0;
	}
}

@media screen and (max-width: 782px) {
	.interim-login input[type=checkbox] {
		height: 16px;
		width: 16px;
	}

	.interim-login input[type=checkbox]:checked:before {
		width: 16px;
		font: normal 21px/1 dashicons;
		margin: -3px -4px 0 0;
	}
}
