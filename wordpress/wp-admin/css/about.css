/*------------------------------------------------------------------------------
  22.0 - About Pages

   1.0 Global: About, Credits, Freedoms
    1.1 Typography
    1.2 Structure
    1.3 Point Releases
   2.0 About Page
    2.1 Typography
    2.2 Structure
    2.3 Floating Header Layout
   3.0 Credits & Freedoms Pages
------------------------------------------------------------------------------*/

/*------------------------------------------------------------------------------
  1.0 - Global: About, Credits, Freedoms
------------------------------------------------------------------------------*/

.about-wrap {
	position: relative;
	margin: 25px 40px 0 20px;
	max-width: 1050px; /* readability */
	font-size: 15px;
}

.about-wrap.full-width-layout {
	max-width: 1200px;
}

.about-wrap-content {
	max-width: 1050px;
}

.about-wrap div.updated,
.about-wrap div.error,
.about-wrap .notice {
	display: none !important;
}

.about-wrap hr {
	border: 0;
	height: 0;
	margin: 3em 0 0;
	border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.about-wrap img {
	margin: 0;
	width: 100%;
	height: auto;
	vertical-align: middle;
}

.about-wrap figure {
	position: relative;
	margin: 0;
}

.about-wrap .feature-section figure img {
	margin-bottom: 0;
}

.about-wrap figcaption {
	position: absolute;
	bottom: 0;
	width: 100%;
	padding: 40px 10px 15px;
	overflow: auto;
	box-sizing: border-box;
	background: linear-gradient(0deg,rgba(0,0,0,.7),rgba(0,0,0,.3) 60%,transparent);
	font-weight: 600;
	text-shadow: 0px 0px 5px rgba(0,0,0,.75);
}

.about-wrap .jetpack-video-wrapper {
	margin-bottom: 0;
}

/* WordPress Version Badge */

.wp-badge {
	background: #0073aa url(../images/w-logo-white.png?ver=20160308) no-repeat;
	background-position: center 25px;
	background-size: 80px 80px;
	color: #fff;
	font-size: 14px;
	text-align: center;
	font-weight: 600;
	margin: 5px 0 0;
	padding-top: 120px;
	height: 40px;
	display: inline-block;
	width: 140px;
	text-rendering: optimizeLegibility;
	box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.svg .wp-badge {
	background-image: url(../images/wordpress-logo-white.svg?ver=20160308);
}

.about-wrap .wp-badge {
	position: absolute;
	top: 0;
	right: 0;
}

/* Tabs */

.about-wrap .nav-tab {
	padding-right: 15px;
	padding-left: 15px;
	font-size: 18px;
}

/* 1.1 - Typography */

.about-wrap p {
	line-height: 1.5;
	font-size: 14px;
}

.about-wrap .feature-section p {
	max-width: 55em;
	margin-left: auto;
	margin-right: auto;
}

.about-wrap h1 {
	margin: 0.2em 200px 0 0;
	padding: 0;
	color: #32373c;
	line-height: 1.2em;
	font-size: 2.8em;
	font-weight: 400;
}

.about-wrap h2 {
	margin: 40px 0 .6em;
	font-size: 2.7em;
	line-height: 1.3;
	font-weight: 300;
	text-align: center;
}

.about-wrap h3 {
	margin: 1.25em 0 .6em;
	font-size: 1.4em;
	line-height: 1.5;
}

.about-wrap .under-the-hood-header {
	margin: 40px 0 0;
	text-align: center;
}

.about-wrap h4 {
	color: #23282d;
}

.about-wrap code,
.about-wrap ol li p {
	font-size: 14px;
	font-weight: 400;
}

.about-wrap figcaption {
	font-size: 13px;
	text-align: center;
	color: white;
	text-overflow: ellipsis;
}

.about-wrap .about-description,
.about-wrap .about-text {
	margin-top: 1.4em;
	font-weight: 400;
	line-height: 1.6em;
	font-size: 19px;
}

.about-wrap .about-text {
	margin: 1em 200px 1em 0;
	color: #555d66;
}

/* 1.2 - Structure */

.about-wrap [class$="-col"] {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}

.about-wrap .one-col {
	margin: 0 auto;
	max-width: 680px;
}

.about-wrap .one-col.is-wide {
	max-width: 760px;
}

.about-wrap .under-the-hood {
	margin: auto;
	max-width: 1020px;
}

.about-wrap .inline-svg img {
	max-width: 100%;
	width: 100%;
	height: auto;
}

.about-wrap .inline-svg.full-width {
	margin-bottom: 120px;
}

.about-wrap .under-the-hood .inline-svg {
	margin-left: 100px;
}

.about-wrap [class$="-col"] .col {
	flex: 1;
	align-self: flex-start;
}

.about-wrap [class$="-col"] .col + .col {
	margin-left: 20px;
}

.about-wrap [class$="-col"] .is-span-two {
	flex: 2;
}

.about-wrap .one-col img {
	margin: 1em 0 2em;
}

.about-wrap .one-col .alignright img {
	margin-top: 0;
}

.about-wrap .two-col img {
	margin-bottom: 1.5em;
}

.about-wrap .feature-video .mejs-controls {
	display: none !important;
}

.about-wrap .feature-video .mejs-overlay-loading span {
	background: transparent; /* Hide loading.gif */
}

.about-wrap video {
	margin: 1.5em auto;
}

.about-wrap .inline-svg.alignright {
	margin-left: 80px;
}

.about-wrap .cta {
	text-align: center;
}

.about-wrap .cta .button {
	margin: 0 auto 5px;
	font-weight: 600;
}

.about-wrap .feature-section .button {
	margin-top: 1.5em;
	font-weight: 600;
}

/* 1.3 - Point Releases */

.about-wrap .point-releases {
	margin-top: 5px;
	border-bottom: 1px solid #ddd;
}

.about-wrap .changelog.point-releases h3 {
	padding-top: 35px;
}

.about-wrap .changelog.point-releases h3:first-child {
	padding-top: 7px;
}

/*------------------------------------------------------------------------------
  2.0 - About Page
------------------------------------------------------------------------------*/

/* 2.1 - Typography */

.about-wrap .feature-section-header {
	margin: 50px 0 0;
}

.about-wrap .feature-section.two-col h3 {
	margin-top: 0;
}

.about-wrap .feature-section h4 {
	margin: 1.4em 0 0.6em 0;
	font-size: 1em;
}

.about-wrap .feature-section p {
	margin-top: 0.6em;
}

.about-wrap .lead-description {
	font-size: 1.5em;
	text-align: center;
}

.about-wrap .two-col-text {
	column-count: 2;
	column-gap: 40px;
}

.about-wrap .two-col-text p:first-of-type {
	margin-top: 0;
}

.about-wrap .streamlined-updates p,
.about-wrap .native-fonts p {
	margin-bottom: 3em;
}

.about-wrap .under-the-hood img + h3 {
	margin-top: 1.25em;
}

/* 2.2 - Structure */

.about-wrap .headline-feature {
	margin-bottom: 40px;
}

.about-wrap .featured-image {
	text-align: center;
}

.about-wrap .feature-section {
	overflow: hidden;
}

.about-wrap .feature-section.no-heading {
	padding-top: 35px;
}

.about-wrap .feature-section .media-container {
	overflow: hidden;
}

.about-wrap .embed-container {
	text-align: center;
}

.about-wrap .embed-container iframe {
	max-width: 100%;
}

.about-wrap .wp-embedded-content {
	max-width: 100%;
}

.about-wrap .feature-section .col {
	margin-top: 40px;
}

.about-wrap .changelog {
	margin-bottom: 40px;
}

.about-wrap .changelog.feature-section .col {
	margin-top: 40px;
}

/* Return to Dashboard Home link */

.about-wrap .return-to-dashboard {
	margin: 30px 0 0 -5px;
	font-size: 14px;
	font-weight: 600;
}

.about-wrap .return-to-dashboard a {
	text-decoration: none;
	padding: 0 5px;
}

/*------------------------------------------------------------------------------
  3.0 - Credits & Freedoms Pages
------------------------------------------------------------------------------*/

/* Credits */

.about-wrap h3.wp-people-group {
	margin: 2.6em 0 1.33em;
	padding: 0;
	font-size: 16px;
	line-height: inherit;
}

.about-wrap .wp-people-group {
	padding: 0 5px;
	margin: 0 -15px 0 -5px;
}

.about-wrap .compact {
	margin-bottom: 0;
}

.about-wrap .wp-person {
	display: inline-block;
	vertical-align: top;
	margin-right: 10px;
	padding-bottom: 15px;
	height: 70px;
	width: 280px;
}

.about-wrap .compact .wp-person {
	height: auto;
	width: 180px;
	padding-bottom: 0;
	margin-bottom: 0;
}

.about-wrap .wp-person .gravatar {
	float: left;
	margin: 0 10px 10px 0;
	padding: 1px;
	width: 60px;
	height: 60px;
}

.about-wrap .compact .wp-person .gravatar {
	width: 30px;
	height: 30px;
}

.about-wrap .wp-person .web {
	margin: 6px 0 2px;
	font-size: 16px;
	font-weight: 400;
	line-height: 2em;
	text-decoration: none;
}

.about-wrap .wp-person .title {
	display: block;
}

.about-wrap #wp-people-group-validators + p.wp-credits-list {
	margin-top: 0;
}

.about-wrap p.wp-credits-list a {
	white-space: nowrap;
}

/* Freedoms */

.freedoms-php .about-wrap ol {
	margin: 40px 60px;
}

.freedoms-php .about-wrap ol li {
	list-style-type: decimal;
	font-weight: 600;
}

.freedoms-php .about-wrap ol p {
	font-weight: 400;
	margin: 0.6em 0;
}

.freedoms-php .about-wrap .col .freedoms-image {
	background-image: url('https://s.w.org/wp-content/themes/pub/wporg-main/images/freedoms-2x.png');
	background-size: 100%;
	padding-top: 100%;
}
.freedoms-php .about-wrap .col:nth-of-type(2) .freedoms-image {
	background-position: 0 34%;
}
.freedoms-php .about-wrap .col:nth-of-type(3) .freedoms-image {
	background-position: 0 66%;
}
.freedoms-php .about-wrap .col:nth-of-type(4) .freedoms-image {
	background-position: 0 100%;
}

/*------------------------------------------------------------------------------
  4.0 - Media Queries
------------------------------------------------------------------------------*/

@media screen and (max-width: 782px) {
	.about-wrap .two-col-text {
		column-count: 1;
	}

	.about-wrap .one-col .alignright {
		margin-left: 20px;
		max-width: 150px;
	}

	.about-wrap .two-col .col,
	.about-wrap .three-col .col,
	.about-wrap .four-col .col {
		min-width: 48% !important;
		max-width: 48% !important;
		margin-left: 0 !important;
	}

	.about-wrap .eight-col .col {
		min-width: 24% !important;
	}

	.about-wrap .three-col img,
	.about-wrap .four-col img,
	.about-wrap .eight-col img {
		display: block;
		margin: 0 auto;
	}

	.about-wrap figcaption {
		position: relative;
		margin-top: 10px;
		margin-bottom: 15px;
		padding: 0;
		background: none;
		color: #40464D;
		text-shadow: none;
	}

	.about-wrap .under-the-hood .inline-svg {
		margin-left: 40px;
	}
}

@media only screen and (max-width: 500px) {
	.about-wrap {
		margin-right: 20px;
		margin-left: 10px;
	}

	.about-wrap h1,
	.about-wrap .about-text {
		margin-right: 0;
	}

	.about-wrap .about-text {
		margin-bottom: 0.25em;
	}

	.about-wrap .wp-badge {
		position: relative;
		margin-bottom: 1.5em;
		width: 100%;
	}

	.about-wrap .one-col .alignright {
		max-width: 120px;
	}

	.about-wrap .feature-section .col {
		margin-top: 1em;
	}

	.about-wrap .two-col .col,
	.about-wrap .three-col .col,
	.about-wrap .three-col .col {
		min-width: 100% !important;
	}

	.about-wrap .eight-col .col {
		min-width: 48% !important;
	}

	.about-wrap .under-the-hood.four-col .col,
	.about-wrap .under-the-hood.three-col .col,
	.about-wrap .under-the-hood.two-col .col,
	.about-wrap .under-the-hood.one-col .col {
		margin-bottom: 2em;
		padding-bottom: 0;
	}

	.about-wrap .under-the-hood:nth-of-type(2n),
	.about-wrap .under-the-hood:nth-of-type(3n) {
		margin-top: 0;
	}

	.about-wrap .under-the-hood img + h3 {
		margin-top: 1.25em;
	}

	.about-wrap .under-the-hood .inline-svg {
		display: none;
	}

	.about-wrap .inline-svg.full-width {
		margin-bottom: 60px;
	}
}

@media only screen and (max-width: 320px) {
	.about-wrap .one-col .alignright {
		float: none;
		margin: 0 auto;
	}
	.about-wrap .one-col .alignright img {
		margin: 0 0 1em;
	}
}
