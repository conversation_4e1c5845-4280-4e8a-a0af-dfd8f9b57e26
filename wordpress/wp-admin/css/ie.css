/* Fixes for IE 7 bugs */

#dashboard-widgets form .input-text-wrap input,
#dashboard-widgets form .textarea-wrap textarea {
	width: 99%;
}

#dashboard-widgets form #title {
	width: 98%;
}

#wpbody-content #dashboard-widgets .postbox-container {
	width: 49.5%;
}

#wpbody-content #dashboard-widgets #postbox-container-2,
#wpbody-content #dashboard-widgets #postbox-container-3,
#wpbody-content #dashboard-widgets #postbox-container-4 {
	float: right;
	width: 50.5%;
}

#dashboard-widgets #postbox-container-3 .empty-container,
#dashboard-widgets #postbox-container-4 .empty-container {
	border: 0 none;
	height: 0;
	min-height: 0;
}

.wp-editor-wrap .wp-editor-tools,
.wp-editor-wrap .wp-switch-editor,
.wp-editor-wrap .wp-editor-tabs,
.wp-editor-wrap .wp-editor-container {
	zoom: 100%;
}

.wp-editor-wrap .wp-editor-container textarea.wp-editor-area {
	width: 97%;
}

#post-body.columns-2 #postbox-container-1 {
	padding-left: 19px;
}

.welcome-panel .wp-badge {
	position: absolute;
}

.welcome-panel .welcome-panel-column:first-child {
	width: 35%;
}

#adminmenuback {
	left: 0;
	background-image: none;
}

#adminmenuwrap {
	position: static;
}

#adminmenu {
	position: relative;
}

#adminmenu,
#adminmenu a {
	cursor: pointer;
}

#adminmenu li.wp-menu-separator,
#adminmenu li.wp-menu-separator-last {
	font-size: 1px;
	line-height: 1;
}

#adminmenu a.menu-top {
	border-bottom: 0 none;
	border-top: 1px solid #ddd;
}

#adminmenu .separator {
	font-size: 1px;
	line-height: 1px;
}

#adminmenu .wp-submenu {
	left: 110px;
}

#adminmenu .wp-submenu ul {
	margin: 0;
}

.folded #wpcontent,
.folded #wpfooter {
	margin-left: 170px;
}

.folded #adminmenuback,
.folded #adminmenuwrap,
.folded #adminmenu,
.folded #adminmenu li.menu-top {
	width: 150px;
}

.folded #adminmenu .wp-submenu {
	border-top-color: transparent;
}

.folded #adminmenu .wp-menu-name {
	display: block;
}

.folded #adminmenu .wp-submenu.sub-open,
.folded #adminmenu .opensub .wp-submenu {
	left: 110px;
}

.folded #adminmenu a.wp-has-current-submenu:focus + .wp-submenu,
.folded #adminmenu .wp-has-current-submenu .wp-submenu {
	top: -1px;
	position: relative;
}

.folded #adminmenu .wp-has-current-submenu .wp-submenu .wp-submenu-head {
	background-color: transparent;
}

#adminmenu .wp-submenu .wp-submenu-head {
	border-top-color: #ddd;
}

.folded #adminmenu .wp-submenu ul {
	margin-left: 5px;
}

#adminmenu li.menu-top {
	margin-bottom: -2px;
}

#adminmenu .wp-menu-arrow {
	display: none !important;
}

.js.folded #adminmenu li.menu-top {
	display: block;
	zoom: 100%;
}

ul#adminmenu {
	z-index: 99;
}

#adminmenu li.menu-top a.menu-top {
	min-width: auto;
	width: auto;
}

#wpcontent #adminmenu li.wp-has-current-submenu a.wp-has-submenu {
	font-style: normal;
}

#wpcontent #adminmenu .wp-submenu li {
	padding: 0;
}

#adminmenu li.wp-has-current-submenu .wp-submenu {
	left: -40px;
}

#adminmenu .wp-menu-image {
	display: none !important;
}

#adminmenu a.menu-top .wp-menu-name {
	padding-left: 8px;
}

#collapse-menu {
	line-height: 23px;
}

#wpadminbar .ab-comments-icon {
	padding-top: 7px;
}


.theme-browser .theme {
	width: 30%;
	margin: 0 3% 4% 0;
	cursor: auto;
}

.theme-browser .theme:hover,
.theme-browser .theme:focus {
	cursor: auto;
}

.theme-browser .theme .theme-screenshot {
	height: 180px;
}

.theme-browser .theme .theme-actions {
	position: static;
	background-color: #e8e8e8;
}

.theme-browser .theme .more-details {
	display: none;
}

.plugins td,
.plugins th {
	border-top: 1px solid #ddd;
}

table.fixed th,
table.fixed td {
	border-top: 1px solid #ddd;
}

#wpbody-content input.button,
#wpbody-content input.button-primary {
	overflow: visible;
}

#dashboard-widgets h3 a {
	height: 14px;
	line-height: 14px;
}

#dashboard_browser_nag {
	color: #fff;
}

#dashboard_browser_nag .browser-icon {
	position: relative;
}

.tablenav-pages .current-page {
	vertical-align: middle;
}

#wpbody-content .postbox {
	border: 1px solid #ddd;
}

#wpbody-content .postbox .hndle {
	margin-bottom: -1px;
}

.major-publishing-actions,
.wp-submenu,
.wp-submenu li,
#template,
#template div,
#editcat,
#addcat {
	zoom: 100%;
}

.wp-menu-arrow {
	height: 28px;
}

.submitbox {
	margin-top: 10px;
}

/* Inline Editor */
#wpbody-content .quick-edit-row-post .inline-edit-col-left {
	width: 39%;
}

#wpbody-content .inline-edit-row-post .inline-edit-col-center {
	width: 19%;
}

#wpbody-content .quick-edit-row-page .inline-edit-col-left {
	width: 49%;
}

#wpbody-content .bulk-edit-row .inline-edit-col-left {
	width: 29%;
}

.inline-edit-row .submit {
	zoom: 100%;
}

.inline-edit-row fieldset label span.title {
	display: block;
	float: left;
	width: 5em;
}

.inline-edit-row fieldset label span.input-text-wrap {
	margin-left: 0;
	zoom: 100%;
}

#wpbody-content .inline-edit-row fieldset label span.input-text-wrap input {
	line-height: 130%;
}

#wpbody-content .inline-edit-row .input-text-wrap input {
	width: 95%;
}

#wpbody-content .inline-edit-row .input-text-wrap input.inline-edit-password-input {
	width: 8em;
}
/* end Inline Editor */

#titlediv #title {
	width: 98%;
}

.button,
input[type="reset"],
input[type="button"],
input[type="submit"] {
	padding: 0 8px;
	line-height: 20px;
	height: auto;
}

.button.button-large,
input[type="reset"].button-large,
input[type="button"].button-large,
input[type="submit"].button-large {
	padding: 0 10px;
	line-height: 24px;
	height: auto;
}

.button.button-small,
input[type="reset"].button-small,
input[type="button"].button-small,
input[type="submit"].button-small {
	padding: 0 6px;
	line-height: 16px;
	height: auto;
}

a.button {
	margin: 1px;
	padding: 1px 9px 2px;
}

a.button.button-large {
	padding: 1px 11px 2px;
}

a.button.button-small {
	padding: 1px 7px 2px;
}

#screen-options-wrap {
	overflow: hidden;
}

#the-comment-list .comment-item,
#post-status-info,
#wpwrap,
#wrap,
#postdivrich,
#postdiv,
#poststuff,
.metabox-holder,
#titlediv,
#post-body,
#editorcontainer,
.tablenav,
.widget-liquid-left,
.widget-liquid-right,
#widgets-left,
.widgets-sortables,
#dragHelper,
.widget .widget-top,
.widget-control-actions,
.tagchecklist,
#col-container,
#col-left,
#col-right,
.fileedit-sub {
	display: block;
	zoom: 100%;
}

p.search-box {
	position: static;
	float: right;
	margin: -3px 0 4px;
}

#widget-list .widget {
	display: inline;
}

#editorcontainer #content {
	overflow: auto;
	margin: auto;
	width: 98%;
}

form#template div {
	width: 100%;
}

.wp-editor-container .quicktags-toolbar input {
	overflow: visible;
	padding: 0 4px;
}

#poststuff h2 {
	font-size: 1.6em;
}

#poststuff .inside #parent_id,
#poststuff .inside #page_template,
.inline-edit-row #post_parent,
.inline-edit-row select[name="page_template"] {
	width: 250px;
}

#submitdiv input,
#submitdiv select,
#submitdiv a.button {
	position: relative;
}

#bh {
	margin: 7px 10px 0 0;
	float: right;
}

/* without this dashboard widgets appear in one column for some screen widths */
div#dashboard-widgets {
	padding-right: 1px;
}

.tagchecklist > li, .tagchecklist .ntdelbutton {
	display: inline-block;
	display: block;
}

.tagchecklist .ntdelbutton:focus .remove-tag-icon:before {
	outline: 1px solid #5b9dd9;
}

.tablenav .button,
.nav .button {
	padding-top: 2px;
	padding-bottom: 2px;
}

.tablenav select {
	font-size: 13px;
	display: inline-block;
	vertical-align: top;
	margin-top: 2px;
}

.tablenav .actions select {
	width: 155px;
}

.subsubsub li {
	display: inline;
}

a.post-state-format {
	text-indent: 0;
	line-height: 0;
	font-size: 0;
}

table.ie-fixed {
	table-layout: fixed;
}

.widefat tr,
.widefat th,
.widefat thead td,
.widefat tfoot td {
	margin-bottom: 0;
	border-spacing: 0;
}

.widefat th input,
.widefat thead td input,
.widefat tfoot td input {
	margin: 0 0 0 5px;
}

.widefat thead .check-column,
.widefat tfoot .check-column {
	padding-top: 6px;
}

.widefat tbody th.check-column,
.media.widefat tbody th.check-column {
	padding: 4px 0 0;
}

.widefat {
	empty-cells: show;
	border-collapse: collapse;
}

.tablenav a.button {
	display: inline-block;
	padding: 2px 5px;
}

.inactive-sidebar .widgets-sortables {
	padding-bottom: 8px;
}

#available-widgets .widget-holder {
	padding-bottom: 65px;
}

#widgets-left .inactive {
	padding-bottom: 10px;
}

.widget-liquid-right .widget,
.inactive-sidebar .widget {
	position: relative;
}

.inactive-sidebar .widget {
	display: block;
	float: left;
}

#wpcontent .button-primary-disabled {
	color: #9FD0D5;
	background: #298CBA;
}

#the-comment-list .unapproved tr,
#the-comment-list .unapproved td {
	background-color: #ffffe0;
}

.imgedit-submit {
	width: 300px;
}

#nav-menus-frame,
#wpbody,
.menu li {
	zoom: 100%;
}

#update-nav-menu #post-body {
	overflow: hidden;
}

.menu li {
	min-width: 100%;
}

.menu li.sortable-placeholder {
	min-width: 400px;
}

.available-theme {
	display: inline;
}

.available-theme ul {
	margin: 0;
}

.available-theme .action-links li {
	padding-right: 7px;
	margin-right: 7px;
}

.about-wrap .three-col.about-updates .col-2 {
	width: 15%;
}

.about-wrap .about-password-meter input {
	width: 98%;
}

.revisions-tickmarks,
.revisions-tooltip {
	display: none !important;
}

.revisions.pinned .revisions-controls {
	position: relative;
}

input[type="password"],
.login form .input {
	font-family: sans-serif;
}

/* TinyMCE icons */
.mce-btn i.mce-i-bold,
.mce-btn i.mce-i-italic,
.mce-btn i.mce-i-bullist,
.mce-btn i.mce-i-numlist,
.mce-btn i.mce-i-blockquote,
.mce-btn i.mce-i-alignleft,
.mce-btn i.mce-i-aligncenter,
.mce-btn i.mce-i-alignright,
.mce-btn i.mce-i-link,
.mce-btn i.mce-i-unlink,
.mce-btn i.mce-i-wp_more,
.mce-btn i.mce-i-strikethrough,
.mce-btn i.mce-i-spellchecker,
.mce-btn i.mce-i-fullscreen,
.mce-btn i.mce-i-wp_fullscreen,
.mce-btn i.mce-i-wp_adv,
.mce-btn i.mce-i-underline,
.mce-btn i.mce-i-alignjustify,
.mce-btn i.mce-i-forecolor,
.mce-btn i.mce-i-pastetext,
.mce-btn i.mce-i-pasteword,
.mce-btn i.mce-i-removeformat,
.mce-btn i.mce-i-charmap,
.mce-btn i.mce-i-outdent,
.mce-btn i.mce-i-indent,
.mce-btn i.mce-i-undo,
.mce-btn i.mce-i-redo,
.mce-btn i.mce-i-help,
.mce-btn i.mce-i-wp_help,
.mce-btn i.mce-i-wp-media-library,
.mce-btn i.mce-i-ltr,
.mce-btn i.mce-i-wp_page,
.mce-btn i.mce-i-hr,
.mce-close {
	font-family: tinymce, Arial;
	font-style: normal;
	font-weight: 400;
	font-variant: normal;
	font-size: 16px;
	margin-left: 0;
	padding-right: 0;
}

.mce-btn i.mce-i-wp_fullscreen,
.qt-fullscreen {
	-ie7-icon: "\e023";
}

.mce-btn i.mce-i-wp_more,
.mce-btn i.mce-i-wp_page {
	-ie7-icon: "\e027";
}

.mce-btn i.mce-i-wp_adv {
	background-color: #a0a5aa;
}

.mce-btn i.mce-i-help,
.mce-btn i.mce-i-wp_help {
	-ie7-icon: "\e016";
}


/* IE6 leftovers */
* html .row-actions {
	visibility: visible;
}

* html div.widget-liquid-left,
* html div.widget-liquid-right {
	display: block;
	position: relative;
}

* html #editorcontainer {
	padding: 0;
}

* html #poststuff h2 {
	margin-left: 0;
}

* html .stuffbox,
* html .stuffbox input,
* html .stuffbox textarea {
	border: 1px solid #ddd;
}

* html div.widget-liquid-left {
	width: 99%;
}

* html .widgets-sortables {
	height: 50px;
}

* html a#content_resize {
	right: -2px;
}

* html .widget-title h4 {
	width: 205px;
}

* html #removing-widget .in-widget-title {
	display: none;
}

* html .media-item .pinkynail {
	height: 32px;
	width: 40px;
}

* html .describe .field input.text,
* html .describe .field textarea {
	width: 440px;
}

* html input {
	border: 1px solid #ddd;
}

* html .edit-box {
	display: inline;
}

* html .postbox-container .meta-box-sortables {
	height: 300px;
}

* html #wpbody-content #screen-options-link-wrap {
	display: inline-block;
	width: 150px;
	text-align: center;
}

* html #wpbody-content #contextual-help-link-wrap {
	display: inline-block;
	width: 100px;
	text-align: center;
}

* html #adminmenu {
	margin-left: -80px;
}

* html .folded #adminmenu {
	margin-left: -22px;
}

* html #wpcontent #adminmenu li.menu-top {
	display: inline;
	padding: 0;
	margin: 0;
}

* html #wpfooter {
	margin: 0;
}

* html #adminmenu div.wp-menu-image {
	height: 29px;
}
