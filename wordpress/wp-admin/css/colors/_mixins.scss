/*
 * Button mixin- creates 3d-ish button effect with correct
 * highlights/shadows, based on a base color.
 */
@mixin button( $button-color, $text-color: #fff ) {
	background: $button-color;
	border-color: darken( $button-color, 10% ) darken( $button-color, 15% ) darken( $button-color, 15% );
	color: $text-color;
	box-shadow: 0 1px 0 darken( $button-color, 15% );
	text-shadow: 0 -1px 1px darken( $button-color, 15% ),
		1px 0 1px darken( $button-color, 15% ),
		0 1px 1px darken( $button-color, 15% ),
		-1px 0 1px darken( $button-color, 15% );

	&:hover,
	&:focus {
		background: lighten( $button-color, 3% );
		border-color: darken( $button-color, 15% );
		color: $text-color;
		box-shadow: 0 1px 0 darken( $button-color, 15% );
	}

	&:focus {
		box-shadow: inset 0 1px 0 darken( $button-color, 10% ),
					0 0 2px 1px #33b3db;
	}

	&:active,
	&.active,
	&.active:focus,
	&.active:hover {
		background: darken( $button-color, 10% );
		border-color: darken( $button-color, 15% );
	 	box-shadow: inset 0 2px 0 darken( $button-color, 15% );
	}

	&[disabled],
	&:disabled,
	&.button-primary-disabled,
	&.disabled {
		color: hsl( hue( $button-color ), 10%, 80% ) !important;
		background: darken( $button-color, 8% ) !important;
		border-color: darken( $button-color, 15% ) !important;
		text-shadow: none !important;
	}

	&.button-hero {
		box-shadow: 0 2px 0 darken( $button-color, 15% ) !important;
		&:active {
		 	box-shadow: inset 0 3px 0 darken( $button-color, 15% ) !important;
		}
	}

}
