/* Include margin and padding in the width calculation of input and textarea. */
input,
textarea {
	box-sizing: border-box;
}

input[type="text"],
input[type="password"],
input[type="checkbox"],
input[type="color"],
input[type="date"],
input[type="datetime"],
input[type="datetime-local"],
input[type="email"],
input[type="month"],
input[type="number"],
input[type="search"],
input[type="radio"],
input[type="tel"],
input[type="text"],
input[type="time"],
input[type="url"],
input[type="week"],
select,
textarea {
	border: 1px solid #ddd;
	box-shadow: inset 0 1px 2px rgba( 0, 0, 0, 0.07 );
	background-color: #fff;
	color: #32373c;
	outline: none;
	transition: 0.05s border-color ease-in-out;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="color"]:focus,
input[type="date"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="email"]:focus,
input[type="month"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="text"]:focus,
input[type="time"]:focus,
input[type="url"]:focus,
input[type="week"]:focus,
input[type="checkbox"]:focus,
input[type="radio"]:focus,
select:focus,
textarea:focus {
	border-color: #5b9dd9;
	box-shadow: 0 0 2px rgba( 30, 140, 190, 0.8 );
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

/* rtl:ignore */
input[type="email"],
input[type="url"] {
	direction: ltr;
}

/* Vertically align the number selector with the input. */
input[type="number"] {
	height: 28px;
	line-height: 1;
}

input[type="checkbox"],
input[type="radio"] {
	border: 1px solid #b4b9be;
	background: #fff;
	color: #555;
	clear: none;
	cursor: pointer;
	display: inline-block;
	line-height: 0;
	height: 16px;
	margin: -4px 4px 0 0;
	outline: 0;
	padding: 0 !important;
	text-align: center;
	vertical-align: middle;
	width: 16px;
	min-width: 16px;
	-webkit-appearance: none;
	box-shadow: inset 0 1px 2px rgba( 0, 0, 0, 0.1 );
	transition: .05s border-color ease-in-out;
}

input[type="radio"]:checked + label:before {
	color: #82878c;
}

.wp-core-ui input[type="reset"]:hover,
.wp-core-ui input[type="reset"]:active {
	color: #00a0d2;
}

td > input[type="checkbox"],
.wp-admin p input[type="checkbox"],
.wp-admin p input[type="radio"] {
	margin-top: 0;
}

.wp-admin p label input[type="checkbox"] {
	margin-top: -4px;
}

.wp-admin p label input[type="radio"] {
	margin-top: -2px;
}

input[type="radio"] {
	border-radius: 50%;
	margin-right: 4px;
	line-height: 10px;
}

input[type="checkbox"]:checked:before,
input[type="radio"]:checked:before {
	float: left;
	display: inline-block;
	vertical-align: middle;
	width: 16px;
	font: normal 21px/1 dashicons;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

input[type="checkbox"]:checked:before {
	content: "\f147";
	margin: -3px 0 0 -4px;
	color: #1e8cbe;
}

input[type="radio"]:checked:before {
	content: "\2022";
	text-indent: -9999px;
	border-radius: 50px;
	font-size: 24px;
	width: 6px;
	height: 6px;
	margin: 4px;
	line-height: 16px;
	background-color: #1e8cbe;
}

@-moz-document url-prefix() {
	input[type="checkbox"],
	input[type="radio"],
	.form-table input.tog {
		margin-bottom: -1px;
	}
}

/* Search */
input[type="search"] {
	-webkit-appearance: textfield;
}

input[type="search"]::-webkit-search-decoration {
	display: none;
}

.ie8 input[type="password"] {
	font-family: sans-serif;
}

textarea,
input,
select,
button {
	font-family: inherit;
	font-size: inherit;
	font-weight: inherit;
}

textarea,
input,
select {
	font-size: 14px;
	padding: 3px 5px;
	border-radius: 0; /* Reset mobile webkit's default element styling */
}

textarea {
	overflow: auto;
	padding: 2px 6px;
	line-height: 1.4;
	resize: vertical;
}

.wp-admin input[type="file"] {
	padding: 3px 0;
	cursor: pointer;
}

label {
	cursor: pointer;
}

input,
select {
	margin: 1px;
	padding: 3px 5px;
}

input.code {
	padding-top: 6px;
}

textarea.code {
	line-height: 1.4;
	padding: 4px 6px 1px 6px;
}

input.readonly,
input[readonly],
textarea.readonly,
textarea[readonly] {
	background-color: #eee;
}

::-webkit-input-placeholder {
	color: #72777c;
}

::-moz-placeholder {
	color: #72777c;
	opacity: 1;
}

:-ms-input-placeholder {
	color: #72777c;
}

.form-invalid input,
.form-invalid input:focus,
.form-invalid select,
.form-invalid select:focus {
	border-color: #dc3232 !important;
	box-shadow: 0 0 2px rgba( 204, 0, 0, 0.8 );
}

.form-table .form-required.form-invalid td:after {
	content: "\f534";
	font: normal 20px/1 dashicons;
	color: #dc3232;
	margin-left: -25px;
	vertical-align: middle;
}

/* Adjust error indicator for password layout */
.form-table .form-required.user-pass1-wrap.form-invalid td:after {
	content: "";
}

.form-table .form-required.user-pass1-wrap.form-invalid .password-input-wrapper:after {
	content: "\f534";
	font: normal 20px/1 dashicons;
	color: #dc3232;
	margin: 0 6px 0 -29px;
	vertical-align: middle;
}

.form-input-tip {
	color: #666;
}

input:disabled,
input.disabled,
select:disabled,
select.disabled,
textarea:disabled,
textarea.disabled {
	background: rgba( 255, 255, 255, 0.5 );
	border-color: rgba( 222, 222, 222, 0.75 );
	box-shadow: inset 0 1px 2px rgba( 0, 0, 0, 0.04 );
	color: rgba( 51, 51, 51, 0.5 );
}

input[type="file"]:disabled,
input[type="file"].disabled,
input[type="range"]:disabled,
input[type="range"].disabled {
	background: none;
	box-shadow: none;
	cursor: default;
}

input[type="checkbox"]:disabled,
input[type="checkbox"].disabled,
input[type="radio"]:disabled,
input[type="radio"].disabled,
input[type="checkbox"]:disabled:checked:before,
input[type="checkbox"].disabled:checked:before,
input[type="radio"]:disabled:checked:before,
input[type="radio"].disabled:checked:before {
	opacity: 0.7;
}

/*------------------------------------------------------------------------------
  2.0 - Forms
------------------------------------------------------------------------------*/


.wp-admin select {
	padding: 2px;
	line-height: 28px;
	height: 28px;
	vertical-align: middle;
}

.wp-admin .button-cancel {
	padding: 0 5px;
	line-height: 2;
}

.meta-box-sortables select {
	max-width: 100%;
}

.wp-admin select[multiple] {
	height: auto;
}

.submit {
	padding: 1.5em 0;
	margin: 5px 0;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	border: none;
}

form p.submit a.cancel:hover {
	text-decoration: none;
}

p.submit {
	text-align: left;
	max-width: 100%;
	margin-top: 20px;
	padding-top: 10px;
}

.textright p.submit {
	border: none;
	text-align: right;
}

table.form-table + p.submit,
table.form-table + input + p.submit,
table.form-table + input + input + p.submit {
	border-top: none;
	padding-top: 0;
}

#minor-publishing-actions input,
#major-publishing-actions input,
#minor-publishing-actions .preview {
	text-align: center;
}

textarea.all-options,
input.all-options {
	width: 250px;
}

input.large-text,
textarea.large-text {
	width: 99%;
}

.regular-text {
	width: 25em;
}

input.small-text {
	width: 50px;
	padding: 1px 6px;
}

input[type="number"].small-text {
	width: 65px;
}

input.tiny-text {
	width: 35px;
}

input[type="number"].tiny-text {
	width: 45px;
}

#doaction,
#doaction2,
#post-query-submit {
	margin: 1px 8px 0 0;
}

.tablenav #changeit,
.tablenav #delete_all,
.tablenav #clear-recent-list,
.wp-filter #delete_all {
	margin-top: 1px;
}

.tablenav .actions select {
	float: left;
	margin-right: 6px;
	max-width: 200px;
}

.ie8 .tablenav .actions select {
	width: 155px;
}

.ie8 .tablenav .actions select#cat {
	width: 200px;
}

#timezone_string option {
	margin-left: 1em;
}

button.wp-hide-pw > .dashicons {
	position: relative;
	top: 3px;
}

label,
#your-profile label + a {
	vertical-align: middle;
}

fieldset label,
#your-profile label + a {
	vertical-align: middle;
}

.options-media-php [for*="_size_"] {
	min-width: 10em;
	vertical-align: baseline;
}

.options-media-php .small-text[name*="_size_"] {
	margin: 0 0 1em;
}

#misc-publishing-actions label {
	vertical-align: baseline;
}

#pass-strength-result {
	background-color: #eee;
	border: 1px solid #ddd;
	color: #23282d;
	margin: -2px 5px 5px 1px;
	padding: 3px 5px;
	text-align: center;
	width: 25em;
	box-sizing: border-box;
	opacity: 0;
}

#pass-strength-result.short {
	background-color: #f1adad;
	border-color: #e35b5b;
	opacity: 1;
}

#pass-strength-result.bad {
	background-color: #fbc5a9;
	border-color: #f78b53;
	opacity: 1;
}

#pass-strength-result.good {
	background-color: #ffe399;
	border-color: #ffc733;
	opacity: 1;
}

#pass-strength-result.strong {
	background-color: #c1e1b9;
	border-color: #83c373;
	opacity: 1;
}

#pass1.short, #pass1-text.short {
	border-color: #e35b5b;
}

#pass1.bad, #pass1-text.bad {
	border-color: #f78b53;
}

#pass1.good, #pass1-text.good {
	border-color: #ffc733;
}

#pass1.strong, #pass1-text.strong {
	border-color: #83c373;
}

.pw-weak {
	display: none;
}

.indicator-hint {
	padding-top: 8px;
}

#pass1-text,
.show-password #pass1 {
	display: none;
}

.show-password #pass1-text
{
	display: inline-block;
}

.form-table span.description.important {
	font-size: 12px;
}

p.search-box {
	float: right;
	margin: 0;
}

.network-admin.themes-php p.search-box {
	clear: left;
}

.search-box input[name="s"],
.tablenav .search-plugins input[name="s"],
.tagsdiv .newtag {
	float: left;
	height: 28px;
	margin: 0 4px 0 0;
}

.js.plugins-php .search-box .wp-filter-search {
	margin: 0;
	width: 280px;
	font-size: 16px;
	font-weight: 300;
	line-height: 1.5;
	padding: 3px 5px;
	height: 32px;
}

input[type="text"].ui-autocomplete-loading,
input[type="email"].ui-autocomplete-loading {
	background-image: url(../images/loading.gif);
	background-repeat: no-repeat;
	background-position: right center;
	visibility: visible;
}

input.ui-autocomplete-input.open {
	border-bottom-color: transparent;
}

ul#add-to-blog-users {
	margin: 0 0 0 14px;
}

.ui-autocomplete {
	padding: 0;
	margin: 0;
	list-style: none;
	position: absolute;
	z-index: 10000;
	border: 1px solid #5b9dd9;
	box-shadow: 0 1px 2px rgba( 30, 140, 190, 0.8 );
	background-color: #fff;
}

.ui-autocomplete li {
	margin-bottom: 0;
	padding: 4px 10px;
	white-space: nowrap;
	text-align: left;
	cursor: pointer;
}

/* Colors for the wplink toolbar autocomplete. */
.ui-autocomplete .ui-state-focus {
	background-color: #ddd;
}

/* Colors for the tags autocomplete. */
.wp-tags-autocomplete .ui-state-focus {
	background-color: #0073aa;
	color: #fff;
}

/*------------------------------------------------------------------------------
  15.0 - Comments Screen
------------------------------------------------------------------------------*/

.form-table {
	border-collapse: collapse;
	margin-top: 0.5em;
	width: 100%;
	clear: both;
}

.form-table,
.form-table td,
.form-table th,
.form-table td p {
	font-size: 14px;
}

.form-table td {
	margin-bottom: 9px;
	padding: 15px 10px;
	line-height: 1.3;
	vertical-align: middle;
}

.form-table th,
.form-wrap label {
	color: #23282d;
	font-weight: 400;
	text-shadow: none;
	vertical-align: baseline;
}

.form-table th {
	vertical-align: top;
	text-align: left;
	padding: 20px 10px 20px 0;
	width: 200px;
	line-height: 1.3;
	font-weight: 600;
}

.form-table th.th-full, /* Not used by core. Back-compat for pre-4.8 */
.form-table .td-full {
	width: auto;
	padding: 20px 10px 20px 0;
	font-weight: 400;
}

.form-table td p {
	margin-top: 4px;
	margin-bottom: 0;
}

.form-table .date-time-doc {
	margin-top: 1em;
}

.form-table p.timezone-info {
	margin: 1em 0;
}

.form-table td fieldset label {
	margin: 0.25em 0 0.5em !important;
	display: inline-block;
}

.form-table td fieldset label,
.form-table td fieldset p,
.form-table td fieldset li {
	line-height: 1.4em;
}

.form-table input.tog,
.form-table input[type="radio"] {
	margin-top: -4px;
	margin-right: 4px;
	float: none;
}

.form-table .pre {
	padding: 8px;
	margin: 0;
}

table.form-table td .updated {
	font-size: 13px;
}

table.form-table td .updated p {
	font-size: 13px;
	margin: 0.3em 0;
}

/*------------------------------------------------------------------------------
  18.0 - Users
------------------------------------------------------------------------------*/

#profile-page .form-table textarea {
	width: 500px;
	margin-bottom: 6px;
}

#profile-page .form-table #rich_editing {
	margin-right: 5px
}

#your-profile legend {
	font-size: 22px;
}

#display_name {
	width: 15em;
}

#adduser .form-field input,
#createuser .form-field input {
	width: 25em;
}

.color-option {
	display: inline-block;
	width: 24%;
	padding: 5px 15px 15px;
	box-sizing: border-box;
	margin-bottom: 3px;
}

.color-option:hover,
.color-option.selected {
	background: #ddd;
}

.color-palette {
	width: 100%;
	border-spacing: 0;
	border-collapse: collapse;
}
.color-palette td {
	height: 20px;
	padding: 0;
	border: none;
}

.color-option {
	cursor: pointer;
}

/*------------------------------------------------------------------------------
  19.0 - Tools
------------------------------------------------------------------------------*/

.tool-box .title {
	margin: 8px 0;
	font-size: 18px;
	font-weight: 400;
	line-height: 24px;
}

.label-responsive {
	vertical-align: middle;
}

#export-filters p {
	margin: 0 0 1em;
}

#export-filters p.submit {
	margin: 7px 0 5px;
}

/* Card styles */

.card {
	position: relative;
	margin-top: 20px;
	padding: 0.7em 2em 1em;
	min-width: 255px;
	max-width: 520px;
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0,0,0,0.04);
	background: #fff;
}

/* Press this styles */

.pressthis h4 {
	margin: 2em 0 1em;
}

.pressthis textarea {
	width: 100%;
	font-size: 1em;
}

#pressthis-code-wrap {
	overflow: auto;
}

.pressthis-bookmarklet-wrapper {
	margin: 20px 0 8px;
	vertical-align: top;
	position: relative;
	z-index: 1;
}

.pressthis-bookmarklet,
.pressthis-bookmarklet:hover,
.pressthis-bookmarklet:focus,
.pressthis-bookmarklet:active {
	display: inline-block;
	position: relative;
	cursor: move;
	color: #32373c;
	background: #e5e5e5;
	border-radius: 5px;
	border: 1px solid #b4b9be;
	font-style: normal;
	line-height: 16px;
	font-size: 14px;
	text-decoration: none;
}

.pressthis-bookmarklet:active {
	outline: none;
}

.pressthis-bookmarklet:after {
	content: "";
	width: 70%;
	height: 55%;
	z-index: -1;
	position: absolute;
	right: 10px;
	bottom: 9px;
	background: transparent;
	transform: skew(20deg) rotate(6deg);
	box-shadow: 0 10px 8px rgba(0, 0, 0, 0.6);
}

.pressthis-bookmarklet:hover:after {
	transform: skew(20deg) rotate(9deg);
	box-shadow: 0 10px 8px rgba(0, 0, 0, 0.7);
}

.pressthis-bookmarklet span {
	display: inline-block;
	margin: 0px 0 0;
	padding: 0px 12px 8px 9px;
}

.pressthis-bookmarklet span:before {
	color: #72777c;
	font: normal 20px/1 dashicons;
	content: "\f157";
	position: relative;
	display: inline-block;
	top: 4px;
	margin-right: 4px;
}

.pressthis-js-toggle {
	margin-left: 10px;
	padding: 0;
	height: auto;
	vertical-align: top;
}

/* to override the button class being applied */
.pressthis-js-toggle.button.button {
	margin-left: 10px;
	padding: 0;
	height: auto;
	vertical-align: top;
}

.pressthis-js-toggle .dashicons {
	margin: 5px 8px 6px 7px;
	color: #555d66;
}

/*------------------------------------------------------------------------------
  20.0 - Settings
------------------------------------------------------------------------------*/

.timezone-info code {
	white-space: nowrap;
}

.defaultavatarpicker .avatar {
	margin: 2px 0;
	vertical-align: middle;
}

.options-general-php .date-time-text {
	display: inline-block;
	min-width: 10em;
}

.options-general-php input.small-text {
	width: 56px;
}

.options-general-php .spinner {
	float: none;
	margin: -3px 3px 0;
}

.settings-php .language-install-spinner,
.options-general-php .language-install-spinner {
	display: inline-block;
	float: none;
	margin: -3px 5px 0;
	vertical-align: middle;
}

.form-table.permalink-structure .available-structure-tags li {
	float: left;
	margin-right: 5px;
}

/*------------------------------------------------------------------------------
  21.0 - Network Admin
------------------------------------------------------------------------------*/

.setup-php textarea {
	max-width: 100%;
}

.form-field #site-address {
	max-width: 25em;
}

.form-field #domain {
	max-width: 22em;
}

.form-field #site-title,
.form-field #admin-email,
.form-field #path,
.form-field #blog_registered,
.form-field #blog_last_updated {
	max-width: 25em;
}

.form-field #path {
	margin-bottom: 5px;
}

#search-users,
#search-sites {
	max-width: 100%;
}

/*------------------------------------------------------------------------------
   Credentials check dialog for Install and Updates
------------------------------------------------------------------------------*/

.request-filesystem-credentials-dialog {
	display: none;
	/* The customizer uses visibility: hidden on the body for full-overlays. */
	visibility: visible;
}

.request-filesystem-credentials-dialog .notification-dialog {
	top: 10%;
	max-height: 85%;
}

.request-filesystem-credentials-dialog-content {
	margin: 25px;
}

#request-filesystem-credentials-title {
	font-size: 1.3em;
	margin: 1em 0;
}

.request-filesystem-credentials-form legend {
	font-size: 1em;
	padding: 1.33em 0;
	font-weight: 600;
}

.request-filesystem-credentials-form input[type="text"],
.request-filesystem-credentials-form input[type="password"] {
	display: block;
}

.request-filesystem-credentials-dialog input[type="text"],
.request-filesystem-credentials-dialog input[type="password"] {
	width: 100%;
}

.request-filesystem-credentials-form .field-title {
	font-weight: 600;
}

.request-filesystem-credentials-dialog label[for="hostname"],
.request-filesystem-credentials-dialog label[for="public_key"],
.request-filesystem-credentials-dialog label[for="private_key"] {
	display: block;
	margin-bottom: 1em;
}

.request-filesystem-credentials-dialog .ftp-username,
.request-filesystem-credentials-dialog .ftp-password {
	float: left;
	width: 48%;
}

.request-filesystem-credentials-dialog .ftp-password {
	margin-left: 4%;
}

.request-filesystem-credentials-dialog .request-filesystem-credentials-action-buttons {
	text-align: right;
}

.request-filesystem-credentials-dialog label[for="ftp"] {
	margin-right: 10px;
}

.request-filesystem-credentials-dialog #auth-keys-desc {
	margin-bottom: 0;
}

#request-filesystem-credentials-dialog .button:not(:last-child) {
	margin-right: 10px;
}

#request-filesystem-credentials-form .cancel-button {
	display: none;
}

#request-filesystem-credentials-dialog .cancel-button {
	display: inline;
}

.request-filesystem-credentials-dialog .ftp-username,
.request-filesystem-credentials-dialog .ftp-password {
	float: none;
	width: auto;
}

.request-filesystem-credentials-dialog .ftp-username {
	margin-bottom: 1em;
}

.request-filesystem-credentials-dialog .ftp-password {
	margin: 0;
}

.request-filesystem-credentials-dialog .ftp-password em {
	color: #888;
}

.request-filesystem-credentials-dialog label {
	display: block;
	line-height: 1.5;
	margin-bottom: 1em;
}

.request-filesystem-credentials-form legend {
	padding-bottom: 0;
}

.request-filesystem-credentials-form #ssh-keys legend {
	font-size: 1.3em;
}

.request-filesystem-credentials-form .notice {
	margin: 0 0 20px 0;
	clear: both;
}

/*------------------------------------------------------------------------------
   Privacy Policy settings screen
------------------------------------------------------------------------------*/
.tools-privacy-policy-page form {
	margin-bottom: 1.3em;
}

.tools-privacy-policy-page input.button,
.tools-privacy-policy-page select {
	margin-left: 6px;
}

.tools-privacy-edit {
	margin: 1.5em 0;
}

.tools-privacy-policy-page span {
	line-height: 2em;
}

.privacy_requests .column-email {
	width: 40%;
}

.privacy_requests .column-type {
	text-align: center;
}

.privacy_requests thead td:first-child,
.privacy_requests tfoot td:first-child {
	border-left: 4px solid #fff;
}

.privacy_requests tbody th {
	border-left: 4px solid #fff;
	background: #fff;
	box-shadow: inset 0 -1px 0 rgba(0,0,0,0.1);
}

.privacy_requests tbody .has-request-results th {
	box-shadow: none;
}

.privacy_requests tbody .request-results th .notice {
	margin: 0 0 5px;
}

.privacy_requests tbody td {
	background: #fff;
	box-shadow: inset 0 -1px 0 rgba(0,0,0,0.1);
}

.privacy_requests tbody .has-request-results td {
	box-shadow: none;
}

.privacy_requests .next_steps .button {
	height: auto;
	line-height: 1.5;
	padding: 4px 10px;
	word-break: break-all;
	white-space: unset;
}

.privacy_requests .status-request-confirmed th,
.privacy_requests .status-request-confirmed td {
	background-color: #f7fcfe;
	border-left-color: #00a0d2;
}

.privacy_requests .status-request-failed th,
.privacy_requests .status-request-failed td {
	background-color: #fef7f1;
	border-left-color: #d64d21;
}

.privacy_requests .export_personal_data_failed a {
	vertical-align: baseline;
}

.status-label {
	font-weight: 600;
}

.status-label.status-request-pending {
	font-weight: 400;
	font-style: italic;
	color: #6c7781;
}

.status-label.status-request-failed {
	color: #aa0000;
	font-weight: 600;
}

.wp-privacy-request-form {
	clear: both;
}

.wp-privacy-request-form-field {
	margin: 1.5em 0;
}

.wp-privacy-request-form label {
	font-weight: 600;
	line-height: 1.5;
	padding-bottom: .5em;
	display: block;
}

.wp-privacy-request-form input {
	line-height: 1.5;
	margin: 0;
}

.email-personal-data::before {
	display: inline-block;
	font: normal 20px/1 dashicons;
	margin: 3px 5px 0 -2px;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	vertical-align: top;
}

.email-personal-data--sending::before {
	color: #f56e28;
	content: "\f463";
	animation: rotation 2s infinite linear;
}

.email-personal-data--sent::before {
	color: #79ba49;
	content: "\f147";
}


/* =Media Queries
-------------------------------------------------------------- */

@media screen and (max-width: 782px) {
	/* Input Elements */
	textarea {
		-webkit-appearance: none;
	}

	input[type="text"],
	input[type="email"],
	input[type="search"],
	input[type="password"],
	input[type="number"] {
		-webkit-appearance: none;
		padding: 6px 10px;
	}

	input[type="number"] {
		height: 40px;
	}

	input.code {
		padding-bottom: 5px;
		padding-top: 10px;
	}

	input[type="checkbox"],
	.widefat th input[type="checkbox"],
	.widefat thead td input[type="checkbox"],
	.widefat tfoot td input[type="checkbox"] {
		-webkit-appearance: none;
		padding: 10px;
	}

	.widefat th input[type="checkbox"],
	.widefat thead td input[type="checkbox"],
	.widefat tfoot td input[type="checkbox"] {
		margin-bottom: 8px;
	}

	input[type="checkbox"]:checked:before,
	.widefat th input[type="checkbox"]:before,
	.widefat thead td input[type="checkbox"]:before,
	.widefat tfoot td input[type="checkbox"]:before {
		font: normal 30px/1 dashicons;
		margin: -3px -5px;
	}

	input[type="radio"],
	input[type="checkbox"] {
		height: 25px;
		width: 25px;
	}

	.wp-admin p input[type="checkbox"],
	.wp-admin p input[type="radio"] {
		margin-top: -3px;
	}

	input[type="radio"]:checked:before {
		vertical-align: middle;
		width: 9px;
		height: 9px;
		margin: 7px;
		line-height: 16px;
	}

	.wp-upload-form input[type="submit"] {
		margin-top: 10px;
	}

	#wpbody select {
		height: 36px;
		font-size: 16px;
	}

	.wp-admin .button-cancel {
		padding: 0;
		font-size: 14px;
	}

	#adduser .form-field input,
	#createuser .form-field input {
		width: 100%;
	}

	.form-table {
		box-sizing: border-box;
	}

	.form-table th,
	.form-table td,
	.label-responsive {
		display: block;
		width: auto;
		vertical-align: middle;
	}

	.label-responsive {
		margin: 0.5em 0;
	}

	.export-filters li {
		margin-bottom: 0;
	}

	.form-table .color-palette td {
		display: table-cell;
		width: 15px;
	}

	.form-table table.color-palette {
		margin-right: 10px;
	}

	textarea,
	input {
		font-size: 16px;
	}

	.form-table td input[type="text"],
	.form-table td input[type="email"],
	.form-table td input[type="password"],
	.form-table td select,
	.form-table td textarea,
	.form-table span.description,
	#profile-page .form-table textarea {
		width: 100%;
		font-size: 16px;
		line-height: 1.5;
		padding: 7px 10px;
		display: block;
		max-width: none;
		box-sizing: border-box;
	}

	.form-table .form-required.form-invalid td:after {
		float: right;
		margin: -30px 3px 0 0;
	}

	#wpbody .form-table td select {
		height: 40px;
	}

	input[type="text"].small-text,
	input[type="search"].small-text,
	input[type="password"].small-text,
	input[type="number"].small-text,
	input[type="number"].small-text,
	.form-table input[type="text"].small-text {
		width: auto;
		max-width: 4.375em; /* 70px, enough for 4 digits to fit comfortably */
		display: inline;
		padding: 3px 6px;
		margin: 0 3px;
	}

	#pass-strength-result {
		width: 100%;
		box-sizing: border-box;
		padding: 8px;
	}

	p.search-box {
		float: none;
		position: absolute;
		bottom: 0;
		width: 98%;
		height: 90px;
		margin-bottom: 20px;
	}

	p.search-box input[name="s"] {
		height: auto;
		float: none;
		width: 100%;
		margin-bottom: 10px;
		vertical-align: middle;
		-webkit-appearance: none;
	}

	p.search-box input[type="submit"] {
		margin-bottom: 10px;
	}

	.form-table span.description {
		display: inline;
		padding: 4px 0 0;
		line-height: 1.4em;
		font-size: 14px;
	}

	.form-table th {
		padding-top: 10px;
		padding-bottom: 0;
		border-bottom: 0;
	}

	.form-table td {
		margin-bottom: 0;
		padding-bottom: 6px;
		padding-top: 4px;
		padding-left: 0;
	}

	.form-table.permalink-structure td code {
		margin-left: 32px;
	}

	.form-table.permalink-structure td input[type="text"] {
		margin-left: 32px;
		margin-top: 4px;
		width: 96%;
	}

	.form-table input.regular-text {
		width: 100%;
	}

	.form-table label {
		font-size: 14px;
	}

	.form-table fieldset label {
		display: block;
	}

	#utc-time,
	#local-time {
		display: block;
		float: none;
		margin-top: 0.5em;
	}

	.form-field #domain {
		max-width: none;
	}

	/* New Password */
	.wp-pwd {
		position: relative;
	}

	.wp-pwd [type="text"],
	.wp-pwd [type="password"] {
		padding-right: 40px;
	}

	.wp-pwd button.button {
		background: transparent;
		border: none;
		box-shadow: none;
		line-height: 2;
		margin: 0;
		padding: 5px 10px;
		position: absolute;
		right: 0;
		top: 0;
	}

	.wp-pwd button.button:hover,
	.wp-pwd button.button:focus,
	.wp-pwd button.button:active {
		background: transparent;
	}

	.wp-pwd .button .text {
		display: none;
	}

	.options-general-php input[type="text"].small-text {
		max-width: 6.25em;
		margin: 0;
	}

	/* Privacy Policy settings screen */
	.tools-privacy-policy-page form.wp-create-privacy-page {
		margin-bottom: 1em;
	}

	.tools-privacy-policy-page input#set-page,
	.tools-privacy-policy-page select {
		margin: 10px 0 0;
	}

	.tools-privacy-policy-page .wp-create-privacy-page span {
		display: block;
		margin-bottom: 1em;
	}

	.tools-privacy-policy-page .wp-create-privacy-page .button {
		margin-left: 0;
	}

	.wp-list-table.privacy_requests tr:not(.inline-edit-row):not(.no-items) td.column-primary:not(.check-column) {
		display: table-cell;
	}

	.wp-list-table.privacy_requests.widefat th input,
	.wp-list-table.privacy_requests.widefat thead td input {
		margin-left: 5px;
	}
}

@media only screen and (max-width: 768px) {
	.form-field input[type="text"],
	.form-field input[type="email"],
	.form-field input[type="password"],
	.form-field select,
	.form-field textarea {
		width: 99%;
	}

	.form-wrap .form-field {
		padding: 0;
	}

	/* users */
	#profile-page .form-table textarea {
		max-width: 400px;
		width: auto;
	}
}

@media only screen and (max-height: 480px), screen and (max-width: 450px) {
	/* Request Credentials / File Editor Warning */
	.request-filesystem-credentials-dialog .notification-dialog,
	.file-editor-warning .notification-dialog {
		width: 100%;
		height: 100%;
		max-height: 100%;
		position: fixed;
		top: 0;
		margin: 0;
		left: 0;
	}
}

/* Smartphone */
@media screen and (max-width: 600px) {
	/* Color Picker Options */
	.color-option {
		width: 49%;
	}
}

@media only screen and (max-width: 320px) {
	.options-general-php .date-time-text.date-time-custom-text {
		min-width: 0;
		margin-right: 0.5em;
	}
}

@keyframes rotation {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(359deg);
	}
}
