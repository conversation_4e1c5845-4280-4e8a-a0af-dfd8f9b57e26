.wp-pointer-content {
	padding: 0 0 10px;
	position: relative;
	font-size: 13px;
	background: #fff;
	border: 1px solid #ddd;
	box-shadow: 0 3px 6px rgba(0,0,0,0.075);
}

.wp-pointer-content h3 {
	position: relative;
	margin: -1px -1px 5px;
	padding: 15px 60px 14px 18px;
	border: 1px solid #3592b6;
	border-bottom: none;
	line-height: 1.4em;
	font-size: 14px;
	color: #fff;
	background: #00a0d2;
}

.wp-pointer-content h3:before {
	background: #fff;
	border-radius: 50%;
	color: #00a0d2;
	content: "\f227";
	font: normal 20px/1.6 dashicons;
	position: absolute;
	top: 8px;
	right: 15px;
	speak: none;
	text-align: center;
	width: 32px;
	height: 32px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wp-pointer-content h4 {
	margin: 1.33em 20px 1em;
	font-size: 1.15em;
}

.wp-pointer-content p {
	padding: 0 20px;
}

.wp-pointer-buttons {
	margin: 0;
	padding: 5px 15px;
	overflow: auto;
}

.wp-pointer-buttons a {
	float: left;
	display: inline-block;
	text-decoration: none;
}

.wp-pointer-buttons a.close {
	padding-right: 3px;
	position: relative;
}

.wp-pointer-buttons a.close:before {
	background: none;
	color: #72777c;
	content: "\f153";
	display: block !important;
	font: normal 16px/1 dashicons;
	speak: none;
	margin: 1px 0;
	text-align: center;
	-webkit-font-smoothing: antialiased !important;
	width: 10px;
	height: 100%;
	position: absolute;
	right: -15px;
	top: 1px;
}

.wp-pointer-buttons a.close:hover:before {
	color: #c00;
}

/* The arrow base class must take up no space, even with transparent borders. */
.wp-pointer-arrow,
.wp-pointer-arrow-inner {
	position: absolute;
	width: 0;
	height: 0;
}

.wp-pointer-arrow {
	z-index: 10;
	width: 0;
	height: 0;
	border: 0 solid transparent;
}

.wp-pointer-arrow-inner {
	z-index: 20;
}

/* Make Room for the Arrow! */
.wp-pointer-top,
.wp-pointer-undefined {
	padding-top: 13px;
}

.wp-pointer-bottom {
	margin-top: -13px;
	padding-bottom: 13px;
}

/* rtl:ignore */
.wp-pointer-left {
	padding-left: 13px;
}
/* rtl:ignore */
.wp-pointer-right {
	margin-left: -13px;
	padding-right: 13px;
}

/* Base Size & Positioning */
.wp-pointer-top .wp-pointer-arrow,
.wp-pointer-bottom .wp-pointer-arrow,
.wp-pointer-undefined .wp-pointer-arrow {
	right: 50px;
}

.wp-pointer-left .wp-pointer-arrow,
.wp-pointer-right .wp-pointer-arrow {
	top: 50%;
	margin-top: -15px;
}

/* Arrow Sprite */
.wp-pointer-top .wp-pointer-arrow,
.wp-pointer-undefined .wp-pointer-arrow {
	top: 0;
	border-width: 0 13px 13px 13px;
	border-bottom-color: #3592b6;
}

.wp-pointer-top .wp-pointer-arrow-inner,
.wp-pointer-undefined .wp-pointer-arrow-inner {
	top: 1px;
	margin-right: -13px;
	margin-top: -13px;
	border: 13px solid transparent;
	border-bottom-color: #00a0d2;
	display: block;
	content: " ";
}

.wp-pointer-bottom .wp-pointer-arrow {
	bottom: 0;
	border-width: 13px 13px 0 13px;
	border-top-color: #ccc;
}

.wp-pointer-bottom .wp-pointer-arrow-inner {
	bottom: 1px;
	margin-right: -13px;
	margin-bottom: -13px;
	border: 13px solid transparent;
	border-top-color: #fff;
	display: block;
	content: " ";
}

/* rtl:ignore */
.wp-pointer-left .wp-pointer-arrow {
	left: 0;
	border-width: 13px 13px 13px 0;
	border-right-color: #ccc;
}

/* rtl:ignore */
.wp-pointer-left .wp-pointer-arrow-inner {
	left: 1px;
	margin-left: -13px;
	margin-top: -13px;
	border: 13px solid transparent;
	border-right-color: #fff;
	display: block;
	content: " ";
}

/* rtl:ignore */
.wp-pointer-right .wp-pointer-arrow {
	right: 0;
	border-width: 13px 0 13px 13px;
	border-left-color: #ccc;
}

/* rtl:ignore */
.wp-pointer-right .wp-pointer-arrow-inner {
	right: 1px;
	margin-right: -13px;
	margin-top: -13px;
	border: 13px solid transparent;
	border-left-color: #fff;
	display: block;
	content: " ";
}

.wp-pointer.arrow-bottom .wp-pointer-content {
	margin-bottom: -45px;
}

.wp-pointer.arrow-bottom .wp-pointer-arrow {
	top: 100%;
	margin-top: -30px;
}

/* Disable pointers at responsive sizes */
@media screen and (max-width: 782px) {
	.wp-pointer {
		display: none;
	}
}
