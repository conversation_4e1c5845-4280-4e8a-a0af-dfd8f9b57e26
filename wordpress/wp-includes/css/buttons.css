/* ----------------------------------------------------------------------------

NOTE: If you edit this file, you should make sure that the CSS rules for
buttons in the following files are updated.

* jquery-ui-dialog.css
* editor.css

WordPress-style Buttons
=======================
Create a button by adding the `.button` class to an element. For backward
compatibility, we support several other classes (such as `.button-secondary`),
but these will *not* work with the stackable classes described below.

Button Styles
-------------
To display a primary button style, add the `.button-primary` class to a button.

Button Sizes
------------
Adjust a button's size by adding the `.button-large` or `.button-small` class.

Button States
-------------
Lock the state of a button by adding the name of the pseudoclass as
an actual class (e.g. `.hover` for `:hover`).


TABLE OF CONTENTS:
------------------
 1.0 - But<PERSON> Layouts
 2.0 - Default Button Style
 3.0 - Primary Button Style
 4.0 - Button Groups
 5.0 - Responsive Button Styles

---------------------------------------------------------------------------- */

/* ----------------------------------------------------------------------------
  1.0 - Button Layouts
---------------------------------------------------------------------------- */

.wp-core-ui .button,
.wp-core-ui .button-primary,
.wp-core-ui .button-secondary {
	display: inline-block;
	text-decoration: none;
	font-size: 13px;
	line-height: 26px;
	height: 28px;
	margin: 0;
	padding: 0 10px 1px;
	cursor: pointer;
	border-width: 1px;
	border-style: solid;
	-webkit-appearance: none;
	border-radius: 3px;
	white-space: nowrap;
	box-sizing: border-box;
}

/* Remove the dotted border on :focus and the extra padding in Firefox */
.wp-core-ui button::-moz-focus-inner,
.wp-core-ui input[type="reset"]::-moz-focus-inner,
.wp-core-ui input[type="button"]::-moz-focus-inner,
.wp-core-ui input[type="submit"]::-moz-focus-inner {
	border-width: 0;
	border-style: none;
	padding: 0;
}

.wp-core-ui .button.button-large,
.wp-core-ui .button-group.button-large .button {
	height: 30px;
	line-height: 28px;
	padding: 0 12px 2px;
}

.wp-core-ui .button.button-small,
.wp-core-ui .button-group.button-small .button {
	height: 24px;
	line-height: 22px;
	padding: 0 8px 1px;
	font-size: 11px;
}

.wp-core-ui .button.button-hero,
.wp-core-ui .button-group.button-hero .button {
	font-size: 14px;
	height: 46px;
	line-height: 44px;
	padding: 0 36px;
}

/* Only visible in Windows High Contrast mode */
.wp-core-ui .button:active,
.wp-core-ui .button:focus {
	outline: 2px solid transparent;
	/* Reset inherited offset from Gutenberg */
	outline-offset: 0;
}

.wp-core-ui .button.hidden {
	display: none;
}

/* Style Reset buttons as simple text links */

.wp-core-ui input[type="reset"],
.wp-core-ui input[type="reset"]:hover,
.wp-core-ui input[type="reset"]:active,
.wp-core-ui input[type="reset"]:focus {
	background: none;
	border: none;
	box-shadow: none;
	padding: 0 2px 1px;
	width: auto;
}

/* ----------------------------------------------------------------------------
  2.0 - Default Button Style
---------------------------------------------------------------------------- */

.wp-core-ui .button,
.wp-core-ui .button-secondary {
	color: #555;
	border-color: #cccccc;
	background: #f7f7f7;
	box-shadow: 0 1px 0 #cccccc;
	vertical-align: top;
}

.wp-core-ui p .button {
	vertical-align: baseline;
}

.wp-core-ui .button.hover,
.wp-core-ui .button:hover,
.wp-core-ui .button-secondary:hover,
.wp-core-ui .button.focus,
.wp-core-ui .button:focus,
.wp-core-ui .button-secondary:focus {
	background: #fafafa;
	border-color: #999;
	color: #23282d;
}

.wp-core-ui .button.focus,
.wp-core-ui .button:focus,
.wp-core-ui .button-secondary:focus {
	border-color: #5b9dd9;
	box-shadow: 0 0 3px rgba( 0, 115, 170, .8 );
}

.wp-core-ui .button.active,
.wp-core-ui .button.active:hover,
.wp-core-ui .button:active,
.wp-core-ui .button-secondary:active {
	background: #eee;
	border-color: #999;
	box-shadow: inset 0 2px 5px -3px rgba( 0, 0, 0, 0.5 );
	transform: translateY(1px);
}

.wp-core-ui .button.active:focus {
	border-color: #5b9dd9;
	box-shadow:
		inset 0 2px 5px -3px rgba( 0, 0, 0, 0.5 ),
		0 0 3px rgba( 0, 115, 170, .8 );
}

.wp-core-ui .button[disabled],
.wp-core-ui .button:disabled,
.wp-core-ui .button.disabled,
.wp-core-ui .button-secondary[disabled],
.wp-core-ui .button-secondary:disabled,
.wp-core-ui .button-secondary.disabled,
.wp-core-ui .button-disabled {
	color: #a0a5aa !important;
	border-color: #ddd !important;
	background: #f7f7f7 !important;
	box-shadow: none !important;
	text-shadow: 0 1px 0 #fff !important;
	cursor: default;
	transform: none !important;
}

/* Buttons that look like links, for a cross of good semantics with the visual */
.wp-core-ui .button-link {
	margin: 0;
	padding: 0;
	box-shadow: none;
	border: 0;
	border-radius: 0;
	background: none;
	cursor: pointer;
	text-align: left;
	/* Mimics the default link style in common.css */
	color: #0073aa;
	text-decoration: underline;
	transition-property: border, background, color;
	transition-duration: .05s;
	transition-timing-function: ease-in-out;
}

.wp-core-ui .button-link:hover,
.wp-core-ui .button-link:active {
	color: #00a0d2;
}

.wp-core-ui .button-link:focus {
	color: #124964;
	box-shadow:
		0 0 0 1px #5b9dd9,
		0 0 2px 1px rgba(30, 140, 190, .8);
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
}

.wp-core-ui .button-link-delete {
	color: #a00;
}

.wp-core-ui .button-link-delete:hover,
.wp-core-ui .button-link-delete:focus {
	color: #dc3232;
}

.ie8 .wp-core-ui .button-link:focus {
	outline: #5b9dd9 solid 1px;
}

/* ----------------------------------------------------------------------------
  3.0 - Primary Button Style
---------------------------------------------------------------------------- */

.wp-core-ui .button-primary {
	background: #0085ba;
	border-color: #0073aa #006799 #006799;
	box-shadow: 0 1px 0 #006799;
	color: #fff;
	text-decoration: none;
	text-shadow: 0 -1px 1px #006799,
		1px 0 1px #006799,
		0 1px 1px #006799,
		-1px 0 1px #006799;
}

.wp-core-ui .button-primary.hover,
.wp-core-ui .button-primary:hover,
.wp-core-ui .button-primary.focus,
.wp-core-ui .button-primary:focus {
	background: #008ec2;
	border-color: #006799;
	color: #fff;
}

.wp-core-ui .button-primary.focus,
.wp-core-ui .button-primary:focus {
	box-shadow: 0 1px 0 #0073aa,
		0 0 2px 1px #33b3db;
}

.wp-core-ui .button-primary.active,
.wp-core-ui .button-primary.active:hover,
.wp-core-ui .button-primary.active:focus,
.wp-core-ui .button-primary:active {
	background: #0073aa;
	border-color: #006799;
	box-shadow: inset 0 2px 0 #006799;
	vertical-align: top;
}

.wp-core-ui .button-primary[disabled],
.wp-core-ui .button-primary:disabled,
.wp-core-ui .button-primary-disabled,
.wp-core-ui .button-primary.disabled {
	color: #66c6e4 !important;
	background: #008ec2 !important;
	border-color: #007cb2 !important;
	box-shadow: none !important;
	text-shadow: 0 -1px 0 rgba( 0, 0, 0, 0.1 ) !important;
	cursor: default;
}

.wp-core-ui .button.button-primary.button-hero {
	box-shadow: 0 2px 0 #006799;
}

.wp-core-ui .button.button-primary.button-hero.active,
.wp-core-ui .button.button-primary.button-hero.active:hover,
.wp-core-ui .button.button-primary.button-hero.active:focus,
.wp-core-ui .button.button-primary.button-hero:active {
	box-shadow: inset 0 3px 0 #006799;
}

/* ----------------------------------------------------------------------------
  4.0 - Button Groups
---------------------------------------------------------------------------- */

.wp-core-ui .button-group {
	position: relative;
	display: inline-block;
	white-space: nowrap;
	font-size: 0;
	vertical-align: middle;
}

.wp-core-ui .button-group > .button {
	display: inline-block;
	border-radius: 0;
	margin-right: -1px;
	z-index: 10;
}

.wp-core-ui .button-group > .button-primary {
	z-index: 100;
}

.wp-core-ui .button-group > .button:hover {
	z-index: 20;
}

.wp-core-ui .button-group > .button:first-child {
	border-radius: 3px 0 0 3px;
}

.wp-core-ui .button-group > .button:last-child {
	border-radius: 0 3px 3px 0;
}

.wp-core-ui .button-group > .button:focus {
	position: relative;
	z-index: 1;
}

/* ----------------------------------------------------------------------------
  5.0 - Responsive Button Styles
---------------------------------------------------------------------------- */

@media screen and (max-width: 782px) {

	.wp-core-ui .button,
	.wp-core-ui .button.button-large,
	.wp-core-ui .button.button-small,
	input#publish,
	input#save-post,
	a.preview {
		padding: 6px 14px;
		line-height: normal;
		font-size: 14px;
		vertical-align: middle;
		height: auto;
		margin-bottom: 4px;
	}

	#media-upload.wp-core-ui .button {
		padding: 0 10px 1px;
		height: 24px;
		line-height: 22px;
		font-size: 13px;
	}

	.media-frame.mode-grid .bulk-select .button {
		margin-bottom: 0;
	}

	/* Publish Metabox Options */
	.wp-core-ui .save-post-status.button {
		position: relative;
		margin: 0 14px 0 10px; /* 14px right margin to match all other buttons */
	}

	/* Reset responsive styles in Press This, Customizer */

	.wp-core-ui.wp-customizer .button {
		padding: 0 10px 1px;
		font-size: 13px;
		line-height: 26px;
		height: 28px;
		margin: 0;
		vertical-align: inherit;
	}

	.media-modal-content .media-toolbar-primary .media-button {
		margin-top: 10px;
		margin-left: 5px;
	}

	/* Reset responsive styles on Log in button on iframed login form */

	.interim-login .button.button-large {
		height: 30px;
		line-height: 28px;
		padding: 0 12px 2px;
	}

}
