/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * Often re-used variables
 */
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * <PERSON><PERSON> states and focus styles
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Applies editor right position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
body {
  font-family: "Noto Serif", serif;
  font-size: 16px;
  line-height: 1.8;
  color: #191e23; }

p {
  font-size: 16px;
  line-height: 1.8; }

ul,
ol {
  margin: 0;
  padding: 0; }

ul {
  list-style-type: disc; }

ol {
  list-style-type: decimal; }

ul ul,
ol ul {
  list-style-type: circle; }

.mce-content-body {
  line-height: 1.8; }
