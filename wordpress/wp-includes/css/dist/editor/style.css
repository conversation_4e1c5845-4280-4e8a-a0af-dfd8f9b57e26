@charset "UTF-8";
/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * Often re-used variables
 */
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * But<PERSON> states and focus styles
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Applies editor right position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
.editor-autocompleters__block .editor-block-icon {
  margin-right: 8px; }

.editor-autocompleters__user .editor-autocompleters__user-avatar {
  margin-right: 8px;
  flex-grow: 0;
  flex-shrink: 0;
  max-width: none;
  width: 24px;
  height: 24px; }

.editor-autocompleters__user .editor-autocompleters__user-name {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 200px;
  flex-shrink: 0;
  flex-grow: 1; }

.editor-autocompleters__user .editor-autocompleters__user-slug {
  margin-left: 8px;
  color: #8f98a1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: none;
  max-width: 100px;
  flex-grow: 0;
  flex-shrink: 0; }

.editor-autocompleters__user:hover .editor-autocompleters__user-slug {
  color: #66c6e4; }

.editor-block-drop-zone {
  border: none;
  border-radius: 0; }
  .editor-block-drop-zone .components-drop-zone__content,
  .editor-block-drop-zone.is-dragging-over-element .components-drop-zone__content {
    display: none; }
  .editor-block-drop-zone.is-close-to-bottom {
    background: none;
    border-bottom: 3px solid #0085ba; }
  body.admin-color-sunrise .editor-block-drop-zone.is-close-to-bottom{
    border-bottom: 3px solid #d1864a; }
  body.admin-color-ocean .editor-block-drop-zone.is-close-to-bottom{
    border-bottom: 3px solid #a3b9a2; }
  body.admin-color-midnight .editor-block-drop-zone.is-close-to-bottom{
    border-bottom: 3px solid #e14d43; }
  body.admin-color-ectoplasm .editor-block-drop-zone.is-close-to-bottom{
    border-bottom: 3px solid #a7b656; }
  body.admin-color-coffee .editor-block-drop-zone.is-close-to-bottom{
    border-bottom: 3px solid #c2a68c; }
  body.admin-color-blue .editor-block-drop-zone.is-close-to-bottom{
    border-bottom: 3px solid #82b4cb; }
  body.admin-color-light .editor-block-drop-zone.is-close-to-bottom{
    border-bottom: 3px solid #0085ba; }
  .editor-block-drop-zone.is-close-to-top, .editor-block-drop-zone.is-appender.is-close-to-top, .editor-block-drop-zone.is-appender.is-close-to-bottom {
    background: none;
    border-top: 3px solid #0085ba;
    border-bottom: none; }
  body.admin-color-sunrise .editor-block-drop-zone.is-close-to-top, body.admin-color-sunrise .editor-block-drop-zone.is-appender.is-close-to-top, body.admin-color-sunrise .editor-block-drop-zone.is-appender.is-close-to-bottom{
    border-top: 3px solid #d1864a; }
  body.admin-color-ocean .editor-block-drop-zone.is-close-to-top, body.admin-color-ocean .editor-block-drop-zone.is-appender.is-close-to-top, body.admin-color-ocean .editor-block-drop-zone.is-appender.is-close-to-bottom{
    border-top: 3px solid #a3b9a2; }
  body.admin-color-midnight .editor-block-drop-zone.is-close-to-top, body.admin-color-midnight .editor-block-drop-zone.is-appender.is-close-to-top, body.admin-color-midnight .editor-block-drop-zone.is-appender.is-close-to-bottom{
    border-top: 3px solid #e14d43; }
  body.admin-color-ectoplasm .editor-block-drop-zone.is-close-to-top, body.admin-color-ectoplasm .editor-block-drop-zone.is-appender.is-close-to-top, body.admin-color-ectoplasm .editor-block-drop-zone.is-appender.is-close-to-bottom{
    border-top: 3px solid #a7b656; }
  body.admin-color-coffee .editor-block-drop-zone.is-close-to-top, body.admin-color-coffee .editor-block-drop-zone.is-appender.is-close-to-top, body.admin-color-coffee .editor-block-drop-zone.is-appender.is-close-to-bottom{
    border-top: 3px solid #c2a68c; }
  body.admin-color-blue .editor-block-drop-zone.is-close-to-top, body.admin-color-blue .editor-block-drop-zone.is-appender.is-close-to-top, body.admin-color-blue .editor-block-drop-zone.is-appender.is-close-to-bottom{
    border-top: 3px solid #82b4cb; }
  body.admin-color-light .editor-block-drop-zone.is-close-to-top, body.admin-color-light .editor-block-drop-zone.is-appender.is-close-to-top, body.admin-color-light .editor-block-drop-zone.is-appender.is-close-to-bottom{
    border-top: 3px solid #0085ba; }

.editor-block-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin: 0;
  border-radius: 4px; }
  .editor-block-icon.has-colors svg {
    fill: currentColor; }
  .editor-block-icon svg {
    min-width: 20px;
    min-height: 20px;
    max-width: 24px;
    max-height: 24px; }

.editor-block-inspector__no-blocks {
  display: block;
  font-size: 13px;
  background: #fff;
  padding: 32px 16px;
  text-align: center; }

.editor-block-inspector__card {
  display: flex;
  align-items: flex-start;
  margin: -16px;
  padding: 16px; }

.editor-block-inspector__card-icon {
  border: 1px solid #ccd0d4;
  padding: 7px;
  margin-right: 10px;
  height: 36px;
  width: 36px; }

.editor-block-inspector__card-content {
  flex-grow: 1; }

.editor-block-inspector__card-title {
  font-weight: 500;
  margin-bottom: 5px; }

.editor-block-inspector__card-description {
  font-size: 13px; }

.editor-block-inspector__card .editor-block-icon {
  margin-left: -2px;
  margin-right: 10px;
  padding: 0 3px;
  width: 36px;
  height: 24px; }

.editor-block-list__layout .components-draggable__clone .editor-block-contextual-toolbar {
  display: none !important; }

.editor-block-list__layout .editor-block-list__block.is-selected.is-dragging .editor-block-list__block-edit::before {
  outline: none; }

.editor-block-list__layout .editor-block-list__block.is-selected.is-dragging > .editor-block-list__block-edit > * {
  background: #f8f9f9; }

.editor-block-list__layout .editor-block-list__block.is-selected.is-dragging > .editor-block-list__block-edit > * > * {
  visibility: hidden; }

.editor-block-list__layout .editor-block-list__block.is-selected.is-dragging .editor-block-mover,
.editor-block-list__layout .editor-block-list__block.is-selected.is-dragging .editor-block-contextual-toolbar {
  display: none; }

.editor-block-list__layout .editor-block-list__block.is-selected > .editor-block-list__block-edit .reusable-block-edit-panel * {
  z-index: 1; }

/**
 * General layout
 */
@media (min-width: 600px) {
  .editor-block-list__layout {
    padding-left: 46px;
    padding-right: 46px; } }

.editor-block-list__block .editor-block-list__layout {
  padding-left: 0;
  padding-right: 0;
  margin-left: -14px;
  margin-right: -14px; }

.editor-block-list__layout .editor-default-block-appender > .editor-default-block-appender__content,
.editor-block-list__layout > .editor-block-list__block > .editor-block-list__block-edit,
.editor-block-list__layout > .editor-block-list__layout > .editor-block-list__block > .editor-block-list__block-edit {
  margin-top: 32px;
  margin-bottom: 32px; }

.editor-block-list__layout .editor-block-list__block {
  position: relative;
  padding-left: 14px;
  padding-right: 14px;
  overflow-wrap: break-word;
  /**
	 * Notices
	 */
  /**
	 * Block outline layout
	 */ }
  @media (min-width: 600px) {
    .editor-block-list__layout .editor-block-list__block {
      padding-left: 43px;
      padding-right: 43px; } }
  .editor-block-list__layout .editor-block-list__block .components-placeholder .components-with-notices-ui {
    margin: -10px 20px 12px 20px;
    width: calc(100% - 40px); }
  .editor-block-list__layout .editor-block-list__block .components-with-notices-ui {
    margin: 0 0 12px 0;
    width: 100%; }
    .editor-block-list__layout .editor-block-list__block .components-with-notices-ui .components-notice {
      margin-left: 0;
      margin-right: 0; }
      .editor-block-list__layout .editor-block-list__block .components-with-notices-ui .components-notice .components-notice__content {
        font-size: 13px; }
  .editor-block-list__layout .editor-block-list__block .editor-block-list__block-edit {
    position: relative; }
    .editor-block-list__layout .editor-block-list__block .editor-block-list__block-edit::before {
      z-index: 0;
      content: "";
      position: absolute;
      outline: 1px solid transparent;
      transition: outline 0.1s linear;
      pointer-events: none;
      right: -14px;
      left: -14px;
      top: -14px;
      bottom: -14px; }
  .editor-block-list__layout .editor-block-list__block.is-selected > .editor-block-list__block-edit::before {
    outline: 1px solid rgba(145, 151, 162, 0.25); }
    .is-dark-theme .editor-block-list__layout .editor-block-list__block.is-selected > .editor-block-list__block-edit::before {
      outline-color: rgba(255, 255, 255, 0.3); }
  .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before {
    outline: 1px solid #007cba; }
  body.admin-color-sunrise .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before{
    outline: 1px solid #837425; }
  body.admin-color-ocean .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before{
    outline: 1px solid #5e7d5e; }
  body.admin-color-midnight .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before{
    outline: 1px solid #497b8d; }
  body.admin-color-ectoplasm .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before{
    outline: 1px solid #523f6d; }
  body.admin-color-coffee .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before{
    outline: 1px solid #59524c; }
  body.admin-color-blue .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before{
    outline: 1px solid #417e9B; }
  body.admin-color-light .editor-block-list__layout .editor-block-list__block.is-hovered > .editor-block-list__block-edit::before{
    outline: 1px solid #007cba; }
  .editor-block-list__layout .editor-block-list__block.is-focus-mode:not(.is-multi-selected) {
    opacity: 0.5;
    transition: opacity 0.1s linear; }
    .editor-block-list__layout .editor-block-list__block.is-focus-mode:not(.is-multi-selected):not(.is-focused) .editor-block-list__block, .editor-block-list__layout .editor-block-list__block.is-focus-mode:not(.is-multi-selected).is-focused {
      opacity: 1; }

/**
 * Cross-block selection
 */

.editor-block-list__layout .editor-block-list__block ::selection {
  background-color: #b3e7fe; }

.editor-block-list__layout .editor-block-list__block.is-multi-selected *::selection {
  background-color: transparent; }

.editor-block-list__layout .editor-block-list__block.is-multi-selected .editor-block-list__block-edit::before {
  background: #b3e7fe;
  mix-blend-mode: multiply;
  top: -14px;
  bottom: -14px; }
  .is-dark-theme .editor-block-list__layout .editor-block-list__block.is-multi-selected .editor-block-list__block-edit::before {
    mix-blend-mode: soft-light; }

/**
 * Block styles and alignments
 */
.editor-block-list__layout .editor-block-list__block.has-warning {
  min-height: 36px; }

.editor-block-list__layout .editor-block-list__block.has-warning .editor-block-list__block-edit > * {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none; }

.editor-block-list__layout .editor-block-list__block.has-warning .editor-block-list__block-edit .editor-warning {
  pointer-events: all; }

.editor-block-list__layout .editor-block-list__block.has-warning:not(.is-hovered) .editor-block-list__block-edit::before {
  outline-color: rgba(145, 151, 162, 0.25); }
  .is-dark-theme .editor-block-list__layout .editor-block-list__block.has-warning:not(.is-hovered) .editor-block-list__block-edit::before {
    outline-color: rgba(255, 255, 255, 0.3); }

.editor-block-list__layout .editor-block-list__block.has-warning .editor-block-list__block-edit::after {
  content: "";
  position: absolute;
  background-color: rgba(248, 249, 249, 0.4);
  top: -14px;
  bottom: -14px;
  right: -14px;
  left: -14px; }

.editor-block-list__layout .editor-block-list__block.has-warning.is-multi-selected .editor-block-list__block-edit::after {
  background-color: transparent; }

.editor-block-list__layout .editor-block-list__block.has-warning.is-selected .editor-block-list__block-edit::after {
  bottom: 22px; }
  @media (min-width: 600px) {
    .editor-block-list__layout .editor-block-list__block.has-warning.is-selected .editor-block-list__block-edit::after {
      bottom: -14px; } }

.editor-block-list__layout .editor-block-list__block.is-typing .editor-block-list__empty-block-inserter,
.editor-block-list__layout .editor-block-list__block.is-typing .editor-block-list__side-inserter {
  opacity: 0; }

.editor-block-list__layout .editor-block-list__block .editor-block-list__empty-block-inserter,
.editor-block-list__layout .editor-block-list__block .editor-block-list__side-inserter {
  opacity: 1;
  transition: opacity 0.2s; }

.editor-block-list__layout .editor-block-list__block.is-reusable > .editor-block-list__block-edit::before {
  outline: 1px dashed rgba(145, 151, 162, 0.25); }
  .is-dark-theme .editor-block-list__layout .editor-block-list__block.is-reusable > .editor-block-list__block-edit::before {
    outline-color: rgba(255, 255, 255, 0.3); }

.editor-block-list__layout .editor-block-list__block[data-align="left"], .editor-block-list__layout .editor-block-list__block[data-align="right"] {
  z-index: 20;
  width: 100%;
  height: 0; }
  .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-list__block-edit, .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-list__block-edit {
    margin-top: 0; }
    .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-list__block-edit::before, .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-list__block-edit::before {
      content: none; }
  .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar, .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar {
    margin-bottom: 1px; }
  .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-mover,
  .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-list__block-mobile-toolbar, .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-mover,
  .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-list__block-mobile-toolbar {
    display: none; }
  .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar, .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar {
    width: auto;
    border-bottom: 1px solid #e2e4e7;
    bottom: auto; }

.editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar {
  left: 0;
  right: auto; }

.editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar {
  left: auto;
  right: 0; }

@media (min-width: 600px) {
  .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar,
  .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar {
    top: 14px; } }

.editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-list__block-edit {
  /*!rtl:begin:ignore*/
  float: left;
  margin-right: 2em;
  /*!rtl:end:ignore*/ }

@media (min-width: 600px) {
  .editor-block-list__layout .editor-block-list__block[data-align="left"] .editor-block-toolbar {
    /*!rtl:begin:ignore*/
    left: 14px;
    right: auto;
    /*!rtl:end:ignore*/ } }

.editor-block-list__layout .editor-block-list__block[data-align="right"] > .editor-block-list__block-edit {
  /*!rtl:begin:ignore*/
  float: right;
  margin-left: 2em;
  /*!rtl:end:ignore*/ }

@media (min-width: 600px) {
  .editor-block-list__layout .editor-block-list__block[data-align="right"] .editor-block-toolbar {
    /*!rtl:begin:ignore*/
    right: 14px;
    left: auto;
    /*!rtl:end:ignore*/ } }

.editor-block-list__layout .editor-block-list__block[data-align="full"], .editor-block-list__layout .editor-block-list__block[data-align="wide"] {
  clear: both;
  z-index: 20; }
  .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-mover, .editor-block-list__layout .editor-block-list__block[data-align="wide"] > .editor-block-mover {
    top: -44px;
    bottom: auto;
    min-height: 0;
    height: auto;
    width: auto;
    z-index: inherit; }
    .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-mover::before, .editor-block-list__layout .editor-block-list__block[data-align="wide"] > .editor-block-mover::before {
      content: none; }
  .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-mover .editor-block-mover__control, .editor-block-list__layout .editor-block-list__block[data-align="wide"] > .editor-block-mover .editor-block-mover__control {
    float: left; }
  .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-list__breadcrumb, .editor-block-list__layout .editor-block-list__block[data-align="wide"] > .editor-block-list__breadcrumb {
    right: -1px; }
  .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-mover, .editor-block-list__layout .editor-block-list__block[data-align="wide"] > .editor-block-mover {
    display: none; }
  @media (min-width: 1280px) {
    .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-mover, .editor-block-list__layout .editor-block-list__block[data-align="wide"] > .editor-block-mover {
      display: block; } }
  @media (min-width: 600px) {
    .editor-block-list__layout .editor-block-list__block[data-align="full"] .editor-block-toolbar, .editor-block-list__layout .editor-block-list__block[data-align="wide"] .editor-block-toolbar {
      display: inline-flex; } }

.editor-block-list__layout .editor-block-list__block[data-align="wide"] > .editor-block-mover {
  left: -13px; }

.editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit > .editor-block-list__breadcrumb {
  right: 0; }

@media (min-width: 600px) {
  .editor-block-list__layout .editor-block-list__block[data-align="full"] {
    margin-left: -45px;
    margin-right: -45px; } }

.editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit {
  margin-left: -14px;
  margin-right: -14px; }
  @media (min-width: 600px) {
    .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit {
      margin-left: -44px;
      margin-right: -44px; } }
  .editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit figure {
    width: 100%; }

.editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit::before {
  left: 0;
  right: 0;
  border-left-width: 0;
  border-right-width: 0; }

.editor-block-list__layout .editor-block-list__block[data-align="full"] > .editor-block-mover {
  left: 1px; }

.editor-block-list__layout .editor-block-list__block[data-clear="true"] {
  float: none; }

.editor-block-list__layout .editor-block-list__block .editor-block-drop-zone {
  top: -4px;
  bottom: -3px;
  margin: 0 14px; }

.editor-block-list__layout .editor-block-list__block .editor-block-list__layout .editor-inserter-with-shortcuts {
  display: none; }

.editor-block-list__layout .editor-block-list__block .editor-block-list__layout .editor-block-list__empty-block-inserter,
.editor-block-list__layout .editor-block-list__block .editor-block-list__layout .editor-default-block-appender .editor-inserter {
  left: auto;
  right: 8px; }

/**
 * Left and right side UI; Unified toolbar on Mobile
 */
.editor-block-list__block > .editor-block-mover {
  position: absolute;
  width: 30px;
  height: 100%;
  max-height: 112px; }

.editor-block-list__block > .editor-block-mover {
  top: -15px; }

@media (min-width: 600px) {
  .editor-block-list__block.is-multi-selected .editor-block-mover, .editor-block-list__block.is-selected .editor-block-mover, .editor-block-list__block.is-hovered .editor-block-mover {
    z-index: 80; } }

.editor-block-list__block > .editor-block-mover {
  padding-right: 2px;
  left: -30px;
  display: none; }
  @media (min-width: 600px) {
    .editor-block-list__block > .editor-block-mover {
      display: block; } }

/**
 * Mobile unified toolbar.
 */
.editor-block-list__block .editor-block-list__block-mobile-toolbar {
  display: flex;
  flex-direction: row;
  transform: translateY(15px);
  margin-top: 37px;
  margin-right: -14px;
  margin-left: -14px;
  border-top: 1px solid #e2e4e7;
  height: 37px;
  box-shadow: 0 5px 10px rgba(25, 30, 35, 0.05), 0 2px 2px rgba(25, 30, 35, 0.05); }
  @media (min-width: 600px) {
    .editor-block-list__block .editor-block-list__block-mobile-toolbar {
      display: none; } }
  @media (min-width: 600px) {
    .editor-block-list__block .editor-block-list__block-mobile-toolbar {
      box-shadow: none; } }
  .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-inserter {
    position: relative;
    left: auto;
    top: auto;
    margin: 0; }
  .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-inserter__toggle,
  .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-block-mover__control {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    padding: 3px;
    margin: 0;
    justify-content: center;
    align-items: center; }
    .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-inserter__toggle .dashicon,
    .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-block-mover__control .dashicon {
      margin: auto; }
  .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-block-mover {
    display: flex;
    margin-right: auto; }
    .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-block-mover .editor-inserter,
    .editor-block-list__block .editor-block-list__block-mobile-toolbar .editor-block-mover .editor-block-mover__control {
      float: left; }

.editor-block-list__block[data-align="full"] .editor-block-list__block-mobile-toolbar {
  margin-left: 0;
  margin-right: 0; }

/**
 * In-Canvas Inserter
 */
.editor-block-list .editor-inserter {
  margin: 8px;
  cursor: move;
  cursor: -webkit-grab;
  cursor: grab; }

.editor-block-list__insertion-point {
  position: relative;
  z-index: 6;
  margin-top: -14px; }

.editor-block-list__insertion-point-indicator {
  position: absolute;
  top: calc(50% - 1px);
  height: 2px;
  left: 0;
  right: 0;
  background: #0085ba; }

body.admin-color-sunrise .editor-block-list__insertion-point-indicator{
  background: #d1864a; }

body.admin-color-ocean .editor-block-list__insertion-point-indicator{
  background: #a3b9a2; }

body.admin-color-midnight .editor-block-list__insertion-point-indicator{
  background: #e14d43; }

body.admin-color-ectoplasm .editor-block-list__insertion-point-indicator{
  background: #a7b656; }

body.admin-color-coffee .editor-block-list__insertion-point-indicator{
  background: #c2a68c; }

body.admin-color-blue .editor-block-list__insertion-point-indicator{
  background: #82b4cb; }

body.admin-color-light .editor-block-list__insertion-point-indicator{
  background: #0085ba; }

.editor-block-list__insertion-point-inserter {
  display: none;
  position: absolute;
  bottom: auto;
  left: 0;
  right: 0;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.1s linear 0.1s; }
  @media (min-width: 480px) {
    .editor-block-list__insertion-point-inserter {
      display: flex; } }
  .editor-block-list__insertion-point-inserter .editor-inserter__toggle {
    margin-top: -4px;
    border-radius: 50%;
    color: #007cba;
    background: #fff;
    height: 36px;
    width: 36px; }
    .editor-block-list__insertion-point-inserter .editor-inserter__toggle:not(:disabled):not([aria-disabled="true"]):hover {
      box-shadow: none; }
  .editor-block-list__insertion-point-inserter:hover, .editor-block-list__insertion-point-inserter.is-visible {
    opacity: 1; }

.edit-post-layout:not(.has-fixed-toolbar) .is-selected > .editor-block-list__insertion-point > .editor-block-list__insertion-point-inserter,
.edit-post-layout:not(.has-fixed-toolbar) .is-focused > .editor-block-list__insertion-point > .editor-block-list__insertion-point-inserter {
  opacity: 0;
  pointer-events: none; }
  .edit-post-layout:not(.has-fixed-toolbar) .is-selected > .editor-block-list__insertion-point > .editor-block-list__insertion-point-inserter:hover, .edit-post-layout:not(.has-fixed-toolbar) .is-selected > .editor-block-list__insertion-point > .editor-block-list__insertion-point-inserter.is-visible,
  .edit-post-layout:not(.has-fixed-toolbar) .is-focused > .editor-block-list__insertion-point > .editor-block-list__insertion-point-inserter:hover,
  .edit-post-layout:not(.has-fixed-toolbar) .is-focused > .editor-block-list__insertion-point > .editor-block-list__insertion-point-inserter.is-visible {
    opacity: 1;
    pointer-events: auto; }

.editor-block-list__block > .editor-block-list__insertion-point {
  position: absolute;
  top: -16px;
  height: 28px;
  bottom: auto;
  left: 0;
  right: 0; }
  @media (min-width: 600px) {
    .editor-block-list__block > .editor-block-list__insertion-point {
      left: -1px;
      right: -1px; } }

.editor-block-list__block[data-align="full"] > .editor-block-list__insertion-point {
  left: 0;
  right: 0; }

.editor-block-list__block .editor-block-list__block-html-textarea {
  display: block;
  margin: 0;
  width: 100%;
  border: none;
  outline: none;
  box-shadow: none;
  resize: none;
  overflow: hidden;
  font-family: Menlo, Consolas, monaco, monospace;
  font-size: 14px;
  line-height: 150%;
  transition: padding 0.2s linear; }
  .editor-block-list__block .editor-block-list__block-html-textarea:focus {
    box-shadow: none; }

/**
 * Block Toolbar when contextual.
 */
.editor-block-list__block .editor-block-contextual-toolbar {
  position: -webkit-sticky;
  position: sticky;
  z-index: 21;
  white-space: nowrap;
  text-align: left;
  pointer-events: none;
  position: absolute;
  bottom: 23px;
  left: -14px;
  right: -14px;
  border-top: 1px solid #e2e4e7; }
  .editor-block-list__block .editor-block-contextual-toolbar .components-toolbar {
    border-top: none;
    border-bottom: none; }
  @media (min-width: 600px) {
    .editor-block-list__block .editor-block-contextual-toolbar {
      border-top: none; }
      .editor-block-list__block .editor-block-contextual-toolbar .components-toolbar {
        border-top: 1px solid #e2e4e7;
        border-bottom: 1px solid #e2e4e7; } }

.editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar,
.editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar {
  margin-bottom: 1px;
  margin-top: -37px; }

.editor-block-list__block .editor-block-contextual-toolbar {
  margin-left: 0;
  margin-right: 0; }
  @media (min-width: 600px) {
    .editor-block-list__block .editor-block-contextual-toolbar {
      margin-left: -15px;
      margin-right: -15px; } }

.editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar {
  /*rtl:ignore*/
  margin-right: 15px; }

.editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar {
  /*rtl:ignore*/
  margin-left: 15px; }

.editor-block-list__block .editor-block-contextual-toolbar > * {
  pointer-events: auto; }

.editor-block-list__block.is-focus-mode:not(.is-multi-selected) > .editor-block-contextual-toolbar {
  margin-left: -28px; }

@media (min-width: 600px) {
  .editor-block-list__block .editor-block-contextual-toolbar {
    bottom: auto;
    left: auto;
    right: auto;
    box-shadow: none;
    transform: translateY(-52px); }
    @supports ((position: -webkit-sticky) or (position: sticky)) {
      .editor-block-list__block .editor-block-contextual-toolbar {
        position: -webkit-sticky;
        position: sticky;
        top: 51px; } } }

.editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar {
  /*rtl:ignore*/
  float: left; }

.editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar {
  /*rtl:ignore*/
  float: right; }

.editor-block-list__block[data-align="left"] .editor-block-contextual-toolbar,
.editor-block-list__block[data-align="right"] .editor-block-contextual-toolbar {
  transform: translateY(-15px); }

.editor-block-contextual-toolbar .editor-block-toolbar {
  width: 100%; }
  @media (min-width: 600px) {
    .editor-block-contextual-toolbar .editor-block-toolbar {
      width: auto;
      border-right: none;
      position: absolute;
      left: 0; } }

/**
 * Hover label
 */
.editor-block-list__breadcrumb {
  position: absolute;
  line-height: 1;
  z-index: 2;
  right: -14px;
  top: -15px; }
  .editor-block-list__breadcrumb .components-toolbar {
    padding: 0;
    border: none;
    background: transparent;
    line-height: 1;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-size: 11px;
    padding: 4px 4px;
    background: #007cba;
    color: #fff; }
  body.admin-color-sunrise .editor-block-list__breadcrumb .components-toolbar{
    background: #837425; }
  body.admin-color-ocean .editor-block-list__breadcrumb .components-toolbar{
    background: #5e7d5e; }
  body.admin-color-midnight .editor-block-list__breadcrumb .components-toolbar{
    background: #497b8d; }
  body.admin-color-ectoplasm .editor-block-list__breadcrumb .components-toolbar{
    background: #523f6d; }
  body.admin-color-coffee .editor-block-list__breadcrumb .components-toolbar{
    background: #59524c; }
  body.admin-color-blue .editor-block-list__breadcrumb .components-toolbar{
    background: #417e9B; }
  body.admin-color-light .editor-block-list__breadcrumb .components-toolbar{
    background: #007cba; }
    .editor-block-list__block:hover .editor-block-list__breadcrumb .components-toolbar {
      opacity: 0;
      animation: edit-post__fade-in-animation 60ms ease-out 0.5s;
      animation-fill-mode: forwards; }
  [data-align="left"] .editor-block-list__breadcrumb,
  [data-align="right"] .editor-block-list__breadcrumb {
    right: 0;
    top: 0; }

.editor-block-list__descendant-arrow::before {
  content: "→";
  display: inline-block;
  padding: 0 4px; }
  .rtl .editor-block-list__descendant-arrow::before {
    content: "←"; }

@media (min-width: 600px) {
  .editor-block-list__block::before {
    bottom: 0;
    content: "";
    left: -28px;
    position: absolute;
    right: -28px;
    top: 0; }
  .editor-block-list__block .editor-block-list__block::before {
    left: 0;
    right: 0; }
  .editor-block-list__block[data-align="full"]::before {
    content: none; } }

.editor-block-list__block .editor-warning {
  z-index: 5;
  position: relative;
  margin-right: -15px;
  margin-left: -15px;
  margin-bottom: -15px;
  transform: translateY(-15px);
  padding: 10px 14px; }
  @media (min-width: 600px) {
    .editor-block-list__block .editor-warning {
      padding: 10px 14px; } }

.block-list-appender > .editor-inserter {
  display: block; }

.block-list-appender__toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  outline: 1px dashed #8d96a0;
  width: 100%;
  color: #555d66; }
  .block-list-appender__toggle:hover {
    outline: 1px dashed #555d66; }

/**
 * Invalid block comparison
 */
.editor-block-compare {
  overflow: auto;
  height: auto; }
  @media (min-width: 600px) {
    .editor-block-compare {
      max-height: 70%; } }

.editor-block-compare__wrapper {
  display: flex;
  padding-bottom: 16px; }
  .editor-block-compare__wrapper > div {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    width: 50%;
    padding: 0 16px 0 0;
    min-width: 200px; }
    .editor-block-compare__wrapper > div button {
      float: right; }
  .editor-block-compare__wrapper .editor-block-compare__converted {
    border-left: 1px solid #ddd;
    padding-left: 15px; }
  .editor-block-compare__wrapper .editor-block-compare__html {
    font-family: Menlo, Consolas, monaco, monospace;
    font-size: 12px;
    color: #23282d;
    border-bottom: 1px solid #ddd;
    padding-bottom: 15px;
    line-height: 1.7; }
    .editor-block-compare__wrapper .editor-block-compare__html span {
      background-color: #e6ffed;
      padding-top: 3px;
      padding-bottom: 3px; }
    .editor-block-compare__wrapper .editor-block-compare__html span.editor-block-compare__added {
      background-color: #acf2bd; }
    .editor-block-compare__wrapper .editor-block-compare__html span.editor-block-compare__removed {
      background-color: #d94f4f; }
  .editor-block-compare__wrapper .editor-block-compare__preview {
    padding: 0;
    padding-top: 14px; }
    .editor-block-compare__wrapper .editor-block-compare__preview p {
      font-size: 12px;
      margin-top: 0; }
  .editor-block-compare__wrapper .editor-block-compare__action {
    margin-top: 14px; }
  .editor-block-compare__wrapper .editor-block-compare__heading {
    font-size: 1em;
    font-weight: 400;
    margin: 0.67em 0; }

.editor-block-mover {
  min-height: 56px;
  opacity: 0; }
  .editor-block-mover.is-visible {
    animation: edit-post__fade-in-animation 0.2s ease-out 0s;
    animation-fill-mode: forwards; }
  @media (min-width: 600px) {
    .editor-block-list__block:not([data-align="wide"]):not([data-align="full"]) .editor-block-mover {
      margin-top: -8px; } }

.editor-block-mover__control {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 24px;
  color: rgba(14, 28, 46, 0.62); }
  .editor-block-mover__control svg {
    width: 28px;
    height: 24px;
    padding: 2px 5px; }
  .is-dark-theme .editor-block-mover__control {
    color: rgba(255, 255, 255, 0.65); }
  .editor-block-mover__control[aria-disabled="true"] {
    cursor: default;
    pointer-events: none;
    color: rgba(130, 148, 147, 0.15); }
    .is-dark-theme .editor-block-mover__control[aria-disabled="true"] {
      color: rgba(255, 255, 255, 0.2); }

.editor-block-mover__control-drag-handle {
  cursor: move;
  cursor: -webkit-grab;
  cursor: grab;
  fill: currentColor;
  border-radius: 4px; }
  .editor-block-mover__control-drag-handle, .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):hover, .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):active, .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):focus {
    box-shadow: none;
    background: none;
    color: rgba(10, 24, 41, 0.7); }
    .is-dark-theme .editor-block-mover__control-drag-handle, .is-dark-theme .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):hover, .is-dark-theme .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):active, .is-dark-theme .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):focus {
      color: rgba(255, 255, 255, 0.75); }
  .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):active {
    cursor: -webkit-grabbing;
    cursor: grabbing; }

.editor-block-mover__description {
  display: none; }

@media (min-width: 600px) {
  .editor-block-list__layout .editor-block-list__layout .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default), .editor-block-list__layout .editor-block-list__layout
  .editor-block-mover__control {
    background: #fff;
    box-shadow: inset 0 0 0 1px #e2e4e7; }
    .editor-block-list__layout .editor-block-list__layout .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):nth-child(-n+2), .editor-block-list__layout .editor-block-list__layout
    .editor-block-mover__control:nth-child(-n+2) {
      margin-bottom: -1px; }
    .editor-block-list__layout .editor-block-list__layout .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):hover, .editor-block-list__layout .editor-block-list__layout .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):active, .editor-block-list__layout .editor-block-list__layout .editor-block-mover__control-drag-handle:not(:disabled):not([aria-disabled="true"]):not(.is-default):focus, .editor-block-list__layout .editor-block-list__layout
    .editor-block-mover__control:hover, .editor-block-list__layout .editor-block-list__layout
    .editor-block-mover__control:active, .editor-block-list__layout .editor-block-list__layout
    .editor-block-mover__control:focus {
      z-index: 1; } }

.editor-block-navigation__container {
  padding: 7px; }

.editor-block-navigation__label {
  margin: 0 0 8px;
  color: #6c7781; }

.editor-block-navigation__list,
.editor-block-navigation__paragraph {
  padding: 0;
  margin: 0; }

.editor-block-navigation__list .editor-block-navigation__list {
  margin-top: 2px;
  border-left: 2px solid #a2aab2;
  margin-left: 1em; }
  .editor-block-navigation__list .editor-block-navigation__list .editor-block-navigation__list {
    margin-left: 1.5em; }
  .editor-block-navigation__list .editor-block-navigation__list .editor-block-navigation__item {
    position: relative; }
    .editor-block-navigation__list .editor-block-navigation__list .editor-block-navigation__item::before {
      position: absolute;
      left: 0;
      background: #a2aab2;
      width: 0.5em;
      height: 2px;
      content: "";
      top: calc(50% - 1px); }
  .editor-block-navigation__list .editor-block-navigation__list .editor-block-navigation__item-button {
    margin-left: 0.8em;
    width: calc(100% - 0.8em); }
  .editor-block-navigation__list .editor-block-navigation__list > li:last-child {
    position: relative; }
    .editor-block-navigation__list .editor-block-navigation__list > li:last-child::after {
      position: absolute;
      content: "";
      background: #fff;
      top: 19px;
      bottom: 0;
      left: -2px;
      width: 2px; }

.editor-block-navigation__item-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 6px;
  text-align: left;
  color: #40464d;
  border-radius: 4px; }
  .editor-block-navigation__item-button .editor-block-icon {
    margin-right: 6px; }
  .editor-block-navigation__item-button:hover:not(:disabled):not([aria-disabled="true"]) {
    color: #191e23;
    border: none;
    box-shadow: none; }
  .editor-block-navigation__item-button:focus:not(:disabled):not([aria-disabled="true"]) {
    color: #191e23;
    border: none;
    box-shadow: none;
    outline-offset: -2px;
    outline: 1px dotted #555d66; }
  .editor-block-navigation__item-button.is-selected, .editor-block-navigation__item-button.is-selected:focus {
    color: #32373c;
    background: #edeff0; }

.editor-block-preview {
  pointer-events: none;
  padding: 10px;
  overflow: hidden;
  display: none; }
  @media (min-width: 782px) {
    .editor-block-preview {
      display: block; } }
  .editor-block-preview .editor-block-preview__content {
    padding: 14px;
    border: 1px solid #e2e4e7;
    font-family: "Noto Serif", serif; }
    .editor-block-preview .editor-block-preview__content > div {
      transform: scale(0.9);
      transform-origin: center top;
      font-family: "Noto Serif", serif; }
    .editor-block-preview .editor-block-preview__content > div section {
      height: auto; }
    .editor-block-preview .editor-block-preview__content > .reusable-block-indicator {
      display: none; }

.editor-block-preview__title {
  margin-bottom: 10px;
  color: #6c7781; }

.editor-block-settings-menu__toggle .dashicon {
  transform: rotate(90deg); }

.editor-block-settings-menu__popover::before, .editor-block-settings-menu__popover::after {
  margin-left: 2px; }

.editor-block-settings-menu__popover .editor-block-settings-menu__content {
  padding: 7px; }

.editor-block-settings-menu__popover .editor-block-settings-menu__separator {
  margin-top: 8px;
  margin-bottom: 8px;
  margin-left: -7px;
  margin-right: -7px;
  border-top: 1px solid #e2e4e7; }
  .editor-block-settings-menu__popover .editor-block-settings-menu__separator:last-child {
    display: none; }

.editor-block-settings-menu__popover .editor-block-settings-menu__title {
  display: block;
  padding: 6px;
  color: #6c7781; }

.editor-block-settings-menu__popover .editor-block-settings-menu__control {
  width: 100%;
  justify-content: flex-start;
  padding: 8px;
  background: none;
  outline: none;
  border-radius: 0;
  color: #555d66;
  text-align: left;
  cursor: pointer;
  border: none;
  box-shadow: none; }
  .editor-block-settings-menu__popover .editor-block-settings-menu__control:hover:not(:disabled):not([aria-disabled="true"]) {
    color: #191e23;
    border: none;
    box-shadow: none; }
  .editor-block-settings-menu__popover .editor-block-settings-menu__control:focus:not(:disabled):not([aria-disabled="true"]) {
    color: #191e23;
    border: none;
    box-shadow: none;
    outline-offset: -2px;
    outline: 1px dotted #555d66; }
  .editor-block-settings-menu__popover .editor-block-settings-menu__control .dashicon {
    margin-right: 5px; }

.editor-block-styles {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between; }

.editor-block-styles__item {
  width: calc(50% - 4px);
  margin: 4px 0;
  flex-shrink: 0;
  cursor: pointer;
  overflow: hidden;
  border-radius: 4px;
  padding: 4px; }
  .editor-block-styles__item.is-active {
    color: #191e23;
    box-shadow: 0 0 0 2px #00a0d2;
    outline: 2px solid transparent;
    outline-offset: -2px;
    box-shadow: 0 0 0 2px #555d66; }
  .editor-block-styles__item:focus {
    color: #191e23;
    box-shadow: 0 0 0 2px #00a0d2;
    outline: 2px solid transparent;
    outline-offset: -2px; }
  .editor-block-styles__item:hover {
    background: #f8f9f9;
    color: #191e23; }

.editor-block-styles__item-preview {
  outline: 1px solid transparent;
  border: 1px solid rgba(25, 30, 35, 0.2);
  overflow: hidden;
  padding: 0;
  text-align: initial;
  border-radius: 4px;
  display: flex;
  height: 60px;
  background: #fff; }
  .editor-block-styles__item-preview .editor-block-preview__content {
    transform: scale(0.7);
    transform-origin: center center;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: visible;
    min-height: auto; }

.editor-block-styles__item-label {
  text-align: center;
  padding: 4px 2px; }

.editor-block-switcher {
  position: relative;
  height: 36px; }

.components-icon-button.editor-block-switcher__toggle,
.components-icon-button.editor-block-switcher__no-switcher-icon {
  margin: 0;
  display: block;
  height: 36px;
  padding: 3px; }

.components-icon-button.editor-block-switcher__no-switcher-icon {
  width: 48px; }
  .components-icon-button.editor-block-switcher__no-switcher-icon .editor-block-icon {
    margin-right: auto;
    margin-left: auto; }

.components-icon-button.editor-block-switcher__toggle {
  width: auto; }
  .components-icon-button.editor-block-switcher__toggle:active, .components-icon-button.editor-block-switcher__toggle:not(:disabled):not([aria-disabled="true"]):hover, .components-icon-button.editor-block-switcher__toggle:not([aria-disabled="true"]):focus {
    outline: none;
    box-shadow: none;
    background: none;
    border: none; }
  .components-icon-button.editor-block-switcher__toggle .editor-block-icon,
  .components-icon-button.editor-block-switcher__toggle .editor-block-switcher__transform {
    width: 42px;
    height: 30px;
    position: relative;
    margin: 0 auto;
    padding: 3px;
    display: flex;
    align-items: center;
    transition: all 0.1s cubic-bezier(0.165, 0.84, 0.44, 1); }
  .components-icon-button.editor-block-switcher__toggle .editor-block-icon::after {
    content: "";
    pointer-events: none;
    display: block;
    width: 0;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 5px solid currentColor;
    margin-left: 4px;
    margin-right: 2px; }
  .components-icon-button.editor-block-switcher__toggle .editor-block-switcher__transform {
    margin-top: 6px;
    border-radius: 4px; }
  .components-icon-button.editor-block-switcher__toggle[aria-expanded="true"] .editor-block-icon,
  .components-icon-button.editor-block-switcher__toggle[aria-expanded="true"] .editor-block-switcher__transform,
  .components-icon-button.editor-block-switcher__toggle:not(:disabled):hover .editor-block-icon,
  .components-icon-button.editor-block-switcher__toggle:not(:disabled):hover .editor-block-switcher__transform,
  .components-icon-button.editor-block-switcher__toggle:not(:disabled):focus .editor-block-icon,
  .components-icon-button.editor-block-switcher__toggle:not(:disabled):focus .editor-block-switcher__transform {
    transform: translateY(-36px); }
  .components-icon-button.editor-block-switcher__toggle:not(:disabled):focus .editor-block-icon,
  .components-icon-button.editor-block-switcher__toggle:not(:disabled):focus .editor-block-switcher__transform {
    box-shadow: inset 0 0 0 1px #555d66, inset 0 0 0 2px #fff;
    outline: 2px solid transparent;
    outline-offset: -2px; }

.components-popover:not(.is-mobile).editor-block-switcher__popover .components-popover__content {
  min-width: 300px;
  max-width: 340px; }

@media (min-width: 782px) {
  .editor-block-switcher__popover .components-popover__content {
    position: relative; }
    .editor-block-switcher__popover .components-popover__content .editor-block-preview {
      border: 1px solid #e2e4e7;
      box-shadow: 0 3px 30px rgba(25, 30, 35, 0.1);
      background: #fff;
      position: absolute;
      left: 100%;
      top: -1px;
      bottom: -1px;
      width: 300px;
      height: auto; } }

.editor-block-switcher__popover .components-popover__content .components-panel__body {
  border: 0;
  position: relative;
  z-index: 1; }

.editor-block-switcher__popover .components-popover__content .components-panel__body + .components-panel__body {
  border-top: 1px solid #e2e4e7; }

.editor-block-switcher__popover:not(.is-mobile) > .components-popover__content {
  overflow-y: visible; }

.editor-block-switcher__popover .editor-block-styles {
  margin: 0 -3px; }

.editor-block-switcher__popover .editor-block-types-list {
  margin: 8px -8px -8px; }

.editor-block-toolbar {
  display: flex;
  flex-grow: 1;
  width: 100%;
  overflow: auto;
  position: relative;
  border-left: 1px solid #e2e4e7; }
  @media (min-width: 600px) {
    .editor-block-toolbar {
      overflow: inherit; } }
  .editor-block-toolbar .components-toolbar {
    border: 0;
    border-top: 1px solid #e2e4e7;
    border-bottom: 1px solid #e2e4e7;
    border-right: 1px solid #e2e4e7; }

.editor-block-types-list {
  list-style: none;
  padding: 2px 0;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap; }

.editor-color-palette-control__color-palette {
  display: inline-block;
  margin-top: 0.6rem; }

.editor-contrast-checker > .components-notice {
  margin: 0; }

.editor-default-block-appender {
  clear: both; }
  .editor-default-block-appender textarea.editor-default-block-appender__content {
    font-family: "Noto Serif", serif;
    font-size: 16px;
    border: none;
    background: none;
    box-shadow: none;
    display: block;
    cursor: text;
    width: 100%;
    outline: 1px solid transparent;
    transition: 0.2s outline;
    resize: none;
    padding: 0 50px 0 14px;
    color: rgba(14, 28, 46, 0.62); }
    .is-dark-theme .editor-default-block-appender textarea.editor-default-block-appender__content {
      color: rgba(255, 255, 255, 0.65); }
  .editor-default-block-appender .editor-inserter-with-shortcuts {
    opacity: 0.5;
    transition: opacity 0.2s; }
    .editor-default-block-appender .editor-inserter-with-shortcuts .components-icon-button:not(:hover) {
      color: rgba(10, 24, 41, 0.7); }
      .is-dark-theme .editor-default-block-appender .editor-inserter-with-shortcuts .components-icon-button:not(:hover) {
        color: rgba(255, 255, 255, 0.75); }
  .editor-default-block-appender .editor-inserter__toggle:not([aria-expanded="true"]) {
    opacity: 0; }
  .editor-default-block-appender:hover .editor-inserter-with-shortcuts {
    opacity: 1; }
  .editor-default-block-appender:hover .editor-inserter__toggle {
    opacity: 1; }
  .editor-default-block-appender .components-drop-zone__content-icon {
    display: none; }

.editor-block-list__empty-block-inserter,
.editor-default-block-appender .editor-inserter,
.editor-inserter-with-shortcuts {
  position: absolute;
  top: 0; }
  .editor-block-list__empty-block-inserter .components-icon-button,
  .editor-default-block-appender .editor-inserter .components-icon-button,
  .editor-inserter-with-shortcuts .components-icon-button {
    width: 28px;
    height: 28px;
    margin-right: 12px;
    padding: 0; }
  .editor-block-list__empty-block-inserter .editor-block-icon,
  .editor-default-block-appender .editor-inserter .editor-block-icon,
  .editor-inserter-with-shortcuts .editor-block-icon {
    margin: auto; }
  .editor-block-list__empty-block-inserter .components-icon-button svg,
  .editor-default-block-appender .editor-inserter .components-icon-button svg,
  .editor-inserter-with-shortcuts .components-icon-button svg {
    display: block;
    margin: auto; }
  .editor-block-list__empty-block-inserter .editor-inserter__toggle,
  .editor-default-block-appender .editor-inserter .editor-inserter__toggle,
  .editor-inserter-with-shortcuts .editor-inserter__toggle {
    margin-right: 0; }

.editor-block-list__empty-block-inserter,
.editor-default-block-appender .editor-inserter {
  right: 8px; }
  @media (min-width: 600px) {
    .editor-block-list__empty-block-inserter,
    .editor-default-block-appender .editor-inserter {
      left: -44px;
      right: auto; } }
  .editor-block-list__empty-block-inserter:disabled,
  .editor-default-block-appender .editor-inserter:disabled {
    display: none; }
  .editor-block-list__empty-block-inserter .editor-inserter__toggle,
  .editor-default-block-appender .editor-inserter .editor-inserter__toggle {
    transition: opacity 0.2s;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    padding: 0; }
    .editor-block-list__empty-block-inserter .editor-inserter__toggle:not(:hover),
    .editor-default-block-appender .editor-inserter .editor-inserter__toggle:not(:hover) {
      color: rgba(10, 24, 41, 0.7); }
      .is-dark-theme .editor-block-list__empty-block-inserter .editor-inserter__toggle:not(:hover), .is-dark-theme
      .editor-default-block-appender .editor-inserter .editor-inserter__toggle:not(:hover) {
        color: rgba(255, 255, 255, 0.75); }

.editor-block-list__side-inserter .editor-inserter-with-shortcuts,
.editor-default-block-appender .editor-inserter-with-shortcuts {
  right: 14px;
  display: none;
  z-index: 5; }
  @media (min-width: 600px) {
    .editor-block-list__side-inserter .editor-inserter-with-shortcuts,
    .editor-default-block-appender .editor-inserter-with-shortcuts {
      right: 0;
      display: flex; } }

.document-outline {
  margin: 20px 0; }
  .document-outline ul {
    margin: 0;
    padding: 0; }

.document-outline__item {
  display: flex;
  margin: 4px 0; }
  .document-outline__item .document-outline__emdash::before {
    color: #e2e4e7;
    margin-right: 4px; }
  .document-outline__item.is-h2 .document-outline__emdash::before {
    content: "—"; }
  .document-outline__item.is-h3 .document-outline__emdash::before {
    content: "——"; }
  .document-outline__item.is-h4 .document-outline__emdash::before {
    content: "———"; }
  .document-outline__item.is-h5 .document-outline__emdash::before {
    content: "————"; }
  .document-outline__item.is-h6 .document-outline__emdash::before {
    content: "—————"; }

.document-outline__button {
  cursor: pointer;
  background: none;
  border: none;
  display: flex;
  align-items: flex-start;
  color: #23282d;
  text-align: left; }
  .document-outline__button:focus {
    background-color: #fff;
    color: #191e23;
    box-shadow: inset 0 0 0 1px #6c7781, inset 0 0 0 2px #fff;
    outline: 2px solid transparent;
    outline-offset: -2px; }

.document-outline__level {
  background: #e2e4e7;
  color: #23282d;
  border-radius: 3px;
  font-size: 13px;
  padding: 1px 6px;
  margin-right: 4px; }
  .is-invalid .document-outline__level {
    background: #f0b849; }

.editor-error-boundary {
  max-width: 610px;
  margin: auto;
  max-width: 780px;
  padding: 20px;
  margin-top: 60px;
  box-shadow: 0 3px 30px rgba(25, 30, 35, 0.2); }

.editor-inner-blocks.has-overlay::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 120; }

.editor-inserter-with-shortcuts {
  display: flex;
  align-items: center; }
  .editor-inserter-with-shortcuts .components-icon-button {
    border-radius: 4px; }
    .editor-inserter-with-shortcuts .components-icon-button svg:not(.dashicon) {
      height: 24px;
      width: 24px; }

.editor-inserter-with-shortcuts__block {
  margin-right: 4px;
  width: 36px;
  height: 36px;
  padding-top: 8px;
  color: rgba(102, 120, 134, 0.35); }
  .is-dark-theme .editor-inserter-with-shortcuts__block {
    color: rgba(255, 255, 255, 0.4); }

.editor-inserter {
  display: inline-block;
  background: none;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  line-height: 1.4; }
  @media (min-width: 782px) {
    .editor-inserter {
      position: relative; } }

@media (min-width: 782px) {
  .editor-inserter__popover:not(.is-mobile) > .components-popover__content {
    overflow-y: visible;
    height: 432px; } }

.editor-inserter__toggle {
  display: inline-flex;
  align-items: center;
  color: #555d66;
  background: none;
  cursor: pointer;
  border: none;
  outline: none;
  transition: color 0.2s ease; }

.editor-inserter__menu {
  width: auto;
  display: flex;
  flex-direction: column;
  height: 100%; }
  @media (min-width: 782px) {
    .editor-inserter__menu {
      width: 400px;
      position: relative; }
      .editor-inserter__menu .editor-block-preview {
        border: 1px solid #e2e4e7;
        box-shadow: 0 3px 30px rgba(25, 30, 35, 0.1);
        background: #fff;
        position: absolute;
        left: 100%;
        top: -1px;
        bottom: -1px;
        width: 300px; } }

.editor-inserter__inline-elements {
  margin-top: -1px; }

.editor-inserter__menu.is-bottom::after {
  border-bottom-color: #fff; }

.components-popover input[type="search"].editor-inserter__search {
  display: block;
  margin: 16px;
  padding: 11px 16px;
  position: relative;
  z-index: 1;
  border-radius: 4px;
  /* Fonts smaller than 16px causes mobile safari to zoom. */
  font-size: 16px; }
  @media (min-width: 600px) {
    .components-popover input[type="search"].editor-inserter__search {
      font-size: 13px; } }
  .components-popover input[type="search"].editor-inserter__search:focus {
    color: #191e23;
    border-color: #00a0d2;
    box-shadow: 0 0 0 1px #00a0d2;
    outline: 2px solid transparent;
    outline-offset: -2px; }

.editor-inserter__results {
  flex-grow: 1;
  overflow: auto;
  position: relative;
  z-index: 1;
  padding: 0 16px 16px 16px; }
  .editor-inserter__results:focus {
    outline: 1px dotted #555d66; }
  @media (min-width: 782px) {
    .editor-inserter__results {
      height: 394px; } }
  .editor-inserter__results [role="presentation"] + .components-panel__body {
    border-top: none; }

.editor-inserter__popover .editor-block-types-list {
  margin: 0 -8px; }

.editor-inserter__reusable-blocks-panel {
  position: relative;
  text-align: right; }

.editor-inserter__manage-reusable-blocks {
  margin: 16px 0 0 16px; }

.editor-inserter__no-results {
  font-style: italic;
  padding: 24px;
  text-align: center; }

.editor-inserter__child-blocks {
  padding: 0 16px; }

.editor-inserter__parent-block-header {
  display: flex;
  align-items: center; }
  .editor-inserter__parent-block-header h2 {
    font-size: 13px; }

.editor-block-types-list__list-item {
  display: block;
  width: 33.33%;
  padding: 0 4px;
  margin: 0 0 12px; }

.editor-block-types-list__item {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 13px;
  color: #32373c;
  padding: 0;
  align-items: stretch;
  justify-content: center;
  cursor: pointer;
  background: transparent;
  word-break: break-word;
  border-radius: 4px;
  border: 1px solid transparent;
  transition: all 0.05s ease-in-out;
  position: relative; }
  .editor-block-types-list__item:disabled {
    opacity: 0.6;
    cursor: default; }
  .editor-block-types-list__item:not(:disabled):hover::before {
    content: "";
    display: block;
    background: #f8f9f9;
    color: #191e23;
    position: absolute;
    z-index: -1;
    border-radius: 4px;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0; }
  .editor-block-types-list__item:not(:disabled):hover .editor-block-types-list__item-icon,
  .editor-block-types-list__item:not(:disabled):hover .editor-block-types-list__item-title {
    color: currentColor; }
  .editor-block-types-list__item:not(:disabled):active, .editor-block-types-list__item:not(:disabled).is-active, .editor-block-types-list__item:not(:disabled):focus {
    position: relative;
    outline: none;
    color: #191e23;
    box-shadow: 0 0 0 2px #00a0d2;
    outline: 2px solid transparent;
    outline-offset: -2px; }
    .editor-block-types-list__item:not(:disabled):active .editor-block-types-list__item-icon,
    .editor-block-types-list__item:not(:disabled):active .editor-block-types-list__item-title, .editor-block-types-list__item:not(:disabled).is-active .editor-block-types-list__item-icon,
    .editor-block-types-list__item:not(:disabled).is-active .editor-block-types-list__item-title, .editor-block-types-list__item:not(:disabled):focus .editor-block-types-list__item-icon,
    .editor-block-types-list__item:not(:disabled):focus .editor-block-types-list__item-title {
      color: currentColor; }

.editor-block-types-list__item-icon {
  padding: 12px 20px;
  border-radius: 4px;
  color: #555d66;
  transition: all 0.05s ease-in-out; }
  .editor-block-types-list__item-icon .editor-block-icon {
    margin-left: auto;
    margin-right: auto; }
  .editor-block-types-list__item-icon svg {
    transition: all 0.15s ease-out; }

.editor-block-types-list__item-title {
  padding: 4px 2px 8px; }

.editor-block-types-list__item-has-children .editor-block-types-list__item-icon {
  background: #fff;
  margin-right: 3px;
  margin-bottom: 6px;
  padding: 9px 20px 9px;
  position: relative;
  top: -2px;
  left: -2px;
  box-shadow: 0 0 0 1px #e2e4e7; }

.editor-block-types-list__item-has-children .editor-block-types-list__item-icon-stack {
  display: block;
  background: #fff;
  box-shadow: 0 0 0 1px #e2e4e7;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -1;
  bottom: -6px;
  right: -6px;
  border-radius: 4px; }

.editor-media-placeholder__url-input-container {
  width: 100%; }
  .editor-media-placeholder__url-input-container .editor-media-placeholder__button {
    margin-bottom: 0; }

.editor-media-placeholder__url-input-form {
  display: flex; }
  .editor-media-placeholder__url-input-form input[type="url"].editor-media-placeholder__url-input-field {
    width: 100%;
    flex-grow: 1;
    border: none;
    border-radius: 0;
    margin: 2px; }
    @media (min-width: 600px) {
      .editor-media-placeholder__url-input-form input[type="url"].editor-media-placeholder__url-input-field {
        width: 300px; } }

.editor-media-placeholder__url-input-submit-button {
  flex-shrink: 1; }

.editor-media-placeholder__button {
  margin-bottom: 0.5rem; }
  .editor-media-placeholder__button .dashicon {
    vertical-align: middle;
    margin-bottom: 3px; }
  .editor-media-placeholder__button:hover {
    color: #23282d; }

.components-form-file-upload .editor-media-placeholder__button {
  margin-right: 4px; }

.editor-multi-selection-inspector__card {
  display: flex;
  align-items: flex-start;
  margin: -16px;
  padding: 16px; }

.editor-multi-selection-inspector__card-content {
  flex-grow: 1; }

.editor-multi-selection-inspector__card-title {
  font-weight: 500;
  margin-bottom: 5px; }

.editor-multi-selection-inspector__card-description {
  font-size: 13px; }

.editor-multi-selection-inspector__card .editor-block-icon {
  margin-left: -2px;
  margin-right: 10px;
  padding: 0 3px;
  width: 36px;
  height: 24px; }

.editor-page-attributes__template {
  margin-bottom: 10px; }
  .editor-page-attributes__template label,
  .editor-page-attributes__template select {
    width: 100%; }

.editor-page-attributes__order {
  width: 100%; }
  .editor-page-attributes__order .components-base-control__field {
    display: flex;
    justify-content: space-between;
    align-items: center; }
  .editor-page-attributes__order input {
    width: 66px; }

.editor-panel-color-settings .component-color-indicator {
  vertical-align: text-bottom; }

.editor-panel-color-settings__panel-title .component-color-indicator {
  display: inline-block; }

.editor-panel-color-settings.is-opened .editor-panel-color-settings__panel-title .component-color-indicator {
  display: none; }

.block-editor .editor-plain-text {
  box-shadow: none;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  line-height: inherit;
  border: none;
  padding: 0;
  margin: 0;
  width: 100%; }

.editor-post-excerpt__textarea {
  width: 100%;
  margin-bottom: 10px; }

.editor-post-featured-image {
  padding: 0; }
  .editor-post-featured-image .components-spinner {
    margin: 0; }
  .editor-post-featured-image .components-button + .components-button {
    margin-top: 1em;
    margin-right: 8px; }
  .editor-post-featured-image .components-responsive-wrapper__content {
    max-width: 100%;
    width: auto; }

.editor-post-featured-image__toggle,
.editor-post-featured-image__preview {
  display: block;
  width: 100%;
  padding: 0;
  transition: all 0.1s ease-out;
  box-shadow: 0 0 0 0 #00a0d2; }

.editor-post-featured-image__preview:not(:disabled):not([aria-disabled="true"]):focus {
  box-shadow: 0 0 0 4px #00a0d2; }

.editor-post-featured-image__toggle {
  border: 1px dashed #a2aab2;
  background-color: #edeff0;
  line-height: 20px;
  padding: 8px 0;
  text-align: center; }
  .editor-post-featured-image__toggle:hover {
    background-color: #f8f9f9; }

.editor-post-format {
  flex-direction: column;
  align-items: stretch;
  width: 100%; }

.editor-post-format__content {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  width: 100%; }

.editor-post-format__suggestion {
  text-align: right;
  font-size: 13px; }

.editor-post-last-revision__title {
  width: 100%;
  font-weight: 600; }
  .editor-post-last-revision__title .dashicon {
    margin-right: 5px; }

.components-icon-button:not(:disabled):not([aria-disabled="true"]).editor-post-last-revision__title:hover, .components-icon-button:not(:disabled):not([aria-disabled="true"]).editor-post-last-revision__title:active {
  border: none;
  box-shadow: none; }

.components-icon-button:not(:disabled):not([aria-disabled="true"]).editor-post-last-revision__title:focus {
  color: #191e23;
  border: none;
  box-shadow: none;
  outline-offset: -2px;
  outline: 1px dotted #555d66; }

.editor-post-locked-modal {
  height: auto;
  padding-right: 10px;
  padding-left: 10px;
  padding-top: 10px;
  max-width: 480px; }
  .editor-post-locked-modal .components-modal__header {
    height: 36px; }
  .editor-post-locked-modal .components-modal__content {
    height: auto; }

.editor-post-locked-modal__buttons {
  margin-top: 10px; }
  .editor-post-locked-modal__buttons .components-button {
    margin-right: 5px; }

.editor-post-locked-modal__avatar {
  float: left;
  margin: 5px;
  margin-right: 15px; }

.editor-post-permalink {
  display: inline-flex;
  align-items: center;
  background: #fff;
  padding: 5px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  height: 40px;
  white-space: nowrap;
  border: 1px solid rgba(145, 151, 162, 0.25);
  background-clip: padding-box;
  margin-left: -15px;
  margin-right: -15px; }
  @media (min-width: 600px) {
    .editor-post-permalink {
      margin-left: -1px;
      margin-right: -1px; } }
  .editor-post-permalink button {
    flex-shrink: 0; }

.editor-post-permalink__copy {
  border-radius: 4px;
  padding: 6px; }

.editor-post-permalink__copy.is-copied {
  opacity: 0.3; }

.editor-post-permalink__label {
  margin: 0 10px 0 5px;
  font-weight: 600; }

.editor-post-permalink__link {
  color: #7e8993;
  text-decoration: underline;
  margin-right: 10px;
  width: 100%;
  overflow: hidden;
  position: relative;
  white-space: nowrap; }
  .editor-post-permalink__link::after {
    content: "";
    display: block;
    position: absolute;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), #fff 90%);
    top: 1px;
    bottom: 1px;
    right: 1px;
    left: auto;
    width: 20%;
    height: auto; }

.editor-post-permalink-editor {
  width: 100%;
  min-width: 20%;
  display: inline-flex;
  align-items: center; }
  .editor-post-permalink-editor .editor-post-permalink__editor-container {
    flex: 0 1 100%;
    display: flex;
    overflow: hidden;
    padding: 1px 0; }
    .editor-post-permalink-editor .editor-post-permalink__editor-container .editor-post-permalink-editor__prefix {
      flex: 1 1 auto; }
      @media (min-width: 600px) {
        .editor-post-permalink-editor .editor-post-permalink__editor-container .editor-post-permalink-editor__prefix {
          flex: 1 0 auto; } }
    .editor-post-permalink-editor .editor-post-permalink__editor-container .editor-post-permalink-editor__edit {
      flex: 1 1 100%; }
  .editor-post-permalink-editor .editor-post-permalink-editor__save {
    margin-left: auto; }

.editor-post-permalink-editor__prefix {
  color: #6c7781;
  min-width: 20%;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis; }

.editor-post-permalink input[type="text"].editor-post-permalink-editor__edit {
  min-width: 10%;
  width: 100%;
  margin: 0 3px;
  padding: 2px 4px; }

.editor-post-permalink-editor__suffix {
  color: #6c7781;
  margin-right: 6px;
  flex: 0 0 0%; }

.editor-post-publish-panel {
  background: #fff;
  color: #555d66; }

.editor-post-publish-panel__content {
  min-height: calc(100% - 140px); }
  .editor-post-publish-panel__content .components-spinner {
    display: block;
    float: none;
    margin: 100px auto 0; }

.editor-post-publish-panel__header {
  background: #fff;
  padding-left: 16px;
  height: 56px;
  border-bottom: 1px solid #e2e4e7;
  display: flex;
  align-items: center;
  align-content: space-between; }

.editor-post-publish-panel__header-publish-button {
  display: flex;
  justify-content: flex-end;
  flex-grow: 1;
  text-align: right;
  flex-wrap: nowrap; }

.editor-post-publish-panel__header-published {
  flex-grow: 1; }

.editor-post-publish-panel__footer {
  padding: 16px; }

.components-button.editor-post-publish-panel__toggle.is-primary {
  display: inline-flex;
  align-items: center; }
  .components-button.editor-post-publish-panel__toggle.is-primary.is-busy .dashicon {
    display: none; }
  .components-button.editor-post-publish-panel__toggle.is-primary .dashicon {
    margin-right: -4px; }

.editor-post-publish-panel__link {
  color: #007fac;
  font-weight: 400;
  padding-left: 4px;
  text-decoration: underline; }

.editor-post-publish-panel__prepublish {
  padding: 16px; }
  .editor-post-publish-panel__prepublish strong {
    color: #191e23; }
  .editor-post-publish-panel__prepublish .components-panel__body {
    background: #fff;
    margin-left: -16px;
    margin-right: -16px; }
  .editor-post-publish-panel__prepublish .editor-post-visibility__dialog-legend {
    display: none; }

.post-publish-panel__postpublish .components-panel__body {
  border-bottom: 1px solid #e2e4e7;
  border-top: none; }

.post-publish-panel__postpublish-buttons {
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
  margin: -5px; }
  .post-publish-panel__postpublish-buttons > * {
    flex-grow: 1;
    margin: 5px; }
  .post-publish-panel__postpublish-buttons .components-button {
    height: auto;
    justify-content: center;
    padding: 3px 10px 4px;
    line-height: 1.6;
    text-align: center;
    white-space: normal; }
  .post-publish-panel__postpublish-buttons .components-clipboard-button {
    width: 100%; }

.post-publish-panel__postpublish-post-address {
  margin-bottom: 16px; }
  .post-publish-panel__postpublish-post-address input[readonly] {
    padding: 10px;
    background: #e8eaeb;
    overflow: hidden;
    text-overflow: ellipsis; }

.post-publish-panel__postpublish-header {
  font-weight: 500; }

.post-publish-panel__postpublish-subheader {
  margin: 0 0 8px; }

.post-publish-panel__tip {
  color: #f0b849; }

.editor-post-saved-state {
  display: flex;
  align-items: center;
  color: #a2aab2;
  overflow: hidden; }
  .editor-post-saved-state.is-saving {
    animation: edit-post__loading-fade-animation 0.5s infinite; }
  .editor-post-saved-state .dashicon {
    display: inline-block;
    flex: 0 0 auto; }

.editor-post-saved-state {
  width: 28px;
  white-space: nowrap;
  padding: 12px 4px; }
  .editor-post-saved-state .dashicon {
    margin-right: 8px; }
  @media (min-width: 600px) {
    .editor-post-saved-state {
      width: auto;
      padding: 8px 12px;
      text-indent: inherit; }
      .editor-post-saved-state .dashicon {
        margin-right: 4px; } }

.edit-post-header .edit-post-header__settings .components-button.editor-post-save-draft {
  margin: 0; }
  @media (min-width: 600px) {
    .edit-post-header .edit-post-header__settings .components-button.editor-post-save-draft .dashicon {
      display: none; } }

.editor-post-taxonomies__hierarchical-terms-list {
  max-height: 14em;
  overflow: auto; }

.editor-post-taxonomies__hierarchical-terms-choice {
  margin-bottom: 8px; }

.editor-post-taxonomies__hierarchical-terms-input[type="checkbox"] {
  margin-top: 0; }

.editor-post-taxonomies__hierarchical-terms-subchoices {
  margin-top: 8px;
  margin-left: 16px; }

.components-button.editor-post-taxonomies__hierarchical-terms-submit,
.components-button.editor-post-taxonomies__hierarchical-terms-add {
  margin-top: 12px; }

.editor-post-taxonomies__hierarchical-terms-label {
  display: inline-block;
  margin-top: 12px; }

.editor-post-taxonomies__hierarchical-terms-input {
  margin-top: 8px;
  width: 100%; }

.editor-post-taxonomies__hierarchical-terms-filter {
  margin-bottom: 8px;
  width: 100%; }

.editor-post-text-editor {
  border: 1px solid #e2e4e7;
  display: block;
  margin: 0 0 2em;
  width: 100%;
  box-shadow: none;
  resize: none;
  overflow: hidden;
  font-family: Menlo, Consolas, monaco, monospace;
  font-size: 14px;
  line-height: 150%; }
  .editor-post-text-editor:hover, .editor-post-text-editor:focus {
    border: 1px solid #e2e4e7;
    box-shadow: none;
    outline: 1px solid #e2e4e7;
    outline-offset: -2px; }

.editor-post-text-editor__toolbar {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap; }
  .editor-post-text-editor__toolbar button {
    height: 30px;
    background: none;
    padding: 0 8px;
    margin: 3px 4px;
    text-align: center;
    cursor: pointer;
    font-family: Menlo, Consolas, monaco, monospace;
    color: #555d66;
    border: 1px solid transparent; }
    .editor-post-text-editor__toolbar button:first-child {
      margin-left: 0; }
    .editor-post-text-editor__toolbar button:hover, .editor-post-text-editor__toolbar button:focus {
      outline: none;
      border: 1px solid #555d66; }

.editor-post-text-editor__bold {
  font-weight: 600; }

.editor-post-text-editor__italic {
  font-style: italic; }

.editor-post-text-editor__link {
  text-decoration: underline;
  color: #0085ba; }

body.admin-color-sunrise .editor-post-text-editor__link{
  color: #d1864a; }

body.admin-color-ocean .editor-post-text-editor__link{
  color: #a3b9a2; }

body.admin-color-midnight .editor-post-text-editor__link{
  color: #e14d43; }

body.admin-color-ectoplasm .editor-post-text-editor__link{
  color: #a7b656; }

body.admin-color-coffee .editor-post-text-editor__link{
  color: #c2a68c; }

body.admin-color-blue .editor-post-text-editor__link{
  color: #82b4cb; }

body.admin-color-light .editor-post-text-editor__link{
  color: #0085ba; }

.editor-post-text-editor__del {
  text-decoration: line-through; }

.edit-post-post-visibility__dialog .editor-post-visibility__dialog-fieldset {
  padding: 4px;
  padding-top: 0; }

.edit-post-post-visibility__dialog .editor-post-visibility__dialog-legend {
  font-weight: 600;
  margin-bottom: 1em;
  margin-top: 0.5em;
  padding: 0; }

.edit-post-post-visibility__dialog .editor-post-visibility__dialog-radio {
  margin-top: 2px; }

.edit-post-post-visibility__dialog .editor-post-visibility__dialog-label {
  font-weight: 600; }

.edit-post-post-visibility__dialog .editor-post-visibility__dialog-info {
  margin-top: 0;
  margin-left: 28px; }

.edit-post-post-visibility__dialog .editor-post-visibility__choice:last-child .editor-post-visibility__dialog-info {
  margin-bottom: 0; }

.edit-post-post-visibility__dialog .editor-post-visibility__dialog-password-input {
  margin-left: 28px; }

.edit-post-post-visibility__dialog.components-popover.is-bottom {
  z-index: 100001; }

.editor-post-title__block {
  position: relative;
  padding: 5px 0;
  font-size: 16px; }
  @media (min-width: 600px) {
    .editor-post-title__block {
      padding: 5px 2px; } }
  .editor-post-title__block .editor-post-title__input {
    display: block;
    width: 100%;
    margin: 0;
    box-shadow: none;
    background: transparent;
    font-family: "Noto Serif", serif;
    line-height: 1.4;
    color: #191e23;
    transition: border 0.1s ease-out;
    padding: 19px 14px;
    word-break: keep-all;
    border: 1px solid transparent;
    border-left-width: 0;
    border-right-width: 0;
    font-size: 2.441em;
    font-weight: 600; }
    @media (min-width: 600px) {
      .editor-post-title__block .editor-post-title__input {
        border-width: 1px; } }
    .editor-post-title__block .editor-post-title__input::-webkit-input-placeholder {
      color: rgba(22, 36, 53, 0.55); }
    .editor-post-title__block .editor-post-title__input::-moz-placeholder {
      color: rgba(22, 36, 53, 0.55); }
    .editor-post-title__block .editor-post-title__input:-ms-input-placeholder {
      color: rgba(22, 36, 53, 0.55); }
  .editor-post-title__block:not(.is-focus-mode).is-selected .editor-post-title__input {
    border-color: rgba(145, 151, 162, 0.25); }
    .is-dark-theme .editor-post-title__block:not(.is-focus-mode).is-selected .editor-post-title__input {
      border-color: rgba(255, 255, 255, 0.3); }
  .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover {
    border-color: #007cba; }
  body.admin-color-sunrise .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover{
    border-color: #837425; }
  body.admin-color-ocean .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover{
    border-color: #5e7d5e; }
  body.admin-color-midnight .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover{
    border-color: #497b8d; }
  body.admin-color-ectoplasm .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover{
    border-color: #523f6d; }
  body.admin-color-coffee .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover{
    border-color: #59524c; }
  body.admin-color-blue .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover{
    border-color: #417e9B; }
  body.admin-color-light .editor-post-title__block:not(.is-focus-mode):not(.has-fixed-toolbar) .editor-post-title__input:hover{
    border-color: #007cba; }
  .editor-post-title__block.is-focus-mode .editor-post-title__input {
    opacity: 0.5;
    transition: opacity 0.1s linear; }
    .editor-post-title__block.is-focus-mode .editor-post-title__input:focus {
      opacity: 1; }

.editor-post-title .editor-post-permalink {
  font-size: 13px;
  color: #191e23;
  position: absolute;
  top: -34px;
  left: 0;
  right: 0; }
  @media (min-width: 600px) {
    .editor-post-title .editor-post-permalink {
      left: 2px;
      right: 2px; } }

.editor-post-trash.components-button {
  width: 100%;
  color: #c92c2c;
  justify-content: center; }
  .editor-post-trash.components-button:hover, .editor-post-trash.components-button:focus {
    color: #b52727; }

.editor-format-toolbar {
  display: flex;
  flex-shrink: 0; }

.editor-format-toolbar__selection-position {
  position: absolute;
  transform: translateX(-50%); }

.editor-rich-text {
  position: relative; }

.editor-rich-text__tinymce {
  margin: 0;
  position: relative;
  line-height: 1.8;
  white-space: pre-wrap; }
  .editor-rich-text__tinymce > p:empty {
    min-height: 28.8px; }
  .editor-rich-text__tinymce > p:first-child {
    margin-top: 0; }
  .editor-rich-text__tinymce:focus {
    outline: none; }
  .editor-rich-text__tinymce a {
    color: #007fac; }
  .editor-rich-text__tinymce code {
    padding: 2px;
    border-radius: 2px;
    color: #23282d;
    background: #f3f4f5;
    font-family: Menlo, Consolas, monaco, monospace;
    font-size: inherit; }
    .is-multi-selected .editor-rich-text__tinymce code {
      background: #67cffd; }
  .editor-rich-text__tinymce:focus a[data-mce-selected],
  .editor-rich-text__tinymce:focus b[data-mce-selected],
  .editor-rich-text__tinymce:focus i[data-mce-selected],
  .editor-rich-text__tinymce:focus strong[data-mce-selected],
  .editor-rich-text__tinymce:focus em[data-mce-selected],
  .editor-rich-text__tinymce:focus del[data-mce-selected],
  .editor-rich-text__tinymce:focus ins[data-mce-selected],
  .editor-rich-text__tinymce:focus sup[data-mce-selected],
  .editor-rich-text__tinymce:focus sub[data-mce-selected] {
    padding: 0 2px;
    margin: 0 -2px;
    border-radius: 2px;
    box-shadow: 0 0 0 1px #e8eaeb;
    background: #e8eaeb;
    color: #191e23; }
  .editor-rich-text__tinymce:focus a[data-mce-selected] {
    box-shadow: 0 0 0 1px #e5f5fa;
    background: #e5f5fa;
    color: #006589; }
  .editor-rich-text__tinymce:focus code[data-mce-selected] {
    background: #e8eaeb;
    box-shadow: 0 0 0 1px #e8eaeb; }
  .editor-rich-text__tinymce img[data-mce-selected] {
    outline: none; }
  .editor-rich-text__tinymce img::selection {
    background: none !important; }
  .editor-rich-text__tinymce[data-is-placeholder-visible="true"] {
    position: absolute;
    top: 0;
    width: 100%;
    margin-top: 0;
    height: 100%; }
    .editor-rich-text__tinymce[data-is-placeholder-visible="true"] > p {
      margin-top: 0; }
  .editor-rich-text__tinymce + .editor-rich-text__tinymce {
    pointer-events: none; }
    .editor-rich-text__tinymce + .editor-rich-text__tinymce,
    .editor-rich-text__tinymce + .editor-rich-text__tinymce p {
      opacity: 0.62; }
  .editor-rich-text__tinymce[data-is-placeholder-visible="true"] + figcaption.editor-rich-text__tinymce {
    opacity: 0.8; }

.editor-rich-text__inline-toolbar {
  display: flex;
  justify-content: center;
  position: absolute;
  top: -40px;
  line-height: 0;
  left: 0;
  right: 0;
  z-index: 1; }
  .editor-rich-text__inline-toolbar ul.components-toolbar {
    box-shadow: 0 2px 10px rgba(25, 30, 35, 0.1), 0 0 2px rgba(25, 30, 35, 0.1); }

.editor-skip-to-selected-block {
  position: absolute;
  top: -9999em; }
  .editor-skip-to-selected-block:focus {
    height: auto;
    width: auto;
    display: block;
    font-size: 14px;
    font-weight: 600;
    padding: 15px 23px 14px;
    background: #f1f1f1;
    color: #11a0d2;
    line-height: normal;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    text-decoration: none;
    outline: none;
    z-index: 100000; }
  body.admin-color-sunrise .editor-skip-to-selected-block:focus{
    color: #c8b03c; }
  body.admin-color-ocean .editor-skip-to-selected-block:focus{
    color: #a89d8a; }
  body.admin-color-midnight .editor-skip-to-selected-block:focus{
    color: #77a6b9; }
  body.admin-color-ectoplasm .editor-skip-to-selected-block:focus{
    color: #c77430; }
  body.admin-color-coffee .editor-skip-to-selected-block:focus{
    color: #9fa47b; }
  body.admin-color-blue .editor-skip-to-selected-block:focus{
    color: #d9ab59; }
  body.admin-color-light .editor-skip-to-selected-block:focus{
    color: #c75726; }

.table-of-contents__popover.components-popover:not(.is-mobile) .components-popover__content {
  min-width: 380px; }

.table-of-contents__popover .components-popover__content {
  padding: 16px; }
  @media (min-width: 600px) {
    .table-of-contents__popover .components-popover__content {
      max-height: calc(100vh - 120px);
      overflow-y: auto; } }

.table-of-contents__popover hr {
  margin: 10px -16px 0; }

.table-of-contents__counts {
  display: flex;
  flex-wrap: wrap; }

.table-of-contents__count {
  width: 25%;
  display: flex;
  flex-direction: column;
  font-size: 13px;
  color: #6c7781; }

.table-of-contents__number,
.table-of-contents__popover .word-count {
  font-size: 21px;
  font-weight: 400;
  line-height: 30px;
  color: #555d66; }

.table-of-contents__title {
  display: block;
  margin-top: 20px;
  font-size: 15px;
  font-weight: 600; }

.editor-template-validation-notice {
  display: flex;
  justify-content: space-between;
  align-items: center; }
  .editor-template-validation-notice .components-button {
    margin-left: 5px; }

.editor-block-list__block .editor-url-input,
.components-popover .editor-url-input,
.editor-url-input {
  flex-grow: 1;
  position: relative;
  padding: 1px; }
  .editor-block-list__block .editor-url-input input[type="text"],
  .components-popover .editor-url-input input[type="text"],
  .editor-url-input input[type="text"] {
    width: 100%;
    padding: 8px;
    border: none;
    border-radius: 0;
    margin-left: 0;
    margin-right: 0; }
    @media (min-width: 600px) {
      .editor-block-list__block .editor-url-input input[type="text"],
      .components-popover .editor-url-input input[type="text"],
      .editor-url-input input[type="text"] {
        width: 300px; } }
    .editor-block-list__block .editor-url-input input[type="text"]::-ms-clear,
    .components-popover .editor-url-input input[type="text"]::-ms-clear,
    .editor-url-input input[type="text"]::-ms-clear {
      display: none; }
  .editor-block-list__block .editor-url-input .components-spinner,
  .components-popover .editor-url-input .components-spinner,
  .editor-url-input .components-spinner {
    position: absolute;
    right: 8px;
    top: 9px;
    margin: 0; }

.editor-url-input__suggestions {
  max-height: 200px;
  transition: all 0.15s ease-in-out;
  padding: 4px 0;
  width: 302px;
  overflow-y: auto; }

.editor-url-input__suggestions,
.editor-url-input .components-spinner {
  display: none; }
  @media (min-width: 600px) {
    .editor-url-input__suggestions,
    .editor-url-input .components-spinner {
      display: inherit; } }

.editor-url-input__suggestion {
  padding: 4px 8px;
  color: #6c7781;
  display: block;
  font-size: 13px;
  cursor: pointer;
  background: #fff;
  width: 100%;
  border: none;
  text-align: left;
  border: none;
  box-shadow: none; }
  .editor-url-input__suggestion:hover {
    background: #e2e4e7; }
  .editor-url-input__suggestion:focus, .editor-url-input__suggestion.is-selected {
    background: rgb(0, 113, 158);
    color: #fff;
    outline: none; }
  body.admin-color-sunrise .editor-url-input__suggestion:focus, body.admin-color-sunrise .editor-url-input__suggestion.is-selected{
    background: rgb(178, 114, 63); }
  body.admin-color-ocean .editor-url-input__suggestion:focus, body.admin-color-ocean .editor-url-input__suggestion.is-selected{
    background: rgb(139, 157, 138); }
  body.admin-color-midnight .editor-url-input__suggestion:focus, body.admin-color-midnight .editor-url-input__suggestion.is-selected{
    background: rgb(191, 65, 57); }
  body.admin-color-ectoplasm .editor-url-input__suggestion:focus, body.admin-color-ectoplasm .editor-url-input__suggestion.is-selected{
    background: rgb(142, 155, 73); }
  body.admin-color-coffee .editor-url-input__suggestion:focus, body.admin-color-coffee .editor-url-input__suggestion.is-selected{
    background: rgb(165, 141, 119); }
  body.admin-color-blue .editor-url-input__suggestion:focus, body.admin-color-blue .editor-url-input__suggestion.is-selected{
    background: rgb(111, 153, 173); }
  body.admin-color-light .editor-url-input__suggestion:focus, body.admin-color-light .editor-url-input__suggestion.is-selected{
    background: rgb(0, 113, 158); }

.components-toolbar > .editor-url-input__button {
  position: inherit; }

.editor-url-input__button .editor-url-input__back {
  margin-right: 4px;
  overflow: visible; }
  .editor-url-input__button .editor-url-input__back::after {
    content: "";
    position: absolute;
    display: block;
    width: 1px;
    height: 24px;
    right: -1px;
    background: #e2e4e7; }

.editor-url-input__button-modal {
  box-shadow: 0 3px 30px rgba(25, 30, 35, 0.1);
  border: 1px solid #e2e4e7;
  background: #fff; }

.editor-url-input__button-modal-line {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
  align-items: flex-start; }
  .editor-url-input__button-modal-line .components-button {
    flex-shrink: 0;
    width: 36px;
    height: 36px; }

.editor-url-popover__row {
  display: flex; }

.editor-url-popover__row > :not(.editor-url-popover__settings-toggle) {
  flex-grow: 1; }

.editor-url-popover__settings-toggle {
  flex-shrink: 0;
  width: 36px;
  height: 36px; }
  .editor-url-popover__settings-toggle .dashicon {
    transform: rotate(90deg); }

.editor-url-popover__settings {
  padding: 7px 8px;
  border-top: 1px solid #e2e4e7;
  padding-top: 8px; }

.editor-warning {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: nowrap;
  background-color: #fff;
  border: 1px solid #e2e4e7;
  text-align: left;
  padding: 20px; }
  .has-warning.is-multi-selected .editor-warning {
    background-color: transparent; }
  .editor-warning .editor-warning__message {
    line-height: 1.4;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-size: 13px; }
  .editor-warning .editor-warning__contents {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    width: 100%; }
  .editor-warning .editor-warning__actions {
    display: flex; }
  .editor-warning .editor-warning__action {
    margin: 0 6px 0 0; }

.editor-warning__secondary {
  margin: 3px 0 0 -4px; }
  .editor-warning__secondary .components-icon-button {
    width: auto;
    padding: 8px 2px; }
  @media (min-width: 600px) {
    .editor-warning__secondary {
      margin-left: 4px; }
      .editor-warning__secondary .components-icon-button {
        padding: 8px 4px; } }
  .editor-warning__secondary .components-button svg {
    transform: rotate(90deg); }

.editor-writing-flow {
  height: 100%;
  display: flex;
  flex-direction: column; }

.editor-writing-flow__click-redirect {
  flex-basis: 100%;
  cursor: text; }
