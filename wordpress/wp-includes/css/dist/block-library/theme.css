/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * Often re-used variables
 */
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * <PERSON><PERSON> states and focus styles
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Applies editor right position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
.wp-block-code {
  font-family: Menlo, Consolas, monaco, monospace;
  font-size: 14px;
  color: #23282d;
  padding: 0.8em 1em;
  border: 1px solid #e2e4e7;
  border-radius: 4px; }

.wp-block-preformatted pre {
  font-family: Menlo, Consolas, monaco, monospace;
  font-size: 14px;
  color: #23282d; }

.wp-block-pullquote {
  border-top: 4px solid #555d66;
  border-bottom: 4px solid #555d66;
  color: #40464d; }
  .wp-block-pullquote cite,
  .wp-block-pullquote footer, .wp-block-pullquote__citation {
    color: #40464d;
    text-transform: uppercase;
    font-size: 13px;
    font-style: normal; }

.wp-block-quote {
  margin: 20px 0; }
  .wp-block-quote cite,
  .wp-block-quote footer, .wp-block-quote__citation {
    color: #6c7781;
    font-size: 13px;
    margin-top: 1em;
    position: relative;
    font-style: normal; }

.wp-block-quote:not(.is-large):not(.is-style-large) {
  border-left: 4px solid #000;
  padding-left: 1em; }

.wp-block-separator {
  border: none;
  border-bottom: 2px solid #8f98a1;
  margin: 1.65em auto; }
  .wp-block-separator:not(.is-style-wide):not(.is-style-dots) {
    max-width: 100px; }

.wp-block-table {
  width: 100%;
  min-width: 240px;
  border-collapse: collapse; }
  .wp-block-table td,
  .wp-block-table th {
    padding: 0.5em;
    border: 1px solid currentColor;
    word-break: break-all; }
