/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * Often re-used variables
 */
/**
 * Breakpoint mixins
 */
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * <PERSON><PERSON> states and focus styles
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Applies editor right position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
body.js.is-fullscreen-mode {
  margin-top: -46px;
  height: calc(100% + 46px);
  animation: edit-post__fade-in-animation 0.3s ease-out 0s;
  animation-fill-mode: forwards; }
  @media (min-width: 782px) {
    body.js.is-fullscreen-mode {
      margin-top: -32px;
      height: calc(100% + 32px); } }
  body.js.is-fullscreen-mode #adminmenumain,
  body.js.is-fullscreen-mode #wpadminbar {
    display: none; }
  body.js.is-fullscreen-mode #wpcontent,
  body.js.is-fullscreen-mode #wpfooter {
    margin-right: 0; }
  body.js.is-fullscreen-mode .edit-post-header {
    transform: translateY(-100%);
    animation: edit-post-fullscreen-mode__slide-in-animation 0.1s forwards; }

@keyframes edit-post-fullscreen-mode__slide-in-animation {
  100% {
    transform: translateY(0%); } }

.edit-post-header {
  height: 56px;
  padding: 4px 2px;
  border-bottom: 1px solid #e2e4e7;
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;
  z-index: 30;
  right: 0;
  left: 0;
  top: 0;
  position: -webkit-sticky;
  position: sticky; }
  @media (min-width: 600px) {
    .edit-post-header {
      position: fixed;
      padding: 8px;
      top: 46px; }
      body.is-fullscreen-mode .edit-post-header {
        top: 0; } }
  @media (min-width: 782px) {
    .edit-post-header {
      top: 32px; }
      body.is-fullscreen-mode .edit-post-header {
        top: 0; } }
  .edit-post-header .editor-post-switch-to-draft + .editor-post-preview {
    display: none; }
    @media (min-width: 600px) {
      .edit-post-header .editor-post-switch-to-draft + .editor-post-preview {
        display: inline-flex; } }
  .edit-post-header > .edit-post-header__settings {
    order: 1; }
    @supports ((position: -webkit-sticky) or (position: sticky)) {
      .edit-post-header > .edit-post-header__settings {
        order: initial; } }

.edit-post-header {
  /* Set left position when auto-fold is not on the body element. */
  right: 0; }
  @media (min-width: 782px) {
    .edit-post-header {
      right: 160px; } }

.auto-fold .edit-post-header {
  /* Auto fold is when on smaller breakpoints, nav menu auto colllapses. */ }
  @media (min-width: 782px) {
    .auto-fold .edit-post-header {
      right: 36px; } }
  @media (min-width: 960px) {
    .auto-fold .edit-post-header {
      right: 160px; } }

/* Sidebar manually collapsed. */
.folded .edit-post-header {
  right: 0; }
  @media (min-width: 782px) {
    .folded .edit-post-header {
      right: 36px; } }

/* Mobile menu opened. */
@media (max-width: 782px) {
  .auto-fold .wp-responsive-open .edit-post-header {
    right: 190px; } }

/* In small screens with resposive menu expanded there is small white space. */
@media (max-width: 600px) {
  .auto-fold .wp-responsive-open .edit-post-header {
    margin-right: -18px; } }

body.is-fullscreen-mode .edit-post-header {
  right: 0 !important; }

.edit-post-header__settings {
  display: inline-flex;
  align-items: center; }

.edit-post-header .components-button.is-toggled {
  color: #fff; }

.edit-post-header .components-button.is-toggled::before {
  content: "";
  border-radius: 4px;
  position: absolute;
  z-index: -1;
  background: #555d66;
  top: 1px;
  left: 1px;
  bottom: 1px;
  right: 1px; }

.edit-post-header .components-button.is-toggled:hover, .edit-post-header .components-button.is-toggled:focus {
  box-shadow: 0 0 0 1px #555d66, inset 0 0 0 1px #fff;
  color: #fff;
  background: #555d66; }

.edit-post-header .components-button.editor-post-save-draft, .edit-post-header .components-button.editor-post-switch-to-draft, .edit-post-header .components-button.editor-post-preview, .edit-post-header .components-button.editor-post-publish-button, .edit-post-header .components-button.editor-post-publish-panel__toggle {
  margin: 2px;
  height: 33px;
  line-height: 32px;
  font-size: 13px; }

.edit-post-header .components-button.editor-post-save-draft, .edit-post-header .components-button.editor-post-switch-to-draft {
  padding: 0 5px; }
  @media (min-width: 600px) {
    .edit-post-header .components-button.editor-post-save-draft, .edit-post-header .components-button.editor-post-switch-to-draft {
      padding: 0 12px; } }

.edit-post-header .components-button.editor-post-preview, .edit-post-header .components-button.editor-post-publish-button, .edit-post-header .components-button.editor-post-publish-panel__toggle {
  padding: 0 5px 2px; }
  @media (min-width: 600px) {
    .edit-post-header .components-button.editor-post-preview, .edit-post-header .components-button.editor-post-publish-button, .edit-post-header .components-button.editor-post-publish-panel__toggle {
      padding: 0 12px 2px; } }

@media (min-width: 782px) {
  .edit-post-header .components-button.editor-post-preview {
    margin: 0 12px 0 3px; }
  .edit-post-header .components-button.editor-post-publish-button, .edit-post-header .components-button.editor-post-publish-panel__toggle {
    margin: 0 3px 0 12px; } }

.edit-post-fullscreen-mode-close__toolbar {
  border-top: 0;
  border-bottom: 0;
  border-right: 0;
  margin: -9px -10px -9px 10px;
  padding: 9px 10px; }

.edit-post-header-toolbar {
  display: inline-flex;
  align-items: center; }
  .edit-post-header-toolbar > .components-button {
    display: none; }
    @media (min-width: 600px) {
      .edit-post-header-toolbar > .components-button {
        display: inline-flex; } }
  .edit-post-header-toolbar .editor-block-navigation,
  .edit-post-header-toolbar .table-of-contents {
    display: none; }
    @media (min-width: 600px) {
      .edit-post-header-toolbar .editor-block-navigation,
      .edit-post-header-toolbar .table-of-contents {
        display: flex; } }

.edit-post-header-toolbar__block-toolbar {
  position: absolute;
  top: 56px;
  right: 0;
  left: 0;
  background: #fff;
  min-height: 37px;
  border-bottom: 1px solid #e2e4e7; }
  .edit-post-header-toolbar__block-toolbar .editor-block-toolbar .components-toolbar {
    border-top: none;
    border-bottom: none; }
  .is-sidebar-opened .edit-post-header-toolbar__block-toolbar {
    display: none; }
  @media (min-width: 782px) {
    .is-sidebar-opened .edit-post-header-toolbar__block-toolbar {
      display: block;
      left: 280px; } }
  @media (min-width: 1080px) {
    .edit-post-header-toolbar__block-toolbar {
      padding-right: 8px;
      position: static;
      right: auto;
      left: auto;
      background: none;
      border-bottom: none;
      min-height: auto; }
      .is-sidebar-opened .edit-post-header-toolbar__block-toolbar {
        left: auto; }
      .edit-post-header-toolbar__block-toolbar .editor-block-toolbar {
        margin: -9px 0; }
      .edit-post-header-toolbar__block-toolbar .editor-block-toolbar .components-toolbar {
        padding: 10px 4px 9px; } }

.edit-post-more-menu {
  margin-right: -4px; }
  .edit-post-more-menu .components-icon-button {
    width: auto;
    padding: 8px 2px; }
  @media (min-width: 600px) {
    .edit-post-more-menu {
      margin-right: 4px; }
      .edit-post-more-menu .components-icon-button {
        padding: 8px 4px; } }
  .edit-post-more-menu .components-button svg {
    transform: rotate(-90deg); }

.edit-post-more-menu__content .components-popover__content {
  min-width: 260px; }
  @media (min-width: 480px) {
    .edit-post-more-menu__content .components-popover__content {
      width: auto;
      max-width: 480px; } }
  .edit-post-more-menu__content .components-popover__content .components-menu-group:not(:last-child),
  .edit-post-more-menu__content .components-popover__content > div:not(:last-child) .components-menu-group {
    border-bottom: 1px solid #e2e4e7; }
  .edit-post-more-menu__content .components-popover__content .components-menu-item__button {
    padding-right: 2rem; }
    .edit-post-more-menu__content .components-popover__content .components-menu-item__button.has-icon {
      padding-right: 0.5rem; }

.edit-post-pinned-plugins {
  display: none; }
  @media (min-width: 600px) {
    .edit-post-pinned-plugins {
      display: flex; } }
  .edit-post-pinned-plugins .components-icon-button {
    margin-right: 4px; }
  .edit-post-pinned-plugins .components-icon-button:not(.is-toggled) svg,
  .edit-post-pinned-plugins .components-icon-button:not(.is-toggled) svg * {
    stroke: #555d66;
    fill: #555d66; }
  .edit-post-pinned-plugins .components-icon-button.is-toggled svg,
  .edit-post-pinned-plugins .components-icon-button.is-toggled svg * {
    stroke: #fff !important;
    fill: #fff !important; }
  .edit-post-pinned-plugins .components-icon-button:hover svg,
  .edit-post-pinned-plugins .components-icon-button:hover svg * {
    stroke: #191e23 !important;
    fill: #191e23 !important; }

.edit-post-keyboard-shortcut-help__title {
  font-size: 1rem;
  font-weight: 600; }

.edit-post-keyboard-shortcut-help__section {
  margin: 0 0 2rem 0; }

.edit-post-keyboard-shortcut-help__section-title {
  font-size: 0.9rem;
  font-weight: 600; }

.edit-post-keyboard-shortcut-help__shortcut {
  display: flex;
  align-items: center;
  padding: 0.6rem 0;
  border-top: 1px solid #e2e4e7; }
  .edit-post-keyboard-shortcut-help__shortcut:last-child {
    border-bottom: 1px solid #e2e4e7; }

.edit-post-keyboard-shortcut-help__shortcut-term {
  order: 1;
  font-weight: 600;
  margin: 0 1rem 0 0; }

.edit-post-keyboard-shortcut-help__shortcut-description {
  flex: 1;
  order: 0;
  margin: 0;
  flex-basis: auto; }

.edit-post-keyboard-shortcut-help__shortcut-key-combination {
  background: none;
  margin: 0;
  padding: 0; }

.edit-post-keyboard-shortcut-help__shortcut-key {
  padding: 0.25rem 0.5rem;
  border-radius: 8%;
  margin: 0 0.2rem 0 0.2rem; }
  .edit-post-keyboard-shortcut-help__shortcut-key:last-child {
    margin: 0 0.2rem 0 0; }

.edit-post-layout,
.edit-post-layout__content {
  height: 100%; }

.edit-post-layout {
  position: relative; }
  .edit-post-layout .components-notice-list {
    position: -webkit-sticky;
    position: sticky;
    top: 56px;
    left: 0;
    color: #191e23; }
    @media (min-width: 600px) {
      .edit-post-layout .components-notice-list {
        position: fixed;
        top: inherit; } }
    .edit-post-layout .components-notice-list .components-notice {
      margin: 0 0 5px;
      padding: 6px 12px;
      min-height: 50px; }
      .edit-post-layout .components-notice-list .components-notice .components-notice__dismiss {
        margin: 10px 5px; }
  @media (min-width: 600px) {
    .edit-post-layout {
      padding-top: 56px; } }
  .edit-post-layout.has-fixed-toolbar .edit-post-layout__content {
    padding-top: 36px; }
  @media (min-width: 600px) {
    .edit-post-layout.has-fixed-toolbar {
      padding-top: 93px; }
      .edit-post-layout.has-fixed-toolbar .edit-post-layout__content {
        padding-top: 0; } }
  @media (min-width: 960px) {
    .edit-post-layout.has-fixed-toolbar {
      padding-top: 56px; } }

.components-notice-list {
  /* Set left position when auto-fold is not on the body element. */
  right: 0; }
  @media (min-width: 782px) {
    .components-notice-list {
      right: 160px; } }

.auto-fold .components-notice-list {
  /* Auto fold is when on smaller breakpoints, nav menu auto colllapses. */ }
  @media (min-width: 782px) {
    .auto-fold .components-notice-list {
      right: 36px; } }
  @media (min-width: 960px) {
    .auto-fold .components-notice-list {
      right: 160px; } }

/* Sidebar manually collapsed. */
.folded .components-notice-list {
  right: 0; }
  @media (min-width: 782px) {
    .folded .components-notice-list {
      right: 36px; } }

/* Mobile menu opened. */
@media (max-width: 782px) {
  .auto-fold .wp-responsive-open .components-notice-list {
    right: 190px; } }

/* In small screens with resposive menu expanded there is small white space. */
@media (max-width: 600px) {
  .auto-fold .wp-responsive-open .components-notice-list {
    margin-right: -18px; } }

body.is-fullscreen-mode .components-notice-list {
  right: 0 !important; }

.components-notice-list {
  left: 0; }

.edit-post-layout.is-sidebar-opened .components-notice-list {
  left: 280px; }

.edit-post-layout__metaboxes:not(:empty) {
  border-top: 1px solid #e2e4e7;
  margin-top: 10px;
  padding: 10px 0 10px;
  clear: both; }
  .edit-post-layout__metaboxes:not(:empty) .edit-post-meta-boxes-area {
    margin: auto 20px; }

.edit-post-layout__content {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  position: relative;
  padding-bottom: 50vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; }
  @media (min-width: 782px) {
    .edit-post-layout__content {
      position: fixed;
      bottom: 0;
      right: 0;
      left: 0;
      top: 88px;
      min-height: calc(100% - 88px);
      height: auto;
      margin-right: 160px; }
      body.auto-fold .edit-post-layout__content {
        margin-right: 36px; } }
    @media (min-width: 782px) and (min-width: 960px) {
      body.auto-fold .edit-post-layout__content {
        margin-right: 160px; } }
  @media (min-width: 782px) {
      body.folded .edit-post-layout__content {
        margin-right: 36px; }
      body.is-fullscreen-mode .edit-post-layout__content {
        margin-right: 0 !important;
        position: relative;
        top: inherit; } }
  @media (min-width: 600px) {
    .edit-post-layout__content {
      padding-bottom: 0; } }
  @media (min-width: 600px) {
    .edit-post-layout__content {
      overscroll-behavior-y: none; } }
  .edit-post-layout__content .edit-post-visual-editor {
    flex: 1 1 auto; }
    @supports ((position: -webkit-sticky) or (position: sticky)) {
      .edit-post-layout__content .edit-post-visual-editor {
        flex-basis: 100%; } }
  .edit-post-layout__content .edit-post-layout__metaboxes {
    flex-shrink: 0; }

.edit-post-layout .editor-post-publish-panel {
  position: fixed;
  z-index: 100001;
  top: 46px;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto; }
  body.is-fullscreen-mode .edit-post-layout .editor-post-publish-panel {
    top: 0; }
  @media (min-width: 782px) {
    .edit-post-layout .editor-post-publish-panel {
      top: 32px;
      right: auto;
      width: 280px;
      border-right: 1px solid #e2e4e7;
      transform: translateX(-100%);
      animation: edit-post-layout__slide-in-animation 0.1s forwards; }
      body.is-fullscreen-mode .edit-post-layout .editor-post-publish-panel {
        top: 0; } }

@keyframes edit-post-layout__slide-in-animation {
  100% {
    transform: translateX(0%); } }

.edit-post-layout .editor-post-publish-panel__header-publish-button .components-button.is-large {
  height: 33px;
  line-height: 32px; }

.edit-post-layout .editor-post-publish-panel__header-publish-button .editor-post-publish-panel__spacer {
  display: inline-flex;
  flex: 0 1 52px; }

.edit-post-toggle-publish-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 100000;
  width: 280px;
  height: 0;
  overflow: hidden; }
  .edit-post-toggle-publish-panel:focus-within {
    height: auto;
    padding: 20px 0 0 0; }
  .edit-post-toggle-publish-panel .edit-post-toggle-publish-panel__button {
    float: left;
    width: auto;
    height: auto;
    font-size: 14px;
    font-weight: 600;
    padding: 15px 23px 14px;
    line-height: normal;
    text-decoration: none;
    outline: none;
    background: #f1f1f1; }

.edit-post-meta-boxes-area {
  position: relative;
  /**
	 * The wordpress default for most meta-box elements is content-box. Some
	 * elements such as textarea and input are set to border-box in forms.css.
	 * These elements therefore specifically set back to border-box here, while
	 * other elements (such as .button) are unaffected by Gutenberg's style
	 * because of their higher specificity.
	 */
  /* Match width and positioning of the meta boxes. Override default styles. */
  /* Override Default meta box stylings */ }
  .edit-post-meta-boxes-area__container,
  .edit-post-meta-boxes-area .inside {
    box-sizing: content-box; }
  .edit-post-meta-boxes-area textarea,
  .edit-post-meta-boxes-area input {
    box-sizing: border-box; }
  .edit-post-meta-boxes-area #poststuff {
    margin: 0 auto;
    padding-top: 0;
    min-width: auto; }
  .edit-post-meta-boxes-area #poststuff h3.hndle,
  .edit-post-meta-boxes-area #poststuff .stuffbox > h3,
  .edit-post-meta-boxes-area #poststuff h2.hndle {
    /* WordPress selectors yolo */
    border-bottom: 1px solid #e2e4e7;
    box-sizing: border-box;
    color: inherit;
    font-weight: 600;
    outline: none;
    padding: 15px;
    position: relative;
    width: 100%; }
  .edit-post-meta-boxes-area .postbox {
    border: 0;
    color: inherit;
    margin-bottom: 0; }
  .edit-post-meta-boxes-area .postbox > .inside {
    border-bottom: 1px solid #e2e4e7;
    color: inherit;
    padding: 0 14px 14px;
    margin: 0; }
  .edit-post-meta-boxes-area .postbox .handlediv {
    height: 44px;
    width: 44px; }
  .edit-post-meta-boxes-area.is-loading::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    content: "";
    background: transparent;
    z-index: 1; }
  .edit-post-meta-boxes-area .components-spinner {
    position: absolute;
    top: 10px;
    left: 20px;
    z-index: 5; }
  .edit-post-meta-boxes-area .is-hidden {
    display: none; }

.edit-post-meta-boxes-area__clear {
  clear: both; }

.edit-post-sidebar {
  position: fixed;
  z-index: 100000;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  border-right: 1px solid #e2e4e7;
  background: #fff;
  color: #555d66;
  height: 100vh;
  overflow: hidden; }
  @media (min-width: 600px) {
    .edit-post-sidebar {
      top: 102px;
      z-index: 90;
      height: auto;
      overflow: auto;
      -webkit-overflow-scrolling: touch; }
      body.is-fullscreen-mode .edit-post-sidebar {
        top: 56px; } }
  @media (min-width: 782px) {
    .edit-post-sidebar {
      top: 88px; }
      body.is-fullscreen-mode .edit-post-sidebar {
        top: 56px; } }
  .edit-post-sidebar > .components-panel {
    border-right: none;
    border-left: none;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    height: auto;
    max-height: calc(100vh - 96px);
    margin-top: -1px;
    margin-bottom: -1px; }
    body.is-fullscreen-mode .edit-post-sidebar > .components-panel {
      max-height: calc(100vh - 50px); }
      @media (min-width: 600px) {
        body.is-fullscreen-mode .edit-post-sidebar > .components-panel {
          max-height: none; } }
    @media (min-width: 600px) {
      .edit-post-sidebar > .components-panel {
        overflow: inherit;
        height: auto;
        max-height: none; } }
  .edit-post-sidebar > .components-panel .components-panel__header {
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    left: 0;
    height: 50px; }
    @media (min-width: 600px) {
      .edit-post-sidebar > .components-panel .components-panel__header {
        position: inherit;
        top: auto;
        right: auto;
        left: auto; } }
  .edit-post-sidebar p {
    margin-top: 0; }
  .edit-post-sidebar h2,
  .edit-post-sidebar h3 {
    font-size: 13px;
    color: #555d66;
    margin-bottom: 1.5em; }
  .edit-post-sidebar hr {
    border-top: none;
    border-bottom: 1px solid #e2e4e7;
    margin: 1.5em 0; }
  .edit-post-sidebar div.components-toolbar {
    box-shadow: none;
    margin-bottom: 1.5em; }
    .edit-post-sidebar div.components-toolbar:last-child {
      margin-bottom: 0; }
  .edit-post-sidebar p + div.components-toolbar {
    margin-top: -1em; }
  .edit-post-sidebar .editor-skip-to-selected-block:focus {
    top: auto;
    left: 10px;
    bottom: 10px;
    right: auto; }

/* Visual and Text editor both */
@media (min-width: 782px) {
  .edit-post-layout.is-sidebar-opened .edit-post-layout__content {
    margin-left: 280px; } }

.edit-post-layout.is-sidebar-opened .edit-post-sidebar,
.edit-post-layout.is-sidebar-opened .edit-post-plugin-sidebar__sidebar-layout {
  /* Sidebar covers screen on mobile */
  width: 100%;
  /* Sidebar sits on the side on larger breakpoints */ }
  @media (min-width: 782px) {
    .edit-post-layout.is-sidebar-opened .edit-post-sidebar,
    .edit-post-layout.is-sidebar-opened .edit-post-plugin-sidebar__sidebar-layout {
      width: 280px; } }

/* Text Editor specific */
.components-panel__header.edit-post-sidebar__header {
  background: #fff;
  padding-left: 8px; }
  .components-panel__header.edit-post-sidebar__header .edit-post-sidebar__title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%; }
  @media (min-width: 782px) {
    .components-panel__header.edit-post-sidebar__header {
      display: none; } }

.components-panel__header.edit-post-sidebar__panel-tabs {
  justify-content: flex-start;
  padding-right: 0;
  padding-left: 4px;
  border-top: 0;
  margin-top: 0; }
  .components-panel__header.edit-post-sidebar__panel-tabs .components-icon-button {
    display: none;
    margin-right: auto; }
    @media (min-width: 782px) {
      .components-panel__header.edit-post-sidebar__panel-tabs .components-icon-button {
        display: flex; } }

.edit-post-sidebar__panel-tab {
  background: transparent;
  border: none;
  border-radius: 0;
  cursor: pointer;
  height: 50px;
  padding: 3px 15px;
  margin-right: 0;
  font-weight: 400;
  outline-offset: -1px; }
  .edit-post-sidebar__panel-tab.is-active {
    padding-bottom: 0;
    border-bottom: 3px solid #0085ba;
    font-weight: 600; }
  body.admin-color-sunrise .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #d1864a; }
  body.admin-color-ocean .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #a3b9a2; }
  body.admin-color-midnight .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #e14d43; }
  body.admin-color-ectoplasm .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #a7b656; }
  body.admin-color-coffee .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #c2a68c; }
  body.admin-color-blue .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #82b4cb; }
  body.admin-color-light .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #0085ba; }
  .edit-post-sidebar__panel-tab:focus {
    color: #191e23;
    outline: 1px solid #6c7781;
    box-shadow: none; }

.components-panel__body.is-opened.edit-post-last-revision__panel {
  padding: 0; }

.editor-post-last-revision__title {
  padding: 13px 16px; }

.editor-post-author__select {
  margin: -5px 0;
  width: 100%; }
  @supports ((position: -webkit-sticky) or (position: sticky)) {
    .editor-post-author__select {
      width: auto; } }

.edit-post-post-link__link-post-name {
  font-weight: 600; }

.edit-post-post-link__preview-label {
  margin: 0; }

.edit-post-post-link__link {
  word-wrap: break-word; }

.edit-post-post-schedule {
  width: 100%;
  position: relative; }

.edit-post-post-schedule__label {
  display: none; }

.components-button.edit-post-post-schedule__toggle {
  text-align: left; }

.edit-post-post-schedule__dialog .components-popover__content {
  padding: 10px; }
  @media (min-width: 782px) {
    .edit-post-post-schedule__dialog .components-popover__content {
      width: 270px; } }

.edit-post-post-status .edit-post-post-publish-dropdown__switch-to-draft {
  margin-top: 15px;
  width: 100%;
  text-align: center; }

.edit-post-post-visibility {
  width: 100%; }

.edit-post-post-visibility__dialog .components-popover__content {
  padding: 10px; }
  @media (min-width: 782px) {
    .edit-post-post-visibility__dialog .components-popover__content {
      width: 257px; } }

.edit-post-post-visibility__dialog-legend {
  font-weight: 600; }

.edit-post-post-visibility__choice {
  margin: 10px 0; }

.edit-post-post-visibility__dialog-radio,
.edit-post-post-visibility__dialog-label {
  vertical-align: top; }

.edit-post-post-visibility__dialog-password-input {
  width: calc(100% - 20px);
  margin-right: 20px; }

.edit-post-post-visibility__dialog-info {
  color: #7e8993;
  padding-right: 20px;
  font-style: italic;
  margin: 4px 0 0;
  line-height: 1.4; }

.components-panel__header.edit-post-sidebar__panel-tabs {
  justify-content: flex-start;
  padding-right: 0;
  padding-left: 4px;
  border-top: 0;
  position: -webkit-sticky;
  position: sticky;
  z-index: 1;
  top: 0; }
  .components-panel__header.edit-post-sidebar__panel-tabs ul {
    display: flex; }
  .components-panel__header.edit-post-sidebar__panel-tabs li {
    margin: 0; }

.edit-post-sidebar__panel-tab {
  background: transparent;
  border: none;
  border-radius: 0;
  cursor: pointer;
  height: 48px;
  padding: 3px 15px;
  margin-right: 0;
  font-weight: 400;
  color: #191e23;
  outline-offset: -1px; }
  .edit-post-sidebar__panel-tab::after {
    content: attr(data-label);
    display: block;
    font-weight: 600;
    height: 0;
    overflow: hidden;
    speak: none;
    visibility: hidden; }
  .edit-post-sidebar__panel-tab.is-active {
    padding-bottom: 0;
    border-bottom: 3px solid #0085ba;
    font-weight: 600; }
  body.admin-color-sunrise .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #d1864a; }
  body.admin-color-ocean .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #a3b9a2; }
  body.admin-color-midnight .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #e14d43; }
  body.admin-color-ectoplasm .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #a7b656; }
  body.admin-color-coffee .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #c2a68c; }
  body.admin-color-blue .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #82b4cb; }
  body.admin-color-light .edit-post-sidebar__panel-tab.is-active {
    border-bottom: 3px solid #0085ba; }
  .edit-post-sidebar__panel-tab:focus {
    color: #191e23;
    outline: 1px solid #6c7781;
    box-shadow: none; }

.edit-post-settings-sidebar__panel-block .components-panel__body {
  border: none;
  border-top: 1px solid #e2e4e7;
  margin: 0 -16px; }
  .edit-post-settings-sidebar__panel-block .components-panel__body .components-base-control {
    margin: 0 0 1.5em 0; }
    .edit-post-settings-sidebar__panel-block .components-panel__body .components-base-control:last-child {
      margin-bottom: 0.5em; }
  .edit-post-settings-sidebar__panel-block .components-panel__body .components-panel__body-toggle {
    color: #191e23; }
  .edit-post-settings-sidebar__panel-block .components-panel__body:first-child {
    margin-top: 16px; }
  .edit-post-settings-sidebar__panel-block .components-panel__body:last-child {
    margin-bottom: -16px; }

/* Text Editor specific */
.components-panel__header.edit-post-sidebar-header__small {
  background: #fff;
  padding-left: 4px; }
  .components-panel__header.edit-post-sidebar-header__small .edit-post-sidebar__title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%; }
  @media (min-width: 782px) {
    .components-panel__header.edit-post-sidebar-header__small {
      display: none; } }

.components-panel__header.edit-post-sidebar-header {
  padding-left: 4px;
  background: #f3f4f5; }
  .components-panel__header.edit-post-sidebar-header .components-icon-button {
    display: none;
    margin-right: auto; }
    .components-panel__header.edit-post-sidebar-header .components-icon-button ~ .components-icon-button {
      margin-right: 0; }
    @media (min-width: 782px) {
      .components-panel__header.edit-post-sidebar-header .components-icon-button {
        display: flex; } }

.edit-post-text-editor__body {
  padding-top: 40px; }
  @media (min-width: 600px) {
    .edit-post-text-editor__body {
      padding-top: 86px; }
      body.is-fullscreen-mode .edit-post-text-editor__body {
        padding-top: 40px; } }
  @media (min-width: 782px) {
    .edit-post-text-editor__body {
      padding-top: 40px; }
      body.is-fullscreen-mode .edit-post-text-editor__body {
        padding-top: 40px; } }

.edit-post-text-editor {
  width: 100%;
  margin-right: 16px;
  margin-left: 16px;
  padding-top: 44px; }
  @media (min-width: 600px) {
    .edit-post-text-editor {
      max-width: 610px;
      margin-right: auto;
      margin-left: auto; } }
  .edit-post-text-editor .editor-post-title__block textarea {
    border: 1px solid #e2e4e7;
    margin-bottom: 4px;
    padding: 14px; }
  .edit-post-text-editor .editor-post-title__block textarea:hover,
  .edit-post-text-editor .editor-post-title__block.is-selected textarea {
    box-shadow: 0 0 0 1px #e2e4e7; }
  .edit-post-text-editor .editor-post-permalink {
    right: 0;
    left: 0;
    margin-top: -6px; }
  @media (min-width: 600px) {
    .edit-post-text-editor .editor-post-title,
    .edit-post-text-editor .editor-post-title__block {
      padding: 0; } }
  .edit-post-text-editor .editor-post-text-editor {
    padding: 14px;
    min-height: 200px;
    line-height: 1.8; }
  .edit-post-text-editor .edit-post-text-editor__toolbar {
    position: absolute;
    top: 8px;
    right: 0;
    left: 0;
    height: 36px;
    line-height: 36px;
    padding: 0 16px 0 8px;
    display: flex; }
    .edit-post-text-editor .edit-post-text-editor__toolbar h2 {
      margin: 0 0 0 auto;
      font-size: 13px;
      color: #555d66; }
    .edit-post-text-editor .edit-post-text-editor__toolbar .components-icon-button svg {
      order: 1; }

.edit-post-visual-editor {
  position: relative;
  padding: 50px 0; }
  .edit-post-visual-editor .components-button {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif; }

.edit-post-visual-editor .editor-writing-flow__click-redirect {
  height: 50px;
  width: 100%;
  margin: -4px auto -50px; }

.edit-post-visual-editor .editor-block-list__block {
  margin-right: auto;
  margin-left: auto; }
  @media (min-width: 600px) {
    .edit-post-visual-editor .editor-block-list__block .editor-block-list__block-edit {
      margin-right: -28px;
      margin-left: -28px; }
    .edit-post-visual-editor .editor-block-list__block[data-align="wide"] > .editor-block-list__block-edit > .editor-block-contextual-toolbar,
    .edit-post-visual-editor .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit > .editor-block-contextual-toolbar {
      width: calc(100% + 30px);
      height: 0;
      text-align: center;
      float: right; }
      .edit-post-visual-editor .editor-block-list__block[data-align="wide"] > .editor-block-list__block-edit > .editor-block-contextual-toolbar .editor-block-toolbar,
      .edit-post-visual-editor .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit > .editor-block-contextual-toolbar .editor-block-toolbar {
        max-width: 610px;
        width: 100%;
        position: relative; }
    .edit-post-visual-editor .editor-block-list__block[data-align="full"] > .editor-block-list__block-edit > .editor-block-contextual-toolbar {
      width: 100%;
      margin-right: 0;
      margin-left: 0; } }

@media (min-width: 600px) {
  .editor-post-title {
    padding-right: 46px;
    padding-left: 46px; } }

.edit-post-visual-editor .editor-post-title__block {
  margin-right: auto;
  margin-left: auto;
  margin-bottom: -20px; }
  .edit-post-visual-editor .editor-post-title__block > div {
    margin-right: 0;
    margin-left: 0; }
  @media (min-width: 600px) {
    .edit-post-visual-editor .editor-post-title__block > div {
      margin-right: -2px;
      margin-left: -2px; } }

.edit-post-visual-editor .editor-block-list__layout > .editor-block-list__block[data-align="left"]:first-child,
.edit-post-visual-editor .editor-block-list__layout > .editor-block-list__block[data-align="right"]:first-child {
  margin-top: 34px; }

.edit-post-visual-editor .editor-default-block-appender {
  margin-right: auto;
  margin-left: auto;
  position: relative; }
  .edit-post-visual-editor .editor-default-block-appender[data-root-client-id=""] .editor-default-block-appender__content:hover {
    outline: 1px solid transparent; }

.edit-post-visual-editor .editor-block-list__block[data-type="core/paragraph"] p[data-is-placeholder-visible="true"] + p,
.edit-post-visual-editor .editor-default-block-appender__content {
  min-height: 28px;
  line-height: 1.8; }

.edit-post-options-modal__title {
  font-size: 1rem;
  font-weight: 600; }

.edit-post-options-modal__section {
  margin: 0 0 2rem 0; }

.edit-post-options-modal__section-title {
  font-size: 0.9rem;
  font-weight: 600; }

.edit-post-options-modal__option {
  border-top: 1px solid #e2e4e7; }
  .edit-post-options-modal__option:last-child {
    border-bottom: 1px solid #e2e4e7; }
  .edit-post-options-modal__option .components-base-control__field {
    align-items: center;
    display: flex;
    margin: 0; }
  .edit-post-options-modal__option .components-checkbox-control__label {
    flex-grow: 1;
    padding: 0.6rem 10px 0.6rem 0; }

/**
 * Animations
 */
@keyframes edit-post__loading-fade-animation {
  0% {
    opacity: 0.5; }
  50% {
    opacity: 1; }
  100% {
    opacity: 0.5; } }

@keyframes edit-post__fade-in-animation {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }

html.wp-toolbar {
  background: #fff; }

body.block-editor-page {
  background: #fff;
  /* We hide legacy notices in Gutenberg, because they were not designed in a way that scaled well.
	   Plugins can use Gutenberg notices if they need to pass on information to the user when they are editing. */ }
  body.block-editor-page #wpcontent {
    padding-right: 0; }
  body.block-editor-page #wpbody-content {
    padding-bottom: 0; }
  body.block-editor-page #wpbody-content > div:not(.block-editor):not(#screen-meta) {
    display: none; }
  body.block-editor-page #wpfooter {
    display: none; }
  body.block-editor-page .a11y-speak-region {
    right: -1px;
    top: -1px; }
  body.block-editor-page ul#adminmenu a.wp-has-current-submenu::after,
  body.block-editor-page ul#adminmenu > li.current > a.current::after {
    border-left-color: #fff; }
  body.block-editor-page .media-frame select.attachment-filters:last-of-type {
    width: auto;
    max-width: 100%; }

.block-editor,
.components-modal__frame {
  box-sizing: border-box; }
  .block-editor *,
  .block-editor *::before,
  .block-editor *::after,
  .components-modal__frame *,
  .components-modal__frame *::before,
  .components-modal__frame *::after {
    box-sizing: inherit; }
  .block-editor select,
  .components-modal__frame select {
    font-size: 13px;
    color: #555d66; }

@media (min-width: 600px) {
  .block-editor__container {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    min-height: calc(100vh - 46px); }
    body.is-fullscreen-mode .block-editor__container {
      min-height: 100vh; } }

@media (min-width: 782px) {
  .block-editor__container {
    min-height: calc(100vh - 32px); }
    body.is-fullscreen-mode .block-editor__container {
      min-height: 100vh; } }

.block-editor__container img {
  max-width: 100%;
  height: auto; }

.block-editor__container iframe {
  width: 100%; }

.block-editor__container .components-navigate-regions {
  height: 100%; }

.editor-post-permalink .input-control,
.editor-post-permalink input[type="text"],
.editor-post-permalink input[type="search"],
.editor-post-permalink input[type="radio"],
.editor-post-permalink input[type="tel"],
.editor-post-permalink input[type="time"],
.editor-post-permalink input[type="url"],
.editor-post-permalink input[type="week"],
.editor-post-permalink input[type="password"],
.editor-post-permalink input[type="checkbox"],
.editor-post-permalink input[type="color"],
.editor-post-permalink input[type="date"],
.editor-post-permalink input[type="datetime"],
.editor-post-permalink input[type="datetime-local"],
.editor-post-permalink input[type="email"],
.editor-post-permalink input[type="month"],
.editor-post-permalink input[type="number"],
.editor-post-permalink select,
.editor-post-permalink textarea,
.edit-post-sidebar .input-control,
.edit-post-sidebar input[type="text"],
.edit-post-sidebar input[type="search"],
.edit-post-sidebar input[type="radio"],
.edit-post-sidebar input[type="tel"],
.edit-post-sidebar input[type="time"],
.edit-post-sidebar input[type="url"],
.edit-post-sidebar input[type="week"],
.edit-post-sidebar input[type="password"],
.edit-post-sidebar input[type="checkbox"],
.edit-post-sidebar input[type="color"],
.edit-post-sidebar input[type="date"],
.edit-post-sidebar input[type="datetime"],
.edit-post-sidebar input[type="datetime-local"],
.edit-post-sidebar input[type="email"],
.edit-post-sidebar input[type="month"],
.edit-post-sidebar input[type="number"],
.edit-post-sidebar select,
.edit-post-sidebar textarea,
.editor-post-publish-panel .input-control,
.editor-post-publish-panel input[type="text"],
.editor-post-publish-panel input[type="search"],
.editor-post-publish-panel input[type="radio"],
.editor-post-publish-panel input[type="tel"],
.editor-post-publish-panel input[type="time"],
.editor-post-publish-panel input[type="url"],
.editor-post-publish-panel input[type="week"],
.editor-post-publish-panel input[type="password"],
.editor-post-publish-panel input[type="checkbox"],
.editor-post-publish-panel input[type="color"],
.editor-post-publish-panel input[type="date"],
.editor-post-publish-panel input[type="datetime"],
.editor-post-publish-panel input[type="datetime-local"],
.editor-post-publish-panel input[type="email"],
.editor-post-publish-panel input[type="month"],
.editor-post-publish-panel input[type="number"],
.editor-post-publish-panel select,
.editor-post-publish-panel textarea,
.editor-block-list__block .input-control,
.editor-block-list__block input[type="text"],
.editor-block-list__block input[type="search"],
.editor-block-list__block input[type="radio"],
.editor-block-list__block input[type="tel"],
.editor-block-list__block input[type="time"],
.editor-block-list__block input[type="url"],
.editor-block-list__block input[type="week"],
.editor-block-list__block input[type="password"],
.editor-block-list__block input[type="checkbox"],
.editor-block-list__block input[type="color"],
.editor-block-list__block input[type="date"],
.editor-block-list__block input[type="datetime"],
.editor-block-list__block input[type="datetime-local"],
.editor-block-list__block input[type="email"],
.editor-block-list__block input[type="month"],
.editor-block-list__block input[type="number"],
.editor-block-list__block select,
.editor-block-list__block textarea,
.components-popover .input-control,
.components-popover input[type="text"],
.components-popover input[type="search"],
.components-popover input[type="radio"],
.components-popover input[type="tel"],
.components-popover input[type="time"],
.components-popover input[type="url"],
.components-popover input[type="week"],
.components-popover input[type="password"],
.components-popover input[type="checkbox"],
.components-popover input[type="color"],
.components-popover input[type="date"],
.components-popover input[type="datetime"],
.components-popover input[type="datetime-local"],
.components-popover input[type="email"],
.components-popover input[type="month"],
.components-popover input[type="number"],
.components-popover select,
.components-popover textarea,
.components-modal__content .input-control,
.components-modal__content input[type="text"],
.components-modal__content input[type="search"],
.components-modal__content input[type="radio"],
.components-modal__content input[type="tel"],
.components-modal__content input[type="time"],
.components-modal__content input[type="url"],
.components-modal__content input[type="week"],
.components-modal__content input[type="password"],
.components-modal__content input[type="checkbox"],
.components-modal__content input[type="color"],
.components-modal__content input[type="date"],
.components-modal__content input[type="datetime"],
.components-modal__content input[type="datetime-local"],
.components-modal__content input[type="email"],
.components-modal__content input[type="month"],
.components-modal__content input[type="number"],
.components-modal__content select,
.components-modal__content textarea {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  padding: 6px 8px;
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.1s linear;
  border-radius: 4px;
  border: 1px solid #8d96a0; }
  .editor-post-permalink .input-control:focus,
  .editor-post-permalink input[type="text"]:focus,
  .editor-post-permalink input[type="search"]:focus,
  .editor-post-permalink input[type="radio"]:focus,
  .editor-post-permalink input[type="tel"]:focus,
  .editor-post-permalink input[type="time"]:focus,
  .editor-post-permalink input[type="url"]:focus,
  .editor-post-permalink input[type="week"]:focus,
  .editor-post-permalink input[type="password"]:focus,
  .editor-post-permalink input[type="checkbox"]:focus,
  .editor-post-permalink input[type="color"]:focus,
  .editor-post-permalink input[type="date"]:focus,
  .editor-post-permalink input[type="datetime"]:focus,
  .editor-post-permalink input[type="datetime-local"]:focus,
  .editor-post-permalink input[type="email"]:focus,
  .editor-post-permalink input[type="month"]:focus,
  .editor-post-permalink input[type="number"]:focus,
  .editor-post-permalink select:focus,
  .editor-post-permalink textarea:focus,
  .edit-post-sidebar .input-control:focus,
  .edit-post-sidebar input[type="text"]:focus,
  .edit-post-sidebar input[type="search"]:focus,
  .edit-post-sidebar input[type="radio"]:focus,
  .edit-post-sidebar input[type="tel"]:focus,
  .edit-post-sidebar input[type="time"]:focus,
  .edit-post-sidebar input[type="url"]:focus,
  .edit-post-sidebar input[type="week"]:focus,
  .edit-post-sidebar input[type="password"]:focus,
  .edit-post-sidebar input[type="checkbox"]:focus,
  .edit-post-sidebar input[type="color"]:focus,
  .edit-post-sidebar input[type="date"]:focus,
  .edit-post-sidebar input[type="datetime"]:focus,
  .edit-post-sidebar input[type="datetime-local"]:focus,
  .edit-post-sidebar input[type="email"]:focus,
  .edit-post-sidebar input[type="month"]:focus,
  .edit-post-sidebar input[type="number"]:focus,
  .edit-post-sidebar select:focus,
  .edit-post-sidebar textarea:focus,
  .editor-post-publish-panel .input-control:focus,
  .editor-post-publish-panel input[type="text"]:focus,
  .editor-post-publish-panel input[type="search"]:focus,
  .editor-post-publish-panel input[type="radio"]:focus,
  .editor-post-publish-panel input[type="tel"]:focus,
  .editor-post-publish-panel input[type="time"]:focus,
  .editor-post-publish-panel input[type="url"]:focus,
  .editor-post-publish-panel input[type="week"]:focus,
  .editor-post-publish-panel input[type="password"]:focus,
  .editor-post-publish-panel input[type="checkbox"]:focus,
  .editor-post-publish-panel input[type="color"]:focus,
  .editor-post-publish-panel input[type="date"]:focus,
  .editor-post-publish-panel input[type="datetime"]:focus,
  .editor-post-publish-panel input[type="datetime-local"]:focus,
  .editor-post-publish-panel input[type="email"]:focus,
  .editor-post-publish-panel input[type="month"]:focus,
  .editor-post-publish-panel input[type="number"]:focus,
  .editor-post-publish-panel select:focus,
  .editor-post-publish-panel textarea:focus,
  .editor-block-list__block .input-control:focus,
  .editor-block-list__block input[type="text"]:focus,
  .editor-block-list__block input[type="search"]:focus,
  .editor-block-list__block input[type="radio"]:focus,
  .editor-block-list__block input[type="tel"]:focus,
  .editor-block-list__block input[type="time"]:focus,
  .editor-block-list__block input[type="url"]:focus,
  .editor-block-list__block input[type="week"]:focus,
  .editor-block-list__block input[type="password"]:focus,
  .editor-block-list__block input[type="checkbox"]:focus,
  .editor-block-list__block input[type="color"]:focus,
  .editor-block-list__block input[type="date"]:focus,
  .editor-block-list__block input[type="datetime"]:focus,
  .editor-block-list__block input[type="datetime-local"]:focus,
  .editor-block-list__block input[type="email"]:focus,
  .editor-block-list__block input[type="month"]:focus,
  .editor-block-list__block input[type="number"]:focus,
  .editor-block-list__block select:focus,
  .editor-block-list__block textarea:focus,
  .components-popover .input-control:focus,
  .components-popover input[type="text"]:focus,
  .components-popover input[type="search"]:focus,
  .components-popover input[type="radio"]:focus,
  .components-popover input[type="tel"]:focus,
  .components-popover input[type="time"]:focus,
  .components-popover input[type="url"]:focus,
  .components-popover input[type="week"]:focus,
  .components-popover input[type="password"]:focus,
  .components-popover input[type="checkbox"]:focus,
  .components-popover input[type="color"]:focus,
  .components-popover input[type="date"]:focus,
  .components-popover input[type="datetime"]:focus,
  .components-popover input[type="datetime-local"]:focus,
  .components-popover input[type="email"]:focus,
  .components-popover input[type="month"]:focus,
  .components-popover input[type="number"]:focus,
  .components-popover select:focus,
  .components-popover textarea:focus,
  .components-modal__content .input-control:focus,
  .components-modal__content input[type="text"]:focus,
  .components-modal__content input[type="search"]:focus,
  .components-modal__content input[type="radio"]:focus,
  .components-modal__content input[type="tel"]:focus,
  .components-modal__content input[type="time"]:focus,
  .components-modal__content input[type="url"]:focus,
  .components-modal__content input[type="week"]:focus,
  .components-modal__content input[type="password"]:focus,
  .components-modal__content input[type="checkbox"]:focus,
  .components-modal__content input[type="color"]:focus,
  .components-modal__content input[type="date"]:focus,
  .components-modal__content input[type="datetime"]:focus,
  .components-modal__content input[type="datetime-local"]:focus,
  .components-modal__content input[type="email"]:focus,
  .components-modal__content input[type="month"]:focus,
  .components-modal__content input[type="number"]:focus,
  .components-modal__content select:focus,
  .components-modal__content textarea:focus {
    color: #191e23;
    border-color: #00a0d2;
    box-shadow: 0 0 0 1px #00a0d2;
    outline: 2px solid transparent;
    outline-offset: -2px; }

.editor-post-permalink select,
.edit-post-sidebar select,
.editor-post-publish-panel select,
.editor-block-list__block select,
.components-popover select,
.components-modal__content select {
  padding: 2px; }
  .editor-post-permalink select:focus,
  .edit-post-sidebar select:focus,
  .editor-post-publish-panel select:focus,
  .editor-block-list__block select:focus,
  .components-popover select:focus,
  .components-modal__content select:focus {
    border-color: #008dbe;
    outline: 2px solid transparent;
    outline-offset: 0; }

.editor-post-permalink input[type="checkbox"],
.editor-post-permalink input[type="radio"],
.edit-post-sidebar input[type="checkbox"],
.edit-post-sidebar input[type="radio"],
.editor-post-publish-panel input[type="checkbox"],
.editor-post-publish-panel input[type="radio"],
.editor-block-list__block input[type="checkbox"],
.editor-block-list__block input[type="radio"],
.components-popover input[type="checkbox"],
.components-popover input[type="radio"],
.components-modal__content input[type="checkbox"],
.components-modal__content input[type="radio"] {
  border: 2px solid #6c7781;
  margin-left: 12px;
  transition: none; }
  .editor-post-permalink input[type="checkbox"]:focus,
  .editor-post-permalink input[type="radio"]:focus,
  .edit-post-sidebar input[type="checkbox"]:focus,
  .edit-post-sidebar input[type="radio"]:focus,
  .editor-post-publish-panel input[type="checkbox"]:focus,
  .editor-post-publish-panel input[type="radio"]:focus,
  .editor-block-list__block input[type="checkbox"]:focus,
  .editor-block-list__block input[type="radio"]:focus,
  .components-popover input[type="checkbox"]:focus,
  .components-popover input[type="radio"]:focus,
  .components-modal__content input[type="checkbox"]:focus,
  .components-modal__content input[type="radio"]:focus {
    border-color: #6c7781;
    box-shadow: 0 0 0 1px #6c7781; }
  .editor-post-permalink input[type="checkbox"]:checked,
  .editor-post-permalink input[type="radio"]:checked,
  .edit-post-sidebar input[type="checkbox"]:checked,
  .edit-post-sidebar input[type="radio"]:checked,
  .editor-post-publish-panel input[type="checkbox"]:checked,
  .editor-post-publish-panel input[type="radio"]:checked,
  .editor-block-list__block input[type="checkbox"]:checked,
  .editor-block-list__block input[type="radio"]:checked,
  .components-popover input[type="checkbox"]:checked,
  .components-popover input[type="radio"]:checked,
  .components-modal__content input[type="checkbox"]:checked,
  .components-modal__content input[type="radio"]:checked {
    background: #11a0d2;
    border-color: #11a0d2; }
  body.admin-color-sunrise .editor-post-permalink input[type="checkbox"]:checked, body.admin-color-sunrise .editor-post-permalink input[type="radio"]:checked, body.admin-color-sunrise .edit-post-sidebar input[type="checkbox"]:checked, body.admin-color-sunrise .edit-post-sidebar input[type="radio"]:checked, body.admin-color-sunrise .editor-post-publish-panel input[type="checkbox"]:checked, body.admin-color-sunrise .editor-post-publish-panel input[type="radio"]:checked, body.admin-color-sunrise .editor-block-list__block input[type="checkbox"]:checked, body.admin-color-sunrise .editor-block-list__block input[type="radio"]:checked, body.admin-color-sunrise .components-popover input[type="checkbox"]:checked, body.admin-color-sunrise .components-popover input[type="radio"]:checked, body.admin-color-sunrise .components-modal__content input[type="checkbox"]:checked, body.admin-color-sunrise .components-modal__content input[type="radio"]:checked {
    background: #c8b03c;
    border-color: #c8b03c; }
  body.admin-color-ocean .editor-post-permalink input[type="checkbox"]:checked, body.admin-color-ocean .editor-post-permalink input[type="radio"]:checked, body.admin-color-ocean .edit-post-sidebar input[type="checkbox"]:checked, body.admin-color-ocean .edit-post-sidebar input[type="radio"]:checked, body.admin-color-ocean .editor-post-publish-panel input[type="checkbox"]:checked, body.admin-color-ocean .editor-post-publish-panel input[type="radio"]:checked, body.admin-color-ocean .editor-block-list__block input[type="checkbox"]:checked, body.admin-color-ocean .editor-block-list__block input[type="radio"]:checked, body.admin-color-ocean .components-popover input[type="checkbox"]:checked, body.admin-color-ocean .components-popover input[type="radio"]:checked, body.admin-color-ocean .components-modal__content input[type="checkbox"]:checked, body.admin-color-ocean .components-modal__content input[type="radio"]:checked {
    background: #a3b9a2;
    border-color: #a3b9a2; }
  body.admin-color-midnight .editor-post-permalink input[type="checkbox"]:checked, body.admin-color-midnight .editor-post-permalink input[type="radio"]:checked, body.admin-color-midnight .edit-post-sidebar input[type="checkbox"]:checked, body.admin-color-midnight .edit-post-sidebar input[type="radio"]:checked, body.admin-color-midnight .editor-post-publish-panel input[type="checkbox"]:checked, body.admin-color-midnight .editor-post-publish-panel input[type="radio"]:checked, body.admin-color-midnight .editor-block-list__block input[type="checkbox"]:checked, body.admin-color-midnight .editor-block-list__block input[type="radio"]:checked, body.admin-color-midnight .components-popover input[type="checkbox"]:checked, body.admin-color-midnight .components-popover input[type="radio"]:checked, body.admin-color-midnight .components-modal__content input[type="checkbox"]:checked, body.admin-color-midnight .components-modal__content input[type="radio"]:checked {
    background: #77a6b9;
    border-color: #77a6b9; }
  body.admin-color-ectoplasm .editor-post-permalink input[type="checkbox"]:checked, body.admin-color-ectoplasm .editor-post-permalink input[type="radio"]:checked, body.admin-color-ectoplasm .edit-post-sidebar input[type="checkbox"]:checked, body.admin-color-ectoplasm .edit-post-sidebar input[type="radio"]:checked, body.admin-color-ectoplasm .editor-post-publish-panel input[type="checkbox"]:checked, body.admin-color-ectoplasm .editor-post-publish-panel input[type="radio"]:checked, body.admin-color-ectoplasm .editor-block-list__block input[type="checkbox"]:checked, body.admin-color-ectoplasm .editor-block-list__block input[type="radio"]:checked, body.admin-color-ectoplasm .components-popover input[type="checkbox"]:checked, body.admin-color-ectoplasm .components-popover input[type="radio"]:checked, body.admin-color-ectoplasm .components-modal__content input[type="checkbox"]:checked, body.admin-color-ectoplasm .components-modal__content input[type="radio"]:checked {
    background: #a7b656;
    border-color: #a7b656; }
  body.admin-color-coffee .editor-post-permalink input[type="checkbox"]:checked, body.admin-color-coffee .editor-post-permalink input[type="radio"]:checked, body.admin-color-coffee .edit-post-sidebar input[type="checkbox"]:checked, body.admin-color-coffee .edit-post-sidebar input[type="radio"]:checked, body.admin-color-coffee .editor-post-publish-panel input[type="checkbox"]:checked, body.admin-color-coffee .editor-post-publish-panel input[type="radio"]:checked, body.admin-color-coffee .editor-block-list__block input[type="checkbox"]:checked, body.admin-color-coffee .editor-block-list__block input[type="radio"]:checked, body.admin-color-coffee .components-popover input[type="checkbox"]:checked, body.admin-color-coffee .components-popover input[type="radio"]:checked, body.admin-color-coffee .components-modal__content input[type="checkbox"]:checked, body.admin-color-coffee .components-modal__content input[type="radio"]:checked {
    background: #c2a68c;
    border-color: #c2a68c; }
  body.admin-color-blue .editor-post-permalink input[type="checkbox"]:checked, body.admin-color-blue .editor-post-permalink input[type="radio"]:checked, body.admin-color-blue .edit-post-sidebar input[type="checkbox"]:checked, body.admin-color-blue .edit-post-sidebar input[type="radio"]:checked, body.admin-color-blue .editor-post-publish-panel input[type="checkbox"]:checked, body.admin-color-blue .editor-post-publish-panel input[type="radio"]:checked, body.admin-color-blue .editor-block-list__block input[type="checkbox"]:checked, body.admin-color-blue .editor-block-list__block input[type="radio"]:checked, body.admin-color-blue .components-popover input[type="checkbox"]:checked, body.admin-color-blue .components-popover input[type="radio"]:checked, body.admin-color-blue .components-modal__content input[type="checkbox"]:checked, body.admin-color-blue .components-modal__content input[type="radio"]:checked {
    background: #82b4cb;
    border-color: #82b4cb; }
  body.admin-color-light .editor-post-permalink input[type="checkbox"]:checked, body.admin-color-light .editor-post-permalink input[type="radio"]:checked, body.admin-color-light .edit-post-sidebar input[type="checkbox"]:checked, body.admin-color-light .edit-post-sidebar input[type="radio"]:checked, body.admin-color-light .editor-post-publish-panel input[type="checkbox"]:checked, body.admin-color-light .editor-post-publish-panel input[type="radio"]:checked, body.admin-color-light .editor-block-list__block input[type="checkbox"]:checked, body.admin-color-light .editor-block-list__block input[type="radio"]:checked, body.admin-color-light .components-popover input[type="checkbox"]:checked, body.admin-color-light .components-popover input[type="radio"]:checked, body.admin-color-light .components-modal__content input[type="checkbox"]:checked, body.admin-color-light .components-modal__content input[type="radio"]:checked {
    background: #11a0d2;
    border-color: #11a0d2; }
  .editor-post-permalink input[type="checkbox"]:checked:focus,
  .editor-post-permalink input[type="radio"]:checked:focus,
  .edit-post-sidebar input[type="checkbox"]:checked:focus,
  .edit-post-sidebar input[type="radio"]:checked:focus,
  .editor-post-publish-panel input[type="checkbox"]:checked:focus,
  .editor-post-publish-panel input[type="radio"]:checked:focus,
  .editor-block-list__block input[type="checkbox"]:checked:focus,
  .editor-block-list__block input[type="radio"]:checked:focus,
  .components-popover input[type="checkbox"]:checked:focus,
  .components-popover input[type="radio"]:checked:focus,
  .components-modal__content input[type="checkbox"]:checked:focus,
  .components-modal__content input[type="radio"]:checked:focus {
    box-shadow: 0 0 0 2px #555d66; }

.editor-post-permalink input[type="checkbox"],
.edit-post-sidebar input[type="checkbox"],
.editor-post-publish-panel input[type="checkbox"],
.editor-block-list__block input[type="checkbox"],
.components-popover input[type="checkbox"],
.components-modal__content input[type="checkbox"] {
  border-radius: 2px; }
  .editor-post-permalink input[type="checkbox"]:checked::before,
  .edit-post-sidebar input[type="checkbox"]:checked::before,
  .editor-post-publish-panel input[type="checkbox"]:checked::before,
  .editor-block-list__block input[type="checkbox"]:checked::before,
  .components-popover input[type="checkbox"]:checked::before,
  .components-modal__content input[type="checkbox"]:checked::before {
    margin: -4px -5px 0 0;
    color: #fff; }

.editor-post-permalink input[type="radio"],
.edit-post-sidebar input[type="radio"],
.editor-post-publish-panel input[type="radio"],
.editor-block-list__block input[type="radio"],
.components-popover input[type="radio"],
.components-modal__content input[type="radio"] {
  border-radius: 50%; }
  .editor-post-permalink input[type="radio"]:checked::before,
  .edit-post-sidebar input[type="radio"]:checked::before,
  .editor-post-publish-panel input[type="radio"]:checked::before,
  .editor-block-list__block input[type="radio"]:checked::before,
  .components-popover input[type="radio"]:checked::before,
  .components-modal__content input[type="radio"]:checked::before {
    margin: 3px 3px 0 0;
    background-color: #fff; }

.editor-post-title input::-webkit-input-placeholder,
.editor-post-title textarea::-webkit-input-placeholder,
.editor-block-list__block input::-webkit-input-placeholder,
.editor-block-list__block textarea::-webkit-input-placeholder {
  color: rgba(14, 28, 46, 0.62); }

.editor-post-title input::-moz-placeholder,
.editor-post-title textarea::-moz-placeholder,
.editor-block-list__block input::-moz-placeholder,
.editor-block-list__block textarea::-moz-placeholder {
  opacity: 1;
  color: rgba(14, 28, 46, 0.62); }

.editor-post-title input:-ms-input-placeholder,
.editor-post-title textarea:-ms-input-placeholder,
.editor-block-list__block input:-ms-input-placeholder,
.editor-block-list__block textarea:-ms-input-placeholder {
  color: rgba(14, 28, 46, 0.62); }

.is-dark-theme .editor-post-title input::-webkit-input-placeholder, .is-dark-theme
.editor-post-title textarea::-webkit-input-placeholder, .is-dark-theme
.editor-block-list__block input::-webkit-input-placeholder, .is-dark-theme
.editor-block-list__block textarea::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.65); }

.is-dark-theme .editor-post-title input::-moz-placeholder, .is-dark-theme
.editor-post-title textarea::-moz-placeholder, .is-dark-theme
.editor-block-list__block input::-moz-placeholder, .is-dark-theme
.editor-block-list__block textarea::-moz-placeholder {
  opacity: 1;
  color: rgba(255, 255, 255, 0.65); }

.is-dark-theme .editor-post-title input:-ms-input-placeholder, .is-dark-theme
.editor-post-title textarea:-ms-input-placeholder, .is-dark-theme
.editor-block-list__block input:-ms-input-placeholder, .is-dark-theme
.editor-block-list__block textarea:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.65); }

.wp-block {
  max-width: 610px; }
  .wp-block[data-align="wide"] {
    max-width: 1100px; }
  .wp-block[data-align="full"] {
    max-width: none; }
