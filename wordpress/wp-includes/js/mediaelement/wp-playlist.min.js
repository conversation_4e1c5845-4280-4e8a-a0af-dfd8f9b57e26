!function(a,b,c){"use strict";function d(){a(".wp-playlist:not(:has(.mejs-container))").each(function(){new e({el:this})})}window.wp=window.wp||{};var e=c.View.extend({initialize:function(d){this.index=0,this.settings={},this.data=d.metadata||a.parseJSON(this.$("script.wp-playlist-script").html()),this.playerNode=this.$(this.data.type),this.tracks=new c.Collection(this.data.tracks),this.current=this.tracks.first(),"audio"===this.data.type&&(this.currentTemplate=wp.template("wp-playlist-current-item"),this.currentNode=this.$(".wp-playlist-current-item")),this.renderCurrent(),this.data.tracklist&&(this.itemTemplate=wp.template("wp-playlist-item"),this.playingClass="wp-playlist-playing",this.renderTracks()),this.playerNode.attr("src",this.current.get("src")),b.bindAll(this,"bindPlayer","bindResetPlayer","setPlayer","ended","clickTrack"),b.isUndefined(window._wpmejsSettings)||(this.settings=b.clone(_wpmejsSettings)),this.settings.success=this.bindPlayer,this.setPlayer()},bindPlayer:function(a){this.mejs=a,this.mejs.addEventListener("ended",this.ended)},bindResetPlayer:function(a){this.bindPlayer(a),this.playCurrentSrc()},setPlayer:function(a){this.player&&(this.player.pause(),this.player.remove(),this.playerNode=this.$(this.data.type)),a&&(this.playerNode.attr("src",this.current.get("src")),this.settings.success=this.bindResetPlayer),this.player=new MediaElementPlayer(this.playerNode.get(0),this.settings)},playCurrentSrc:function(){this.renderCurrent(),this.mejs.setSrc(this.playerNode.attr("src")),this.mejs.load(),this.mejs.play()},renderCurrent:function(){var a,b="wp-includes/images/media/video.png";"video"===this.data.type?(this.data.images&&this.current.get("image")&&-1===this.current.get("image").src.indexOf(b)&&this.playerNode.attr("poster",this.current.get("image").src),a=this.current.get("dimensions").resized,this.playerNode.attr(a)):(this.data.images||this.current.set("image",!1),this.currentNode.html(this.currentTemplate(this.current.toJSON())))},renderTracks:function(){var b=this,c=1,d=a('<div class="wp-playlist-tracks"></div>');this.tracks.each(function(a){b.data.images||a.set("image",!1),a.set("artists",b.data.artists),a.set("index",!!b.data.tracknumbers&&c),d.append(b.itemTemplate(a.toJSON())),c+=1}),this.$el.append(d),this.$(".wp-playlist-item").eq(0).addClass(this.playingClass)},events:{"click .wp-playlist-item":"clickTrack","click .wp-playlist-next":"next","click .wp-playlist-prev":"prev"},clickTrack:function(a){a.preventDefault(),this.index=this.$(".wp-playlist-item").index(a.currentTarget),this.setCurrent()},ended:function(){this.index+1<this.tracks.length?this.next():(this.index=0,this.setCurrent())},next:function(){this.index=this.index+1>=this.tracks.length?0:this.index+1,this.setCurrent()},prev:function(){this.index=this.index-1<0?this.tracks.length-1:this.index-1,this.setCurrent()},loadCurrent:function(){var a=this.playerNode.attr("src")&&this.playerNode.attr("src").split(".").pop(),b=this.current.get("src").split(".").pop();this.mejs&&this.mejs.pause(),a!==b?this.setPlayer(!0):(this.playerNode.attr("src",this.current.get("src")),this.playCurrentSrc())},setCurrent:function(){this.current=this.tracks.at(this.index),this.data.tracklist&&this.$(".wp-playlist-item").removeClass(this.playingClass).eq(this.index).addClass(this.playingClass),this.loadCurrent()}});window.wp.playlist={initialize:d},a(document).ready(d),window.WPPlaylistView=e}(jQuery,_,Backbone);