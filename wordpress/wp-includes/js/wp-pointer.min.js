!function(a){var b=0,c=9999;a.widget("wp.pointer",{options:{pointerClass:"wp-pointer",pointerWidth:320,content:function(){return a(this).text()},buttons:function(b,c){var d=wpPointerL10n?wpPointerL10n.dismiss:"Dismiss",e=a('<a class="close" href="#">'+d+"</a>");return e.bind("click.pointer",function(a){a.preventDefault(),c.element.pointer("close")})},position:"top",show:function(a,b){b.pointer.show(),b.opened()},hide:function(a,b){b.pointer.hide(),b.closed()},document:document},_create:function(){var c,d;this.content=a('<div class="wp-pointer-content"></div>'),this.arrow=a('<div class="wp-pointer-arrow"><div class="wp-pointer-arrow-inner"></div></div>'),d=this.element.parents().add(this.element),c="absolute",d.filter(function(){return"fixed"===a(this).css("position")}).length&&(c="fixed"),this.pointer=a("<div />").append(this.content).append(this.arrow).attr("id","wp-pointer-"+b++).addClass(this.options.pointerClass).css({position:c,width:this.options.pointerWidth+"px",display:"none"}).appendTo(this.options.document.body)},_setOption:function(b,c){var d=this.options,e=this.pointer;"document"===b&&c!==d.document?e.detach().appendTo(c.body):"pointerClass"===b&&e.removeClass(d.pointerClass).addClass(c),a.Widget.prototype._setOption.apply(this,arguments),"position"===b?this.reposition():"content"===b&&this.active&&this.update()},destroy:function(){this.pointer.remove(),a.Widget.prototype.destroy.call(this)},widget:function(){return this.pointer},update:function(b){var c,d=this,e=this.options,f=a.Deferred();if(!e.disabled)return f.done(function(a){d._update(b,a)}),c="string"==typeof e.content?e.content:e.content.call(this.element[0],f.resolve,b,this._handoff()),c&&f.resolve(c),f.promise()},_update:function(a,b){var c,d=this.options;b&&(this.pointer.stop(),this.content.html(b),c=d.buttons.call(this.element[0],a,this._handoff()),c&&c.wrap('<div class="wp-pointer-buttons" />').parent().appendTo(this.content),this.reposition())},reposition:function(){var b;this.options.disabled||(b=this._processPosition(this.options.position),this.pointer.css({top:0,left:0,zIndex:c++}).show().position(a.extend({of:this.element,collision:"fit none"},b)),this.repoint())},repoint:function(){var a,b=this.options;b.disabled||(a="string"==typeof b.position?b.position:b.position.edge,this.pointer[0].className=this.pointer[0].className.replace(/wp-pointer-[^\s'"]*/,""),this.pointer.addClass("wp-pointer-"+a))},_processPosition:function(b){var c,d={top:"bottom",bottom:"top",left:"right",right:"left"};return c="string"==typeof b?{edge:b+""}:a.extend({},b),c.edge?("top"==c.edge||"bottom"==c.edge?(c.align=c.align||"left",c.at=c.at||c.align+" "+d[c.edge],c.my=c.my||c.align+" "+c.edge):(c.align=c.align||"top",c.at=c.at||d[c.edge]+" "+c.align,c.my=c.my||c.edge+" "+c.align),c):c},open:function(a){var b=this,c=this.options;this.active||c.disabled||this.element.is(":hidden")||this.update().done(function(){b._open(a)})},_open:function(a){var b=this,c=this.options;this.active||c.disabled||this.element.is(":hidden")||(this.active=!0,this._trigger("open",a,this._handoff()),this._trigger("show",a,this._handoff({opened:function(){b._trigger("opened",a,b._handoff())}})))},close:function(a){if(this.active&&!this.options.disabled){var b=this;this.active=!1,this._trigger("close",a,this._handoff()),this._trigger("hide",a,this._handoff({closed:function(){b._trigger("closed",a,b._handoff())}}))}},sendToTop:function(){this.active&&this.pointer.css("z-index",c++)},toggle:function(a){this.pointer.is(":hidden")?this.open(a):this.close(a)},_handoff:function(b){return a.extend({pointer:this.pointer,element:this.element},b)}})}(jQuery);