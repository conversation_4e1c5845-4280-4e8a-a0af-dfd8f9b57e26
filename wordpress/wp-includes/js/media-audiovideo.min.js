!function(a){function b(d){if(c[d])return c[d].exports;var e=c[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,b),e.l=!0,e.exports}var c={};return b.m=a,b.c=c,b.d=function(a,c,d){b.o(a,c)||Object.defineProperty(a,c,{enumerable:!0,get:d})},b.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},b.t=function(a,c){if(1&c&&(a=b(a)),8&c)return a;if(4&c&&"object"==typeof a&&a&&a.__esModule)return a;var d=Object.create(null);if(b.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&c&&"string"!=typeof a)for(var e in a)b.d(d,e,function(b){return a[b]}.bind(null,e));return d},b.n=function(a){var c=a&&a.__esModule?function(){return a["default"]}:function(){return a};return b.d(c,"a",c),c},b.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},b.p="",b(b.s=0)}([function(a,b,c){a.exports=c(1)},function(a,b,c){var d=wp.media,e=window._wpmejsSettings||{},f=window._wpMediaViewsL10n||{};wp.media.mixin={mejsSettings:e,removeAllPlayers:function(){var a;if(window.mejs&&window.mejs.players)for(a in window.mejs.players)window.mejs.players[a].pause(),this.removePlayer(window.mejs.players[a])},removePlayer:function(a){var b,c;if(a.options){for(b in a.options.features)if(c=a.options.features[b],a["clean"+c])try{a["clean"+c](a)}catch(d){}a.isDynamic||a.node.remove(),"html5"!==a.media.rendererName&&a.media.remove(),delete window.mejs.players[a.id],a.container.remove(),a.globalUnbind("resize",a.globalResizeCallback),a.globalUnbind("keydown",a.globalKeydownCallback),a.globalUnbind("click",a.globalClickCallback),delete a.media.player}},unsetPlayers:function(){this.players&&this.players.length&&(_.each(this.players,function(a){a.pause(),wp.media.mixin.removePlayer(a)}),this.players=[])}},wp.media.playlist=new wp.media.collection({tag:"playlist",editTitle:f.editPlaylistTitle,defaults:{id:wp.media.view.settings.post.id,style:"light",tracklist:!0,tracknumbers:!0,images:!0,artists:!0,type:"audio"}}),wp.media.audio={coerce:wp.media.coerce,defaults:{id:wp.media.view.settings.post.id,src:"",loop:!1,autoplay:!1,preload:"none",width:400},edit:function(a){var b,c=wp.shortcode.next("audio",a).shortcode;return b=wp.media({frame:"audio",state:"audio-details",metadata:_.defaults(c.attrs.named,this.defaults)})},shortcode:function(a){var b;return _.each(this.defaults,function(b,c){a[c]=this.coerce(a,c),b===a[c]&&delete a[c]},this),b=a.content,delete a.content,new wp.shortcode({tag:"audio",attrs:a,content:b})}},wp.media.video={coerce:wp.media.coerce,defaults:{id:wp.media.view.settings.post.id,src:"",poster:"",loop:!1,autoplay:!1,preload:"metadata",content:"",width:640,height:360},edit:function(a){var b,c,d=wp.shortcode.next("video",a).shortcode;return c=d.attrs.named,c.content=d.content,b=wp.media({frame:"video",state:"video-details",metadata:_.defaults(c,this.defaults)})},shortcode:function(a){var b;return _.each(this.defaults,function(b,c){a[c]=this.coerce(a,c),b===a[c]&&delete a[c]},this),b=a.content,delete a.content,new wp.shortcode({tag:"video",attrs:a,content:b})}},d.model.PostMedia=c(2),d.controller.AudioDetails=c(3),d.controller.VideoDetails=c(4),d.view.MediaFrame.MediaDetails=c(5),d.view.MediaFrame.AudioDetails=c(6),d.view.MediaFrame.VideoDetails=c(7),d.view.MediaDetails=c(8),d.view.AudioDetails=c(9),d.view.VideoDetails=c(10)},function(a,b){var c=Backbone.Model.extend({initialize:function(){this.attachment=!1},setSource:function(a){this.attachment=a,this.extension=a.get("filename").split(".").pop(),this.get("src")&&this.extension===this.get("src").split(".").pop()&&this.unset("src"),_.contains(wp.media.view.settings.embedExts,this.extension)?this.set(this.extension,this.attachment.get("url")):this.unset(this.extension)},changeAttachment:function(a){this.setSource(a),this.unset("src"),_.each(_.without(wp.media.view.settings.embedExts,this.extension),function(a){this.unset(a)},this)}});a.exports=c},function(a,b){var c,d=wp.media.controller.State,e=wp.media.view.l10n;c=d.extend({defaults:{id:"audio-details",toolbar:"audio-details",title:e.audioDetailsTitle,content:"audio-details",menu:"audio-details",router:!1,priority:60},initialize:function(a){this.media=a.media,d.prototype.initialize.apply(this,arguments)}}),a.exports=c},function(a,b){var c,d=wp.media.controller.State,e=wp.media.view.l10n;c=d.extend({defaults:{id:"video-details",toolbar:"video-details",title:e.videoDetailsTitle,content:"video-details",menu:"video-details",router:!1,priority:60},initialize:function(a){this.media=a.media,d.prototype.initialize.apply(this,arguments)}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaFrame.Select,e=wp.media.view.l10n;c=d.extend({defaults:{id:"media",url:"",menu:"media-details",content:"media-details",toolbar:"media-details",type:"link",priority:120},initialize:function(a){this.DetailsView=a.DetailsView,this.cancelText=a.cancelText,this.addText=a.addText,this.media=new wp.media.model.PostMedia(a.metadata),this.options.selection=new wp.media.model.Selection(this.media.attachment,{multiple:!1}),d.prototype.initialize.apply(this,arguments)},bindHandlers:function(){var a=this.defaults.menu;d.prototype.bindHandlers.apply(this,arguments),this.on("menu:create:"+a,this.createMenu,this),this.on("content:render:"+a,this.renderDetailsContent,this),this.on("menu:render:"+a,this.renderMenu,this),this.on("toolbar:render:"+a,this.renderDetailsToolbar,this)},renderDetailsContent:function(){var a=new this.DetailsView({controller:this,model:this.state().media,attachment:this.state().media.attachment}).render();this.content.set(a)},renderMenu:function(a){var b=this.lastState(),c=b&&b.id,d=this;a.set({cancel:{text:this.cancelText,priority:20,click:function(){c?d.setState(c):d.close()}},separateCancel:new wp.media.View({className:"separator",priority:40})})},setPrimaryButton:function(a,b){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{button:{style:"primary",text:a,priority:80,click:function(){var a=this.controller;b.call(this,a,a.state()),a.setState(a.options.state),a.reset()}}}}))},renderDetailsToolbar:function(){this.setPrimaryButton(e.update,function(a,b){a.close(),b.trigger("update",a.media.toJSON())})},renderReplaceToolbar:function(){this.setPrimaryButton(e.replace,function(a,b){var c=b.get("selection").single();a.media.changeAttachment(c),b.trigger("replace",a.media.toJSON())})},renderAddSourceToolbar:function(){this.setPrimaryButton(this.addText,function(a,b){var c=b.get("selection").single();a.media.setSource(c),b.trigger("add-source",a.media.toJSON())})}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaFrame.MediaDetails,e=wp.media.controller.MediaLibrary,f=wp.media.view.l10n;c=d.extend({defaults:{id:"audio",url:"",menu:"audio-details",content:"audio-details",toolbar:"audio-details",type:"link",title:f.audioDetailsTitle,priority:120},initialize:function(a){a.DetailsView=wp.media.view.AudioDetails,a.cancelText=f.audioDetailsCancel,a.addText=f.audioAddSourceTitle,d.prototype.initialize.call(this,a)},bindHandlers:function(){d.prototype.bindHandlers.apply(this,arguments),this.on("toolbar:render:replace-audio",this.renderReplaceToolbar,this),this.on("toolbar:render:add-audio-source",this.renderAddSourceToolbar,this)},createStates:function(){this.states.add([new wp.media.controller.AudioDetails({media:this.media}),new e({type:"audio",id:"replace-audio",title:f.audioReplaceTitle,toolbar:"replace-audio",media:this.media,menu:"audio-details"}),new e({type:"audio",id:"add-audio-source",title:f.audioAddSourceTitle,toolbar:"add-audio-source",media:this.media,menu:!1})])}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaFrame.MediaDetails,e=wp.media.controller.MediaLibrary,f=wp.media.view.l10n;c=d.extend({defaults:{id:"video",url:"",menu:"video-details",content:"video-details",toolbar:"video-details",type:"link",title:f.videoDetailsTitle,priority:120},initialize:function(a){a.DetailsView=wp.media.view.VideoDetails,a.cancelText=f.videoDetailsCancel,a.addText=f.videoAddSourceTitle,d.prototype.initialize.call(this,a)},bindHandlers:function(){d.prototype.bindHandlers.apply(this,arguments),this.on("toolbar:render:replace-video",this.renderReplaceToolbar,this),this.on("toolbar:render:add-video-source",this.renderAddSourceToolbar,this),this.on("toolbar:render:select-poster-image",this.renderSelectPosterImageToolbar,this),this.on("toolbar:render:add-track",this.renderAddTrackToolbar,this)},createStates:function(){this.states.add([new wp.media.controller.VideoDetails({media:this.media}),new e({type:"video",id:"replace-video",title:f.videoReplaceTitle,toolbar:"replace-video",media:this.media,menu:"video-details"}),new e({type:"video",id:"add-video-source",title:f.videoAddSourceTitle,toolbar:"add-video-source",media:this.media,menu:!1}),new e({type:"image",id:"select-poster-image",title:f.videoSelectPosterImageTitle,toolbar:"select-poster-image",media:this.media,menu:"video-details"}),new e({type:"text",id:"add-track",title:f.videoAddTrackTitle,toolbar:"add-track",media:this.media,menu:"video-details"})])},renderSelectPosterImageToolbar:function(){this.setPrimaryButton(f.videoSelectPosterImageTitle,function(a,b){var c=[],d=b.get("selection").single();a.media.set("poster",d.get("url")),b.trigger("set-poster-image",a.media.toJSON()),_.each(wp.media.view.settings.embedExts,function(b){a.media.get(b)&&c.push(a.media.get(b))}),wp.ajax.send("set-attachment-thumbnail",{data:{urls:c,thumbnail_id:d.get("id")}})})},renderAddTrackToolbar:function(){this.setPrimaryButton(f.videoAddTrackTitle,function(a,b){var c=b.get("selection").single(),d=a.media.get("content");-1===d.indexOf(c.get("url"))&&(d+=['<track srclang="en" label="English" kind="subtitles" src="',c.get("url"),'" />'].join(""),a.media.set("content",d)),b.trigger("add-track",a.media.toJSON())})}}),a.exports=c},function(a,b){var c,d=wp.media.view.Settings.AttachmentDisplay,e=jQuery;c=d.extend({initialize:function(){_.bindAll(this,"success"),this.players=[],this.listenTo(this.controller,"close",wp.media.mixin.unsetPlayers),this.on("ready",this.setPlayer),this.on("media:setting:remove",wp.media.mixin.unsetPlayers,this),this.on("media:setting:remove",this.render),this.on("media:setting:remove",this.setPlayer),d.prototype.initialize.apply(this,arguments)},events:function(){return _.extend({"click .remove-setting":"removeSetting","change .content-track":"setTracks","click .remove-track":"setTracks","click .add-media-source":"addSource"},d.prototype.events)},prepare:function(){return _.defaults({model:this.model.toJSON()},this.options)},removeSetting:function(a){var b,c=e(a.currentTarget).parent();b=c.find("input").data("setting"),b&&(this.model.unset(b),this.trigger("media:setting:remove",this)),c.remove()},setTracks:function(){var a="";_.each(this.$(".content-track"),function(b){a+=e(b).val()}),this.model.set("content",a),this.trigger("media:setting:remove",this)},addSource:function(a){this.controller.lastMime=e(a.currentTarget).data("mime"),this.controller.setState("add-"+this.controller.defaults.id+"-source")},loadPlayer:function(){this.players.push(new MediaElementPlayer(this.media,this.settings)),this.scriptXhr=!1},setPlayer:function(){var a;this.players.length||!this.media||this.scriptXhr||(a=this.model.get("src"),a&&a.indexOf("vimeo")>-1&&!("Vimeo"in window)?this.scriptXhr=e.getScript("https://player.vimeo.com/api/player.js",_.bind(this.loadPlayer,this)):this.loadPlayer())},setMedia:function(){return this},success:function(a){var b=a.attributes.autoplay&&"false"!==a.attributes.autoplay;"flash"===a.pluginType&&b&&a.addEventListener("canplay",function(){a.play()},!1),this.mejs=a},render:function(){return d.prototype.render.apply(this,arguments),setTimeout(_.bind(function(){this.resetFocus()},this),10),this.settings=_.defaults({success:this.success},wp.media.mixin.mejsSettings),this.setMedia()},resetFocus:function(){this.$(".embed-media-settings").scrollTop(0)}},{instances:0,prepareSrc:function(a){var b=c.instances++;return _.each(e(a).find("source"),function(a){a.src=[a.src,a.src.indexOf("?")>-1?"&":"?","_=",b].join("")}),a}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaDetails;c=d.extend({className:"audio-details",template:wp.template("audio-details"),setMedia:function(){var a=this.$(".wp-audio-shortcode");return a.find("source").length?(a.is(":hidden")&&a.show(),this.media=d.prepareSrc(a.get(0))):(a.hide(),this.media=!1),this}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaDetails;c=d.extend({className:"video-details",template:wp.template("video-details"),setMedia:function(){var a=this.$(".wp-video-shortcode");return a.find("source").length?(a.is(":hidden")&&a.show(),a.hasClass("youtube-video")||a.hasClass("vimeo-video")?this.media=a.get(0):this.media=d.prepareSrc(a.get(0))):(a.hide(),this.media=!1),this}}),a.exports=c}]);