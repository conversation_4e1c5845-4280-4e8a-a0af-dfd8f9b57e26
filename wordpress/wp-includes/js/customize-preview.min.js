!function(a,b){var c,d=wp.customize,e={};!function(a){var c;a.replaceState&&(c=function(a){var c,e,f;return c=document.createElement("a"),c.href=a,e=d.utils.parseQueryString(location.search.substr(1)),f=d.utils.parseQueryString(c.search.substr(1)),f.customize_changeset_uuid=e.customize_changeset_uuid,e.customize_autosaved&&(f.customize_autosaved="on"),e.customize_theme&&(f.customize_theme=e.customize_theme),e.customize_messenger_channel&&(f.customize_messenger_channel=e.customize_messenger_channel),c.search=b.param(f),c.href},a.replaceState=function(b){return function(d,f,g){return e=d,b.call(a,d,f,"string"==typeof g&&g.length>0?c(g):g)}}(a.replaceState),a.pushState=function(b){return function(d,f,g){return e=d,b.call(a,d,f,"string"==typeof g&&g.length>0?c(g):g)}}(a.pushState),window.addEventListener("popstate",function(a){e=a.state}))}(history),c=function(a,b,c){var d;return function(){var e=arguments;c=c||this,clearTimeout(d),d=setTimeout(function(){d=null,a.apply(c,e)},b)}},d.Preview=d.Messenger.extend({initialize:function(a,e){var f=this,g=document.createElement("a");d.Messenger.prototype.initialize.call(f,a,e),g.href=f.origin(),f.add("scheme",g.protocol.replace(/:$/,"")),f.body=b(document.body),f.window=b(window),d.settings.channel&&(f.body.on("click.preview","a",function(a){f.handleLinkClick(a)}),f.body.on("submit.preview","form",function(a){f.handleFormSubmit(a)}),f.window.on("scroll.preview",c(function(){f.send("scroll",f.window.scrollTop())},200)),f.bind("scroll",function(a){f.window.scrollTop(a)}))},handleLinkClick:function(a){var c,e,f=this;if(c=b(a.target).closest("a"),!_.isUndefined(c.attr("href"))&&(e="#"===c.attr("href").substr(0,1),!e&&/^https?:$/.test(c.prop("protocol")))){if(!d.isLinkPreviewable(c[0]))return wp.a11y.speak(d.settings.l10n.linkUnpreviewable),void a.preventDefault();a.preventDefault(),a.shiftKey||f.send("url",c.prop("href"))}},handleFormSubmit:function(a){var c,e,f=this;return c=document.createElement("a"),e=b(a.target),c.href=e.prop("action"),"GET"===e.prop("method").toUpperCase()&&d.isLinkPreviewable(c)?(a.isDefaultPrevented()||(c.search.length>1&&(c.search+="&"),c.search+=e.serialize(),f.send("url",c.href)),void a.preventDefault()):(wp.a11y.speak(d.settings.l10n.formUnpreviewable),void a.preventDefault())}}),d.addLinkPreviewing=function(){var a="a[href], area[href]";b(document.body).find(a).each(function(){d.prepareLinkPreview(this)}),"undefined"!=typeof MutationObserver?(d.mutationObserver=new MutationObserver(function(c){_.each(c,function(c){b(c.target).find(a).each(function(){d.prepareLinkPreview(this)})})}),d.mutationObserver.observe(document.documentElement,{childList:!0,subtree:!0})):b(document.documentElement).on("click focus mouseover",a,function(){d.prepareLinkPreview(this)})},d.isLinkPreviewable=function(a,b){var c,e,f,g;return f=_.extend({},{allowAdminAjax:!1},b||{}),"javascript:"===a.protocol||("https:"===a.protocol||"http:"===a.protocol)&&(g=a.host.replace(/:(80|443)$/,""),e=document.createElement("a"),c=!_.isUndefined(_.find(d.settings.url.allowed,function(b){return e.href=b,e.protocol===a.protocol&&e.host.replace(/:(80|443)$/,"")===g&&0===a.pathname.indexOf(e.pathname.replace(/\/$/,""))})),!!c&&(!/\/wp-(login|signup)\.php$/.test(a.pathname)&&(/\/wp-admin\/admin-ajax\.php$/.test(a.pathname)?f.allowAdminAjax:!/\/wp-(admin|includes|content)(\/|$)/.test(a.pathname))))},d.prepareLinkPreview=function(a){var c,e=b(a);if(a.hasAttribute("href")&&!e.closest("#wpadminbar").length&&"#"!==e.attr("href").substr(0,1)&&/^https?:$/.test(a.protocol)&&(d.settings.channel&&"https"===d.preview.scheme.get()&&"http:"===a.protocol&&-1!==d.settings.url.allowedHosts.indexOf(a.host)&&(a.protocol="https:"),!e.hasClass("wp-playlist-caption"))){if(!d.isLinkPreviewable(a))return void(d.settings.channel&&e.addClass("customize-unpreviewable"));e.removeClass("customize-unpreviewable"),c=d.utils.parseQueryString(a.search.substring(1)),c.customize_changeset_uuid=d.settings.changeset.uuid,d.settings.changeset.autosaved&&(c.customize_autosaved="on"),d.settings.theme.active||(c.customize_theme=d.settings.theme.stylesheet),d.settings.channel&&(c.customize_messenger_channel=d.settings.channel),a.search=b.param(c)}},d.addRequestPreviewing=function(){var a=function(a,c,e){var f,g,h,i={};f=document.createElement("a"),f.href=a.url,d.isLinkPreviewable(f,{allowAdminAjax:!0})&&(g=d.utils.parseQueryString(f.search.substring(1)),d.each(function(a){a._dirty&&(i[a.id]=a.get())}),_.isEmpty(i)||(h=a.type.toUpperCase(),"POST"!==h&&(e.setRequestHeader("X-HTTP-Method-Override",h),g._method=h,a.type="POST"),a.data?a.data+="&":a.data="",a.data+=b.param({customized:JSON.stringify(i)})),g.customize_changeset_uuid=d.settings.changeset.uuid,d.settings.changeset.autosaved&&(g.customize_autosaved="on"),d.settings.theme.active||(g.customize_theme=d.settings.theme.stylesheet),g.customize_preview_nonce=d.settings.nonce.preview,f.search=b.param(g),a.url=f.href)};b.ajaxPrefilter(a)},d.addFormPreviewing=function(){b(document.body).find("form").each(function(){d.prepareFormPreview(this)}),"undefined"!=typeof MutationObserver&&(d.mutationObserver=new MutationObserver(function(a){_.each(a,function(a){b(a.target).find("form").each(function(){d.prepareFormPreview(this)})})}),d.mutationObserver.observe(document.documentElement,{childList:!0,subtree:!0}))},d.prepareFormPreview=function(a){var c,e={};return a.action||(a.action=location.href),c=document.createElement("a"),c.href=a.action,d.settings.channel&&"https"===d.preview.scheme.get()&&"http:"===c.protocol&&-1!==d.settings.url.allowedHosts.indexOf(c.host)&&(c.protocol="https:",a.action=c.href),"GET"===a.method.toUpperCase()&&d.isLinkPreviewable(c)?(b(a).removeClass("customize-unpreviewable"),e.customize_changeset_uuid=d.settings.changeset.uuid,d.settings.changeset.autosaved&&(e.customize_autosaved="on"),d.settings.theme.active||(e.customize_theme=d.settings.theme.stylesheet),d.settings.channel&&(e.customize_messenger_channel=d.settings.channel),_.each(e,function(c,d){var e=b(a).find('input[name="'+d+'"]');e.length?e.val(c):b(a).prepend(b("<input>",{type:"hidden",name:d,value:c}))}),void(d.settings.channel&&(a.target="_self"))):void(d.settings.channel&&b(a).addClass("customize-unpreviewable"))},d.keepAliveCurrentUrl=function(){var a=location.pathname,c=location.search.substr(1),e=null,f=["customize_theme","customize_changeset_uuid","customize_messenger_channel","customize_autosaved"];return function(){var g,h;return c===location.search.substr(1)&&a===location.pathname?void d.preview.send("keep-alive"):(g=document.createElement("a"),null===e&&(g.search=c,e=d.utils.parseQueryString(c),_.each(f,function(a){delete e[a]})),g.href=location.href,h=d.utils.parseQueryString(g.search.substr(1)),_.each(f,function(a){delete h[a]}),a===location.pathname&&_.isEqual(e,h)?d.preview.send("keep-alive"):(g.search=b.param(h),g.hash="",d.settings.url.self=g.href,d.preview.send("ready",{currentUrl:d.settings.url.self,activePanels:d.settings.activePanels,activeSections:d.settings.activeSections,activeControls:d.settings.activeControls,settingValidities:d.settings.settingValidities})),e=h,c=location.search.substr(1),void(a=location.pathname))}}(),d.settingPreviewHandlers={custom_logo:function(a){b("body").toggleClass("wp-custom-logo",!!a)},custom_css:function(a){b("#wp-custom-css").text(a)},background:function(){var a="",c={};_.each(["color","image","preset","position_x","position_y","size","repeat","attachment"],function(a){c[a]=d("background_"+a)}),b(document.body).toggleClass("custom-background",!(!c.color()&&!c.image())),c.color()&&(a+="background-color: "+c.color()+";"),c.image()&&(a+='background-image: url("'+c.image()+'");',a+="background-size: "+c.size()+";",a+="background-position: "+c.position_x()+" "+c.position_y()+";",a+="background-repeat: "+c.repeat()+";",a+="background-attachment: "+c.attachment()+";"),b("#custom-background-css").text("body.custom-background { "+a+" }")}},b(function(){var a,c,f;d.settings=window._wpCustomizeSettings,d.settings&&(d.preview=new d.Preview({url:window.location.href,channel:d.settings.channel}),d.addLinkPreviewing(),d.addRequestPreviewing(),d.addFormPreviewing(),c=function(a,b,c){var e=d(a);e?e.set(b):(c=c||!1,e=d.create(a,b,{id:a}),c&&(e._dirty=!0))},d.preview.bind("settings",function(a){b.each(a,c)}),d.preview.trigger("settings",d.settings.values),b.each(d.settings._dirty,function(a,b){var c=d(b);c&&(c._dirty=!0)}),d.preview.bind("setting",function(a){var b=!0;c.apply(null,a.concat(b))}),d.preview.bind("sync",function(a){a.settings&&a["settings-modified-while-loading"]&&_.each(_.keys(a.settings),function(b){d.has(b)&&!a["settings-modified-while-loading"][b]&&delete a.settings[b]}),b.each(a,function(a,b){d.preview.trigger(a,b)}),d.preview.send("synced")}),d.preview.bind("active",function(){d.preview.send("nonce",d.settings.nonce),d.preview.send("documentTitle",document.title),d.preview.send("scroll",b(window).scrollTop())}),f=function(a){d.settings.changeset.uuid=a,b(document.body).find("a[href], area[href]").each(function(){d.prepareLinkPreview(this)}),b(document.body).find("form").each(function(){d.prepareFormPreview(this)}),history.replaceState&&history.replaceState(e,"",location.href)},d.preview.bind("changeset-uuid",f),d.preview.bind("saved",function(a){a.next_changeset_uuid&&f(a.next_changeset_uuid),d.trigger("saved",a)}),d.preview.bind("autosaving",function(){d.settings.changeset.autosaved||(d.settings.changeset.autosaved=!0,b(document.body).find("a[href], area[href]").each(function(){d.prepareLinkPreview(this)}),b(document.body).find("form").each(function(){d.prepareFormPreview(this)}),history.replaceState&&history.replaceState(e,"",location.href))}),d.preview.bind("changeset-saved",function(a){_.each(a.saved_changeset_values,function(a,b){var c=d(b);c&&_.isEqual(c.get(),a)&&(c._dirty=!1)})}),d.preview.bind("nonce-refresh",function(a){b.extend(d.settings.nonce,a)}),d.preview.send("ready",{currentUrl:d.settings.url.self,activePanels:d.settings.activePanels,activeSections:d.settings.activeSections,activeControls:d.settings.activeControls,settingValidities:d.settings.settingValidities}),setInterval(d.keepAliveCurrentUrl,d.settings.timeouts.keepAliveSend),d.preview.bind("loading-initiated",function(){b("body").addClass("wp-customizer-unloading")}),d.preview.bind("loading-failed",function(){b("body").removeClass("wp-customizer-unloading")}),a=b.map(["color","image","preset","position_x","position_y","size","repeat","attachment"],function(a){return"background_"+a}),d.when.apply(d,a).done(function(){b.each(arguments,function(){this.bind(d.settingPreviewHandlers.background)})}),d("custom_logo",function(a){d.settingPreviewHandlers.custom_logo.call(a,a.get()),a.bind(d.settingPreviewHandlers.custom_logo)}),d("custom_css["+d.settings.theme.stylesheet+"]",function(a){a.bind(d.settingPreviewHandlers.custom_css)}),d.trigger("preview-ready"))})}(wp,jQuery);