/*!
 * jQuery UI Effects Fold 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/fold-effect/
 */
!function(a){"function"==typeof define&&define.amd?define(["jquery","./effect"],a):a(jQuery)}(function(a){return a.effects.effect.fold=function(b,c){var d,e,f=a(this),g=["position","top","bottom","left","right","height","width"],h=a.effects.setMode(f,b.mode||"hide"),i="show"===h,j="hide"===h,k=b.size||15,l=/([0-9]+)%/.exec(k),m=!!b.horizFirst,n=i!==m,o=n?["width","height"]:["height","width"],p=b.duration/2,q={},r={};a.effects.save(f,g),f.show(),d=a.effects.createWrapper(f).css({overflow:"hidden"}),e=n?[d.width(),d.height()]:[d.height(),d.width()],l&&(k=parseInt(l[1],10)/100*e[j?0:1]),i&&d.css(m?{height:0,width:k}:{height:k,width:0}),q[o[0]]=i?e[0]:k,r[o[1]]=i?e[1]:0,d.animate(q,p,b.easing).animate(r,p,b.easing,function(){j&&f.hide(),a.effects.restore(f,g),a.effects.removeWrapper(f),c()})}});