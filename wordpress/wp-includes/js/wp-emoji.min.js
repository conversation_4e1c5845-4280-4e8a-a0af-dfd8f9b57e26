!function(a,b){function c(){function c(){return!j.implementation.hasFeature||j.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#Image","1.1")}function d(){if(!k){if("undefined"==typeof a.twemoji){if(l>600)return;return a.clearTimeout(h),h=a.setTimeout(d,50),void l++}g=a.twemoji,k=!0,i&&new i(function(a){for(var b,c,d,g,h=a.length;h--;){if(b=a[h].addedNodes,c=a[h].removedNodes,d=b.length,1===d&&1===c.length&&3===b[0].nodeType&&"IMG"===c[0].nodeName&&b[0].data===c[0].alt&&"load-failed"===c[0].getAttribute("data-error"))return;for(;d--;){if(g=b[d],3===g.nodeType){if(!g.parentNode)continue;if(m)for(;g.nextSibling&&3===g.nextSibling.nodeType;)g.nodeValue=g.nodeValue+g.nextSibling.nodeValue,g.parentNode.removeChild(g.nextSibling);g=g.parentNode}!g||1!==g.nodeType||g.className&&"string"==typeof g.className&&g.className.indexOf("wp-exclude-emoji")!==-1||e(g.textContent)&&f(g)}}}).observe(j.body,{childList:!0,subtree:!0}),f(j.body)}}function e(a){var b=/[\u203C\u2049\u20E3\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2300\u231A\u231B\u2328\u2388\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638\u2639\u263A\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267B\u267F\u2692\u2693\u2694\u2696\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753\u2754\u2755\u2757\u2763\u2764\u2795\u2796\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05\u2B06\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]/,c=/[\uDC00-\uDFFF]/;return!!a&&(c.test(a)||b.test(a))}function f(a,d){var e;return!b.supports.everything&&g&&a&&("string"==typeof a||a.childNodes&&a.childNodes.length)?(d=d||{},e={base:c()?b.svgUrl:b.baseUrl,ext:c()?b.svgExt:b.ext,className:d.className||"emoji",callback:function(a,c){switch(a){case"a9":case"ae":case"2122":case"2194":case"2660":case"2663":case"2665":case"2666":return!1}return!(b.supports.everythingExceptFlag&&!/^1f1(?:e[6-9a-f]|f[0-9a-f])-1f1(?:e[6-9a-f]|f[0-9a-f])$/.test(a)&&!/^(1f3f3-fe0f-200d-1f308|1f3f4-200d-2620-fe0f)$/.test(a))&&"".concat(c.base,a,c.ext)},onerror:function(){g.parentNode&&(this.setAttribute("data-error","load-failed"),g.parentNode.replaceChild(j.createTextNode(g.alt),g))}},"object"==typeof d.imgAttr&&(e.attributes=function(){return d.imgAttr}),g.parse(a,e)):a}var g,h,i=a.MutationObserver||a.WebKitMutationObserver||a.MozMutationObserver,j=a.document,k=!1,l=0,m=a.navigator.userAgent.indexOf("Trident/7.0")>0;return b&&(b.DOMReady?d():b.readyCallback=d),{parse:f,test:e}}a.wp=a.wp||{},a.wp.emoji=new c}(window,window._wpemojiSettings);