this.wp=this.wp||{},this.wp.notices=function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=280)}({122:function(e,t){e.exports=function(e){return e&&e.__esModule?e:{default:e}}},175:function(e,t){e.exports=function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},176:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_STATUS=t.DEFAULT_CONTEXT=void 0;t.DEFAULT_CONTEXT="global";t.DEFAULT_STATUS="info"},2:function(e,t){!function(){e.exports=this.lodash}()},280:function(e,t,r){"use strict";r(281)},281:function(e,t,r){"use strict";var n=r(282),o=r(122);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(5),u=o(r(283)),c=n(r(290)),a=n(r(291)),f=o(r(292)),s=(0,i.registerStore)("core/notices",{reducer:u.default,actions:c,selectors:a,controls:f.default});t.default=s},282:function(e,t){e.exports=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,r):{};n.get||n.set?Object.defineProperty(t,r,n):t[r]=e[r]}return t.default=e,t}},283:function(e,t,r){"use strict";var n=r(122);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(284)),i=r(2),u=(0,n(r(288)).default)("context")(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"CREATE_NOTICE":return(0,o.default)((0,i.reject)(e,{id:t.notice.id})).concat([t.notice]);case"REMOVE_NOTICE":return(0,i.reject)(e,{id:t.id})}return e});t.default=u},284:function(e,t,r){var n=r(285),o=r(286),i=r(287);e.exports=function(e){return n(e)||o(e)||i()}},285:function(e,t){e.exports=function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}},286:function(e,t){e.exports=function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}},287:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}},288:function(e,t,r){"use strict";var n=r(122);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.onSubKey=void 0;var o=n(r(175)),i=n(r(289)),u=function(e){return function(t){return function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0,u=n[e];if(void 0===u)return r;var c=t(r[u],n);return c===r[u]?r:(0,i.default)({},r,(0,o.default)({},u,c))}}};t.onSubKey=u;var c=u;t.default=c},289:function(e,t,r){var n=r(175);e.exports=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},o=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),o.forEach(function(t){n(e,t,r[t])})}return e}},290:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createNotice=u,t.createSuccessNotice=function(e,t){return u("success",e,t)},t.createInfoNotice=function(e,t){return u("info",e,t)},t.createErrorNotice=function(e,t){return u("error",e,t)},t.createWarningNotice=function(e,t){return u("warning",e,t)},t.removeNotice=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.DEFAULT_CONTEXT;return{type:"REMOVE_NOTICE",id:e,context:t}};var n=r(2),o=r(176),i=regeneratorRuntime.mark(u);function u(){var e,t,r,u,c,a,f,s,l,d,p,v,b,y,O=arguments;return regeneratorRuntime.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(e=O.length>0&&void 0!==O[0]?O[0]:o.DEFAULT_STATUS,t=O.length>1?O[1]:void 0,r=O.length>2&&void 0!==O[2]?O[2]:{},u=r.speak,c=void 0===u||u,a=r.isDismissible,f=void 0===a||a,s=r.context,l=void 0===s?o.DEFAULT_CONTEXT:s,d=r.id,p=void 0===d?(0,n.uniqueId)(l):d,v=r.actions,b=void 0===v?[]:v,y=r.__unstableHTML,t=String(t),!c){i.next=8;break}return i.next=8,{type:"SPEAK",message:t};case 8:return i.next=10,{type:"CREATE_NOTICE",context:l,notice:{id:p,status:e,content:t,__unstableHTML:y,isDismissible:f,actions:b}};case 10:case"end":return i.stop()}},i,this)}},291:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNotices=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n.DEFAULT_CONTEXT;return e[t]||o};var n=r(176),o=[]},292:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(44),o={SPEAK:function(e){(0,n.speak)(e.message,"assertive")}};t.default=o},44:function(e,t){!function(){e.exports=this.wp.a11y}()},5:function(e,t){!function(){e.exports=this.wp.data}()}});