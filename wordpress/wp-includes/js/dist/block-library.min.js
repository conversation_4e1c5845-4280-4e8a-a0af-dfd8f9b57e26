this.wp=this.wp||{},this.wp.blockLibrary=function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=306)}({0:function(e,t){!function(){e.exports=this.wp.element}()},1:function(e,t){!function(){e.exports=this.wp.i18n}()},10:function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,"a",function(){return r})},100:function(e,t,n){(function(e,r){var o;/*! https://mths.be/punycode v1.4.1 by @mathias */!function(a){t&&t.nodeType,e&&e.nodeType;var c="object"==typeof r&&r;c.global!==c&&c.window!==c&&c.self;var i,l=2147483647,s=36,u=1,b=26,m=38,d=700,h=72,p=128,g="-",O=/^xn--/,f=/[^\x20-\x7E]/,j=/[\x2E\u3002\uFF0E\uFF61]/g,v={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},y=s-u,k=Math.floor,w=String.fromCharCode;function E(e){throw new RangeError(v[e])}function C(e,t){for(var n=e.length,r=[];n--;)r[n]=t(e[n]);return r}function _(e,t){var n=e.split("@"),r="";return n.length>1&&(r=n[0]+"@",e=n[1]),r+C((e=e.replace(j,".")).split("."),t).join(".")}function x(e){for(var t,n,r=[],o=0,a=e.length;o<a;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<a?56320==(64512&(n=e.charCodeAt(o++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),o--):r.push(t);return r}function S(e){return C(e,function(e){var t="";return e>65535&&(t+=w((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=w(e)}).join("")}function T(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function N(e,t,n){var r=0;for(e=n?k(e/d):e>>1,e+=k(e/t);e>y*b>>1;r+=s)e=k(e/y);return k(r+(y+1)*e/(e+m))}function R(e){var t,n,r,o,a,c,i,m,d,O,f,j=[],v=e.length,y=0,w=p,C=h;for((n=e.lastIndexOf(g))<0&&(n=0),r=0;r<n;++r)e.charCodeAt(r)>=128&&E("not-basic"),j.push(e.charCodeAt(r));for(o=n>0?n+1:0;o<v;){for(a=y,c=1,i=s;o>=v&&E("invalid-input"),((m=(f=e.charCodeAt(o++))-48<10?f-22:f-65<26?f-65:f-97<26?f-97:s)>=s||m>k((l-y)/c))&&E("overflow"),y+=m*c,!(m<(d=i<=C?u:i>=C+b?b:i-C));i+=s)c>k(l/(O=s-d))&&E("overflow"),c*=O;C=N(y-a,t=j.length+1,0==a),k(y/t)>l-w&&E("overflow"),w+=k(y/t),y%=t,j.splice(y++,0,w)}return S(j)}function B(e){var t,n,r,o,a,c,i,m,d,O,f,j,v,y,C,_=[];for(j=(e=x(e)).length,t=p,n=0,a=h,c=0;c<j;++c)(f=e[c])<128&&_.push(w(f));for(r=o=_.length,o&&_.push(g);r<j;){for(i=l,c=0;c<j;++c)(f=e[c])>=t&&f<i&&(i=f);for(i-t>k((l-n)/(v=r+1))&&E("overflow"),n+=(i-t)*v,t=i,c=0;c<j;++c)if((f=e[c])<t&&++n>l&&E("overflow"),f==t){for(m=n,d=s;!(m<(O=d<=a?u:d>=a+b?b:d-a));d+=s)C=m-O,y=s-O,_.push(w(T(O+C%y,0))),m=k(C/y);_.push(w(T(m,0))),a=N(n,v,r==o),n=0,++r}++n,++t}return _.join("")}i={version:"1.4.1",ucs2:{decode:x,encode:S},decode:R,encode:B,toASCII:function(e){return _(e,function(e){return f.test(e)?"xn--"+B(e):e})},toUnicode:function(e){return _(e,function(e){return O.test(e)?R(e.slice(4).toLowerCase()):e})}},void 0===(o=function(){return i}.call(t,n,t,e))||(e.exports=o)}()}).call(this,n(101)(e),n(51))},101:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},102:function(e,t,n){"use strict";e.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},103:function(e,t,n){"use strict";t.decode=t.parse=n(104),t.encode=t.stringify=n(105)},104:function(e,t,n){"use strict";function r(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,n,a){t=t||"&",n=n||"=";var c={};if("string"!=typeof e||0===e.length)return c;var i=/\+/g;e=e.split(t);var l=1e3;a&&"number"==typeof a.maxKeys&&(l=a.maxKeys);var s=e.length;l>0&&s>l&&(s=l);for(var u=0;u<s;++u){var b,m,d,h,p=e[u].replace(i,"%20"),g=p.indexOf(n);g>=0?(b=p.substr(0,g),m=p.substr(g+1)):(b=p,m=""),d=decodeURIComponent(b),h=decodeURIComponent(m),r(c,d)?o(c[d])?c[d].push(h):c[d]=[c[d],h]:c[d]=h}return c};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},105:function(e,t,n){"use strict";var r=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,n,i){return t=t||"&",n=n||"=",null===e&&(e=void 0),"object"==typeof e?a(c(e),function(c){var i=encodeURIComponent(r(c))+n;return o(e[c])?a(e[c],function(e){return i+encodeURIComponent(r(e))}).join(t):i+encodeURIComponent(r(e[c]))}).join(t):i?encodeURIComponent(r(i))+n+encodeURIComponent(r(e)):""};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function a(e,t){if(e.map)return e.map(t);for(var n=[],r=0;r<e.length;r++)n.push(t(e[r],r));return n}var c=Object.keys||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t}},11:function(e,t){!function(){e.exports=this.wp.blocks}()},12:function(e,t,n){"use strict";n.d(t,"a",function(){return a});var r=n(28),o=n(3);function a(e,t){return!t||"object"!==Object(r.a)(t)&&"function"!=typeof t?Object(o.a)(e):t}},13:function(e,t,n){"use strict";function r(e){return(r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}n.d(t,"a",function(){return r})},14:function(e,t,n){"use strict";function r(e,t){return(r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&r(e,t)}n.d(t,"a",function(){return o})},15:function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",function(){return r})},16:function(e,t){!function(){e.exports=this.wp.keycodes}()},17:function(e,t,n){var r;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var a=typeof r;if("string"===a||"number"===a)e.push(r);else if(Array.isArray(r)&&r.length){var c=o.apply(null,r);c&&e.push(c)}else if("object"===a)for(var i in r)n.call(r,i)&&r[i]&&e.push(i)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()},18:function(e,t,n){"use strict";function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}n.d(t,"a",function(){return r})},19:function(e,t,n){"use strict";var r=n(33);function o(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||Object(r.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}n.d(t,"a",function(){return o})},2:function(e,t){!function(){e.exports=this.lodash}()},20:function(e,t){!function(){e.exports=this.wp.richText}()},21:function(e,t,n){"use strict";function r(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}n.d(t,"a",function(){return r})},24:function(e,t){!function(){e.exports=this.wp.url}()},25:function(e,t,n){"use strict";var r=n(35);var o=n(36);function a(e,t){return Object(r.a)(e)||function(e,t){var n=[],r=!0,o=!1,a=void 0;try{for(var c,i=e[Symbol.iterator]();!(r=(c=i.next()).done)&&(n.push(c.value),!t||n.length!==t);r=!0);}catch(e){o=!0,a=e}finally{try{r||null==i.return||i.return()}finally{if(o)throw a}}return n}(e,t)||Object(o.a)()}n.d(t,"a",function(){return a})},28:function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e){return(o="function"==typeof Symbol&&"symbol"===r(Symbol.iterator)?function(e){return r(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":r(e)})(e)}n.d(t,"a",function(){return o})},3:function(e,t,n){"use strict";function r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n.d(t,"a",function(){return r})},30:function(e,t){!function(){e.exports=this.wp.apiFetch}()},306:function(e,t,n){"use strict";n.r(t);var r={};n.r(r),n.d(r,"name",function(){return be}),n.d(r,"settings",function(){return me});var o={};n.r(o),n.d(o,"name",function(){return $e}),n.d(o,"settings",function(){return Xe});var a={};n.r(a),n.d(a,"getLevelFromHeadingNodeName",function(){return nt}),n.d(a,"name",function(){return ct}),n.d(a,"settings",function(){return it});var c={};n.r(c),n.d(c,"name",function(){return ut}),n.d(c,"settings",function(){return bt});var i={};n.r(i),n.d(i,"name",function(){return kt}),n.d(i,"settings",function(){return Et});var l={};n.r(l),n.d(l,"name",function(){return Ct}),n.d(l,"settings",function(){return _t});var s={};n.r(s),n.d(s,"name",function(){return Nt}),n.d(s,"settings",function(){return Rt});var u={};n.r(u),n.d(u,"name",function(){return Mt}),n.d(u,"settings",function(){return Ht});var b={};n.r(b),n.d(b,"name",function(){return Vt}),n.d(b,"settings",function(){return Ut});var m={};n.r(m),n.d(m,"name",function(){return Wt}),n.d(m,"settings",function(){return qt});var d={};n.r(d),n.d(d,"name",function(){return Yt}),n.d(d,"settings",function(){return Zt});var h={};n.r(h),n.d(h,"name",function(){return Jt}),n.d(h,"settings",function(){return Xt});var p={};n.r(p),n.d(p,"name",function(){return nn}),n.d(p,"settings",function(){return on});var g={};n.r(g),n.d(g,"name",function(){return jn}),n.d(g,"settings",function(){return vn}),n.d(g,"common",function(){return yn}),n.d(g,"others",function(){return kn});var O={};n.r(O),n.d(O,"name",function(){return xn}),n.d(O,"settings",function(){return Sn});var f={};n.r(f),n.d(f,"name",function(){return Tn}),n.d(f,"settings",function(){return Nn});var j={};n.r(j),n.d(j,"name",function(){return Mn}),n.d(j,"settings",function(){return Hn});var v={};n.r(v),n.d(v,"name",function(){return Fn}),n.d(v,"settings",function(){return Vn});var y={};n.r(y),n.d(y,"name",function(){return Yn}),n.d(y,"settings",function(){return Zn});var k={};n.r(k),n.d(k,"name",function(){return tr}),n.d(k,"settings",function(){return nr});var w={};n.r(w),n.d(w,"name",function(){return or}),n.d(w,"settings",function(){return ar});var E={};n.r(E),n.d(E,"name",function(){return ir}),n.d(E,"settings",function(){return lr});var C={};n.r(C),n.d(C,"name",function(){return sr}),n.d(C,"settings",function(){return ur});var _={};n.r(_),n.d(_,"name",function(){return br}),n.d(_,"settings",function(){return mr});var x={};n.r(x),n.d(x,"name",function(){return Or}),n.d(x,"settings",function(){return fr});var S={};n.r(S),n.d(S,"name",function(){return Er}),n.d(S,"settings",function(){return Cr});var T={};n.r(T),n.d(T,"name",function(){return _r}),n.d(T,"settings",function(){return xr});var N={};n.r(N),n.d(N,"name",function(){return Tr}),n.d(N,"settings",function(){return Nr});var R={};n.r(R),n.d(R,"name",function(){return Rr}),n.d(R,"settings",function(){return Br});var B={};n.r(B),n.d(B,"name",function(){return Pr}),n.d(B,"settings",function(){return Lr});var A={};n.r(A),n.d(A,"name",function(){return Fr}),n.d(A,"settings",function(){return Vr});var I={};n.r(I),n.d(I,"name",function(){return Ur}),n.d(I,"settings",function(){return Wr});var P={};n.r(P),n.d(P,"name",function(){return qr}),n.d(P,"settings",function(){return Gr});var L={};n.r(L),n.d(L,"name",function(){return Kr}),n.d(L,"settings",function(){return $r});var M={};n.r(M),n.d(M,"name",function(){return Xr}),n.d(M,"settings",function(){return eo});var z={};n.r(z),n.d(z,"name",function(){return ro}),n.d(z,"settings",function(){return oo});var H=n(19),D=(n(79),n(11)),F=n(15),V=n(8),U=n(0),W=n(17),q=n.n(W),G=n(2),K=n(1),$=n(6),Q=n(4),Y=n(18),Z=n(10),J=n(9),X=n(12),ee=n(13),te=n(14),ne=n(3),re=n(7),oe=n(5),ae=window.getComputedStyle,ce=Object(Q.withFallbackStyles)(function(e,t){var n=t.attributes,r=n.textColor,o=n.backgroundColor,a=n.fontSize,c=n.customFontSize,i=e.querySelector('[contenteditable="true"]'),l=i?ae(i):null;return{fallbackBackgroundColor:o||!l?void 0:l.backgroundColor,fallbackTextColor:r||!l?void 0:l.color,fallbackFontSize:a||c||!l?void 0:parseInt(l.fontSize)||void 0}}),ie=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).onReplace=e.onReplace.bind(Object(ne.a)(Object(ne.a)(e))),e.toggleDropCap=e.toggleDropCap.bind(Object(ne.a)(Object(ne.a)(e))),e.splitBlock=e.splitBlock.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"onReplace",value:function(e){var t=this.props,n=t.attributes,r=t.onReplace;r(e.map(function(e,t){return 0===t&&"core/paragraph"===e.name?Object(V.a)({},e,{attributes:Object(V.a)({},n,e.attributes)}):e}))}},{key:"toggleDropCap",value:function(){var e=this.props,t=e.attributes;(0,e.setAttributes)({dropCap:!t.dropCap})}},{key:"getDropCapHelp",value:function(e){return e?Object(K.__)("Showing large initial letter."):Object(K.__)("Toggle to show a large initial letter.")}},{key:"splitBlock",value:function(e,t){for(var n=this.props,r=n.attributes,o=n.insertBlocksAfter,a=n.setAttributes,c=n.onReplace,i=arguments.length,l=new Array(i>2?i-2:0),s=2;s<i;s++)l[s-2]=arguments[s];null!==t&&l.push(Object(D.createBlock)("core/paragraph",{content:t})),l.length&&o&&o(l);var u=r.content;null===e?c([]):u!==e&&a({content:e})}},{key:"render",value:function(){var e,t=this.props,n=t.attributes,r=t.setAttributes,o=t.mergeBlocks,a=t.onReplace,c=t.className,i=t.backgroundColor,l=t.textColor,s=t.setBackgroundColor,u=t.setTextColor,b=t.fallbackBackgroundColor,m=t.fallbackTextColor,d=t.fallbackFontSize,h=t.fontSize,p=t.setFontSize,g=t.isRTL,O=n.align,f=n.content,j=n.dropCap,v=n.placeholder,y=n.direction;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.AlignmentToolbar,{value:O,onChange:function(e){r({align:e})}}),g&&Object(U.createElement)(Q.Toolbar,{controls:[{icon:"editor-ltr",title:Object(K._x)("Left to right","editor button"),isActive:"ltr"===y,onClick:function(){r({direction:"ltr"===y?void 0:"ltr"})}}]})),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Text Settings"),className:"blocks-font-size"},Object(U.createElement)($.FontSizePicker,{fallbackFontSize:d,value:h.size,onChange:p}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Drop Cap"),checked:!!j,onChange:this.toggleDropCap,help:this.getDropCapHelp})),Object(U.createElement)($.PanelColorSettings,{title:Object(K.__)("Color Settings"),initialOpen:!1,colorSettings:[{value:i.color,onChange:s,label:Object(K.__)("Background Color")},{value:l.color,onChange:u,label:Object(K.__)("Text Color")}]},Object(U.createElement)($.ContrastChecker,Object(Y.a)({textColor:l.color,backgroundColor:i.color,fallbackTextColor:m,fallbackBackgroundColor:b},{fontSize:h.size})))),Object(U.createElement)($.RichText,{identifier:"content",tagName:"p",className:q()("wp-block-paragraph",c,(e={"has-text-color":l.color,"has-background":i.color,"has-drop-cap":j},Object(F.a)(e,i.class,i.class),Object(F.a)(e,l.class,l.class),Object(F.a)(e,h.class,h.class),e)),style:{backgroundColor:i.color,color:l.color,fontSize:h.size?h.size+"px":void 0,textAlign:O,direction:y},value:f,onChange:function(e){r({content:e})},unstableOnSplit:this.splitBlock,onMerge:o,onReplace:this.onReplace,onRemove:function(){return a([])},"aria-label":f?Object(K.__)("Paragraph block"):Object(K.__)("Empty block; start writing or type forward slash to choose a block"),placeholder:v||Object(K.__)("Start writing or type / to choose a block")}))}}]),t}(U.Component),le=Object(re.compose)([Object($.withColors)("backgroundColor",{textColor:"color"}),Object($.withFontSizes)("fontSize"),ce,Object(oe.withSelect)(function(e){return{isRTL:(0,e("core/editor").getEditorSettings)().isRTL}})])(ie),se={className:!1},ue={content:{type:"string",source:"html",selector:"p",default:""},align:{type:"string"},dropCap:{type:"boolean",default:!1},placeholder:{type:"string"},textColor:{type:"string"},customTextColor:{type:"string"},backgroundColor:{type:"string"},customBackgroundColor:{type:"string"},fontSize:{type:"string"},customFontSize:{type:"number"},direction:{type:"string",enum:["ltr","rtl"]}},be="core/paragraph",me={title:Object(K.__)("Paragraph"),description:Object(K.__)("Start with the building block of all narrative."),icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M11 5v7H9.5C7.6 12 6 10.4 6 8.5S7.6 5 9.5 5H11m8-2H9.5C6.5 3 4 5.5 4 8.5S6.5 14 9.5 14H11v7h2V5h2v16h2V5h2V3z"})),category:"common",keywords:[Object(K.__)("text")],supports:se,attributes:ue,transforms:{from:[{type:"raw",priority:20,selector:"p",schema:{p:{children:Object(D.getPhrasingContentSchema)()}}}]},deprecated:[{supports:se,attributes:Object(V.a)({},ue,{width:{type:"string"}}),save:function(e){var t,n=e.attributes,r=n.width,o=n.align,a=n.content,c=n.dropCap,i=n.backgroundColor,l=n.textColor,s=n.customBackgroundColor,u=n.customTextColor,b=n.fontSize,m=n.customFontSize,d=Object($.getColorClassName)("color",l),h=Object($.getColorClassName)("background-color",i),p=b&&"is-".concat(b,"-text"),g=q()((t={},Object(F.a)(t,"align".concat(r),r),Object(F.a)(t,"has-background",i||s),Object(F.a)(t,"has-drop-cap",c),Object(F.a)(t,p,p),Object(F.a)(t,d,d),Object(F.a)(t,h,h),t)),O={backgroundColor:h?void 0:s,color:d?void 0:u,fontSize:p?void 0:m,textAlign:o};return Object(U.createElement)($.RichText.Content,{tagName:"p",style:O,className:g||void 0,value:a})}},{supports:se,attributes:Object(G.omit)(Object(V.a)({},ue,{fontSize:{type:"number"}}),"customFontSize","customTextColor","customBackgroundColor"),save:function(e){var t,n=e.attributes,r=n.width,o=n.align,a=n.content,c=n.dropCap,i=n.backgroundColor,l=n.textColor,s=n.fontSize,u=q()((t={},Object(F.a)(t,"align".concat(r),r),Object(F.a)(t,"has-background",i),Object(F.a)(t,"has-drop-cap",c),t)),b={backgroundColor:i,color:l,fontSize:s,textAlign:o};return Object(U.createElement)("p",{style:b,className:u||void 0},a)},migrate:function(e){return Object(G.omit)(Object(V.a)({},e,{customFontSize:Object(G.isFinite)(e.fontSize)?e.fontSize:void 0,customTextColor:e.textColor&&"#"===e.textColor[0]?e.textColor:void 0,customBackgroundColor:e.backgroundColor&&"#"===e.backgroundColor[0]?e.backgroundColor:void 0}),["fontSize","textColor","backgroundColor"])}},{supports:se,attributes:Object(V.a)({},ue,{content:{type:"string",source:"html",default:""}}),save:function(e){var t=e.attributes;return Object(U.createElement)(U.RawHTML,null,t.content)},migrate:function(e){return e}}],merge:function(e,t){return{content:e.content+t.content}},getEditWrapperProps:function(e){var t=e.width;if(-1!==["wide","full","left","right"].indexOf(t))return{"data-align":t}},edit:le,save:function(e){var t,n=e.attributes,r=n.align,o=n.content,a=n.dropCap,c=n.backgroundColor,i=n.textColor,l=n.customBackgroundColor,s=n.customTextColor,u=n.fontSize,b=n.customFontSize,m=n.direction,d=Object($.getColorClassName)("color",i),h=Object($.getColorClassName)("background-color",c),p=Object($.getFontSizeClass)(u),g=q()((t={"has-text-color":i||s,"has-background":c||l,"has-drop-cap":a},Object(F.a)(t,p,p),Object(F.a)(t,d,d),Object(F.a)(t,h,h),t)),O={backgroundColor:h?void 0:l,color:d?void 0:s,fontSize:p?void 0:b,textAlign:r};return Object(U.createElement)($.RichText.Content,{tagName:"p",style:O,className:g||void 0,value:o,dir:m})}},de=n(32),he=n(25),pe=n(24),ge=n(37),Oe=Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"M19,4H5C3.89,4,3,4.9,3,6v12c0,1.1,0.89,2,2,2h14c1.1,0,2-0.9,2-2V6C21,4.9,20.11,4,19,4z M19,18H5V8h14V18z"})),fe=Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.Path,{d:"M21 3H3L1 5v14l2 2h18l2-2V5l-2-2zm0 16H3V5h18v14zM8 15a3 3 0 0 1 4-3V6h5v2h-3v7a3 3 0 0 1-6 0z"})),je=Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"M21,4H3C1.9,4,1,4.9,1,6v12c0,1.1,0.9,2,2,2h18c1.1,0,2-0.9,2-2V6C23,4.9,22.1,4,21,4z M21,18H3V6h18V18z"}),Object(U.createElement)(Q.Polygon,{points:"14.5 11 11 15.51 8.5 12.5 5 17 19 17"})),ve=Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"m10 8v8l5-4-5-4zm9-5h-14c-1.1 0-2 0.9-2 2v14c0 1.1 0.9 2 2 2h14c1.1 0 2-0.9 2-2v-14c0-1.1-0.9-2-2-2zm0 16h-14v-14h14v14z"})),ye={foreground:"#1da1f2",src:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M22.23 5.924c-.736.326-1.527.547-2.357.646.847-.508 1.498-1.312 1.804-2.27-.793.47-1.67.812-2.606.996C18.325 4.498 17.258 4 16.078 4c-2.266 0-4.103 1.837-4.103 4.103 0 .322.036.635.106.935-3.41-.17-6.433-1.804-8.457-4.287-.353.607-.556 1.312-.556 2.064 0 1.424.724 2.68 1.825 3.415-.673-.022-1.305-.207-1.86-.514v.052c0 1.988 1.415 3.647 3.293 4.023-.344.095-.707.145-1.08.145-.265 0-.522-.026-.773-.074.522 1.63 2.038 2.817 3.833 2.85-1.404 1.1-3.174 1.757-5.096 1.757-.332 0-.66-.02-.98-.057 1.816 1.164 3.973 1.843 6.29 1.843 7.547 0 11.675-6.252 11.675-11.675 0-.178-.004-.355-.012-.53.802-.578 1.497-1.3 2.047-2.124z"})))},ke={foreground:"#ff0000",src:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M21.8 8s-.195-1.377-.795-1.984c-.76-.797-1.613-.8-2.004-.847-2.798-.203-6.996-.203-6.996-.203h-.01s-4.197 0-6.996.202c-.39.046-1.242.05-2.003.846C2.395 6.623 2.2 8 2.2 8S2 9.62 2 11.24v1.517c0 1.618.2 3.237.2 3.237s.195 1.378.795 1.985c.76.797 1.76.77 2.205.855 1.6.153 6.8.2 6.8.2s4.203-.005 7-.208c.392-.047 1.244-.05 2.005-.847.6-.607.795-1.985.795-1.985s.2-1.618.2-3.237v-1.517C22 9.62 21.8 8 21.8 8zM9.935 14.595v-5.62l5.403 2.82-5.403 2.8z"}))},we={foreground:"#3b5998",src:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M20 3H4c-.6 0-1 .4-1 1v16c0 .5.4 1 1 1h8.6v-7h-2.3v-2.7h2.3v-2c0-2.3 1.4-3.6 3.5-3.6 1 0 1.8.1 2.1.1v2.4h-1.4c-1.1 0-1.3.5-1.3 1.3v1.7h2.7l-.4 2.8h-2.3v7H20c.5 0 1-.4 1-1V4c0-.6-.4-1-1-1z"}))},Ee=Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M12 4.622c2.403 0 2.688.01 3.637.052.877.04 1.354.187 1.67.31.42.163.72.358 1.036.673.315.315.51.615.673 1.035.123.317.27.794.31 1.67.043.95.052 1.235.052 3.638s-.01 2.688-.052 3.637c-.04.877-.187 1.354-.31 1.67-.163.42-.358.72-.673 1.036-.315.315-.615.51-1.035.673-.317.123-.794.27-1.67.31-.95.043-1.234.052-3.638.052s-2.688-.01-3.637-.052c-.877-.04-1.354-.187-1.67-.31-.42-.163-.72-.358-1.036-.673-.315-.315-.51-.615-.673-1.035-.123-.317-.27-.794-.31-1.67-.043-.95-.052-1.235-.052-3.638s.01-2.688.052-3.637c.04-.877.187-1.354.31-1.67.163-.42.358-.72.673-1.036.315-.315.615-.51 1.035-.673.317-.123.794-.27 1.67-.31.95-.043 1.235-.052 3.638-.052M12 3c-2.444 0-2.75.01-3.71.054s-1.613.196-2.185.418c-.592.23-1.094.538-1.594 1.04-.5.5-.807 1-1.037 1.593-.223.572-.375 1.226-.42 2.184C3.01 9.25 3 9.555 3 12s.01 2.75.054 3.71.196 1.613.418 2.186c.23.592.538 1.094 1.038 1.594s1.002.808 1.594 1.038c.572.222 1.227.375 2.185.418.96.044 1.266.054 3.71.054s2.75-.01 3.71-.054 1.613-.196 2.186-.418c.592-.23 1.094-.538 1.594-1.038s.808-1.002 1.038-1.594c.222-.572.375-1.227.418-2.185.044-.96.054-1.266.054-3.71s-.01-2.75-.054-3.71-.196-1.613-.418-2.186c-.23-.592-.538-1.094-1.038-1.594s-1.002-.808-1.594-1.038c-.572-.222-1.227-.375-2.185-.418C14.75 3.01 14.445 3 12 3zm0 4.378c-2.552 0-4.622 2.07-4.622 4.622s2.07 4.622 4.622 4.622 4.622-2.07 4.622-4.622S14.552 7.378 12 7.378zM12 15c-1.657 0-3-1.343-3-3s1.343-3 3-3 3 1.343 3 3-1.343 3-3 3zm4.804-8.884c-.596 0-1.08.484-1.08 1.08s.484 1.08 1.08 1.08c.596 0 1.08-.484 1.08-1.08s-.483-1.08-1.08-1.08z"}))),Ce={foreground:"#0073AA",src:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M12.158 12.786l-2.698 7.84c.806.236 1.657.365 2.54.365 1.047 0 2.05-.18 2.986-.51-.024-.037-.046-.078-.065-.123l-2.762-7.57zM3.008 12c0 3.56 2.07 6.634 5.068 8.092L3.788 8.342c-.5 1.117-.78 2.354-.78 3.658zm15.06-.454c0-1.112-.398-1.88-.74-2.48-.456-.74-.883-1.368-.883-2.11 0-.825.627-1.595 1.51-1.595.04 0 .078.006.116.008-1.598-1.464-3.73-2.36-6.07-2.36-3.14 0-5.904 1.613-7.512 4.053.21.008.41.012.58.012.94 0 2.395-.114 2.395-.114.484-.028.54.684.057.74 0 0-.487.058-1.03.086l3.275 9.74 1.968-5.902-1.4-3.838c-.485-.028-.944-.085-.944-.085-.486-.03-.43-.77.056-.742 0 0 1.484.114 2.368.114.94 0 2.397-.114 2.397-.114.486-.028.543.684.058.74 0 0-.488.058-1.03.086l3.25 9.665.897-2.997c.456-1.17.684-2.137.684-2.907zm1.82-3.86c.04.286.06.593.06.924 0 .912-.17 1.938-.683 3.22l-2.746 7.94c2.672-1.558 4.47-4.454 4.47-7.77 0-1.564-.4-3.033-1.1-4.314zM12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10z"})))},_e={foreground:"#1db954",src:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m4.586 14.424c-.18.295-.563.387-.857.207-2.35-1.434-5.305-1.76-8.786-.963-.335.077-.67-.133-.746-.47-.077-.334.132-.67.47-.745 3.808-.87 7.076-.496 9.712 1.115.293.18.386.563.206.857M17.81 13.7c-.226.367-.706.482-1.072.257-2.687-1.652-6.785-2.13-9.965-1.166-.413.127-.848-.106-.973-.517-.125-.413.108-.848.52-.973 3.632-1.102 8.147-.568 11.234 1.328.366.226.48.707.256 1.072m.105-2.835C14.692 8.95 9.375 8.775 6.297 9.71c-.493.15-1.016-.13-1.166-.624-.148-.495.13-1.017.625-1.167 3.532-1.073 9.404-.866 13.115 1.337.445.264.59.838.327 1.282-.264.443-.838.59-1.282.325"}))},xe=Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"m6.5 7c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5zm11 0c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5z"})),Se={foreground:"#1ab7ea",src:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M22.396 7.164c-.093 2.026-1.507 4.8-4.245 8.32C15.323 19.16 12.93 21 10.97 21c-1.214 0-2.24-1.12-3.08-3.36-.56-2.052-1.118-4.105-1.68-6.158-.622-2.24-1.29-3.36-2.004-3.36-.156 0-.7.328-1.634.98l-.978-1.26c1.027-.903 2.04-1.806 3.037-2.71C6 3.95 7.03 3.328 7.716 3.265c1.62-.156 2.616.95 2.99 3.32.404 2.558.685 4.148.84 4.77.468 2.12.982 3.18 1.543 3.18.435 0 1.09-.687 1.963-2.064.872-1.376 1.34-2.422 1.402-3.142.125-1.187-.343-1.782-1.4-1.782-.5 0-1.013.115-1.542.34 1.023-3.35 2.977-4.976 5.862-4.883 2.14.063 3.148 1.45 3.024 4.16z"})))},Te=Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M22 11.816c0-1.256-1.02-2.277-2.277-2.277-.593 0-1.122.24-1.526.613-1.48-.965-3.455-1.594-5.647-1.69l1.17-3.702 3.18.75c.01 1.027.847 1.86 1.877 1.86 1.035 0 1.877-.84 1.877-1.877 0-1.035-.842-1.877-1.877-1.877-.77 0-1.43.466-1.72 1.13L13.55 3.92c-.204-.047-.4.067-.46.26l-1.35 4.27c-2.317.037-4.412.67-5.97 1.67-.402-.355-.917-.58-1.493-.58C3.02 9.54 2 10.56 2 11.815c0 .814.433 1.523 1.078 1.925-.037.222-.06.445-.06.673 0 3.292 4.01 5.97 8.94 5.97s8.94-2.678 8.94-5.97c0-.214-.02-.424-.052-.632.687-.39 1.154-1.12 1.154-1.964zm-3.224-7.422c.606 0 1.1.493 1.1 1.1s-.493 1.1-1.1 1.1-1.1-.494-1.1-1.1.493-1.1 1.1-1.1zm-16 7.422c0-.827.673-1.5 1.5-1.5.313 0 .598.103.838.27-.85.675-1.477 1.478-1.812 2.36-.32-.274-.525-.676-.525-1.13zm9.183 7.79c-4.502 0-8.165-2.33-8.165-5.193S7.457 9.22 11.96 9.22s8.163 2.33 8.163 5.193-3.663 5.193-8.164 5.193zM20.635 13c-.326-.89-.948-1.7-1.797-2.383.247-.186.55-.3.882-.3.827 0 1.5.672 1.5 1.5 0 .482-.23.91-.586 1.184zm-11.64 1.704c-.76 0-1.397-.616-1.397-1.376 0-.76.636-1.397 1.396-1.397.76 0 1.376.638 1.376 1.398 0 .76-.616 1.376-1.376 1.376zm7.405-1.376c0 .76-.615 1.376-1.375 1.376s-1.4-.616-1.4-1.376c0-.76.64-1.397 1.4-1.397.76 0 1.376.638 1.376 1.398zm-1.17 3.38c.15.152.15.398 0 .55-.675.674-1.728 1.002-3.22 1.002l-.01-.002-.012.002c-1.492 0-2.544-.328-3.218-1.002-.152-.152-.152-.398 0-.55.152-.152.4-.15.55 0 .52.52 1.394.775 2.67.775l.01.002.01-.002c1.276 0 2.15-.253 2.67-.775.15-.152.398-.152.55 0z"})),Ne={foreground:"#35465c",src:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M19 3H5c-1.105 0-2 .895-2 2v14c0 1.105.895 2 2 2h14c1.105 0 2-.895 2-2V5c0-1.105-.895-2-2-2zm-5.57 14.265c-2.445.042-3.37-1.742-3.37-2.998V10.6H8.922V9.15c1.703-.615 2.113-2.15 2.21-3.026.006-.06.053-.084.08-.084h1.645V8.9h2.246v1.7H12.85v3.495c.008.476.182 1.13 1.08 1.107.3-.008.698-.094.907-.194l.54 1.6c-.205.297-1.12.642-1.946.657z"}))},Re=[{name:"core-embed/twitter",settings:{title:"Twitter",icon:ye,keywords:["tweet"],description:Object(K.__)("Embed a tweet.")},patterns:[/^https?:\/\/(www\.)?twitter\.com\/.+/i]},{name:"core-embed/youtube",settings:{title:"YouTube",icon:ke,keywords:[Object(K.__)("music"),Object(K.__)("video")],description:Object(K.__)("Embed a YouTube video.")},patterns:[/^https?:\/\/((m|www)\.)?youtube\.com\/.+/i,/^https?:\/\/youtu\.be\/.+/i]},{name:"core-embed/facebook",settings:{title:"Facebook",icon:we,description:Object(K.__)("Embed a Facebook post.")},patterns:[/^https?:\/\/www\.facebook.com\/.+/i]},{name:"core-embed/instagram",settings:{title:"Instagram",icon:Ee,keywords:[Object(K.__)("image")],description:Object(K.__)("Embed an Instagram post.")},patterns:[/^https?:\/\/(www\.)?instagr(\.am|am\.com)\/.+/i]},{name:"core-embed/wordpress",settings:{title:"WordPress",icon:Ce,keywords:[Object(K.__)("post"),Object(K.__)("blog")],responsive:!1,description:Object(K.__)("Embed a WordPress post.")}},{name:"core-embed/soundcloud",settings:{title:"SoundCloud",icon:fe,keywords:[Object(K.__)("music"),Object(K.__)("audio")],description:Object(K.__)("Embed SoundCloud content.")},patterns:[/^https?:\/\/(www\.)?soundcloud\.com\/.+/i]},{name:"core-embed/spotify",settings:{title:"Spotify",icon:_e,keywords:[Object(K.__)("music"),Object(K.__)("audio")],description:Object(K.__)("Embed Spotify content.")},patterns:[/^https?:\/\/(open|play)\.spotify\.com\/.+/i]},{name:"core-embed/flickr",settings:{title:"Flickr",icon:xe,keywords:[Object(K.__)("image")],description:Object(K.__)("Embed Flickr content.")},patterns:[/^https?:\/\/(www\.)?flickr\.com\/.+/i,/^https?:\/\/flic\.kr\/.+/i]},{name:"core-embed/vimeo",settings:{title:"Vimeo",icon:Se,keywords:[Object(K.__)("video")],description:Object(K.__)("Embed a Vimeo video.")},patterns:[/^https?:\/\/(www\.)?vimeo\.com\/.+/i]}],Be=[{name:"core-embed/animoto",settings:{title:"Animoto",icon:ve,description:Object(K.__)("Embed an Animoto video.")},patterns:[/^https?:\/\/(www\.)?(animoto|video214)\.com\/.+/i]},{name:"core-embed/cloudup",settings:{title:"Cloudup",icon:Oe,description:Object(K.__)("Embed Cloudup content.")},patterns:[/^https?:\/\/cloudup\.com\/.+/i]},{name:"core-embed/collegehumor",settings:{title:"CollegeHumor",icon:ve,description:Object(K.__)("Embed CollegeHumor content.")},patterns:[/^https?:\/\/(www\.)?collegehumor\.com\/.+/i]},{name:"core-embed/crowdsignal",settings:{title:"Crowdsignal",icon:Oe,keywords:["polldaddy"],transform:[{type:"block",blocks:["core-embed/polldaddy"],transform:function(e){return Object(D.createBlock)("core-embed/crowdsignal",{content:e})}}],description:Object(K.__)("Embed Crowdsignal (formerly Polldaddy) content.")},patterns:[/^https?:\/\/((.+\.)?polldaddy\.com|poll\.fm|.+\.survey\.fm)\/.+/i]},{name:"core-embed/dailymotion",settings:{title:"Dailymotion",icon:ve,description:Object(K.__)("Embed a Dailymotion video.")},patterns:[/^https?:\/\/(www\.)?dailymotion\.com\/.+/i]},{name:"core-embed/funnyordie",settings:{title:"Funny or Die",icon:ve,description:Object(K.__)("Embed Funny or Die content.")},patterns:[/^https?:\/\/(www\.)?funnyordie\.com\/.+/i]},{name:"core-embed/hulu",settings:{title:"Hulu",icon:ve,description:Object(K.__)("Embed Hulu content.")},patterns:[/^https?:\/\/(www\.)?hulu\.com\/.+/i]},{name:"core-embed/imgur",settings:{title:"Imgur",icon:je,description:Object(K.__)("Embed Imgur content.")},patterns:[/^https?:\/\/(.+\.)?imgur\.com\/.+/i]},{name:"core-embed/issuu",settings:{title:"Issuu",icon:Oe,description:Object(K.__)("Embed Issuu content.")},patterns:[/^https?:\/\/(www\.)?issuu\.com\/.+/i]},{name:"core-embed/kickstarter",settings:{title:"Kickstarter",icon:Oe,description:Object(K.__)("Embed Kickstarter content.")},patterns:[/^https?:\/\/(www\.)?kickstarter\.com\/.+/i,/^https?:\/\/kck\.st\/.+/i]},{name:"core-embed/meetup-com",settings:{title:"Meetup.com",icon:Oe,description:Object(K.__)("Embed Meetup.com content.")},patterns:[/^https?:\/\/(www\.)?meetu(\.ps|p\.com)\/.+/i]},{name:"core-embed/mixcloud",settings:{title:"Mixcloud",icon:fe,keywords:[Object(K.__)("music"),Object(K.__)("audio")],description:Object(K.__)("Embed Mixcloud content.")},patterns:[/^https?:\/\/(www\.)?mixcloud\.com\/.+/i]},{name:"core-embed/photobucket",settings:{title:"Photobucket",icon:je,description:Object(K.__)("Embed a Photobucket image.")},patterns:[/^http:\/\/g?i*\.photobucket\.com\/.+/i]},{name:"core-embed/polldaddy",settings:{title:"Polldaddy",icon:Oe,description:Object(K.__)("Embed Polldaddy content."),supports:{inserter:!1}},patterns:[]},{name:"core-embed/reddit",settings:{title:"Reddit",icon:Te,description:Object(K.__)("Embed a Reddit thread.")},patterns:[/^https?:\/\/(www\.)?reddit\.com\/.+/i]},{name:"core-embed/reverbnation",settings:{title:"ReverbNation",icon:fe,description:Object(K.__)("Embed ReverbNation content.")},patterns:[/^https?:\/\/(www\.)?reverbnation\.com\/.+/i]},{name:"core-embed/screencast",settings:{title:"Screencast",icon:ve,description:Object(K.__)("Embed Screencast content.")},patterns:[/^https?:\/\/(www\.)?screencast\.com\/.+/i]},{name:"core-embed/scribd",settings:{title:"Scribd",icon:Oe,description:Object(K.__)("Embed Scribd content.")},patterns:[/^https?:\/\/(www\.)?scribd\.com\/.+/i]},{name:"core-embed/slideshare",settings:{title:"Slideshare",icon:Oe,description:Object(K.__)("Embed Slideshare content.")},patterns:[/^https?:\/\/(.+?\.)?slideshare\.net\/.+/i]},{name:"core-embed/smugmug",settings:{title:"SmugMug",icon:je,description:Object(K.__)("Embed SmugMug content.")},patterns:[/^https?:\/\/(www\.)?smugmug\.com\/.+/i]},{name:"core-embed/speaker",settings:{title:"Speaker",icon:fe,supports:{inserter:!1}},patterns:[]},{name:"core-embed/speaker-deck",settings:{title:"Speaker Deck",icon:Oe,transform:[{type:"block",blocks:["core-embed/speaker"],transform:function(e){return Object(D.createBlock)("core-embed/speaker-deck",{content:e})}}],description:Object(K.__)("Embed Speaker Deck content.")},patterns:[/^https?:\/\/(www\.)?speakerdeck\.com\/.+/i]},{name:"core-embed/ted",settings:{title:"TED",icon:ve,description:Object(K.__)("Embed a TED video.")},patterns:[/^https?:\/\/(www\.|embed\.)?ted\.com\/.+/i]},{name:"core-embed/tumblr",settings:{title:"Tumblr",icon:Ne,description:Object(K.__)("Embed a Tumblr post.")},patterns:[/^https?:\/\/(www\.)?tumblr\.com\/.+/i]},{name:"core-embed/videopress",settings:{title:"VideoPress",icon:ve,keywords:[Object(K.__)("video")],description:Object(K.__)("Embed a VideoPress video.")},patterns:[/^https?:\/\/videopress\.com\/.+/i]},{name:"core-embed/wordpress-tv",settings:{title:"WordPress.tv",icon:ve,description:Object(K.__)("Embed a WordPress.tv video.")},patterns:[/^https?:\/\/wordpress\.tv\/.+/i]}],Ae=["facebook.com"],Ie=[{ratio:"2.33",className:"wp-embed-aspect-21-9"},{ratio:"2.00",className:"wp-embed-aspect-18-9"},{ratio:"1.78",className:"wp-embed-aspect-16-9"},{ratio:"1.33",className:"wp-embed-aspect-4-3"},{ratio:"1.00",className:"wp-embed-aspect-1-1"},{ratio:"0.56",className:"wp-embed-aspect-9-16"},{ratio:"0.50",className:"wp-embed-aspect-1-2"}],Pe=n(60),Le=n.n(Pe),Me=function(e){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).some(function(t){return e.match(t)})},ze=function(e){return Object(G.includes)(e,'class="wp-embedded-content" data-secret')},He=function(e,t){var n=e.preview,r=e.name,o=e.attributes.url;if(o){var a=function(e){for(var t=Object(H.a)(Re).concat(Object(H.a)(Be)),n=0;n<t.length;n++){var r=t[n];if(Me(e,r.patterns))return r.name}return"core/embed"}(o);if("core-embed/wordpress"!==r&&"core/embed"!==a&&r!==a)return Object(D.createBlock)(a,{url:o});if(n){var c=n.html;if(ze(c)&&"core-embed/wordpress"!==r)return Object(D.createBlock)("core-embed/wordpress",Object(V.a)({url:o},t))}}};function De(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!n){for(var r={"wp-has-aspect-ratio":!1},o=0;o<Ie.length;o++){r[Ie[o].className]=!1}return Le()(t,r)}var a=document.implementation.createHTMLDocument("");a.body.innerHTML=e;var c=a.body.querySelector("iframe");if(c&&c.height&&c.width)for(var i=(c.width/c.height).toFixed(2),l=0;l<Ie.length;l++){var s,u=Ie[l];if(i>=u.ratio)return Le()(t,(s={},Object(F.a)(s,u.className,n),Object(F.a)(s,"wp-has-aspect-ratio",n),s))}return t}var Fe=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).state={width:void 0,height:void 0},e.bindContainer=e.bindContainer.bind(Object(ne.a)(Object(ne.a)(e))),e.calculateSize=e.calculateSize.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"bindContainer",value:function(e){this.container=e}},{key:"componentDidUpdate",value:function(e){this.props.src!==e.src&&(this.setState({width:void 0,height:void 0}),this.fetchImageSize()),this.props.dirtynessTrigger!==e.dirtynessTrigger&&this.calculateSize()}},{key:"componentDidMount",value:function(){this.fetchImageSize()}},{key:"componentWillUnmount",value:function(){this.image&&(this.image.onload=G.noop)}},{key:"fetchImageSize",value:function(){this.image=new window.Image,this.image.onload=this.calculateSize,this.image.src=this.props.src}},{key:"calculateSize",value:function(){var e=this.container.clientWidth,t=this.image.width>e,n=this.image.height/this.image.width,r=t?e:this.image.width,o=t?e*n:this.image.height;this.setState({width:r,height:o})}},{key:"render",value:function(){var e={imageWidth:this.image&&this.image.width,imageHeight:this.image&&this.image.height,containerWidth:this.container&&this.container.clientWidth,containerHeight:this.container&&this.container.clientHeight,imageWidthWithinContainer:this.state.width,imageHeightWithinContainer:this.state.height};return Object(U.createElement)("div",{ref:this.bindContainer},this.props.children(e))}}]),t}(U.Component),Ve=Object(re.withGlobalEvents)({resize:"calculateSize"})(Fe),Ue=["image"],We=function(e){var t=Object(G.pick)(e,["alt","id","link","caption"]);return t.url=Object(G.get)(e,["sizes","large","url"])||Object(G.get)(e,["media_details","sizes","large","source_url"])||e.url,t},qe=function(e,t){return!e&&Object(de.isBlobURL)(t)},Ge=function(e){function t(e){var n,r=e.attributes;return Object(Z.a)(this,t),(n=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).updateAlt=n.updateAlt.bind(Object(ne.a)(Object(ne.a)(n))),n.updateAlignment=n.updateAlignment.bind(Object(ne.a)(Object(ne.a)(n))),n.onFocusCaption=n.onFocusCaption.bind(Object(ne.a)(Object(ne.a)(n))),n.onImageClick=n.onImageClick.bind(Object(ne.a)(Object(ne.a)(n))),n.onSelectImage=n.onSelectImage.bind(Object(ne.a)(Object(ne.a)(n))),n.onSelectURL=n.onSelectURL.bind(Object(ne.a)(Object(ne.a)(n))),n.updateImageURL=n.updateImageURL.bind(Object(ne.a)(Object(ne.a)(n))),n.updateWidth=n.updateWidth.bind(Object(ne.a)(Object(ne.a)(n))),n.updateHeight=n.updateHeight.bind(Object(ne.a)(Object(ne.a)(n))),n.updateDimensions=n.updateDimensions.bind(Object(ne.a)(Object(ne.a)(n))),n.onSetCustomHref=n.onSetCustomHref.bind(Object(ne.a)(Object(ne.a)(n))),n.onSetLinkClass=n.onSetLinkClass.bind(Object(ne.a)(Object(ne.a)(n))),n.onSetLinkRel=n.onSetLinkRel.bind(Object(ne.a)(Object(ne.a)(n))),n.onSetLinkDestination=n.onSetLinkDestination.bind(Object(ne.a)(Object(ne.a)(n))),n.onSetNewTab=n.onSetNewTab.bind(Object(ne.a)(Object(ne.a)(n))),n.getFilename=n.getFilename.bind(Object(ne.a)(Object(ne.a)(n))),n.toggleIsEditing=n.toggleIsEditing.bind(Object(ne.a)(Object(ne.a)(n))),n.onUploadError=n.onUploadError.bind(Object(ne.a)(Object(ne.a)(n))),n.onImageError=n.onImageError.bind(Object(ne.a)(Object(ne.a)(n))),n.state={captionFocused:!1,isEditing:!r.url},n}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentDidMount",value:function(){var e=this,t=this.props,n=t.attributes,r=t.setAttributes,o=t.noticeOperations,a=n.id,c=n.url,i=void 0===c?"":c;if(qe(a,i)){var l=Object(de.getBlobByURL)(i);l&&Object($.mediaUpload)({filesList:[l],onFileChange:function(e){var t=Object(he.a)(e,1)[0];r(We(t))},allowedTypes:Ue,onError:function(t){o.createErrorNotice(t),e.setState({isEditing:!0})}})}}},{key:"componentDidUpdate",value:function(e){var t=e.attributes,n=t.id,r=t.url,o=void 0===r?"":r,a=this.props.attributes,c=a.id,i=a.url,l=void 0===i?"":i;qe(n,o)&&!qe(c,l)&&Object(de.revokeBlobURL)(l),!this.props.isSelected&&e.isSelected&&this.state.captionFocused&&this.setState({captionFocused:!1})}},{key:"onUploadError",value:function(e){this.props.noticeOperations.createErrorNotice(e),this.setState({isEditing:!0})}},{key:"onSelectImage",value:function(e){e&&e.url?(this.setState({isEditing:!1}),this.props.setAttributes(Object(V.a)({},We(e),{width:void 0,height:void 0}))):this.props.setAttributes({url:void 0,alt:void 0,id:void 0,caption:void 0})}},{key:"onSetLinkDestination",value:function(e){var t;t="none"===e?void 0:"media"===e?this.props.image&&this.props.image.source_url||this.props.attributes.url:"attachment"===e?this.props.image&&this.props.image.link:this.props.attributes.href,this.props.setAttributes({linkDestination:e,href:t})}},{key:"onSelectURL",value:function(e){e!==this.props.attributes.url&&this.props.setAttributes({url:e,id:void 0}),this.setState({isEditing:!1})}},{key:"onImageError",value:function(e){var t=He({attributes:{url:e}});void 0!==t&&this.props.onReplace(t)}},{key:"onSetCustomHref",value:function(e){this.props.setAttributes({href:e})}},{key:"onSetLinkClass",value:function(e){this.props.setAttributes({linkClass:e})}},{key:"onSetLinkRel",value:function(e){this.props.setAttributes({rel:e})}},{key:"onSetNewTab",value:function(e){var t=this.props.attributes.rel,n=e?"_blank":void 0,r=t;n&&!t?r="noreferrer noopener":n||"noreferrer noopener"!==t||(r=void 0),this.props.setAttributes({linkTarget:n,rel:r})}},{key:"onFocusCaption",value:function(){this.state.captionFocused||this.setState({captionFocused:!0})}},{key:"onImageClick",value:function(){this.state.captionFocused&&this.setState({captionFocused:!1})}},{key:"updateAlt",value:function(e){this.props.setAttributes({alt:e})}},{key:"updateAlignment",value:function(e){var t=-1!==["wide","full"].indexOf(e)?{width:void 0,height:void 0}:{};this.props.setAttributes(Object(V.a)({},t,{align:e}))}},{key:"updateImageURL",value:function(e){this.props.setAttributes({url:e,width:void 0,height:void 0})}},{key:"updateWidth",value:function(e){this.props.setAttributes({width:parseInt(e,10)})}},{key:"updateHeight",value:function(e){this.props.setAttributes({height:parseInt(e,10)})}},{key:"updateDimensions",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return function(){e.props.setAttributes({width:t,height:n})}}},{key:"getFilename",value:function(e){var t=Object(pe.getPath)(e);if(t)return Object(G.last)(t.split("/"))}},{key:"getLinkDestinationOptions",value:function(){return[{value:"none",label:Object(K.__)("None")},{value:"media",label:Object(K.__)("Media File")},{value:"attachment",label:Object(K.__)("Attachment Page")},{value:"custom",label:Object(K.__)("Custom URL")}]}},{key:"toggleIsEditing",value:function(){this.setState({isEditing:!this.state.isEditing})}},{key:"getImageSizeOptions",value:function(){var e=this.props,t=e.imageSizes,n=e.image;return Object(G.compact)(Object(G.map)(t,function(e){var t=e.name,r=e.slug,o=Object(G.get)(n,["media_details","sizes",r,"source_url"]);return o?{value:o,label:t}:null}))}},{key:"render",value:function(){var e,t=this,n=this.state.isEditing,r=this.props,o=r.attributes,a=r.setAttributes,c=r.isLargeViewport,i=r.isSelected,l=r.className,s=r.maxWidth,u=r.noticeUI,b=r.toggleSelection,m=r.isRTL,d=o.url,h=o.alt,p=o.caption,g=o.align,O=o.id,f=o.href,j=o.rel,v=o.linkClass,y=o.linkDestination,k=o.width,w=o.height,E=o.linkTarget,C=function(e,t){return t&&!e&&!Object(de.isBlobURL)(t)}(O,d);d&&(e=C?Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)(Q.IconButton,{className:"components-icon-button components-toolbar__control",label:Object(K.__)("Edit image"),onClick:this.toggleIsEditing,icon:"edit"})):Object(U.createElement)($.MediaUploadCheck,null,Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)($.MediaUpload,{onSelect:this.onSelectImage,allowedTypes:Ue,value:O,render:function(e){var t=e.open;return Object(U.createElement)(Q.IconButton,{className:"components-toolbar__control",label:Object(K.__)("Edit image"),icon:"edit",onClick:t})}}))));var _=Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.BlockAlignmentToolbar,{value:g,onChange:this.updateAlignment}),e);if(n||!d){var x=C?d:void 0;return Object(U.createElement)(U.Fragment,null,_,Object(U.createElement)($.MediaPlaceholder,{icon:"format-image",className:l,onSelect:this.onSelectImage,onSelectURL:this.onSelectURL,notices:u,onError:this.onUploadError,accept:"image/*",allowedTypes:Ue,value:{id:O,src:x}}))}var S=q()(l,{"is-transient":Object(de.isBlobURL)(d),"is-resized":!!k||!!w,"is-focused":i}),T=-1===["wide","full"].indexOf(g)&&c,N="custom"!==y,R=this.getImageSizeOptions(),B=function(e,n){return Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Image Settings")},Object(U.createElement)(Q.TextareaControl,{label:Object(K.__)("Alt Text (Alternative Text)"),value:h,onChange:t.updateAlt,help:Object(K.__)("Alternative text describes your image to people who can’t see it. Add a short description with its key details.")}),!Object(G.isEmpty)(R)&&Object(U.createElement)(Q.SelectControl,{label:Object(K.__)("Image Size"),value:d,options:R,onChange:t.updateImageURL}),T&&Object(U.createElement)("div",{className:"block-library-image__dimensions"},Object(U.createElement)("p",{className:"block-library-image__dimensions__row"},Object(K.__)("Image Dimensions")),Object(U.createElement)("div",{className:"block-library-image__dimensions__row"},Object(U.createElement)(Q.TextControl,{type:"number",className:"block-library-image__dimensions__width",label:Object(K.__)("Width"),value:void 0!==k?k:"",placeholder:e,min:1,onChange:t.updateWidth}),Object(U.createElement)(Q.TextControl,{type:"number",className:"block-library-image__dimensions__height",label:Object(K.__)("Height"),value:void 0!==w?w:"",placeholder:n,min:1,onChange:t.updateHeight})),Object(U.createElement)("div",{className:"block-library-image__dimensions__row"},Object(U.createElement)(Q.ButtonGroup,{"aria-label":Object(K.__)("Image Size")},[25,50,75,100].map(function(r){var o=Math.round(e*(r/100)),a=Math.round(n*(r/100)),c=k===o&&w===a;return Object(U.createElement)(Q.Button,{key:r,isSmall:!0,isPrimary:c,"aria-pressed":c,onClick:t.updateDimensions(o,a)},r,"%")})),Object(U.createElement)(Q.Button,{isSmall:!0,onClick:t.updateDimensions()},Object(K.__)("Reset"))))),Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Link Settings")},Object(U.createElement)(Q.SelectControl,{label:Object(K.__)("Link To"),value:y,options:t.getLinkDestinationOptions(),onChange:t.onSetLinkDestination}),"none"!==y&&Object(U.createElement)(U.Fragment,null,Object(U.createElement)(Q.TextControl,{label:Object(K.__)("Link URL"),value:f||"",onChange:t.onSetCustomHref,placeholder:N?void 0:"https://",readOnly:N}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Open in New Tab"),onChange:t.onSetNewTab,checked:"_blank"===E}),Object(U.createElement)(Q.TextControl,{label:Object(K.__)("Link CSS Class"),value:v||"",onChange:t.onSetLinkClass}),Object(U.createElement)(Q.TextControl,{label:Object(K.__)("Link Rel"),value:j||"",onChange:t.onSetLinkRel}))))};return Object(U.createElement)(U.Fragment,null,_,Object(U.createElement)("figure",{className:S},Object(U.createElement)(Ve,{src:d,dirtynessTrigger:g},function(e){var n,r=e.imageWidthWithinContainer,o=e.imageHeightWithinContainer,c=e.imageWidth,i=e.imageHeight,l=t.getFilename(d);n=h||(l?Object(K.sprintf)(Object(K.__)("This image has an empty alt attribute; its file name is %s"),l):Object(K.__)("This image has an empty alt attribute"));var u=Object(U.createElement)(U.Fragment,null,Object(U.createElement)("img",{src:d,alt:n,onClick:t.onImageClick,onError:function(){return t.onImageError(d)}}),Object(de.isBlobURL)(d)&&Object(U.createElement)(Q.Spinner,null));if(!T||!r)return Object(U.createElement)(U.Fragment,null,B(c,i),Object(U.createElement)("div",{style:{width:k,height:w}},u));var p=k||r,O=w||o,f=c/i,j=c<i?20:20*f,v=i<c?20:20/f,y=2.5*s,E=!1,C=!1;return"center"===g?(E=!0,C=!0):m?"left"===g?E=!0:C=!0:"right"===g?C=!0:E=!0,Object(U.createElement)(U.Fragment,null,B(c,i),Object(U.createElement)(Q.ResizableBox,{size:k&&w?{width:k,height:w}:void 0,minWidth:j,maxWidth:y,minHeight:v,maxHeight:y/f,lockAspectRatio:!0,enable:{top:!1,right:E,bottom:!0,left:C},onResizeStart:function(){b(!1)},onResizeStop:function(e,t,n,r){a({width:parseInt(p+r.width,10),height:parseInt(O+r.height,10)}),b(!0)}},u))}),(!$.RichText.isEmpty(p)||i)&&Object(U.createElement)($.RichText,{tagName:"figcaption",placeholder:Object(K.__)("Write caption…"),value:p,unstableOnFocus:this.onFocusCaption,onChange:function(e){return a({caption:e})},isSelected:this.state.captionFocused,inlineToolbar:!0})))}}]),t}(U.Component),Ke=Object(re.compose)([Object(oe.withSelect)(function(e,t){var n=e("core").getMedia,r=e("core/editor").getEditorSettings,o=t.attributes.id,a=r(),c=a.maxWidth,i=a.isRTL,l=a.imageSizes;return{image:o?n(o):null,maxWidth:c,isRTL:i,imageSizes:l}}),Object(ge.withViewportMatch)({isLargeViewport:"medium"}),Q.withNotices])(Ge),$e="core/image",Qe={url:{type:"string",source:"attribute",selector:"img",attribute:"src"},alt:{type:"string",source:"attribute",selector:"img",attribute:"alt",default:""},caption:{type:"string",source:"html",selector:"figcaption"},href:{type:"string",source:"attribute",selector:"figure > a",attribute:"href"},rel:{type:"string",source:"attribute",selector:"figure > a",attribute:"rel"},linkClass:{type:"string",source:"attribute",selector:"figure > a",attribute:"class"},id:{type:"number"},align:{type:"string"},width:{type:"number"},height:{type:"number"},linkDestination:{type:"string",default:"none"},linkTarget:{type:"string",source:"attribute",selector:"figure > a",attribute:"target"}},Ye={img:{attributes:["src","alt"],classes:["alignleft","aligncenter","alignright","alignnone",/^wp-image-\d+$/]}},Ze={figure:{require:["img"],children:Object(V.a)({},Ye,{a:{attributes:["href","rel","target"],children:Ye},figcaption:{children:Object(D.getPhrasingContentSchema)()}})}};function Je(e,t){var n=document.implementation.createHTMLDocument("").body;n.innerHTML=e;var r=n.firstElementChild;if(r&&"A"===r.nodeName)return r.getAttribute(t)||void 0}var Xe={title:Object(K.__)("Image"),description:Object(K.__)("Insert an image to make a visual statement."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"m19 5v14h-14v-14h14m0-2h-14c-1.1 0-2 0.9-2 2v14c0 1.1 0.9 2 2 2h14c1.1 0 2-0.9 2-2v-14c0-1.1-0.9-2-2-2z"}),Object(U.createElement)(Q.Path,{d:"m14.14 11.86l-3 3.87-2.14-2.59-3 3.86h12l-3.86-5.14z"})),category:"common",keywords:["img",Object(K.__)("photo")],attributes:Qe,transforms:{from:[{type:"raw",isMatch:function(e){return"FIGURE"===e.nodeName&&!!e.querySelector("img")},schema:Ze,transform:function(e){var t=e.className+" "+e.querySelector("img").className,n=/(?:^|\s)align(left|center|right)(?:$|\s)/.exec(t),r=n?n[1]:void 0,o=/(?:^|\s)wp-image-(\d+)(?:$|\s)/.exec(t),a=o?Number(o[1]):void 0,c=e.querySelector("a"),i=c&&c.href?"custom":void 0,l=c&&c.href?c.href:void 0,s=c&&c.rel?c.rel:void 0,u=c&&c.className?c.className:void 0,b=Object(D.getBlockAttributes)("core/image",e.outerHTML,{align:r,id:a,linkDestination:i,href:l,rel:s,linkClass:u});return Object(D.createBlock)("core/image",b)}},{type:"files",isMatch:function(e){return 1===e.length&&0===e[0].type.indexOf("image/")},transform:function(e){var t=e[0];return Object(D.createBlock)("core/image",{url:Object(de.createBlobURL)(t)})}},{type:"shortcode",tag:"caption",attributes:{url:{type:"string",source:"attribute",attribute:"src",selector:"img"},alt:{type:"string",source:"attribute",attribute:"alt",selector:"img"},caption:{shortcode:function(e,t){var n=t.shortcode,r=document.implementation.createHTMLDocument("").body;return r.innerHTML=n.content,r.removeChild(r.firstElementChild),r.innerHTML.trim()}},href:{shortcode:function(e,t){return Je(t.shortcode.content,"href")}},rel:{shortcode:function(e,t){return Je(t.shortcode.content,"rel")}},linkClass:{shortcode:function(e,t){return Je(t.shortcode.content,"class")}},id:{type:"number",shortcode:function(e){var t=e.named.id;if(t)return parseInt(t.replace("attachment_",""),10)}},align:{type:"string",shortcode:function(e){var t=e.named.align;return(void 0===t?"alignnone":t).replace("align","")}}}}]},getEditWrapperProps:function(e){var t=e.align,n=e.width;if("left"===t||"center"===t||"right"===t||"wide"===t||"full"===t)return{"data-align":t,"data-resized":!!n}},edit:Ke,save:function(e){var t,n=e.attributes,r=n.url,o=n.alt,a=n.caption,c=n.align,i=n.href,l=n.rel,s=n.linkClass,u=n.width,b=n.height,m=n.id,d=n.linkTarget,h=q()((t={},Object(F.a)(t,"align".concat(c),c),Object(F.a)(t,"is-resized",u||b),t)),p=Object(U.createElement)("img",{src:r,alt:o,className:m?"wp-image-".concat(m):null,width:u,height:b}),g=Object(U.createElement)(U.Fragment,null,i?Object(U.createElement)("a",{className:s,href:i,target:d,rel:l},p):p,!$.RichText.isEmpty(a)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:a}));return"left"===c||"right"===c||"center"===c?Object(U.createElement)("div",null,Object(U.createElement)("figure",{className:h},g)):Object(U.createElement)("figure",{className:h},g)},deprecated:[{attributes:Qe,save:function(e){var t,n=e.attributes,r=n.url,o=n.alt,a=n.caption,c=n.align,i=n.href,l=n.width,s=n.height,u=n.id,b=q()((t={},Object(F.a)(t,"align".concat(c),c),Object(F.a)(t,"is-resized",l||s),t)),m=Object(U.createElement)("img",{src:r,alt:o,className:u?"wp-image-".concat(u):null,width:l,height:s});return Object(U.createElement)("figure",{className:b},i?Object(U.createElement)("a",{href:i},m):m,!$.RichText.isEmpty(a)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:a}))}},{attributes:Qe,save:function(e){var t=e.attributes,n=t.url,r=t.alt,o=t.caption,a=t.align,c=t.href,i=t.width,l=t.height,s=t.id,u=Object(U.createElement)("img",{src:n,alt:r,className:s?"wp-image-".concat(s):null,width:i,height:l});return Object(U.createElement)("figure",{className:a?"align".concat(a):null},c?Object(U.createElement)("a",{href:c},u):u,!$.RichText.isEmpty(o)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:o}))}},{attributes:Qe,save:function(e){var t=e.attributes,n=t.url,r=t.alt,o=t.caption,a=t.align,c=t.href,i=t.width,l=t.height,s=i||l?{width:i,height:l}:{},u=Object(U.createElement)("img",Object(Y.a)({src:n,alt:r},s)),b={};return i?b={width:i}:"left"!==a&&"right"!==a||(b={maxWidth:"50%"}),Object(U.createElement)("figure",{className:a?"align".concat(a):null,style:b},c?Object(U.createElement)("a",{href:c},u):u,!$.RichText.isEmpty(o)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:o}))}}]},et=n(21),tt=function(e){function t(){return Object(Z.a)(this,t),Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))}return Object(te.a)(t,e),Object(J.a)(t,[{key:"createLevelControl",value:function(e,t,n){return{icon:"heading",title:Object(K.sprintf)(Object(K.__)("Heading %d"),e),isActive:e===t,onClick:function(){return n(e)},subscript:String(e)}}},{key:"render",value:function(){var e=this,t=this.props,n=t.minLevel,r=t.maxLevel,o=t.selectedLevel,a=t.onChange;return Object(U.createElement)(Q.Toolbar,{controls:Object(G.range)(n,r).map(function(t){return e.createLevelControl(t,o,a)})})}}]),t}(U.Component);function nt(e){return Number(e.substr(1))}var rt,ot={className:!1,anchor:!0},at={content:{type:"string",source:"html",selector:"h1,h2,h3,h4,h5,h6",default:""},level:{type:"number",default:2},align:{type:"string"},placeholder:{type:"string"}},ct="core/heading",it={title:Object(K.__)("Heading"),description:Object(K.__)("Introduce new sections and organize content to help visitors (and search engines) understand the structure of your content."),icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M5 4v3h5.5v12h3V7H19V4z"}),Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"})),category:"common",keywords:[Object(K.__)("title"),Object(K.__)("subtitle")],supports:ot,attributes:at,transforms:{from:[{type:"block",blocks:["core/paragraph"],transform:function(e){var t=e.content;return Object(D.createBlock)("core/heading",{content:t})}},{type:"raw",selector:"h1,h2,h3,h4,h5,h6",schema:{h1:{children:Object(D.getPhrasingContentSchema)()},h2:{children:Object(D.getPhrasingContentSchema)()},h3:{children:Object(D.getPhrasingContentSchema)()},h4:{children:Object(D.getPhrasingContentSchema)()},h5:{children:Object(D.getPhrasingContentSchema)()},h6:{children:Object(D.getPhrasingContentSchema)()}},transform:function(e){return Object(D.createBlock)("core/heading",Object(V.a)({},Object(D.getBlockAttributes)("core/heading",e.outerHTML),{level:nt(e.nodeName)}))}}].concat(Object(H.a)([2,3,4,5,6].map(function(e){return{type:"prefix",prefix:Array(e+1).join("#"),transform:function(t){return Object(D.createBlock)("core/heading",{level:e,content:t})}}}))),to:[{type:"block",blocks:["core/paragraph"],transform:function(e){var t=e.content;return Object(D.createBlock)("core/paragraph",{content:t})}}]},deprecated:[{supports:ot,attributes:Object(V.a)({},Object(G.omit)(at,["level"]),{nodeName:{type:"string",source:"property",selector:"h1,h2,h3,h4,h5,h6",property:"nodeName",default:"H2"}}),migrate:function(e){var t=e.nodeName,n=Object(et.a)(e,["nodeName"]);return Object(V.a)({},n,{level:nt(t)})},save:function(e){var t=e.attributes,n=t.align,r=t.nodeName,o=t.content;return Object(U.createElement)($.RichText.Content,{tagName:r.toLowerCase(),style:{textAlign:n},value:o})}}],merge:function(e,t){return{content:e.content+t.content}},edit:function(e){var t=e.attributes,n=e.setAttributes,r=e.mergeBlocks,o=e.insertBlocksAfter,a=e.onReplace,c=e.className,i=t.align,l=t.content,s=t.level,u=t.placeholder,b="h"+s;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)(tt,{minLevel:2,maxLevel:5,selectedLevel:s,onChange:function(e){return n({level:e})}})),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Heading Settings")},Object(U.createElement)("p",null,Object(K.__)("Level")),Object(U.createElement)(tt,{minLevel:1,maxLevel:7,selectedLevel:s,onChange:function(e){return n({level:e})}}),Object(U.createElement)("p",null,Object(K.__)("Text Alignment")),Object(U.createElement)($.AlignmentToolbar,{value:i,onChange:function(e){n({align:e})}}))),Object(U.createElement)($.RichText,{identifier:"content",wrapperClassName:"wp-block-heading",tagName:b,value:l,onChange:function(e){return n({content:e})},onMerge:r,unstableOnSplit:o?function(e,t){n({content:e});for(var r=arguments.length,a=new Array(r>2?r-2:0),c=2;c<r;c++)a[c-2]=arguments[c];o(a.concat([Object(D.createBlock)("core/paragraph",{content:t})]))}:void 0,onRemove:function(){return a([])},style:{textAlign:i},className:c,placeholder:u||Object(K.__)("Write heading…")}))},save:function(e){var t=e.attributes,n=t.align,r=t.level,o=t.content,a="h"+r;return Object(U.createElement)($.RichText.Content,{tagName:a,style:{textAlign:n},value:o})}},lt=n(20),st=(rt={},Object(F.a)(rt,"value",{type:"string",source:"html",selector:"blockquote",multiline:"p",default:""}),Object(F.a)(rt,"citation",{type:"string",source:"html",selector:"cite",default:""}),Object(F.a)(rt,"align",{type:"string"}),rt),ut="core/quote",bt={title:Object(K.__)("Quote"),description:Object(K.__)('Give quoted text visual emphasis. "In quoting others, we cite ourselves." — Julio Cortázar'),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M19 18h-6l2-4h-2V6h8v7l-2 5zm-2-2l2-3V8h-4v4h4l-2 4zm-8 2H3l2-4H3V6h8v7l-2 5zm-2-2l2-3V8H5v4h4l-2 4z"}))),category:"common",keywords:[Object(K.__)("blockquote")],attributes:st,styles:[{name:"default",label:Object(K._x)("Regular","block style"),isDefault:!0},{name:"large",label:Object(K._x)("Large","block style")}],transforms:{from:[{type:"block",isMultiBlock:!0,blocks:["core/paragraph"],transform:function(e){return Object(D.createBlock)("core/quote",{value:Object(lt.toHTMLString)({value:Object(lt.join)(e.map(function(e){var t=e.content;return Object(lt.create)({html:t})}),"\u2028"),multilineTag:"p"})})}},{type:"block",blocks:["core/heading"],transform:function(e){var t=e.content;return Object(D.createBlock)("core/quote",{value:"<p>".concat(t,"</p>")})}},{type:"block",blocks:["core/pullquote"],transform:function(e){var t=e.value,n=e.citation;return Object(D.createBlock)("core/quote",{value:t,citation:n})}},{type:"prefix",prefix:">",transform:function(e){return Object(D.createBlock)("core/quote",{value:"<p>".concat(e,"</p>")})}},{type:"raw",isMatch:function(e){return"BLOCKQUOTE"===e.nodeName&&Array.from(e.childNodes).every(function(e){return"P"===e.nodeName})},schema:{blockquote:{children:{p:{children:Object(D.getPhrasingContentSchema)()}}}}}],to:[{type:"block",blocks:["core/paragraph"],transform:function(e){var t=e.value,n=e.citation,r=[];return t&&"<p></p>"!==t&&r.push.apply(r,Object(H.a)(Object(lt.split)(Object(lt.create)({html:t,multilineTag:"p"}),"\u2028").map(function(e){return Object(D.createBlock)("core/paragraph",{content:Object(lt.toHTMLString)({value:e})})}))),n&&"<p></p>"!==n&&r.push(Object(D.createBlock)("core/paragraph",{content:n})),0===r.length?Object(D.createBlock)("core/paragraph",{content:""}):r}},{type:"block",blocks:["core/heading"],transform:function(e){var t=e.value,n=e.citation,r=Object(et.a)(e,["value","citation"]);if("<p></p>"===t)return Object(D.createBlock)("core/heading",{content:n});var o=Object(lt.split)(Object(lt.create)({html:t,multilineTag:"p"}),"\u2028"),a=o.slice(1);return[Object(D.createBlock)("core/heading",{content:Object(lt.toHTMLString)({value:o[0]})}),Object(D.createBlock)("core/quote",Object(V.a)({},r,{citation:n,value:Object(lt.toHTMLString)({value:a.length?Object(lt.join)(o.slice(1),"\u2028"):Object(lt.create)(),multilineTag:"p"})}))]}},{type:"block",blocks:["core/pullquote"],transform:function(e){var t=e.value,n=e.citation;return Object(D.createBlock)("core/pullquote",{value:t,citation:n})}}]},edit:function(e){var t=e.attributes,n=e.setAttributes,r=e.isSelected,o=e.mergeBlocks,a=e.onReplace,c=e.className,i=t.align,l=t.value,s=t.citation;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.AlignmentToolbar,{value:i,onChange:function(e){n({align:e})}})),Object(U.createElement)("blockquote",{className:c,style:{textAlign:i}},Object(U.createElement)($.RichText,{identifier:"value",multiline:!0,value:l,onChange:function(e){return n({value:e})},onMerge:o,onRemove:function(e){var t=!s||0===s.length;!e&&t&&a([])},placeholder:Object(K.__)("Write quote…")}),(!$.RichText.isEmpty(s)||r)&&Object(U.createElement)($.RichText,{identifier:"citation",value:s,onChange:function(e){return n({citation:e})},placeholder:Object(K.__)("Write citation…"),className:"wp-block-quote__citation"})))},save:function(e){var t=e.attributes,n=t.align,r=t.value,o=t.citation;return Object(U.createElement)("blockquote",{style:{textAlign:n||null}},Object(U.createElement)($.RichText.Content,{multiline:!0,value:r}),!$.RichText.isEmpty(o)&&Object(U.createElement)($.RichText.Content,{tagName:"cite",value:o}))},merge:function(e,t){var n=t.value,r=t.citation;return n&&"<p></p>"!==n?Object(V.a)({},e,{value:e.value+n,citation:e.citation+r}):Object(V.a)({},e,{citation:e.citation+r})},deprecated:[{attributes:Object(V.a)({},st,{style:{type:"number",default:1}}),migrate:function(e){return 2===e.style?Object(V.a)({},Object(G.omit)(e,["style"]),{className:e.className?e.className+" is-style-large":"is-style-large"}):e},save:function(e){var t=e.attributes,n=t.align,r=t.value,o=t.citation,a=t.style;return Object(U.createElement)("blockquote",{className:2===a?"is-large":"",style:{textAlign:n||null}},Object(U.createElement)($.RichText.Content,{multiline:!0,value:r}),!$.RichText.isEmpty(o)&&Object(U.createElement)($.RichText.Content,{tagName:"cite",value:o}))}},{attributes:Object(V.a)({},st,{citation:{type:"string",source:"html",selector:"footer",default:""},style:{type:"number",default:1}}),save:function(e){var t=e.attributes,n=t.align,r=t.value,o=t.citation,a=t.style;return Object(U.createElement)("blockquote",{className:"blocks-quote-style-".concat(a),style:{textAlign:n||null}},Object(U.createElement)($.RichText.Content,{multiline:!0,value:r}),!$.RichText.isEmpty(o)&&Object(U.createElement)($.RichText.Content,{tagName:"footer",value:o}))}}]},mt=n(16),dt=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).onImageClick=e.onImageClick.bind(Object(ne.a)(Object(ne.a)(e))),e.onSelectCaption=e.onSelectCaption.bind(Object(ne.a)(Object(ne.a)(e))),e.onKeyDown=e.onKeyDown.bind(Object(ne.a)(Object(ne.a)(e))),e.bindContainer=e.bindContainer.bind(Object(ne.a)(Object(ne.a)(e))),e.state={captionSelected:!1},e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"bindContainer",value:function(e){this.container=e}},{key:"onSelectCaption",value:function(){this.state.captionSelected||this.setState({captionSelected:!0}),this.props.isSelected||this.props.onSelect()}},{key:"onImageClick",value:function(){this.props.isSelected||this.props.onSelect(),this.state.captionSelected&&this.setState({captionSelected:!1})}},{key:"onKeyDown",value:function(e){this.container===document.activeElement&&this.props.isSelected&&-1!==[mt.BACKSPACE,mt.DELETE].indexOf(e.keyCode)&&(e.stopPropagation(),e.preventDefault(),this.props.onRemove())}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isSelected,r=t.image,o=t.url;r&&!o&&this.props.setAttributes({url:r.source_url,alt:r.alt_text}),this.state.captionSelected&&!n&&e.isSelected&&this.setState({captionSelected:!1})}},{key:"render",value:function(){var e,t=this.props,n=t.url,r=t.alt,o=t.id,a=t.linkTo,c=t.link,i=t.isSelected,l=t.caption,s=t.onRemove,u=t.setAttributes,b=t["aria-label"];switch(a){case"media":e=n;break;case"attachment":e=c}var m=Object(U.createElement)(U.Fragment,null,Object(U.createElement)("img",{src:n,alt:r,"data-id":o,onClick:this.onImageClick,tabIndex:"0",onKeyDown:this.onImageClick,"aria-label":b}),Object(de.isBlobURL)(n)&&Object(U.createElement)(Q.Spinner,null)),d=q()({"is-selected":i,"is-transient":Object(de.isBlobURL)(n)});return Object(U.createElement)("figure",{className:d,tabIndex:"-1",onKeyDown:this.onKeyDown,ref:this.bindContainer},i&&Object(U.createElement)("div",{className:"block-library-gallery-item__inline-menu"},Object(U.createElement)(Q.IconButton,{icon:"no-alt",onClick:s,className:"blocks-gallery-item__remove",label:Object(K.__)("Remove Image")})),e?Object(U.createElement)("a",{href:e},m):m,!$.RichText.isEmpty(l)||i?Object(U.createElement)($.RichText,{tagName:"figcaption",placeholder:Object(K.__)("Write caption…"),value:l,isSelected:this.state.captionSelected,onChange:function(e){return u({caption:e})},unstableOnFocus:this.onSelectCaption,inlineToolbar:!0}):null)}}]),t}(U.Component),ht=Object(oe.withSelect)(function(e,t){var n=e("core").getMedia,r=t.id;return{image:r?n(r):null}})(dt),pt=[{value:"attachment",label:Object(K.__)("Attachment Page")},{value:"media",label:Object(K.__)("Media File")},{value:"none",label:Object(K.__)("None")}],gt=["image"];function Ot(e){return Math.min(3,e.images.length)}var ft=function(e){var t=Object(G.pick)(e,["alt","id","link","caption"]);return t.url=Object(G.get)(e,["sizes","large","url"])||Object(G.get)(e,["media_details","sizes","large","source_url"])||e.url,t},jt=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).onSelectImage=e.onSelectImage.bind(Object(ne.a)(Object(ne.a)(e))),e.onSelectImages=e.onSelectImages.bind(Object(ne.a)(Object(ne.a)(e))),e.setLinkTo=e.setLinkTo.bind(Object(ne.a)(Object(ne.a)(e))),e.setColumnsNumber=e.setColumnsNumber.bind(Object(ne.a)(Object(ne.a)(e))),e.toggleImageCrop=e.toggleImageCrop.bind(Object(ne.a)(Object(ne.a)(e))),e.onRemoveImage=e.onRemoveImage.bind(Object(ne.a)(Object(ne.a)(e))),e.setImageAttributes=e.setImageAttributes.bind(Object(ne.a)(Object(ne.a)(e))),e.addFiles=e.addFiles.bind(Object(ne.a)(Object(ne.a)(e))),e.uploadFromFiles=e.uploadFromFiles.bind(Object(ne.a)(Object(ne.a)(e))),e.setAttributes=e.setAttributes.bind(Object(ne.a)(Object(ne.a)(e))),e.state={selectedImage:null},e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"setAttributes",value:function(e){if(e.ids)throw new Error('The "ids" attribute should not be changed directly. It is managed automatically when "images" attribute changes');e.images&&(e=Object(V.a)({},e,{ids:Object(G.map)(e.images,"id")})),this.props.setAttributes(e)}},{key:"onSelectImage",value:function(e){var t=this;return function(){t.state.selectedImage!==e&&t.setState({selectedImage:e})}}},{key:"onRemoveImage",value:function(e){var t=this;return function(){var n=Object(G.filter)(t.props.attributes.images,function(t,n){return e!==n}),r=t.props.attributes.columns;t.setState({selectedImage:null}),t.setAttributes({images:n,columns:r?Math.min(n.length,r):r})}}},{key:"onSelectImages",value:function(e){this.setAttributes({images:e.map(function(e){return ft(e)})})}},{key:"setLinkTo",value:function(e){this.setAttributes({linkTo:e})}},{key:"setColumnsNumber",value:function(e){this.setAttributes({columns:e})}},{key:"toggleImageCrop",value:function(){this.setAttributes({imageCrop:!this.props.attributes.imageCrop})}},{key:"getImageCropHelp",value:function(e){return e?Object(K.__)("Thumbnails are cropped to align."):Object(K.__)("Thumbnails are not cropped.")}},{key:"setImageAttributes",value:function(e,t){var n=this.props.attributes.images,r=this.setAttributes;n[e]&&r({images:Object(H.a)(n.slice(0,e)).concat([Object(V.a)({},n[e],t)],Object(H.a)(n.slice(e+1)))})}},{key:"uploadFromFiles",value:function(e){this.addFiles(e.target.files)}},{key:"addFiles",value:function(e){var t=this.props.attributes.images||[],n=this.props.noticeOperations,r=this.setAttributes;Object($.mediaUpload)({allowedTypes:gt,filesList:e,onFileChange:function(e){var n=e.map(function(e){return ft(e)});r({images:t.concat(n)})},onError:n.createErrorNotice})}},{key:"componentDidUpdate",value:function(e){!this.props.isSelected&&e.isSelected&&this.setState({selectedImage:null,captionSelected:!1})}},{key:"render",value:function(){var e=this,t=this.props,n=t.attributes,r=t.isSelected,o=t.className,a=t.noticeOperations,c=t.noticeUI,i=n.images,l=n.columns,s=void 0===l?Ot(n):l,u=n.align,b=n.imageCrop,m=n.linkTo,d=Object(U.createElement)(Q.DropZone,{onFilesDrop:this.addFiles}),h=Object(U.createElement)($.BlockControls,null,!!i.length&&Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)($.MediaUpload,{onSelect:this.onSelectImages,allowedTypes:gt,multiple:!0,gallery:!0,value:i.map(function(e){return e.id}),render:function(e){var t=e.open;return Object(U.createElement)(Q.IconButton,{className:"components-toolbar__control",label:Object(K.__)("Edit Gallery"),icon:"edit",onClick:t})}})));return 0===i.length?Object(U.createElement)(U.Fragment,null,h,Object(U.createElement)($.MediaPlaceholder,{icon:"format-gallery",className:o,labels:{title:Object(K.__)("Gallery"),instructions:Object(K.__)("Drag images, upload new ones or select files from your library.")},onSelect:this.onSelectImages,accept:"image/*",allowedTypes:gt,multiple:!0,notices:c,onError:a.createErrorNotice})):Object(U.createElement)(U.Fragment,null,h,Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Gallery Settings")},i.length>1&&Object(U.createElement)(Q.RangeControl,{label:Object(K.__)("Columns"),value:s,onChange:this.setColumnsNumber,min:1,max:Math.min(8,i.length)}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Crop Images"),checked:!!b,onChange:this.toggleImageCrop,help:this.getImageCropHelp}),Object(U.createElement)(Q.SelectControl,{label:Object(K.__)("Link To"),value:m,onChange:this.setLinkTo,options:pt}))),c,Object(U.createElement)("ul",{className:"".concat(o," align").concat(u," columns-").concat(s," ").concat(b?"is-cropped":"")},d,i.map(function(t,n){var o=Object(K.__)(Object(K.sprintf)("image %1$d of %2$d in gallery",n+1,i.length));return Object(U.createElement)("li",{className:"blocks-gallery-item",key:t.id||t.url},Object(U.createElement)(ht,{url:t.url,alt:t.alt,id:t.id,isSelected:r&&e.state.selectedImage===n,onRemove:e.onRemoveImage(n),onSelect:e.onSelectImage(n),setAttributes:function(t){return e.setImageAttributes(n,t)},caption:t.caption,"aria-label":o}))}),r&&Object(U.createElement)("li",{className:"blocks-gallery-item has-add-item-button"},Object(U.createElement)(Q.FormFileUpload,{multiple:!0,isLarge:!0,className:"block-library-gallery-add-item-button",onChange:this.uploadFromFiles,accept:"image/*",icon:"insert"},Object(K.__)("Upload an image")))))}}]),t}(U.Component),vt=Object(Q.withNotices)(jt),yt={images:{type:"array",default:[],source:"query",selector:"ul.wp-block-gallery .blocks-gallery-item",query:{url:{source:"attribute",selector:"img",attribute:"src"},link:{source:"attribute",selector:"img",attribute:"data-link"},alt:{source:"attribute",selector:"img",attribute:"alt",default:""},id:{source:"attribute",selector:"img",attribute:"data-id"},caption:{type:"string",source:"html",selector:"figcaption"}}},ids:{type:"array",default:[]},columns:{type:"number"},imageCrop:{type:"boolean",default:!0},linkTo:{type:"string",default:"none"}},kt="core/gallery",wt=function(e){return e?e.split(",").map(function(e){return parseInt(e,10)}):[]},Et={title:Object(K.__)("Gallery"),description:Object(K.__)("Display multiple images in a rich gallery."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M20 4v12H8V4h12m0-2H8L6 4v12l2 2h12l2-2V4l-2-2z"}),Object(U.createElement)(Q.Path,{d:"M12 12l1 2 3-3 3 4H9z"}),Object(U.createElement)(Q.Path,{d:"M2 6v14l2 2h14v-2H4V6H2z"}))),category:"common",keywords:[Object(K.__)("images"),Object(K.__)("photos")],attributes:yt,supports:{align:!0},transforms:{from:[{type:"block",isMultiBlock:!0,blocks:["core/image"],transform:function(e){var t=e[0].align;t=Object(G.every)(e,["align",t])?t:void 0;var n=Object(G.filter)(e,function(e){var t=e.id,n=e.url;return t&&n});return Object(D.createBlock)("core/gallery",{images:n.map(function(e){return{id:e.id,url:e.url,alt:e.alt,caption:e.caption}}),ids:n.map(function(e){return e.id}),align:t})}},{type:"shortcode",tag:"gallery",attributes:{images:{type:"array",shortcode:function(e){var t=e.named.ids;return wt(t).map(function(e){return{id:e}})}},ids:{type:"array",shortcode:function(e){var t=e.named.ids;return wt(t)}},columns:{type:"number",shortcode:function(e){var t=e.named.columns;return parseInt(void 0===t?"3":t,10)}},linkTo:{type:"string",shortcode:function(e){var t=e.named.link,n=void 0===t?"attachment":t;return"file"===n?"media":n}}}},{type:"files",isMatch:function(e){return 1!==e.length&&Object(G.every)(e,function(e){return 0===e.type.indexOf("image/")})},transform:function(e,t){var n=Object(D.createBlock)("core/gallery",{images:e.map(function(e){return ft({url:Object(de.createBlobURL)(e)})})});return Object($.mediaUpload)({filesList:e,onFileChange:function(e){var r=e.map(ft);t(n.clientId,{ids:Object(G.map)(r,"id"),images:r})},allowedTypes:["image"]}),n}}],to:[{type:"block",blocks:["core/image"],transform:function(e){var t=e.images,n=e.align;return t.length>0?t.map(function(e){var t=e.id,r=e.url,o=e.alt,a=e.caption;return Object(D.createBlock)("core/image",{id:t,url:r,alt:o,caption:a,align:n})}):Object(D.createBlock)("core/image",{align:n})}}]},edit:vt,save:function(e){var t=e.attributes,n=t.images,r=t.columns,o=void 0===r?Ot(t):r,a=t.imageCrop,c=t.linkTo;return Object(U.createElement)("ul",{className:"columns-".concat(o," ").concat(a?"is-cropped":"")},n.map(function(e){var t;switch(c){case"media":t=e.url;break;case"attachment":t=e.link}var n=Object(U.createElement)("img",{src:e.url,alt:e.alt,"data-id":e.id,"data-link":e.link,className:e.id?"wp-image-".concat(e.id):null});return Object(U.createElement)("li",{key:e.id||e.url,className:"blocks-gallery-item"},Object(U.createElement)("figure",null,t?Object(U.createElement)("a",{href:t},n):n,e.caption&&e.caption.length>0&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:e.caption})))}))},deprecated:[{attributes:yt,isEligible:function(e){var t=e.images,n=e.ids;return t&&t.length>0&&(!n&&t||n&&t&&n.length!==t.length||Object(G.some)(t,function(e,t){return!e&&null!==n[t]||parseInt(e,10)!==n[t]}))},migrate:function(e){return Object(V.a)({},e,{ids:Object(G.map)(e.images,function(e){var t=e.id;return t?parseInt(t,10):null})})},save:function(e){var t=e.attributes,n=t.images,r=t.columns,o=void 0===r?Ot(t):r,a=t.imageCrop,c=t.linkTo;return Object(U.createElement)("ul",{className:"columns-".concat(o," ").concat(a?"is-cropped":"")},n.map(function(e){var t;switch(c){case"media":t=e.url;break;case"attachment":t=e.link}var n=Object(U.createElement)("img",{src:e.url,alt:e.alt,"data-id":e.id,"data-link":e.link,className:e.id?"wp-image-".concat(e.id):null});return Object(U.createElement)("li",{key:e.id||e.url,className:"blocks-gallery-item"},Object(U.createElement)("figure",null,t?Object(U.createElement)("a",{href:t},n):n,e.caption&&e.caption.length>0&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:e.caption})))}))}},{attributes:yt,save:function(e){var t=e.attributes,n=t.images,r=t.columns,o=void 0===r?Ot(t):r,a=t.imageCrop,c=t.linkTo;return Object(U.createElement)("ul",{className:"columns-".concat(o," ").concat(a?"is-cropped":"")},n.map(function(e){var t;switch(c){case"media":t=e.url;break;case"attachment":t=e.link}var n=Object(U.createElement)("img",{src:e.url,alt:e.alt,"data-id":e.id,"data-link":e.link});return Object(U.createElement)("li",{key:e.id||e.url,className:"blocks-gallery-item"},Object(U.createElement)("figure",null,t?Object(U.createElement)("a",{href:t},n):n,e.caption&&e.caption.length>0&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:e.caption})))}))}},{attributes:Object(V.a)({},yt,{images:Object(V.a)({},yt.images,{selector:"div.wp-block-gallery figure.blocks-gallery-image img"}),align:{type:"string",default:"none"}}),save:function(e){var t=e.attributes,n=t.images,r=t.columns,o=void 0===r?Ot(t):r,a=t.align,c=t.imageCrop,i=t.linkTo;return Object(U.createElement)("div",{className:"align".concat(a," columns-").concat(o," ").concat(c?"is-cropped":"")},n.map(function(e){var t;switch(i){case"media":t=e.url;break;case"attachment":t=e.link}var n=Object(U.createElement)("img",{src:e.url,alt:e.alt,"data-id":e.id});return Object(U.createElement)("figure",{key:e.id||e.url,className:"blocks-gallery-image"},t?Object(U.createElement)("a",{href:t},n):n)}))}}]};var Ct="core/archives",_t={title:Object(K.__)("Archives"),description:Object(K.__)("Display a monthly archive of your posts."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M7 11h2v2H7v-2zm14-5v14l-2 2H5l-2-2V6l2-2h1V2h2v2h8V2h2v2h1l2 2zM5 8h14V6H5v2zm14 12V10H5v10h14zm-4-7h2v-2h-2v2zm-4 0h2v-2h-2v2z"}))),category:"widgets",supports:{html:!1},getEditWrapperProps:function(e){var t=e.align;if(["left","center","right"].includes(t))return{"data-align":t}},edit:function(e){var t=e.attributes,n=e.setAttributes,r=t.align,o=t.showPostCounts,a=t.displayAsDropdown;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Archives Settings")},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Display as Dropdown"),checked:a,onChange:function(){return n({displayAsDropdown:!a})}}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Show Post Counts"),checked:o,onChange:function(){return n({showPostCounts:!o})}}))),Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.BlockAlignmentToolbar,{value:r,onChange:function(e){n({align:e})},controls:["left","center","right"]})),Object(U.createElement)(Q.Disabled,null,Object(U.createElement)($.ServerSideRender,{block:"core/archives",attributes:t})))},save:function(){return null}},xt=["audio"],St=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).state={editing:!e.props.attributes.src},e.toggleAttribute=e.toggleAttribute.bind(Object(ne.a)(Object(ne.a)(e))),e.onSelectURL=e.onSelectURL.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentDidMount",value:function(){var e=this,t=this.props,n=t.attributes,r=t.noticeOperations,o=t.setAttributes,a=n.id,c=n.src,i=void 0===c?"":c;if(!a&&Object(de.isBlobURL)(i)){var l=Object(de.getBlobByURL)(i);l&&Object($.mediaUpload)({filesList:[l],onFileChange:function(e){var t=Object(he.a)(e,1)[0],n=t.id,r=t.url;o({id:n,src:r})},onError:function(t){o({src:void 0,id:void 0}),e.setState({editing:!0}),r.createErrorNotice(t)},allowedTypes:xt})}}},{key:"toggleAttribute",value:function(e){var t=this;return function(n){t.props.setAttributes(Object(F.a)({},e,n))}}},{key:"onSelectURL",value:function(e){var t=this.props,n=t.attributes,r=t.setAttributes;if(e!==n.src){var o=He({attributes:{url:e}});if(void 0!==o)return void this.props.onReplace(o);r({src:e,id:void 0})}this.setState({editing:!1})}},{key:"render",value:function(){var e=this,t=this.props.attributes,n=t.autoplay,r=t.caption,o=t.loop,a=t.preload,c=t.src,i=this.props,l=i.setAttributes,s=i.isSelected,u=i.className,b=i.noticeOperations,m=i.noticeUI,d=this.state.editing,h=function(){e.setState({editing:!0})};return d?Object(U.createElement)($.MediaPlaceholder,{icon:"media-audio",className:u,onSelect:function(t){if(!t||!t.url)return l({src:void 0,id:void 0}),void h();l({src:t.url,id:t.id}),e.setState({src:t.url,editing:!1})},onSelectURL:this.onSelectURL,accept:"audio/*",allowedTypes:xt,value:this.props.attributes,notices:m,onError:b.createErrorNotice}):Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)(Q.IconButton,{className:"components-icon-button components-toolbar__control",label:Object(K.__)("Edit audio"),onClick:h,icon:"edit"}))),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Audio Settings")},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Autoplay"),onChange:this.toggleAttribute("autoplay"),checked:n}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Loop"),onChange:this.toggleAttribute("loop"),checked:o}),Object(U.createElement)(Q.SelectControl,{label:Object(K.__)("Preload"),value:void 0!==a?a:"none",onChange:function(e){return l({preload:"none"!==e?e:void 0})},options:[{value:"auto",label:Object(K.__)("Auto")},{value:"metadata",label:Object(K.__)("Metadata")},{value:"none",label:Object(K.__)("None")}]}))),Object(U.createElement)("figure",{className:u},Object(U.createElement)(Q.Disabled,null,Object(U.createElement)("audio",{controls:"controls",src:c})),(!$.RichText.isEmpty(r)||s)&&Object(U.createElement)($.RichText,{tagName:"figcaption",placeholder:Object(K.__)("Write caption…"),value:r,onChange:function(e){return l({caption:e})},inlineToolbar:!0})))}}]),t}(U.Component),Tt=Object(Q.withNotices)(St),Nt="core/audio",Rt={title:Object(K.__)("Audio"),description:Object(K.__)("Embed a simple audio player."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"m12 3l0.01 10.55c-0.59-0.34-1.27-0.55-2-0.55-2.22 0-4.01 1.79-4.01 4s1.79 4 4.01 4 3.99-1.79 3.99-4v-10h4v-4h-6zm-1.99 16c-1.1 0-2-0.9-2-2s0.9-2 2-2 2 0.9 2 2-0.9 2-2 2z"})),category:"common",attributes:{src:{type:"string",source:"attribute",selector:"audio",attribute:"src"},caption:{type:"string",source:"html",selector:"figcaption"},id:{type:"number"},autoplay:{type:"boolean",source:"attribute",selector:"audio",attribute:"autoplay"},loop:{type:"boolean",source:"attribute",selector:"audio",attribute:"loop"},preload:{type:"string",source:"attribute",selector:"audio",attribute:"preload"}},transforms:{from:[{type:"files",isMatch:function(e){return 1===e.length&&0===e[0].type.indexOf("audio/")},transform:function(e){var t=e[0];return Object(D.createBlock)("core/audio",{src:Object(de.createBlobURL)(t)})}}]},supports:{align:!0},edit:Tt,save:function(e){var t=e.attributes,n=t.autoplay,r=t.caption,o=t.loop,a=t.preload,c=t.src;return Object(U.createElement)("figure",null,Object(U.createElement)("audio",{controls:"controls",src:c,autoPlay:n,loop:o,preload:a}),!$.RichText.isEmpty(r)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:r}))}},Bt=window.getComputedStyle,At=Object(Q.withFallbackStyles)(function(e,t){var n=t.textColor,r=t.backgroundColor,o=r&&r.color,a=n&&n.color,c=!a&&e?e.querySelector('[contenteditable="true"]'):null;return{fallbackBackgroundColor:o||!e?void 0:Bt(e).backgroundColor,fallbackTextColor:a||!c?void 0:Bt(c).color}}),It=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).nodeRef=null,e.bindRef=e.bindRef.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"bindRef",value:function(e){e&&(this.nodeRef=e)}},{key:"render",value:function(){var e,t=this.props,n=t.attributes,r=t.backgroundColor,o=t.textColor,a=t.setBackgroundColor,c=t.setTextColor,i=t.fallbackBackgroundColor,l=t.fallbackTextColor,s=t.setAttributes,u=t.isSelected,b=t.className,m=n.text,d=n.url,h=n.title;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)("div",{className:b,title:h,ref:this.bindRef},Object(U.createElement)($.RichText,{placeholder:Object(K.__)("Add text…"),value:m,onChange:function(e){return s({text:e})},formattingControls:["bold","italic","strikethrough"],className:q()("wp-block-button__link",(e={"has-background":r.color},Object(F.a)(e,r.class,r.class),Object(F.a)(e,"has-text-color",o.color),Object(F.a)(e,o.class,o.class),e)),style:{backgroundColor:r.color,color:o.color},keepPlaceholderOnFocus:!0}),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)($.PanelColorSettings,{title:Object(K.__)("Color Settings"),colorSettings:[{value:r.color,onChange:a,label:Object(K.__)("Background Color")},{value:o.color,onChange:c,label:Object(K.__)("Text Color")}]},Object(U.createElement)($.ContrastChecker,{isLargeText:!1,textColor:o.color,backgroundColor:r.color,fallbackBackgroundColor:i,fallbackTextColor:l})))),u&&Object(U.createElement)("form",{className:"block-library-button__inline-link",onSubmit:function(e){return e.preventDefault()}},Object(U.createElement)(Q.Dashicon,{icon:"admin-links"}),Object(U.createElement)($.URLInput,{value:d,onChange:function(e){return s({url:e})}}),Object(U.createElement)(Q.IconButton,{icon:"editor-break",label:Object(K.__)("Apply"),type:"submit"})))}}]),t}(U.Component),Pt=Object(re.compose)([Object($.withColors)("backgroundColor",{textColor:"color"}),At])(It),Lt={url:{type:"string",source:"attribute",selector:"a",attribute:"href"},title:{type:"string",source:"attribute",selector:"a",attribute:"title"},text:{type:"string",source:"html",selector:"a"},backgroundColor:{type:"string"},textColor:{type:"string"},customBackgroundColor:{type:"string"},customTextColor:{type:"string"}},Mt="core/button",zt=function(e){return Object(G.omit)(Object(V.a)({},e,{customTextColor:e.textColor&&"#"===e.textColor[0]?e.textColor:void 0,customBackgroundColor:e.color&&"#"===e.color[0]?e.color:void 0}),["color","textColor"])},Ht={title:Object(K.__)("Button"),description:Object(K.__)("Prompt visitors to take action with a custom button."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M19 6H5L3 8v8l2 2h14l2-2V8l-2-2zm0 10H5V8h14v8z"}))),category:"layout",attributes:Lt,supports:{align:!0,alignWide:!1},styles:[{name:"default",label:Object(K._x)("Rounded","block style"),isDefault:!0},{name:"outline",label:Object(K.__)("Outline")},{name:"squared",label:Object(K._x)("Squared","block style")}],edit:Pt,save:function(e){var t,n=e.attributes,r=n.url,o=n.text,a=n.title,c=n.backgroundColor,i=n.textColor,l=n.customBackgroundColor,s=n.customTextColor,u=Object($.getColorClassName)("color",i),b=Object($.getColorClassName)("background-color",c),m=q()("wp-block-button__link",(t={"has-text-color":i||s},Object(F.a)(t,u,u),Object(F.a)(t,"has-background",c||l),Object(F.a)(t,b,b),t)),d={backgroundColor:b?void 0:l,color:u?void 0:s};return Object(U.createElement)("div",null,Object(U.createElement)($.RichText.Content,{tagName:"a",className:m,href:r,title:a,style:d,value:o}))},deprecated:[{attributes:Object(V.a)({},Object(G.pick)(Lt,["url","title","text"]),{color:{type:"string"},textColor:{type:"string"},align:{type:"string",default:"none"}}),save:function(e){var t=e.attributes,n=t.url,r=t.text,o=t.title,a=t.align,c={backgroundColor:t.color,color:t.textColor};return Object(U.createElement)("div",{className:"align".concat(a)},Object(U.createElement)($.RichText.Content,{tagName:"a",className:"wp-block-button__link",href:n,title:o,style:c,value:r}))},migrate:zt},{attributes:Object(V.a)({},Object(G.pick)(Lt,["url","title","text"]),{color:{type:"string"},textColor:{type:"string"},align:{type:"string",default:"none"}}),save:function(e){var t=e.attributes,n=t.url,r=t.text,o=t.title,a=t.align,c=t.color,i=t.textColor;return Object(U.createElement)("div",{className:"align".concat(a),style:{backgroundColor:c}},Object(U.createElement)($.RichText.Content,{tagName:"a",href:n,title:o,style:{color:i},value:r}))},migrate:zt}]},Dt=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).toggleDisplayAsDropdown=e.toggleDisplayAsDropdown.bind(Object(ne.a)(Object(ne.a)(e))),e.toggleShowPostCounts=e.toggleShowPostCounts.bind(Object(ne.a)(Object(ne.a)(e))),e.toggleShowHierarchy=e.toggleShowHierarchy.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"toggleDisplayAsDropdown",value:function(){var e=this.props,t=e.attributes;(0,e.setAttributes)({displayAsDropdown:!t.displayAsDropdown})}},{key:"toggleShowPostCounts",value:function(){var e=this.props,t=e.attributes;(0,e.setAttributes)({showPostCounts:!t.showPostCounts})}},{key:"toggleShowHierarchy",value:function(){var e=this.props,t=e.attributes;(0,e.setAttributes)({showHierarchy:!t.showHierarchy})}},{key:"getCategories",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=this.props.categories;return t&&t.length?null===e?t:t.filter(function(t){return t.parent===e}):[]}},{key:"getCategoryListClassName",value:function(e){var t=this.props.className;return"".concat(t,"__list ").concat(t,"__list-level-").concat(e)}},{key:"renderCategoryName",value:function(e){return e.name?Object(G.unescape)(e.name).trim():Object(K.__)("(Untitled)")}},{key:"renderCategoryList",value:function(){var e=this,t=this.props.attributes.showHierarchy?0:null,n=this.getCategories(t);return Object(U.createElement)("ul",{className:this.getCategoryListClassName(0)},n.map(function(t){return e.renderCategoryListItem(t,0)}))}},{key:"renderCategoryListItem",value:function(e,t){var n=this,r=this.props.attributes,o=r.showHierarchy,a=r.showPostCounts,c=this.getCategories(e.id);return Object(U.createElement)("li",{key:e.id},Object(U.createElement)("a",{href:e.link,target:"_blank"},this.renderCategoryName(e)),a&&Object(U.createElement)("span",{className:"".concat(this.props.className,"__post-count")}," ","(",e.count,")"),o&&!!c.length&&Object(U.createElement)("ul",{className:this.getCategoryListClassName(t+1)},c.map(function(e){return n.renderCategoryListItem(e,t+1)})))}},{key:"renderCategoryDropdown",value:function(){var e=this,t=this.props,n=t.showHierarchy,r=t.instanceId,o=t.className,a=n?0:null,c=this.getCategories(a),i="blocks-category-select-".concat(r);return Object(U.createElement)(U.Fragment,null,Object(U.createElement)("label",{htmlFor:i,className:"screen-reader-text"},Object(K.__)("Categories")),Object(U.createElement)("select",{id:i,className:"".concat(o,"__dropdown")},c.map(function(t){return e.renderCategoryDropdownItem(t,0)})))}},{key:"renderCategoryDropdownItem",value:function(e,t){var n=this,r=this.props.attributes,o=r.showHierarchy,a=r.showPostCounts,c=this.getCategories(e.id);return[Object(U.createElement)("option",{key:e.id},Object(G.times)(3*t,function(){return" "}),this.renderCategoryName(e),a?" (".concat(e.count,")"):""),o&&!!c.length&&c.map(function(e){return n.renderCategoryDropdownItem(e,t+1)})]}},{key:"render",value:function(){var e=this.props,t=e.attributes,n=e.setAttributes,r=e.isRequesting,o=t.align,a=t.displayAsDropdown,c=t.showHierarchy,i=t.showPostCounts,l=Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Categories Settings")},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Display as Dropdown"),checked:a,onChange:this.toggleDisplayAsDropdown}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Show Hierarchy"),checked:c,onChange:this.toggleShowHierarchy}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Show Post Counts"),checked:i,onChange:this.toggleShowPostCounts})));return r?Object(U.createElement)(U.Fragment,null,l,Object(U.createElement)(Q.Placeholder,{icon:"admin-post",label:Object(K.__)("Categories")},Object(U.createElement)(Q.Spinner,null))):Object(U.createElement)(U.Fragment,null,l,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.BlockAlignmentToolbar,{value:o,onChange:function(e){n({align:e})},controls:["left","center","right","full"]})),Object(U.createElement)("div",{className:this.props.className},a?this.renderCategoryDropdown():this.renderCategoryList()))}}]),t}(U.Component),Ft=Object(re.compose)(Object(oe.withSelect)(function(e){var t=e("core").getEntityRecords,n=e("core/data").isResolving,r={per_page:-1};return{categories:t("taxonomy","category",r),isRequesting:n("core","getEntityRecords",["taxonomy","category",r])}}),re.withInstanceId)(Dt),Vt="core/categories",Ut={title:Object(K.__)("Categories"),description:Object(K.__)("Display a list of all categories."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"M12,2l-5.5,9h11L12,2z M12,5.84L13.93,9h-3.87L12,5.84z"}),Object(U.createElement)(Q.Path,{d:"m17.5 13c-2.49 0-4.5 2.01-4.5 4.5s2.01 4.5 4.5 4.5 4.5-2.01 4.5-4.5-2.01-4.5-4.5-4.5zm0 7c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"}),Object(U.createElement)(Q.Path,{d:"m3 21.5h8v-8h-8v8zm2-6h4v4h-4v-4z"})),category:"widgets",attributes:{align:{type:"string"},displayAsDropdown:{type:"boolean",default:!1},showHierarchy:{type:"boolean",default:!1},showPostCounts:{type:"boolean",default:!1}},supports:{html:!1},getEditWrapperProps:function(e){var t=e.align;if(["left","center","right","full"].includes(t))return{"data-align":t}},edit:Ft,save:function(){return null}};var Wt="core/code",qt={title:Object(K.__)("Code"),description:Object(K.__)("Display code snippets that respect your spacing and tabs."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"M9.4,16.6L4.8,12l4.6-4.6L8,6l-6,6l6,6L9.4,16.6z M14.6,16.6l4.6-4.6l-4.6-4.6L16,6l6,6l-6,6L14.6,16.6z"})),category:"formatting",attributes:{content:{type:"string",source:"text",selector:"code"}},supports:{html:!1},transforms:{from:[{type:"enter",regExp:/^```$/,transform:function(){return Object(D.createBlock)("core/code")}},{type:"raw",isMatch:function(e){return"PRE"===e.nodeName&&1===e.children.length&&"CODE"===e.firstChild.nodeName},schema:{pre:{children:{code:{children:{"#text":{}}}}}}}]},edit:function(e){var t=e.attributes,n=e.setAttributes,r=e.className;return Object(U.createElement)("div",{className:r},Object(U.createElement)($.PlainText,{value:t.content,onChange:function(e){return n({content:e})},placeholder:Object(K.__)("Write code…"),"aria-label":Object(K.__)("Code")}))},save:function(e){var t=e.attributes;return Object(U.createElement)("pre",null,Object(U.createElement)("code",null,t.content))}},Gt=n(41),Kt=["core/column"],$t=n.n(Gt)()(function(e){return Object(G.times)(e,function(){return["core/column"]})});function Qt(e){var t,n=Qt.doc;n||(n=document.implementation.createHTMLDocument(""),Qt.doc=n),n.body.innerHTML=e;var r=!0,o=!1,a=void 0;try{for(var c,i=n.body.firstChild.classList[Symbol.iterator]();!(r=(c=i.next()).done);r=!0){if(t=c.value.match(/^layout-column-(\d+)$/))return Number(t[1])-1}}catch(e){o=!0,a=e}finally{try{r||null==i.return||i.return()}finally{if(o)throw a}}}var Yt="core/columns",Zt={title:Object(K.__)("Columns"),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M21 4H3L2 5v14l1 1h18l1-1V5l-1-1zM8 18H4V6h4v12zm6 0h-4V6h4v12zm6 0h-4V6h4v12z"}))),category:"layout",attributes:{columns:{type:"number",default:2}},description:Object(K.__)("Add a block that displays content in multiple columns, then add whatever content blocks you’d like."),supports:{align:["wide","full"],html:!1},deprecated:[{attributes:{columns:{type:"number",default:2}},isEligible:function(e,t){return!!t.some(function(e){return/layout-column-\d+/.test(e.originalContent)})&&t.some(function(e){return void 0!==Qt(e.originalContent)})},migrate:function(e,t){return[e,t.reduce(function(e,t){var n=Qt(t.originalContent);return void 0===n&&(n=0),e[n]||(e[n]=[]),e[n].push(t),e},[]).map(function(e){return Object(D.createBlock)("core/column",{},e)})]},save:function(e){var t=e.attributes.columns;return Object(U.createElement)("div",{className:"has-".concat(t,"-columns")},Object(U.createElement)($.InnerBlocks.Content,null))}}],edit:function(e){var t=e.attributes,n=e.setAttributes,r=e.className,o=t.columns,a=q()(r,"has-".concat(o,"-columns"));return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,null,Object(U.createElement)(Q.RangeControl,{label:Object(K.__)("Columns"),value:o,onChange:function(e){n({columns:e})},min:2,max:6}))),Object(U.createElement)("div",{className:a},Object(U.createElement)($.InnerBlocks,{template:$t(o),templateLock:"all",allowedBlocks:Kt})))},save:function(e){var t=e.attributes.columns;return Object(U.createElement)("div",{className:"has-".concat(t,"-columns")},Object(U.createElement)($.InnerBlocks.Content,null))}},Jt="core/column",Xt={title:Object(K.__)("Column"),parent:["core/columns"],icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.Path,{d:"M11.99 18.54l-7.37-5.73L3 14.07l9 7 9-7-1.63-1.27zM12 16l7.36-5.73L21 9l-9-7-9 7 1.63 1.27L12 16zm0-11.47L17.74 9 12 13.47 6.26 9 12 4.53z"})),description:Object(K.__)("A single column within a columns block."),category:"common",supports:{inserter:!1,reusable:!1,html:!1},edit:function(){return Object(U.createElement)($.InnerBlocks,{templateLock:!1})},save:function(){return Object(U.createElement)("div",null,Object(U.createElement)($.InnerBlocks.Content,null))}},en=["left","center","right","wide","full"],tn={title:{type:"string",source:"html",selector:"p"},url:{type:"string"},align:{type:"string"},contentAlign:{type:"string",default:"center"},id:{type:"number"},hasParallax:{type:"boolean",default:!1},dimRatio:{type:"number",default:50},overlayColor:{type:"string"},customOverlayColor:{type:"string"},backgroundType:{type:"string",default:"image"}},nn="core/cover",rn=["image","video"],on={title:Object(K.__)("Cover"),description:Object(K.__)("Add an image or video with a text overlay — great for headers."),icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M4 4h7V2H4c-1.1 0-2 .9-2 2v7h2V4zm6 9l-4 5h12l-3-4-2.03 2.71L10 13zm7-4.5c0-.83-.67-1.5-1.5-1.5S14 7.67 14 8.5s.67 1.5 1.5 1.5S17 9.33 17 8.5zM20 2h-7v2h7v7h2V4c0-1.1-.9-2-2-2zm0 18h-7v2h7c1.1 0 2-.9 2-2v-7h-2v7zM4 13H2v7c0 1.1.9 2 2 2h7v-2H4v-7z"}),Object(U.createElement)(Q.Path,{d:"M0 0h24v24H0z",fill:"none"})),category:"common",attributes:tn,transforms:{from:[{type:"block",blocks:["core/heading"],transform:function(e){var t=e.content;return Object(D.createBlock)("core/cover",{title:t})}},{type:"block",blocks:["core/image"],transform:function(e){var t=e.caption,n=e.url,r=e.align,o=e.id;return Object(D.createBlock)("core/cover",{title:t,url:n,align:r,id:o})}},{type:"block",blocks:["core/video"],transform:function(e){var t=e.caption,n=e.src,r=e.align,o=e.id;return Object(D.createBlock)("core/cover",{title:t,url:n,align:r,id:o,backgroundType:"video"})}}],to:[{type:"block",blocks:["core/heading"],transform:function(e){var t=e.title;return Object(D.createBlock)("core/heading",{content:t})}},{type:"block",blocks:["core/image"],isMatch:function(e){var t=e.backgroundType;return!e.url||"image"===t},transform:function(e){var t=e.title,n=e.url,r=e.align,o=e.id;return Object(D.createBlock)("core/image",{caption:t,url:n,align:r,id:o})}},{type:"block",blocks:["core/video"],isMatch:function(e){var t=e.backgroundType;return!e.url||"video"===t},transform:function(e){var t=e.title,n=e.url,r=e.align,o=e.id;return Object(D.createBlock)("core/video",{caption:t,src:n,id:o,align:r})}}]},getEditWrapperProps:function(e){var t=e.align;if(-1!==en.indexOf(t))return{"data-align":t}},edit:Object(re.compose)([Object($.withColors)({overlayColor:"background-color"}),Q.withNotices])(function(e){var t=e.attributes,n=e.setAttributes,r=e.isSelected,o=e.className,a=e.noticeOperations,c=e.noticeUI,i=e.overlayColor,l=e.setOverlayColor,s=t.align,u=t.backgroundType,b=t.contentAlign,m=t.dimRatio,d=t.hasParallax,h=t.id,p=t.title,g=t.url,O=function(e){if(e&&e.url){var t;if(e.media_type)t="image"===e.media_type?"image":"video";else{if("image"!==e.type&&"video"!==e.type)return;t=e.type}n({url:e.url,id:e.id,backgroundType:t})}else n({url:void 0,id:void 0})},f=function(e){return n({title:e})},j=Object(V.a)({},"image"===u?cn(g):{},{backgroundColor:i.color}),v=Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.BlockAlignmentToolbar,{value:s,onChange:function(e){return n({align:e})}}),!!g&&Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.AlignmentToolbar,{value:b,onChange:function(e){n({contentAlign:e})}}),Object(U.createElement)($.MediaUploadCheck,null,Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)($.MediaUpload,{onSelect:O,allowedTypes:rn,value:h,render:function(e){var t=e.open;return Object(U.createElement)(Q.IconButton,{className:"components-toolbar__control",label:Object(K.__)("Edit media"),icon:"edit",onClick:t})}}))))),!!g&&Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Cover Settings")},"image"===u&&Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Fixed Background"),checked:d,onChange:function(){return n({hasParallax:!d})}}),Object(U.createElement)($.PanelColorSettings,{title:Object(K.__)("Overlay"),initialOpen:!0,colorSettings:[{value:i.color,onChange:l,label:Object(K.__)("Overlay Color")}]},Object(U.createElement)(Q.RangeControl,{label:Object(K.__)("Background Opacity"),value:m,onChange:function(e){return n({dimRatio:e})},min:0,max:100,step:10})))));if(!g){var y=!$.RichText.isEmpty(p),k=y?void 0:"format-image",w=y?Object(U.createElement)($.RichText,{tagName:"h2",value:p,onChange:f,inlineToolbar:!0}):Object(K.__)("Cover");return Object(U.createElement)(U.Fragment,null,v,Object(U.createElement)($.MediaPlaceholder,{icon:k,className:o,labels:{title:w,instructions:Object(K.__)("Drag an image or a video, upload a new one or select a file from your library.")},onSelect:O,accept:"image/*,video/*",allowedTypes:rn,notices:c,onError:a.createErrorNotice}))}var E=q()(o,"center"!==b&&"has-".concat(b,"-content"),an(m),{"has-background-dim":0!==m,"has-parallax":d});return Object(U.createElement)(U.Fragment,null,v,Object(U.createElement)("div",{"data-url":g,style:j,className:E},"video"===u&&Object(U.createElement)("video",{className:"wp-block-cover__video-background",autoPlay:!0,muted:!0,loop:!0,src:g}),(!$.RichText.isEmpty(p)||r)&&Object(U.createElement)($.RichText,{tagName:"p",className:"wp-block-cover-text",placeholder:Object(K.__)("Write title…"),value:p,onChange:f,inlineToolbar:!0})))}),save:function(e){var t=e.attributes,n=t.align,r=t.backgroundType,o=t.contentAlign,a=t.customOverlayColor,c=t.dimRatio,i=t.hasParallax,l=t.overlayColor,s=t.title,u=t.url,b=Object($.getColorClassName)("background-color",l),m="image"===r?cn(u):{};b||(m.backgroundColor=a);var d=q()(an(c),b,Object(F.a)({"has-background-dim":0!==c,"has-parallax":i},"has-".concat(o,"-content"),"center"!==o),n?"align".concat(n):null);return Object(U.createElement)("div",{className:d,style:m},"video"===r&&u&&Object(U.createElement)("video",{className:"wp-block-cover__video-background",autoPlay:!0,muted:!0,loop:!0,src:u}),!$.RichText.isEmpty(s)&&Object(U.createElement)($.RichText.Content,{tagName:"p",className:"wp-block-cover-text",value:s}))},deprecated:[{attributes:Object(V.a)({},tn),supports:{className:!1},save:function(e){var t=e.attributes,n=t.url,r=t.title,o=t.hasParallax,a=t.dimRatio,c=t.align,i=t.contentAlign,l=t.overlayColor,s=t.customOverlayColor,u=Object($.getColorClassName)("background-color",l),b=cn(n);u||(b.backgroundColor=s);var m=q()("wp-block-cover-image",an(a),u,Object(F.a)({"has-background-dim":0!==a,"has-parallax":o},"has-".concat(i,"-content"),"center"!==i),c?"align".concat(c):null);return Object(U.createElement)("div",{className:m,style:b},!$.RichText.isEmpty(r)&&Object(U.createElement)($.RichText.Content,{tagName:"p",className:"wp-block-cover-image-text",value:r}))}},{attributes:Object(V.a)({},tn,{title:{type:"string",source:"html",selector:"h2"}}),save:function(e){var t=e.attributes,n=t.url,r=t.title,o=t.hasParallax,a=t.dimRatio,c=t.align,i=cn(n),l=q()(an(a),{"has-background-dim":0!==a,"has-parallax":o},c?"align".concat(c):null);return Object(U.createElement)("section",{className:l,style:i},Object(U.createElement)($.RichText.Content,{tagName:"h2",value:r}))}}]};function an(e){return 0===e||50===e?null:"has-background-dim-"+10*Math.round(e/10)}function cn(e){return e?{backgroundImage:"url(".concat(e,")")}:{}}var ln=function(e){var t=e.blockSupportsResponsive,n=e.showEditButton,r=e.themeSupportsResponsive,o=e.allowResponsive,a=e.getResponsiveHelp,c=e.toggleResponsive,i=e.switchBackToURLInput;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)(Q.Toolbar,null,n&&Object(U.createElement)(Q.IconButton,{className:"components-toolbar__control",label:Object(K.__)("Edit URL"),icon:"edit",onClick:i}))),r&&t&&Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Media Settings"),className:"blocks-responsive"},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Resize for smaller devices"),checked:o,help:a,onChange:c}))))},sn=function(){return Object(U.createElement)("div",{className:"wp-block-embed is-loading"},Object(U.createElement)(Q.Spinner,null),Object(U.createElement)("p",null,Object(K.__)("Embedding…")))},un=function(e){var t=e.icon,n=e.label,r=e.value,o=e.onSubmit,a=e.onChange,c=e.cannotEmbed,i=e.fallback,l=e.tryAgain;return Object(U.createElement)(Q.Placeholder,{icon:Object(U.createElement)($.BlockIcon,{icon:t,showColors:!0}),label:n,className:"wp-block-embed"},Object(U.createElement)("form",{onSubmit:o},Object(U.createElement)("input",{type:"url",value:r||"",className:"components-placeholder__input","aria-label":n,placeholder:Object(K.__)("Enter URL to embed here…"),onChange:a}),Object(U.createElement)(Q.Button,{isLarge:!0,type:"submit"},Object(K._x)("Embed","button label")),c&&Object(U.createElement)("p",{className:"components-placeholder__error"},Object(K.__)("Sorry, we could not embed that content."),Object(U.createElement)("br",null),Object(U.createElement)(Q.Button,{isLarge:!0,onClick:l},Object(K._x)("Try again","button label"))," ",Object(U.createElement)(Q.Button,{isLarge:!0,onClick:i},Object(K._x)("Convert to link","button label")))))},bn=n(75),mn=window.FocusEvent,dn=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).checkFocus=e.checkFocus.bind(Object(ne.a)(Object(ne.a)(e))),e.node=Object(U.createRef)(),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"checkFocus",value:function(){var e=document.activeElement;if("IFRAME"===e.tagName&&e.parentNode===this.node.current){var t=new mn("focus",{bubbles:!0});e.dispatchEvent(t)}}},{key:"render",value:function(){var e=this.props.html;return Object(U.createElement)("div",{ref:this.node,className:"wp-block-embed__wrapper",dangerouslySetInnerHTML:{__html:e}})}}]),t}(U.Component),hn=Object(re.withGlobalEvents)({blur:"checkFocus"})(dn),pn=function(e){var t,n,r=e.preview,o=e.url,a=e.type,c=e.caption,i=e.onCaptionChange,l=e.isSelected,s=e.className,u=e.icon,b=e.label,m=r.scripts,d="photo"===a?(t=r,n=Object(U.createElement)("p",null,Object(U.createElement)("img",{src:t.thumbnail_url,alt:t.title,width:"100%"})),Object(U.renderToString)(n)):r.html,h=Object(bn.parse)(o),p=Object(G.includes)(Ae,h.host.replace(/^www\./,"")),g=Object(K.sprintf)(Object(K.__)("Embedded content from %s"),h.host),O=Le()(a,s,"wp-block-embed__wrapper"),f="wp-embed"===a?Object(U.createElement)(hn,{html:d}):Object(U.createElement)("div",{className:"wp-block-embed__wrapper"},Object(U.createElement)(Q.SandBox,{html:d,scripts:m,title:g,type:O}));return Object(U.createElement)("figure",{className:Le()(s,"wp-block-embed",{"is-type-video":"video"===a})},p?Object(U.createElement)(Q.Placeholder,{icon:Object(U.createElement)($.BlockIcon,{icon:u,showColors:!0}),label:b},Object(U.createElement)("p",{className:"components-placeholder__error"},Object(U.createElement)("a",{href:o},o)),Object(U.createElement)("p",{className:"components-placeholder__error"},Object(K.__)("Sorry, we cannot preview this embedded content in the editor."))):f,(!$.RichText.isEmpty(c)||l)&&Object(U.createElement)($.RichText,{tagName:"figcaption",placeholder:Object(K.__)("Write caption…"),value:c,onChange:i,inlineToolbar:!0}))};function gn(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return function(r){function o(){var e;return Object(Z.a)(this,o),(e=Object(X.a)(this,Object(ee.a)(o).apply(this,arguments))).switchBackToURLInput=e.switchBackToURLInput.bind(Object(ne.a)(Object(ne.a)(e))),e.setUrl=e.setUrl.bind(Object(ne.a)(Object(ne.a)(e))),e.getAttributesFromPreview=e.getAttributesFromPreview.bind(Object(ne.a)(Object(ne.a)(e))),e.setAttributesFromPreview=e.setAttributesFromPreview.bind(Object(ne.a)(Object(ne.a)(e))),e.getResponsiveHelp=e.getResponsiveHelp.bind(Object(ne.a)(Object(ne.a)(e))),e.toggleResponsive=e.toggleResponsive.bind(Object(ne.a)(Object(ne.a)(e))),e.handleIncomingPreview=e.handleIncomingPreview.bind(Object(ne.a)(Object(ne.a)(e))),e.state={editingURL:!1,url:e.props.attributes.url},e.props.preview&&e.handleIncomingPreview(),e}return Object(te.a)(o,r),Object(J.a)(o,[{key:"handleIncomingPreview",value:function(){var e=this.props.attributes.allowResponsive;this.setAttributesFromPreview();var t=He(this.props,this.getAttributesFromPreview(this.props.preview,e));t&&this.props.onReplace(t)}},{key:"componentDidUpdate",value:function(e){var t=void 0!==this.props.preview,n=void 0!==e.preview,r=e.preview&&this.props.preview&&this.props.preview.html!==e.preview.html||t&&!n,o=this.props.attributes.url!==e.attributes.url;if(r||o){if(this.props.cannotEmbed)return;this.handleIncomingPreview()}}},{key:"setUrl",value:function(e){e&&e.preventDefault();var t=this.state.url,n=this.props.setAttributes;this.setState({editingURL:!1}),n({url:t})}},{key:"getAttributesFromPreview",value:function(t){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o={},a=t.type,c=void 0===a?"rich":a,i=t.html,l=t.provider_name,s=Object(G.kebabCase)(Object(G.toLower)(""!==l?l:e));return ze(i)&&(c="wp-embed"),(i||"photo"===c)&&(o.type=c,o.providerNameSlug=s),o.className=De(i,this.props.attributes.className,n&&r),o}},{key:"setAttributesFromPreview",value:function(){var e=this.props,t=e.setAttributes,n=e.preview,r=this.props.attributes.allowResponsive;t(this.getAttributesFromPreview(n,r))}},{key:"switchBackToURLInput",value:function(){this.setState({editingURL:!0})}},{key:"getResponsiveHelp",value:function(e){return e?Object(K.__)("This embed will preserve its aspect ratio when the browser is resized."):Object(K.__)("This embed may not preserve its aspect ratio when the browser is resized.")}},{key:"toggleResponsive",value:function(){var e=this.props.attributes,t=e.allowResponsive,r=e.className,o=this.props.preview.html,a=!t;this.props.setAttributes({allowResponsive:a,className:De(o,r,n&&a)})}},{key:"render",value:function(){var r=this,o=this.state,a=o.url,c=o.editingURL,i=this.props.attributes,l=i.caption,s=i.type,u=i.allowResponsive,b=this.props,m=b.fetching,d=b.setAttributes,h=b.isSelected,p=b.className,g=b.preview,O=b.cannotEmbed,f=b.themeSupportsResponsive,j=b.tryAgain;if(m)return Object(U.createElement)(sn,null);var v=Object(K.sprintf)(Object(K.__)("%s URL"),e);return!g||O||c?Object(U.createElement)(un,{icon:t,label:v,onSubmit:this.setUrl,value:a,cannotEmbed:O,onChange:function(e){return r.setState({url:e.target.value})},fallback:function(){return function(e,t){var n=Object(U.createElement)("a",{href:e},e);t(Object(D.createBlock)("core/paragraph",{content:Object(U.renderToString)(n)}))}(a,r.props.onReplace)},tryAgain:j}):Object(U.createElement)(U.Fragment,null,Object(U.createElement)(ln,{showEditButton:g&&!O,themeSupportsResponsive:f,blockSupportsResponsive:n,allowResponsive:u,getResponsiveHelp:this.getResponsiveHelp,toggleResponsive:this.toggleResponsive,switchBackToURLInput:this.switchBackToURLInput}),Object(U.createElement)(pn,{preview:g,className:p,url:a,type:s,caption:l,onCaptionChange:function(e){return d({caption:e})},isSelected:h,icon:t,label:v}))}}]),o}(U.Component)}var On={url:{type:"string"},caption:{type:"string",source:"html",selector:"figcaption"},type:{type:"string"},providerNameSlug:{type:"string"},allowResponsive:{type:"boolean",default:!0}};function fn(e){var t=e.title,n=e.description,r=e.icon,o=e.category,a=void 0===o?"embed":o,c=e.transforms,i=e.keywords,l=void 0===i?[]:i,s=e.supports,u=void 0===s?{}:s,b=e.responsive,m=void 0===b||b,d=n||Object(K.sprintf)(Object(K.__)("Add a block that displays content pulled from other sites, like Twitter, Instagram or YouTube."),t),h=gn(t,r,m);return{title:t,description:d,icon:r,category:a,keywords:l,attributes:On,supports:Object(V.a)({align:!0},u),transforms:c,edit:Object(re.compose)(Object(oe.withSelect)(function(e,t){var n=t.attributes.url,r=e("core"),o=r.getEmbedPreview,a=r.isPreviewEmbedFallback,c=r.isRequestingEmbedPreview,i=r.getThemeSupports,l=void 0!==n&&o(n),s=void 0!==n&&a(n),u=void 0!==n&&c(n),b=i(),m=!!l&&void 0===l.type&&!1===l.html,d=!!l&&l.data&&404===l.data.status,h=!!l&&!m&&!d,p=void 0!==n&&(!h||s);return{preview:h?l:void 0,fetching:u,themeSupportsResponsive:b["responsive-embeds"],cannotEmbed:p}}),Object(oe.withDispatch)(function(e,t){var n=t.attributes.url,r=e("core/data");return{tryAgain:function(){r.invalidateResolution("core","getEmbedPreview",[n])}}}))(h),save:function(e){var t,n=e.attributes,r=n.url,o=n.caption,a=n.type,c=n.providerNameSlug;if(!r)return null;var i=Le()("wp-block-embed",(t={},Object(F.a)(t,"is-type-".concat(a),a),Object(F.a)(t,"is-provider-".concat(c),c),t));return Object(U.createElement)("figure",{className:i},Object(U.createElement)("div",{className:"wp-block-embed__wrapper"},"\n".concat(r,"\n")),!$.RichText.isEmpty(o)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:o}))},deprecated:[{attributes:On,save:function(e){var t,n=e.attributes,r=n.url,o=n.caption,a=n.type,c=n.providerNameSlug;if(!r)return null;var i=Le()("wp-block-embed",(t={},Object(F.a)(t,"is-type-".concat(a),a),Object(F.a)(t,"is-provider-".concat(c),c),t));return Object(U.createElement)("figure",{className:i},"\n".concat(r,"\n"),!$.RichText.isEmpty(o)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:o}))}}]}}var jn="core/embed",vn=fn({title:Object(K._x)("Embed","block title"),description:Object(K.__)("Embed videos, images, tweets, audio, and other content from external sources."),icon:Oe,responsive:!1,transforms:{from:[{type:"raw",isMatch:function(e){return"P"===e.nodeName&&/^\s*(https?:\/\/\S+)\s*$/i.test(e.textContent)},transform:function(e){return Object(D.createBlock)("core/embed",{url:e.textContent.trim()})}}]}}),yn=Re.map(function(e){return Object(V.a)({},e,{settings:fn(e.settings)})}),kn=Be.map(function(e){return Object(V.a)({},e,{settings:fn(e.settings)})});function wn(e){return e?Object(K.__)("The download button is visible."):Object(K.__)("The download button is hidden.")}function En(e){var t=e.hrefs,n=e.openInNewWindow,r=e.showDownloadButton,o=e.changeLinkDestinationOption,a=e.changeOpenInNewWindow,c=e.changeShowDownloadButton,i=t.href,l=t.textLinkHref,s=t.attachmentPage,u=[{value:i,label:Object(K.__)("URL")}];return s&&(u=[{value:i,label:Object(K.__)("Media File")},{value:s,label:Object(K.__)("Attachment Page")}]),Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Text Link Settings")},Object(U.createElement)(Q.SelectControl,{label:Object(K.__)("Link To"),value:l,options:u,onChange:o}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Open in New Tab"),checked:n,onChange:a})),Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Download Button Settings")},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Show Download Button"),help:wn,checked:r,onChange:c}))))}var Cn=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).onSelectFile=e.onSelectFile.bind(Object(ne.a)(Object(ne.a)(e))),e.confirmCopyURL=e.confirmCopyURL.bind(Object(ne.a)(Object(ne.a)(e))),e.resetCopyConfirmation=e.resetCopyConfirmation.bind(Object(ne.a)(Object(ne.a)(e))),e.changeLinkDestinationOption=e.changeLinkDestinationOption.bind(Object(ne.a)(Object(ne.a)(e))),e.changeOpenInNewWindow=e.changeOpenInNewWindow.bind(Object(ne.a)(Object(ne.a)(e))),e.changeShowDownloadButton=e.changeShowDownloadButton.bind(Object(ne.a)(Object(ne.a)(e))),e.state={hasError:!1,showCopyConfirmation:!1},e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentDidMount",value:function(){var e=this,t=this.props,n=t.attributes,r=t.noticeOperations,o=n.href;if(Object(de.isBlobURL)(o)){var a=Object(de.getBlobByURL)(o);Object($.mediaUpload)({filesList:[a],onFileChange:function(t){var n=Object(he.a)(t,1)[0];return e.onSelectFile(n)},onError:function(t){e.setState({hasError:!0}),r.createErrorNotice(t)}}),Object(de.revokeBlobURL)(o)}}},{key:"componentDidUpdate",value:function(e){e.isSelected&&!this.props.isSelected&&this.setState({showCopyConfirmation:!1})}},{key:"onSelectFile",value:function(e){e&&e.url&&(this.setState({hasError:!1}),this.props.setAttributes({href:e.url,fileName:e.title,textLinkHref:e.url,id:e.id}))}},{key:"confirmCopyURL",value:function(){this.setState({showCopyConfirmation:!0})}},{key:"resetCopyConfirmation",value:function(){this.setState({showCopyConfirmation:!1})}},{key:"changeLinkDestinationOption",value:function(e){this.props.setAttributes({textLinkHref:e})}},{key:"changeOpenInNewWindow",value:function(e){this.props.setAttributes({textLinkTarget:!!e&&"_blank"})}},{key:"changeShowDownloadButton",value:function(e){this.props.setAttributes({showDownloadButton:e})}},{key:"render",value:function(){var e=this.props,t=e.className,n=e.isSelected,r=e.attributes,o=e.setAttributes,a=e.noticeUI,c=e.noticeOperations,i=e.media,l=r.fileName,s=r.href,u=r.textLinkHref,b=r.textLinkTarget,m=r.showDownloadButton,d=r.downloadButtonText,h=r.id,p=this.state,g=p.hasError,O=p.showCopyConfirmation,f=i&&i.link;if(!s||g)return Object(U.createElement)($.MediaPlaceholder,{icon:"media-default",labels:{title:Object(K.__)("File"),instructions:Object(K.__)("Drag a file, upload a new one or select a file from your library.")},onSelect:this.onSelectFile,notices:a,onError:c.createErrorNotice,accept:"*"});var j=q()(t,{"is-transient":Object(de.isBlobURL)(s)});return Object(U.createElement)(U.Fragment,null,Object(U.createElement)(En,Object(Y.a)({hrefs:{href:s,textLinkHref:u,attachmentPage:f}},{openInNewWindow:!!b,showDownloadButton:m,changeLinkDestinationOption:this.changeLinkDestinationOption,changeOpenInNewWindow:this.changeOpenInNewWindow,changeShowDownloadButton:this.changeShowDownloadButton})),Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.MediaUploadCheck,null,Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)($.MediaUpload,{onSelect:this.onSelectFile,value:h,render:function(e){var t=e.open;return Object(U.createElement)(Q.IconButton,{className:"components-toolbar__control",label:Object(K.__)("Edit file"),onClick:t,icon:"edit"})}})))),Object(U.createElement)("div",{className:j},Object(U.createElement)("div",{className:"".concat(t,"__content-wrapper")},Object(U.createElement)($.RichText,{wrapperClassName:"".concat(t,"__textlink"),tagName:"div",value:l,placeholder:Object(K.__)("Write file name…"),keepPlaceholderOnFocus:!0,formattingControls:[],onChange:function(e){return o({fileName:e})}}),m&&Object(U.createElement)("div",{className:"".concat(t,"__button-richtext-wrapper")},Object(U.createElement)($.RichText,{tagName:"div",className:"".concat(t,"__button"),value:d,formattingControls:[],placeholder:Object(K.__)("Add text…"),keepPlaceholderOnFocus:!0,onChange:function(e){return o({downloadButtonText:e})}}))),n&&Object(U.createElement)(Q.ClipboardButton,{isDefault:!0,text:s,className:"".concat(t,"__copy-url-button"),onCopy:this.confirmCopyURL,onFinishCopy:this.resetCopyConfirmation,disabled:Object(de.isBlobURL)(s)},O?Object(K.__)("Copied!"):Object(K.__)("Copy URL"))))}}]),t}(U.Component),_n=Object(re.compose)([Object(oe.withSelect)(function(e,t){var n=e("core").getMedia,r=t.attributes.id;return{media:void 0===r?void 0:n(r)}}),Q.withNotices])(Cn),xn="core/file",Sn={title:Object(K.__)("File"),description:Object(K.__)("Add a link to a downloadable file."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.Path,{d:"M9 6l2 2h9v10H4V6h5m1-2H4L2 6v12l2 2h16l2-2V8l-2-2h-8l-2-2z"})),category:"common",keywords:[Object(K.__)("document"),Object(K.__)("pdf")],attributes:{id:{type:"number"},href:{type:"string"},fileName:{type:"string",source:"html",selector:"a:not([download])"},textLinkHref:{type:"string",source:"attribute",selector:"a:not([download])",attribute:"href"},textLinkTarget:{type:"string",source:"attribute",selector:"a:not([download])",attribute:"target"},showDownloadButton:{type:"boolean",default:!0},downloadButtonText:{type:"string",source:"html",selector:"a[download]",default:Object(K._x)("Download","button label")}},supports:{align:!0},transforms:{from:[{type:"files",isMatch:function(e){return e.length>0},priority:15,transform:function(e){var t=[];return e.map(function(e){var n=Object(de.createBlobURL)(e);t.push(Object(D.createBlock)("core/file",{href:n,fileName:e.name,textLinkHref:n}))}),t}},{type:"block",blocks:["core/audio"],transform:function(e){return Object(D.createBlock)("core/file",{href:e.src,fileName:e.caption,textLinkHref:e.src,id:e.id})}},{type:"block",blocks:["core/video"],transform:function(e){return Object(D.createBlock)("core/file",{href:e.src,fileName:e.caption,textLinkHref:e.src,id:e.id})}},{type:"block",blocks:["core/image"],transform:function(e){return Object(D.createBlock)("core/file",{href:e.url,fileName:e.caption,textLinkHref:e.url,id:e.id})}}],to:[{type:"block",blocks:["core/audio"],isMatch:function(e){var t=e.id;if(!t)return!1;var n=(0,Object(oe.select)("core").getMedia)(t);return!!n&&Object(G.includes)(n.mime_type,"audio")},transform:function(e){return Object(D.createBlock)("core/audio",{src:e.href,caption:e.fileName,id:e.id})}},{type:"block",blocks:["core/video"],isMatch:function(e){var t=e.id;if(!t)return!1;var n=(0,Object(oe.select)("core").getMedia)(t);return!!n&&Object(G.includes)(n.mime_type,"video")},transform:function(e){return Object(D.createBlock)("core/video",{src:e.href,caption:e.fileName,id:e.id})}},{type:"block",blocks:["core/image"],isMatch:function(e){var t=e.id;if(!t)return!1;var n=(0,Object(oe.select)("core").getMedia)(t);return!!n&&Object(G.includes)(n.mime_type,"image")},transform:function(e){return Object(D.createBlock)("core/image",{url:e.href,caption:e.fileName,id:e.id})}}]},edit:_n,save:function(e){var t=e.attributes,n=t.href,r=t.fileName,o=t.textLinkHref,a=t.textLinkTarget,c=t.showDownloadButton,i=t.downloadButtonText;return n&&Object(U.createElement)("div",null,!$.RichText.isEmpty(r)&&Object(U.createElement)("a",{href:o,target:a,rel:!!a&&"noreferrer noopener"},Object(U.createElement)($.RichText.Content,{value:r})),c&&Object(U.createElement)("a",{href:n,className:"wp-block-file__button",download:!0},Object(U.createElement)($.RichText.Content,{value:i})))}},Tn="core/html",Nn={title:Object(K.__)("Custom HTML"),description:Object(K.__)("Add custom HTML code and preview it as you edit."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M4.5,11h-2V9H1v6h1.5v-2.5h2V15H6V9H4.5V11z M7,10.5h1.5V15H10v-4.5h1.5V9H7V10.5z M14.5,10l-1-1H12v6h1.5v-3.9  l1,1l1-1V15H17V9h-1.5L14.5,10z M19.5,13.5V9H18v6h5v-1.5H19.5z"})),category:"formatting",keywords:[Object(K.__)("embed")],supports:{customClassName:!1,className:!1,html:!1},attributes:{content:{type:"string",source:"html"}},transforms:{from:[{type:"raw",isMatch:function(e){return"FIGURE"===e.nodeName&&!!e.querySelector("iframe")},schema:{figure:{require:["iframe"],children:{iframe:{attributes:["src","allowfullscreen","height","width"]},figcaption:{children:Object(D.getPhrasingContentSchema)()}}}}}]},edit:Object(re.withState)({isPreview:!1})(function(e){var t=e.attributes,n=e.setAttributes,r=e.setState,o=e.isPreview;return Object(U.createElement)("div",{className:"wp-block-html"},Object(U.createElement)($.BlockControls,null,Object(U.createElement)("div",{className:"components-toolbar"},Object(U.createElement)("button",{className:"components-tab-button ".concat(o?"":"is-active"),onClick:function(){return r({isPreview:!1})}},Object(U.createElement)("span",null,"HTML")),Object(U.createElement)("button",{className:"components-tab-button ".concat(o?"is-active":""),onClick:function(){return r({isPreview:!0})}},Object(U.createElement)("span",null,Object(K.__)("Preview"))))),Object(U.createElement)(Q.Disabled.Consumer,null,function(e){return o||e?Object(U.createElement)(Q.SandBox,{html:t.content}):Object(U.createElement)($.PlainText,{value:t.content,onChange:function(e){return n({content:e})},placeholder:Object(K.__)("Write HTML…"),"aria-label":Object(K.__)("HTML")})}))}),save:function(e){var t=e.attributes;return Object(U.createElement)(U.RawHTML,null,t.content)}},Rn=["image","video"],Bn=function(e){function t(){return Object(Z.a)(this,t),Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))}return Object(te.a)(t,e),Object(J.a)(t,[{key:"renderToolbarEditButton",value:function(){var e=this.props,t=e.mediaId,n=e.onSelectMedia;return Object(U.createElement)($.BlockControls,null,Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)($.MediaUpload,{onSelect:n,allowedTypes:Rn,value:t,render:function(e){var t=e.open;return Object(U.createElement)(Q.IconButton,{className:"components-toolbar__control",label:Object(K.__)("Edit media"),icon:"edit",onClick:t})}})))}},{key:"renderImage",value:function(){var e=this.props,t=e.mediaAlt,n=e.mediaUrl,r=e.className;return Object(U.createElement)(U.Fragment,null,this.renderToolbarEditButton(),Object(U.createElement)("figure",{className:r},Object(U.createElement)("img",{src:n,alt:t})))}},{key:"renderVideo",value:function(){var e=this.props,t=e.mediaUrl,n=e.className;return Object(U.createElement)(U.Fragment,null,this.renderToolbarEditButton(),Object(U.createElement)("figure",{className:n},Object(U.createElement)("video",{controls:!0,src:t})))}},{key:"renderPlaceholder",value:function(){var e=this.props,t=e.onSelectMedia,n=e.className;return Object(U.createElement)($.MediaPlaceholder,{icon:"format-image",labels:{title:Object(K.__)("Media area")},className:n,onSelect:t,accept:"image/*,video/*",allowedTypes:Rn})}},{key:"render",value:function(){var e=this.props,t=e.mediaPosition,n=e.mediaUrl,r=e.mediaType,o=e.mediaWidth,a=e.commitWidthChange,c=e.onWidthChange;if(r&&n){var i={right:"left"===t,left:"right"===t},l=null;switch(r){case"image":l=this.renderImage();break;case"video":l=this.renderVideo()}return Object(U.createElement)(Q.ResizableBox,{className:"editor-media-container__resizer",size:{width:o+"%"},minWidth:"10%",maxWidth:"100%",enable:i,onResize:function(e,t,n){c(parseInt(n.style.width))},onResizeStop:function(e,t,n){a(parseInt(n.style.width))},axis:"x"},l)}return this.renderPlaceholder()}}]),t}(U.Component),An=["core/button","core/paragraph","core/heading","core/list"],In=[["core/paragraph",{fontSize:"large",placeholder:Object(K._x)("Content…","content placeholder")}]],Pn=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).onSelectMedia=e.onSelectMedia.bind(Object(ne.a)(Object(ne.a)(e))),e.onWidthChange=e.onWidthChange.bind(Object(ne.a)(Object(ne.a)(e))),e.commitWidthChange=e.commitWidthChange.bind(Object(ne.a)(Object(ne.a)(e))),e.state={mediaWidth:null},e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"onSelectMedia",value:function(e){var t,n,r=this.props.setAttributes;"image"===(t=e.media_type?"image"===e.media_type?"image":"video":e.type)&&(n=Object(G.get)(e,["sizes","large","url"])||Object(G.get)(e,["media_details","sizes","large","source_url"])),r({mediaAlt:e.alt,mediaId:e.id,mediaType:t,mediaUrl:n||e.url})}},{key:"onWidthChange",value:function(e){this.setState({mediaWidth:e})}},{key:"commitWidthChange",value:function(e){(0,this.props.setAttributes)({mediaWidth:e}),this.setState({mediaWidth:null})}},{key:"renderMediaArea",value:function(){var e=this.props.attributes,t=e.mediaAlt,n=e.mediaId,r=e.mediaPosition,o=e.mediaType,a=e.mediaUrl,c=e.mediaWidth;return Object(U.createElement)(Bn,Object(Y.a)({className:"block-library-media-text__media-container",onSelectMedia:this.onSelectMedia,onWidthChange:this.onWidthChange,commitWidthChange:this.commitWidthChange},{mediaAlt:t,mediaId:n,mediaType:o,mediaUrl:a,mediaPosition:r,mediaWidth:c}))}},{key:"render",value:function(){var e,t=this.props,n=t.attributes,r=t.className,o=t.backgroundColor,a=t.isSelected,c=t.setAttributes,i=t.setBackgroundColor,l=n.isStackedOnMobile,s=n.mediaAlt,u=n.mediaPosition,b=n.mediaType,m=n.mediaWidth,d=this.state.mediaWidth,h=q()(r,(e={"has-media-on-the-right":"right"===u,"is-selected":a},Object(F.a)(e,o.class,o.class),Object(F.a)(e,"is-stacked-on-mobile",l),e)),p="".concat(d||m,"%"),g={gridTemplateColumns:"right"===u?"auto ".concat(p):"".concat(p," auto"),backgroundColor:o.color},O=[{value:o.color,onChange:i,label:Object(K.__)("Background Color")}],f=[{icon:"align-pull-left",title:Object(K.__)("Show media on left"),isActive:"left"===u,onClick:function(){return c({mediaPosition:"left"})}},{icon:"align-pull-right",title:Object(K.__)("Show media on right"),isActive:"right"===u,onClick:function(){return c({mediaPosition:"right"})}}],j=Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Media & Text Settings")},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Stack on mobile"),checked:l,onChange:function(){return c({isStackedOnMobile:!l})}}),"image"===b&&Object(U.createElement)(Q.TextareaControl,{label:Object(K.__)("Alt Text (Alternative Text)"),value:s,onChange:function(e){c({mediaAlt:e})},help:Object(K.__)("Alternative text describes your image to people who can’t see it. Add a short description with its key details.")}));return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.InspectorControls,null,j,Object(U.createElement)($.PanelColorSettings,{title:Object(K.__)("Color Settings"),initialOpen:!1,colorSettings:O})),Object(U.createElement)($.BlockControls,null,Object(U.createElement)(Q.Toolbar,{controls:f})),Object(U.createElement)("div",{className:h,style:g},this.renderMediaArea(),Object(U.createElement)($.InnerBlocks,{allowedBlocks:An,template:In,templateInsertUpdatesSelection:!1})))}}]),t}(U.Component),Ln=Object($.withColors)("backgroundColor")(Pn),Mn="core/media-text",zn={align:{type:"string",default:"wide"},backgroundColor:{type:"string"},customBackgroundColor:{type:"string"},mediaAlt:{type:"string",source:"attribute",selector:"figure img",attribute:"alt",default:""},mediaPosition:{type:"string",default:"left"},mediaId:{type:"number"},mediaUrl:{type:"string",source:"attribute",selector:"figure video,figure img",attribute:"src"},mediaType:{type:"string"},mediaWidth:{type:"number",default:50},isStackedOnMobile:{type:"boolean",default:!1}},Hn={title:Object(K.__)("Media & Text"),description:Object(K.__)("Set media and words side-by-side for a richer layout."),icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M13 17h8v-2h-8v2zM3 19h8V5H3v14zM13 9h8V7h-8v2zm0 4h8v-2h-8v2z"})),category:"layout",keywords:[Object(K.__)("image"),Object(K.__)("video")],attributes:zn,supports:{align:["wide","full"],html:!1},transforms:{from:[{type:"block",blocks:["core/image"],transform:function(e){var t=e.alt,n=e.url,r=e.id;return Object(D.createBlock)("core/media-text",{mediaAlt:t,mediaId:r,mediaUrl:n,mediaType:"image"})}},{type:"block",blocks:["core/video"],transform:function(e){var t=e.src,n=e.id;return Object(D.createBlock)("core/media-text",{mediaId:n,mediaUrl:t,mediaType:"video"})}}],to:[{type:"block",blocks:["core/image"],isMatch:function(e){var t=e.mediaType;return!e.mediaUrl||"image"===t},transform:function(e){var t=e.mediaAlt,n=e.mediaId,r=e.mediaUrl;return Object(D.createBlock)("core/image",{alt:t,id:n,url:r})}},{type:"block",blocks:["core/video"],isMatch:function(e){var t=e.mediaType;return!e.mediaUrl||"video"===t},transform:function(e){var t=e.mediaId,n=e.mediaUrl;return Object(D.createBlock)("core/video",{id:t,src:n})}}]},edit:Ln,save:function(e){var t,n,r=e.attributes,o=r.backgroundColor,a=r.customBackgroundColor,c=r.isStackedOnMobile,i=r.mediaAlt,l=r.mediaPosition,s=r.mediaType,u=r.mediaUrl,b=r.mediaWidth,m=r.mediaId,d={image:function(){return Object(U.createElement)("img",{src:u,alt:i,className:m&&"image"===s?"wp-image-".concat(m):null})},video:function(){return Object(U.createElement)("video",{controls:!0,src:u})}},h=Object($.getColorClassName)("background-color",o),p=q()((t={"has-media-on-the-right":"right"===l},Object(F.a)(t,h,h),Object(F.a)(t,"is-stacked-on-mobile",c),t));50!==b&&(n="right"===l?"auto ".concat(b,"%"):"".concat(b,"% auto"));var g={backgroundColor:h?void 0:a,gridTemplateColumns:n};return Object(U.createElement)("div",{className:p,style:g},Object(U.createElement)("figure",{className:"wp-block-media-text__media"},(d[s]||G.noop)()),Object(U.createElement)("div",{className:"wp-block-media-text__content"},Object(U.createElement)($.InnerBlocks.Content,null)))},deprecated:[{attributes:zn,save:function(e){var t,n,r=e.attributes,o=r.backgroundColor,a=r.customBackgroundColor,c=r.isStackedOnMobile,i=r.mediaAlt,l=r.mediaPosition,s=r.mediaType,u=r.mediaUrl,b=r.mediaWidth,m={image:function(){return Object(U.createElement)("img",{src:u,alt:i})},video:function(){return Object(U.createElement)("video",{controls:!0,src:u})}},d=Object($.getColorClassName)("background-color",o),h=q()((t={"has-media-on-the-right":"right"===l},Object(F.a)(t,d,d),Object(F.a)(t,"is-stacked-on-mobile",c),t));50!==b&&(n="right"===l?"auto ".concat(b,"%"):"".concat(b,"% auto"));var p={backgroundColor:d?void 0:a,gridTemplateColumns:n};return Object(U.createElement)("div",{className:h,style:p},Object(U.createElement)("figure",{className:"wp-block-media-text__media"},(m[s]||G.noop)()),Object(U.createElement)("div",{className:"wp-block-media-text__content"},Object(U.createElement)($.InnerBlocks.Content,null)))}}]},Dn=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).setAlignment=e.setAlignment.bind(Object(ne.a)(Object(ne.a)(e))),e.setCommentsToShow=e.setCommentsToShow.bind(Object(ne.a)(Object(ne.a)(e))),e.toggleDisplayAvatar=e.createToggleAttribute("displayAvatar"),e.toggleDisplayDate=e.createToggleAttribute("displayDate"),e.toggleDisplayExcerpt=e.createToggleAttribute("displayExcerpt"),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"createToggleAttribute",value:function(e){var t=this;return function(){var n=t.props.attributes[e];(0,t.props.setAttributes)(Object(F.a)({},e,!n))}}},{key:"setAlignment",value:function(e){this.props.setAttributes({align:e})}},{key:"setCommentsToShow",value:function(e){this.props.setAttributes({commentsToShow:e})}},{key:"render",value:function(){var e=this.props.attributes,t=e.align,n=e.commentsToShow,r=e.displayAvatar,o=e.displayDate,a=e.displayExcerpt;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.BlockAlignmentToolbar,{value:t,onChange:this.setAlignment})),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Latest Comments Settings")},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Display Avatar"),checked:r,onChange:this.toggleDisplayAvatar}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Display Date"),checked:o,onChange:this.toggleDisplayDate}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Display Excerpt"),checked:a,onChange:this.toggleDisplayExcerpt}),Object(U.createElement)(Q.RangeControl,{label:Object(K.__)("Number of Comments"),value:n,onChange:this.setCommentsToShow,min:1,max:100}))),Object(U.createElement)(Q.Disabled,null,Object(U.createElement)($.ServerSideRender,{block:"core/latest-comments",attributes:this.props.attributes})))}}]),t}(U.Component),Fn="core/latest-comments",Vn={title:Object(K.__)("Latest Comments"),description:Object(K.__)("Display a list of your most recent comments."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M22 4l-2-2H4L2 4v12l2 2h14l4 4V4zm-2 0v13l-1-1H4V4h16z"}),Object(U.createElement)(Q.Path,{d:"M6 12h12v2H6zM6 9h12v2H6zM6 6h12v2H6z"}))),category:"widgets",keywords:[Object(K.__)("recent comments")],supports:{html:!1},getEditWrapperProps:function(e){var t=e.align;if(["left","center","right","wide","full"].includes(t))return{"data-align":t}},edit:Dn,save:function(){return null}},Un=n(30),Wn=n.n(Un),qn=n(46),Gn=n(49),Kn={per_page:-1},$n=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).state={categoriesList:[]},e.toggleDisplayPostDate=e.toggleDisplayPostDate.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentWillMount",value:function(){var e=this;this.isStillMounted=!0,this.fetchRequest=Wn()({path:Object(pe.addQueryArgs)("/wp/v2/categories",Kn)}).then(function(t){e.isStillMounted&&e.setState({categoriesList:t})}).catch(function(){e.isStillMounted&&e.setState({categoriesList:[]})})}},{key:"componentWillUnmount",value:function(){this.isStillMounted=!1}},{key:"toggleDisplayPostDate",value:function(){var e=this.props.attributes.displayPostDate;(0,this.props.setAttributes)({displayPostDate:!e})}},{key:"render",value:function(){var e=this.props,t=e.attributes,n=e.setAttributes,r=e.latestPosts,o=this.state.categoriesList,a=t.displayPostDate,c=t.align,i=t.postLayout,l=t.columns,s=t.order,u=t.orderBy,b=t.categories,m=t.postsToShow,d=Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Latest Posts Settings")},Object(U.createElement)(Q.QueryControls,Object(Y.a)({order:s,orderBy:u},{numberOfItems:m,categoriesList:o,selectedCategoryId:b,onOrderChange:function(e){return n({order:e})},onOrderByChange:function(e){return n({orderBy:e})},onCategoryChange:function(e){return n({categories:""!==e?e:void 0})},onNumberOfItemsChange:function(e){return n({postsToShow:e})}})),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Display post date"),checked:a,onChange:this.toggleDisplayPostDate}),"grid"===i&&Object(U.createElement)(Q.RangeControl,{label:Object(K.__)("Columns"),value:l,onChange:function(e){return n({columns:e})},min:2,max:h?Math.min(6,r.length):6}))),h=Array.isArray(r)&&r.length;if(!h)return Object(U.createElement)(U.Fragment,null,d,Object(U.createElement)(Q.Placeholder,{icon:"admin-post",label:Object(K.__)("Latest Posts")},Array.isArray(r)?Object(K.__)("No posts found."):Object(U.createElement)(Q.Spinner,null)));var p=r.length>m?r.slice(0,m):r,g=[{icon:"list-view",title:Object(K.__)("List View"),onClick:function(){return n({postLayout:"list"})},isActive:"list"===i},{icon:"grid-view",title:Object(K.__)("Grid View"),onClick:function(){return n({postLayout:"grid"})},isActive:"grid"===i}],O=Object(qn.__experimentalGetSettings)().formats.date;return Object(U.createElement)(U.Fragment,null,d,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.BlockAlignmentToolbar,{value:c,onChange:function(e){n({align:e})},controls:["center","wide","full"]}),Object(U.createElement)(Q.Toolbar,{controls:g})),Object(U.createElement)("ul",{className:q()(this.props.className,Object(F.a)({"is-grid":"grid"===i,"has-dates":a},"columns-".concat(l),"grid"===i))},p.map(function(e,t){return Object(U.createElement)("li",{key:t},Object(U.createElement)("a",{href:e.link,target:"_blank"},Object(Gn.decodeEntities)(e.title.rendered.trim())||Object(K.__)("(Untitled)")),a&&e.date_gmt&&Object(U.createElement)("time",{dateTime:Object(qn.format)("c",e.date_gmt),className:"wp-block-latest-posts__post-date"},Object(qn.dateI18n)(O,e.date_gmt)))})))}}]),t}(U.Component),Qn=Object(oe.withSelect)(function(e,t){var n=t.attributes,r=n.postsToShow,o=n.order,a=n.orderBy,c=n.categories;return{latestPosts:(0,e("core").getEntityRecords)("postType","post",Object(G.pickBy)({categories:c,order:o,orderby:a,per_page:r},function(e){return!Object(G.isUndefined)(e)}))}})($n),Yn="core/latest-posts",Zn={title:Object(K.__)("Latest Posts"),description:Object(K.__)("Display a list of your most recent posts."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Rect,{x:"11",y:"7",width:"6",height:"2"}),Object(U.createElement)(Q.Rect,{x:"11",y:"11",width:"6",height:"2"}),Object(U.createElement)(Q.Rect,{x:"11",y:"15",width:"6",height:"2"}),Object(U.createElement)(Q.Rect,{x:"7",y:"7",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"7",y:"11",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"7",y:"15",width:"2",height:"2"}),Object(U.createElement)(Q.Path,{d:"M20.1,3H3.9C3.4,3,3,3.4,3,3.9v16.2C3,20.5,3.4,21,3.9,21h16.2c0.4,0,0.9-0.5,0.9-0.9V3.9C21,3.4,20.5,3,20.1,3z M19,19H5V5h14V19z"})),category:"widgets",keywords:[Object(K.__)("recent posts")],supports:{html:!1},getEditWrapperProps:function(e){var t=e.align;if(["left","center","right","wide","full"].includes(t))return{"data-align":t}},edit:Qn,save:function(){return null}},Jn=Object(V.a)({},Object(D.getPhrasingContentSchema)(),{ul:{},ol:{attributes:["type"]}});["ul","ol"].forEach(function(e){Jn[e].children={li:{children:Jn}}});var Xn={className:!1},er={ordered:{type:"boolean",default:!1},values:{type:"string",source:"html",selector:"ol,ul",multiline:"li",default:""}},tr="core/list",nr={title:Object(K.__)("List"),description:Object(K.__)("Create a bulleted or numbered list."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M9 19h12v-2H9v2zm0-6h12v-2H9v2zm0-8v2h12V5H9zm-4-.5c-.828 0-1.5.672-1.5 1.5S4.172 7.5 5 7.5 6.5 6.828 6.5 6 5.828 4.5 5 4.5zm0 6c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5zm0 6c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5z"}))),category:"common",keywords:[Object(K.__)("bullet list"),Object(K.__)("ordered list"),Object(K.__)("numbered list")],attributes:er,supports:Xn,transforms:{from:[{type:"block",isMultiBlock:!0,blocks:["core/paragraph"],transform:function(e){return Object(D.createBlock)("core/list",{values:Object(lt.toHTMLString)({value:Object(lt.join)(e.map(function(e){var t=e.content;return Object(lt.replace)(Object(lt.create)({html:t}),/\n/g,lt.LINE_SEPARATOR)}),lt.LINE_SEPARATOR),multilineTag:"li"})})}},{type:"block",blocks:["core/quote"],transform:function(e){var t=e.value;return Object(D.createBlock)("core/list",{values:Object(lt.toHTMLString)({value:Object(lt.create)({html:t,multilineTag:"p"}),multilineTag:"li"})})}},{type:"raw",selector:"ol,ul",schema:{ol:Jn.ol,ul:Jn.ul},transform:function(e){return Object(D.createBlock)("core/list",Object(V.a)({},Object(D.getBlockAttributes)("core/list",e.outerHTML),{ordered:"OL"===e.nodeName}))}}].concat(Object(H.a)(["*","-"].map(function(e){return{type:"prefix",prefix:e,transform:function(e){return Object(D.createBlock)("core/list",{values:"<li>".concat(e,"</li>")})}}})),Object(H.a)(["1.","1)"].map(function(e){return{type:"prefix",prefix:e,transform:function(e){return Object(D.createBlock)("core/list",{ordered:!0,values:"<li>".concat(e,"</li>")})}}}))),to:[{type:"block",blocks:["core/paragraph"],transform:function(e){var t=e.values;return Object(lt.split)(Object(lt.create)({html:t,multilineTag:"li",multilineWrapperTags:["ul","ol"]}),lt.LINE_SEPARATOR).map(function(e){return Object(D.createBlock)("core/paragraph",{content:Object(lt.toHTMLString)({value:e})})})}},{type:"block",blocks:["core/quote"],transform:function(e){var t=e.values;return Object(D.createBlock)("core/quote",{value:Object(lt.toHTMLString)({value:Object(lt.create)({html:t,multilineTag:"li",multilineWrapperTags:["ul","ol"]}),multilineTag:"p"})})}}]},deprecated:[{supports:Xn,attributes:Object(V.a)({},Object(G.omit)(er,["ordered"]),{nodeName:{type:"string",source:"property",selector:"ol,ul",property:"nodeName",default:"UL"}}),migrate:function(e){var t=e.nodeName,n=Object(et.a)(e,["nodeName"]);return Object(V.a)({},n,{ordered:"OL"===t})},save:function(e){var t=e.attributes,n=t.nodeName,r=t.values;return Object(U.createElement)($.RichText.Content,{tagName:n.toLowerCase(),value:r})}}],merge:function(e,t){var n=t.values;return n&&"<li></li>"!==n?Object(V.a)({},e,{values:e.values+n}):e},edit:function(e){var t=e.attributes,n=e.insertBlocksAfter,r=e.setAttributes,o=e.mergeBlocks,a=e.onReplace,c=e.className,i=t.ordered,l=t.values;return Object(U.createElement)($.RichText,{identifier:"values",multiline:"li",tagName:i?"ol":"ul",onChange:function(e){return r({values:e})},value:l,wrapperClassName:"block-library-list",className:c,placeholder:Object(K.__)("Write list…"),onMerge:o,unstableOnSplit:n?function(e,t){for(var o=arguments.length,a=new Array(o>2?o-2:0),c=2;c<o;c++)a[c-2]=arguments[c];a.length||a.push(Object(D.createBlock)("core/paragraph")),"<li></li>"!==t&&a.push(Object(D.createBlock)("core/list",{ordered:i,values:t})),r({values:e}),n(a)}:void 0,onRemove:function(){return a([])},onTagNameChange:function(e){return r({ordered:"ol"===e})}})},save:function(e){var t=e.attributes,n=t.ordered,r=t.values,o=n?"ol":"ul";return Object(U.createElement)($.RichText.Content,{tagName:o,value:r,multiline:"li"})}};var rr=Object(oe.withDispatch)(function(e,t){var n=t.clientId,r=t.attributes,o=e("core/editor").replaceBlock;return{convertToHTML:function(){o(n,Object(D.createBlock)("core/html",{content:r.originalUndelimitedContent}))}}})(function(e){var t,n=e.attributes,r=e.convertToHTML,o=n.originalName,a=n.originalUndelimitedContent,c=!!a,i=Object(D.getBlockType)("core/html"),l=[];return c&&i?(t=Object(K.sprintf)(Object(K.__)('Your site doesn’t include support for the "%s" block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely.'),o),l.push(Object(U.createElement)(Q.Button,{key:"convert",onClick:r,isLarge:!0,isPrimary:!0},Object(K.__)("Keep as HTML")))):t=Object(K.sprintf)(Object(K.__)('Your site doesn’t include support for the "%s" block. You can leave this block intact or remove it entirely.'),o),Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.Warning,{actions:l},t),Object(U.createElement)(U.RawHTML,null,a))}),or="core/missing",ar={name:or,category:"common",title:Object(K.__)("Unrecognized Block"),description:Object(K.__)("Your site doesn’t include support for this block."),supports:{className:!1,customClassName:!1,inserter:!1,html:!1,reusable:!1},attributes:{originalName:{type:"string"},originalUndelimitedContent:{type:"string"},originalContent:{type:"string",source:"html"}},edit:rr,save:function(e){var t=e.attributes;return Object(U.createElement)(U.RawHTML,null,t.originalContent)}},cr=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).onChangeInput=e.onChangeInput.bind(Object(ne.a)(Object(ne.a)(e))),e.onKeyDown=e.onKeyDown.bind(Object(ne.a)(Object(ne.a)(e))),e.state={defaultText:Object(K.__)("Read more")},e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"onChangeInput",value:function(e){this.setState({defaultText:""});var t=0===e.target.value.length?void 0:e.target.value;this.props.setAttributes({customText:t})}},{key:"onKeyDown",value:function(e){var t=e.keyCode,n=this.props.insertBlocksAfter;t===mt.ENTER&&n([Object(D.createBlock)(Object(D.getDefaultBlockName)())])}},{key:"render",value:function(){var e=this.props.attributes,t=e.customText,n=e.noTeaser,r=this.props.setAttributes,o=this.state.defaultText,a=void 0!==t?t:o,c=a.length+1;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,null,Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)('Hide the teaser before the "More" tag'),checked:!!n,onChange:function(){return r({noTeaser:!n})}}))),Object(U.createElement)("div",{className:"wp-block-more"},Object(U.createElement)("input",{type:"text",value:a,size:c,onChange:this.onChangeInput,onKeyDown:this.onKeyDown})))}}]),t}(U.Component),ir="core/more",lr={title:Object(K._x)("More","block name"),description:Object(K.__)("Mark the excerpt of this content. Content before this block will be shown in the excerpt on your archives page."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M2 9v2h19V9H2zm0 6h5v-2H2v2zm7 0h5v-2H9v2zm7 0h5v-2h-5v2z"}))),category:"layout",supports:{customClassName:!1,className:!1,html:!1,multiple:!1},attributes:{customText:{type:"string"},noTeaser:{type:"boolean",default:!1}},transforms:{from:[{type:"raw",schema:{"wp-block":{attributes:["data-block"]}},isMatch:function(e){return e.dataset&&"core/more"===e.dataset.block},transform:function(e){var t=e.dataset,n=t.customText,r=t.noTeaser,o={};return n&&(o.customText=n),""===r&&(o.noTeaser=!0),Object(D.createBlock)("core/more",o)}}]},edit:cr,save:function(e){var t=e.attributes,n=t.customText,r=t.noTeaser,o=n?"\x3c!--more ".concat(n,"--\x3e"):"\x3c!--more--\x3e",a=r?"\x3c!--noteaser--\x3e":"";return Object(U.createElement)(U.RawHTML,null,Object(G.compact)([o,a]).join("\n"))}};var sr="core/nextpage",ur={title:Object(K.__)("Page Break"),description:Object(K.__)("Separate your content into a multi-page experience."),icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M9 12h6v-2H9zm-7 0h5v-2H2zm15 0h5v-2h-5zm3 2v2l-6 6H6a2 2 0 0 1-2-2v-6h2v6h6v-4a2 2 0 0 1 2-2h6zM4 8V4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4h-2V4H6v4z"}))),category:"layout",keywords:[Object(K.__)("next page"),Object(K.__)("pagination")],supports:{customClassName:!1,className:!1,html:!1},attributes:{},transforms:{from:[{type:"raw",schema:{"wp-block":{attributes:["data-block"]}},isMatch:function(e){return e.dataset&&"core/nextpage"===e.dataset.block},transform:function(){return Object(D.createBlock)("core/nextpage",{})}}]},edit:function(){return Object(U.createElement)("div",{className:"wp-block-nextpage"},Object(U.createElement)("span",null,Object(K.__)("Page break")))},save:function(){return Object(U.createElement)(U.RawHTML,null,"\x3c!--nextpage--\x3e")}},br="core/preformatted",mr={title:Object(K.__)("Preformatted"),description:Object(K.__)("Add text that respects your spacing and tabs, and also allows styling."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M20,18H4V6h16V18z"}),Object(U.createElement)(Q.Rect,{x:"6",y:"10",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"6",y:"14",width:"8",height:"2"}),Object(U.createElement)(Q.Rect,{x:"16",y:"14",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"10",y:"10",width:"8",height:"2"})),category:"formatting",attributes:{content:{type:"string",source:"html",selector:"pre",default:""}},transforms:{from:[{type:"block",blocks:["core/code","core/paragraph"],transform:function(e){var t=e.content;return Object(D.createBlock)("core/preformatted",{content:t})}},{type:"raw",isMatch:function(e){return"PRE"===e.nodeName&&!(1===e.children.length&&"CODE"===e.firstChild.nodeName)},schema:{pre:{children:Object(D.getPhrasingContentSchema)()}}}],to:[{type:"block",blocks:["core/paragraph"],transform:function(e){return Object(D.createBlock)("core/paragraph",e)}}]},edit:function(e){var t=e.attributes,n=e.mergeBlocks,r=e.setAttributes,o=e.className,a=t.content;return Object(U.createElement)($.RichText,{tagName:"pre",value:a,onChange:function(e){r({content:e})},placeholder:Object(K.__)("Write preformatted text…"),wrapperClassName:o,onMerge:n})},save:function(e){var t=e.attributes.content;return Object(U.createElement)($.RichText.Content,{tagName:"pre",value:t})},merge:function(e,t){return{content:e.content+t.content}}},dr="is-style-".concat("solid-color"),hr=function(e){function t(e){var n;return Object(Z.a)(this,t),(n=Object(X.a)(this,Object(ee.a)(t).call(this,e))).wasTextColorAutomaticallyComputed=!1,n.pullQuoteMainColorSetter=n.pullQuoteMainColorSetter.bind(Object(ne.a)(Object(ne.a)(n))),n.pullQuoteTextColorSetter=n.pullQuoteTextColorSetter.bind(Object(ne.a)(Object(ne.a)(n))),n}return Object(te.a)(t,e),Object(J.a)(t,[{key:"pullQuoteMainColorSetter",value:function(e){var t=this.props,n=t.colorUtils,r=t.textColor,o=t.setTextColor,a=t.setMainColor,c=t.className,i=Object(G.includes)(c,dr),l=!r.color||this.wasTextColorAutomaticallyComputed,s=i&&l&&e;a(e),s&&(this.wasTextColorAutomaticallyComputed=!0,o(n.getMostReadableColor(e)))}},{key:"pullQuoteTextColorSetter",value:function(e){(0,this.props.setTextColor)(e),this.wasTextColorAutomaticallyComputed=!1}},{key:"render",value:function(){var e=this.props,t=e.attributes,n=e.mainColor,r=e.textColor,o=e.setAttributes,a=e.isSelected,c=e.className,i=t.value,l=t.citation,s=Object(G.includes)(c,dr),u=s?{backgroundColor:n.color}:{borderColor:n.color},b={color:r.color},m=r.color?q()("has-text-color",Object(F.a)({},r.class,r.class)):void 0;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)("figure",{style:u,className:q()(c,Object(F.a)({},n.class,s&&n.class))},Object(U.createElement)("blockquote",{style:b,className:m},Object(U.createElement)($.RichText,{multiline:!0,value:i,onChange:function(e){return o({value:e})},placeholder:Object(K.__)("Write quote…"),wrapperClassName:"block-library-pullquote__content"}),(!$.RichText.isEmpty(l)||a)&&Object(U.createElement)($.RichText,{value:l,placeholder:Object(K.__)("Write citation…"),onChange:function(e){return o({citation:e})},className:"wp-block-pullquote__citation"}))),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)($.PanelColorSettings,{title:Object(K.__)("Color Settings"),colorSettings:[{value:n.color,onChange:this.pullQuoteMainColorSetter,label:Object(K.__)("Main Color")},{value:r.color,onChange:this.pullQuoteTextColorSetter,label:Object(K.__)("Text Color")}]},s&&Object(U.createElement)($.ContrastChecker,Object(Y.a)({textColor:r.color,backgroundColor:n.color},{isLargeText:!1})))))}}]),t}(U.Component),pr=Object($.withColors)({mainColor:"background-color",textColor:"color"})(hr),gr={value:{type:"string",source:"html",selector:"blockquote",multiline:"p"},citation:{type:"string",source:"html",selector:"cite",default:""},mainColor:{type:"string"},customMainColor:{type:"string"},textColor:{type:"string"},customTextColor:{type:"string"}},Or="core/pullquote",fr={title:Object(K.__)("Pullquote"),description:Object(K.__)("Give special visual emphasis to a quote from your text."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Polygon,{points:"21 18 2 18 2 20 21 20"}),Object(U.createElement)(Q.Path,{d:"m19 10v4h-15v-4h15m1-2h-17c-0.55 0-1 0.45-1 1v6c0 0.55 0.45 1 1 1h17c0.55 0 1-0.45 1-1v-6c0-0.55-0.45-1-1-1z"}),Object(U.createElement)(Q.Polygon,{points:"21 4 2 4 2 6 21 6"})),category:"formatting",attributes:gr,styles:[{name:"default",label:Object(K._x)("Regular","block style"),isDefault:!0},{name:"solid-color",label:Object(K.__)("Solid Color")}],supports:{align:["left","right","wide","full"]},edit:pr,save:function(e){var t,n,r=e.attributes,o=r.mainColor,a=r.customMainColor,c=r.textColor,i=r.customTextColor,l=r.value,s=r.citation,u=r.className;if(Object(G.includes)(u,dr))(t=Object($.getColorClassName)("background-color",o))||(n={backgroundColor:a});else if(a)n={borderColor:a};else if(o){var b=Object(G.get)(Object(oe.select)("core/editor").getEditorSettings(),["colors"],[]);n={borderColor:Object($.getColorObjectByAttributeValues)(b,o).color}}var m=Object($.getColorClassName)("color",c),d=c||i?q()("has-text-color",Object(F.a)({},m,m)):void 0,h=m?void 0:{color:i};return Object(U.createElement)("figure",{className:t,style:n},Object(U.createElement)("blockquote",{className:d,style:h},Object(U.createElement)($.RichText.Content,{value:l,multiline:!0}),!$.RichText.isEmpty(s)&&Object(U.createElement)($.RichText.Content,{tagName:"cite",value:s})))},deprecated:[{attributes:Object(V.a)({},gr),save:function(e){var t=e.attributes,n=t.value,r=t.citation;return Object(U.createElement)("blockquote",null,Object(U.createElement)($.RichText.Content,{value:n,multiline:!0}),!$.RichText.isEmpty(r)&&Object(U.createElement)($.RichText.Content,{tagName:"cite",value:r}))}},{attributes:Object(V.a)({},gr,{citation:{type:"string",source:"html",selector:"footer"},align:{type:"string",default:"none"}}),save:function(e){var t=e.attributes,n=t.value,r=t.citation,o=t.align;return Object(U.createElement)("blockquote",{className:"align".concat(o)},Object(U.createElement)($.RichText.Content,{value:n,multiline:!0}),!$.RichText.isEmpty(r)&&Object(U.createElement)($.RichText.Content,{tagName:"footer",value:r}))}}]},jr=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).titleField=Object(U.createRef)(),e.editButton=Object(U.createRef)(),e.handleFormSubmit=e.handleFormSubmit.bind(Object(ne.a)(Object(ne.a)(e))),e.handleTitleChange=e.handleTitleChange.bind(Object(ne.a)(Object(ne.a)(e))),e.handleTitleKeyDown=e.handleTitleKeyDown.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentDidMount",value:function(){this.props.isEditing&&this.titleField.current&&this.titleField.current.select()}},{key:"componentDidUpdate",value:function(e){!e.isEditing&&this.props.isEditing&&this.titleField.current.select(),!e.isEditing&&!e.isSaving||this.props.isEditing||this.props.isSaving||this.editButton.current.focus()}},{key:"handleFormSubmit",value:function(e){e.preventDefault(),this.props.onSave()}},{key:"handleTitleChange",value:function(e){this.props.onChangeTitle(e.target.value)}},{key:"handleTitleKeyDown",value:function(e){e.keyCode===mt.ESCAPE&&(e.stopPropagation(),this.props.onCancel())}},{key:"render",value:function(){var e=this.props,t=e.isEditing,n=e.title,r=e.isSaving,o=e.onEdit,a=e.instanceId;return Object(U.createElement)(U.Fragment,null,!t&&!r&&Object(U.createElement)("div",{className:"reusable-block-edit-panel"},Object(U.createElement)("b",{className:"reusable-block-edit-panel__info"},n),Object(U.createElement)(Q.Button,{ref:this.editButton,isLarge:!0,className:"reusable-block-edit-panel__button",onClick:o},Object(K.__)("Edit"))),(t||r)&&Object(U.createElement)("form",{className:"reusable-block-edit-panel",onSubmit:this.handleFormSubmit},Object(U.createElement)("label",{htmlFor:"reusable-block-edit-panel__title-".concat(a),className:"reusable-block-edit-panel__label"},Object(K.__)("Name:")),Object(U.createElement)("input",{ref:this.titleField,type:"text",disabled:r,className:"reusable-block-edit-panel__title",value:n,onChange:this.handleTitleChange,onKeyDown:this.handleTitleKeyDown,id:"reusable-block-edit-panel__title-".concat(a)}),Object(U.createElement)(Q.Button,{type:"submit",isLarge:!0,isBusy:r,disabled:!n||r,className:"reusable-block-edit-panel__button"},Object(K.__)("Save"))))}}]),t}(U.Component),vr=Object(re.withInstanceId)(jr);var yr=function(e){var t=e.title,n=Object(K.sprintf)(Object(K.__)("Reusable Block: %s"),t);return Object(U.createElement)(Q.Tooltip,{text:n},Object(U.createElement)("span",{className:"reusable-block-indicator"},Object(U.createElement)(Q.Dashicon,{icon:"controls-repeat"})))},kr=function(e){function t(e){var n,r=e.reusableBlock;return Object(Z.a)(this,t),(n=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).startEditing=n.startEditing.bind(Object(ne.a)(Object(ne.a)(n))),n.stopEditing=n.stopEditing.bind(Object(ne.a)(Object(ne.a)(n))),n.setAttributes=n.setAttributes.bind(Object(ne.a)(Object(ne.a)(n))),n.setTitle=n.setTitle.bind(Object(ne.a)(Object(ne.a)(n))),n.save=n.save.bind(Object(ne.a)(Object(ne.a)(n))),r&&r.isTemporary?n.state={isEditing:!0,title:r.title,changedAttributes:{}}:n.state={isEditing:!1,title:null,changedAttributes:null},n}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentDidMount",value:function(){this.props.reusableBlock||this.props.fetchReusableBlock()}},{key:"startEditing",value:function(){var e=this.props.reusableBlock;this.setState({isEditing:!0,title:e.title,changedAttributes:{}})}},{key:"stopEditing",value:function(){this.setState({isEditing:!1,title:null,changedAttributes:null})}},{key:"setAttributes",value:function(e){this.setState(function(t){if(null!==t.changedAttributes)return{changedAttributes:Object(V.a)({},t.changedAttributes,e)}})}},{key:"setTitle",value:function(e){this.setState({title:e})}},{key:"save",value:function(){var e=this.props,t=e.reusableBlock,n=e.onUpdateTitle,r=e.updateAttributes,o=e.block,a=e.onSave,c=this.state,i=c.title,l=c.changedAttributes;i!==t.title&&n(i),r(o.clientId,l),a(),this.stopEditing()}},{key:"render",value:function(){var e=this.props,t=e.isSelected,n=e.reusableBlock,r=e.block,o=e.isFetching,a=e.isSaving,c=this.state,i=c.isEditing,l=c.title,s=c.changedAttributes;if(!n&&o)return Object(U.createElement)(Q.Placeholder,null,Object(U.createElement)(Q.Spinner,null));if(!n||!r)return Object(U.createElement)(Q.Placeholder,null,Object(K.__)("Block has been deleted or is unavailable."));var u=Object(U.createElement)($.BlockEdit,Object(Y.a)({},this.props,{isSelected:i&&t,clientId:r.clientId,name:r.name,attributes:Object(V.a)({},r.attributes,s),setAttributes:i?this.setAttributes:G.noop}));return i||(u=Object(U.createElement)(Q.Disabled,null,u)),Object(U.createElement)(U.Fragment,null,(t||i)&&Object(U.createElement)(vr,{isEditing:i,title:null!==l?l:n.title,isSaving:a&&!n.isTemporary,onEdit:this.startEditing,onChangeTitle:this.setTitle,onSave:this.save,onCancel:this.stopEditing}),!t&&!i&&Object(U.createElement)(yr,{title:n.title}),u)}}]),t}(U.Component),wr=Object(re.compose)([Object(oe.withSelect)(function(e,t){var n=e("core/editor"),r=n.__experimentalGetReusableBlock,o=n.__experimentalIsFetchingReusableBlock,a=n.__experimentalIsSavingReusableBlock,c=n.getBlock,i=t.attributes.ref,l=r(i);return{reusableBlock:l,isFetching:o(i),isSaving:a(i),block:l?c(l.clientId):null}}),Object(oe.withDispatch)(function(e,t){var n=e("core/editor"),r=n.__experimentalFetchReusableBlocks,o=n.updateBlockAttributes,a=n.__experimentalUpdateReusableBlockTitle,c=n.__experimentalSaveReusableBlock,i=t.attributes.ref;return{fetchReusableBlock:Object(G.partial)(r,i),updateAttributes:o,onUpdateTitle:Object(G.partial)(a,i),onSave:Object(G.partial)(c,i)}})])(kr),Er="core/block",Cr={title:Object(K.__)("Reusable Block"),category:"reusable",description:Object(K.__)("Create content, and save it for you and other contributors to reuse across your site. Update the block, and the changes apply everywhere it’s used."),attributes:{ref:{type:"number"}},supports:{customClassName:!1,html:!1,inserter:!1},edit:wr,save:function(){return null}},_r="core/separator",xr={title:Object(K.__)("Separator"),description:Object(K.__)("Create a break between ideas or sections with a horizontal separator."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.Path,{d:"M19 13H5v-2h14v2z"})),category:"layout",keywords:[Object(K.__)("horizontal-line"),"hr",Object(K.__)("divider")],styles:[{name:"default",label:Object(K.__)("Short Line"),isDefault:!0},{name:"wide",label:Object(K.__)("Wide Line")},{name:"dots",label:Object(K.__)("Dots")}],transforms:{from:[{type:"enter",regExp:/^-{3,}$/,transform:function(){return Object(D.createBlock)("core/separator")}},{type:"raw",selector:"hr",schema:{hr:{}}}]},edit:function(e){var t=e.className;return Object(U.createElement)("hr",{className:t})},save:function(){return Object(U.createElement)("hr",null)}},Sr=n(58),Tr="core/shortcode",Nr={title:Object(K.__)("Shortcode"),description:Object(K.__)("Insert additional custom elements with a WordPress shortcode."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M8.5,21.4l1.9,0.5l5.2-19.3l-1.9-0.5L8.5,21.4z M3,19h4v-2H5V7h2V5H3V19z M17,5v2h2v10h-2v2h4V5H17z"})),category:"widgets",attributes:{text:{type:"string",source:"html"}},transforms:{from:[{type:"shortcode",tag:"[a-z][a-z0-9_-]*",attributes:{text:{type:"string",shortcode:function(e,t){var n=t.content;return Object(Sr.removep)(Object(Sr.autop)(n))}}},priority:20}]},supports:{customClassName:!1,className:!1,html:!1},edit:Object(re.withInstanceId)(function(e){var t=e.attributes,n=e.setAttributes,r=e.instanceId,o="blocks-shortcode-input-".concat(r);return Object(U.createElement)("div",{className:"wp-block-shortcode"},Object(U.createElement)("label",{htmlFor:o},Object(U.createElement)(Q.Dashicon,{icon:"shortcode"}),Object(K.__)("Shortcode")),Object(U.createElement)($.PlainText,{className:"input-control",id:o,value:t.text,placeholder:Object(K.__)("Write shortcode here…"),onChange:function(e){return n({text:e})}}))}),save:function(e){var t=e.attributes;return Object(U.createElement)(U.RawHTML,null,t.text)}},Rr="core/spacer",Br={title:Object(K.__)("Spacer"),description:Object(K.__)("Add white space between blocks and customize its height."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M13 4v2h3.59L6 16.59V13H4v7h7v-2H7.41L18 7.41V11h2V4h-7"}))),category:"layout",attributes:{height:{type:"number",default:100}},edit:Object(re.withInstanceId)(function(e){var t=e.attributes,n=e.isSelected,r=e.setAttributes,o=e.toggleSelection,a=e.instanceId,c=t.height,i="block-spacer-height-input-".concat(a);return Object(U.createElement)(U.Fragment,null,Object(U.createElement)(Q.ResizableBox,{className:q()("block-library-spacer__resize-container",{"is-selected":n}),size:{height:c},minHeight:"20",enable:{top:!1,right:!1,bottom:!0,left:!1,topRight:!1,bottomRight:!1,bottomLeft:!1,topLeft:!1},onResizeStop:function(e,t,n,a){r({height:parseInt(c+a.height,10)}),o(!0)},onResizeStart:function(){o(!1)}}),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Spacer Settings")},Object(U.createElement)(Q.BaseControl,{label:Object(K.__)("Height in pixels"),id:i},Object(U.createElement)("input",{type:"number",id:i,onChange:function(e){r({height:parseInt(e.target.value,10)})},value:c,min:"20",step:"10"})))))}),save:function(e){var t=e.attributes;return Object(U.createElement)("div",{style:{height:t.height},"aria-hidden":!0})}},Ar=n(65),Ir=n.n(Ar),Pr="core/subhead",Lr={title:Object(K.__)("Subheading (deprecated)"),description:Object(K.__)("This block is deprecated. Please use the Paragraph block instead."),icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.Path,{d:"M7.1 6l-.5 3h4.5L9.4 19h3l1.8-10h4.5l.5-3H7.1z"})),category:"common",supports:{inserter:!1,multiple:!1},attributes:{content:{type:"string",source:"html",selector:"p"},align:{type:"string"}},transforms:{to:[{type:"block",blocks:["core/paragraph"],transform:function(e){return Object(D.createBlock)("core/paragraph",e)}}]},edit:function(e){var t=e.attributes,n=e.setAttributes,r=e.className,o=t.align,a=t.content,c=t.placeholder;return Ir()("The Subheading block",{alternative:"the Paragraph block",plugin:"Gutenberg"}),Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.AlignmentToolbar,{value:o,onChange:function(e){n({align:e})}})),Object(U.createElement)($.RichText,{tagName:"p",value:a,onChange:function(e){n({content:e})},style:{textAlign:o},className:r,placeholder:c||Object(K.__)("Write subheading…")}))},save:function(e){var t=e.attributes,n=t.align,r=t.content;return Object(U.createElement)($.RichText.Content,{tagName:"p",style:{textAlign:n},value:r})}};var Mr=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).onCreateTable=e.onCreateTable.bind(Object(ne.a)(Object(ne.a)(e))),e.onChangeFixedLayout=e.onChangeFixedLayout.bind(Object(ne.a)(Object(ne.a)(e))),e.onChange=e.onChange.bind(Object(ne.a)(Object(ne.a)(e))),e.onChangeInitialColumnCount=e.onChangeInitialColumnCount.bind(Object(ne.a)(Object(ne.a)(e))),e.onChangeInitialRowCount=e.onChangeInitialRowCount.bind(Object(ne.a)(Object(ne.a)(e))),e.renderSection=e.renderSection.bind(Object(ne.a)(Object(ne.a)(e))),e.getTableControls=e.getTableControls.bind(Object(ne.a)(Object(ne.a)(e))),e.onInsertRow=e.onInsertRow.bind(Object(ne.a)(Object(ne.a)(e))),e.onInsertRowBefore=e.onInsertRowBefore.bind(Object(ne.a)(Object(ne.a)(e))),e.onInsertRowAfter=e.onInsertRowAfter.bind(Object(ne.a)(Object(ne.a)(e))),e.onDeleteRow=e.onDeleteRow.bind(Object(ne.a)(Object(ne.a)(e))),e.onInsertColumn=e.onInsertColumn.bind(Object(ne.a)(Object(ne.a)(e))),e.onInsertColumnBefore=e.onInsertColumnBefore.bind(Object(ne.a)(Object(ne.a)(e))),e.onInsertColumnAfter=e.onInsertColumnAfter.bind(Object(ne.a)(Object(ne.a)(e))),e.onDeleteColumn=e.onDeleteColumn.bind(Object(ne.a)(Object(ne.a)(e))),e.state={initialRowCount:2,initialColumnCount:2,selectedCell:null},e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"onChangeInitialColumnCount",value:function(e){this.setState({initialColumnCount:e})}},{key:"onChangeInitialRowCount",value:function(e){this.setState({initialRowCount:e})}},{key:"onCreateTable",value:function(e){e.preventDefault();var t,n,r,o=this.props.setAttributes,a=this.state,c=a.initialRowCount,i=a.initialColumnCount;c=parseInt(c,10)||2,i=parseInt(i,10)||2,o((n=(t={rowCount:c,columnCount:i}).rowCount,r=t.columnCount,{body:Object(G.times)(n,function(){return{cells:Object(G.times)(r,function(){return{content:"",tag:"td"}})}})}))}},{key:"onChangeFixedLayout",value:function(){var e=this.props,t=e.attributes;(0,e.setAttributes)({hasFixedLayout:!t.hasFixedLayout})}},{key:"onChange",value:function(e){var t=this.state.selectedCell;if(t){var n=this.props,r=n.attributes;(0,n.setAttributes)(function(e,t){var n=t.section,r=t.rowIndex,o=t.columnIndex,a=t.content;return Object(F.a)({},n,e[n].map(function(e,t){return t!==r?e:{cells:e.cells.map(function(e,t){return t!==o?e:Object(V.a)({},e,{content:a})})}}))}(r,{section:t.section,rowIndex:t.rowIndex,columnIndex:t.columnIndex,content:e}))}}},{key:"onInsertRow",value:function(e){var t=this.state.selectedCell;if(t){var n=this.props,r=n.attributes,o=n.setAttributes,a=t.section,c=t.rowIndex;this.setState({selectedCell:null}),o(function(e,t){var n=t.section,r=t.rowIndex,o=e[n][0].cells.length;return Object(F.a)({},n,Object(H.a)(e[n].slice(0,r)).concat([{cells:Object(G.times)(o,function(){return{content:"",tag:"td"}})}],Object(H.a)(e[n].slice(r))))}(r,{section:a,rowIndex:c+e}))}}},{key:"onInsertRowBefore",value:function(){this.onInsertRow(0)}},{key:"onInsertRowAfter",value:function(){this.onInsertRow(1)}},{key:"onDeleteRow",value:function(){var e=this.state.selectedCell;if(e){var t=this.props,n=t.attributes,r=t.setAttributes,o=e.section,a=e.rowIndex;this.setState({selectedCell:null}),r(function(e,t){var n=t.section,r=t.rowIndex;return Object(F.a)({},n,e[n].filter(function(e,t){return t!==r}))}(n,{section:o,rowIndex:a}))}}},{key:"onInsertColumn",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=this.state.selectedCell;if(t){var n=this.props,r=n.attributes,o=n.setAttributes,a=t.section,c=t.columnIndex;this.setState({selectedCell:null}),o(function(e,t){var n=t.section,r=t.columnIndex;return Object(F.a)({},n,e[n].map(function(e){return{cells:Object(H.a)(e.cells.slice(0,r)).concat([{content:"",tag:"td"}],Object(H.a)(e.cells.slice(r)))}}))}(r,{section:a,columnIndex:c+e}))}}},{key:"onInsertColumnBefore",value:function(){this.onInsertColumn(0)}},{key:"onInsertColumnAfter",value:function(){this.onInsertColumn(1)}},{key:"onDeleteColumn",value:function(){var e=this.state.selectedCell;if(e){var t=this.props,n=t.attributes,r=t.setAttributes,o=e.section,a=e.columnIndex;this.setState({selectedCell:null}),r(function(e,t){var n=t.section,r=t.columnIndex;return Object(F.a)({},n,e[n].map(function(e){return{cells:e.cells.filter(function(e,t){return t!==r})}}).filter(function(e){return e.cells.length}))}(n,{section:o,columnIndex:a}))}}},{key:"createOnFocus",value:function(e){var t=this;return function(){t.setState({selectedCell:e})}}},{key:"getTableControls",value:function(){var e=this.state.selectedCell;return[{icon:"table-row-before",title:Object(K.__)("Add Row Before"),isDisabled:!e,onClick:this.onInsertRowBefore},{icon:"table-row-after",title:Object(K.__)("Add Row After"),isDisabled:!e,onClick:this.onInsertRowAfter},{icon:"table-row-delete",title:Object(K.__)("Delete Row"),isDisabled:!e,onClick:this.onDeleteRow},{icon:"table-col-before",title:Object(K.__)("Add Column Before"),isDisabled:!e,onClick:this.onInsertColumnBefore},{icon:"table-col-after",title:Object(K.__)("Add Column After"),isDisabled:!e,onClick:this.onInsertColumnAfter},{icon:"table-col-delete",title:Object(K.__)("Delete Column"),isDisabled:!e,onClick:this.onDeleteColumn}]}},{key:"renderSection",value:function(e){var t=this,n=e.type,r=e.rows;if(!r.length)return null;var o="t".concat(n),a=this.state.selectedCell;return Object(U.createElement)(o,null,r.map(function(e,r){var o=e.cells;return Object(U.createElement)("tr",{key:r},o.map(function(e,o){var c=e.content,i=e.tag,l=a&&n===a.section&&r===a.rowIndex&&o===a.columnIndex,s={section:n,rowIndex:r,columnIndex:o},u=q()({"is-selected":l});return Object(U.createElement)(i,{key:o,className:u},Object(U.createElement)($.RichText,{className:"wp-block-table__cell-content",value:c,onChange:t.onChange,unstableOnFocus:t.createOnFocus(s)}))}))}))}},{key:"componentDidUpdate",value:function(){var e=this.props.isSelected,t=this.state.selectedCell;!e&&t&&this.setState({selectedCell:null})}},{key:"render",value:function(){var e=this.props,t=e.attributes,n=e.className,r=this.state,o=r.initialRowCount,a=r.initialColumnCount,c=t.hasFixedLayout,i=t.head,l=t.body,s=t.foot,u=!i.length&&!l.length&&!s.length,b=this.renderSection;if(u)return Object(U.createElement)("form",{onSubmit:this.onCreateTable},Object(U.createElement)(Q.TextControl,{type:"number",label:Object(K.__)("Column Count"),value:a,onChange:this.onChangeInitialColumnCount,min:"1"}),Object(U.createElement)(Q.TextControl,{type:"number",label:Object(K.__)("Row Count"),value:o,onChange:this.onChangeInitialRowCount,min:"1"}),Object(U.createElement)(Q.Button,{isPrimary:!0,type:"submit"},Object(K.__)("Create")));var m=q()(n,{"has-fixed-layout":c});return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)(Q.DropdownMenu,{icon:"editor-table",label:Object(K.__)("Edit Table"),controls:this.getTableControls()}))),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Table Settings"),className:"blocks-table-settings"},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Fixed width table cells"),checked:!!c,onChange:this.onChangeFixedLayout}))),Object(U.createElement)("table",{className:m},Object(U.createElement)(b,{type:"head",rows:i}),Object(U.createElement)(b,{type:"body",rows:l}),Object(U.createElement)(b,{type:"foot",rows:s})))}}]),t}(U.Component),zr={tr:{children:{th:{children:Object(D.getPhrasingContentSchema)()},td:{children:Object(D.getPhrasingContentSchema)()}}}},Hr={table:{children:{thead:{children:zr},tfoot:{children:zr},tbody:{children:zr}}}};function Dr(e){return{type:"array",default:[],source:"query",selector:"t".concat(e," tr"),query:{cells:{type:"array",default:[],source:"query",selector:"td,th",query:{content:{type:"string",source:"html"},tag:{type:"string",default:"td",source:"tag"}}}}}}var Fr="core/table",Vr={title:Object(K.__)("Table"),description:Object(K.__)("Insert a table — perfect for sharing charts and data."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M20 3H5L3 5v14l2 2h15l2-2V5l-2-2zm0 2v3H5V5h15zm-5 14h-5v-9h5v9zM5 10h3v9H5v-9zm12 9v-9h3v9h-3z"}))),category:"formatting",attributes:{hasFixedLayout:{type:"boolean",default:!1},head:Dr("head"),body:Dr("body"),foot:Dr("foot")},styles:[{name:"regular",label:Object(K._x)("Regular","block style"),isDefault:!0},{name:"stripes",label:Object(K.__)("Stripes")}],supports:{align:!0},transforms:{from:[{type:"raw",selector:"table",schema:Hr}]},edit:Mr,save:function(e){var t=e.attributes,n=t.hasFixedLayout,r=t.head,o=t.body,a=t.foot;if(!r.length&&!o.length&&!a.length)return null;var c=q()({"has-fixed-layout":n}),i=function(e){var t=e.type,n=e.rows;if(!n.length)return null;var r="t".concat(t);return Object(U.createElement)(r,null,n.map(function(e,t){var n=e.cells;return Object(U.createElement)("tr",{key:t},n.map(function(e,t){var n=e.content,r=e.tag;return Object(U.createElement)($.RichText.Content,{tagName:r,value:n,key:t})}))}))};return Object(U.createElement)("table",{className:c},Object(U.createElement)(i,{type:"head",rows:r}),Object(U.createElement)(i,{type:"body",rows:o}),Object(U.createElement)(i,{type:"foot",rows:a}))}},Ur="core/template",Wr={title:Object(K.__)("Reusable Template"),category:"reusable",description:Object(K.__)("Template block used as a container."),icon:Object(U.createElement)(Q.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(U.createElement)(Q.Rect,{x:"0",fill:"none",width:"24",height:"24"}),Object(U.createElement)(Q.G,null,Object(U.createElement)(Q.Path,{d:"M19 3H5c-1.105 0-2 .895-2 2v14c0 1.105.895 2 2 2h14c1.105 0 2-.895 2-2V5c0-1.105-.895-2-2-2zM6 6h5v5H6V6zm4.5 13C9.12 19 8 17.88 8 16.5S9.12 14 10.5 14s2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5zm3-6l3-5 3 5h-6z"}))),supports:{customClassName:!1,html:!1,inserter:!1},edit:function(){return Object(U.createElement)($.InnerBlocks,null)},save:function(){return Object(U.createElement)($.InnerBlocks.Content,null)}},qr="core/text-columns",Gr={supports:{inserter:!1},title:Object(K.__)("Text Columns (deprecated)"),description:Object(K.__)("This block is deprecated. Please use the Columns block instead."),icon:"columns",category:"layout",attributes:{content:{type:"array",source:"query",selector:"p",query:{children:{type:"string",source:"html"}},default:[{},{}]},columns:{type:"number",default:2},width:{type:"string"}},transforms:{to:[{type:"block",blocks:["core/columns"],transform:function(e){var t=e.className,n=e.columns,r=e.content,o=e.width;return Object(D.createBlock)("core/columns",{align:"wide"===o||"full"===o?o:void 0,className:t,columns:n},r.map(function(e){var t=e.children;return Object(D.createBlock)("core/column",{},[Object(D.createBlock)("core/paragraph",{content:t})])}))}}]},getEditWrapperProps:function(e){var t=e.width;if("wide"===t||"full"===t)return{"data-align":t}},edit:function(e){var t=e.attributes,n=e.setAttributes,r=e.className,o=t.width,a=t.content,c=t.columns;return Ir()("The Text Columns block",{alternative:"the Columns block",plugin:"Gutenberg"}),Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.BlockAlignmentToolbar,{value:o,onChange:function(e){return n({width:e})},controls:["center","wide","full"]})),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,null,Object(U.createElement)(Q.RangeControl,{label:Object(K.__)("Columns"),value:c,onChange:function(e){return n({columns:e})},min:2,max:4}))),Object(U.createElement)("div",{className:"".concat(r," align").concat(o," columns-").concat(c)},Object(G.times)(c,function(e){return Object(U.createElement)("div",{className:"wp-block-column",key:"column-".concat(e)},Object(U.createElement)($.RichText,{tagName:"p",value:Object(G.get)(a,[e,"children"]),onChange:function(t){n({content:Object(H.a)(a.slice(0,e)).concat([{children:t}],Object(H.a)(a.slice(e+1)))})},placeholder:Object(K.__)("New Column")}))})))},save:function(e){var t=e.attributes,n=t.width,r=t.content,o=t.columns;return Object(U.createElement)("div",{className:"align".concat(n," columns-").concat(o)},Object(G.times)(o,function(e){return Object(U.createElement)("div",{className:"wp-block-column",key:"column-".concat(e)},Object(U.createElement)($.RichText.Content,{tagName:"p",value:Object(G.get)(r,[e,"children"])}))}))}},Kr="core/verse",$r={title:Object(K.__)("Verse"),description:Object(K.__)("Insert poetry. Use special spacing formats. Or quote song lyrics."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.Path,{d:"M3 17v4h4l11-11-4-4L3 17zm3 2H5v-1l9-9 1 1-9 9zM21 6l-3-3h-1l-2 2 4 4 2-2V6z"})),category:"formatting",keywords:[Object(K.__)("poetry")],attributes:{content:{type:"string",source:"html",selector:"pre",default:""},textAlign:{type:"string"}},transforms:{from:[{type:"block",blocks:["core/paragraph"],transform:function(e){return Object(D.createBlock)("core/verse",e)}}],to:[{type:"block",blocks:["core/paragraph"],transform:function(e){return Object(D.createBlock)("core/paragraph",e)}}]},edit:function(e){var t=e.attributes,n=e.setAttributes,r=e.className,o=e.mergeBlocks,a=t.textAlign,c=t.content;return Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)($.AlignmentToolbar,{value:a,onChange:function(e){n({textAlign:e})}})),Object(U.createElement)($.RichText,{tagName:"pre",value:c,onChange:function(e){n({content:e})},style:{textAlign:a},placeholder:Object(K.__)("Write…"),wrapperClassName:r,onMerge:o}))},save:function(e){var t=e.attributes,n=t.textAlign,r=t.content;return Object(U.createElement)($.RichText.Content,{tagName:"pre",style:{textAlign:n},value:r})},merge:function(e,t){return{content:e.content+t.content}}},Qr=["video"],Yr=["image"],Zr=function(e){function t(){var e;return Object(Z.a)(this,t),(e=Object(X.a)(this,Object(ee.a)(t).apply(this,arguments))).state={editing:!e.props.attributes.src},e.videoPlayer=Object(U.createRef)(),e.posterImageButton=Object(U.createRef)(),e.toggleAttribute=e.toggleAttribute.bind(Object(ne.a)(Object(ne.a)(e))),e.onSelectURL=e.onSelectURL.bind(Object(ne.a)(Object(ne.a)(e))),e.onSelectPoster=e.onSelectPoster.bind(Object(ne.a)(Object(ne.a)(e))),e.onRemovePoster=e.onRemovePoster.bind(Object(ne.a)(Object(ne.a)(e))),e}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentDidMount",value:function(){var e=this,t=this.props,n=t.attributes,r=t.noticeOperations,o=t.setAttributes,a=n.id,c=n.src,i=void 0===c?"":c;if(!a&&Object(de.isBlobURL)(i)){var l=Object(de.getBlobByURL)(i);l&&Object($.mediaUpload)({filesList:[l],onFileChange:function(e){var t=Object(he.a)(e,1)[0].url;o({src:t})},onError:function(t){e.setState({editing:!0}),r.createErrorNotice(t)},allowedTypes:Qr})}}},{key:"componentDidUpdate",value:function(e){this.props.attributes.poster!==e.attributes.poster&&this.videoPlayer.current.load()}},{key:"toggleAttribute",value:function(e){var t=this;return function(n){t.props.setAttributes(Object(F.a)({},e,n))}}},{key:"onSelectURL",value:function(e){var t=this.props,n=t.attributes,r=t.setAttributes;if(e!==n.src){var o=He({attributes:{url:e}});if(void 0!==o)return void this.props.onReplace(o);r({src:e,id:void 0})}this.setState({editing:!1})}},{key:"onSelectPoster",value:function(e){(0,this.props.setAttributes)({poster:e.url})}},{key:"onRemovePoster",value:function(){(0,this.props.setAttributes)({poster:""}),this.posterImageButton.current.focus()}},{key:"render",value:function(){var e=this,t=this.props.attributes,n=t.autoplay,r=t.caption,o=t.controls,a=t.loop,c=t.muted,i=t.poster,l=t.preload,s=t.src,u=this.props,b=u.setAttributes,m=u.isSelected,d=u.className,h=u.noticeOperations,p=u.noticeUI,g=this.state.editing,O=function(){e.setState({editing:!0})};return g?Object(U.createElement)($.MediaPlaceholder,{icon:"media-video",className:d,onSelect:function(t){if(!t||!t.url)return b({src:void 0,id:void 0}),void O();b({src:t.url,id:t.id}),e.setState({src:t.url,editing:!1})},onSelectURL:this.onSelectURL,accept:"video/*",allowedTypes:Qr,value:this.props.attributes,notices:p,onError:h.createErrorNotice}):Object(U.createElement)(U.Fragment,null,Object(U.createElement)($.BlockControls,null,Object(U.createElement)(Q.Toolbar,null,Object(U.createElement)(Q.IconButton,{className:"components-icon-button components-toolbar__control",label:Object(K.__)("Edit video"),onClick:O,icon:"edit"}))),Object(U.createElement)($.InspectorControls,null,Object(U.createElement)(Q.PanelBody,{title:Object(K.__)("Video Settings")},Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Autoplay"),onChange:this.toggleAttribute("autoplay"),checked:n}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Loop"),onChange:this.toggleAttribute("loop"),checked:a}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Muted"),onChange:this.toggleAttribute("muted"),checked:c}),Object(U.createElement)(Q.ToggleControl,{label:Object(K.__)("Playback Controls"),onChange:this.toggleAttribute("controls"),checked:o}),Object(U.createElement)(Q.SelectControl,{label:Object(K.__)("Preload"),value:l,onChange:function(e){return b({preload:e})},options:[{value:"auto",label:Object(K.__)("Auto")},{value:"metadata",label:Object(K.__)("Metadata")},{value:"none",label:Object(K.__)("None")}]}),Object(U.createElement)($.MediaUploadCheck,null,Object(U.createElement)(Q.BaseControl,{className:"editor-video-poster-control",label:Object(K.__)("Poster Image")},Object(U.createElement)($.MediaUpload,{title:Object(K.__)("Select Poster Image"),onSelect:this.onSelectPoster,allowedTypes:Yr,render:function(t){var n=t.open;return Object(U.createElement)(Q.Button,{isDefault:!0,onClick:n,ref:e.posterImageButton},e.props.attributes.poster?Object(K.__)("Replace image"):Object(K.__)("Select Poster Image"))}}),!!this.props.attributes.poster&&Object(U.createElement)(Q.Button,{onClick:this.onRemovePoster,isLink:!0,isDestructive:!0},Object(K.__)("Remove Poster Image")))))),Object(U.createElement)("figure",{className:d},Object(U.createElement)(Q.Disabled,null,Object(U.createElement)("video",{controls:o,poster:i,src:s,ref:this.videoPlayer})),(!$.RichText.isEmpty(r)||m)&&Object(U.createElement)($.RichText,{tagName:"figcaption",placeholder:Object(K.__)("Write caption…"),value:r,onChange:function(e){return b({caption:e})},inlineToolbar:!0})))}}]),t}(U.Component),Jr=Object(Q.withNotices)(Zr),Xr="core/video",eo={title:Object(K.__)("Video"),description:Object(K.__)("Embed a video from your media library or upload a new one."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{fill:"none",d:"M0 0h24v24H0V0z"}),Object(U.createElement)(Q.Path,{d:"M4 6l2 4h14v8H4V6m18-2h-4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4L2 6v12l2 2h16l2-2V4z"})),keywords:[Object(K.__)("movie")],category:"common",attributes:{autoplay:{type:"boolean",source:"attribute",selector:"video",attribute:"autoplay"},caption:{type:"string",source:"html",selector:"figcaption"},controls:{type:"boolean",source:"attribute",selector:"video",attribute:"controls",default:!0},id:{type:"number"},loop:{type:"boolean",source:"attribute",selector:"video",attribute:"loop"},muted:{type:"boolean",source:"attribute",selector:"video",attribute:"muted"},poster:{type:"string",source:"attribute",selector:"video",attribute:"poster"},preload:{type:"string",source:"attribute",selector:"video",attribute:"preload",default:"metadata"},src:{type:"string",source:"attribute",selector:"video",attribute:"src"}},transforms:{from:[{type:"files",isMatch:function(e){return 1===e.length&&0===e[0].type.indexOf("video/")},transform:function(e){var t=e[0];return Object(D.createBlock)("core/video",{src:Object(de.createBlobURL)(t)})}}]},supports:{align:!0},edit:Jr,save:function(e){var t=e.attributes,n=t.autoplay,r=t.caption,o=t.controls,a=t.loop,c=t.muted,i=t.poster,l=t.preload,s=t.src;return Object(U.createElement)("figure",null,s&&Object(U.createElement)("video",{autoPlay:n,controls:o,loop:a,muted:c,poster:i,preload:"metadata"!==l?l:void 0,src:s}),!$.RichText.isEmpty(r)&&Object(U.createElement)($.RichText.Content,{tagName:"figcaption",value:r}))}},to=window.wp;var no=function(e){function t(e){var n;return Object(Z.a)(this,t),(n=Object(X.a)(this,Object(ee.a)(t).call(this,e))).initialize=n.initialize.bind(Object(ne.a)(Object(ne.a)(n))),n.onSetup=n.onSetup.bind(Object(ne.a)(Object(ne.a)(n))),n.focus=n.focus.bind(Object(ne.a)(Object(ne.a)(n))),n}return Object(te.a)(t,e),Object(J.a)(t,[{key:"componentDidMount",value:function(){var e=window.wpEditorL10n.tinymce,t=e.baseURL,n=e.suffix;window.tinymce.EditorManager.overrideDefaults({base_url:t,suffix:n}),"complete"===document.readyState?this.initialize():window.addEventListener("DOMContentLoaded",this.initialize)}},{key:"componentWillUnmount",value:function(){window.addEventListener("DOMContentLoaded",this.initialize),to.oldEditor.remove("editor-".concat(this.props.clientId))}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.clientId,r=t.attributes.content,o=window.tinymce.get("editor-".concat(n));e.attributes.content!==r&&o.setContent(r||"")}},{key:"initialize",value:function(){var e=this.props.clientId,t=window.wpEditorL10n.tinymce.settings;to.oldEditor.initialize("editor-".concat(e),{tinymce:Object(V.a)({},t,{inline:!0,content_css:!1,fixed_toolbar_container:"#toolbar-".concat(e),setup:this.onSetup})})}},{key:"onSetup",value:function(e){var t,n=this,r=this.props,o=r.attributes.content,a=r.setAttributes,c=this.ref;this.editor=e,o&&e.on("loadContent",function(){return e.setContent(o)}),e.on("blur",function(){return t=e.selection.getBookmark(2,!0),a({content:e.getContent()}),e.once("focus",function(){t&&e.selection.moveToBookmark(t)}),!1}),e.on("mousedown touchstart",function(){t=null}),e.on("keydown",function(t){t.keyCode!==mt.BACKSPACE&&t.keyCode!==mt.DELETE||!function(e){var t=e.getBody();return!(t.childNodes.length>1)&&(0===t.childNodes.length||!(t.childNodes[0].childNodes.length>1)&&/^\n?$/.test(t.innerText||t.textContent))}(e)||(n.props.onReplace([]),t.preventDefault(),t.stopImmediatePropagation()),t.altKey&&t.keyCode===mt.F10&&t.stopPropagation()}),e.addButton("kitchensink",{tooltip:Object(K._x)("More","button to expand options"),icon:"dashicon dashicons-editor-kitchensink",onClick:function(){var t=!this.active();this.active(t),e.dom.toggleClass(c,"has-advanced-toolbar",t)}}),e.on("init",function(){e.settings.toolbar1&&-1===e.settings.toolbar1.indexOf("kitchensink")&&e.dom.addClass(c,"has-advanced-toolbar")}),e.addButton("wp_add_media",{tooltip:Object(K.__)("Insert Media"),icon:"dashicon dashicons-admin-media",cmd:"WP_Medialib"}),e.on("init",function(){var e=n.editor.getBody();document.activeElement===e&&(e.blur(),n.editor.focus())})}},{key:"focus",value:function(){this.editor&&this.editor.focus()}},{key:"onToolbarKeyDown",value:function(e){e.stopPropagation(),e.nativeEvent.stopImmediatePropagation()}},{key:"render",value:function(){var e=this,t=this.props.clientId;return[Object(U.createElement)("div",{key:"toolbar",id:"toolbar-".concat(t),ref:function(t){return e.ref=t},className:"block-library-classic__toolbar",onClick:this.focus,"data-placeholder":Object(K.__)("Classic"),onKeyDown:this.onToolbarKeyDown}),Object(U.createElement)("div",{key:"editor",id:"editor-".concat(t),className:"wp-block-freeform block-library-rich-text__tinymce"})]}}]),t}(U.Component),ro="core/freeform",oo={title:Object(K._x)("Classic","block title"),description:Object(K.__)("Use the classic WordPress editor."),icon:Object(U.createElement)(Q.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Object(U.createElement)(Q.Path,{d:"M0,0h24v24H0V0z M0,0h24v24H0V0z",fill:"none"}),Object(U.createElement)(Q.Path,{d:"m20 7v10h-16v-10h16m0-2h-16c-1.1 0-1.99 0.9-1.99 2l-0.01 10c0 1.1 0.9 2 2 2h16c1.1 0 2-0.9 2-2v-10c0-1.1-0.9-2-2-2z"}),Object(U.createElement)(Q.Rect,{x:"11",y:"8",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"11",y:"11",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"8",y:"8",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"8",y:"11",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"5",y:"11",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"5",y:"8",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"8",y:"14",width:"8",height:"2"}),Object(U.createElement)(Q.Rect,{x:"14",y:"11",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"14",y:"8",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"17",y:"11",width:"2",height:"2"}),Object(U.createElement)(Q.Rect,{x:"17",y:"8",width:"2",height:"2"})),category:"formatting",attributes:{content:{type:"string",source:"html"}},supports:{className:!1,customClassName:!1,reusable:!1},edit:no,save:function(e){var t=e.attributes.content;return Object(U.createElement)(U.RawHTML,null,t)}};n.d(t,"registerCoreBlocks",function(){return ao});var ao=function(){[r,o,a,i,k,c,N,l,s,u,b,m,d,h,p,g].concat(Object(H.a)(yn),Object(H.a)(kn),[O,window.wp&&window.wp.oldEditor?z:null,f,j,v,y,w,E,C,_,x,T,S,R,B,A,I,P,L,M]).forEach(function(e){if(e){var t=e.name,n=e.settings;Object(D.registerBlockType)(t,n)}}),Object(D.setDefaultBlockName)(be),window.wp&&window.wp.oldEditor&&Object(D.setFreeformContentHandlerName)(ro),Object(D.setUnregisteredTypeHandlerName)(or)}},32:function(e,t){!function(){e.exports=this.wp.blob}()},33:function(e,t,n){"use strict";function r(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}n.d(t,"a",function(){return r})},35:function(e,t,n){"use strict";function r(e){if(Array.isArray(e))return e}n.d(t,"a",function(){return r})},36:function(e,t,n){"use strict";function r(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}n.d(t,"a",function(){return r})},37:function(e,t){!function(){e.exports=this.wp.viewport}()},4:function(e,t){!function(){e.exports=this.wp.components}()},41:function(e,t,n){e.exports=function(e,t){var n,r,o,a=0;function c(){var t,c,i=r,l=arguments.length;e:for(;i;){if(i.args.length===arguments.length){for(c=0;c<l;c++)if(i.args[c]!==arguments[c]){i=i.next;continue e}return i!==r&&(i===o&&(o=i.prev),i.prev.next=i.next,i.next&&(i.next.prev=i.prev),i.next=r,i.prev=null,r.prev=i,r=i),i.val}i=i.next}for(t=new Array(l),c=0;c<l;c++)t[c]=arguments[c];return i={args:t,val:e.apply(null,t)},r?(r.prev=i,i.next=r):o=i,a===n?(o=o.prev).next=null:a++,r=i,i.val}return t&&t.maxSize&&(n=t.maxSize),c.clear=function(){r=null,o=null,a=0},c}},46:function(e,t){!function(){e.exports=this.wp.date}()},49:function(e,t){!function(){e.exports=this.wp.htmlEntities}()},5:function(e,t){!function(){e.exports=this.wp.data}()},51:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},58:function(e,t){!function(){e.exports=this.wp.autop}()},6:function(e,t){!function(){e.exports=this.wp.editor}()},60:function(e,t,n){var r;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
!function(){"use strict";var n=function(){function e(){}function t(e,t){for(var n=t.length,r=0;r<n;++r)o(e,t[r])}e.prototype=Object.create(null);var n={}.hasOwnProperty;var r=/\s+/;function o(e,o){if(o){var a=typeof o;"string"===a?function(e,t){for(var n=t.split(r),o=n.length,a=0;a<o;++a)e[n[a]]=!0}(e,o):Array.isArray(o)?t(e,o):"object"===a?function(e,t){for(var r in t)n.call(t,r)&&(e[r]=!!t[r])}(e,o):"number"===a&&function(e,t){e[t]=!0}(e,o)}}return function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];var a=new e;t(a,r);var c=[];for(var i in a)a[i]&&c.push(i);return c.join(" ")}}();e.exports?(n.default=n,e.exports=n):void 0===(r=function(){return n}.apply(t,[]))||(e.exports=r)}()},65:function(e,t){!function(){e.exports=this.wp.deprecated}()},7:function(e,t){!function(){e.exports=this.wp.compose}()},75:function(e,t,n){"use strict";var r=n(100),o=n(102);function a(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=j,t.resolve=function(e,t){return j(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?j(e,!1,!0).resolveObject(t):t},t.format=function(e){o.isString(e)&&(e=j(e));return e instanceof a?e.format():a.prototype.format.call(e)},t.Url=a;var c=/^([a-z0-9.+-]+:)/i,i=/:[0-9]*$/,l=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,s=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),u=["'"].concat(s),b=["%","/","?",";","#"].concat(u),m=["/","?","#"],d=/^[+a-z0-9A-Z_-]{0,63}$/,h=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,p={javascript:!0,"javascript:":!0},g={javascript:!0,"javascript:":!0},O={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},f=n(103);function j(e,t,n){if(e&&o.isObject(e)&&e instanceof a)return e;var r=new a;return r.parse(e,t,n),r}a.prototype.parse=function(e,t,n){if(!o.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e.indexOf("?"),i=-1!==a&&a<e.indexOf("#")?"?":"#",s=e.split(i);s[0]=s[0].replace(/\\/g,"/");var j=e=s.join(i);if(j=j.trim(),!n&&1===e.split("#").length){var v=l.exec(j);if(v)return this.path=j,this.href=j,this.pathname=v[1],v[2]?(this.search=v[2],this.query=t?f.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var y=c.exec(j);if(y){var k=(y=y[0]).toLowerCase();this.protocol=k,j=j.substr(y.length)}if(n||y||j.match(/^\/\/[^@\/]+@[^@\/]+/)){var w="//"===j.substr(0,2);!w||y&&g[y]||(j=j.substr(2),this.slashes=!0)}if(!g[y]&&(w||y&&!O[y])){for(var E,C,_=-1,x=0;x<m.length;x++){-1!==(S=j.indexOf(m[x]))&&(-1===_||S<_)&&(_=S)}-1!==(C=-1===_?j.lastIndexOf("@"):j.lastIndexOf("@",_))&&(E=j.slice(0,C),j=j.slice(C+1),this.auth=decodeURIComponent(E)),_=-1;for(x=0;x<b.length;x++){var S;-1!==(S=j.indexOf(b[x]))&&(-1===_||S<_)&&(_=S)}-1===_&&(_=j.length),this.host=j.slice(0,_),j=j.slice(_),this.parseHost(),this.hostname=this.hostname||"";var T="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!T)for(var N=this.hostname.split(/\./),R=(x=0,N.length);x<R;x++){var B=N[x];if(B&&!B.match(d)){for(var A="",I=0,P=B.length;I<P;I++)B.charCodeAt(I)>127?A+="x":A+=B[I];if(!A.match(d)){var L=N.slice(0,x),M=N.slice(x+1),z=B.match(h);z&&(L.push(z[1]),M.unshift(z[2])),M.length&&(j="/"+M.join(".")+j),this.hostname=L.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),T||(this.hostname=r.toASCII(this.hostname));var H=this.port?":"+this.port:"",D=this.hostname||"";this.host=D+H,this.href+=this.host,T&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==j[0]&&(j="/"+j))}if(!p[k])for(x=0,R=u.length;x<R;x++){var F=u[x];if(-1!==j.indexOf(F)){var V=encodeURIComponent(F);V===F&&(V=escape(F)),j=j.split(F).join(V)}}var U=j.indexOf("#");-1!==U&&(this.hash=j.substr(U),j=j.slice(0,U));var W=j.indexOf("?");if(-1!==W?(this.search=j.substr(W),this.query=j.substr(W+1),t&&(this.query=f.parse(this.query)),j=j.slice(0,W)):t&&(this.search="",this.query={}),j&&(this.pathname=j),O[k]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){H=this.pathname||"";var q=this.search||"";this.path=H+q}return this.href=this.format(),this},a.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",n=this.pathname||"",r=this.hash||"",a=!1,c="";this.host?a=e+this.host:this.hostname&&(a=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(a+=":"+this.port)),this.query&&o.isObject(this.query)&&Object.keys(this.query).length&&(c=f.stringify(this.query));var i=this.search||c&&"?"+c||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||O[t])&&!1!==a?(a="//"+(a||""),n&&"/"!==n.charAt(0)&&(n="/"+n)):a||(a=""),r&&"#"!==r.charAt(0)&&(r="#"+r),i&&"?"!==i.charAt(0)&&(i="?"+i),t+a+(n=n.replace(/[?#]/g,function(e){return encodeURIComponent(e)}))+(i=i.replace("#","%23"))+r},a.prototype.resolve=function(e){return this.resolveObject(j(e,!1,!0)).format()},a.prototype.resolveObject=function(e){if(o.isString(e)){var t=new a;t.parse(e,!1,!0),e=t}for(var n=new a,r=Object.keys(this),c=0;c<r.length;c++){var i=r[c];n[i]=this[i]}if(n.hash=e.hash,""===e.href)return n.href=n.format(),n;if(e.slashes&&!e.protocol){for(var l=Object.keys(e),s=0;s<l.length;s++){var u=l[s];"protocol"!==u&&(n[u]=e[u])}return O[n.protocol]&&n.hostname&&!n.pathname&&(n.path=n.pathname="/"),n.href=n.format(),n}if(e.protocol&&e.protocol!==n.protocol){if(!O[e.protocol]){for(var b=Object.keys(e),m=0;m<b.length;m++){var d=b[m];n[d]=e[d]}return n.href=n.format(),n}if(n.protocol=e.protocol,e.host||g[e.protocol])n.pathname=e.pathname;else{for(var h=(e.pathname||"").split("/");h.length&&!(e.host=h.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==h[0]&&h.unshift(""),h.length<2&&h.unshift(""),n.pathname=h.join("/")}if(n.search=e.search,n.query=e.query,n.host=e.host||"",n.auth=e.auth,n.hostname=e.hostname||e.host,n.port=e.port,n.pathname||n.search){var p=n.pathname||"",f=n.search||"";n.path=p+f}return n.slashes=n.slashes||e.slashes,n.href=n.format(),n}var j=n.pathname&&"/"===n.pathname.charAt(0),v=e.host||e.pathname&&"/"===e.pathname.charAt(0),y=v||j||n.host&&e.pathname,k=y,w=n.pathname&&n.pathname.split("/")||[],E=(h=e.pathname&&e.pathname.split("/")||[],n.protocol&&!O[n.protocol]);if(E&&(n.hostname="",n.port=null,n.host&&(""===w[0]?w[0]=n.host:w.unshift(n.host)),n.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===h[0]?h[0]=e.host:h.unshift(e.host)),e.host=null),y=y&&(""===h[0]||""===w[0])),v)n.host=e.host||""===e.host?e.host:n.host,n.hostname=e.hostname||""===e.hostname?e.hostname:n.hostname,n.search=e.search,n.query=e.query,w=h;else if(h.length)w||(w=[]),w.pop(),w=w.concat(h),n.search=e.search,n.query=e.query;else if(!o.isNullOrUndefined(e.search)){if(E)n.hostname=n.host=w.shift(),(T=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=T.shift(),n.host=n.hostname=T.shift());return n.search=e.search,n.query=e.query,o.isNull(n.pathname)&&o.isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.href=n.format(),n}if(!w.length)return n.pathname=null,n.search?n.path="/"+n.search:n.path=null,n.href=n.format(),n;for(var C=w.slice(-1)[0],_=(n.host||e.host||w.length>1)&&("."===C||".."===C)||""===C,x=0,S=w.length;S>=0;S--)"."===(C=w[S])?w.splice(S,1):".."===C?(w.splice(S,1),x++):x&&(w.splice(S,1),x--);if(!y&&!k)for(;x--;x)w.unshift("..");!y||""===w[0]||w[0]&&"/"===w[0].charAt(0)||w.unshift(""),_&&"/"!==w.join("/").substr(-1)&&w.push("");var T,N=""===w[0]||w[0]&&"/"===w[0].charAt(0);E&&(n.hostname=n.host=N?"":w.length?w.shift():"",(T=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=T.shift(),n.host=n.hostname=T.shift()));return(y=y||n.host&&w.length)&&!N&&w.unshift(""),w.length?n.pathname=w.join("/"):(n.pathname=null,n.path=null),o.isNull(n.pathname)&&o.isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.auth=e.auth||n.auth,n.slashes=n.slashes||e.slashes,n.href=n.format(),n},a.prototype.parseHost=function(){var e=this.host,t=i.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},79:function(e,t){!function(){e.exports=this.wp.coreData}()},8:function(e,t,n){"use strict";n.d(t,"a",function(){return o});var r=n(15);function o(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},o=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),o.forEach(function(t){Object(r.a)(e,t,n[t])})}return e}},9:function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}n.d(t,"a",function(){return o})}});