this.wp=this.wp||{},this.wp.editor=function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=303)}([function(e,t){!function(){e.exports=this.wp.element}()},function(e,t){!function(){e.exports=this.wp.i18n}()},function(e,t){!function(){e.exports=this.lodash}()},function(e,t,n){"use strict";function r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n.d(t,"a",function(){return r})},function(e,t){!function(){e.exports=this.wp.components}()},function(e,t){!function(){e.exports=this.wp.data}()},,function(e,t){!function(){e.exports=this.wp.compose}()},function(e,t,n){"use strict";n.d(t,"a",function(){return o});var r=n(15);function o(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},o=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),o.forEach(function(t){Object(r.a)(e,t,n[t])})}return e}},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}n.d(t,"a",function(){return o})},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,"a",function(){return r})},function(e,t){!function(){e.exports=this.wp.blocks}()},function(e,t,n){"use strict";n.d(t,"a",function(){return i});var r=n(28),o=n(3);function i(e,t){return!t||"object"!==Object(r.a)(t)&&"function"!=typeof t?Object(o.a)(e):t}},function(e,t,n){"use strict";function r(e){return(r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";function r(e,t){return(r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&r(e,t)}n.d(t,"a",function(){return o})},function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",function(){return r})},function(e,t){!function(){e.exports=this.wp.keycodes}()},function(e,t,n){var r;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)&&r.length){var c=o.apply(null,r);c&&e.push(c)}else if("object"===i)for(var a in r)n.call(r,a)&&r[a]&&e.push(a)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()},function(e,t,n){"use strict";function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";var r=n(33);function o(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||Object(r.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}n.d(t,"a",function(){return o})},function(e,t){!function(){e.exports=this.wp.richText}()},function(e,t,n){"use strict";function r(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}n.d(t,"a",function(){return r})},function(e,t){!function(){e.exports=this.wp.dom}()},function(e,t){!function(){e.exports=this.wp.hooks}()},function(e,t){!function(){e.exports=this.wp.url}()},function(e,t,n){"use strict";var r=n(35);var o=n(36);function i(e,t){return Object(r.a)(e)||function(e,t){var n=[],r=!0,o=!1,i=void 0;try{for(var c,a=e[Symbol.iterator]();!(r=(c=a.next()).done)&&(n.push(c.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==a.return||a.return()}finally{if(o)throw i}}return n}(e,t)||Object(o.a)()}n.d(t,"a",function(){return i})},function(e,t){!function(){e.exports=this.React}()},,function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e){return(o="function"==typeof Symbol&&"symbol"===r(Symbol.iterator)?function(e){return r(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":r(e)})(e)}n.d(t,"a",function(){return o})},function(e,t,n){e.exports=n(109)()},function(e,t){!function(){e.exports=this.wp.apiFetch}()},function(e,t,n){"use strict";var r,o;function i(e){return[e]}function c(){var e={clear:function(){e.head=null}};return e}function a(e,t,n){var r;if(e.length!==t.length)return!1;for(r=n;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}r={},o="undefined"!=typeof WeakMap,t.a=function(e,t){var n,s;function l(){n=o?new WeakMap:c()}function u(){var n,r,o,i,c,l=arguments.length;for(i=new Array(l),o=0;o<l;o++)i[o]=arguments[o];for(c=t.apply(null,i),(n=s(c)).isUniqueByDependants||(n.lastDependants&&!a(c,n.lastDependants,0)&&n.clear(),n.lastDependants=c),r=n.head;r;){if(a(r.args,i,1))return r!==n.head&&(r.prev.next=r.next,r.next&&(r.next.prev=r.prev),r.next=n.head,r.prev=null,n.head.prev=r,n.head=r),r.val;r=r.next}return r={val:e.apply(null,i)},i[0]=null,r.args=i,n.head&&(n.head.prev=r,r.next=n.head),n.head=r,r.val}return t||(t=i),s=o?function(e){var t,o,i,a,s,l=n,u=!0;for(t=0;t<e.length;t++){if(o=e[t],!(s=o)||"object"!=typeof s){u=!1;break}l.has(o)?l=l.get(o):(i=new WeakMap,l.set(o,i),l=i)}return l.has(r)||((a=c()).isUniqueByDependants=u,l.set(r,a)),l.get(r)}:function(){return n},u.getDependants=t,u.clear=l,l(),u}},function(e,t){!function(){e.exports=this.wp.blob}()},function(e,t,n){"use strict";function r(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}n.d(t,"a",function(){return r})},,function(e,t,n){"use strict";function r(e){if(Array.isArray(e))return e}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";function r(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}n.d(t,"a",function(){return r})},function(e,t){!function(){e.exports=this.wp.viewport}()},function(e,t,n){"use strict";function r(e,t,n,r,o,i,c){try{var a=e[i](c),s=a.value}catch(e){return void n(e)}a.done?t(s):Promise.resolve(s).then(r,o)}function o(e){return function(){var t=this,n=arguments;return new Promise(function(o,i){var c=e.apply(t,n);function a(e){r(c,o,i,a,s,"next",e)}function s(e){r(c,o,i,a,s,"throw",e)}a(void 0)})}}n.d(t,"a",function(){return o})},,function(e,t){!function(){e.exports=this.wp.isShallowEqual}()},function(e,t,n){e.exports=function(e,t){var n,r,o,i=0;function c(){var t,c,a=r,s=arguments.length;e:for(;a;){if(a.args.length===arguments.length){for(c=0;c<s;c++)if(a.args[c]!==arguments[c]){a=a.next;continue e}return a!==r&&(a===o&&(o=a.prev),a.prev.next=a.next,a.next&&(a.next.prev=a.prev),a.next=r,a.prev=null,r.prev=a,r=a),a.val}a=a.next}for(t=new Array(s),c=0;c<s;c++)t[c]=arguments[c];return a={args:t,val:e.apply(null,t)},r?(r.prev=a,a.next=r):o=a,i===n?(o=o.prev).next=null:i++,r=a,a.val}return t&&t.maxSize&&(n=t.maxSize),c.clear=function(){r=null,o=null,i=0},c}},,,function(e,t){!function(){e.exports=this.wp.a11y}()},function(e,t,n){var r;!function(o){var i=/^\s+/,c=/\s+$/,a=0,s=o.round,l=o.min,u=o.max,d=o.random;function p(e,t){if(t=t||{},(e=e||"")instanceof p)return e;if(!(this instanceof p))return new p(e,t);var n=function(e){var t={r:0,g:0,b:0},n=1,r=null,a=null,s=null,d=!1,p=!1;"string"==typeof e&&(e=function(e){e=e.replace(i,"").replace(c,"").toLowerCase();var t,n=!1;if(I[e])e=I[e],n=!0;else if("transparent"==e)return{r:0,g:0,b:0,a:0,format:"name"};if(t=K.rgb.exec(e))return{r:t[1],g:t[2],b:t[3]};if(t=K.rgba.exec(e))return{r:t[1],g:t[2],b:t[3],a:t[4]};if(t=K.hsl.exec(e))return{h:t[1],s:t[2],l:t[3]};if(t=K.hsla.exec(e))return{h:t[1],s:t[2],l:t[3],a:t[4]};if(t=K.hsv.exec(e))return{h:t[1],s:t[2],v:t[3]};if(t=K.hsva.exec(e))return{h:t[1],s:t[2],v:t[3],a:t[4]};if(t=K.hex8.exec(e))return{r:N(t[1]),g:N(t[2]),b:N(t[3]),a:M(t[4]),format:n?"name":"hex8"};if(t=K.hex6.exec(e))return{r:N(t[1]),g:N(t[2]),b:N(t[3]),format:n?"name":"hex"};if(t=K.hex4.exec(e))return{r:N(t[1]+""+t[1]),g:N(t[2]+""+t[2]),b:N(t[3]+""+t[3]),a:M(t[4]+""+t[4]),format:n?"name":"hex8"};if(t=K.hex3.exec(e))return{r:N(t[1]+""+t[1]),g:N(t[2]+""+t[2]),b:N(t[3]+""+t[3]),format:n?"name":"hex"};return!1}(e));"object"==typeof e&&(z(e.r)&&z(e.g)&&z(e.b)?(b=e.r,f=e.g,h=e.b,t={r:255*L(b,255),g:255*L(f,255),b:255*L(h,255)},d=!0,p="%"===String(e.r).substr(-1)?"prgb":"rgb"):z(e.h)&&z(e.s)&&z(e.v)?(r=D(e.s),a=D(e.v),t=function(e,t,n){e=6*L(e,360),t=L(t,100),n=L(n,100);var r=o.floor(e),i=e-r,c=n*(1-t),a=n*(1-i*t),s=n*(1-(1-i)*t),l=r%6;return{r:255*[n,a,c,c,s,n][l],g:255*[s,n,n,a,c,c][l],b:255*[c,c,s,n,n,a][l]}}(e.h,r,a),d=!0,p="hsv"):z(e.h)&&z(e.s)&&z(e.l)&&(r=D(e.s),s=D(e.l),t=function(e,t,n){var r,o,i;function c(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}if(e=L(e,360),t=L(t,100),n=L(n,100),0===t)r=o=i=n;else{var a=n<.5?n*(1+t):n+t-n*t,s=2*n-a;r=c(s,a,e+1/3),o=c(s,a,e),i=c(s,a,e-1/3)}return{r:255*r,g:255*o,b:255*i}}(e.h,r,s),d=!0,p="hsl"),e.hasOwnProperty("a")&&(n=e.a));var b,f,h;return n=x(n),{ok:d,format:e.format||p,r:l(255,u(t.r,0)),g:l(255,u(t.g,0)),b:l(255,u(t.b,0)),a:n}}(e);this._originalInput=e,this._r=n.r,this._g=n.g,this._b=n.b,this._a=n.a,this._roundA=s(100*this._a)/100,this._format=t.format||n.format,this._gradientType=t.gradientType,this._r<1&&(this._r=s(this._r)),this._g<1&&(this._g=s(this._g)),this._b<1&&(this._b=s(this._b)),this._ok=n.ok,this._tc_id=a++}function b(e,t,n){e=L(e,255),t=L(t,255),n=L(n,255);var r,o,i=u(e,t,n),c=l(e,t,n),a=(i+c)/2;if(i==c)r=o=0;else{var s=i-c;switch(o=a>.5?s/(2-i-c):s/(i+c),i){case e:r=(t-n)/s+(t<n?6:0);break;case t:r=(n-e)/s+2;break;case n:r=(e-t)/s+4}r/=6}return{h:r,s:o,l:a}}function f(e,t,n){e=L(e,255),t=L(t,255),n=L(n,255);var r,o,i=u(e,t,n),c=l(e,t,n),a=i,s=i-c;if(o=0===i?0:s/i,i==c)r=0;else{switch(i){case e:r=(t-n)/s+(t<n?6:0);break;case t:r=(n-e)/s+2;break;case n:r=(e-t)/s+4}r/=6}return{h:r,s:o,v:a}}function h(e,t,n,r){var o=[R(s(e).toString(16)),R(s(t).toString(16)),R(s(n).toString(16))];return r&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0):o.join("")}function m(e,t,n,r){return[R(F(r)),R(s(e).toString(16)),R(s(t).toString(16)),R(s(n).toString(16))].join("")}function v(e,t){t=0===t?0:t||10;var n=p(e).toHsl();return n.s-=t/100,n.s=A(n.s),p(n)}function O(e,t){t=0===t?0:t||10;var n=p(e).toHsl();return n.s+=t/100,n.s=A(n.s),p(n)}function g(e){return p(e).desaturate(100)}function j(e,t){t=0===t?0:t||10;var n=p(e).toHsl();return n.l+=t/100,n.l=A(n.l),p(n)}function y(e,t){t=0===t?0:t||10;var n=p(e).toRgb();return n.r=u(0,l(255,n.r-s(-t/100*255))),n.g=u(0,l(255,n.g-s(-t/100*255))),n.b=u(0,l(255,n.b-s(-t/100*255))),p(n)}function k(e,t){t=0===t?0:t||10;var n=p(e).toHsl();return n.l-=t/100,n.l=A(n.l),p(n)}function _(e,t){var n=p(e).toHsl(),r=(n.h+t)%360;return n.h=r<0?360+r:r,p(n)}function E(e){var t=p(e).toHsl();return t.h=(t.h+180)%360,p(t)}function S(e){var t=p(e).toHsl(),n=t.h;return[p(e),p({h:(n+120)%360,s:t.s,l:t.l}),p({h:(n+240)%360,s:t.s,l:t.l})]}function C(e){var t=p(e).toHsl(),n=t.h;return[p(e),p({h:(n+90)%360,s:t.s,l:t.l}),p({h:(n+180)%360,s:t.s,l:t.l}),p({h:(n+270)%360,s:t.s,l:t.l})]}function w(e){var t=p(e).toHsl(),n=t.h;return[p(e),p({h:(n+72)%360,s:t.s,l:t.l}),p({h:(n+216)%360,s:t.s,l:t.l})]}function T(e,t,n){t=t||6,n=n||30;var r=p(e).toHsl(),o=360/n,i=[p(e)];for(r.h=(r.h-(o*t>>1)+720)%360;--t;)r.h=(r.h+o)%360,i.push(p(r));return i}function P(e,t){t=t||6;for(var n=p(e).toHsv(),r=n.h,o=n.s,i=n.v,c=[],a=1/t;t--;)c.push(p({h:r,s:o,v:i})),i=(i+a)%1;return c}p.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},getLuminance:function(){var e,t,n,r=this.toRgb();return e=r.r/255,t=r.g/255,n=r.b/255,.2126*(e<=.03928?e/12.92:o.pow((e+.055)/1.055,2.4))+.7152*(t<=.03928?t/12.92:o.pow((t+.055)/1.055,2.4))+.0722*(n<=.03928?n/12.92:o.pow((n+.055)/1.055,2.4))},setAlpha:function(e){return this._a=x(e),this._roundA=s(100*this._a)/100,this},toHsv:function(){var e=f(this._r,this._g,this._b);return{h:360*e.h,s:e.s,v:e.v,a:this._a}},toHsvString:function(){var e=f(this._r,this._g,this._b),t=s(360*e.h),n=s(100*e.s),r=s(100*e.v);return 1==this._a?"hsv("+t+", "+n+"%, "+r+"%)":"hsva("+t+", "+n+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var e=b(this._r,this._g,this._b);return{h:360*e.h,s:e.s,l:e.l,a:this._a}},toHslString:function(){var e=b(this._r,this._g,this._b),t=s(360*e.h),n=s(100*e.s),r=s(100*e.l);return 1==this._a?"hsl("+t+", "+n+"%, "+r+"%)":"hsla("+t+", "+n+"%, "+r+"%, "+this._roundA+")"},toHex:function(e){return h(this._r,this._g,this._b,e)},toHexString:function(e){return"#"+this.toHex(e)},toHex8:function(e){return function(e,t,n,r,o){var i=[R(s(e).toString(16)),R(s(t).toString(16)),R(s(n).toString(16)),R(F(r))];if(o&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1))return i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0);return i.join("")}(this._r,this._g,this._b,this._a,e)},toHex8String:function(e){return"#"+this.toHex8(e)},toRgb:function(){return{r:s(this._r),g:s(this._g),b:s(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+s(this._r)+", "+s(this._g)+", "+s(this._b)+")":"rgba("+s(this._r)+", "+s(this._g)+", "+s(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:s(100*L(this._r,255))+"%",g:s(100*L(this._g,255))+"%",b:s(100*L(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+s(100*L(this._r,255))+"%, "+s(100*L(this._g,255))+"%, "+s(100*L(this._b,255))+"%)":"rgba("+s(100*L(this._r,255))+"%, "+s(100*L(this._g,255))+"%, "+s(100*L(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(B[h(this._r,this._g,this._b,!0)]||!1)},toFilter:function(e){var t="#"+m(this._r,this._g,this._b,this._a),n=t,r=this._gradientType?"GradientType = 1, ":"";if(e){var o=p(e);n="#"+m(o._r,o._g,o._b,o._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+t+",endColorstr="+n+")"},toString:function(e){var t=!!e;e=e||this._format;var n=!1,r=this._a<1&&this._a>=0;return t||!r||"hex"!==e&&"hex6"!==e&&"hex3"!==e&&"hex4"!==e&&"hex8"!==e&&"name"!==e?("rgb"===e&&(n=this.toRgbString()),"prgb"===e&&(n=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(n=this.toHexString()),"hex3"===e&&(n=this.toHexString(!0)),"hex4"===e&&(n=this.toHex8String(!0)),"hex8"===e&&(n=this.toHex8String()),"name"===e&&(n=this.toName()),"hsl"===e&&(n=this.toHslString()),"hsv"===e&&(n=this.toHsvString()),n||this.toHexString()):"name"===e&&0===this._a?this.toName():this.toRgbString()},clone:function(){return p(this.toString())},_applyModification:function(e,t){var n=e.apply(null,[this].concat([].slice.call(t)));return this._r=n._r,this._g=n._g,this._b=n._b,this.setAlpha(n._a),this},lighten:function(){return this._applyModification(j,arguments)},brighten:function(){return this._applyModification(y,arguments)},darken:function(){return this._applyModification(k,arguments)},desaturate:function(){return this._applyModification(v,arguments)},saturate:function(){return this._applyModification(O,arguments)},greyscale:function(){return this._applyModification(g,arguments)},spin:function(){return this._applyModification(_,arguments)},_applyCombination:function(e,t){return e.apply(null,[this].concat([].slice.call(t)))},analogous:function(){return this._applyCombination(T,arguments)},complement:function(){return this._applyCombination(E,arguments)},monochromatic:function(){return this._applyCombination(P,arguments)},splitcomplement:function(){return this._applyCombination(w,arguments)},triad:function(){return this._applyCombination(S,arguments)},tetrad:function(){return this._applyCombination(C,arguments)}},p.fromRatio=function(e,t){if("object"==typeof e){var n={};for(var r in e)e.hasOwnProperty(r)&&(n[r]="a"===r?e[r]:D(e[r]));e=n}return p(e,t)},p.equals=function(e,t){return!(!e||!t)&&p(e).toRgbString()==p(t).toRgbString()},p.random=function(){return p.fromRatio({r:d(),g:d(),b:d()})},p.mix=function(e,t,n){n=0===n?0:n||50;var r=p(e).toRgb(),o=p(t).toRgb(),i=n/100;return p({r:(o.r-r.r)*i+r.r,g:(o.g-r.g)*i+r.g,b:(o.b-r.b)*i+r.b,a:(o.a-r.a)*i+r.a})},p.readability=function(e,t){var n=p(e),r=p(t);return(o.max(n.getLuminance(),r.getLuminance())+.05)/(o.min(n.getLuminance(),r.getLuminance())+.05)},p.isReadable=function(e,t,n){var r,o,i=p.readability(e,t);switch(o=!1,(r=function(e){var t,n;t=((e=e||{level:"AA",size:"small"}).level||"AA").toUpperCase(),n=(e.size||"small").toLowerCase(),"AA"!==t&&"AAA"!==t&&(t="AA");"small"!==n&&"large"!==n&&(n="small");return{level:t,size:n}}(n)).level+r.size){case"AAsmall":case"AAAlarge":o=i>=4.5;break;case"AAlarge":o=i>=3;break;case"AAAsmall":o=i>=7}return o},p.mostReadable=function(e,t,n){var r,o,i,c,a=null,s=0;o=(n=n||{}).includeFallbackColors,i=n.level,c=n.size;for(var l=0;l<t.length;l++)(r=p.readability(e,t[l]))>s&&(s=r,a=p(t[l]));return p.isReadable(e,a,{level:i,size:c})||!o?a:(n.includeFallbackColors=!1,p.mostReadable(e,["#fff","#000"],n))};var I=p.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},B=p.hexNames=function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}(I);function x(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function L(e,t){(function(e){return"string"==typeof e&&-1!=e.indexOf(".")&&1===parseFloat(e)})(e)&&(e="100%");var n=function(e){return"string"==typeof e&&-1!=e.indexOf("%")}(e);return e=l(t,u(0,parseFloat(e))),n&&(e=parseInt(e*t,10)/100),o.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function A(e){return l(1,u(0,e))}function N(e){return parseInt(e,16)}function R(e){return 1==e.length?"0"+e:""+e}function D(e){return e<=1&&(e=100*e+"%"),e}function F(e){return o.round(255*parseFloat(e)).toString(16)}function M(e){return N(e)/255}var U,H,V,K=(H="[\\s|\\(]+("+(U="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+U+")[,|\\s]+("+U+")\\s*\\)?",V="[\\s|\\(]+("+U+")[,|\\s]+("+U+")[,|\\s]+("+U+")[,|\\s]+("+U+")\\s*\\)?",{CSS_UNIT:new RegExp(U),rgb:new RegExp("rgb"+H),rgba:new RegExp("rgba"+V),hsl:new RegExp("hsl"+H),hsla:new RegExp("hsla"+V),hsv:new RegExp("hsv"+H),hsva:new RegExp("hsva"+V),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function z(e){return!!K.CSS_UNIT.exec(e)}e.exports?e.exports=p:void 0===(r=function(){return p}.call(t,n,t,e))||(e.exports=r)}(Math)},function(e,t){!function(){e.exports=this.wp.date}()},,,function(e,t){!function(){e.exports=this.wp.htmlEntities}()},,function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){!function(){e.exports=this.wp.nux}()},function(e,t,n){e.exports=n(270)},,function(e,t,n){"use strict";t.__esModule=!0;var r=n(271);t.default=r.default},,,function(e,t){!function(){e.exports=this.wp.autop}()},function(e,t,n){"use strict";e.exports=n(118)},,,,,,function(e,t){!function(){e.exports=this.wp.deprecated}()},,,,,,,,,,function(e,t,n){"use strict";var r=n(100),o=n(102);function i(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=g,t.resolve=function(e,t){return g(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?g(e,!1,!0).resolveObject(t):t},t.format=function(e){o.isString(e)&&(e=g(e));return e instanceof i?e.format():i.prototype.format.call(e)},t.Url=i;var c=/^([a-z0-9.+-]+:)/i,a=/:[0-9]*$/,s=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,l=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),u=["'"].concat(l),d=["%","/","?",";","#"].concat(u),p=["/","?","#"],b=/^[+a-z0-9A-Z_-]{0,63}$/,f=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,h={javascript:!0,"javascript:":!0},m={javascript:!0,"javascript:":!0},v={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},O=n(103);function g(e,t,n){if(e&&o.isObject(e)&&e instanceof i)return e;var r=new i;return r.parse(e,t,n),r}i.prototype.parse=function(e,t,n){if(!o.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var i=e.indexOf("?"),a=-1!==i&&i<e.indexOf("#")?"?":"#",l=e.split(a);l[0]=l[0].replace(/\\/g,"/");var g=e=l.join(a);if(g=g.trim(),!n&&1===e.split("#").length){var j=s.exec(g);if(j)return this.path=g,this.href=g,this.pathname=j[1],j[2]?(this.search=j[2],this.query=t?O.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var y=c.exec(g);if(y){var k=(y=y[0]).toLowerCase();this.protocol=k,g=g.substr(y.length)}if(n||y||g.match(/^\/\/[^@\/]+@[^@\/]+/)){var _="//"===g.substr(0,2);!_||y&&m[y]||(g=g.substr(2),this.slashes=!0)}if(!m[y]&&(_||y&&!v[y])){for(var E,S,C=-1,w=0;w<p.length;w++){-1!==(T=g.indexOf(p[w]))&&(-1===C||T<C)&&(C=T)}-1!==(S=-1===C?g.lastIndexOf("@"):g.lastIndexOf("@",C))&&(E=g.slice(0,S),g=g.slice(S+1),this.auth=decodeURIComponent(E)),C=-1;for(w=0;w<d.length;w++){var T;-1!==(T=g.indexOf(d[w]))&&(-1===C||T<C)&&(C=T)}-1===C&&(C=g.length),this.host=g.slice(0,C),g=g.slice(C),this.parseHost(),this.hostname=this.hostname||"";var P="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!P)for(var I=this.hostname.split(/\./),B=(w=0,I.length);w<B;w++){var x=I[w];if(x&&!x.match(b)){for(var L="",A=0,N=x.length;A<N;A++)x.charCodeAt(A)>127?L+="x":L+=x[A];if(!L.match(b)){var R=I.slice(0,w),D=I.slice(w+1),F=x.match(f);F&&(R.push(F[1]),D.unshift(F[2])),D.length&&(g="/"+D.join(".")+g),this.hostname=R.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),P||(this.hostname=r.toASCII(this.hostname));var M=this.port?":"+this.port:"",U=this.hostname||"";this.host=U+M,this.href+=this.host,P&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==g[0]&&(g="/"+g))}if(!h[k])for(w=0,B=u.length;w<B;w++){var H=u[w];if(-1!==g.indexOf(H)){var V=encodeURIComponent(H);V===H&&(V=escape(H)),g=g.split(H).join(V)}}var K=g.indexOf("#");-1!==K&&(this.hash=g.substr(K),g=g.slice(0,K));var z=g.indexOf("?");if(-1!==z?(this.search=g.substr(z),this.query=g.substr(z+1),t&&(this.query=O.parse(this.query)),g=g.slice(0,z)):t&&(this.search="",this.query={}),g&&(this.pathname=g),v[k]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){M=this.pathname||"";var W=this.search||"";this.path=M+W}return this.href=this.format(),this},i.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",n=this.pathname||"",r=this.hash||"",i=!1,c="";this.host?i=e+this.host:this.hostname&&(i=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&o.isObject(this.query)&&Object.keys(this.query).length&&(c=O.stringify(this.query));var a=this.search||c&&"?"+c||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||v[t])&&!1!==i?(i="//"+(i||""),n&&"/"!==n.charAt(0)&&(n="/"+n)):i||(i=""),r&&"#"!==r.charAt(0)&&(r="#"+r),a&&"?"!==a.charAt(0)&&(a="?"+a),t+i+(n=n.replace(/[?#]/g,function(e){return encodeURIComponent(e)}))+(a=a.replace("#","%23"))+r},i.prototype.resolve=function(e){return this.resolveObject(g(e,!1,!0)).format()},i.prototype.resolveObject=function(e){if(o.isString(e)){var t=new i;t.parse(e,!1,!0),e=t}for(var n=new i,r=Object.keys(this),c=0;c<r.length;c++){var a=r[c];n[a]=this[a]}if(n.hash=e.hash,""===e.href)return n.href=n.format(),n;if(e.slashes&&!e.protocol){for(var s=Object.keys(e),l=0;l<s.length;l++){var u=s[l];"protocol"!==u&&(n[u]=e[u])}return v[n.protocol]&&n.hostname&&!n.pathname&&(n.path=n.pathname="/"),n.href=n.format(),n}if(e.protocol&&e.protocol!==n.protocol){if(!v[e.protocol]){for(var d=Object.keys(e),p=0;p<d.length;p++){var b=d[p];n[b]=e[b]}return n.href=n.format(),n}if(n.protocol=e.protocol,e.host||m[e.protocol])n.pathname=e.pathname;else{for(var f=(e.pathname||"").split("/");f.length&&!(e.host=f.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==f[0]&&f.unshift(""),f.length<2&&f.unshift(""),n.pathname=f.join("/")}if(n.search=e.search,n.query=e.query,n.host=e.host||"",n.auth=e.auth,n.hostname=e.hostname||e.host,n.port=e.port,n.pathname||n.search){var h=n.pathname||"",O=n.search||"";n.path=h+O}return n.slashes=n.slashes||e.slashes,n.href=n.format(),n}var g=n.pathname&&"/"===n.pathname.charAt(0),j=e.host||e.pathname&&"/"===e.pathname.charAt(0),y=j||g||n.host&&e.pathname,k=y,_=n.pathname&&n.pathname.split("/")||[],E=(f=e.pathname&&e.pathname.split("/")||[],n.protocol&&!v[n.protocol]);if(E&&(n.hostname="",n.port=null,n.host&&(""===_[0]?_[0]=n.host:_.unshift(n.host)),n.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===f[0]?f[0]=e.host:f.unshift(e.host)),e.host=null),y=y&&(""===f[0]||""===_[0])),j)n.host=e.host||""===e.host?e.host:n.host,n.hostname=e.hostname||""===e.hostname?e.hostname:n.hostname,n.search=e.search,n.query=e.query,_=f;else if(f.length)_||(_=[]),_.pop(),_=_.concat(f),n.search=e.search,n.query=e.query;else if(!o.isNullOrUndefined(e.search)){if(E)n.hostname=n.host=_.shift(),(P=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=P.shift(),n.host=n.hostname=P.shift());return n.search=e.search,n.query=e.query,o.isNull(n.pathname)&&o.isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.href=n.format(),n}if(!_.length)return n.pathname=null,n.search?n.path="/"+n.search:n.path=null,n.href=n.format(),n;for(var S=_.slice(-1)[0],C=(n.host||e.host||_.length>1)&&("."===S||".."===S)||""===S,w=0,T=_.length;T>=0;T--)"."===(S=_[T])?_.splice(T,1):".."===S?(_.splice(T,1),w++):w&&(_.splice(T,1),w--);if(!y&&!k)for(;w--;w)_.unshift("..");!y||""===_[0]||_[0]&&"/"===_[0].charAt(0)||_.unshift(""),C&&"/"!==_.join("/").substr(-1)&&_.push("");var P,I=""===_[0]||_[0]&&"/"===_[0].charAt(0);E&&(n.hostname=n.host=I?"":_.length?_.shift():"",(P=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=P.shift(),n.host=n.hostname=P.shift()));return(y=y||n.host&&_.length)&&!I&&_.unshift(""),_.length?n.pathname=_.join("/"):(n.pathname=null,n.path=null),o.isNull(n.pathname)&&o.isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.auth=e.auth||n.auth,n.slashes=n.slashes||e.slashes,n.href=n.format(),n},i.prototype.parseHost=function(){var e=this.host,t=a.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},,,,function(e,t){!function(){e.exports=this.wp.coreData}()},,,,,,,,function(e,t,n){"use strict";e.exports=function(e){var t,n={};return function e(t,n){var r;if(Array.isArray(n))for(r=0;r<n.length;r++)e(t,n[r]);else for(r in n)t[r]=(t[r]||[]).concat(n[r])}(n,e),(t=function(e){return function(t){return function(r){var o,i,c=n[r.type],a=t(r);if(c)for(o=0;o<c.length;o++)(i=c[o](r,e))&&e.dispatch(i);return a}}}).effects=n,t}},,,,,,,,,,function(e,t){!function(){e.exports=this.wp.wordcount}()},function(e,t){"function"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},function(e,t){!function(){e.exports=this.jQuery}()},function(e,t,n){(function(e,r){var o;/*! https://mths.be/punycode v1.4.1 by @mathias */!function(i){t&&t.nodeType,e&&e.nodeType;var c="object"==typeof r&&r;c.global!==c&&c.window!==c&&c.self;var a,s=2147483647,l=36,u=1,d=26,p=38,b=700,f=72,h=128,m="-",v=/^xn--/,O=/[^\x20-\x7E]/,g=/[\x2E\u3002\uFF0E\uFF61]/g,j={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},y=l-u,k=Math.floor,_=String.fromCharCode;function E(e){throw new RangeError(j[e])}function S(e,t){for(var n=e.length,r=[];n--;)r[n]=t(e[n]);return r}function C(e,t){var n=e.split("@"),r="";return n.length>1&&(r=n[0]+"@",e=n[1]),r+S((e=e.replace(g,".")).split("."),t).join(".")}function w(e){for(var t,n,r=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(n=e.charCodeAt(o++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),o--):r.push(t);return r}function T(e){return S(e,function(e){var t="";return e>65535&&(t+=_((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=_(e)}).join("")}function P(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function I(e,t,n){var r=0;for(e=n?k(e/b):e>>1,e+=k(e/t);e>y*d>>1;r+=l)e=k(e/y);return k(r+(y+1)*e/(e+p))}function B(e){var t,n,r,o,i,c,a,p,b,v,O,g=[],j=e.length,y=0,_=h,S=f;for((n=e.lastIndexOf(m))<0&&(n=0),r=0;r<n;++r)e.charCodeAt(r)>=128&&E("not-basic"),g.push(e.charCodeAt(r));for(o=n>0?n+1:0;o<j;){for(i=y,c=1,a=l;o>=j&&E("invalid-input"),((p=(O=e.charCodeAt(o++))-48<10?O-22:O-65<26?O-65:O-97<26?O-97:l)>=l||p>k((s-y)/c))&&E("overflow"),y+=p*c,!(p<(b=a<=S?u:a>=S+d?d:a-S));a+=l)c>k(s/(v=l-b))&&E("overflow"),c*=v;S=I(y-i,t=g.length+1,0==i),k(y/t)>s-_&&E("overflow"),_+=k(y/t),y%=t,g.splice(y++,0,_)}return T(g)}function x(e){var t,n,r,o,i,c,a,p,b,v,O,g,j,y,S,C=[];for(g=(e=w(e)).length,t=h,n=0,i=f,c=0;c<g;++c)(O=e[c])<128&&C.push(_(O));for(r=o=C.length,o&&C.push(m);r<g;){for(a=s,c=0;c<g;++c)(O=e[c])>=t&&O<a&&(a=O);for(a-t>k((s-n)/(j=r+1))&&E("overflow"),n+=(a-t)*j,t=a,c=0;c<g;++c)if((O=e[c])<t&&++n>s&&E("overflow"),O==t){for(p=n,b=l;!(p<(v=b<=i?u:b>=i+d?d:b-i));b+=l)S=p-v,y=l-v,C.push(_(P(v+S%y,0))),p=k(S/y);C.push(_(P(p,0))),i=I(n,j,r==o),n=0,++r}++n,++t}return C.join("")}a={version:"1.4.1",ucs2:{decode:w,encode:T},decode:B,encode:x,toASCII:function(e){return C(e,function(e){return O.test(e)?"xn--"+x(e):e})},toUnicode:function(e){return C(e,function(e){return v.test(e)?B(e.slice(4).toLowerCase()):e})}},void 0===(o=function(){return a}.call(t,n,t,e))||(e.exports=o)}()}).call(this,n(101)(e),n(51))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";e.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},function(e,t,n){"use strict";t.decode=t.parse=n(104),t.encode=t.stringify=n(105)},function(e,t,n){"use strict";function r(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,n,i){t=t||"&",n=n||"=";var c={};if("string"!=typeof e||0===e.length)return c;var a=/\+/g;e=e.split(t);var s=1e3;i&&"number"==typeof i.maxKeys&&(s=i.maxKeys);var l=e.length;s>0&&l>s&&(l=s);for(var u=0;u<l;++u){var d,p,b,f,h=e[u].replace(a,"%20"),m=h.indexOf(n);m>=0?(d=h.substr(0,m),p=h.substr(m+1)):(d=h,p=""),b=decodeURIComponent(d),f=decodeURIComponent(p),r(c,b)?o(c[b])?c[b].push(f):c[b]=[c[b],f]:c[b]=f}return c};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},function(e,t,n){"use strict";var r=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,n,a){return t=t||"&",n=n||"=",null===e&&(e=void 0),"object"==typeof e?i(c(e),function(c){var a=encodeURIComponent(r(c))+n;return o(e[c])?i(e[c],function(e){return a+encodeURIComponent(r(e))}).join(t):a+encodeURIComponent(r(e[c]))}).join(t):a?encodeURIComponent(r(a))+n+encodeURIComponent(r(e)):""};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function i(e,t){if(e.map)return e.map(t);for(var n=[],r=0;r<e.length;r++)n.push(t(e[r],r));return n}var c=Object.keys||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t}},,,,function(e,t,n){"use strict";var r=n(110);function o(){}e.exports=function(){function e(e,t,n,o,i,c){if(c!==r){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t};return n.checkPropTypes=o,n.PropTypes=n,n}},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},,,,,,,,function(e,t,n){"use strict";var r=n(119);e.exports=function(e,t,n){n=n||{},9===t.nodeType&&(t=r.getWindow(t));var o=n.allowHorizontalScroll,i=n.onlyScrollIfNeeded,c=n.alignWithTop,a=n.alignWithLeft,s=n.offsetTop||0,l=n.offsetLeft||0,u=n.offsetBottom||0,d=n.offsetRight||0;o=void 0===o||o;var p=r.isWindow(t),b=r.offset(e),f=r.outerHeight(e),h=r.outerWidth(e),m=void 0,v=void 0,O=void 0,g=void 0,j=void 0,y=void 0,k=void 0,_=void 0,E=void 0,S=void 0;p?(k=t,S=r.height(k),E=r.width(k),_={left:r.scrollLeft(k),top:r.scrollTop(k)},j={left:b.left-_.left-l,top:b.top-_.top-s},y={left:b.left+h-(_.left+E)+d,top:b.top+f-(_.top+S)+u},g=_):(m=r.offset(t),v=t.clientHeight,O=t.clientWidth,g={left:t.scrollLeft,top:t.scrollTop},j={left:b.left-(m.left+(parseFloat(r.css(t,"borderLeftWidth"))||0))-l,top:b.top-(m.top+(parseFloat(r.css(t,"borderTopWidth"))||0))-s},y={left:b.left+h-(m.left+O+(parseFloat(r.css(t,"borderRightWidth"))||0))+d,top:b.top+f-(m.top+v+(parseFloat(r.css(t,"borderBottomWidth"))||0))+u}),j.top<0||y.top>0?!0===c?r.scrollTop(t,g.top+j.top):!1===c?r.scrollTop(t,g.top+y.top):j.top<0?r.scrollTop(t,g.top+j.top):r.scrollTop(t,g.top+y.top):i||((c=void 0===c||!!c)?r.scrollTop(t,g.top+j.top):r.scrollTop(t,g.top+y.top)),o&&(j.left<0||y.left>0?!0===a?r.scrollLeft(t,g.left+j.left):!1===a?r.scrollLeft(t,g.left+y.left):j.left<0?r.scrollLeft(t,g.left+j.left):r.scrollLeft(t,g.left+y.left):i||((a=void 0===a||!!a)?r.scrollLeft(t,g.left+j.left):r.scrollLeft(t,g.left+y.left)))}},function(e,t,n){"use strict";var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function i(e,t){var n=e["page"+(t?"Y":"X")+"Offset"],r="scroll"+(t?"Top":"Left");if("number"!=typeof n){var o=e.document;"number"!=typeof(n=o.documentElement[r])&&(n=o.body[r])}return n}function c(e){return i(e)}function a(e){return i(e,!0)}function s(e){var t=function(e){var t,n=void 0,r=void 0,o=e.ownerDocument,i=o.body,c=o&&o.documentElement;return n=(t=e.getBoundingClientRect()).left,r=t.top,{left:n-=c.clientLeft||i.clientLeft||0,top:r-=c.clientTop||i.clientTop||0}}(e),n=e.ownerDocument,r=n.defaultView||n.parentWindow;return t.left+=c(r),t.top+=a(r),t}var l=new RegExp("^("+/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source+")(?!px)[a-z%]+$","i"),u=/^(top|right|bottom|left)$/,d="currentStyle",p="runtimeStyle",b="left",f="px";var h=void 0;function m(e,t){for(var n=0;n<e.length;n++)t(e[n])}function v(e){return"border-box"===h(e,"boxSizing")}"undefined"!=typeof window&&(h=window.getComputedStyle?function(e,t,n){var r="",o=e.ownerDocument,i=n||o.defaultView.getComputedStyle(e,null);return i&&(r=i.getPropertyValue(t)||i[t]),r}:function(e,t){var n=e[d]&&e[d][t];if(l.test(n)&&!u.test(t)){var r=e.style,o=r[b],i=e[p][b];e[p][b]=e[d][b],r[b]="fontSize"===t?"1em":n||0,n=r.pixelLeft+f,r[b]=o,e[p][b]=i}return""===n?"auto":n});var O=["margin","border","padding"],g=-1,j=2,y=1;function k(e,t,n){var r=0,o=void 0,i=void 0,c=void 0;for(i=0;i<t.length;i++)if(o=t[i])for(c=0;c<n.length;c++){var a=void 0;a="border"===o?o+n[c]+"Width":o+n[c],r+=parseFloat(h(e,a))||0}return r}function _(e){return null!=e&&e==e.window}var E={};function S(e,t,n){if(_(e))return"width"===t?E.viewportWidth(e):E.viewportHeight(e);if(9===e.nodeType)return"width"===t?E.docWidth(e):E.docHeight(e);var r="width"===t?["Left","Right"]:["Top","Bottom"],o="width"===t?e.offsetWidth:e.offsetHeight,i=(h(e),v(e)),c=0;(null==o||o<=0)&&(o=void 0,(null==(c=h(e,t))||Number(c)<0)&&(c=e.style[t]||0),c=parseFloat(c)||0),void 0===n&&(n=i?y:g);var a=void 0!==o||i,s=o||c;if(n===g)return a?s-k(e,["border","padding"],r):c;if(a){var l=n===j?-k(e,["border"],r):k(e,["margin"],r);return s+(n===y?0:l)}return c+k(e,O.slice(n),r)}m(["Width","Height"],function(e){E["doc"+e]=function(t){var n=t.document;return Math.max(n.documentElement["scroll"+e],n.body["scroll"+e],E["viewport"+e](n))},E["viewport"+e]=function(t){var n="client"+e,r=t.document,o=r.body,i=r.documentElement[n];return"CSS1Compat"===r.compatMode&&i||o&&o[n]||i}});var C={position:"absolute",visibility:"hidden",display:"block"};function w(e){var t=void 0,n=arguments;return 0!==e.offsetWidth?t=S.apply(void 0,n):function(e,t,n){var r={},o=e.style,i=void 0;for(i in t)t.hasOwnProperty(i)&&(r[i]=o[i],o[i]=t[i]);for(i in n.call(e),t)t.hasOwnProperty(i)&&(o[i]=r[i])}(e,C,function(){t=S.apply(void 0,n)}),t}function T(e,t,n){var r=n;if("object"!==(void 0===t?"undefined":o(t)))return void 0!==r?("number"==typeof r&&(r+="px"),void(e.style[t]=r)):h(e,t);for(var i in t)t.hasOwnProperty(i)&&T(e,i,t[i])}m(["width","height"],function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);E["outer"+t]=function(t,n){return t&&w(t,e,n?0:y)};var n="width"===e?["Left","Right"]:["Top","Bottom"];E[e]=function(t,r){if(void 0===r)return t&&w(t,e,g);if(t){h(t);return v(t)&&(r+=k(t,["padding","border"],n)),T(t,e,r)}}}),e.exports=r({getWindow:function(e){var t=e.ownerDocument||e;return t.defaultView||t.parentWindow},offset:function(e,t){if(void 0===t)return s(e);!function(e,t){"static"===T(e,"position")&&(e.style.position="relative");var n=s(e),r={},o=void 0,i=void 0;for(i in t)t.hasOwnProperty(i)&&(o=parseFloat(T(e,i))||0,r[i]=o+t[i]-n[i]);T(e,r)}(e,t)},isWindow:_,each:m,css:T,clone:function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);if(e.overflow)for(var n in e)e.hasOwnProperty(n)&&(t.overflow[n]=e.overflow[n]);return t},scrollLeft:function(e,t){if(_(e)){if(void 0===t)return c(e);window.scrollTo(t,a(e))}else{if(void 0===t)return e.scrollLeft;e.scrollLeft=t}},scrollTop:function(e,t){if(_(e)){if(void 0===t)return a(e);window.scrollTo(c(e),t)}else{if(void 0===t)return e.scrollTop;e.scrollTop=t}},viewportWidth:0,viewportHeight:0},E)},,function(e,t){!function(){e.exports=this.wp.notices}()},,,function(e,t){!function(){e.exports=this.wp.tokenList}()},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.dispatch;return function(e){return function(n){return Array.isArray(n)?n.filter(Boolean).map(t):e(n)}}}},function(e,t,n){
/*!

 diff v3.5.0

Software License Agreement (BSD License)

Copyright (c) 2009-2015, Kevin Decker <<EMAIL>>

All rights reserved.

Redistribution and use of this software in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above
  copyright notice, this list of conditions and the
  following disclaimer.

* Redistributions in binary form must reproduce the above
  copyright notice, this list of conditions and the
  following disclaimer in the documentation and/or other
  materials provided with the distribution.

* Neither the name of Kevin Decker nor the names of its
  contributors may be used to endorse or promote products
  derived from this software without specific prior
  written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
@license
*/
var r;r=function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={exports:{},id:r,loaded:!1};return e[r].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}return n.m=e,n.c=t,n.p="",n(0)}([function(e,t,n){"use strict";t.__esModule=!0,t.canonicalize=t.convertChangesToXML=t.convertChangesToDMP=t.merge=t.parsePatch=t.applyPatches=t.applyPatch=t.createPatch=t.createTwoFilesPatch=t.structuredPatch=t.diffArrays=t.diffJson=t.diffCss=t.diffSentences=t.diffTrimmedLines=t.diffLines=t.diffWordsWithSpace=t.diffWords=t.diffChars=t.Diff=void 0;var r,o=n(1),i=(r=o)&&r.__esModule?r:{default:r},c=n(2),a=n(3),s=n(5),l=n(6),u=n(7),d=n(8),p=n(9),b=n(10),f=n(11),h=n(13),m=n(14),v=n(16),O=n(17);t.Diff=i.default,t.diffChars=c.diffChars,t.diffWords=a.diffWords,t.diffWordsWithSpace=a.diffWordsWithSpace,t.diffLines=s.diffLines,t.diffTrimmedLines=s.diffTrimmedLines,t.diffSentences=l.diffSentences,t.diffCss=u.diffCss,t.diffJson=d.diffJson,t.diffArrays=p.diffArrays,t.structuredPatch=m.structuredPatch,t.createTwoFilesPatch=m.createTwoFilesPatch,t.createPatch=m.createPatch,t.applyPatch=b.applyPatch,t.applyPatches=b.applyPatches,t.parsePatch=f.parsePatch,t.merge=h.merge,t.convertChangesToDMP=v.convertChangesToDMP,t.convertChangesToXML=O.convertChangesToXML,t.canonicalize=d.canonicalize},function(e,t){"use strict";function n(){}function r(e,t,n,r,o){for(var i=0,c=t.length,a=0,s=0;i<c;i++){var l=t[i];if(l.removed){if(l.value=e.join(r.slice(s,s+l.count)),s+=l.count,i&&t[i-1].added){var u=t[i-1];t[i-1]=t[i],t[i]=u}}else{if(!l.added&&o){var d=n.slice(a,a+l.count);d=d.map(function(e,t){var n=r[s+t];return n.length>e.length?n:e}),l.value=e.join(d)}else l.value=e.join(n.slice(a,a+l.count));a+=l.count,l.added||(s+=l.count)}}var p=t[c-1];return c>1&&"string"==typeof p.value&&(p.added||p.removed)&&e.equals("",p.value)&&(t[c-2].value+=p.value,t.pop()),t}t.__esModule=!0,t.default=n,n.prototype={diff:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.callback;"function"==typeof n&&(o=n,n={}),this.options=n;var i=this;function c(e){return o?(setTimeout(function(){o(void 0,e)},0),!0):e}e=this.castInput(e),t=this.castInput(t),e=this.removeEmpty(this.tokenize(e));var a=(t=this.removeEmpty(this.tokenize(t))).length,s=e.length,l=1,u=a+s,d=[{newPos:-1,components:[]}],p=this.extractCommon(d[0],t,e,0);if(d[0].newPos+1>=a&&p+1>=s)return c([{value:this.join(t),count:t.length}]);function b(){for(var n=-1*l;n<=l;n+=2){var o=void 0,u=d[n-1],p=d[n+1],b=(p?p.newPos:0)-n;u&&(d[n-1]=void 0);var f=u&&u.newPos+1<a,h=p&&0<=b&&b<s;if(f||h){if(!f||h&&u.newPos<p.newPos?(o={newPos:(m=p).newPos,components:m.components.slice(0)},i.pushComponent(o.components,void 0,!0)):((o=u).newPos++,i.pushComponent(o.components,!0,void 0)),b=i.extractCommon(o,t,e,n),o.newPos+1>=a&&b+1>=s)return c(r(i,o.components,t,e,i.useLongestToken));d[n]=o}else d[n]=void 0}var m;l++}if(o)!function e(){setTimeout(function(){if(l>u)return o();b()||e()},0)}();else for(;l<=u;){var f=b();if(f)return f}},pushComponent:function(e,t,n){var r=e[e.length-1];r&&r.added===t&&r.removed===n?e[e.length-1]={count:r.count+1,added:t,removed:n}:e.push({count:1,added:t,removed:n})},extractCommon:function(e,t,n,r){for(var o=t.length,i=n.length,c=e.newPos,a=c-r,s=0;c+1<o&&a+1<i&&this.equals(t[c+1],n[a+1]);)c++,a++,s++;return s&&e.components.push({count:s}),e.newPos=c,a},equals:function(e,t){return this.options.comparator?this.options.comparator(e,t):e===t||this.options.ignoreCase&&e.toLowerCase()===t.toLowerCase()},removeEmpty:function(e){for(var t=[],n=0;n<e.length;n++)e[n]&&t.push(e[n]);return t},castInput:function(e){return e},tokenize:function(e){return e.split("")},join:function(e){return e.join("")}}},function(e,t,n){"use strict";t.__esModule=!0,t.characterDiff=void 0,t.diffChars=function(e,t,n){return c.diff(e,t,n)};var r,o=n(1),i=(r=o)&&r.__esModule?r:{default:r};var c=t.characterDiff=new i.default},function(e,t,n){"use strict";t.__esModule=!0,t.wordDiff=void 0,t.diffWords=function(e,t,n){return n=(0,c.generateOptions)(n,{ignoreWhitespace:!0}),l.diff(e,t,n)},t.diffWordsWithSpace=function(e,t,n){return l.diff(e,t,n)};var r,o=n(1),i=(r=o)&&r.__esModule?r:{default:r},c=n(4);var a=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,s=/\S/,l=t.wordDiff=new i.default;l.equals=function(e,t){return this.options.ignoreCase&&(e=e.toLowerCase(),t=t.toLowerCase()),e===t||this.options.ignoreWhitespace&&!s.test(e)&&!s.test(t)},l.tokenize=function(e){for(var t=e.split(/(\s+|\b)/),n=0;n<t.length-1;n++)!t[n+1]&&t[n+2]&&a.test(t[n])&&a.test(t[n+2])&&(t[n]+=t[n+2],t.splice(n+1,2),n--);return t}},function(e,t){"use strict";t.__esModule=!0,t.generateOptions=function(e,t){if("function"==typeof e)t.callback=e;else if(e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},function(e,t,n){"use strict";t.__esModule=!0,t.lineDiff=void 0,t.diffLines=function(e,t,n){return a.diff(e,t,n)},t.diffTrimmedLines=function(e,t,n){var r=(0,c.generateOptions)(n,{ignoreWhitespace:!0});return a.diff(e,t,r)};var r,o=n(1),i=(r=o)&&r.__esModule?r:{default:r},c=n(4);var a=t.lineDiff=new i.default;a.tokenize=function(e){var t=[],n=e.split(/(\n|\r\n)/);n[n.length-1]||n.pop();for(var r=0;r<n.length;r++){var o=n[r];r%2&&!this.options.newlineIsToken?t[t.length-1]+=o:(this.options.ignoreWhitespace&&(o=o.trim()),t.push(o))}return t}},function(e,t,n){"use strict";t.__esModule=!0,t.sentenceDiff=void 0,t.diffSentences=function(e,t,n){return c.diff(e,t,n)};var r,o=n(1),i=(r=o)&&r.__esModule?r:{default:r};var c=t.sentenceDiff=new i.default;c.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)}},function(e,t,n){"use strict";t.__esModule=!0,t.cssDiff=void 0,t.diffCss=function(e,t,n){return c.diff(e,t,n)};var r,o=n(1),i=(r=o)&&r.__esModule?r:{default:r};var c=t.cssDiff=new i.default;c.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)}},function(e,t,n){"use strict";t.__esModule=!0,t.jsonDiff=void 0;var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.diffJson=function(e,t,n){return l.diff(e,t,n)},t.canonicalize=u;var o,i=n(1),c=(o=i)&&o.__esModule?o:{default:o},a=n(5);var s=Object.prototype.toString,l=t.jsonDiff=new c.default;function u(e,t,n,o,i){t=t||[],n=n||[],o&&(e=o(i,e));var c=void 0;for(c=0;c<t.length;c+=1)if(t[c]===e)return n[c];var a=void 0;if("[object Array]"===s.call(e)){for(t.push(e),a=new Array(e.length),n.push(a),c=0;c<e.length;c+=1)a[c]=u(e[c],t,n,o,i);return t.pop(),n.pop(),a}if(e&&e.toJSON&&(e=e.toJSON()),"object"===(void 0===e?"undefined":r(e))&&null!==e){t.push(e),a={},n.push(a);var l=[],d=void 0;for(d in e)e.hasOwnProperty(d)&&l.push(d);for(l.sort(),c=0;c<l.length;c+=1)a[d=l[c]]=u(e[d],t,n,o,d);t.pop(),n.pop()}else a=e;return a}l.useLongestToken=!0,l.tokenize=a.lineDiff.tokenize,l.castInput=function(e){var t=this.options,n=t.undefinedReplacement,r=t.stringifyReplacer,o=void 0===r?function(e,t){return void 0===t?n:t}:r;return"string"==typeof e?e:JSON.stringify(u(e,null,null,o),o,"  ")},l.equals=function(e,t){return c.default.prototype.equals.call(l,e.replace(/,([\r\n])/g,"$1"),t.replace(/,([\r\n])/g,"$1"))}},function(e,t,n){"use strict";t.__esModule=!0,t.arrayDiff=void 0,t.diffArrays=function(e,t,n){return c.diff(e,t,n)};var r,o=n(1),i=(r=o)&&r.__esModule?r:{default:r};var c=t.arrayDiff=new i.default;c.tokenize=function(e){return e.slice()},c.join=c.removeEmpty=function(e){return e}},function(e,t,n){"use strict";t.__esModule=!0,t.applyPatch=a,t.applyPatches=function(e,t){"string"==typeof e&&(e=(0,o.parsePatch)(e));var n=0;!function r(){var o=e[n++];if(!o)return t.complete();t.loadFile(o,function(e,n){if(e)return t.complete(e);var i=a(n,o,t);t.patched(o,i,function(e){if(e)return t.complete(e);r()})})}()};var r,o=n(11),i=n(12),c=(r=i)&&r.__esModule?r:{default:r};function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof t&&(t=(0,o.parsePatch)(t)),Array.isArray(t)){if(t.length>1)throw new Error("applyPatch only works with a single input.");t=t[0]}var r=e.split(/\r\n|[\n\v\f\r\x85]/),i=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],a=t.hunks,s=n.compareLine||function(e,t,n,r){return t===r},l=0,u=n.fuzzFactor||0,d=0,p=0,b=void 0,f=void 0;function h(e,t){for(var n=0;n<e.lines.length;n++){var o=e.lines[n],i=o.length>0?o[0]:" ",c=o.length>0?o.substr(1):o;if(" "===i||"-"===i){if(!s(t+1,r[t],i,c)&&++l>u)return!1;t++}}return!0}for(var m=0;m<a.length;m++){for(var v=a[m],O=r.length-v.oldLines,g=0,j=p+v.oldStart-1,y=(0,c.default)(j,d,O);void 0!==g;g=y())if(h(v,j+g)){v.offset=p+=g;break}if(void 0===g)return!1;d=v.offset+v.oldStart+v.oldLines}for(var k=0,_=0;_<a.length;_++){var E=a[_],S=E.oldStart+E.offset+k-1;k+=E.newLines-E.oldLines,S<0&&(S=0);for(var C=0;C<E.lines.length;C++){var w=E.lines[C],T=w.length>0?w[0]:" ",P=w.length>0?w.substr(1):w,I=E.linedelimiters[C];if(" "===T)S++;else if("-"===T)r.splice(S,1),i.splice(S,1);else if("+"===T)r.splice(S,0,P),i.splice(S,0,I),S++;else if("\\"===T){var B=E.lines[C-1]?E.lines[C-1][0]:null;"+"===B?b=!0:"-"===B&&(f=!0)}}}if(b)for(;!r[r.length-1];)r.pop(),i.pop();else f&&(r.push(""),i.push("\n"));for(var x=0;x<r.length-1;x++)r[x]=r[x]+i[x];return r.join("")}},function(e,t){"use strict";t.__esModule=!0,t.parsePatch=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.split(/\r\n|[\n\v\f\r\x85]/),r=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],o=[],i=0;function c(){var e={};for(o.push(e);i<n.length;){var r=n[i];if(/^(\-\-\-|\+\+\+|@@)\s/.test(r))break;var c=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(r);c&&(e.index=c[1]),i++}for(a(e),a(e),e.hunks=[];i<n.length;){var l=n[i];if(/^(Index:|diff|\-\-\-|\+\+\+)\s/.test(l))break;if(/^@@/.test(l))e.hunks.push(s());else{if(l&&t.strict)throw new Error("Unknown line "+(i+1)+" "+JSON.stringify(l));i++}}}function a(e){var t=/^(---|\+\+\+)\s+(.*)$/.exec(n[i]);if(t){var r="---"===t[1]?"old":"new",o=t[2].split("\t",2),c=o[0].replace(/\\\\/g,"\\");/^".*"$/.test(c)&&(c=c.substr(1,c.length-2)),e[r+"FileName"]=c,e[r+"Header"]=(o[1]||"").trim(),i++}}function s(){for(var e=i,o=n[i++],c=o.split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),a={oldStart:+c[1],oldLines:+c[2]||1,newStart:+c[3],newLines:+c[4]||1,lines:[],linedelimiters:[]},s=0,l=0;i<n.length&&!(0===n[i].indexOf("--- ")&&i+2<n.length&&0===n[i+1].indexOf("+++ ")&&0===n[i+2].indexOf("@@"));i++){var u=0==n[i].length&&i!=n.length-1?" ":n[i][0];if("+"!==u&&"-"!==u&&" "!==u&&"\\"!==u)break;a.lines.push(n[i]),a.linedelimiters.push(r[i]||"\n"),"+"===u?s++:"-"===u?l++:" "===u&&(s++,l++)}if(s||1!==a.newLines||(a.newLines=0),l||1!==a.oldLines||(a.oldLines=0),t.strict){if(s!==a.newLines)throw new Error("Added line count did not match for hunk at line "+(e+1));if(l!==a.oldLines)throw new Error("Removed line count did not match for hunk at line "+(e+1))}return a}for(;i<n.length;)c();return o}},function(e,t){"use strict";t.__esModule=!0,t.default=function(e,t,n){var r=!0,o=!1,i=!1,c=1;return function a(){if(r&&!i){if(o?c++:r=!1,e+c<=n)return c;i=!0}if(!o)return i||(r=!0),t<=e-c?-c++:(o=!0,a())}}},function(e,t,n){"use strict";t.__esModule=!0,t.calcLineCount=a,t.merge=function(e,t,n){e=s(e,n),t=s(t,n);var r={};(e.index||t.index)&&(r.index=e.index||t.index);(e.newFileName||t.newFileName)&&(l(e)?l(t)?(r.oldFileName=u(r,e.oldFileName,t.oldFileName),r.newFileName=u(r,e.newFileName,t.newFileName),r.oldHeader=u(r,e.oldHeader,t.oldHeader),r.newHeader=u(r,e.newHeader,t.newHeader)):(r.oldFileName=e.oldFileName,r.newFileName=e.newFileName,r.oldHeader=e.oldHeader,r.newHeader=e.newHeader):(r.oldFileName=t.oldFileName||e.oldFileName,r.newFileName=t.newFileName||e.newFileName,r.oldHeader=t.oldHeader||e.oldHeader,r.newHeader=t.newHeader||e.newHeader));r.hunks=[];var o=0,i=0,c=0,a=0;for(;o<e.hunks.length||i<t.hunks.length;){var f=e.hunks[o]||{oldStart:1/0},h=t.hunks[i]||{oldStart:1/0};if(d(f,h))r.hunks.push(p(f,c)),o++,a+=f.newLines-f.oldLines;else if(d(h,f))r.hunks.push(p(h,a)),i++,c+=h.newLines-h.oldLines;else{var m={oldStart:Math.min(f.oldStart,h.oldStart),oldLines:0,newStart:Math.min(f.newStart+c,h.oldStart+a),newLines:0,lines:[]};b(m,f.oldStart,f.lines,h.oldStart,h.lines),i++,o++,r.hunks.push(m)}}return r};var r=n(14),o=n(11),i=n(15);function c(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function a(e){var t=function e(t){var n=0;var r=0;t.forEach(function(t){if("string"!=typeof t){var o=e(t.mine),i=e(t.theirs);void 0!==n&&(o.oldLines===i.oldLines?n+=o.oldLines:n=void 0),void 0!==r&&(o.newLines===i.newLines?r+=o.newLines:r=void 0)}else void 0===r||"+"!==t[0]&&" "!==t[0]||r++,void 0===n||"-"!==t[0]&&" "!==t[0]||n++});return{oldLines:n,newLines:r}}(e.lines),n=t.oldLines,r=t.newLines;void 0!==n?e.oldLines=n:delete e.oldLines,void 0!==r?e.newLines=r:delete e.newLines}function s(e,t){if("string"==typeof e){if(/^@@/m.test(e)||/^Index:/m.test(e))return(0,o.parsePatch)(e)[0];if(!t)throw new Error("Must provide a base reference or pass in a patch");return(0,r.structuredPatch)(void 0,void 0,t,e)}return e}function l(e){return e.newFileName&&e.newFileName!==e.oldFileName}function u(e,t,n){return t===n?t:(e.conflict=!0,{mine:t,theirs:n})}function d(e,t){return e.oldStart<t.oldStart&&e.oldStart+e.oldLines<t.oldStart}function p(e,t){return{oldStart:e.oldStart,oldLines:e.oldLines,newStart:e.newStart+t,newLines:e.newLines,lines:e.lines}}function b(e,t,n,r,o){var i={offset:t,lines:n,index:0},s={offset:r,lines:o,index:0};for(v(e,i,s),v(e,s,i);i.index<i.lines.length&&s.index<s.lines.length;){var l=i.lines[i.index],u=s.lines[s.index];if("-"!==l[0]&&"+"!==l[0]||"-"!==u[0]&&"+"!==u[0])if("+"===l[0]&&" "===u[0]){var d;(d=e.lines).push.apply(d,c(g(i)))}else if("+"===u[0]&&" "===l[0]){var p;(p=e.lines).push.apply(p,c(g(s)))}else"-"===l[0]&&" "===u[0]?h(e,i,s):"-"===u[0]&&" "===l[0]?h(e,s,i,!0):l===u?(e.lines.push(l),i.index++,s.index++):m(e,g(i),g(s));else f(e,i,s)}O(e,i),O(e,s),a(e)}function f(e,t,n){var r=g(t),o=g(n);if(j(r)&&j(o)){var a,s;if((0,i.arrayStartsWith)(r,o)&&y(n,r,r.length-o.length))return void(a=e.lines).push.apply(a,c(r));if((0,i.arrayStartsWith)(o,r)&&y(t,o,o.length-r.length))return void(s=e.lines).push.apply(s,c(o))}else if((0,i.arrayEqual)(r,o)){var l;return void(l=e.lines).push.apply(l,c(r))}m(e,r,o)}function h(e,t,n,r){var o,i=g(t),a=function(e,t){var n=[],r=[],o=0,i=!1,c=!1;for(;o<t.length&&e.index<e.lines.length;){var a=e.lines[e.index],s=t[o];if("+"===s[0])break;if(i=i||" "!==a[0],r.push(s),o++,"+"===a[0])for(c=!0;"+"===a[0];)n.push(a),a=e.lines[++e.index];s.substr(1)===a.substr(1)?(n.push(a),e.index++):c=!0}"+"===(t[o]||"")[0]&&i&&(c=!0);if(c)return n;for(;o<t.length;)r.push(t[o++]);return{merged:r,changes:n}}(n,i);a.merged?(o=e.lines).push.apply(o,c(a.merged)):m(e,r?a:i,r?i:a)}function m(e,t,n){e.conflict=!0,e.lines.push({conflict:!0,mine:t,theirs:n})}function v(e,t,n){for(;t.offset<n.offset&&t.index<t.lines.length;){var r=t.lines[t.index++];e.lines.push(r),t.offset++}}function O(e,t){for(;t.index<t.lines.length;){var n=t.lines[t.index++];e.lines.push(n)}}function g(e){for(var t=[],n=e.lines[e.index][0];e.index<e.lines.length;){var r=e.lines[e.index];if("-"===n&&"+"===r[0]&&(n="+"),n!==r[0])break;t.push(r),e.index++}return t}function j(e){return e.reduce(function(e,t){return e&&"-"===t[0]},!0)}function y(e,t,n){for(var r=0;r<n;r++){var o=t[t.length-n+r].substr(1);if(e.lines[e.index+r]!==" "+o)return!1}return e.index+=n,!0}},function(e,t,n){"use strict";t.__esModule=!0,t.structuredPatch=i,t.createTwoFilesPatch=c,t.createPatch=function(e,t,n,r,o,i){return c(e,e,t,n,r,o,i)};var r=n(5);function o(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function i(e,t,n,i,c,a,s){s||(s={}),void 0===s.context&&(s.context=4);var l=(0,r.diffLines)(n,i,s);function u(e){return e.map(function(e){return" "+e})}l.push({value:"",lines:[]});for(var d=[],p=0,b=0,f=[],h=1,m=1,v=function(e){var t=l[e],r=t.lines||t.value.replace(/\n$/,"").split("\n");if(t.lines=r,t.added||t.removed){var c;if(!p){var a=l[e-1];p=h,b=m,a&&(f=s.context>0?u(a.lines.slice(-s.context)):[],p-=f.length,b-=f.length)}(c=f).push.apply(c,o(r.map(function(e){return(t.added?"+":"-")+e}))),t.added?m+=r.length:h+=r.length}else{if(p)if(r.length<=2*s.context&&e<l.length-2){var v;(v=f).push.apply(v,o(u(r)))}else{var O,g=Math.min(r.length,s.context);(O=f).push.apply(O,o(u(r.slice(0,g))));var j={oldStart:p,oldLines:h-p+g,newStart:b,newLines:m-b+g,lines:f};if(e>=l.length-2&&r.length<=s.context){var y=/\n$/.test(n),k=/\n$/.test(i);0!=r.length||y?y&&k||f.push("\\ No newline at end of file"):f.splice(j.oldLines,0,"\\ No newline at end of file")}d.push(j),p=0,b=0,f=[]}h+=r.length,m+=r.length}},O=0;O<l.length;O++)v(O);return{oldFileName:e,newFileName:t,oldHeader:c,newHeader:a,hunks:d}}function c(e,t,n,r,o,c,a){var s=i(e,t,n,r,o,c,a),l=[];e==t&&l.push("Index: "+e),l.push("==================================================================="),l.push("--- "+s.oldFileName+(void 0===s.oldHeader?"":"\t"+s.oldHeader)),l.push("+++ "+s.newFileName+(void 0===s.newHeader?"":"\t"+s.newHeader));for(var u=0;u<s.hunks.length;u++){var d=s.hunks[u];l.push("@@ -"+d.oldStart+","+d.oldLines+" +"+d.newStart+","+d.newLines+" @@"),l.push.apply(l,d.lines)}return l.join("\n")+"\n"}},function(e,t){"use strict";function n(e,t){if(t.length>e.length)return!1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}t.__esModule=!0,t.arrayEqual=function(e,t){if(e.length!==t.length)return!1;return n(e,t)},t.arrayStartsWith=n},function(e,t){"use strict";t.__esModule=!0,t.convertChangesToDMP=function(e){for(var t=[],n=void 0,r=void 0,o=0;o<e.length;o++)n=e[o],r=n.added?1:n.removed?-1:0,t.push([r,n.value]);return t}},function(e,t){"use strict";function n(e){var t=e;return t=(t=(t=(t=t.replace(/&/g,"&amp;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;")).replace(/"/g,"&quot;")}t.__esModule=!0,t.convertChangesToXML=function(e){for(var t=[],r=0;r<e.length;r++){var o=e[r];o.added?t.push("<ins>"):o.removed&&t.push("<del>"),t.push(n(o.value)),o.added?t.push("</ins>"):o.removed&&t.push("</del>")}return t.join("")}}])},e.exports=r()},function(e,t){!function(){e.exports=this.tinymce}()},function(e,t){var n=e.exports=function(e){return new r(e)};function r(e){this.value=e}function o(e,t,n){var r=[],o=[],a=!0;return function e(d){var p=n?i(d):d,b={},f=!0,h={node:p,node_:d,path:[].concat(r),parent:o[o.length-1],parents:o,key:r.slice(-1)[0],isRoot:0===r.length,level:r.length,circular:null,update:function(e,t){h.isRoot||(h.parent.node[h.key]=e),h.node=e,t&&(f=!1)},delete:function(e){delete h.parent.node[h.key],e&&(f=!1)},remove:function(e){s(h.parent.node)?h.parent.node.splice(h.key,1):delete h.parent.node[h.key],e&&(f=!1)},keys:null,before:function(e){b.before=e},after:function(e){b.after=e},pre:function(e){b.pre=e},post:function(e){b.post=e},stop:function(){a=!1},block:function(){f=!1}};if(!a)return h;function m(){if("object"==typeof h.node&&null!==h.node){h.keys&&h.node_===h.node||(h.keys=c(h.node)),h.isLeaf=0==h.keys.length;for(var e=0;e<o.length;e++)if(o[e].node_===d){h.circular=o[e];break}}else h.isLeaf=!0,h.keys=null;h.notLeaf=!h.isLeaf,h.notRoot=!h.isRoot}m();var v=t.call(h,h.node);return void 0!==v&&h.update&&h.update(v),b.before&&b.before.call(h,h.node),f?("object"!=typeof h.node||null===h.node||h.circular||(o.push(h),m(),l(h.keys,function(t,o){r.push(t),b.pre&&b.pre.call(h,h.node[t],t);var i=e(h.node[t]);n&&u.call(h.node,t)&&(h.node[t]=i.node),i.isLast=o==h.keys.length-1,i.isFirst=0==o,b.post&&b.post.call(h,i),r.pop()}),o.pop()),b.after&&b.after.call(h,h.node),h):h}(e).node}function i(e){if("object"==typeof e&&null!==e){var t;if(s(e))t=[];else if("[object Date]"===a(e))t=new Date(e.getTime?e.getTime():e);else if(function(e){return"[object RegExp]"===a(e)}(e))t=new RegExp(e);else if(function(e){return"[object Error]"===a(e)}(e))t={message:e.message};else if(function(e){return"[object Boolean]"===a(e)}(e))t=new Boolean(e);else if(function(e){return"[object Number]"===a(e)}(e))t=new Number(e);else if(function(e){return"[object String]"===a(e)}(e))t=new String(e);else if(Object.create&&Object.getPrototypeOf)t=Object.create(Object.getPrototypeOf(e));else if(e.constructor===Object)t={};else{var n=e.constructor&&e.constructor.prototype||e.__proto__||{},r=function(){};r.prototype=n,t=new r}return l(c(e),function(n){t[n]=e[n]}),t}return e}r.prototype.get=function(e){for(var t=this.value,n=0;n<e.length;n++){var r=e[n];if(!t||!u.call(t,r)){t=void 0;break}t=t[r]}return t},r.prototype.has=function(e){for(var t=this.value,n=0;n<e.length;n++){var r=e[n];if(!t||!u.call(t,r))return!1;t=t[r]}return!0},r.prototype.set=function(e,t){for(var n=this.value,r=0;r<e.length-1;r++){var o=e[r];u.call(n,o)||(n[o]={}),n=n[o]}return n[e[r]]=t,t},r.prototype.map=function(e){return o(this.value,e,!0)},r.prototype.forEach=function(e){return this.value=o(this.value,e,!1),this.value},r.prototype.reduce=function(e,t){var n=1===arguments.length,r=n?this.value:t;return this.forEach(function(t){this.isRoot&&n||(r=e.call(this,r,t))}),r},r.prototype.paths=function(){var e=[];return this.forEach(function(t){e.push(this.path)}),e},r.prototype.nodes=function(){var e=[];return this.forEach(function(t){e.push(this.node)}),e},r.prototype.clone=function(){var e=[],t=[];return function n(r){for(var o=0;o<e.length;o++)if(e[o]===r)return t[o];if("object"==typeof r&&null!==r){var a=i(r);return e.push(r),t.push(a),l(c(r),function(e){a[e]=n(r[e])}),e.pop(),t.pop(),a}return r}(this.value)};var c=Object.keys||function(e){var t=[];for(var n in e)t.push(n);return t};function a(e){return Object.prototype.toString.call(e)}var s=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},l=function(e,t){if(e.forEach)return e.forEach(t);for(var n=0;n<e.length;n++)t(e[n],n,e)};l(c(r.prototype),function(e){n[e]=function(t){var n=[].slice.call(arguments,1),o=new r(t);return o[e].apply(o,n)}});var u=Object.hasOwnProperty||function(e,t){return t in e}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};var o="BEGIN",i="COMMIT",c="REVERT",a=[];function s(e,t){return e.optimist&&e.optimist.id===t}function l(e,t){if(!e||"object"!=typeof e||Array.isArray(e))throw new TypeError('Error while handling "'+t.type+'": Optimist requires that state is always a plain object.')}function u(e){if(e){var t=e.optimist;return{optimist:void 0===t?a:t,innerState:function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(e,["optimist"])}}return{optimist:a,innerState:e}}e.exports=function(e){function t(t,n,o){return t.length&&(t=t.concat([{action:o}])),l(n=e(n,o),o),r({optimist:t},n)}return function(n,a){if(a.optimist)switch(a.optimist.type){case o:return function(t,n){var o=u(t),i=o.optimist,c=o.innerState;return i=i.concat([{beforeState:c,action:n}]),l(c=e(c,n),n),r({optimist:i},c)}(n,a);case i:return function(e,n){var r=u(e),o=r.optimist,i=r.innerState,c=[],a=!1,l=!1;o.forEach(function(e){a?e.beforeState&&s(e.action,n.optimist.id)?(l=!0,c.push({action:e.action})):c.push(e):e.beforeState&&!s(e.action,n.optimist.id)?(a=!0,c.push(e)):e.beforeState&&s(e.action,n.optimist.id)&&(l=!0)}),l||console.error('Cannot commit transaction with id "'+n.optimist.id+'" because it does not exist');return t(o=c,i,n)}(n,a);case c:return function(n,r){var o=u(n),i=o.optimist,c=o.innerState,a=[],d=!1,p=!1,b=c;i.forEach(function(t){t.beforeState&&s(t.action,r.optimist.id)&&(b=t.beforeState,p=!0),s(t.action,r.optimist.id)||(t.beforeState&&(d=!0),d&&(p&&t.beforeState?a.push({beforeState:b,action:t.action}):a.push(t)),p&&(b=e(b,t.action),l(c,r)))}),p||console.error('Cannot revert transaction with id "'+r.optimist.id+'" because it does not exist');return t(i=a,b,r)}(n,a)}var d=u(n),p=d.optimist,b=d.innerState;if(n&&!p.length){var f=e(b,a);return f===b?n:(l(f,a),r({optimist:p},f))}return t(p,b,a)}},e.exports.BEGIN=o,e.exports.COMMIT=i,e.exports.REVERT=c},function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__assign||Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},c=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&(n[r[o]]=e[r[o]])}return n};t.__esModule=!0;var a=n(26),s=n(29),l=n(272),u=n(273),d=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={lineHeight:null},t.dispatchEvent=function(e){var n=document.createEvent("Event");n.initEvent(e,!0,!1),t.textarea.dispatchEvent(n)},t.updateLineHeight=function(){t.setState({lineHeight:u(t.textarea)})},t.onChange=function(e){var n=t.props.onChange;t.currentValue=e.currentTarget.value,n&&n(e)},t.saveDOMNodeRef=function(e){var n=t.props.innerRef;n&&n(e),t.textarea=e},t.getLocals=function(){var e=t,n=e.props,r=(n.onResize,n.maxRows),o=(n.onChange,n.style),a=(n.innerRef,c(n,["onResize","maxRows","onChange","style","innerRef"])),s=e.state.lineHeight,l=e.saveDOMNodeRef,u=r&&s?s*r:null;return i({},a,{saveDOMNodeRef:l,style:u?i({},o,{maxHeight:u}):o,onChange:t.onChange})},t}return o(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props,n=t.onResize;"number"==typeof t.maxRows&&this.updateLineHeight(),setTimeout(function(){return l(e.textarea)}),n&&this.textarea.addEventListener("autosize:resized",n)},t.prototype.componentWillUnmount=function(){var e=this.props.onResize;e&&this.textarea.removeEventListener("autosize:resized",e),this.dispatchEvent("autosize:destroy")},t.prototype.render=function(){var e=this.getLocals(),t=e.children,n=e.saveDOMNodeRef,r=c(e,["children","saveDOMNodeRef"]);return a.createElement("textarea",i({},r,{ref:n}),t)},t.prototype.componentDidUpdate=function(e){this.props.value===this.currentValue&&this.props.rows===e.rows||this.dispatchEvent("autosize:update")},t.defaultProps={rows:1},t.propTypes={rows:s.number,maxRows:s.number,onResize:s.func,innerRef:s.func},t}(a.Component);t.default=d},function(e,t,n){var r,o,i;
/*!
	autosize 4.0.2
	license: MIT
	http://www.jacklmoore.com/autosize
*/o=[e,t],void 0===(i="function"==typeof(r=function(e,t){"use strict";var n,r,o="function"==typeof Map?new Map:(n=[],r=[],{has:function(e){return n.indexOf(e)>-1},get:function(e){return r[n.indexOf(e)]},set:function(e,t){-1===n.indexOf(e)&&(n.push(e),r.push(t))},delete:function(e){var t=n.indexOf(e);t>-1&&(n.splice(t,1),r.splice(t,1))}}),i=function(e){return new Event(e,{bubbles:!0})};try{new Event("test")}catch(e){i=function(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!1),t}}function c(e){if(e&&e.nodeName&&"TEXTAREA"===e.nodeName&&!o.has(e)){var t=null,n=null,r=null,c=function(){e.clientWidth!==n&&d()},a=function(t){window.removeEventListener("resize",c,!1),e.removeEventListener("input",d,!1),e.removeEventListener("keyup",d,!1),e.removeEventListener("autosize:destroy",a,!1),e.removeEventListener("autosize:update",d,!1),Object.keys(t).forEach(function(n){e.style[n]=t[n]}),o.delete(e)}.bind(e,{height:e.style.height,resize:e.style.resize,overflowY:e.style.overflowY,overflowX:e.style.overflowX,wordWrap:e.style.wordWrap});e.addEventListener("autosize:destroy",a,!1),"onpropertychange"in e&&"oninput"in e&&e.addEventListener("keyup",d,!1),window.addEventListener("resize",c,!1),e.addEventListener("input",d,!1),e.addEventListener("autosize:update",d,!1),e.style.overflowX="hidden",e.style.wordWrap="break-word",o.set(e,{destroy:a,update:d}),"vertical"===(s=window.getComputedStyle(e,null)).resize?e.style.resize="none":"both"===s.resize&&(e.style.resize="horizontal"),t="content-box"===s.boxSizing?-(parseFloat(s.paddingTop)+parseFloat(s.paddingBottom)):parseFloat(s.borderTopWidth)+parseFloat(s.borderBottomWidth),isNaN(t)&&(t=0),d()}var s;function l(t){var n=e.style.width;e.style.width="0px",e.offsetWidth,e.style.width=n,e.style.overflowY=t}function u(){if(0!==e.scrollHeight){var r=function(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push({node:e.parentNode,scrollTop:e.parentNode.scrollTop}),e=e.parentNode;return t}(e),o=document.documentElement&&document.documentElement.scrollTop;e.style.height="",e.style.height=e.scrollHeight+t+"px",n=e.clientWidth,r.forEach(function(e){e.node.scrollTop=e.scrollTop}),o&&(document.documentElement.scrollTop=o)}}function d(){u();var t=Math.round(parseFloat(e.style.height)),n=window.getComputedStyle(e,null),o="content-box"===n.boxSizing?Math.round(parseFloat(n.height)):e.offsetHeight;if(o<t?"hidden"===n.overflowY&&(l("scroll"),u(),o="content-box"===n.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight):"hidden"!==n.overflowY&&(l("hidden"),u(),o="content-box"===n.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight),r!==o){r=o;var c=i("autosize:resized");try{e.dispatchEvent(c)}catch(e){}}}}function a(e){var t=o.get(e);t&&t.destroy()}function s(e){var t=o.get(e);t&&t.update()}var l=null;"undefined"==typeof window||"function"!=typeof window.getComputedStyle?((l=function(e){return e}).destroy=function(e){return e},l.update=function(e){return e}):((l=function(e,t){return e&&Array.prototype.forEach.call(e.length?e:[e],function(e){return c(e)}),e}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],a),e},l.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],s),e}),t.default=l,e.exports=t.default})?r.apply(t,o):r)||(e.exports=i)},function(e,t,n){var r=n(274);e.exports=function(e){var t=r(e,"line-height"),n=parseFloat(t,10);if(t===n+""){var o=e.style.lineHeight;e.style.lineHeight=t+"em",t=r(e,"line-height"),n=parseFloat(t,10),o?e.style.lineHeight=o:delete e.style.lineHeight}if(-1!==t.indexOf("pt")?(n*=4,n/=3):-1!==t.indexOf("mm")?(n*=96,n/=25.4):-1!==t.indexOf("cm")?(n*=96,n/=2.54):-1!==t.indexOf("in")?n*=96:-1!==t.indexOf("pc")&&(n*=16),n=Math.round(n),"normal"===t){var i=e.nodeName,c=document.createElement(i);c.innerHTML="&nbsp;","TEXTAREA"===i.toUpperCase()&&c.setAttribute("rows","1");var a=r(e,"font-size");c.style.fontSize=a,c.style.padding="0px",c.style.border="0px";var s=document.body;s.appendChild(c),n=c.offsetHeight,s.removeChild(c)}return n}},function(e,t){e.exports=function(e,t,n){return((n=window.getComputedStyle)?n(e):e.currentStyle)[t.replace(/-(\w)/gi,function(e,t){return t.toUpperCase()})]}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.r(t);var r={};n.r(r),n.d(r,"setupEditor",function(){return G}),n.d(r,"resetPost",function(){return $}),n.d(r,"resetAutosave",function(){return Y}),n.d(r,"updatePost",function(){return Q}),n.d(r,"setupEditorState",function(){return X}),n.d(r,"resetBlocks",function(){return Z}),n.d(r,"receiveBlocks",function(){return J}),n.d(r,"updateBlockAttributes",function(){return ee}),n.d(r,"updateBlock",function(){return te}),n.d(r,"selectBlock",function(){return ne}),n.d(r,"startMultiSelect",function(){return re}),n.d(r,"stopMultiSelect",function(){return oe}),n.d(r,"multiSelect",function(){return ie}),n.d(r,"clearSelectedBlock",function(){return ce}),n.d(r,"toggleSelection",function(){return ae}),n.d(r,"replaceBlocks",function(){return se}),n.d(r,"replaceBlock",function(){return le}),n.d(r,"moveBlocksDown",function(){return de}),n.d(r,"moveBlocksUp",function(){return pe}),n.d(r,"moveBlockToPosition",function(){return be}),n.d(r,"insertBlock",function(){return fe}),n.d(r,"insertBlocks",function(){return he}),n.d(r,"showInsertionPoint",function(){return me}),n.d(r,"hideInsertionPoint",function(){return ve}),n.d(r,"setTemplateValidity",function(){return Oe}),n.d(r,"synchronizeTemplate",function(){return ge}),n.d(r,"editPost",function(){return je}),n.d(r,"savePost",function(){return ye}),n.d(r,"refreshPost",function(){return ke}),n.d(r,"trashPost",function(){return _e}),n.d(r,"mergeBlocks",function(){return Ee}),n.d(r,"autosave",function(){return Se}),n.d(r,"redo",function(){return Ce}),n.d(r,"undo",function(){return we}),n.d(r,"createUndoLevel",function(){return Te}),n.d(r,"removeBlocks",function(){return Pe}),n.d(r,"removeBlock",function(){return Ie}),n.d(r,"toggleBlockMode",function(){return Be}),n.d(r,"startTyping",function(){return xe}),n.d(r,"stopTyping",function(){return Le}),n.d(r,"enterFormattedText",function(){return Ae}),n.d(r,"exitFormattedText",function(){return Ne}),n.d(r,"updatePostLock",function(){return Re}),n.d(r,"__experimentalFetchReusableBlocks",function(){return De}),n.d(r,"__experimentalReceiveReusableBlocks",function(){return Fe}),n.d(r,"__experimentalSaveReusableBlock",function(){return Me}),n.d(r,"__experimentalDeleteReusableBlock",function(){return Ue}),n.d(r,"__experimentalUpdateReusableBlockTitle",function(){return He}),n.d(r,"__experimentalConvertBlockToStatic",function(){return Ve}),n.d(r,"__experimentalConvertBlockToReusable",function(){return Ke}),n.d(r,"insertDefaultBlock",function(){return ze}),n.d(r,"updateBlockListSettings",function(){return We}),n.d(r,"updateEditorSettings",function(){return qe}),n.d(r,"enablePublishSidebar",function(){return Ge}),n.d(r,"disablePublishSidebar",function(){return $e}),n.d(r,"lockPostSaving",function(){return Ye}),n.d(r,"unlockPostSaving",function(){return Qe});var o={};n.r(o),n.d(o,"POST_UPDATE_TRANSACTION_ID",function(){return et}),n.d(o,"INSERTER_UTILITY_HIGH",function(){return nt}),n.d(o,"INSERTER_UTILITY_MEDIUM",function(){return rt}),n.d(o,"INSERTER_UTILITY_LOW",function(){return ot}),n.d(o,"INSERTER_UTILITY_NONE",function(){return it}),n.d(o,"hasEditorUndo",function(){return st}),n.d(o,"hasEditorRedo",function(){return lt}),n.d(o,"isEditedPostNew",function(){return ut}),n.d(o,"hasChangedContent",function(){return dt}),n.d(o,"isEditedPostDirty",function(){return pt}),n.d(o,"isCleanNewPost",function(){return bt}),n.d(o,"getCurrentPost",function(){return ft}),n.d(o,"getCurrentPostType",function(){return ht}),n.d(o,"getCurrentPostId",function(){return mt}),n.d(o,"getCurrentPostRevisionsCount",function(){return vt}),n.d(o,"getCurrentPostLastRevisionId",function(){return Ot}),n.d(o,"getPostEdits",function(){return gt}),n.d(o,"getReferenceByDistinctEdits",function(){return jt}),n.d(o,"getCurrentPostAttribute",function(){return yt}),n.d(o,"getEditedPostAttribute",function(){return kt}),n.d(o,"getAutosaveAttribute",function(){return _t}),n.d(o,"getEditedPostVisibility",function(){return Et}),n.d(o,"isCurrentPostPending",function(){return St}),n.d(o,"isCurrentPostPublished",function(){return Ct}),n.d(o,"isCurrentPostScheduled",function(){return wt}),n.d(o,"isEditedPostPublishable",function(){return Tt}),n.d(o,"isEditedPostSaveable",function(){return Pt}),n.d(o,"isEditedPostEmpty",function(){return It}),n.d(o,"isEditedPostAutosaveable",function(){return Bt}),n.d(o,"getAutosave",function(){return xt}),n.d(o,"hasAutosave",function(){return Lt}),n.d(o,"isEditedPostBeingScheduled",function(){return At}),n.d(o,"isEditedPostDateFloating",function(){return Nt}),n.d(o,"getBlockDependantsCacheBust",function(){return Rt}),n.d(o,"getBlockName",function(){return Dt}),n.d(o,"isBlockValid",function(){return Ft}),n.d(o,"getBlockAttributes",function(){return Mt}),n.d(o,"getBlock",function(){return Ut}),n.d(o,"__unstableGetBlockWithoutInnerBlocks",function(){return Ht}),n.d(o,"getBlocks",function(){return Vt}),n.d(o,"getClientIdsOfDescendants",function(){return Kt}),n.d(o,"getClientIdsWithDescendants",function(){return zt}),n.d(o,"getGlobalBlockCount",function(){return Wt}),n.d(o,"getBlocksByClientId",function(){return qt}),n.d(o,"getBlockCount",function(){return Gt}),n.d(o,"getBlockSelectionStart",function(){return $t}),n.d(o,"getBlockSelectionEnd",function(){return Yt}),n.d(o,"getSelectedBlockCount",function(){return Qt}),n.d(o,"hasSelectedBlock",function(){return Xt}),n.d(o,"getSelectedBlockClientId",function(){return Zt}),n.d(o,"getSelectedBlock",function(){return Jt}),n.d(o,"getBlockRootClientId",function(){return en}),n.d(o,"getBlockHierarchyRootClientId",function(){return tn}),n.d(o,"getAdjacentBlockClientId",function(){return nn}),n.d(o,"getPreviousBlockClientId",function(){return rn}),n.d(o,"getNextBlockClientId",function(){return on}),n.d(o,"getSelectedBlocksInitialCaretPosition",function(){return cn}),n.d(o,"getMultiSelectedBlockClientIds",function(){return an}),n.d(o,"getMultiSelectedBlocks",function(){return sn}),n.d(o,"getFirstMultiSelectedBlockClientId",function(){return ln}),n.d(o,"getLastMultiSelectedBlockClientId",function(){return un}),n.d(o,"isFirstMultiSelectedBlock",function(){return pn}),n.d(o,"isBlockMultiSelected",function(){return bn}),n.d(o,"isAncestorMultiSelected",function(){return fn}),n.d(o,"getMultiSelectedBlocksStartClientId",function(){return hn}),n.d(o,"getMultiSelectedBlocksEndClientId",function(){return mn}),n.d(o,"getBlockOrder",function(){return vn}),n.d(o,"getBlockIndex",function(){return On}),n.d(o,"isBlockSelected",function(){return gn}),n.d(o,"hasSelectedInnerBlock",function(){return jn}),n.d(o,"isBlockWithinSelection",function(){return yn}),n.d(o,"hasMultiSelection",function(){return kn}),n.d(o,"isMultiSelecting",function(){return _n}),n.d(o,"isSelectionEnabled",function(){return En}),n.d(o,"getBlockMode",function(){return Sn}),n.d(o,"isTyping",function(){return Cn}),n.d(o,"isCaretWithinFormattedText",function(){return wn}),n.d(o,"getBlockInsertionPoint",function(){return Tn}),n.d(o,"isBlockInsertionPointVisible",function(){return Pn}),n.d(o,"isValidTemplate",function(){return In}),n.d(o,"getTemplate",function(){return Bn}),n.d(o,"getTemplateLock",function(){return xn}),n.d(o,"isSavingPost",function(){return Ln}),n.d(o,"didPostSaveRequestSucceed",function(){return An}),n.d(o,"didPostSaveRequestFail",function(){return Nn}),n.d(o,"isAutosavingPost",function(){return Rn}),n.d(o,"isPreviewingPost",function(){return Dn}),n.d(o,"getEditedPostPreviewLink",function(){return Fn}),n.d(o,"getSuggestedPostFormat",function(){return Mn}),n.d(o,"getBlocksForSerialization",function(){return Un}),n.d(o,"getEditedPostContent",function(){return Hn}),n.d(o,"canInsertBlockType",function(){return Kn}),n.d(o,"getInserterItems",function(){return Gn}),n.d(o,"hasInserterItems",function(){return $n}),n.d(o,"__experimentalGetReusableBlock",function(){return Yn}),n.d(o,"__experimentalIsSavingReusableBlock",function(){return Qn}),n.d(o,"__experimentalIsFetchingReusableBlock",function(){return Xn}),n.d(o,"__experimentalGetReusableBlocks",function(){return Zn}),n.d(o,"getStateBeforeOptimisticTransaction",function(){return Jn}),n.d(o,"isPublishingPost",function(){return er}),n.d(o,"isPermalinkEditable",function(){return tr}),n.d(o,"getPermalink",function(){return nr}),n.d(o,"getPermalinkParts",function(){return rr}),n.d(o,"inSomeHistory",function(){return or}),n.d(o,"getBlockListSettings",function(){return ir}),n.d(o,"getEditorSettings",function(){return cr}),n.d(o,"getTokenSettings",function(){return ar}),n.d(o,"isPostLocked",function(){return sr}),n.d(o,"isPostSavingLocked",function(){return lr}),n.d(o,"isPostLockTakeover",function(){return ur}),n.d(o,"getPostLockUser",function(){return dr}),n.d(o,"getActivePostLock",function(){return pr}),n.d(o,"canUserUseUnfilteredHTML",function(){return br}),n.d(o,"isPublishSidebarEnabled",function(){return fr});var i=n(11),c=(n(79),n(121),n(52)),a=n(20),s=n(37),l=n(5),u=n(25),d=n(8),p=n(21),b=n(19),f=n(15),h=n(28),m=n(53),v=n.n(m),O=n(2),g=n(24),j={resetTypes:[],ignoreTypes:[],shouldOverwriteState:function(){return!1}},y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(t){(e=Object(d.a)({},j,e)).shouldOverwriteState=Object(O.overSome)([e.shouldOverwriteState,function(t){return Object(O.includes)(e.ignoreTypes,t.type)}]);var n={past:[],present:t(void 0,{}),future:[],lastAction:null,shouldCreateUndoLevel:!1},r=e,o=r.resetTypes,i=void 0===o?[]:o,c=r.shouldOverwriteState,a=void 0===c?function(){return!1}:c;return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,r=arguments.length>1?arguments[1]:void 0,o=e.past,c=e.present,s=e.future,l=e.lastAction,u=e.shouldCreateUndoLevel,p=l;switch(r.type){case"UNDO":return o.length?{past:Object(O.dropRight)(o),present:Object(O.last)(o),future:[c].concat(Object(b.a)(s)),lastAction:null,shouldCreateUndoLevel:!1}:e;case"REDO":return s.length?{past:Object(b.a)(o).concat([c]),present:Object(O.first)(s),future:Object(O.drop)(s),lastAction:null,shouldCreateUndoLevel:!1}:e;case"CREATE_UNDO_LEVEL":return Object(d.a)({},e,{lastAction:null,shouldCreateUndoLevel:!0})}var f=t(c,r);if(Object(O.includes)(i,r.type))return{past:[],present:f,future:[],lastAction:null,shouldCreateUndoLevel:!1};if(c===f)return e;var h=o,m=p;return!u&&o.length&&a(r,p)||(h=Object(b.a)(o).concat([c]),m=r),{past:h,present:f,future:[],shouldCreateUndoLevel:!1,lastAction:m}}}},k=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(t){return function(n,r){var o=t(n,r),i=void 0===n||Object(O.includes)(e.resetTypes,r.type),c=n!==o;if(!c&&!i)return n;c&&void 0!==n||(o=Object(d.a)({},o));var a=Object(O.includes)(e.ignoreTypes,r.type);return o.isDirty=a?n.isDirty:!i&&c,o}}},_=n(1),E={insertUsage:{},isPublishSidebarEnabled:!0},S={alignWide:!1,colors:[{name:Object(_.__)("Pale pink"),slug:"pale-pink",color:"#f78da7"},{name:Object(_.__)("Vivid red"),slug:"vivid-red",color:"#cf2e2e"},{name:Object(_.__)("Luminous vivid orange"),slug:"luminous-vivid-orange",color:"#ff6900"},{name:Object(_.__)("Luminous vivid amber"),slug:"luminous-vivid-amber",color:"#fcb900"},{name:Object(_.__)("Light green cyan"),slug:"light-green-cyan",color:"#7bdcb5"},{name:Object(_.__)("Vivid green cyan"),slug:"vivid-green-cyan",color:"#00d084"},{name:Object(_.__)("Pale cyan blue"),slug:"pale-cyan-blue",color:"#8ed1fc"},{name:Object(_.__)("Vivid cyan blue"),slug:"vivid-cyan-blue",color:"#0693e3"},{name:Object(_.__)("Very light gray"),slug:"very-light-gray",color:"#eeeeee"},{name:Object(_.__)("Cyan bluish gray"),slug:"cyan-bluish-gray",color:"#abb8c3"},{name:Object(_.__)("Very dark gray"),slug:"very-dark-gray",color:"#313131"}],fontSizes:[{name:Object(_._x)("Small","font size name"),size:13,slug:"small"},{name:Object(_._x)("Normal","font size name"),size:16,slug:"normal"},{name:Object(_._x)("Medium","font size name"),size:20,slug:"medium"},{name:Object(_._x)("Large","font size name"),size:36,slug:"large"},{name:Object(_._x)("Huge","font size name"),size:48,slug:"huge"}],imageSizes:[{slug:"thumbnail",label:Object(_.__)("Thumbnail")},{slug:"medium",label:Object(_.__)("Medium")},{slug:"large",label:Object(_.__)("Large")},{slug:"full",label:Object(_.__)("Full Size")}],maxWidth:580,allowedBlockTypes:!0,maxUploadFileSize:0,allowedMimeTypes:null,richEditingEnabled:!0},C={};function w(e,t,n){return Object(b.a)(e.slice(0,n)).concat(Object(b.a)(Object(O.castArray)(t)),Object(b.a)(e.slice(n)))}function T(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=Object(b.a)(e);return o.splice(t,r),w(o,e.slice(t,t+r),n)}var P=new Set(["meta"]);function I(e){return e&&"object"===Object(h.a)(e)&&"raw"in e?e.raw:e}function B(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=Object(f.a)({},t,[]);return e.forEach(function(e){var r=e.clientId,o=e.innerBlocks;n[t].push(r),Object.assign(n,B(o,r))}),n}function x(e,t){for(var n={},r=Object(b.a)(e);r.length;){var o=r.shift(),i=o.innerBlocks,c=Object(p.a)(o,["innerBlocks"]);r.push.apply(r,Object(b.a)(i)),n[c.clientId]=t(c)}return n}function L(e){return x(e,function(e){return Object(O.omit)(e,"attributes")})}function A(e){return x(e,function(e){return e.attributes})}function N(e,t){return e===t?Object(d.a)({},e):t}function R(e,t){return Object(O.isEqual)(Object(O.keys)(e),Object(O.keys)(t))}function D(e,t){return"UPDATE_BLOCK_ATTRIBUTES"===e.type&&e.clientId===t.clientId&&R(e.attributes,t.attributes)}function F(e,t){return"EDIT_POST"===e.type&&R(e.edits,t.edits)}var M=Object(O.flow)([l.combineReducers,function(e){return function(t,n){if(t&&"REMOVE_BLOCKS"===n.type){for(var r=Object(b.a)(n.clientIds),o=0;o<r.length;o++)r.push.apply(r,Object(b.a)(t.blocks.order[r[o]]));n=Object(d.a)({},n,{clientIds:r})}return e(t,n)}},y({resetTypes:["SETUP_EDITOR_STATE"],ignoreTypes:["RECEIVE_BLOCKS","RESET_POST","UPDATE_POST"],shouldOverwriteState:function(e,t){return!(!t||e.type!==t.type)&&Object(O.overSome)([D,F])(e,t)}})])({edits:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"EDIT_POST":return Object(O.reduce)(t.edits,function(t,n,r){return n!==e[r]&&(t=N(e,t),P.has(r)?t[r]=Object(d.a)({},t[r],n):t[r]=n),t},e);case"RESET_BLOCKS":return"content"in e?Object(O.omit)(e,"content"):e;case"UPDATE_POST":case"RESET_POST":var n="UPDATE_POST"===t.type?function(e){return t.edits[e]}:function(e){return I(t.post[e])};return Object(O.reduce)(e,function(t,r,o){return Object(O.isEqual)(r,n(o))?(delete(t=N(e,t))[o],t):t},e)}return e},blocks:Object(O.flow)([l.combineReducers,function(e){return function(t,n){if(t&&"RESET_BLOCKS"===n.type){var r=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return Object(O.reduce)(t[n],function(n,r){return Object(b.a)(n).concat([r],Object(b.a)(e(t,r)))},[])}(t.order);return Object(d.a)({},t,{byClientId:Object(d.a)({},Object(O.omit)(t.byClientId,r),L(n.blocks)),attributes:Object(d.a)({},Object(O.omit)(t.attributes,r),A(n.blocks)),order:Object(d.a)({},Object(O.omit)(t.order,r),B(n.blocks))})}return e(t,n)}},function(e){return function(t,n){if(t&&"SAVE_REUSABLE_BLOCK_SUCCESS"===n.type){var r=n.id,o=n.updatedId;if(r===o)return t;(t=Object(d.a)({},t)).attributes=Object(O.mapValues)(t.attributes,function(e,n){return"core/block"===t.byClientId[n].name&&e.ref===r?Object(d.a)({},e,{ref:o}):e})}return e(t,n)}},k({resetTypes:["SETUP_EDITOR_STATE","REQUEST_POST_UPDATE_START"],ignoreTypes:["RECEIVE_BLOCKS","RESET_POST","UPDATE_POST"]})])({byClientId:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_EDITOR_STATE":return L(t.blocks);case"RECEIVE_BLOCKS":return Object(d.a)({},e,L(t.blocks));case"UPDATE_BLOCK":if(!e[t.clientId])return e;var n=Object(O.omit)(t.updates,"attributes");return Object(O.isEmpty)(n)?e:Object(d.a)({},e,Object(f.a)({},t.clientId,Object(d.a)({},e[t.clientId],n)));case"INSERT_BLOCKS":return Object(d.a)({},e,L(t.blocks));case"REPLACE_BLOCKS":return t.blocks?Object(d.a)({},Object(O.omit)(e,t.clientIds),L(t.blocks)):e;case"REMOVE_BLOCKS":return Object(O.omit)(e,t.clientIds)}return e},attributes:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_EDITOR_STATE":return A(t.blocks);case"RECEIVE_BLOCKS":return Object(d.a)({},e,A(t.blocks));case"UPDATE_BLOCK":return e[t.clientId]&&t.updates.attributes?Object(d.a)({},e,Object(f.a)({},t.clientId,Object(d.a)({},e[t.clientId],t.updates.attributes))):e;case"UPDATE_BLOCK_ATTRIBUTES":if(!e[t.clientId])return e;var n=Object(O.reduce)(t.attributes,function(n,r,o){return r!==n[o]&&((n=N(e[t.clientId],n))[o]=r),n},e[t.clientId]);return n===e[t.clientId]?e:Object(d.a)({},e,Object(f.a)({},t.clientId,n));case"INSERT_BLOCKS":return Object(d.a)({},e,A(t.blocks));case"REPLACE_BLOCKS":return t.blocks?Object(d.a)({},Object(O.omit)(e,t.clientIds),A(t.blocks)):e;case"REMOVE_BLOCKS":return Object(O.omit)(e,t.clientIds)}return e},order:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_EDITOR_STATE":return B(t.blocks);case"RECEIVE_BLOCKS":return Object(d.a)({},e,Object(O.omit)(B(t.blocks),""));case"INSERT_BLOCKS":var n=t.rootClientId,r=void 0===n?"":n,o=t.blocks,i=e[r]||[],c=B(o,r),a=t.index,s=void 0===a?i.length:a;return Object(d.a)({},e,c,Object(f.a)({},r,w(i,c[r],s)));case"MOVE_BLOCK_TO_POSITION":var l,u=t.fromRootClientId,p=void 0===u?"":u,h=t.toRootClientId,m=void 0===h?"":h,v=t.clientId,g=t.index,j=void 0===g?e[m].length:g;if(p===m){var y=e[m].indexOf(v);return Object(d.a)({},e,Object(f.a)({},m,T(e[m],y,j)))}return Object(d.a)({},e,(l={},Object(f.a)(l,p,Object(O.without)(e[p],v)),Object(f.a)(l,m,w(e[m],v,j)),l));case"MOVE_BLOCKS_UP":var k=t.clientIds,_=t.rootClientId,E=void 0===_?"":_,S=Object(O.first)(k),C=e[E];if(!C.length||S===Object(O.first)(C))return e;var P=C.indexOf(S);return Object(d.a)({},e,Object(f.a)({},E,T(C,P,P-1,k.length)));case"MOVE_BLOCKS_DOWN":var I=t.clientIds,x=t.rootClientId,L=void 0===x?"":x,A=Object(O.first)(I),N=Object(O.last)(I),R=e[L];if(!R.length||N===Object(O.last)(R))return e;var D=R.indexOf(A);return Object(d.a)({},e,Object(f.a)({},L,T(R,D,D+1,I.length)));case"REPLACE_BLOCKS":var F=t.blocks,M=t.clientIds;if(!F)return e;var U=B(F);return Object(O.flow)([function(e){return Object(O.omit)(e,M)},function(e){return Object(d.a)({},e,Object(O.omit)(U,""))},function(e){return Object(O.mapValues)(e,function(e){return Object(O.reduce)(e,function(e,t){return t===M[0]?Object(b.a)(e).concat(Object(b.a)(U[""])):(-1===M.indexOf(t)&&e.push(t),e)},[])})}])(e);case"REMOVE_BLOCKS":return Object(O.flow)([function(e){return Object(O.omit)(e,t.clientIds)},function(e){return Object(O.mapValues)(e,function(e){return O.without.apply(void 0,[e].concat(Object(b.a)(t.clientIds)))})}])(e)}return e}})});var U=Object(l.combineReducers)({data:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"RECEIVE_REUSABLE_BLOCKS":return Object(O.reduce)(t.results,function(t,n){var r=n.reusableBlock,o=r.id,i=r.title,c={clientId:n.parsedBlock.clientId,title:i};return Object(O.isEqual)(t[o],c)||((t=N(e,t))[o]=c),t},e);case"UPDATE_REUSABLE_BLOCK_TITLE":var n=t.id,r=t.title;return e[n]&&e[n].title!==r?Object(d.a)({},e,Object(f.a)({},n,Object(d.a)({},e[n],{title:r}))):e;case"SAVE_REUSABLE_BLOCK_SUCCESS":var o=t.id,i=t.updatedId;if(o===i)return e;var c=e[o];return Object(d.a)({},Object(O.omit)(e,o),Object(f.a)({},i,c));case"REMOVE_REUSABLE_BLOCK":var a=t.id;return Object(O.omit)(e,a)}return e},isFetching:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"FETCH_REUSABLE_BLOCKS":var n=t.id;return n?Object(d.a)({},e,Object(f.a)({},n,!0)):e;case"FETCH_REUSABLE_BLOCKS_SUCCESS":case"FETCH_REUSABLE_BLOCKS_FAILURE":var r=t.id;return Object(O.omit)(e,r)}return e},isSaving:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SAVE_REUSABLE_BLOCK":return Object(d.a)({},e,Object(f.a)({},t.id,!0));case"SAVE_REUSABLE_BLOCK_SUCCESS":case"SAVE_REUSABLE_BLOCK_FAILURE":var n=t.id;return Object(O.omit)(e,n)}return e}});var H=v()(Object(l.combineReducers)({editor:M,initialEdits:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:C,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_EDITOR":if(!t.edits)break;return t.edits;case"SETUP_EDITOR_STATE":return"content"in e?Object(O.omit)(e,"content"):e;case"UPDATE_POST":return Object(O.reduce)(t.edits,function(t,n,r){return t.hasOwnProperty(r)?(delete(t=N(e,t))[r],t):t},e);case"RESET_POST":return C}return e},currentPost:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_EDITOR_STATE":case"RESET_POST":case"UPDATE_POST":var n;if(t.post)n=t.post;else{if(!t.edits)return e;n=Object(d.a)({},e,t.edits)}return Object(O.mapValues)(n,I)}return e},isTyping:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];switch((arguments.length>1?arguments[1]:void 0).type){case"START_TYPING":return!0;case"STOP_TYPING":return!1}return e},isCaretWithinFormattedText:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];switch((arguments.length>1?arguments[1]:void 0).type){case"ENTER_FORMATTED_TEXT":return!0;case"EXIT_FORMATTED_TEXT":return!1}return e},blockSelection:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{start:null,end:null,isMultiSelecting:!1,isEnabled:!0,initialPosition:null},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"CLEAR_SELECTED_BLOCK":return null!==e.start||null!==e.end||e.isMultiSelecting?Object(d.a)({},e,{start:null,end:null,isMultiSelecting:!1,initialPosition:null}):e;case"START_MULTI_SELECT":return e.isMultiSelecting?e:Object(d.a)({},e,{isMultiSelecting:!0,initialPosition:null});case"STOP_MULTI_SELECT":return e.isMultiSelecting?Object(d.a)({},e,{isMultiSelecting:!1,initialPosition:null}):e;case"MULTI_SELECT":return Object(d.a)({},e,{start:t.start,end:t.end,initialPosition:null});case"SELECT_BLOCK":return t.clientId===e.start&&t.clientId===e.end?e:Object(d.a)({},e,{start:t.clientId,end:t.clientId,initialPosition:t.initialPosition});case"INSERT_BLOCKS":return t.updateSelection?Object(d.a)({},e,{start:t.blocks[0].clientId,end:t.blocks[0].clientId,initialPosition:null,isMultiSelecting:!1}):e;case"REMOVE_BLOCKS":return t.clientIds&&t.clientIds.length&&-1!==t.clientIds.indexOf(e.start)?Object(d.a)({},e,{start:null,end:null,initialPosition:null,isMultiSelecting:!1}):e;case"REPLACE_BLOCKS":if(-1===t.clientIds.indexOf(e.start))return e;var n=Object(O.get)(t.blocks,[0,"clientId"],null);return n===e.start&&n===e.end?e:Object(d.a)({},e,{start:n,end:n,initialPosition:null,isMultiSelecting:!1});case"TOGGLE_SELECTION":return Object(d.a)({},e,{isEnabled:t.isSelectionEnabled})}return e},blocksMode:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if("TOGGLE_BLOCK_MODE"===t.type){var n=t.clientId;return Object(d.a)({},e,Object(f.a)({},n,e[n]&&"html"===e[n]?"visual":"html"))}return e},blockListSettings:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"REPLACE_BLOCKS":case"REMOVE_BLOCKS":return Object(O.omit)(e,t.clientIds);case"UPDATE_BLOCK_LIST_SETTINGS":var n=t.clientId;return t.settings?Object(O.isEqual)(e[n],t.settings)?e:Object(d.a)({},e,Object(f.a)({},n,t.settings)):e.hasOwnProperty(n)?Object(O.omit)(e,n):e}return e},insertionPoint:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SHOW_INSERTION_POINT":return{rootClientId:t.rootClientId,index:t.index};case"HIDE_INSERTION_POINT":return null}return e},preferences:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:E,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"INSERT_BLOCKS":case"REPLACE_BLOCKS":return t.blocks.reduce(function(e,n){var r=n.name,o={name:n.name};return Object(i.isReusableBlock)(n)&&(o.ref=n.attributes.ref,r+="/"+n.attributes.ref),Object(d.a)({},e,{insertUsage:Object(d.a)({},e.insertUsage,Object(f.a)({},r,{time:t.time,count:e.insertUsage[r]?e.insertUsage[r].count+1:1,insert:o}))})},e);case"REMOVE_REUSABLE_BLOCK":return Object(d.a)({},e,{insertUsage:Object(O.omitBy)(e.insertUsage,function(e){return e.insert.ref===t.id})});case"ENABLE_PUBLISH_SIDEBAR":return Object(d.a)({},e,{isPublishSidebarEnabled:!0});case"DISABLE_PUBLISH_SIDEBAR":return Object(d.a)({},e,{isPublishSidebarEnabled:!1})}return e},saving:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"REQUEST_POST_UPDATE_START":return{requesting:!0,successful:!1,error:null,options:t.options||{}};case"REQUEST_POST_UPDATE_SUCCESS":return{requesting:!1,successful:!0,error:null,options:t.options||{}};case"REQUEST_POST_UPDATE_FAILURE":return{requesting:!1,successful:!1,error:t.error,options:t.options||{}}}return e},postLock:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{isLocked:!1},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"UPDATE_POST_LOCK":return t.lock}return e},reusableBlocks:U,template:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{isValid:!0},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_TEMPLATE_VALIDITY":return Object(d.a)({},e,{isValid:t.isValid})}return e},autosave:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"RESET_AUTOSAVE":var n=t.post,r=["title","excerpt","content"].map(function(e){return I(n[e])}),o=Object(u.a)(r,3);return{title:o[0],excerpt:o[1],content:o[2]}}return e},previewLink:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"REQUEST_POST_UPDATE_SUCCESS":return t.post.preview_link?t.post.preview_link:t.post.link?Object(g.addQueryArgs)(t.post.link,{preview:!0}):e;case"REQUEST_POST_UPDATE_START":if(e&&t.options.isPreview)return null}return e},settings:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"UPDATE_EDITOR_SETTINGS":return Object(d.a)({},e,t.settings)}return e},postSavingLock:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOCK_POST_SAVING":return Object(d.a)({},e,Object(f.a)({},t.lockName,!0));case"UNLOCK_POST_SAVING":return Object(O.omit)(e,t.lockName)}return e}})),V=n(87),K=n.n(V),z=n(190),W=n.n(z),q=n(44);function G(e,t){return{type:"SETUP_EDITOR",post:e,edits:t}}function $(e){return{type:"RESET_POST",post:e}}function Y(e){return{type:"RESET_AUTOSAVE",post:e}}function Q(e){return{type:"UPDATE_POST",edits:e}}function X(e,t){return{type:"SETUP_EDITOR_STATE",post:e,blocks:t}}function Z(e){return{type:"RESET_BLOCKS",blocks:e}}function J(e){return{type:"RECEIVE_BLOCKS",blocks:e}}function ee(e,t){return{type:"UPDATE_BLOCK_ATTRIBUTES",clientId:e,attributes:t}}function te(e,t){return{type:"UPDATE_BLOCK",clientId:e,updates:t}}function ne(e){return{type:"SELECT_BLOCK",initialPosition:arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,clientId:e}}function re(){return{type:"START_MULTI_SELECT"}}function oe(){return{type:"STOP_MULTI_SELECT"}}function ie(e,t){return{type:"MULTI_SELECT",start:e,end:t}}function ce(){return{type:"CLEAR_SELECTED_BLOCK"}}function ae(){return{type:"TOGGLE_SELECTION",isSelectionEnabled:!(arguments.length>0&&void 0!==arguments[0])||arguments[0]}}function se(e,t){return{type:"REPLACE_BLOCKS",clientIds:Object(O.castArray)(e),blocks:Object(O.castArray)(t),time:Date.now()}}function le(e,t){return se(e,t)}function ue(e){return function(t,n){return{clientIds:Object(O.castArray)(t),type:e,rootClientId:n}}}var de=ue("MOVE_BLOCKS_DOWN"),pe=ue("MOVE_BLOCKS_UP");function be(e,t,n,r){return{type:"MOVE_BLOCK_TO_POSITION",fromRootClientId:t,toRootClientId:n,clientId:e,index:r}}function fe(e,t,n){return he([e],t,n,!(arguments.length>3&&void 0!==arguments[3])||arguments[3])}function he(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return{type:"INSERT_BLOCKS",blocks:Object(O.castArray)(e),index:t,rootClientId:n,time:Date.now(),updateSelection:r}}function me(e,t){return{type:"SHOW_INSERTION_POINT",rootClientId:e,index:t}}function ve(){return{type:"HIDE_INSERTION_POINT"}}function Oe(e){return{type:"SET_TEMPLATE_VALIDITY",isValid:e}}function ge(){return{type:"SYNCHRONIZE_TEMPLATE"}}function je(e){return{type:"EDIT_POST",edits:e}}function ye(){return{type:"REQUEST_POST_UPDATE",options:arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}}}function ke(){return{type:"REFRESH_POST"}}function _e(e,t){return{type:"TRASH_POST",postId:e,postType:t}}function Ee(e,t){return{type:"MERGE_BLOCKS",blocks:[e,t]}}function Se(e){return ye(Object(d.a)({isAutosave:!0},e))}function Ce(){return{type:"REDO"}}function we(){return{type:"UNDO"}}function Te(){return{type:"CREATE_UNDO_LEVEL"}}function Pe(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return{type:"REMOVE_BLOCKS",clientIds:Object(O.castArray)(e),selectPrevious:t}}function Ie(e,t){return Pe([e],t)}function Be(e){return{type:"TOGGLE_BLOCK_MODE",clientId:e}}function xe(){return{type:"START_TYPING"}}function Le(){return{type:"STOP_TYPING"}}function Ae(){return{type:"ENTER_FORMATTED_TEXT"}}function Ne(){return{type:"EXIT_FORMATTED_TEXT"}}function Re(e){return{type:"UPDATE_POST_LOCK",lock:e}}function De(e){return{type:"FETCH_REUSABLE_BLOCKS",id:e}}function Fe(e){return{type:"RECEIVE_REUSABLE_BLOCKS",results:e}}function Me(e){return{type:"SAVE_REUSABLE_BLOCK",id:e}}function Ue(e){return{type:"DELETE_REUSABLE_BLOCK",id:e}}function He(e,t){return{type:"UPDATE_REUSABLE_BLOCK_TITLE",id:e,title:t}}function Ve(e){return{type:"CONVERT_BLOCK_TO_STATIC",clientId:e}}function Ke(e){return{type:"CONVERT_BLOCK_TO_REUSABLE",clientIds:Object(O.castArray)(e)}}function ze(e,t,n){return fe(Object(i.createBlock)(Object(i.getDefaultBlockName)(),e),n,t)}function We(e,t){return{type:"UPDATE_BLOCK_LIST_SETTINGS",clientId:e,settings:t}}function qe(e){return{type:"UPDATE_EDITOR_SETTINGS",settings:e}}function Ge(){return{type:"ENABLE_PUBLISH_SIDEBAR"}}function $e(){return{type:"DISABLE_PUBLISH_SIDEBAR"}}function Ye(e){return{type:"LOCK_POST_SAVING",lockName:e}}function Qe(e){return{type:"UNLOCK_POST_SAVING",lockName:e}}var Xe=n(31),Ze=n(46),Je=n(58),et="post-update",tt=/%(?:postname|pagename)%/,nt=3,rt=2,ot=1,it=0,ct=6e4,at=[];function st(e){return e.editor.past.length>0}function lt(e){return e.editor.future.length>0}function ut(e){return"auto-draft"===ft(e).status}function dt(e){return e.editor.present.blocks.isDirty||"content"in e.editor.present.edits}function pt(e){return!!dt(e)||(Object.keys(e.editor.present.edits).length>0||or(e,pt))}function bt(e){return!pt(e)&&ut(e)}function ft(e){return e.currentPost}function ht(e){return e.currentPost.type}function mt(e){return ft(e).id||null}function vt(e){return Object(O.get)(ft(e),["_links","version-history",0,"count"],0)}function Ot(e){return Object(O.get)(ft(e),["_links","predecessor-version",0,"id"],null)}var gt=Object(Xe.a)(function(e){return Object(d.a)({},e.initialEdits,e.editor.present.edits)},function(e){return[e.editor.present.edits,e.initialEdits]}),jt=Object(Xe.a)(function(){return[]},function(e){return[e.editor]});function yt(e,t){var n=ft(e);if(n.hasOwnProperty(t))return n[t]}function kt(e,t){switch(t){case"content":return Hn(e)}var n=gt(e);return n.hasOwnProperty(t)?P.has(t)?Object(d.a)({},yt(e,t),n[t]):n[t]:yt(e,t)}function _t(e,t){if(!Lt(e))return null;var n=xt(e);return n.hasOwnProperty(t)?n[t]:void 0}function Et(e){return"private"===kt(e,"status")?"private":kt(e,"password")?"password":"public"}function St(e){return"pending"===ft(e).status}function Ct(e){var t=ft(e);return-1!==["publish","private"].indexOf(t.status)||"future"===t.status&&!Object(Ze.isInTheFuture)(new Date(Number(Object(Ze.getDate)(t.date))-ct))}function wt(e){return"future"===ft(e).status&&!Ct(e)}function Tt(e){var t=ft(e);return pt(e)||-1===["publish","private","future"].indexOf(t.status)}function Pt(e){return!Ln(e)&&(!!kt(e,"title")||!!kt(e,"excerpt")||!It(e))}function It(e){var t=Un(e);if(t.length&&!("content"in gt(e))){if(t.length>1)return!1;if(t[0].name!==Object(i.getFreeformContentHandlerName)())return!1}return!Hn(e)}function Bt(e){if(!Pt(e))return!1;if(!Lt(e))return!0;if(dt(e))return!0;var t=xt(e);return["title","excerpt"].some(function(n){return t[n]!==kt(e,n)})}function xt(e){return e.autosave}function Lt(e){return!!xt(e)}function At(e){var t=kt(e,"date"),n=new Date(Number(Object(Ze.getDate)(t))-ct);return Object(Ze.isInTheFuture)(n)}function Nt(e){var t=kt(e,"date"),n=kt(e,"modified"),r=kt(e,"status");return("draft"===r||"auto-draft"===r)&&t===n}var Rt=Object(Xe.a)(function(){return[]},function(e,t){return Object(O.map)(vn(e,t),function(t){return Ut(e,t)})});function Dt(e,t){var n=e.editor.present.blocks.byClientId[t];return n?n.name:null}function Ft(e,t){var n=e.editor.present.blocks.byClientId[t];return!!n&&n.isValid}var Mt=Object(Xe.a)(function(e,t){var n=e.editor.present.blocks.byClientId[t];if(!n)return null;var r=e.editor.present.blocks.attributes[t],o=Object(i.getBlockType)(n.name);return o&&(r=Object(O.reduce)(o.attributes,function(t,n,o){return"meta"===n.source&&(t===r&&(t=Object(d.a)({},t)),t[o]=function(e,t){return Object(O.has)(e,["editor","present","edits","meta",t])?Object(O.get)(e,["editor","present","edits","meta",t]):Object(O.get)(e,["currentPost","meta",t])}(e,n.meta)),t},r)),r},function(e,t){return[e.editor.present.blocks.byClientId[t],e.editor.present.blocks.attributes[t],e.editor.present.edits.meta,e.initialEdits.meta,e.currentPost.meta]}),Ut=Object(Xe.a)(function(e,t){var n=e.editor.present.blocks.byClientId[t];return n?Object(d.a)({},n,{attributes:Mt(e,t),innerBlocks:Vt(e,t)}):null},function(e,t){return Object(b.a)(Mt.getDependants(e,t)).concat([Rt(e,t)])}),Ht=Object(Xe.a)(function(e,t){var n=e.editor.present.blocks.byClientId[t];return n?Object(d.a)({},n,{attributes:Mt(e,t)}):null},function(e,t){return[e.editor.present.blocks.byClientId[t]].concat(Object(b.a)(Mt.getDependants(e,t)))});var Vt=Object(Xe.a)(function(e,t){return Object(O.map)(vn(e,t),function(t){return Ut(e,t)})},function(e){return[e.editor.present.blocks]}),Kt=function e(t,n){return Object(O.flatMap)(n,function(n){var r=vn(t,n);return Object(b.a)(r).concat(Object(b.a)(e(t,r)))})},zt=Object(Xe.a)(function(e){var t=vn(e);return Object(b.a)(t).concat(Object(b.a)(Kt(e,t)))},function(e){return[e.editor.present.blocks.order]}),Wt=Object(Xe.a)(function(e,t){var n=zt(e);return t?Object(O.reduce)(n,function(n,r){return e.editor.present.blocks.byClientId[r].name===t?n+1:n},0):n.length},function(e){return[e.editor.present.blocks.order,e.editor.present.blocks.byClientId]}),qt=Object(Xe.a)(function(e,t){return Object(O.map)(Object(O.castArray)(t),function(t){return Ut(e,t)})},function(e){return[e.editor.present.edits.meta,e.initialEdits.meta,e.currentPost.meta,e.editor.present.blocks]});function Gt(e,t){return vn(e,t).length}function $t(e){return e.blockSelection.start}function Yt(e){return e.blockSelection.end}function Qt(e){var t=an(e).length;return t||(e.blockSelection.start?1:0)}function Xt(e){var t=e.blockSelection,n=t.start,r=t.end;return!!n&&n===r}function Zt(e){var t=e.blockSelection,n=t.start,r=t.end;return n&&n===r&&e.editor.present.blocks.byClientId[n]?n:null}function Jt(e){var t=Zt(e);return t?Ut(e,t):null}var en=Object(Xe.a)(function(e,t){var n=e.editor.present.blocks.order;for(var r in n)if(Object(O.includes)(n[r],t))return r;return null},function(e){return[e.editor.present.blocks.order]}),tn=Object(Xe.a)(function(e,t){for(var n=t,r=t;n;)n=en(e,r=n);return r},function(e){return[e.editor.present.blocks.order]});function nn(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(void 0===t&&(t=Zt(e)),void 0===t&&(t=n<0?ln(e):un(e)),!t)return null;var r=en(e,t);if(null===r)return null;var o=e.editor.present.blocks.order[r],i=o.indexOf(t)+1*n;return i<0?null:i===o.length?null:o[i]}function rn(e,t){return nn(e,t,-1)}function on(e,t){return nn(e,t,1)}function cn(e){var t=e.blockSelection,n=t.start;return n===t.end&&n?e.blockSelection.initialPosition:null}var an=Object(Xe.a)(function(e){var t=e.blockSelection,n=t.start,r=t.end;if(n===r)return[];var o=en(e,n);if(null===o)return[];var i=vn(e,o),c=i.indexOf(n),a=i.indexOf(r);return c>a?i.slice(a,c+1):i.slice(c,a+1)},function(e){return[e.editor.present.blocks.order,e.blockSelection.start,e.blockSelection.end]}),sn=Object(Xe.a)(function(e){var t=an(e);return t.length?t.map(function(t){return Ut(e,t)}):at},function(e){return Object(b.a)(an.getDependants(e)).concat([e.editor.present.blocks,e.editor.present.edits.meta,e.initialEdits.meta,e.currentPost.meta])});function ln(e){return Object(O.first)(an(e))||null}function un(e){return Object(O.last)(an(e))||null}var dn=Object(Xe.a)(function(e,t,n){for(var r=n;t!==r&&r;)r=en(e,r);return t===r},function(e){return[e.editor.present.blocks.order]});function pn(e,t){return ln(e)===t}function bn(e,t){return-1!==an(e).indexOf(t)}var fn=Object(Xe.a)(function(e,t){for(var n=t,r=!1;n&&!r;)r=bn(e,n=en(e,n));return r},function(e){return[e.editor.present.blocks.order,e.blockSelection.start,e.blockSelection.end]});function hn(e){var t=e.blockSelection,n=t.start;return n===t.end?null:n||null}function mn(e){var t=e.blockSelection,n=t.start,r=t.end;return n===r?null:r||null}function vn(e,t){return e.editor.present.blocks.order[t||""]||at}function On(e,t,n){return vn(e,n).indexOf(t)}function gn(e,t){var n=e.blockSelection,r=n.start;return r===n.end&&r===t}function jn(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return Object(O.some)(vn(e,t),function(t){return gn(e,t)||bn(e,t)||n&&jn(e,t,n)})}function yn(e,t){if(!t)return!1;var n=an(e),r=n.indexOf(t);return r>-1&&r<n.length-1}function kn(e){var t=e.blockSelection;return t.start!==t.end}function _n(e){return e.blockSelection.isMultiSelecting}function En(e){return e.blockSelection.isEnabled}function Sn(e,t){return e.blocksMode[t]||"visual"}function Cn(e){return e.isTyping}function wn(e){return e.isCaretWithinFormattedText}function Tn(e){var t,n,r=e.insertionPoint,o=e.blockSelection;if(null!==r)return r;var i=o.end;return n=i?On(e,i,t=en(e,i)||void 0)+1:vn(e).length,{rootClientId:t,index:n}}function Pn(e){return null!==e.insertionPoint}function In(e){return e.template.isValid}function Bn(e){return e.settings.template}function xn(e,t){if(!t)return e.settings.templateLock;var n=ir(e,t);return n?n.templateLock:null}function Ln(e){return e.saving.requesting}function An(e){return e.saving.successful}function Nn(e){return!!e.saving.error}function Rn(e){return Ln(e)&&!!e.saving.options.isAutosave}function Dn(e){return Ln(e)&&!!e.saving.options.isPreview}function Fn(e){var t=kt(e,"featured_media"),n=e.previewLink;return n&&t?Object(g.addQueryArgs)(n,{_thumbnail_id:t}):n}function Mn(e){var t,n=vn(e);switch(1===n.length&&(t=Dt(e,n[0])),2===n.length&&"core/paragraph"===Dt(e,n[1])&&(t=Dt(e,n[0])),t){case"core/image":return"image";case"core/quote":case"core/pullquote":return"quote";case"core/gallery":return"gallery";case"core/video":case"core-embed/youtube":case"core-embed/vimeo":return"video";case"core/audio":case"core-embed/spotify":case"core-embed/soundcloud":return"audio"}return null}function Un(e){var t=Vt(e);return 1===t.length&&Object(i.isUnmodifiedDefaultBlock)(t[0])?[]:t}var Hn=Object(Xe.a)(function(e){var t=gt(e);if("content"in t)return t.content;var n=Un(e),r=Object(i.serialize)(n);return 1===n.length&&n[0].name===Object(i.getFreeformContentHandlerName)()?Object(Je.removep)(r):r},function(e){return[e.editor.present.blocks,e.editor.present.edits.content,e.initialEdits.content]}),Vn=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return Object(O.isBoolean)(e)?e:Object(O.isArray)(e)?Object(O.includes)(e,t):n},o=Object(i.getBlockType)(t);if(!o)return!1;if(!r(cr(e).allowedBlockTypes,t,!0))return!1;if(!!xn(e,n))return!1;var c=ir(e,n),a=r(Object(O.get)(c,["allowedBlocks"]),t),s=r(o.parent,Dt(e,n));return null!==a&&null!==s?a||s:null!==a?a:null===s||s},Kn=Object(Xe.a)(Vn,function(e,t,n){return[e.blockListSettings[n],e.editor.present.blocks.byClientId[n],e.settings.allowedBlockTypes,e.settings.templateLock]});function zn(e,t){return e.preferences.insertUsage[t]||null}var Wn=function(e,t,n){return!!Object(i.hasBlockSupport)(t,"inserter",!0)&&Vn(e,t.name,n)},qn=function(e,t,n){if(!Vn(e,"core/block",n))return!1;var r=Dt(e,t.clientId);return!!r&&(!!Object(i.getBlockType)(r)&&(!!Vn(e,r,n)&&!dn(e,t.clientId,n)))},Gn=Object(Xe.a)(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t,n){return n?nt:t>0?rt:"common"===e?ot:it},r=function(e,t){if(!e)return t;var n=Date.now()-e;switch(!0){case n<36e5:return 4*t;case n<864e5:return 2*t;case n<6048e5:return t/2;default:return t/4}},o=Object(i.getBlockTypes)().filter(function(n){return Wn(e,n,t)}).map(function(t){var o=t.name,c=!1;Object(i.hasBlockSupport)(t.name,"multiple",!0)||(c=Object(O.some)(qt(e,zt(e)),{name:t.name}));var a=Object(O.isArray)(t.parent),s=zn(e,o)||{},l=s.time,u=s.count,d=void 0===u?0:u;return{id:o,name:t.name,initialAttributes:{},title:t.title,icon:t.icon,category:t.category,keywords:t.keywords,isDisabled:c,utility:n(t.category,d,a),frecency:r(l,d),hasChildBlocksWithInserterSupport:Object(i.hasChildBlocksWithInserterSupport)(t.name)}}),c=Zn(e).filter(function(n){return qn(e,n,t)}).map(function(t){var o="core/block/".concat(t.id),c=Dt(e,t.clientId),a=Object(i.getBlockType)(c),s=zn(e,o)||{},l=s.time,u=s.count,d=void 0===u?0:u,p=n("reusable",d,!1),b=r(l,d);return{id:o,name:"core/block",initialAttributes:{ref:t.id},title:t.title,icon:a.icon,category:"reusable",keywords:[],isDisabled:!1,utility:p,frecency:b}});return Object(O.orderBy)(Object(b.a)(o).concat(Object(b.a)(c)),["utility","frecency"],["desc","desc"])},function(e,t){return[e.blockListSettings[t],e.editor.present.blocks.byClientId,e.editor.present.blocks.order,e.preferences.insertUsage,e.settings.allowedBlockTypes,e.settings.templateLock,e.reusableBlocks.data,Object(i.getBlockTypes)()]}),$n=Object(Xe.a)(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return!!Object(O.some)(Object(i.getBlockTypes)(),function(n){return Wn(e,n,t)})||Object(O.some)(Zn(e),function(n){return qn(e,n,t)})},function(e,t){return[e.blockListSettings[t],e.editor.present.blocks.byClientId,e.settings.allowedBlockTypes,e.settings.templateLock,e.reusableBlocks.data,Object(i.getBlockTypes)()]}),Yn=Object(Xe.a)(function(e,t){var n=e.reusableBlocks.data[t];if(!n)return null;var r=isNaN(parseInt(t));return Object(d.a)({},n,{id:r?t:+t,isTemporary:r})},function(e,t){return[e.reusableBlocks.data[t]]});function Qn(e,t){return e.reusableBlocks.isSaving[t]||!1}function Xn(e,t){return!!e.reusableBlocks.isFetching[t]}function Zn(e){return Object(O.map)(e.reusableBlocks.data,function(t,n){return Yn(e,n)})}function Jn(e,t){var n=Object(O.find)(e.optimist,function(e){return e.beforeState&&Object(O.get)(e.action,["optimist","id"])===t});return n?n.beforeState:null}function er(e){if(!Ln(e))return!1;if(!Ct(e))return!1;var t=Jn(e,et);return!!t&&!Ct(t)}function tr(e){var t=kt(e,"permalink_template");return tt.test(t)}function nr(e){var t=rr(e);if(!t)return null;var n=t.prefix,r=t.postName,o=t.suffix;return tr(e)?n+r+o:n}function rr(e){var t=kt(e,"permalink_template");if(!t)return null;var n=kt(e,"slug")||kt(e,"generated_slug"),r=t.split(tt),o=Object(u.a)(r,2);return{prefix:o[0],postName:n,suffix:o[1]}}function or(e,t){var n=e.optimist;return!!n&&n.some(function(e){var n=e.beforeState;return n&&t(n)})}function ir(e,t){return e.blockListSettings[t]}function cr(e){return e.settings}function ar(e,t){return t?e.tokens[t]:e.tokens}function sr(e){return e.postLock.isLocked}function lr(e){return Object.keys(e.postSavingLock).length>0}function ur(e){return e.postLock.isTakeover}function dr(e){return e.postLock.user}function pr(e){return e.postLock.activePostLock}function br(e){return Object(O.has)(ft(e),["_links","wp:action-unfiltered-html"])}function fr(e){return e.preferences.hasOwnProperty("isPublishSidebarEnabled")?e.preferences.isPublishSidebarEnabled:E.isPublishSidebarEnabled}var hr=n(38),mr=n(30),vr=n.n(mr),Or=function(){var e=Object(hr.a)(regeneratorRuntime.mark(function e(t,n){var r,o,c,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.id,o=n.dispatch,e.next=4,vr()({path:"/wp/v2/types/wp_block"});case 4:if(c=e.sent){e.next=7;break}return e.abrupt("return");case 7:if(e.prev=7,!r){e.next=15;break}return e.next=11,vr()({path:"/wp/v2/".concat(c.rest_base,"/").concat(r)});case 11:e.t0=e.sent,a=[e.t0],e.next=18;break;case 15:return e.next=17,vr()({path:"/wp/v2/".concat(c.rest_base,"?per_page=-1")});case 17:a=e.sent;case 18:(s=Object(O.compact)(Object(O.map)(a,function(e){if("publish"!==e.status||e.content.protected)return null;var t=Object(i.parse)(e.content.raw);return{reusableBlock:{id:e.id,title:I(e.title)},parsedBlock:1===t.length?t[0]:Object(i.createBlock)("core/template",{},t)}}))).length&&o(Fe(s)),o({type:"FETCH_REUSABLE_BLOCKS_SUCCESS",id:r}),e.next=26;break;case 23:e.prev=23,e.t1=e.catch(7),o({type:"FETCH_REUSABLE_BLOCKS_FAILURE",id:r,error:e.t1});case 26:case"end":return e.stop()}},e,this,[[7,23]])}));return function(t,n){return e.apply(this,arguments)}}(),gr=function(){var e=Object(hr.a)(regeneratorRuntime.mark(function e(t,n){var r,o,c,a,s,u,d,p,b,f,h,m,v,O,g;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,vr()({path:"/wp/v2/types/wp_block"});case 2:if(r=e.sent){e.next=5;break}return e.abrupt("return");case 5:return o=t.id,c=n.dispatch,a=n.getState(),s=Yn(a,o),u=s.clientId,d=s.title,p=s.isTemporary,b=Ut(a,u),f=Object(i.serialize)("core/template"===b.name?b.innerBlocks:b),h=p?{title:d,content:f,status:"publish"}:{id:o,title:d,content:f,status:"publish"},m=p?"/wp/v2/".concat(r.rest_base):"/wp/v2/".concat(r.rest_base,"/").concat(o),v=p?"POST":"PUT",e.prev=14,e.next=17,vr()({path:m,data:h,method:v});case 17:O=e.sent,c({type:"SAVE_REUSABLE_BLOCK_SUCCESS",updatedId:O.id,id:o}),g=p?Object(_.__)("Block created."):Object(_.__)("Block updated."),Object(l.dispatch)("core/notices").createSuccessNotice(g,{id:"REUSABLE_BLOCK_NOTICE_ID"}),e.next=27;break;case 23:e.prev=23,e.t0=e.catch(14),c({type:"SAVE_REUSABLE_BLOCK_FAILURE",id:o}),Object(l.dispatch)("core/notices").createErrorNotice(e.t0.message,{id:"REUSABLE_BLOCK_NOTICE_ID"});case 27:case"end":return e.stop()}},e,this,[[14,23]])}));return function(t,n){return e.apply(this,arguments)}}(),jr=function(){var e=Object(hr.a)(regeneratorRuntime.mark(function e(t,n){var r,o,c,a,s,u,d,p,f,h;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,vr()({path:"/wp/v2/types/wp_block"});case 2:if(r=e.sent){e.next=5;break}return e.abrupt("return");case 5:if(o=t.id,c=n.getState,a=n.dispatch,(s=Yn(c(),o))&&!s.isTemporary){e.next=10;break}return e.abrupt("return");case 10:return u=Vt(c()),d=u.filter(function(e){return Object(i.isReusableBlock)(e)&&e.attributes.ref===o}),p=d.map(function(e){return e.clientId}),f=Object(O.uniqueId)(),a({type:"REMOVE_REUSABLE_BLOCK",id:o,optimist:{type:m.BEGIN,id:f}}),a(Pe(Object(b.a)(p).concat([s.clientId]))),e.prev=16,e.next=19,vr()({path:"/wp/v2/".concat(r.rest_base,"/").concat(o),method:"DELETE"});case 19:a({type:"DELETE_REUSABLE_BLOCK_SUCCESS",id:o,optimist:{type:m.COMMIT,id:f}}),h=Object(_.__)("Block deleted."),Object(l.dispatch)("core/notices").createSuccessNotice(h,{id:"REUSABLE_BLOCK_NOTICE_ID"}),e.next=28;break;case 24:e.prev=24,e.t0=e.catch(16),a({type:"DELETE_REUSABLE_BLOCK_FAILURE",id:o,optimist:{type:m.REVERT,id:f}}),Object(l.dispatch)("core/notices").createErrorNotice(e.t0.message,{id:"REUSABLE_BLOCK_NOTICE_ID"});case 28:case"end":return e.stop()}},e,this,[[16,24]])}));return function(t,n){return e.apply(this,arguments)}}();function yr(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];return new Promise(function(n){var o=function(){return Object(l.select)("core/data").hasFinishedResolution(e,t,r)},i=function(){return Object(l.select)(e)[t].apply(null,r)},c=i();if(o())return n(c);var a=Object(l.subscribe)(function(){o()&&(a(),n(i()))})})}var kr=function(){var e=Object(hr.a)(regeneratorRuntime.mark(function e(t,n){var r,o,i,c,a,s,u,p,b,f,h;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.dispatch,o=n.getState,Pt(i=o())){e.next=4;break}return e.abrupt("return");case 4:return c=gt(i),(a=!!t.options.isAutosave)&&(c=Object(O.pick)(c,["title","content","excerpt"])),ut(i)&&(c=Object(d.a)({status:"draft"},c)),s=ft(i),u=Object(d.a)({},c,{content:Hn(i),id:s.id}),e.next=12,yr("core","getPostType",ht(i));case 12:return p=e.sent,r({type:"REQUEST_POST_UPDATE_START",optimist:{type:m.BEGIN,id:et},options:t.options}),r(Object(d.a)({},Q(u),{optimist:{id:et}})),a?(u=Object(d.a)({},Object(O.pick)(s,["title","content","excerpt"]),xt(i),u),b=vr()({path:"/wp/v2/".concat(p.rest_base,"/").concat(s.id,"/autosaves"),method:"POST",data:u})):(Object(l.dispatch)("core/notices").removeNotice("SAVE_POST_NOTICE_ID"),Object(l.dispatch)("core/notices").removeNotice("autosave-exists"),b=vr()({path:"/wp/v2/".concat(p.rest_base,"/").concat(s.id),method:"PUT",data:u})),e.prev=16,e.next=19,b;case 19:f=e.sent,r((a?Y:$)(f)),h=f.id!==s.id,r({type:"REQUEST_POST_UPDATE_SUCCESS",previousPost:s,post:f,optimist:{type:h?m.REVERT:m.COMMIT,id:et},options:t.options,postType:p}),e.next=29;break;case 26:e.prev=26,e.t0=e.catch(16),r({type:"REQUEST_POST_UPDATE_FAILURE",optimist:{type:m.REVERT,id:et},post:s,edits:c,error:e.t0,options:t.options});case 29:case"end":return e.stop()}},e,this,[[16,26]])}));return function(t,n){return e.apply(this,arguments)}}(),_r=function(){var e=Object(hr.a)(regeneratorRuntime.mark(function e(t,n){var r,o,i,c,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.dispatch,o=n.getState,i=t.postId,c=ht(o()),e.next=5,yr("core","getPostType",c);case 5:return a=e.sent,Object(l.dispatch)("core/notices").removeNotice("TRASH_POST_NOTICE_ID"),e.prev=7,e.next=10,vr()({path:"/wp/v2/".concat(a.rest_base,"/").concat(i),method:"DELETE"});case 10:s=ft(o()),r($(Object(d.a)({},s,{status:"trash"}))),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),r(Object(d.a)({},t,{type:"TRASH_POST_FAILURE",error:e.t0}));case 17:case"end":return e.stop()}},e,this,[[7,14]])}));return function(t,n){return e.apply(this,arguments)}}(),Er=function(){var e=Object(hr.a)(regeneratorRuntime.mark(function e(t,n){var r,o,i,c,a,s,l;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.dispatch,o=n.getState,i=o(),c=ft(i),a=ht(o()),e.next=6,yr("core","getPostType",a);case 6:return s=e.sent,e.next=9,vr()({path:"/wp/v2/".concat(s.rest_base,"/").concat(c.id,"?context=edit&_timestamp=").concat(Date.now())});case 9:l=e.sent,r($(l));case 11:case"end":return e.stop()}},e,this)}));return function(t,n){return e.apply(this,arguments)}}();function Sr(e,t){var n=t.getState(),r=Bn(n),o=xn(n),c=!r||"all"!==o||Object(i.doBlocksMatchTemplate)(e.blocks,r);if(c!==In(n))return Oe(c)}function Cr(e,t){if(!Gt(t.getState()))return ze()}var wr={REQUEST_POST_UPDATE:function(e,t){kr(e,t)},REQUEST_POST_UPDATE_SUCCESS:function(e){var t=e.previousPost,n=e.post,r=e.postType;if(!Object(O.get)(e.options,["isAutosave"])){var o,i=["publish","private","future"],c=Object(O.includes)(i,t.status),a=Object(O.includes)(i,n.status),s=Object(O.get)(r,["viewable"],!1);if(c||a?c&&!a?(o=r.labels.item_reverted_to_draft,s=!1):o=!c&&a?{publish:r.labels.item_published,private:r.labels.item_published_privately,future:r.labels.item_scheduled}[n.status]:r.labels.item_updated:o=null,o){var u=[];s&&u.push({label:r.labels.view_item,url:n.link}),Object(l.dispatch)("core/notices").createSuccessNotice(o,{id:"SAVE_POST_NOTICE_ID",actions:u})}}},REQUEST_POST_UPDATE_FAILURE:function(e){var t=e.post,n=e.edits,r=e.error;if(!r||"rest_autosave_no_changes"!==r.code){var o=["publish","private","future"],i=-1!==o.indexOf(t.status),c={publish:Object(_.__)("Publishing failed"),private:Object(_.__)("Publishing failed"),future:Object(_.__)("Scheduling failed")},a=i||-1===o.indexOf(n.status)?Object(_.__)("Updating failed"):c[n.status];Object(l.dispatch)("core/notices").createErrorNotice(a,{id:"SAVE_POST_NOTICE_ID"})}},TRASH_POST:function(e,t){_r(e,t)},TRASH_POST_FAILURE:function(e){var t=e.error.message&&"unknown_error"!==e.error.code?e.error.message:Object(_.__)("Trashing failed");Object(l.dispatch)("core/notices").createErrorNotice(t,{id:"TRASH_POST_NOTICE_ID"})},REFRESH_POST:function(e,t){Er(e,t)},MERGE_BLOCKS:function(e,t){var n=t.dispatch,r=t.getState(),o=Object(u.a)(e.blocks,2),c=o[0],a=o[1],s=Ut(r,c),l=Object(i.getBlockType)(s.name);if(l.merge){var p=Ut(r,a),f=s.name===p.name?[p]:Object(i.switchToBlockType)(p,s.name);if(f&&f.length){var h=l.merge(s.attributes,f[0].attributes);n(ne(s.clientId,-1)),n(se([s.clientId,p.clientId],[Object(d.a)({},s,{attributes:Object(d.a)({},s.attributes,h)})].concat(Object(b.a)(f.slice(1)))))}}else n(ne(s.clientId))},SETUP_EDITOR:function(e,t){var n,r=e.post,o=e.edits,c=t.getState();n=Object(O.has)(o,["content"])?o.content:r.content.raw;var a=Object(i.parse)(n),s="auto-draft"===r.status,l=Bn(c);s&&l&&(a=Object(i.synchronizeBlocksWithTemplate)(a,l));var u=X(r,a);return Object(O.compact)([u,Sr(u,t)])},RESET_BLOCKS:[Sr],SYNCHRONIZE_TEMPLATE:function(e,t){var n=(0,t.getState)(),r=Vt(n),o=Bn(n);return Z(Object(i.synchronizeBlocksWithTemplate)(r,o))},FETCH_REUSABLE_BLOCKS:function(e,t){Or(e,t)},SAVE_REUSABLE_BLOCK:function(e,t){gr(e,t)},DELETE_REUSABLE_BLOCK:function(e,t){jr(e,t)},RECEIVE_REUSABLE_BLOCKS:function(e){return J(Object(O.map)(e.results,"parsedBlock"))},CONVERT_BLOCK_TO_STATIC:function(e,t){var n,r=t.getState(),o=Ut(r,e.clientId),c=Yn(r,o.attributes.ref),a=Ut(r,c.clientId);n="core/template"===a.name?a.innerBlocks.map(function(e){return Object(i.cloneBlock)(e)}):[Object(i.cloneBlock)(a)],t.dispatch(se(o.clientId,n))},CONVERT_BLOCK_TO_REUSABLE:function(e,t){var n,r=t.getState,o=t.dispatch;1===e.clientIds.length?n=Ut(r(),e.clientIds[0]):o(J([n=Object(i.createBlock)("core/template",{},qt(r(),e.clientIds))]));var c={id:Object(O.uniqueId)("reusable"),clientId:n.clientId,title:Object(_.__)("Untitled Reusable Block")};o(Fe([{reusableBlock:c,parsedBlock:n}])),o(Me(c.id)),o(se(e.clientIds,Object(i.createBlock)("core/block",{ref:c.id}))),o(J([n]))},REMOVE_BLOCKS:[function(e,t){if(e.selectPrevious){var n=e.clientIds[0],r=t.getState(),o=Zt(r),i=Object(d.a)({},r,{editor:{present:Object(O.last)(r.editor.past)}}),c=en(i,n),a=rn(i,n)||c;return a!==o?ne(a,-1):void 0}},Cr],REPLACE_BLOCKS:[Cr],MULTI_SELECT:function(e,t){var n=Qt((0,t.getState)());Object(q.speak)(Object(_.sprintf)(Object(_._n)("%s block selected.","%s blocks selected.",n),n),"assertive")}};var Tr=function(e){var t,n=[K()(wr),W.a],r=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},o={getState:e.getState,dispatch:function(){return r.apply(void 0,arguments)}};return t=n.map(function(e){return e(o)}),r=O.flowRight.apply(void 0,Object(b.a)(t))(e.dispatch),e.dispatch=r,e},Pr=Object(l.registerStore)("core/editor",{reducer:H,selectors:o,actions:r,persist:["preferences"]});Tr(Pr);var Ir=n(18),Br=n(0),xr=n(17),Lr=n.n(xr),Ar=n(7),Nr=n(23),Rr=n(10),Dr=n(9),Fr=n(12),Mr=n(13),Ur=n(14),Hr=n(3),Vr=n(4),Kr=Object(Br.createContext)({name:"",isSelected:!1,focusedElement:null,setFocusedElement:O.noop,clientId:null}),zr=Kr.Consumer,Wr=Kr.Provider,qr=function(e){return Object(Ar.createHigherOrderComponent)(function(t){return function(n){return Object(Br.createElement)(zr,null,function(r){return Object(Br.createElement)(t,Object(Ir.a)({},n,e(r,n)))})}},"withBlockEditContext")},Gr=Object(Ar.createHigherOrderComponent)(function(e){return function(t){return Object(Br.createElement)(zr,null,function(n){return n.isSelected&&Object(Br.createElement)(e,t)})}},"ifBlockEditSelected"),$r=[];var Yr=Object(Ar.compose)([qr(function(e){return{blockName:e.name}}),function(e){return function(t){function n(){var e;return Object(Rr.a)(this,n),(e=Object(Fr.a)(this,Object(Mr.a)(n).call(this))).state={completers:$r},e.saveParentRef=e.saveParentRef.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onFocus=e.onFocus.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(n,t),Object(Dr.a)(n,[{key:"componentDidUpdate",value:function(){this.parentNode.contains(document.activeElement)&&this.hasStaleCompleters()&&this.updateCompletersState()}},{key:"onFocus",value:function(){this.hasStaleCompleters()&&this.updateCompletersState()}},{key:"hasStaleCompleters",value:function(){return!("lastFilteredCompletersProp"in this.state)||this.state.lastFilteredCompletersProp!==this.props.completers}},{key:"updateCompletersState",value:function(){var e=this.props,t=e.blockName,n=e.completers,r=n;Object(Nr.hasFilter)("editor.Autocomplete.completers")&&(n=Object(Nr.applyFilters)("editor.Autocomplete.completers",n&&n.map(O.clone),t)),this.setState({lastFilteredCompletersProp:r,completers:n||$r})}},{key:"saveParentRef",value:function(e){this.parentNode=e}},{key:"render",value:function(){var t=this.state.completers,n=Object(d.a)({},this.props,{completers:t});return Object(Br.createElement)("div",{onFocus:this.onFocus,ref:this.saveParentRef},Object(Br.createElement)(e,Object(Ir.a)({onFocus:this.onFocus},n)))}}]),n}(Br.Component)}])(Vr.Autocomplete);function Qr(e){var t=e.icon,n=e.showColors,r=void 0!==n&&n,o=e.className;"block-default"===Object(O.get)(t,["src"])&&(t={src:Object(Br.createElement)(Vr.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(Br.createElement)(Vr.Path,{d:"M19 7h-1V5h-4v2h-4V5H6v2H5c-1.1 0-2 .9-2 2v10h18V9c0-1.1-.9-2-2-2zm0 10H5V9h14v8z"}))});var i=Object(Br.createElement)(Vr.Icon,{icon:t&&t.src?t.src:t}),c=r?{backgroundColor:t&&t.background,color:t&&t.foreground}:{};return Object(Br.createElement)("span",{style:c,className:Lr()("editor-block-icon",o,{"has-colors":r})},i)}function Xr(){return Object(l.select)("core/editor").getBlockInsertionPoint().rootClientId}function Zr(e){return Object(l.select)("core/editor").getInserterItems(e)}function Jr(){var e=Object(l.select)("core/editor"),t=e.getSelectedBlockClientId,n=e.getBlockName,r=t();return r?n(r):null}var eo=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.getBlockInsertionParentClientId,n=void 0===t?Xr:t,r=e.getInserterItems,o=void 0===r?Zr:r,c=e.getSelectedBlockName,a=void 0===c?Jr:c;return{name:"blocks",className:"editor-autocompleters__block",triggerPrefix:"/",options:function(){var e=a();return o(n()).filter(function(t){return e!==t.name})},getOptionKeywords:function(e){var t=e.title,n=e.keywords,r=void 0===n?[]:n;return[e.category].concat(Object(b.a)(r),[t])},getOptionLabel:function(e){var t=e.icon,n=e.title;return[Object(Br.createElement)(Qr,{key:"icon",icon:t,showColors:!0}),n]},allowContext:function(e,t){return!(/\S/.test(e)||/\S/.test(t))},getOptionCompletion:function(e){var t=e.name,n=e.initialAttributes;return{action:"replace",value:Object(i.createBlock)(t,n)}},isOptionDisabled:function(e){return e.isDisabled}}}(),to={name:"users",className:"editor-autocompleters__user",triggerPrefix:"@",options:function(e){var t="";return e&&(t="?search="+encodeURIComponent(e)),vr()({path:"/wp/v2/users"+t})},isDebounced:!0,getOptionKeywords:function(e){return[e.slug,e.name]},getOptionLabel:function(e){return[Object(Br.createElement)("img",{key:"avatar",className:"editor-autocompleters__user-avatar",alt:"",src:e.avatar_urls[24]}),Object(Br.createElement)("span",{key:"name",className:"editor-autocompleters__user-name"},e.name),Object(Br.createElement)("span",{key:"slug",className:"editor-autocompleters__user-slug"},e.slug)]},getOptionCompletion:function(e){return"@".concat(e.slug)}},no=[{icon:"editor-alignleft",title:Object(_.__)("Align text left"),align:"left"},{icon:"editor-aligncenter",title:Object(_.__)("Align text center"),align:"center"},{icon:"editor-alignright",title:Object(_.__)("Align text right"),align:"right"}];var ro=Object(Ar.compose)(qr(function(e){return{clientId:e.clientId}}),Object(s.withViewportMatch)({isLargeViewport:"medium"}),Object(l.withSelect)(function(e,t){var n=t.clientId,r=t.isLargeViewport,o=t.isCollapsed,i=e("core/editor"),c=i.getBlockRootClientId,a=i.getEditorSettings;return{isCollapsed:o||!r||!a().hasFixedToolbar&&c(n)}}))(function(e){var t=e.isCollapsed,n=e.value,r=e.onChange,o=e.alignmentControls,i=void 0===o?no:o;function c(e){return function(){return r(n===e?void 0:e)}}var a=Object(O.find)(i,function(e){return e.align===n});return Object(Br.createElement)(Vr.Toolbar,{isCollapsed:t,icon:a?a.icon:"editor-alignleft",label:Object(_.__)("Change Text Alignment"),controls:i.map(function(e){var t=e.align,r=n===t;return Object(d.a)({},e,{isActive:r,onClick:c(t)})})})}),oo={left:{icon:"align-left",title:Object(_.__)("Align left")},center:{icon:"align-center",title:Object(_.__)("Align center")},right:{icon:"align-right",title:Object(_.__)("Align right")},wide:{icon:"align-wide",title:Object(_.__)("Wide width")},full:{icon:"align-full-width",title:Object(_.__)("Full width")}},io=["left","center","right","wide","full"],co=["wide","full"];var ao=Object(Ar.compose)(qr(function(e){return{clientId:e.clientId}}),Object(s.withViewportMatch)({isLargeViewport:"medium"}),Object(l.withSelect)(function(e,t){var n=t.clientId,r=t.isLargeViewport,o=t.isCollapsed,i=e("core/editor"),c=i.getBlockRootClientId,a=i.getEditorSettings;return{wideControlsEnabled:e("core/editor").getEditorSettings().alignWide,isCollapsed:o||!r||!a().hasFixedToolbar&&c(n)}}))(function(e){var t=e.isCollapsed,n=e.value,r=e.onChange,o=e.controls,i=void 0===o?io:o,c=e.wideControlsEnabled,a=void 0!==c&&c?i:i.filter(function(e){return-1===co.indexOf(e)}),s=oo[n];return Object(Br.createElement)(Vr.Toolbar,{isCollapsed:t,icon:s?s.icon:"align-left",label:Object(_.__)("Change Alignment"),controls:a.map(function(e){return Object(d.a)({},oo[e],{isActive:n===e,onClick:(t=e,function(){return r(n===t?void 0:t)})});var t})})}),so=Object(Vr.createSlotFill)("BlockControls"),lo=so.Fill,uo=so.Slot,po=Gr(function(e){var t=e.controls,n=e.children;return Object(Br.createElement)(lo,null,Object(Br.createElement)(Vr.Toolbar,{controls:t}),n)});po.Slot=uo;var bo=po,fo=Object(Vr.withFilters)("editor.BlockEdit")(function(e){var t=e.attributes,n=void 0===t?{}:t,r=e.name,o=Object(i.getBlockType)(r);if(!o)return null;var c=Object(i.hasBlockSupport)(o,"className",!0)?Object(i.getBlockDefaultClassName)(r):null,a=Lr()(c,n.className),s=o.edit||o.save;return Object(Br.createElement)(s,Object(Ir.a)({},e,{className:a}))}),ho=function(e){function t(e){var n;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).call(this,e))).setFocusedElement=n.setFocusedElement.bind(Object(Hr.a)(Object(Hr.a)(n))),n.state={focusedElement:null,setFocusedElement:n.setFocusedElement},n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"setFocusedElement",value:function(e){this.setState(function(t){return t.focusedElement===e?null:{focusedElement:e}})}},{key:"render",value:function(){return Object(Br.createElement)(Wr,{value:this.state},Object(Br.createElement)(fo,this.props))}}],[{key:"getDerivedStateFromProps",value:function(e){var t=e.clientId;return{name:e.name,isSelected:e.isSelected,clientId:t}}}]),t}(Br.Component),mo=Object(Vr.createSlotFill)("BlockFormatControls"),vo=mo.Fill,Oo=mo.Slot,go=Gr(vo);go.Slot=Oo;var jo=go,yo=n(16);function ko(e){var t=e.blocks,n=e.selectedBlockClientId,r=e.selectBlock,o=e.showNestedBlocks;return Object(Br.createElement)("ul",{className:"editor-block-navigation__list",role:"list"},Object(O.map)(t,function(e){var t=Object(i.getBlockType)(e.name),c=e.clientId===n;return Object(Br.createElement)("li",{key:e.clientId},Object(Br.createElement)("div",{className:"editor-block-navigation__item"},Object(Br.createElement)(Vr.Button,{className:Lr()("editor-block-navigation__item-button",{"is-selected":e.clientId===n}),onClick:function(){return r(e.clientId)},isSelected:c},Object(Br.createElement)(Qr,{icon:t.icon,showColors:!0}),t.title,c&&Object(Br.createElement)("span",{className:"screen-reader-text"},Object(_.__)("(selected block)")))),o&&!!e.innerBlocks&&!!e.innerBlocks.length&&Object(Br.createElement)(ko,{blocks:e.innerBlocks,selectedBlockClientId:n,selectBlock:r,showNestedBlocks:!0}))}))}var _o=Object(Ar.compose)(Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getSelectedBlockClientId,r=t.getBlockHierarchyRootClientId,o=t.getBlock,i=t.getBlocks,c=n();return{rootBlocks:i(),rootBlock:c?o(r(c)):null,selectedBlockClientId:c}}),Object(l.withDispatch)(function(e,t){var n=t.onSelect,r=void 0===n?O.noop:n;return{selectBlock:function(t){e("core/editor").selectBlock(t),r(t)}}}))(function(e){var t=e.rootBlock,n=e.rootBlocks,r=e.selectedBlockClientId,o=e.selectBlock;if(!n||0===n.length)return null;var i=t&&(t.clientId!==r||t.innerBlocks&&0!==t.innerBlocks.length);return Object(Br.createElement)(Vr.NavigableMenu,{role:"presentation",className:"editor-block-navigation__container"},Object(Br.createElement)("p",{className:"editor-block-navigation__label"},Object(_.__)("Block Navigation")),i&&Object(Br.createElement)(ko,{blocks:[t],selectedBlockClientId:r,selectBlock:o,showNestedBlocks:!0}),!i&&Object(Br.createElement)(ko,{blocks:n,selectedBlockClientId:r,selectBlock:o}))}),Eo=Object(Br.createElement)(Vr.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"20",height:"20"},Object(Br.createElement)(Vr.Path,{d:"M5 5H3v2h2V5zm3 8h11v-2H8v2zm9-8H6v2h11V5zM7 11H5v2h2v-2zm0 8h2v-2H7v2zm3-2v2h11v-2H10z"}));var So=Object(l.withSelect)(function(e){return{hasBlocks:!!e("core/editor").getBlockCount()}})(function(e){var t=e.hasBlocks;return Object(Br.createElement)(Vr.Dropdown,{renderToggle:function(e){var n=e.isOpen,r=e.onToggle;return Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(Vr.KeyboardShortcuts,{bindGlobal:!0,shortcuts:Object(f.a)({},yo.rawShortcut.access("o"),r)}),Object(Br.createElement)(Vr.IconButton,{icon:Eo,"aria-expanded":n,onClick:t?r:void 0,label:Object(_.__)("Block Navigation"),className:"editor-block-navigation",shortcut:yo.displayShortcut.access("o"),"aria-disabled":!t}))},renderContent:function(e){var t=e.onClose;return Object(Br.createElement)(_o,{onSelect:t})}})}),Co=Object(Ar.createHigherOrderComponent)(Object(l.withSelect)(function(e,t){var n=e("core/editor").getEditorSettings(),r=void 0===t.colors?n.colors:t.colors,o=void 0===t.disableCustomColors?n.disableCustomColors:t.disableCustomColors;return{colors:r,disableCustomColors:o,hasColorsToChoose:!Object(O.isEmpty)(r)||!o}}),"withColorContext"),wo=Co(Vr.ColorPalette),To=n(45),Po=n.n(To),Io=function(e,t,n){if(t){var r=Object(O.find)(e,{slug:t});if(r)return r}return{color:n}},Bo=function(e,t){return Object(O.find)(e,{color:t})};function xo(e,t){if(e&&t)return"has-".concat(Object(O.kebabCase)(t),"-").concat(e)}var Lo=[],Ao=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=Object(O.reduce)(t,function(e,t){return Object(d.a)({},e,Object(O.isString)(t)?Object(f.a)({},t,Object(O.kebabCase)(t)):t)},{});return Object(Ar.createHigherOrderComponent)(Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor").getEditorSettings();return{colors:Object(O.get)(t,["colors"],Lo)}}),function(e){return function(t){function n(e){var t;return Object(Rr.a)(this,n),(t=Object(Fr.a)(this,Object(Mr.a)(n).call(this,e))).setters=t.createSetters(),t.colorUtils={getMostReadableColor:t.getMostReadableColor.bind(Object(Hr.a)(Object(Hr.a)(t)))},t.state={},t}return Object(Ur.a)(n,t),Object(Dr.a)(n,[{key:"getMostReadableColor",value:function(e){return function(e,t){return Po.a.mostReadable(t,Object(O.map)(e,"color")).toHexString()}(this.props.colors,e)}},{key:"createSetters",value:function(){var e=this;return Object(O.reduce)(r,function(t,n,r){var o=Object(O.upperFirst)(r),i="custom".concat(o);return t["set".concat(o)]=e.createSetColor(r,i),t},{})}},{key:"createSetColor",value:function(e,t){var n=this;return function(r){var o,i=Bo(n.props.colors,r);n.props.setAttributes((o={},Object(f.a)(o,e,i&&i.slug?i.slug:void 0),Object(f.a)(o,t,i&&i.slug?void 0:r),o))}}},{key:"render",value:function(){return Object(Br.createElement)(e,Object(d.a)({},this.props,{colors:void 0},this.state,this.setters,{colorUtils:this.colorUtils}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.attributes,o=e.colors;return Object(O.reduce)(r,function(e,r,i){var c=Io(o,n[i],n["custom".concat(Object(O.upperFirst)(i))]),a=t[i];return Object(O.get)(a,["color"])===c.color&&a?e[i]=a:e[i]=Object(d.a)({},c,{class:xo(r,c.slug)}),e},{})}}]),n}(Br.Component)}]),"withColors")};var No=function(e){var t=e.backgroundColor,n=e.fallbackBackgroundColor,r=e.fallbackTextColor,o=e.fontSize,i=e.isLargeText,c=e.textColor;if(!t&&!n||!c&&!r)return null;var a=Po()(t||n),s=Po()(c||r);if(1!==a.getAlpha()||1!==s.getAlpha()||Po.a.isReadable(a,s,{level:"AA",size:i||!1!==i&&o>=24?"large":"small"}))return null;var l=a.getBrightness()<s.getBrightness()?Object(_.__)("This color combination may be hard for people to read. Try using a darker background color and/or a brighter text color."):Object(_.__)("This color combination may be hard for people to read. Try using a brighter background color and/or a darker text color.");return Object(Br.createElement)("div",{className:"editor-contrast-checker"},Object(Br.createElement)(Vr.Notice,{status:"warning",isDismissible:!1},l))},Ro=function(e,t,n){if(t){var r=Object(O.find)(e,{slug:t});if(r)return r}return{size:n}};function Do(e){if(e)return"has-".concat(Object(O.kebabCase)(e),"-font-size")}var Fo=Object(l.withSelect)(function(e){var t=e("core/editor").getEditorSettings();return{disableCustomFontSizes:t.disableCustomFontSizes,fontSizes:t.fontSizes}})(Vr.FontSizePicker),Mo=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=Object(O.reduce)(t,function(e,t){return e[t]="custom".concat(Object(O.upperFirst)(t)),e},{});return Object(Ar.createHigherOrderComponent)(Object(Ar.compose)([Object(l.withSelect)(function(e){return{fontSizes:e("core/editor").getEditorSettings().fontSizes}}),function(e){return function(t){function n(e){var t;return Object(Rr.a)(this,n),(t=Object(Fr.a)(this,Object(Mr.a)(n).call(this,e))).setters=t.createSetters(),t.state={},t}return Object(Ur.a)(n,t),Object(Dr.a)(n,[{key:"createSetters",value:function(){var e=this;return Object(O.reduce)(r,function(t,n,r){var o=Object(O.upperFirst)(r);return t["set".concat(o)]=e.createSetFontSize(r,n),t},{})}},{key:"createSetFontSize",value:function(e,t){var n=this;return function(r){var o,i=Object(O.find)(n.props.fontSizes,{size:r});n.props.setAttributes((o={},Object(f.a)(o,e,i&&i.slug?i.slug:void 0),Object(f.a)(o,t,i&&i.slug?void 0:r),o))}}},{key:"render",value:function(){return Object(Br.createElement)(e,Object(d.a)({},this.props,{fontSizes:void 0},this.state,this.setters))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.attributes,o=e.fontSizes,i=function(e,r){return!t[r]||(n[r]?n[r]!==t[r].slug:t[r].size!==n[e])};if(!Object(O.some)(r,i))return null;var c=Object(O.reduce)(Object(O.pickBy)(r,i),function(e,t,r){var i=n[r],c=Ro(o,i,n[t]);return e[r]=Object(d.a)({},c,{class:Do(i)}),e},{});return Object(d.a)({},t,c)}}]),n}(Br.Component)}]),"withFontSizes")},Uo=n(40),Ho=n.n(Uo),Vo=n(22);function Ko(e,t,n,r,o,i){var c=n+1;return e>1?function(e,t,n,r,o){var i=t+1;if(o<0&&n)return Object(_.__)("Blocks cannot be moved up as they are already at the top");if(o>0&&r)return Object(_.__)("Blocks cannot be moved down as they are already at the bottom");if(o<0&&!n)return Object(_.sprintf)(Object(_._n)("Move %1$d block from position %2$d up by one place","Move %1$d blocks from position %2$d up by one place",e),e,i);if(o>0&&!r)return Object(_.sprintf)(Object(_._n)("Move %1$d block from position %2$d down by one place","Move %1$d blocks from position %2$d down by one place",e),e,i)}(e,n,r,o,i):r&&o?Object(_.sprintf)(Object(_.__)("Block %s is the only block, and cannot be moved"),t):i>0&&!o?Object(_.sprintf)(Object(_.__)("Move %1$s block from position %2$d down to position %3$d"),t,c,c+1):i>0&&o?Object(_.sprintf)(Object(_.__)("Block %s is at the end of the content and can’t be moved down"),t):i<0&&!r?Object(_.sprintf)(Object(_.__)("Move %1$s block from position %2$d up to position %3$d"),t,c,c-1):i<0&&r?Object(_.sprintf)(Object(_.__)("Block %s is at the beginning of the content and can’t be moved up"),t):void 0}var zo=Object(Br.createElement)(Vr.SVG,{width:"18",height:"18",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},Object(Br.createElement)(Vr.Polygon,{points:"9,4.5 3.3,10.1 4.8,11.5 9,7.3 13.2,11.5 14.7,10.1 "})),Wo=Object(Br.createElement)(Vr.SVG,{width:"18",height:"18",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},Object(Br.createElement)(Vr.Polygon,{points:"9,13.5 14.7,7.9 13.2,6.5 9,10.7 4.8,6.5 3.3,7.9 "})),qo=Object(Br.createElement)(Vr.SVG,{width:"18",height:"18",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},Object(Br.createElement)(Vr.Path,{d:"M13,8c0.6,0,1-0.4,1-1s-0.4-1-1-1s-1,0.4-1,1S12.4,8,13,8z M5,6C4.4,6,4,6.4,4,7s0.4,1,1,1s1-0.4,1-1S5.6,6,5,6z M5,10 c-0.6,0-1,0.4-1,1s0.4,1,1,1s1-0.4,1-1S5.6,10,5,10z M13,10c-0.6,0-1,0.4-1,1s0.4,1,1,1s1-0.4,1-1S13.6,10,13,10z M9,6 C8.4,6,8,6.4,8,7s0.4,1,1,1s1-0.4,1-1S9.6,6,9,6z M9,10c-0.6,0-1,0.4-1,1s0.4,1,1,1s1-0.4,1-1S9.6,10,9,10z"})),Go=Object(l.withSelect)(function(e,t){var n=t.clientId,r=e("core/editor"),o=r.getBlockIndex,i=r.getBlockRootClientId;return{index:o(n),rootClientId:i(n)}})(function(e){var t=e.children,n=e.clientId,r=e.rootClientId,o=e.blockElementId,i=e.index,c=e.onDragStart,a=e.onDragEnd,s={type:"block",srcIndex:i,srcRootClientId:r,srcClientId:n};return Object(Br.createElement)(Vr.Draggable,{elementId:o,transferData:s,onDragStart:c,onDragEnd:a},function(e){var n=e.onDraggableStart,r=e.onDraggableEnd;return t({onDraggableStart:n,onDraggableEnd:r})})}),$o=function(e){var t=e.isVisible,n=e.className,r=e.icon,o=e.onDragStart,i=e.onDragEnd,c=e.blockElementId,a=e.clientId;if(!t)return null;var s=Lr()("editor-block-mover__control-drag-handle",n);return Object(Br.createElement)(Go,{clientId:a,blockElementId:c,onDragStart:o,onDragEnd:i},function(e){var t=e.onDraggableStart,n=e.onDraggableEnd;return Object(Br.createElement)("div",{className:s,"aria-hidden":"true",onDragStart:t,onDragEnd:n,draggable:!0},r)})},Yo=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={isFocused:!1},e.onFocus=e.onFocus.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onBlur=e.onBlur.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onFocus",value:function(){this.setState({isFocused:!0})}},{key:"onBlur",value:function(){this.setState({isFocused:!1})}},{key:"render",value:function(){var e=this.props,t=e.onMoveUp,n=e.onMoveDown,r=e.isFirst,o=e.isLast,i=e.isDraggable,c=e.onDragStart,a=e.onDragEnd,s=e.clientIds,l=e.blockElementId,u=e.blockType,d=e.firstIndex,p=e.isLocked,b=e.instanceId,f=e.isHidden,h=this.state.isFocused,m=Object(O.castArray)(s).length;return p||r&&o?null:Object(Br.createElement)("div",{className:Lr()("editor-block-mover",{"is-visible":h||!f})},Object(Br.createElement)(Vr.IconButton,{className:"editor-block-mover__control",onClick:r?null:t,icon:zo,label:Object(_.__)("Move up"),"aria-describedby":"editor-block-mover__up-description-".concat(b),"aria-disabled":r,onFocus:this.onFocus,onBlur:this.onBlur}),Object(Br.createElement)($o,{className:"editor-block-mover__control",icon:qo,clientId:s,blockElementId:l,isVisible:i,onDragStart:c,onDragEnd:a}),Object(Br.createElement)(Vr.IconButton,{className:"editor-block-mover__control",onClick:o?null:n,icon:Wo,label:Object(_.__)("Move down"),"aria-describedby":"editor-block-mover__down-description-".concat(b),"aria-disabled":o,onFocus:this.onFocus,onBlur:this.onBlur}),Object(Br.createElement)("span",{id:"editor-block-mover__up-description-".concat(b),className:"editor-block-mover__description"},Ko(m,u&&u.title,d,r,o,-1)),Object(Br.createElement)("span",{id:"editor-block-mover__down-description-".concat(b),className:"editor-block-mover__description"},Ko(m,u&&u.title,d,r,o,1)))}}]),t}(Br.Component),Qo=Object(Ar.compose)(Object(l.withSelect)(function(e,t){var n=t.clientIds,r=e("core/editor"),o=r.getBlock,c=r.getBlockIndex,a=r.getTemplateLock,s=r.getBlockRootClientId,l=Object(O.first)(Object(O.castArray)(n)),u=o(l),d=s(Object(O.first)(Object(O.castArray)(n)));return{firstIndex:c(l,d),blockType:u?Object(i.getBlockType)(u.name):null,isLocked:"all"===a(d),rootClientId:d}}),Object(l.withDispatch)(function(e,t){var n=t.clientIds,r=t.rootClientId,o=e("core/editor"),i=o.moveBlocksDown,c=o.moveBlocksUp;return{onMoveDown:Object(O.partial)(i,n,r),onMoveUp:Object(O.partial)(c,n,r)}}),Ar.withInstanceId)(Yo);var Xo=Object(l.withSelect)(function(e){return{hasUploadPermissions:(0,e("core").hasUploadPermissions)()}})(function(e){var t=e.hasUploadPermissions,n=e.fallback,r=void 0===n?null:n,o=e.children;return t?o:r}),Zo=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onFilesDrop=e.onFilesDrop.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onHTMLDrop=e.onHTMLDrop.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onDrop=e.onDrop.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"getInsertIndex",value:function(e){var t=this.props.index;if(void 0!==t)return"top"===e.y?t:t+1}},{key:"onFilesDrop",value:function(e,t){var n=Object(i.findTransform)(Object(i.getBlockTransforms)("from"),function(t){return"files"===t.type&&t.isMatch(e)});if(n){var r=this.getInsertIndex(t),o=n.transform(e,this.props.updateBlockAttributes);this.props.insertBlocks(o,r)}}},{key:"onHTMLDrop",value:function(e,t){var n=Object(i.pasteHandler)({HTML:e,mode:"BLOCKS"});n.length&&this.props.insertBlocks(n,this.getInsertIndex(t))}},{key:"onDrop",value:function(e,t){var n=this.props,r=n.rootClientId,o=n.clientId,i=n.index,c=n.getClientIdsOfDescendants,a=function(e){var t={srcRootClientId:null,srcClientId:null,srcIndex:null,type:null};if(!e.dataTransfer)return t;try{t=Object.assign(t,JSON.parse(e.dataTransfer.getData("text")))}catch(e){return t}return t}(e),s=a.srcRootClientId,l=a.srcClientId,u=a.srcIndex,d=a.type;if("block"===d&&l!==o&&!function(e,t){return c([e]).some(function(e){return e===t})}(l,o)){var p,b,f=this.getInsertIndex(t),h=i&&u<i&&((p=s)===(b=r)||1==!p&&1==!b)?f-1:f;this.props.moveBlockToPosition(l,s,h)}}},{key:"render",value:function(){var e=this.props,t=e.isLocked,n=e.index;if(t)return null;var r=void 0===n;return Object(Br.createElement)(Xo,null,Object(Br.createElement)(Vr.DropZone,{className:Lr()("editor-block-drop-zone",{"is-appender":r}),onFilesDrop:this.onFilesDrop,onHTMLDrop:this.onHTMLDrop,onDrop:this.onDrop}))}}]),t}(Br.Component),Jo=Object(Ar.compose)(Object(l.withDispatch)(function(e,t){var n=e("core/editor"),r=n.insertBlocks,o=n.updateBlockAttributes,i=n.moveBlockToPosition;return{insertBlocks:function(e,n){var o=t.rootClientId;r(e,n,o)},updateBlockAttributes:function(){o.apply(void 0,arguments)},moveBlockToPosition:function(e,n,r){var o=t.rootClientId;i(e,n,o,r)}}}),Object(l.withSelect)(function(e,t){var n=t.rootClientId,r=e("core/editor"),o=r.getClientIdsOfDescendants;return{isLocked:!!(0,r.getTemplateLock)(n),getClientIdsOfDescendants:o}}),Object(Vr.withFilters)("editor.BlockDropZone"))(Zo);var ei=function(e){var t=e.className,n=e.actions,r=e.children,o=e.secondaryActions;return Object(Br.createElement)("div",{className:Lr()(t,"editor-warning")},Object(Br.createElement)("div",{className:"editor-warning__contents"},Object(Br.createElement)("p",{className:"editor-warning__message"},r),Br.Children.count(n)>0&&Object(Br.createElement)("div",{className:"editor-warning__actions"},Br.Children.map(n,function(e,t){return Object(Br.createElement)("span",{key:t,className:"editor-warning__action"},e)}))),o&&Object(Br.createElement)(Vr.Dropdown,{className:"editor-warning__secondary",position:"bottom left",renderToggle:function(e){var t=e.isOpen,n=e.onToggle;return Object(Br.createElement)(Vr.IconButton,{icon:"ellipsis",label:Object(_.__)("More options"),onClick:n,"aria-expanded":t})},renderContent:function(){return Object(Br.createElement)(Vr.MenuGroup,{label:Object(_.__)("More options")},o.map(function(e,t){return Object(Br.createElement)(Vr.MenuItem,{onClick:e.onClick,key:t},e.title)}))}}))},ti=n(191),ni=function(e){var t=e.title,n=e.rawContent,r=e.renderedContent,o=e.action,i=e.actionText,c=e.className;return Object(Br.createElement)("div",{className:c},Object(Br.createElement)("div",{className:"editor-block-compare__content"},Object(Br.createElement)("h2",{className:"editor-block-compare__heading"},t),Object(Br.createElement)("div",{className:"editor-block-compare__html"},n),Object(Br.createElement)("div",{className:"editor-block-compare__preview edit-post-visual-editor"},r)),Object(Br.createElement)("div",{className:"editor-block-compare__action"},Object(Br.createElement)(Vr.Button,{isLarge:!0,tabIndex:"0",onClick:o},i)))},ri=function(e){function t(){return Object(Rr.a)(this,t),Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"getDifference",value:function(e,t){return Object(ti.diffChars)(e,t).map(function(e,t){var n=Lr()({"editor-block-compare__added":e.added,"editor-block-compare__removed":e.removed});return Object(Br.createElement)("span",{key:t,className:n},e.value)})}},{key:"getOriginalContent",value:function(e){return{rawContent:e.originalContent,renderedContent:Object(i.getSaveElement)(e.name,e.attributes)}}},{key:"getConvertedContent",value:function(e){var t=Object(O.castArray)(e),n=t.map(function(e){return Object(i.getSaveContent)(e.name,e.attributes,e.innerBlocks)}),r=t.map(function(e){return Object(i.getSaveElement)(e.name,e.attributes,e.innerBlocks)});return{rawContent:n.join(""),renderedContent:r}}},{key:"render",value:function(){var e=this.props,t=e.block,n=e.onKeep,r=e.onConvert,o=e.convertor,i=e.convertButtonText,c=this.getOriginalContent(t),a=this.getConvertedContent(o(t)),s=this.getDifference(c.rawContent,a.rawContent);return Object(Br.createElement)("div",{className:"editor-block-compare__wrapper"},Object(Br.createElement)(ni,{title:Object(_.__)("Current"),className:"editor-block-compare__current",action:n,actionText:Object(_.__)("Convert to HTML"),rawContent:c.rawContent,renderedContent:c.renderedContent}),Object(Br.createElement)(ni,{title:Object(_.__)("After Conversion"),className:"editor-block-compare__converted",action:r,actionText:i,rawContent:s,renderedContent:a.renderedContent}))}}]),t}(Br.Component),oi=function(e){function t(e){var n;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).call(this,e))).state={compare:!1},n.onCompare=n.onCompare.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onCompareClose=n.onCompareClose.bind(Object(Hr.a)(Object(Hr.a)(n))),n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onCompare",value:function(){this.setState({compare:!0})}},{key:"onCompareClose",value:function(){this.setState({compare:!1})}},{key:"render",value:function(){var e=this.props,t=e.convertToHTML,n=e.convertToBlocks,r=e.convertToClassic,o=e.block,c=!!Object(i.getBlockType)("core/html"),a=this.state.compare,s=[{title:Object(_.__)("Convert to Classic Block"),onClick:r}];return a?Object(Br.createElement)(Vr.Modal,{title:Object(_.__)("Resolve Block"),onRequestClose:this.onCompareClose,className:"editor-block-compare"},Object(Br.createElement)(ri,{block:o,onKeep:t,onConvert:n,convertor:ii,convertButtonText:Object(_.__)("Convert to Blocks")})):Object(Br.createElement)(ei,{actions:[Object(Br.createElement)(Vr.Button,{key:"convert",onClick:this.onCompare,isLarge:!0,isPrimary:!c},Object(_._x)("Resolve","imperative verb")),c&&Object(Br.createElement)(Vr.Button,{key:"edit",onClick:t,isLarge:!0,isPrimary:!0},Object(_.__)("Convert to HTML"))],secondaryActions:s},Object(_.__)("This block contains unexpected or invalid content."))}}]),t}(Br.Component),ii=function(e){return Object(i.rawHandler)({HTML:e.originalContent})},ci=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.clientId;return{block:e("core/editor").getBlock(n)}}),Object(l.withDispatch)(function(e,t){var n=t.block,r=e("core/editor").replaceBlock;return{convertToClassic:function(){r(n.clientId,function(e){return Object(i.createBlock)("core/freeform",{content:e.originalContent})}(n))},convertToHTML:function(){r(n.clientId,function(e){return Object(i.createBlock)("core/html",{content:e.originalContent})}(n))},convertToBlocks:function(){r(n.clientId,ii(n))}}})])(oi),ai=Object(Br.createElement)(ei,null,Object(_.__)("This block has encountered an error and cannot be previewed.")),si=function(){return ai},li=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={hasError:!1},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidCatch",value:function(e){this.props.onError(e),this.setState({hasError:!0})}},{key:"render",value:function(){return this.state.hasError?null:this.props.children}}]),t}(Br.Component),ui=n(55),di=n.n(ui),pi=function(e){function t(e){var n;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onChange=n.onChange.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onBlur=n.onBlur.bind(Object(Hr.a)(Object(Hr.a)(n))),n.state={html:e.block.isValid?Object(i.getBlockContent)(e.block):e.block.originalContent},n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidUpdate",value:function(e){Object(O.isEqual)(this.props.block.attributes,e.block.attributes)||this.setState({html:Object(i.getBlockContent)(this.props.block)})}},{key:"onBlur",value:function(){var e=this.state.html,t=Object(i.getBlockType)(this.props.block.name),n=Object(i.getBlockAttributes)(t,e,this.props.block.attributes),r=e||Object(i.getSaveContent)(t,n),o=!e||Object(i.isValidBlockContent)(t,n,r);this.props.onChange(this.props.clientId,n,r,o),e||this.setState({html:r})}},{key:"onChange",value:function(e){this.setState({html:e.target.value})}},{key:"render",value:function(){var e=this.state.html;return Object(Br.createElement)(di.a,{className:"editor-block-list__block-html-textarea",value:e,onBlur:this.onBlur,onChange:this.onChange})}}]),t}(Br.Component),bi=Object(Ar.compose)([Object(l.withSelect)(function(e,t){return{block:e("core/editor").getBlock(t.clientId)}}),Object(l.withDispatch)(function(e){return{onChange:function(t,n,r,o){e("core/editor").updateBlock(t,{attributes:n,originalContent:r,isValid:o})}}})])(pi);var fi=Object(l.withSelect)(function(e,t){return{name:(0,e("core/editor").getBlockName)(t.clientId)}})(function(e){var t=e.name;if(!t)return null;var n=Object(i.getBlockType)(t);return n?n.title:null}),hi=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={isFocused:!1},e.onFocus=e.onFocus.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onBlur=e.onBlur.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onFocus",value:function(e){this.setState({isFocused:!0}),e.stopPropagation()}},{key:"onBlur",value:function(){this.setState({isFocused:!1})}},{key:"render",value:function(){var e=this.props,t=e.clientId,n=e.rootClientId;return Object(Br.createElement)("div",{className:"editor-block-list__breadcrumb"},Object(Br.createElement)(Vr.Toolbar,null,n&&Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(fi,{clientId:n}),Object(Br.createElement)("span",{className:"editor-block-list__descendant-arrow"})),Object(Br.createElement)(fi,{clientId:t})))}}]),t}(Br.Component),mi=Object(Ar.compose)([Object(l.withSelect)(function(e,t){return{rootClientId:(0,e("core/editor").getBlockRootClientId)(t.clientId)}})])(hi),vi=window,Oi=vi.Node,gi=vi.getSelection,ji=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).focusToolbar=e.focusToolbar.bind(Object(Hr.a)(Object(Hr.a)(e))),e.focusSelection=e.focusSelection.bind(Object(Hr.a)(Object(Hr.a)(e))),e.switchOnKeyDown=Object(O.cond)([[Object(O.matchesProperty)(["keyCode"],yo.ESCAPE),e.focusSelection]]),e.toolbar=Object(Br.createRef)(),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"focusToolbar",value:function(){var e=Vo.focus.tabbable.find(this.toolbar.current);e.length&&e[0].focus()}},{key:"focusSelection",value:function(){var e=gi();if(e){var t=e.focusNode;t.nodeType!==Oi.ELEMENT_NODE&&(t=t.parentElement),t&&t.focus()}}},{key:"componentDidMount",value:function(){this.props.focusOnMount&&this.focusToolbar()}},{key:"render",value:function(){var e=this.props,t=e.children,n=Object(p.a)(e,["children"]);return Object(Br.createElement)(Vr.NavigableMenu,Object(Ir.a)({orientation:"horizontal",role:"toolbar",ref:this.toolbar,onKeyDown:this.switchOnKeyDown},Object(O.omit)(n,["focusOnMount"])),Object(Br.createElement)(Vr.KeyboardShortcuts,{bindGlobal:!0,eventName:"keydown",shortcuts:{"alt+f10":this.focusToolbar}}),t)}}]),t}(Br.Component);var yi=function(e){var t=e.focusOnMount;return Object(Br.createElement)(ji,{focusOnMount:t,className:"editor-block-contextual-toolbar","aria-label":Object(_.__)("Block tools")},Object(Br.createElement)(ql,null))};var ki=Object(l.withSelect)(function(e,t){var n=t.clientId,r=e("core/editor"),o=r.getMultiSelectedBlockClientIds,i=r.isMultiSelecting,c=r.getBlockIndex,a=r.getBlockCount,s=o(),l=c(Object(O.first)(s),n),u=c(Object(O.last)(s),n);return{multiSelectedBlockClientIds:s,isSelecting:i(),isFirst:0===l,isLast:u+1===a()}})(function(e){var t=e.multiSelectedBlockClientIds,n=e.clientId,r=e.isSelecting,o=e.isFirst,i=e.isLast;return r?null:Object(Br.createElement)(Qo,{key:"mover",clientId:n,clientIds:t,isFirst:o,isLast:i})}),_i=n(59),Ei=n.n(_i);function Si(e){var t=e.name,n=e.attributes,r=Object(i.createBlock)(t,n);return Object(Br.createElement)(Vr.Disabled,{className:"editor-block-preview__content editor-styles-wrapper","aria-hidden":!0},Object(Br.createElement)(ho,{name:t,focus:!1,attributes:r.attributes,setAttributes:O.noop}))}var Ci=function(e){return Object(Br.createElement)("div",{className:"editor-block-preview"},Object(Br.createElement)("div",{className:"editor-block-preview__title"},Object(_.__)("Preview")),Object(Br.createElement)(Si,e))};var wi=function(e){var t=e.icon,n=e.hasChildBlocksWithInserterSupport,r=e.onClick,o=e.isDisabled,i=e.title,c=e.className,a=Object(p.a)(e,["icon","hasChildBlocksWithInserterSupport","onClick","isDisabled","title","className"]),s=t?{backgroundColor:t.background,color:t.foreground}:{},l=t&&t.shadowColor?{backgroundColor:t.shadowColor}:{};return Object(Br.createElement)("li",{className:"editor-block-types-list__list-item"},Object(Br.createElement)("button",Object(Ir.a)({className:Lr()("editor-block-types-list__item",c,{"editor-block-types-list__item-has-children":n}),onClick:function(e){e.preventDefault(),r()},disabled:o,"aria-label":i},a),Object(Br.createElement)("span",{className:"editor-block-types-list__item-icon",style:s},Object(Br.createElement)(Qr,{icon:t,showColors:!0}),n&&Object(Br.createElement)("span",{className:"editor-block-types-list__item-icon-stack",style:l})),Object(Br.createElement)("span",{className:"editor-block-types-list__item-title"},i)))};var Ti=function(e){var t=e.items,n=e.onSelect,r=e.onHover,o=void 0===r?function(){}:r,c=e.children;return Object(Br.createElement)("ul",{role:"list",className:"editor-block-types-list"},t&&t.map(function(e){return Object(Br.createElement)(wi,{key:e.id,className:Object(i.getBlockMenuDefaultClassName)(e.id),icon:e.icon,hasChildBlocksWithInserterSupport:e.hasChildBlocksWithInserterSupport,onClick:function(){n(e),o(null)},onFocus:function(){return o(e)},onMouseEnter:function(){return o(e)},onMouseLeave:function(){return o(null)},onBlur:function(){return o(null)},isDisabled:e.isDisabled,title:e.title})}),c)};var Pi=Object(Ar.compose)(Object(Ar.ifCondition)(function(e){var t=e.items;return t&&t.length>0}),Object(l.withSelect)(function(e,t){var n=t.rootClientId,r=(0,e("core/blocks").getBlockType)((0,e("core/editor").getBlockName)(n));return{rootBlockTitle:r&&r.title,rootBlockIcon:r&&r.icon}}))(function(e){var t=e.rootBlockIcon,n=e.rootBlockTitle,r=e.items,o=Object(p.a)(e,["rootBlockIcon","rootBlockTitle","items"]);return Object(Br.createElement)("div",{className:"editor-inserter__child-blocks"},(t||n)&&Object(Br.createElement)("div",{className:"editor-inserter__parent-block-header"},Object(Br.createElement)(Qr,{icon:t,showColors:!0}),n&&Object(Br.createElement)("h2",null,n)),Object(Br.createElement)(Ti,Object(Ir.a)({items:r},o)))}),Ii=function(e){var t=e.filterValue;return Object(Br.createElement)(Vr.Slot,{name:"Inserter.InlineElements",fillProps:{filterValue:t}},function(e){return!Object(O.isEmpty)(e)&&Object(Br.createElement)(Vr.PanelBody,{title:Object(_.__)("Inline Elements"),initialOpen:!1,className:"editor-inserter__inline-elements"},Object(Br.createElement)(Ti,null,e))})},Bi=function(e){return e.stopPropagation()},xi=function(e){return e=(e=(e=(e=Object(O.deburr)(e)).replace(/^\//,"")).toLowerCase()).trim()},Li=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={childItems:[],filterValue:"",hoveredItem:null,suggestedItems:[],reusableItems:[],itemsPerCategory:{},openPanels:["suggested"]},e.onChangeSearchInput=e.onChangeSearchInput.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onHover=e.onHover.bind(Object(Hr.a)(Object(Hr.a)(e))),e.panels={},e.inserterResults=Object(Br.createRef)(),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){this.props.fetchReusableBlocks(),this.filter()}},{key:"componentDidUpdate",value:function(e){e.items!==this.props.items&&this.filter(this.state.filterValue)}},{key:"onChangeSearchInput",value:function(e){this.filter(e.target.value)}},{key:"onHover",value:function(e){this.setState({hoveredItem:e});var t=this.props,n=t.showInsertionPoint,r=t.hideInsertionPoint;if(e){var o=this.props;n(o.rootClientId,o.index)}else r()}},{key:"bindPanel",value:function(e){var t=this;return function(n){t.panels[e]=n}}},{key:"onTogglePanel",value:function(e){var t=this;return function(){-1!==t.state.openPanels.indexOf(e)?t.setState({openPanels:Object(O.without)(t.state.openPanels,e)}):(t.setState({openPanels:Object(b.a)(t.state.openPanels).concat([e])}),t.props.setTimeout(function(){Ei()(t.panels[e],t.inserterResults.current,{alignWithTop:!0})}))}}},{key:"filter",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=this.props,n=t.debouncedSpeak,r=t.items,o=t.rootChildBlocks,c=function(e,t){var n=xi(t),r=function(e){return-1!==xi(e).indexOf(n)},o=Object(i.getCategories)();return e.filter(function(e){var t=Object(O.find)(o,{slug:e.category});return r(e.title)||Object(O.some)(e.keywords,r)||t&&r(t.title)})}(r,e),a=Object(O.filter)(c,function(e){var t=e.name;return Object(O.includes)(o,t)}),s=[];if(!e){var l=this.props.maxSuggestedItems||9;s=Object(O.filter)(r,function(e){return e.utility>0}).slice(0,l)}var u=Object(O.filter)(c,{category:"reusable"}),d=function(e){return Object(O.findIndex)(Object(i.getCategories)(),function(t){return t.slug===e.category})},p=Object(O.flow)(function(e){return Object(O.filter)(e,function(e){return"reusable"!==e.category})},function(e){return Object(O.sortBy)(e,d)},function(e){return Object(O.groupBy)(e,"category")})(c),b=this.state.openPanels;if(e!==this.state.filterValue)if(e){if(u.length)b=["reusable"];else if(c.length){b=[Object(O.find)(Object(i.getCategories)(),function(e){var t=e.slug;return p[t]&&p[t].length}).slug]}}else b=["suggested"];this.setState({hoveredItem:null,childItems:a,filterValue:e,suggestedItems:s,reusableItems:u,itemsPerCategory:p,openPanels:b});var f=Object.keys(p).reduce(function(e,t){return e+p[t].length},0);n(Object(_.sprintf)(Object(_._n)("%d result found.","%d results found.",f),f),"assertive")}},{key:"onKeyDown",value:function(e){Object(O.includes)([yo.LEFT,yo.DOWN,yo.RIGHT,yo.UP,yo.BACKSPACE,yo.ENTER],e.keyCode)&&e.stopPropagation()}},{key:"render",value:function(){var e=this,t=this.props,n=t.instanceId,r=t.onSelect,o=t.rootClientId,c=this.state,a=c.childItems,s=c.filterValue,l=c.hoveredItem,u=c.suggestedItems,d=c.reusableItems,p=c.itemsPerCategory,b=c.openPanels,f=function(e){return-1!==b.indexOf(e)},h=!!s;return Object(Br.createElement)("div",{className:"editor-inserter__menu",onKeyPress:Bi,onKeyDown:this.onKeyDown},Object(Br.createElement)("label",{htmlFor:"editor-inserter__search-".concat(n),className:"screen-reader-text"},Object(_.__)("Search for a block")),Object(Br.createElement)("input",{id:"editor-inserter__search-".concat(n),type:"search",placeholder:Object(_.__)("Search for a block"),className:"editor-inserter__search",autoFocus:!0,onChange:this.onChangeSearchInput}),Object(Br.createElement)("div",{className:"editor-inserter__results",ref:this.inserterResults,tabIndex:"0",role:"region","aria-label":Object(_.__)("Available block types")},Object(Br.createElement)(Pi,{rootClientId:o,items:a,onSelect:r,onHover:this.onHover}),!!u.length&&Object(Br.createElement)(Vr.PanelBody,{title:Object(_._x)("Most Used","blocks"),opened:f("suggested"),onToggle:this.onTogglePanel("suggested"),ref:this.bindPanel("suggested")},Object(Br.createElement)(Ti,{items:u,onSelect:r,onHover:this.onHover})),Object(Br.createElement)(Ii,{filterValue:s}),Object(O.map)(Object(i.getCategories)(),function(t){var n=p[t.slug];return n&&n.length?Object(Br.createElement)(Vr.PanelBody,{key:t.slug,title:t.title,icon:t.icon,opened:h||f(t.slug),onToggle:e.onTogglePanel(t.slug),ref:e.bindPanel(t.slug)},Object(Br.createElement)(Ti,{items:n,onSelect:r,onHover:e.onHover})):null}),!!d.length&&Object(Br.createElement)(Vr.PanelBody,{className:"editor-inserter__reusable-blocks-panel",title:Object(_.__)("Reusable"),opened:f("reusable"),onToggle:this.onTogglePanel("reusable"),icon:"controls-repeat",ref:this.bindPanel("reusable")},Object(Br.createElement)(Ti,{items:d,onSelect:r,onHover:this.onHover}),Object(Br.createElement)("a",{className:"editor-inserter__manage-reusable-blocks",href:"edit.php?post_type=wp_block"},Object(_.__)("Manage All Reusable Blocks"))),Object(O.isEmpty)(u)&&Object(O.isEmpty)(d)&&Object(O.isEmpty)(p)&&Object(Br.createElement)("p",{className:"editor-inserter__no-results"},Object(_.__)("No blocks found."))),l&&Object(i.isReusableBlock)(l)&&Object(Br.createElement)(Ci,{name:l.name,attributes:l.initialAttributes}))}}]),t}(Br.Component),Ai=Object(Ar.compose)(Object(l.withSelect)(function(e,t){var n=t.rootClientId,r=e("core/editor"),o=r.getEditedPostAttribute,i=r.getSelectedBlock,c=r.getInserterItems,a=r.getBlockName,s=e("core/blocks").getChildBlockNames,l=a(n);return{selectedBlock:i(),rootChildBlocks:s(l),title:o("title"),items:c(n),rootClientId:n}}),Object(l.withDispatch)(function(e,t){var n=e("core/editor");return{fetchReusableBlocks:n.__experimentalFetchReusableBlocks,showInsertionPoint:n.showInsertionPoint,hideInsertionPoint:n.hideInsertionPoint,onSelect:function(n){var r=e("core/editor"),o=r.replaceBlocks,c=r.insertBlock,a=t.selectedBlock,s=t.index,l=t.rootClientId,u=n.name,d=n.initialAttributes,p=Object(i.createBlock)(u,d);a&&Object(i.isUnmodifiedDefaultBlock)(a)?o(a.clientId,p):c(p,s,l),t.onSelect()}}}),Vr.withSpokenMessages,Ar.withInstanceId,Ar.withSafeTimeout)(Li),Ni=function(e){var t=e.onToggle,n=e.disabled,r=e.isOpen;return Object(Br.createElement)(Vr.IconButton,{icon:"insert",label:Object(_.__)("Add block"),labelPosition:"bottom",onClick:t,className:"editor-inserter__toggle","aria-haspopup":"true","aria-expanded":r,disabled:n})},Ri=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onToggle=e.onToggle.bind(Object(Hr.a)(Object(Hr.a)(e))),e.renderToggle=e.renderToggle.bind(Object(Hr.a)(Object(Hr.a)(e))),e.renderContent=e.renderContent.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onToggle",value:function(e){var t=this.props.onToggle;t&&t(e)}},{key:"renderToggle",value:function(e){var t=e.onToggle,n=e.isOpen,r=this.props,o=r.disabled,i=r.renderToggle,c=void 0===i?Ni:i;return c({onToggle:t,isOpen:n,disabled:o})}},{key:"renderContent",value:function(e){var t=e.onClose,n=this.props,r=n.rootClientId,o=n.index;return Object(Br.createElement)(Ai,{onSelect:t,rootClientId:r,index:o})}},{key:"render",value:function(){var e=this.props,t=e.position,n=e.title;return Object(Br.createElement)(Vr.Dropdown,{className:"editor-inserter",contentClassName:"editor-inserter__popover",position:t,onToggle:this.onToggle,expandOnMobile:!0,headerTitle:n,renderToggle:this.renderToggle,renderContent:this.renderContent})}}]),t}(Br.Component),Di=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.rootClientId,r=t.index,o=e("core/editor"),i=o.getEditedPostAttribute,c=o.getBlockInsertionPoint,a=o.hasInserterItems;if(void 0===n&&void 0===r){var s=c();n=s.rootClientId,r=s.index}return{title:i("title"),hasItems:a(n),rootClientId:n,index:r}}),Object(Ar.ifCondition)(function(e){return e.hasItems})])(Ri);var Fi=Object(s.ifViewportMatches)("< small")(function(e){var t=e.clientId;return Object(Br.createElement)("div",{className:"editor-block-list__block-mobile-toolbar"},Object(Br.createElement)(Di,null),Object(Br.createElement)(Qo,{clientIds:[t]}))}),Mi=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={isInserterFocused:!1},e.onBlurInserter=e.onBlurInserter.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onFocusInserter=e.onFocusInserter.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onFocusInserter",value:function(e){e.stopPropagation(),this.setState({isInserterFocused:!0})}},{key:"onBlurInserter",value:function(){this.setState({isInserterFocused:!1})}},{key:"render",value:function(){var e=this.state.isInserterFocused,t=this.props,n=t.showInsertionPoint,r=t.rootClientId,o=t.insertIndex;return Object(Br.createElement)("div",{className:"editor-block-list__insertion-point"},n&&Object(Br.createElement)("div",{className:"editor-block-list__insertion-point-indicator"}),Object(Br.createElement)("div",{onFocus:this.onFocusInserter,onBlur:this.onBlurInserter,tabIndex:-1,className:Lr()("editor-block-list__insertion-point-inserter",{"is-visible":e})},Object(Br.createElement)(Di,{rootClientId:r,index:o})))}}]),t}(Br.Component),Ui=Object(l.withSelect)(function(e,t){var n=t.clientId,r=t.rootClientId,o=e("core/editor"),i=o.getBlockIndex,c=o.getBlockInsertionPoint,a=o.isBlockInsertionPointVisible,s=i(n,r),l=c();return{showInsertionPoint:a()&&l.index===s&&l.rootClientId===r,insertIndex:s}})(Mi),Hi=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).proxyEvent=e.proxyEvent.bind(Object(Hr.a)(Object(Hr.a)(e))),e.eventMap={},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"proxyEvent",value:function(e){var t=!!e.nativeEvent._blockHandled;e.nativeEvent._blockHandled=!0;var n=this.eventMap[e.type];t&&(n+="Handled"),this.props[n]&&this.props[n](e)}},{key:"render",value:function(){var e=this,t=this.props,n=t.childHandledEvents,r=void 0===n?[]:n,o=t.forwardedRef,i=Object(p.a)(t,["childHandledEvents","forwardedRef"]),c=Object(O.reduce)(Object(b.a)(r).concat(Object(b.a)(Object.keys(i))),function(t,n){var r=n.match(/^on([A-Z][a-zA-Z]+?)(Handled)?$/);if(r){!!r[2]&&delete i[n];var o="on"+r[1];t[o]=e.proxyEvent,e.eventMap[r[1].toLowerCase()]=o}return t},{});return Object(Br.createElement)("div",Object(Ir.a)({ref:o},i,c))}}]),t}(Br.Component),Vi=function(e,t){return Object(Br.createElement)(Hi,Object(Ir.a)({},e,{forwardedRef:t}))};Vi.displayName="IgnoreNestedEvents";var Ki=Object(Br.forwardRef)(Vi);var zi=Object(Ar.compose)(Object(l.withSelect)(function(e,t){var n=t.rootClientId,r=e("core/editor"),o=r.getInserterItems,i=r.getTemplateLock;return{items:o(n),isLocked:!!i(n)}}),Object(l.withDispatch)(function(e,t){var n=t.clientId,r=t.rootClientId;return{onInsert:function(t){var o=t.name,c=t.initialAttributes,a=Object(i.createBlock)(o,c);n?e("core/editor").replaceBlocks(n,a):e("core/editor").insertBlock(a,void 0,r)}}}))(function(e){var t=e.items,n=e.isLocked,r=e.onInsert;if(n)return null;var o=Object(O.filter)(t,function(e){return!(e.isDisabled||e.name===Object(i.getDefaultBlockName)()&&Object(O.isEmpty)(e.initialAttributes))}).slice(0,3);return Object(Br.createElement)("div",{className:"editor-inserter-with-shortcuts"},o.map(function(e){return Object(Br.createElement)(Vr.IconButton,{key:e.id,className:"editor-inserter-with-shortcuts__block",onClick:function(){return r(e)},label:Object(_.sprintf)(Object(_.__)("Add %s"),e.title),icon:Object(Br.createElement)(Qr,{icon:e.icon})})}))}),Wi=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={hoverArea:null},e.onMouseLeave=e.onMouseLeave.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onMouseMove=e.onMouseMove.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentWillUnmount",value:function(){this.props.container&&this.toggleListeners(this.props.container,!1)}},{key:"componentDidMount",value:function(){this.props.container&&this.toggleListeners(this.props.container)}},{key:"componentDidUpdate",value:function(e){e.container!==this.props.container&&(e.container&&this.toggleListeners(e.container,!1),this.props.container&&this.toggleListeners(this.props.container,!0))}},{key:"toggleListeners",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?"addEventListener":"removeEventListener";e[t]("mousemove",this.onMouseMove),e[t]("mouseleave",this.onMouseLeave)}},{key:"onMouseLeave",value:function(){this.state.hoverArea&&this.setState({hoverArea:null})}},{key:"onMouseMove",value:function(e){var t=this.props,n=t.isRTL,r=t.container.getBoundingClientRect(),o=r.width,i=r.left,c=r.right,a=null;e.clientX-i<o/3?a=n?"right":"left":c-e.clientX<o/3&&(a=n?"left":"right"),a!==this.state.hoverArea&&this.setState({hoverArea:a})}},{key:"render",value:function(){var e=this.state.hoverArea;return(0,this.props.children)({hoverArea:e})}}]),t}(Br.Component),qi=Object(l.withSelect)(function(e){return{isRTL:e("core/editor").getEditorSettings().isRTL}})(Wi);function Gi(e){return document.querySelector('[data-block="'+e+'"]')}var $i=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).setBlockListRef=e.setBlockListRef.bind(Object(Hr.a)(Object(Hr.a)(e))),e.bindBlockNode=e.bindBlockNode.bind(Object(Hr.a)(Object(Hr.a)(e))),e.setAttributes=e.setAttributes.bind(Object(Hr.a)(Object(Hr.a)(e))),e.maybeHover=e.maybeHover.bind(Object(Hr.a)(Object(Hr.a)(e))),e.forceFocusedContextualToolbar=e.forceFocusedContextualToolbar.bind(Object(Hr.a)(Object(Hr.a)(e))),e.hideHoverEffects=e.hideHoverEffects.bind(Object(Hr.a)(Object(Hr.a)(e))),e.insertBlocksAfter=e.insertBlocksAfter.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onFocus=e.onFocus.bind(Object(Hr.a)(Object(Hr.a)(e))),e.preventDrag=e.preventDrag.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onPointerDown=e.onPointerDown.bind(Object(Hr.a)(Object(Hr.a)(e))),e.deleteOrInsertAfterWrapper=e.deleteOrInsertAfterWrapper.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onBlockError=e.onBlockError.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onTouchStart=e.onTouchStart.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onClick=e.onClick.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onDragStart=e.onDragStart.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onDragEnd=e.onDragEnd.bind(Object(Hr.a)(Object(Hr.a)(e))),e.selectOnOpen=e.selectOnOpen.bind(Object(Hr.a)(Object(Hr.a)(e))),e.hadTouchStart=!1,e.state={error:null,dragging:!1,isHovered:!1},e.isForcingContextualToolbar=!1,e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){this.props.isSelected&&this.focusTabbable()}},{key:"componentDidUpdate",value:function(e){this.isForcingContextualToolbar&&(this.isForcingContextualToolbar=!1),(this.props.isTypingWithinBlock||this.props.isSelected)&&this.hideHoverEffects(),this.props.isSelected&&!e.isSelected&&this.focusTabbable(!0),this.props.isFirstMultiSelected&&!e.isFirstMultiSelected&&this.wrapperNode.focus()}},{key:"setBlockListRef",value:function(e){this.wrapperNode=e,this.props.blockRef(e,this.props.clientId),this.forceUpdate()}},{key:"bindBlockNode",value:function(e){this.node=e}},{key:"focusTabbable",value:function(e){var t=this,n=this.props.initialPosition;if(!this.wrapperNode.contains(document.activeElement)){var r=Vo.focus.tabbable.find(this.node).filter(Vo.isTextField).filter(function(n){return!e||(r=t.node,o=n,i=r.querySelector(".editor-block-list__layout"),r.contains(o)&&(!i||!i.contains(o)));var r,o,i}),o=-1===n,i=(o?O.last:O.first)(r);i?(i.focus(),o&&(Object(Vo.placeCaretAtHorizontalEdge)(i,!0),Object(Vo.placeCaretAtVerticalEdge)(i,!0))):this.wrapperNode.focus()}}},{key:"setAttributes",value:function(e){var t=this.props,n=t.clientId,r=t.name,o=t.onChange,c=Object(i.getBlockType)(r);o(n,e);var a=Object(O.reduce)(e,function(e,t,n){return"meta"===Object(O.get)(c,["attributes",n,"source"])&&(e[c.attributes[n].meta]=t),e},{});Object(O.size)(a)&&this.props.onMetaChange(a)}},{key:"onTouchStart",value:function(){this.hadTouchStart=!0}},{key:"onClick",value:function(){this.hadTouchStart=!1}},{key:"maybeHover",value:function(){var e=this.props,t=e.isPartOfMultiSelection,n=e.isSelected;this.state.isHovered||t||n||this.props.isMultiSelecting||this.hadTouchStart||this.setState({isHovered:!0})}},{key:"hideHoverEffects",value:function(){this.state.isHovered&&this.setState({isHovered:!1})}},{key:"insertBlocksAfter",value:function(e){this.props.onInsertBlocks(e,this.props.order+1)}},{key:"onFocus",value:function(){this.props.isSelected||this.props.isPartOfMultiSelection||this.props.onSelect()}},{key:"preventDrag",value:function(e){e.preventDefault()}},{key:"onPointerDown",value:function(e){0===e.button&&(e.shiftKey?this.props.isSelected||(this.props.onShiftSelection(),e.preventDefault()):(this.props.onSelectionStart(this.props.clientId),this.props.isPartOfMultiSelection&&this.props.onSelect()))}},{key:"deleteOrInsertAfterWrapper",value:function(e){var t=e.keyCode,n=e.target;if(this.props.isSelected&&n===this.wrapperNode&&!this.props.isLocked)switch(t){case yo.ENTER:this.props.onInsertDefaultBlockAfter(),e.preventDefault();break;case yo.BACKSPACE:case yo.DELETE:var r=this.props,o=r.clientId;(0,r.onRemove)(o),e.preventDefault()}}},{key:"onBlockError",value:function(e){this.setState({error:e})}},{key:"onDragStart",value:function(){this.setState({dragging:!0})}},{key:"onDragEnd",value:function(){this.setState({dragging:!1})}},{key:"selectOnOpen",value:function(e){e&&!this.props.isSelected&&this.props.onSelect()}},{key:"forceFocusedContextualToolbar",value:function(){this.isForcingContextualToolbar=!0,this.setState(function(){return{}})}},{key:"render",value:function(){var e=this;return Object(Br.createElement)(qi,{container:this.wrapperNode},function(t){var n=t.hoverArea,r=e.props,o=r.order,c=r.mode,a=r.isFocusMode,s=r.hasFixedToolbar,l=r.isLocked,u=r.isFirst,p=r.isLast,b=r.clientId,f=r.rootClientId,h=r.isSelected,m=r.isPartOfMultiSelection,v=r.isFirstMultiSelected,O=r.isTypingWithinBlock,g=r.isCaretWithinFormattedText,j=r.isMultiSelecting,y=r.isEmptyDefaultBlock,k=r.isMovable,E=r.isParentOfSelectedBlock,S=r.isDraggable,C=r.className,w=r.name,T=r.isValid,P=r.attributes,I=e.state.isHovered&&!j,B=Object(i.getBlockType)(w),x=Object(_.sprintf)(Object(_.__)("Block: %s"),B.title),L=w===Object(i.getUnregisteredTypeHandlerName)(),A=(h||I)&&y&&T,N=(h||I)&&y,R=!a&&!N&&h&&!O,D=!a&&!s&&I&&!y,F=!a&&(h||"left"===n)&&!A&&!j&&!m&&!O,M=!a&&I&&!y,U=!s&&!N&&(h&&(!O||g)||v),H=R,V=e.state,K=V.error,z=V.dragging,W=m&&v||!m,q=Lr()("wp-block editor-block-list__block",{"has-warning":!T||!!K||L,"is-selected":R,"is-multi-selected":m,"is-hovered":D,"is-reusable":Object(i.isReusableBlock)(B),"is-dragging":z,"is-typing":O,"is-focused":a&&(h||E),"is-focus-mode":a},C),G=e.props.onReplace,$=e.props.wrapperProps;B.getEditWrapperProps&&($=Object(d.a)({},$,B.getEditWrapperProps(P)));var Y="block-".concat(b),Q=Object(Br.createElement)(ho,{name:w,isSelected:h,attributes:P,setAttributes:e.setAttributes,insertBlocksAfter:l?void 0:e.insertBlocksAfter,onReplace:l?void 0:G,mergeBlocks:l?void 0:e.props.onMerge,clientId:b,isSelectionEnabled:e.props.isSelectionEnabled,toggleSelection:e.props.toggleSelection});return"visual"!==c&&(Q=Object(Br.createElement)("div",{style:{display:"none"}},Q)),Object(Br.createElement)(Ki,Object(Ir.a)({id:Y,ref:e.setBlockListRef,onMouseOver:e.maybeHover,onMouseOverHandled:e.hideHoverEffects,onMouseLeave:e.hideHoverEffects,className:q,"data-type":w,onTouchStart:e.onTouchStart,onFocus:e.onFocus,onClick:e.onClick,onKeyDown:e.deleteOrInsertAfterWrapper,tabIndex:"0","aria-label":x,childHandledEvents:["onDragStart","onMouseDown"]},$),W&&Object(Br.createElement)(Ui,{clientId:b,rootClientId:f}),Object(Br.createElement)(Jo,{index:o,clientId:b,rootClientId:f}),F&&Object(Br.createElement)(Qo,{clientIds:b,blockElementId:Y,isFirst:u,isLast:p,isHidden:!(I||h)||"left"!==n,isDraggable:!1!==S&&!m&&k,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd}),v&&Object(Br.createElement)(ki,{rootClientId:f}),Object(Br.createElement)("div",{className:"editor-block-list__block-edit"},M&&Object(Br.createElement)(mi,{clientId:b,isHidden:!(I||h)||"left"!==n}),(U||e.isForcingContextualToolbar)&&Object(Br.createElement)(yi,{focusOnMount:e.isForcingContextualToolbar}),!U&&h&&!s&&!y&&Object(Br.createElement)(Vr.KeyboardShortcuts,{bindGlobal:!0,eventName:"keydown",shortcuts:{"alt+f10":e.forceFocusedContextualToolbar}}),Object(Br.createElement)(Ki,{ref:e.bindBlockNode,onDragStart:e.preventDrag,onMouseDown:e.onPointerDown,"data-block":b},Object(Br.createElement)(li,{onError:e.onBlockError},T&&Q,T&&"html"===c&&Object(Br.createElement)(bi,{clientId:b}),!T&&[Object(Br.createElement)(ci,{key:"invalid-warning",clientId:b}),Object(Br.createElement)("div",{key:"invalid-preview"},Object(i.getSaveElement)(B,P))]),H&&Object(Br.createElement)(Fi,{clientId:b}),!!K&&Object(Br.createElement)(si,null))),A&&Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)("div",{className:"editor-block-list__side-inserter"},Object(Br.createElement)(zi,{clientId:b,rootClientId:f,onToggle:e.selectOnOpen})),Object(Br.createElement)("div",{className:"editor-block-list__empty-block-inserter"},Object(Br.createElement)(Di,{position:"top right",onToggle:e.selectOnOpen}))))})}}]),t}(Br.Component),Yi=Object(l.withSelect)(function(e,t){var n=t.clientId,r=t.rootClientId,o=t.isLargeViewport,c=e("core/editor"),a=c.isBlockSelected,s=c.isAncestorMultiSelected,l=c.isBlockMultiSelected,u=c.isFirstMultiSelectedBlock,d=c.isMultiSelecting,p=c.isTyping,b=c.isCaretWithinFormattedText,f=c.getBlockIndex,h=c.getBlockMode,m=c.isSelectionEnabled,v=c.getSelectedBlocksInitialCaretPosition,O=c.getEditorSettings,g=c.hasSelectedInnerBlock,j=c.getTemplateLock,y=(0,c.__unstableGetBlockWithoutInnerBlocks)(n),k=a(n),_=O(),E=_.hasFixedToolbar,S=_.focusMode,C=j(r),w=g(n,!0),T=y||{},P=T.name,I=T.attributes,B=T.isValid;return{isPartOfMultiSelection:l(n)||s(n),isFirstMultiSelected:u(n),isMultiSelecting:d(),isTypingWithinBlock:(k||w)&&p(),isCaretWithinFormattedText:b(),order:f(n,r),mode:h(n),isSelectionEnabled:m(),initialPosition:v(),isEmptyDefaultBlock:P&&Object(i.isUnmodifiedDefaultBlock)({name:P,attributes:I}),isMovable:"all"!==C,isLocked:!!C,isFocusMode:S&&o,hasFixedToolbar:E&&o,block:y,name:P,attributes:I,isValid:B,isSelected:k,isParentOfSelectedBlock:w}}),Qi=Object(l.withDispatch)(function(e,t,n){var r=n.select,o=r("core/editor").getBlockSelectionStart,i=e("core/editor"),c=i.updateBlockAttributes,a=i.selectBlock,s=i.multiSelect,l=i.insertBlocks,u=i.insertDefaultBlock,d=i.removeBlock,p=i.mergeBlocks,b=i.replaceBlocks,f=i.editPost,h=i.toggleSelection;return{onChange:function(e,t){c(e,t)},onSelect:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t.clientId,n=arguments.length>1?arguments[1]:void 0;a(e,n)},onInsertBlocks:function(e,n){var r=t.rootClientId;l(e,n,r)},onInsertDefaultBlockAfter:function(){var e=t.order,n=t.rootClientId;u({},n,e+1)},onRemove:function(e){d(e)},onMerge:function(e){var n=t.clientId,o=r("core/editor"),i=o.getPreviousBlockClientId,c=o.getNextBlockClientId;if(e){var a=c(n);a&&p(n,a)}else{var s=i(n);s&&p(s,n)}},onReplace:function(e){b([t.clientId],e)},onMetaChange:function(e){f({meta:e})},onShiftSelection:function(){t.isSelectionEnabled&&(o()?s(o(),t.clientId):a(t.clientId))},toggleSelection:function(e){h(e)}}}),Xi=Object(Ar.compose)(Object(s.withViewportMatch)({isLargeViewport:"medium"}),Yi,Qi,Object(Vr.withFilters)("editor.BlockListBlock"))($i),Zi=n(49);var Ji=Object(Ar.compose)(Object(Ar.withState)({hovered:!1}),Object(l.withSelect)(function(e,t){var n=e("core/editor"),r=n.getBlockCount,o=n.getBlockName,c=n.isBlockValid,a=n.getEditorSettings,s=n.getTemplateLock,l=!r(t.rootClientId),u=o(t.lastBlockClientId)===Object(i.getDefaultBlockName)(),d=c(t.lastBlockClientId),p=a().bodyPlaceholder;return{isVisible:l||!u||!d,showPrompt:l,isLocked:!!s(t.rootClientId),placeholder:p}}),Object(l.withDispatch)(function(e,t){var n=e("core/editor"),r=n.insertDefaultBlock,o=n.startTyping;return{onAppend:function(){var e=t.rootClientId;r(void 0,e),o()}}}))(function(e){var t=e.isLocked,n=e.isVisible,r=e.onAppend,o=e.showPrompt,i=e.placeholder,c=e.rootClientId,a=e.hovered,s=e.setState;if(t||!n)return null;var l=Object(Zi.decodeEntities)(i)||Object(_.__)("Start writing or type / to choose a block");return Object(Br.createElement)("div",{"data-root-client-id":c||"",className:"wp-block editor-default-block-appender",onMouseEnter:function(){return s({hovered:!0})},onMouseLeave:function(){return s({hovered:!1})}},Object(Br.createElement)(Jo,{rootClientId:c}),Object(Br.createElement)(di.a,{role:"button","aria-label":Object(_.__)("Add block"),className:"editor-default-block-appender__content",readOnly:!0,onFocus:r,value:o?l:""}),a&&Object(Br.createElement)(zi,{rootClientId:c}),Object(Br.createElement)(Di,{position:"top right"}))});var ec=Object(l.withSelect)(function(e,t){var n=t.rootClientId,r=e("core/editor"),o=r.getBlockOrder,c=r.canInsertBlockType;return{isLocked:!!(0,r.getTemplateLock)(n),blockClientIds:o(n),canInsertDefaultBlock:c(Object(i.getDefaultBlockName)(),n)}})(function(e){var t=e.blockClientIds,n=e.rootClientId,r=e.canInsertDefaultBlock;return e.isLocked?null:r?Object(Br.createElement)(Ki,{childHandledEvents:["onFocus","onClick","onKeyDown"]},Object(Br.createElement)(Ji,{rootClientId:n,lastBlockClientId:Object(O.last)(t)})):Object(Br.createElement)("div",{className:"block-list-appender"},Object(Br.createElement)(Di,{rootClientId:n,renderToggle:function(e){var t=e.onToggle,n=e.disabled,r=e.isOpen;return Object(Br.createElement)(Vr.Button,{"aria-label":Object(_.__)("Add block"),onClick:t,className:"block-list-appender__toggle","aria-haspopup":"true","aria-expanded":r,disabled:n},Object(Br.createElement)(Vr.Dashicon,{icon:"insert"}))}}))}),tc=function(e){function t(e){var n;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).call(this,e))).onSelectionStart=n.onSelectionStart.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onSelectionEnd=n.onSelectionEnd.bind(Object(Hr.a)(Object(Hr.a)(n))),n.setBlockRef=n.setBlockRef.bind(Object(Hr.a)(Object(Hr.a)(n))),n.setLastClientY=n.setLastClientY.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onPointerMove=Object(O.throttle)(n.onPointerMove.bind(Object(Hr.a)(Object(Hr.a)(n))),100),n.onScroll=function(){return n.onPointerMove({clientY:n.lastClientY})},n.lastClientY=0,n.nodes={},n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){window.addEventListener("mousemove",this.setLastClientY)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("mousemove",this.setLastClientY)}},{key:"setLastClientY",value:function(e){var t=e.clientY;this.lastClientY=t}},{key:"setBlockRef",value:function(e,t){null===e?delete this.nodes[t]:this.nodes=Object(d.a)({},this.nodes,Object(f.a)({},t,e))}},{key:"onPointerMove",value:function(e){var t=e.clientY;this.props.isMultiSelecting||this.props.onStartMultiSelect();var n=Gi(this.selectionAtStart).getBoundingClientRect();if(!(t>=n.top&&t<=n.bottom)){var r=t-n.top,o=Object(O.findLast)(this.coordMapKeys,function(e){return e<r});this.onSelectionChange(this.coordMap[o])}}},{key:"onSelectionStart",value:function(e){if(this.props.isSelectionEnabled){var t=this.nodes[e].getBoundingClientRect(),n=Object(O.mapValues)(this.nodes,function(e){return e.getBoundingClientRect().top-t.top});this.coordMap=Object(O.invert)(n),this.coordMapKeys=Object(O.sortBy)(Object.values(n)),this.selectionAtStart=e,window.addEventListener("mousemove",this.onPointerMove),window.addEventListener("scroll",this.onScroll,!0),window.addEventListener("mouseup",this.onSelectionEnd)}}},{key:"onSelectionChange",value:function(e){var t=this.props,n=t.onMultiSelect,r=t.selectionStart,o=t.selectionEnd,i=this.selectionAtStart,c=i===e;i&&this.props.isSelectionEnabled&&(c&&r&&n(null,null),c||o===e||n(i,e))}},{key:"onSelectionEnd",value:function(){this.onPointerMove.cancel(),delete this.coordMap,delete this.coordMapKeys,delete this.selectionAtStart,window.removeEventListener("mousemove",this.onPointerMove),window.removeEventListener("scroll",this.onScroll,!0),window.removeEventListener("mouseup",this.onSelectionEnd),this.props.isMultiSelecting&&this.props.onStopMultiSelect()}},{key:"render",value:function(){var e=this,t=this.props,n=t.blockClientIds,r=t.rootClientId,o=t.isDraggable;return Object(Br.createElement)("div",{className:"editor-block-list__layout"},Object(O.map)(n,function(t,i){return Object(Br.createElement)(Xi,{key:"block-"+t,index:i,clientId:t,blockRef:e.setBlockRef,onSelectionStart:e.onSelectionStart,rootClientId:r,isFirst:0===i,isLast:i===n.length-1,isDraggable:o})}),Object(Br.createElement)(ec,{rootClientId:r}))}}]),t}(Br.Component),nc=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=e("core/editor"),r=n.getBlockOrder,o=n.isSelectionEnabled,i=n.isMultiSelecting,c=n.getMultiSelectedBlocksStartClientId,a=n.getMultiSelectedBlocksEndClientId;return{blockClientIds:r(t.rootClientId),selectionStart:c(),selectionEnd:a(),isSelectionEnabled:o(),isMultiSelecting:i()}}),Object(l.withDispatch)(function(e){var t=e("core/editor");return{onStartMultiSelect:t.startMultiSelect,onStopMultiSelect:t.stopMultiSelect,onMultiSelect:t.multiSelect}})])(tc),rc=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={templateInProcess:!!e.props.template},e.updateNestedSettings(),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"getTemplateLock",value:function(){var e=this.props,t=e.templateLock,n=e.parentLock;return void 0===t?n:t}},{key:"componentDidMount",value:function(){0!==this.props.block.innerBlocks.length&&"all"!==this.getTemplateLock()||this.synchronizeBlocksWithTemplate(),this.state.templateInProcess&&this.setState({templateInProcess:!1})}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.template,r=t.block.innerBlocks;(this.updateNestedSettings(),0===r.length||"all"===this.getTemplateLock())&&(!Object(O.isEqual)(n,e.template)&&this.synchronizeBlocksWithTemplate())}},{key:"synchronizeBlocksWithTemplate",value:function(){var e=this.props,t=e.template,n=e.block,r=e.replaceInnerBlocks,o=n.innerBlocks,c=Object(i.synchronizeBlocksWithTemplate)(o,t);Object(O.isEqual)(c,o)||r(c)}},{key:"updateNestedSettings",value:function(){var e=this.props,t=e.blockListSettings,n=e.allowedBlocks,r=e.updateNestedSettings,o={allowedBlocks:n,templateLock:this.getTemplateLock()};Ho()(t,o)||r(o)}},{key:"render",value:function(){var e=this.props,t=e.clientId,n=e.isSmallScreen,r=e.isSelectedBlockInRoot,o=this.state.templateInProcess,i=Lr()("editor-inner-blocks",{"has-overlay":n&&!r});return Object(Br.createElement)("div",{className:i},!o&&Object(Br.createElement)(nc,{rootClientId:t}))}}]),t}(Br.Component);(rc=Object(Ar.compose)([qr(function(e){return Object(O.pick)(e,["clientId"])}),Object(s.withViewportMatch)({isSmallScreen:"< medium"}),Object(l.withSelect)(function(e,t){var n=e("core/editor"),r=n.isBlockSelected,o=n.hasSelectedInnerBlock,i=n.getBlock,c=n.getBlockListSettings,a=n.getBlockRootClientId,s=n.getTemplateLock,l=t.clientId,u=a(l);return{isSelectedBlockInRoot:r(l)||o(l),block:i(l),blockListSettings:c(l),parentLock:s(u)}}),Object(l.withDispatch)(function(e,t){var n=e("core/editor"),r=n.replaceBlocks,o=n.insertBlocks,i=n.updateBlockListSettings,c=t.block,a=t.clientId,s=t.templateInsertUpdatesSelection,l=void 0===s||s;return{replaceInnerBlocks:function(e){var t=Object(O.map)(c.innerBlocks,"clientId");t.length?r(t,e):o(e,void 0,a,l)},updateNestedSettings:function(t){e(i(a,t))}}})])(rc)).Content=Object(i.withBlockContentContext)(function(e){var t=e.BlockContent;return Object(Br.createElement)(t,null)});var oc=rc,ic=Object(Vr.createSlotFill)("InspectorAdvancedControls"),cc=ic.Fill,ac=ic.Slot,sc=Gr(cc);sc.Slot=ac;var lc=sc,uc=Object(Vr.createSlotFill)("InspectorControls"),dc=uc.Fill,pc=uc.Slot,bc=Gr(dc);bc.Slot=pc;var fc=bc,hc=Object(_.__)("(current %s: %s)");var mc=Object(Ar.compose)([Co,Object(Ar.ifCondition)(function(e){return e.hasColorsToChoose})])(function(e){var t=e.colors,n=e.disableCustomColors,r=e.label,o=e.onChange,i=e.value,c=Bo(t,i),a=c&&c.name,s=Object(_.sprintf)(hc,r.toLowerCase(),a||i),l=Object(Br.createElement)(Br.Fragment,null,r,i&&Object(Br.createElement)(Vr.ColorIndicator,{colorValue:i,"aria-label":s}));return Object(Br.createElement)(Vr.BaseControl,{className:"editor-color-palette-control",label:l},Object(Br.createElement)(wo,Object(Ir.a)({className:"editor-color-palette-control__color-palette",value:i,onChange:o},{colors:t,disableCustomColors:n})))}),vc=Object(_.__)("(%s: %s)"),Oc=Object(Ar.ifCondition)(function(e){var t=e.colors,n=e.disableCustomColors,r=e.colorSettings;return Object(O.some)(r,function(e){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;return!function(e,t){return void 0!==t.disableCustomColors?t.disableCustomColors:e}(t,n)||(n.colors||e).length>0}(t,n,e)})})(function(e){var t=e.children,n=e.colors,r=e.colorSettings,o=e.disableCustomColors,i=e.title,c=Object(p.a)(e,["children","colors","colorSettings","disableCustomColors","title"]),a=Object(Br.createElement)("span",{className:"".concat("editor-panel-color-settings","__panel-title")},i,function(e,t){return e.map(function(e,n){var r=e.value,o=e.label,i=e.colors;if(!r)return null;var c=Bo(i||t,r),a=c&&c.name,s=Object(_.sprintf)(vc,o.toLowerCase(),a||r);return Object(Br.createElement)(Vr.ColorIndicator,{key:n,colorValue:r,"aria-label":s})})}(r,n));return Object(Br.createElement)(Vr.PanelBody,Object(Ir.a)({className:"editor-panel-color-settings",title:a},c),r.map(function(e,t){return Object(Br.createElement)(mc,Object(Ir.a)({key:t},Object(d.a)({colors:n,disableCustomColors:o},e)))}),t)}),gc=Co(Oc);var jc=function(e){var t=e.onChange,n=e.className,r=Object(p.a)(e,["onChange","className"]);return Object(Br.createElement)(di.a,Object(Ir.a)({className:Lr()("editor-plain-text",n),onChange:function(e){return t(e.target.value)}},r))},yc=n(41),kc=n.n(yc),_c=n(32),Ec=n(65),Sc=n.n(Ec),Cc=Object(l.withSelect)(function(e){return{formatTypes:(0,e("core/rich-text").getFormatTypes)()}})(function(e){var t=e.formatTypes,n=e.onChange,r=e.value;return Object(Br.createElement)(Br.Fragment,null,t.map(function(e){var t=e.name,o=e.edit;if(!o)return null;var i=Object(a.getActiveFormat)(r,t),c=void 0!==i,s=c&&i.attributes||{};return Object(Br.createElement)(o,{key:t,isActive:c,activeAttributes:s,value:r,onChange:n})}))}),wc=function(e){var t=e.controls;return Object(Br.createElement)("div",{className:"editor-format-toolbar"},Object(Br.createElement)(Vr.Toolbar,null,t.map(function(e){return Object(Br.createElement)(Vr.Slot,{name:"RichText.ToolbarControls.".concat(e),key:e})}),Object(Br.createElement)(Vr.Slot,{name:"RichText.ToolbarControls"})))},Tc=n(192),Pc=n.n(Tc),Ic=function(e){return Object(O.pickBy)(e,function(e,t){return n=t,Object(O.startsWith)(n,"aria-")&&!Object(O.isNil)(e);var n})},Bc=window.getSelection,xc=window.Node.TEXT_NODE,Lc=window.navigator.userAgent;var Ac=Lc.indexOf("Trident")>=0,Nc=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).call(this))).bindEditorNode=e.bindEditorNode.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onFocus=e.onFocus.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onKeyDown=e.onKeyDown.bind(Object(Hr.a)(Object(Hr.a)(e))),e.initialize=e.initialize.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onFocus",value:function(){this.props.onFocus&&this.props.onFocus(),this.initialize()}},{key:"shouldComponentUpdate",value:function(e){var t=this;this.configureIsPlaceholderVisible(e.isPlaceholderVisible),Object(O.isEqual)(this.props.style,e.style)||(this.editorNode.setAttribute("style",""),Object.assign(this.editorNode.style,e.style)),Object(O.isEqual)(this.props.className,e.className)||(this.editorNode.className=Lr()(e.className,"editor-rich-text__tinymce"));var n=function(e,t){var n=Object(O.keys)(Ic(e)),r=Object(O.keys)(Ic(t));return{removedKeys:Object(O.difference)(n,r),updatedKeys:r.filter(function(n){return!Object(O.isEqual)(e[n],t[n])})}}(this.props,e),r=n.removedKeys,o=n.updatedKeys;return r.forEach(function(e){return t.editorNode.removeAttribute(e)}),o.forEach(function(n){return t.editorNode.setAttribute(n,e[n])}),!1}},{key:"componentWillUnmount",value:function(){this.editor&&(this.editor.destroy(),delete this.editor)}},{key:"configureIsPlaceholderVisible",value:function(e){var t=String(!!e);this.editorNode.getAttribute("data-is-placeholder-visible")!==t&&this.editorNode.setAttribute("data-is-placeholder-visible",t)}},{key:"initialize",value:function(){var e=this;if(!this.initialize.called){this.initialize.called=!0;var t={theme:!1,inline:!0,toolbar:!1,browser_spellcheck:!0,entity_encoding:"raw",convert_urls:!1,verify_html:!1,inline_boundaries_selector:"a[href],code,b,i,strong,em,del,ins,sup,sub",plugins:[],forced_root_block:this.props.multilineTag||!1,custom_undo_redo_levels:1,lists_indent_on_tab:!1};Pc.a.init(Object(d.a)({},t,{target:this.editorNode,setup:function(t){var n;e.editor=t,t.on("preinit",function(){n=t.dom.setHTML,t.dom.setHTML=function(){}}),t.on("init",function(){["z","y"].forEach(function(e){t.shortcuts.remove("meta+".concat(e))}),t.shortcuts.remove("meta+shift+z"),["b","i","u"].forEach(function(e){t.shortcuts.remove("meta+".concat(e))}),[1,2,3,4,5,6,7,8,9].forEach(function(e){t.shortcuts.remove("access+".concat(e))}),t.dom.setHTML=n,Ac&&document.activeElement!==e.editorNode&&document.activeElement.contains(e.editorNode)&&e.editorNode.focus()}),t.on("keydown",e.onKeyDown,!0)}}))}}},{key:"bindEditorNode",value:function(e){this.editorNode=e,this.props.setRef&&this.props.setRef(e),Ac&&(e?this.removeInternetExplorerInputFix=function(e){function t(e){e.stopImmediatePropagation();var t=document.createEvent("Event");t.initEvent("input",!0,!1),t.data=e.data,e.target.dispatchEvent(t)}function n(t){var n=t.target,r=t.keyCode;if((yo.BACKSPACE===r||yo.DELETE===r)&&e.contains(n)){var o=document.createEvent("Event");o.initEvent("input",!0,!1),o.data=null,n.dispatchEvent(o)}}return e.addEventListener("textinput",t),document.addEventListener("keyup",n,!0),function(){e.removeEventListener("textinput",t),document.removeEventListener("keyup",n,!0)}}(e):this.removeInternetExplorerInputFix())}},{key:"onKeyDown",value:function(e){var t=e.keyCode,n=t===yo.DELETE||t===yo.BACKSPACE;if(t===yo.ENTER||n&&Object(Vo.isEntirelySelected)(this.editorNode))return e.preventDefault(),!1;if(t===yo.LEFT||t===yo.RIGHT){var r=Bc().focusNode,o=r.nodeType,i=r.nodeValue;if(o===xc)if(1===i.length&&"\ufeff"===i[0])r[e.keyCode===yo.LEFT?"previousSibling":"nextSibling"]||(e.preventDefault=O.noop)}}},{key:"render",value:function(){var e,t=Ic(this.props),n=this.props,r=n.tagName,o=void 0===r?"div":r,i=n.style,c=n.record,a=n.valueToEditableHTML,s=n.className,l=n.isPlaceholderVisible,u=n.onPaste,p=n.onInput,b=n.onKeyDown,h=n.onCompositionEnd,m=n.onBlur;return"table"!==o&&(t.role="textbox",t["aria-multiline"]=!0),Object(Br.createElement)(o,Object(d.a)({},t,(e={className:Lr()(s,"editor-rich-text__tinymce"),contentEditable:!0},Object(f.a)(e,"data-is-placeholder-visible",l),Object(f.a)(e,"ref",this.bindEditorNode),Object(f.a)(e,"style",i),Object(f.a)(e,"suppressContentEditableWarning",!0),Object(f.a)(e,"dangerouslySetInnerHTML",{__html:a(c)}),Object(f.a)(e,"onPaste",u),Object(f.a)(e,"onInput",p),Object(f.a)(e,"onFocus",this.onFocus),Object(f.a)(e,"onBlur",m),Object(f.a)(e,"onKeyDown",b),Object(f.a)(e,"onCompositionEnd",h),e)))}}]),t}(Br.Component);var Rc=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onUse=e.onUse.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onUse",value:function(){return this.props.onUse(),!1}},{key:"render",value:function(){var e=this.props,t=e.character,n=e.type;return Object(Br.createElement)(Vr.KeyboardShortcuts,{bindGlobal:!0,shortcuts:Object(f.a)({},yo.rawShortcut[n](t),this.onUse)})}}]),t}(Br.Component),Dc=window.Node,Fc=Dc.TEXT_NODE,Mc=Dc.ELEMENT_NODE;function Uc(){var e=window.getSelection();if(0!==e.rangeCount){var t=e.getRangeAt(0).startContainer;if(t.nodeType===Fc&&(t=t.parentNode),t.nodeType===Mc){var n=t.closest("*[contenteditable]");if(n&&n.contains(t))return t.closest("ol,ul")}}}function Hc(){var e=Uc();return!e||"true"===e.contentEditable}function Vc(e,t){var n=Uc();return n?n.nodeName.toLowerCase()===e:e===t}var Kc=function(e){var t=e.onTagNameChange,n=e.tagName,r=e.value,o=e.onChange;return Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(Rc,{type:"primary",character:"[",onUse:function(){o(Object(a.outdentListItems)(r))}}),Object(Br.createElement)(Rc,{type:"primary",character:"]",onUse:function(){o(Object(a.indentListItems)(r,{type:n}))}}),Object(Br.createElement)(Rc,{type:"primary",character:"m",onUse:function(){o(Object(a.indentListItems)(r,{type:n}))}}),Object(Br.createElement)(Rc,{type:"primaryShift",character:"m",onUse:function(){o(Object(a.outdentListItems)(r))}}),Object(Br.createElement)(jo,null,Object(Br.createElement)(Vr.Toolbar,{controls:[{icon:"editor-ul",title:Object(_.__)("Convert to unordered list"),isActive:Vc("ul",n),onClick:function(){o(Object(a.changeListType)(r,{type:"ul"})),Hc()&&t("ul")}},{icon:"editor-ol",title:Object(_.__)("Convert to ordered list"),isActive:Vc("ol",n),onClick:function(){o(Object(a.changeListType)(r,{type:"ol"})),Hc()&&t("ol")}},{icon:"editor-outdent",title:Object(_.__)("Outdent list item"),onClick:function(){o(Object(a.outdentListItems)(r))}},{icon:"editor-indent",title:Object(_.__)("Indent list item"),onClick:function(){o(Object(a.indentListItems)(r,{type:n}))}}]})))},zc=[yo.rawShortcut.primary("z"),yo.rawShortcut.primaryShift("z"),yo.rawShortcut.primary("y")],Wc=Object(Br.createElement)(Vr.KeyboardShortcuts,{bindGlobal:!0,shortcuts:Object(O.fromPairs)(zc.map(function(e){return[e,function(e){return e.preventDefault()}]}))}),qc=function(){return Wc};function Gc(e){var t,n=e.name,r=e.shortcutType,o=e.shortcutCharacter,i=Object(p.a)(e,["name","shortcutType","shortcutCharacter"]),c="RichText.ToolbarControls";return n&&(c+=".".concat(n)),r&&o&&(t=yo.displayShortcut[r](o)),Object(Br.createElement)(Vr.Fill,{name:c},Object(Br.createElement)(Vr.ToolbarButton,Object(Ir.a)({},i,{shortcut:t})))}var $c=Object(l.withSelect)(function(e,t){var n=t.name;return{formatType:e("core/rich-text").getFormatType(n)}})(function(e){return Object(Br.createElement)(Vr.Fill,{name:"Inserter.InlineElements"},function(t){var n=t.filterValue,r=e.formatType,o=r.keywords,c=void 0===o?[]:o,a=r.title;return c.push(a,e.title),n&&!function(e,t){return e.some(function(e){return-1!==xi(e).indexOf(xi(t))})}(c,n)?null:Object(Br.createElement)(wi,Object(Ir.a)({},e,{icon:Object(i.normalizeIconObject)(e.icon)}))})}),Yc=window.getSelection,Qc=function(e){function t(e){var n,r=e.value,o=e.onReplace,c=e.multiline;return Object(Rr.a)(this,t),n=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments)),!0!==c&&"p"!==c&&"li"!==c||(n.multilineTag=!0===c?"p":c),"li"===n.multilineTag&&(n.multilineWrapperTags=["ul","ol"]),n.props.onSplit?(n.onSplit=n.props.onSplit,Sc()("wp.editor.RichText onSplit prop",{plugin:"Gutenberg",alternative:"wp.editor.RichText unstableOnSplit prop"})):n.props.unstableOnSplit&&(n.onSplit=n.props.unstableOnSplit),n.onFocus=n.onFocus.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onBlur=n.onBlur.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onChange=n.onChange.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onDeleteKeyDown=n.onDeleteKeyDown.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onKeyDown=n.onKeyDown.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onPaste=n.onPaste.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onCreateUndoLevel=n.onCreateUndoLevel.bind(Object(Hr.a)(Object(Hr.a)(n))),n.setFocusedElement=n.setFocusedElement.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onInput=n.onInput.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onCompositionEnd=n.onCompositionEnd.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onSelectionChange=n.onSelectionChange.bind(Object(Hr.a)(Object(Hr.a)(n))),n.getRecord=n.getRecord.bind(Object(Hr.a)(Object(Hr.a)(n))),n.createRecord=n.createRecord.bind(Object(Hr.a)(Object(Hr.a)(n))),n.applyRecord=n.applyRecord.bind(Object(Hr.a)(Object(Hr.a)(n))),n.isEmpty=n.isEmpty.bind(Object(Hr.a)(Object(Hr.a)(n))),n.valueToFormat=n.valueToFormat.bind(Object(Hr.a)(Object(Hr.a)(n))),n.setRef=n.setRef.bind(Object(Hr.a)(Object(Hr.a)(n))),n.valueToEditableHTML=n.valueToEditableHTML.bind(Object(Hr.a)(Object(Hr.a)(n))),n.formatToValue=kc()(n.formatToValue.bind(Object(Hr.a)(Object(Hr.a)(n))),{size:1}),n.savedContent=r,n.patterns=function(e){var t=e.onReplace,n=e.valueToFormat,r=e.onCreateUndoLevel,o=e.onChange,c=Object(i.getBlockTransforms)("from").filter(function(e){return"prefix"===e.type});return[function(e){if(!t)return e;var o=Object(a.getSelectionStart)(e),s=Object(a.getTextContent)(e),l=s.slice(o-1,o);if(!/\s/.test(l))return e;var u=s.slice(0,o).trim(),d=Object(i.findTransform)(c,function(e){var t=e.prefix;return u===t});if(!d)return e;var p=n(Object(a.slice)(e,o,s.length)),b=d.transform(p);return r(),t([b]),e},function(e){var t=Object(a.getSelectionStart)(e),n=Object(a.getTextContent)(e);if("`"!==n.slice(t-1,t))return e;var r=n.slice(0,t-1).lastIndexOf("`");if(-1===r)return e;var i=r,c=t-2;return i===c?e:(o(e),e=Object(a.remove)(e,i,i+1),e=Object(a.remove)(e,c,c+1),e=Object(a.applyFormat)(e,{type:"code"},i,c))}]}({onReplace:o,onCreateUndoLevel:n.onCreateUndoLevel,valueToFormat:n.valueToFormat,onChange:n.onChange}),n.enterPatterns=Object(i.getBlockTransforms)("from").filter(function(e){return"enter"===e.type}),n.state={},n.usedDeprecatedChildrenSource=Array.isArray(r),n.lastHistoryValue=r,n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentWillUnmount",value:function(){document.removeEventListener("selectionchange",this.onSelectionChange)}},{key:"setRef",value:function(e){this.editableRef=e}},{key:"setFocusedElement",value:function(){this.props.setFocusedElement&&this.props.setFocusedElement(this.props.instanceId)}},{key:"getRecord",value:function(){var e=this.formatToValue(this.props.value),t=e.formats,n=e.text,r=this.state;return{formats:t,text:n,start:r.start,end:r.end}}},{key:"createRecord",value:function(){var e=Yc().getRangeAt(0);return Object(a.create)({element:this.editableRef,range:e,multilineTag:this.multilineTag,multilineWrapperTags:this.multilineWrapperTags,removeNode:function(e){return"all"===e.getAttribute("data-mce-bogus")},unwrapNode:function(e){return!!e.getAttribute("data-mce-bogus")},removeAttribute:function(e){return 0===e.indexOf("data-mce-")},filterString:function(e){return e.replace("\ufeff","")},prepareEditableTree:this.props.prepareEditableTree})}},{key:"applyRecord",value:function(e){Object(a.apply)({value:e,current:this.editableRef,multilineTag:this.multilineTag,multilineWrapperTags:this.multilineWrapperTags,createLinePadding:function(e){var t=e.createElement("br");return t.setAttribute("data-mce-bogus","1"),t},prepareEditableTree:this.props.prepareEditableTree})}},{key:"isEmpty",value:function(){return Object(a.isEmpty)(this.formatToValue(this.props.value))}},{key:"onPaste",value:function(e){var t=e.clipboardData,n=t.items,r=t.files;n=Object(O.isNil)(n)?[]:n,r=Object(O.isNil)(r)?[]:r;var o="",c="";try{o=t.getData("text/plain"),c=t.getData("text/html")}catch(e){try{c=t.getData("Text")}catch(e){return}}e.preventDefault(),window.console.log("Received HTML:\n\n",c),window.console.log("Received plain text:\n\n",o);var s=Object(O.find)(Object(b.a)(n).concat(Object(b.a)(r)),function(e){var t=e.type;return/^image\/(?:jpe?g|png|gif)$/.test(t)});if(s&&!c){var l=s.getAsFile?s.getAsFile():s,u=Object(i.pasteHandler)({HTML:'<img src="'.concat(Object(_c.createBlobURL)(l),'">'),mode:"BLOCKS",tagName:this.props.tagName}),d=this.props.onReplace&&this.isEmpty();return window.console.log("Received item:\n\n",l),void(d?this.props.onReplace(u):this.onSplit&&this.splitContent(u))}var p=this.getRecord();if(!Object(a.isCollapsed)(p)){var f=(c||o).replace(/<[^>]+>/g,"").trim();if(Object(g.isURL)(f))return this.onChange(Object(a.applyFormat)(p,{type:"a",attributes:{href:Object(Zi.decodeEntities)(f)}})),void window.console.log("Created link:\n\n",f)}var h=this.props.onReplace&&this.isEmpty(),m="INLINE";h?m="BLOCKS":this.onSplit&&(m="AUTO");var v=Object(i.pasteHandler)({HTML:c,plainText:o,mode:m,tagName:this.props.tagName,canUserUseUnfilteredHTML:this.props.canUserUseUnfilteredHTML});if("string"==typeof v){var j=Object(a.create)({html:v});this.onChange(Object(a.insert)(p,j))}else if(this.onSplit){if(!v.length)return;h?this.props.onReplace(v):this.splitContent(v,{paste:!0})}}},{key:"onFocus",value:function(){var e=this.props.unstableOnFocus;e&&e(),document.addEventListener("selectionchange",this.onSelectionChange)}},{key:"onBlur",value:function(){document.removeEventListener("selectionchange",this.onSelectionChange)}},{key:"onInput",value:function(e){if(!e.nativeEvent.isComposing){var t=this.createRecord(),n=this.patterns.reduce(function(e,t){return t(e)},t);this.onChange(n,{withoutHistory:!0}),this.props.clearTimeout(this.onInput.timeout),this.onInput.timeout=this.props.setTimeout(this.onCreateUndoLevel,1e3)}}},{key:"onCompositionEnd",value:function(){this.onChange(this.createRecord())}},{key:"onSelectionChange",value:function(){var e=this.createRecord(),t=e.start,n=e.end,r=e.formats;if(t!==this.state.start||n!==this.state.end){var o=this.props.isCaretWithinFormattedText;!o&&r[t]?this.props.onEnterFormattedText():o&&!r[t]&&this.props.onExitFormattedText(),this.setState({start:t,end:n})}}},{key:"onChangeEditableValue",value:function(e){var t=e.formats,n=e.text;Object(O.get)(this.props,["onChangeEditableValue"],[]).forEach(function(e){e(t,n)})}},{key:"onChange",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).withoutHistory;this.applyRecord(e);var n=e.start,r=e.end;this.onChangeEditableValue(e),this.savedContent=this.valueToFormat(e),this.props.onChange(this.savedContent),this.setState({start:n,end:r}),t||this.onCreateUndoLevel()}},{key:"onCreateUndoLevel",value:function(){this.lastHistoryValue!==this.savedContent&&(this.props.onCreateUndoLevel(),this.lastHistoryValue=this.savedContent)}},{key:"onDeleteKeyDown",value:function(e){var t=this.props,n=t.onMerge,r=t.onRemove;if(n||r){var o=e.keyCode===yo.BACKSPACE;if(Object(a.isCollapsed)(this.createRecord())){var i=this.isEmpty();(i||Object(Vo.isHorizontalEdge)(this.editableRef,o))&&(n&&n(!o),r&&i&&o&&r(!o),e.preventDefault())}}}},{key:"onKeyDown",value:function(e){var t=e.keyCode;if(t===yo.DELETE||t===yo.BACKSPACE){var n,r=this.createRecord(),o=Object(a.getSelectionStart)(r),c=Object(a.getSelectionEnd)(r);if(0===o&&0!==c&&c===r.text.length)return this.onChange(Object(a.remove)(r)),void e.preventDefault();if(this.multilineTag)t===yo.BACKSPACE?Object(a.charAt)(r,o-1)===a.LINE_SEPARATOR&&(n=Object(a.remove)(r,Object(a.isCollapsed)(r)?o-1:o,c)):Object(a.charAt)(r,c)===a.LINE_SEPARATOR&&(n=Object(a.remove)(r,o,Object(a.isCollapsed)(r)?c+1:c)),n&&(this.onChange(n),e.preventDefault());this.onDeleteKeyDown(e)}else if(t===yo.ENTER){e.preventDefault();var s=this.createRecord();if(this.props.onReplace){var l=Object(a.getTextContent)(s),u=Object(i.findTransform)(this.enterPatterns,function(e){return e.regExp.test(l)});if(u)return void this.props.onReplace([u.transform({content:l})])}if(this.multilineTag)this.onSplit&&Object(a.isEmptyLine)(s)?this.onSplit.apply(this,Object(b.a)(Object(a.split)(s).map(this.valueToFormat))):this.onChange(Object(a.insertLineSeparator)(s));else if(e.shiftKey||!this.onSplit){var d=Object(a.getTextContent)(s),p=d.length,f="\n";s.end!==p||"\n"===d.charAt(p-1)&&0!==p||(f="\n\n"),this.onChange(Object(a.insert)(s,f))}else this.splitContent()}}},{key:"splitContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.onSplit){var n=this.createRecord(),r=Object(a.split)(n),o=Object(u.a)(r,2),i=o[0],c=o[1];Object(a.isEmpty)(c)?i=n:Object(a.isEmpty)(i)&&(c=n),t.paste&&(i=Object(a.isEmpty)(i)?null:i,c=Object(a.isEmpty)(c)?null:c),i&&(i=this.valueToFormat(i)),c&&(c=this.valueToFormat(c)),this.onSplit.apply(this,[i,c].concat(Object(b.a)(e)))}}},{key:"componentDidUpdate",value:function(e){var t=this,n=this.props,r=n.tagName,o=n.value,i=n.isSelected;if(r===e.tagName&&o!==e.value&&o!==this.savedContent){if(Array.isArray(o)&&Object(O.isEqual)(o,this.savedContent))return;var c=this.formatToValue(o);if(i){var s=this.formatToValue(e.value),l=Object(a.getTextContent)(s).length;c.start=l,c.end=l}this.applyRecord(c),this.savedContent=o}if(Object.keys(this.props).some(function(n){return 0===n.indexOf("format_")&&(Object(O.isPlainObject)(t.props[n])?Object.keys(t.props[n]).some(function(r){return t.props[n][r]!==e[n][r]}):t.props[n]!==e[n])})){var u=this.formatToValue(o);i&&(u.start=this.state.start,u.end=this.state.end),this.applyRecord(u)}}},{key:"getFormatProps",value:function(){return Object(O.pickBy)(this.props,function(e,t){return t.startsWith("format_")})}},{key:"formatToValue",value:function(e){return Array.isArray(e)?Object(a.create)({html:i.children.toHTML(e),multilineTag:this.multilineTag,multilineWrapperTags:this.multilineWrapperTags}):"string"===this.props.format?Object(a.create)({html:e,multilineTag:this.multilineTag,multilineWrapperTags:this.multilineWrapperTags}):null===e?Object(a.create)():e}},{key:"valueToEditableHTML",value:function(e){return Object(a.unstableToDom)({value:e,multilineTag:this.multilineTag,multilineWrapperTags:this.multilineWrapperTags,createLinePadding:function(e){var t=e.createElement("br");return t.setAttribute("data-mce-bogus","1"),t},prepareEditableTree:this.props.prepareEditableTree}).body.innerHTML}},{key:"removeEditorOnlyFormats",value:function(e){return this.props.formatTypes.forEach(function(t){t.__experimentalCreatePrepareEditableTree&&(e=Object(a.removeFormat)(e,t.name,0,e.text.length))}),e}},{key:"valueToFormat",value:function(e){return e=this.removeEditorOnlyFormats(e),this.usedDeprecatedChildrenSource?i.children.fromDOM(Object(a.unstableToDom)({value:e,multilineTag:this.multilineTag,multilineWrapperTags:this.multilineWrapperTags}).body.childNodes):"string"===this.props.format?Object(a.toHTMLString)({value:e,multilineTag:this.multilineTag,multilineWrapperTags:this.multilineWrapperTags}):e}},{key:"render",value:function(){var e=this,t=this.props,n=t.tagName,r=void 0===n?"div":n,o=t.style,i=t.wrapperClassName,c=t.className,a=t.inlineToolbar,s=void 0!==a&&a,l=t.formattingControls,u=t.placeholder,d=t.keepPlaceholderOnFocus,p=void 0!==d&&d,b=t.isSelected,f=t.autocompleters,h=t.onTagNameChange,m=this.multilineTag,v=Ic(this.props),O=["editor",r].join(),g=u&&(!b||p)&&this.isEmpty(),j=Lr()(i,"editor-rich-text"),y=this.getRecord();return Object(Br.createElement)("div",{className:j,onFocus:this.setFocusedElement},b&&"li"===this.multilineTag&&Object(Br.createElement)(Kc,{onTagNameChange:h,tagName:r,value:y,onChange:this.onChange}),b&&!s&&Object(Br.createElement)(jo,null,Object(Br.createElement)(wc,{controls:l})),b&&s&&Object(Br.createElement)(Vr.IsolatedEventContainer,{className:"editor-rich-text__inline-toolbar"},Object(Br.createElement)(wc,{controls:l})),Object(Br.createElement)(Yr,{onReplace:this.props.onReplace,completers:f,record:y,onChange:this.onChange},function(t){var n=t.listBoxId,i=t.activeId;return Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(Nc,Object(Ir.a)({tagName:r,style:o,record:y,valueToEditableHTML:e.valueToEditableHTML,isPlaceholderVisible:g,"aria-label":u,"aria-autocomplete":"list","aria-owns":n,"aria-activedescendant":i},v,{className:c,key:O,onPaste:e.onPaste,onInput:e.onInput,onCompositionEnd:e.onCompositionEnd,onKeyDown:e.onKeyDown,onFocus:e.onFocus,onBlur:e.onBlur,multilineTag:e.multilineTag,multilineWrapperTags:e.multilineWrapperTags,setRef:e.setRef})),g&&Object(Br.createElement)(r,{className:Lr()("editor-rich-text__tinymce",c),style:o},m?Object(Br.createElement)(m,null,u):u),b&&Object(Br.createElement)(Cc,{value:y,onChange:e.onChange}))}),b&&Object(Br.createElement)(qc,null))}}]),t}(Br.Component);Qc.defaultProps={formattingControls:["bold","italic","link","strikethrough"],format:"string",value:""};var Xc=Object(Ar.compose)([Ar.withInstanceId,qr(function(e,t){return!1===t.isSelected?{clientId:e.clientId}:!0===t.isSelected?{isSelected:e.isSelected,clientId:e.clientId}:{isSelected:e.isSelected&&e.focusedElement===t.instanceId,setFocusedElement:e.setFocusedElement,clientId:e.clientId}}),Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.canUserUseUnfilteredHTML,r=t.isCaretWithinFormattedText,o=e("core/rich-text").getFormatTypes;return{canUserUseUnfilteredHTML:n(),isCaretWithinFormattedText:r(),formatTypes:o()}}),Object(l.withDispatch)(function(e){var t=e("core/editor");return{onCreateUndoLevel:t.createUndoLevel,onRedo:t.redo,onUndo:t.undo,onEnterFormattedText:t.enterFormattedText,onExitFormattedText:t.exitFormattedText}}),Ar.withSafeTimeout,Object(Vr.withFilters)("experimentalRichText")])(Qc);Xc.Content=function(e){var t,n=e.value,r=e.tagName,o=e.multiline,c=Object(p.a)(e,["value","tagName","multiline"]),a=n;!0!==o&&"p"!==o&&"li"!==o||(t=!0===o?"p":o),Array.isArray(n)&&(a=i.children.toHTML(n)),!a&&t&&(a="<".concat(t,"></").concat(t,">"));var s=Object(Br.createElement)(Br.RawHTML,null,a);return r?Object(Br.createElement)(r,Object(O.omit)(c,["format"]),s):s},Xc.isEmpty=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return Array.isArray(e)&&!e||0===e.length},Xc.Content.defaultProps={format:"string",value:""};var Zc=Xc,Jc=function(e){var t=e.urlQueryArgs,n=void 0===t?{}:t,r=Object(p.a)(e,["urlQueryArgs"]),o=Object(l.select)("core/editor").getCurrentPostId;return n=Object(d.a)({post_id:o()},n),Object(Br.createElement)(Vr.ServerSideRender,Object(Ir.a)({urlQueryArgs:n},r))},ea=Object(Vr.withFilters)("editor.MediaUpload")(function(){return null}),ta=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).toggleSettingsVisibility=e.toggleSettingsVisibility.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={isSettingsExpanded:!1},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"toggleSettingsVisibility",value:function(){this.setState({isSettingsExpanded:!this.state.isSettingsExpanded})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.renderSettings,r=e.onClose,o=e.onClickOutside,i=e.position,c=void 0===i?"bottom center":i,a=e.focusOnMount,s=void 0===a?"firstElement":a,l=this.state.isSettingsExpanded,u=!!n&&l;return Object(Br.createElement)(Vr.Popover,{className:"editor-url-popover",focusOnMount:s,position:c,onClose:r,onClickOutside:o},Object(Br.createElement)("div",{className:"editor-url-popover__row"},t,!!n&&Object(Br.createElement)(Vr.IconButton,{className:"editor-url-popover__settings-toggle",icon:"ellipsis",label:Object(_.__)("Link Settings"),onClick:this.toggleSettingsVisibility,"aria-expanded":l})),u&&Object(Br.createElement)("div",{className:"editor-url-popover__row editor-url-popover__settings"},n()))}}]),t}(Br.Component);function na(){return(na=Object(hr.a)(regeneratorRuntime.mark(function e(t){var n,r,o,i,c,a,s,l,p,f,h,m,v,g,j,y,k,E,S,C,w,T,P,I,B,x,L,A,N;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=t.allowedTypes,r=t.additionalData,o=void 0===r?{}:r,i=t.filesList,c=t.maxUploadFileSize,a=t.onError,s=void 0===a?O.noop:a,l=t.onFileChange,p=t.wpAllowedMimeTypes,f=void 0===p?null:p,h=Object(b.a)(i),m=[],v=function(e,t){Object(_c.revokeBlobURL)(Object(O.get)(m,[e,"url"])),m[e]=t,l(Object(O.compact)(m))},g=function(e){return!n||Object(O.some)(n,function(t){return Object(O.includes)(t,"/")?t===e:Object(O.startsWith)(e,"".concat(t,"/"))})},j=(R=f)?Object(O.flatMap)(R,function(e,t){var n=e.split("/"),r=Object(u.a)(n,1)[0],o=t.split("|");return[e].concat(Object(b.a)(Object(O.map)(o,function(e){return"".concat(r,"/").concat(e)})))}):R,y=function(e){return Object(O.includes)(j,e)},k=function(e){e.message=[Object(Br.createElement)("strong",{key:"filename"},e.file.name),": ",e.message],s(e)},E=[],S=!0,C=!1,w=void 0,e.prev=12,T=h[Symbol.iterator]();case 14:if(S=(P=T.next()).done){e.next=34;break}if(I=P.value,!j||y(I.type)){e.next=19;break}return k({code:"MIME_TYPE_NOT_ALLOWED_FOR_USER",message:Object(_.__)("Sorry, this file type is not permitted for security reasons."),file:I}),e.abrupt("continue",31);case 19:if(g(I.type)){e.next=22;break}return k({code:"MIME_TYPE_NOT_SUPPORTED",message:Object(_.__)("Sorry, this file type is not supported here."),file:I}),e.abrupt("continue",31);case 22:if(!(c&&I.size>c)){e.next=25;break}return k({code:"SIZE_ABOVE_LIMIT",message:Object(_.__)("This file exceeds the maximum upload size for this site."),file:I}),e.abrupt("continue",31);case 25:if(!(I.size<=0)){e.next=28;break}return k({code:"EMPTY_FILE",message:Object(_.__)("This file is empty."),file:I}),e.abrupt("continue",31);case 28:E.push(I),m.push({url:Object(_c.createBlobURL)(I)}),l(m);case 31:S=!0,e.next=14;break;case 34:e.next=40;break;case 36:e.prev=36,e.t0=e.catch(12),C=!0,w=e.t0;case 40:e.prev=40,e.prev=41,S||null==T.return||T.return();case 43:if(e.prev=43,!C){e.next=46;break}throw w;case 46:return e.finish(43);case 47:return e.finish(40);case 48:B=0;case 49:if(!(B<E.length)){e.next=68;break}return x=E[B],e.prev=51,e.next=54,ra(x,o);case 54:L=e.sent,A=Object(d.a)({},Object(O.omit)(L,["alt_text","source_url"]),{alt:L.alt_text,caption:Object(O.get)(L,["caption","raw"],""),title:L.title.raw,url:L.source_url}),v(B,A),e.next=65;break;case 59:e.prev=59,e.t1=e.catch(51),v(B,null),N=void 0,N=Object(O.has)(e.t1,["message"])?Object(O.get)(e.t1,["message"]):Object(_.sprintf)(Object(_.__)("Error while uploading file %s to the media library."),x.name),s({code:"GENERAL",message:N,file:x});case 65:++B,e.next=49;break;case 68:case"end":return e.stop()}var R},e,this,[[12,36,40,48],[41,,43,47],[51,59]])}))).apply(this,arguments)}function ra(e,t){var n=new window.FormData;return n.append("file",e,e.name||e.type.replace("/",".")),n.append("title",e.name?e.name.replace(/\.[^.]+$/,""):e.type.replace("/",".")),Object(O.forEach)(t,function(e,t){return n.append(t,e)}),vr()({path:"/wp/v2/media",body:n,method:"POST"})}var oa=function(e){var t=e.allowedTypes,n=e.filesList,r=e.maxUploadFileSize,o=e.onError,i=void 0===o?O.noop:o,c=e.onFileChange,a=Object(l.select)("core/editor"),s=a.getCurrentPostId,u=a.getEditorSettings,d=u().allowedMimeTypes;r=r||u().maxUploadFileSize,function(e){na.apply(this,arguments)}({allowedTypes:t,filesList:n,onFileChange:c,additionalData:{post:s()},maxUploadFileSize:r,onError:function(e){var t=e.message;return i(t)},wpAllowedMimeTypes:d})};function ia(e,t){return Object(g.addQueryArgs)(e,t)}function ca(e){return Object(O.toLower)(Object(O.deburr)(Object(O.trim)(e.replace(/[\s\.\/_]+/g,"-"),"-")))}var aa=function(e){var t=e.src,n=e.onChange,r=e.onSubmit,o=e.onClose;return Object(Br.createElement)(ta,{onClose:o},Object(Br.createElement)("form",{className:"editor-media-placeholder__url-input-form",onSubmit:r},Object(Br.createElement)("input",{className:"editor-media-placeholder__url-input-field",type:"url","aria-label":Object(_.__)("URL"),placeholder:Object(_.__)("Paste or type URL"),onChange:n,value:t}),Object(Br.createElement)(Vr.IconButton,{className:"editor-media-placeholder__url-input-submit-button",icon:"editor-break",label:Object(_.__)("Apply"),type:"submit"})))},sa=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={src:"",isURLInputVisible:!1},e.onChangeSrc=e.onChangeSrc.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onSubmitSrc=e.onSubmitSrc.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onUpload=e.onUpload.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onFilesUpload=e.onFilesUpload.bind(Object(Hr.a)(Object(Hr.a)(e))),e.openURLInput=e.openURLInput.bind(Object(Hr.a)(Object(Hr.a)(e))),e.closeURLInput=e.closeURLInput.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onlyAllowsImages",value:function(){var e=this.props.allowedTypes;return!!e&&Object(O.every)(e,function(e){return"image"===e||Object(O.startsWith)(e,"image/")})}},{key:"componentDidMount",value:function(){this.setState({src:Object(O.get)(this.props.value,["src"],"")})}},{key:"componentDidUpdate",value:function(e){Object(O.get)(e.value,["src"],"")!==Object(O.get)(this.props.value,["src"],"")&&this.setState({src:Object(O.get)(this.props.value,["src"],"")})}},{key:"onChangeSrc",value:function(e){this.setState({src:e.target.value})}},{key:"onSubmitSrc",value:function(e){e.preventDefault(),this.state.src&&this.props.onSelectURL&&(this.props.onSelectURL(this.state.src),this.closeURLInput())}},{key:"onUpload",value:function(e){this.onFilesUpload(e.target.files)}},{key:"onFilesUpload",value:function(e){var t=this.props,n=t.onSelect,r=t.multiple,o=t.onError,i=t.allowedTypes;oa({allowedTypes:i,filesList:e,onFileChange:r?n:function(e){var t=Object(u.a)(e,1)[0];return n(t)},onError:o})}},{key:"openURLInput",value:function(){this.setState({isURLInputVisible:!0})}},{key:"closeURLInput",value:function(){this.setState({isURLInputVisible:!1})}},{key:"render",value:function(){var e=this.props,t=e.accept,n=e.icon,r=e.className,o=e.labels,i=void 0===o?{}:o,c=e.onSelect,a=e.value,s=void 0===a?{}:a,l=e.onSelectURL,u=e.onHTMLDrop,d=void 0===u?O.noop:u,p=e.multiple,b=void 0!==p&&p,f=e.notices,h=e.allowedTypes,m=void 0===h?[]:h,v=e.hasUploadPermissions,g=this.state,j=g.isURLInputVisible,y=g.src,k=i.instructions||"",E=i.title||"";if(v||l||(k=Object(_.__)("To edit this block, you need permission to upload media.")),!k||!E){var S=1===m.length,C=S&&"audio"===m[0],w=S&&"image"===m[0],T=S&&"video"===m[0];k||(v?(k=Object(_.__)("Drag a media file, upload a new one or select a file from your library."),C?k=Object(_.__)("Drag an audio, upload a new one or select a file from your library."):w?k=Object(_.__)("Drag an image, upload a new one or select a file from your library."):T&&(k=Object(_.__)("Drag a video, upload a new one or select a file from your library."))):!v&&l&&(k=Object(_.__)("Given your current role, you can only link a media file, you cannot upload."),C?k=Object(_.__)("Given your current role, you can only link an audio, you cannot upload."):w?k=Object(_.__)("Given your current role, you can only link an image, you cannot upload."):T&&(k=Object(_.__)("Given your current role, you can only link a video, you cannot upload.")))),E||(E=Object(_.__)("Media"),C?E=Object(_.__)("Audio"):w?E=Object(_.__)("Image"):T&&(E=Object(_.__)("Video")))}return Object(Br.createElement)(Vr.Placeholder,{icon:n,label:E,instructions:k,className:Lr()("editor-media-placeholder",r),notices:f},Object(Br.createElement)(Xo,null,Object(Br.createElement)(Vr.DropZone,{onFilesDrop:this.onFilesUpload,onHTMLDrop:d}),Object(Br.createElement)(Vr.FormFileUpload,{isLarge:!0,className:"editor-media-placeholder__button",onChange:this.onUpload,accept:t,multiple:b},Object(_.__)("Upload")),Object(Br.createElement)(ea,{gallery:b&&this.onlyAllowsImages(),multiple:b,onSelect:c,allowedTypes:m,value:s.id,render:function(e){var t=e.open;return Object(Br.createElement)(Vr.Button,{isLarge:!0,className:"editor-media-placeholder__button",onClick:t},Object(_.__)("Media Library"))}})),l&&Object(Br.createElement)("div",{className:"editor-media-placeholder__url-input-container"},Object(Br.createElement)(Vr.Button,{className:"editor-media-placeholder__button",onClick:this.openURLInput,isToggled:j,isLarge:!0},Object(_.__)("Insert from URL")),j&&Object(Br.createElement)(aa,{src:y,onChange:this.onChangeSrc,onSubmit:this.onSubmitSrc,onClose:this.closeURLInput})))}}]),t}(Br.Component),la=Object(l.withSelect)(function(e){return{hasUploadPermissions:(0,e("core").hasUploadPermissions)()}}),ua=Object(Ar.compose)(la,Object(Vr.withFilters)("editor.MediaPlaceholder"))(sa),da=function(e){return e.stopPropagation()},pa=function(e){function t(e){var n,r=e.autocompleteRef;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onChange=n.onChange.bind(Object(Hr.a)(Object(Hr.a)(n))),n.onKeyDown=n.onKeyDown.bind(Object(Hr.a)(Object(Hr.a)(n))),n.autocompleteRef=r||Object(Br.createRef)(),n.inputRef=Object(Br.createRef)(),n.updateSuggestions=Object(O.throttle)(n.updateSuggestions.bind(Object(Hr.a)(Object(Hr.a)(n))),200),n.suggestionNodes=[],n.state={posts:[],showSuggestions:!1,selectedSuggestion:null},n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidUpdate",value:function(){var e=this,t=this.state,n=t.showSuggestions,r=t.selectedSuggestion;n&&null!==r&&!this.scrollingIntoView&&(this.scrollingIntoView=!0,Ei()(this.suggestionNodes[r],this.autocompleteRef.current,{onlyScrollIfNeeded:!0}),setTimeout(function(){e.scrollingIntoView=!1},100))}},{key:"componentWillUnmount",value:function(){delete this.suggestionsRequest}},{key:"bindSuggestionNode",value:function(e){var t=this;return function(n){t.suggestionNodes[e]=n}}},{key:"updateSuggestions",value:function(e){var t=this;if(e.length<2||/^https?:/.test(e))this.setState({showSuggestions:!1,selectedSuggestion:null,loading:!1});else{this.setState({showSuggestions:!0,selectedSuggestion:null,loading:!0});var n=vr()({path:Object(g.addQueryArgs)("/wp/v2/search",{search:e,per_page:20,type:"post"})});n.then(function(e){t.suggestionsRequest===n&&(t.setState({posts:e,loading:!1}),e.length?t.props.debouncedSpeak(Object(_.sprintf)(Object(_._n)("%d result found, use up and down arrow keys to navigate.","%d results found, use up and down arrow keys to navigate.",e.length),e.length),"assertive"):t.props.debouncedSpeak(Object(_.__)("No results."),"assertive"))}).catch(function(){t.suggestionsRequest===n&&t.setState({loading:!1})}),this.suggestionsRequest=n}}},{key:"onChange",value:function(e){var t=e.target.value;this.props.onChange(t),this.updateSuggestions(t)}},{key:"onKeyDown",value:function(e){var t=this.state,n=t.showSuggestions,r=t.selectedSuggestion,o=t.posts,i=t.loading;if(n&&o.length&&!i){var c=this.state.posts[this.state.selectedSuggestion];switch(e.keyCode){case yo.UP:e.stopPropagation(),e.preventDefault();var a=r?r-1:o.length-1;this.setState({selectedSuggestion:a});break;case yo.DOWN:e.stopPropagation(),e.preventDefault();var s=null===r||r===o.length-1?0:r+1;this.setState({selectedSuggestion:s});break;case yo.TAB:null!==this.state.selectedSuggestion&&(this.selectLink(c),this.props.speak(Object(_.__)("Link selected.")));break;case yo.ENTER:null!==this.state.selectedSuggestion&&(e.stopPropagation(),this.selectLink(c))}}else switch(e.keyCode){case yo.UP:0!==e.target.selectionStart&&(e.stopPropagation(),e.preventDefault(),e.target.setSelectionRange(0,0));break;case yo.DOWN:this.props.value.length!==e.target.selectionStart&&(e.stopPropagation(),e.preventDefault(),e.target.setSelectionRange(this.props.value.length,this.props.value.length))}}},{key:"selectLink",value:function(e){this.props.onChange(e.url,e),this.setState({selectedSuggestion:null,showSuggestions:!1})}},{key:"handleOnClick",value:function(e){this.selectLink(e),this.inputRef.current.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.value,r=void 0===n?"":n,o=t.autoFocus,i=void 0===o||o,c=t.instanceId,a=this.state,s=a.showSuggestions,l=a.posts,u=a.selectedSuggestion,d=a.loading;return Object(Br.createElement)("div",{className:"editor-url-input"},Object(Br.createElement)("input",{autoFocus:i,type:"text","aria-label":Object(_.__)("URL"),required:!0,value:r,onChange:this.onChange,onInput:da,placeholder:Object(_.__)("Paste URL or type to search"),onKeyDown:this.onKeyDown,role:"combobox","aria-expanded":s,"aria-autocomplete":"list","aria-owns":"editor-url-input-suggestions-".concat(c),"aria-activedescendant":null!==u?"editor-url-input-suggestion-".concat(c,"-").concat(u):void 0,ref:this.inputRef}),d&&Object(Br.createElement)(Vr.Spinner,null),s&&!!l.length&&Object(Br.createElement)(Vr.Popover,{position:"bottom",noArrow:!0,focusOnMount:!1},Object(Br.createElement)("div",{className:"editor-url-input__suggestions",id:"editor-url-input-suggestions-".concat(c),ref:this.autocompleteRef,role:"listbox"},l.map(function(t,n){return Object(Br.createElement)("button",{key:t.id,role:"option",tabIndex:"-1",id:"editor-url-input-suggestion-".concat(c,"-").concat(n),ref:e.bindSuggestionNode(n),className:Lr()("editor-url-input__suggestion",{"is-selected":n===u}),onClick:function(){return e.handleOnClick(t)},"aria-selected":n===u},Object(Zi.decodeEntities)(t.title)||Object(_.__)("(no title)"))}))))}}]),t}(Br.Component),ba=Object(Vr.withSpokenMessages)(Object(Ar.withInstanceId)(pa)),fa=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).toggle=e.toggle.bind(Object(Hr.a)(Object(Hr.a)(e))),e.submitLink=e.submitLink.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={expanded:!1},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"toggle",value:function(){this.setState({expanded:!this.state.expanded})}},{key:"submitLink",value:function(e){e.preventDefault(),this.toggle()}},{key:"render",value:function(){var e=this.props,t=e.url,n=e.onChange,r=this.state.expanded,o=t?Object(_.__)("Edit Link"):Object(_.__)("Insert Link");return Object(Br.createElement)("div",{className:"editor-url-input__button"},Object(Br.createElement)(Vr.IconButton,{icon:"admin-links",label:o,onClick:this.toggle,className:Lr()("components-toolbar__control",{"is-active":t})}),r&&Object(Br.createElement)("form",{className:"editor-url-input__button-modal",onSubmit:this.submitLink},Object(Br.createElement)("div",{className:"editor-url-input__button-modal-line"},Object(Br.createElement)(Vr.IconButton,{className:"editor-url-input__back",icon:"arrow-left-alt",label:Object(_.__)("Close"),onClick:this.toggle}),Object(Br.createElement)(ba,{value:t||"",onChange:n}),Object(Br.createElement)(Vr.IconButton,{icon:"editor-break",label:Object(_.__)("Submit"),type:"submit"}))))}}]),t}(Br.Component),ha=function(e){function t(){return Object(Rr.a)(this,t),Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDirty,r=t.editsReference,o=t.isAutosaveable;e.isDirty===n&&e.isAutosaveable===o&&e.editsReference===r||this.toggleTimer(n&&o)}},{key:"componentWillUnmount",value:function(){this.toggleTimer(!1)}},{key:"toggleTimer",value:function(e){var t=this;clearTimeout(this.pendingSave);var n=this.props.autosaveInterval;e&&(this.pendingSave=setTimeout(function(){return t.props.autosave()},1e3*n))}},{key:"render",value:function(){return null}}]),t}(Br.Component),ma=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.isEditedPostDirty,r=t.isEditedPostAutosaveable,o=t.getEditorSettings,i=t.getReferenceByDistinctEdits,c=o().autosaveInterval;return{isDirty:n(),isAutosaveable:r(),editsReference:i(),autosaveInterval:c}}),Object(l.withDispatch)(function(e){return{autosave:e("core/editor").autosave}})])(ha),va=function(e){var t=e.children,n=e.isValid,r=e.level,o=e.onClick,i=e.path,c=void 0===i?[]:i;return Object(Br.createElement)("li",{className:Lr()("document-outline__item","is-".concat(r.toLowerCase()),{"is-invalid":!n})},Object(Br.createElement)("button",{className:"document-outline__button",onClick:o},Object(Br.createElement)("span",{className:"document-outline__emdash","aria-hidden":"true"}),c.map(function(e,t){var n=e.clientId;return Object(Br.createElement)("strong",{key:t,className:"document-outline__level"},Object(Br.createElement)(fi,{clientId:n}))}),Object(Br.createElement)("strong",{className:"document-outline__level"},r),Object(Br.createElement)("span",{className:"document-outline__item-content"},t),Object(Br.createElement)("span",{className:"screen-reader-text"},Object(_.__)("(Click to focus this heading)"))))},Oa=Object(Br.createElement)("em",null,Object(_.__)("(Empty heading)")),ga=[Object(Br.createElement)("br",{key:"incorrect-break"}),Object(Br.createElement)("em",{key:"incorrect-message"},Object(_.__)("(Incorrect heading level)"))],ja=[Object(Br.createElement)("br",{key:"incorrect-break-h1"}),Object(Br.createElement)("em",{key:"incorrect-message-h1"},Object(_.__)("(Your theme may already use a H1 for the post title)"))],ya=[Object(Br.createElement)("br",{key:"incorrect-break-multiple-h1"}),Object(Br.createElement)("em",{key:"incorrect-message-multiple-h1"},Object(_.__)("(Multiple H1 headings are not recommended)"))],ka=function(e){return!e.attributes.content||0===e.attributes.content.length},_a=Object(Ar.compose)(Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=t.getBlocks,o=(0,e("core").getPostType)(n("type"));return{title:n("title"),blocks:r(),isTitleSupported:Object(O.get)(o,["supports","title"],!1)}}),Object(l.withDispatch)(function(e){return{onSelect:e("core/editor").selectBlock}}))(function(e){var t=e.blocks,n=void 0===t?[]:t,r=e.title,o=e.onSelect,i=e.isTitleSupported,c=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return Object(O.flatMap)(t,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return"core/heading"===t.name?Object(d.a)({},t,{path:n,level:t.attributes.level,isEmpty:ka(t)}):e(t.innerBlocks,Object(b.a)(n).concat([t]))})}(n);if(c.length<1)return null;var s=1,l=i&&r,u=Object(O.countBy)(c,"level")[1]>1;return Object(Br.createElement)("div",{className:"document-outline"},Object(Br.createElement)("ul",null,l&&Object(Br.createElement)(va,{level:Object(_.__)("Title"),isValid:!0,onClick:function(){var e=document.querySelector(".editor-post-title__input");e&&e.focus()}},r),c.map(function(e,t){var n=e.level>s+1,r=!(e.isEmpty||n||!e.level||1===e.level&&(u||l));return s=e.level,Object(Br.createElement)(va,{key:t,level:"H".concat(e.level),isValid:r,onClick:function(){return t=e.clientId,o(t);var t},path:e.path},e.isEmpty?Oa:Object(a.getTextContent)(Object(a.create)({html:e.attributes.content})),n&&ga,1===e.level&&u&&ya,l&&1===e.level&&!u&&ja)})))});var Ea=Object(l.withSelect)(function(e){return{blocks:e("core/editor").getBlocks()}})(function(e){var t=e.blocks,n=e.children;return Object(O.filter)(t,function(e){return"core/heading"===e.name}).length<1?null:n});var Sa=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=e("core/editor"),r=n.getBlocksByClientId,o=n.getBlockIndex,c=n.getTemplateLock,a=n.getBlockRootClientId,s=r(t.clientIds),l=Object(O.every)(s,function(e){return!!e&&Object(i.hasBlockSupport)(e.name,"multiple",!0)}),u=a(t.clientIds[0]);return{firstSelectedIndex:o(Object(O.first)(Object(O.castArray)(t.clientIds)),u),lastSelectedIndex:o(Object(O.last)(Object(O.castArray)(t.clientIds)),u),isLocked:!!c(u),blocks:s,canDuplicate:l,rootClientId:u,extraProps:t}}),Object(l.withDispatch)(function(e,t){var n=t.clientIds,r=t.rootClientId,o=t.blocks,c=t.firstSelectedIndex,a=t.lastSelectedIndex,s=t.isLocked,l=t.canDuplicate,u=e("core/editor"),d=u.insertBlocks,p=u.multiSelect,b=u.removeBlocks,f=u.insertDefaultBlock;return{onDuplicate:function(){if(!s&&l){var e=o.map(function(e){return Object(i.cloneBlock)(e)});d(e,a+1,r),e.length>1&&p(Object(O.first)(e).clientId,Object(O.last)(e).clientId)}},onRemove:function(){s||b(n)},onInsertBefore:function(){s||f({},r,c)},onInsertAfter:function(){s||f({},r,a+1)}}})])(function(e){var t=e.onDuplicate,n=e.onRemove,r=e.onInsertBefore,o=e.onInsertAfter,i=e.isLocked,c=e.canDuplicate;return(0,e.children)({onDuplicate:t,onRemove:n,onInsertAfter:o,onInsertBefore:r,isLocked:i,canDuplicate:c})}),Ca=function(e){return e.preventDefault(),e},wa={duplicate:{raw:yo.rawShortcut.primaryShift("d"),display:yo.displayShortcut.primaryShift("d")},removeBlock:{raw:yo.rawShortcut.access("z"),display:yo.displayShortcut.access("z")},insertBefore:{raw:yo.rawShortcut.primaryAlt("t"),display:yo.displayShortcut.primaryAlt("t")},insertAfter:{raw:yo.rawShortcut.primaryAlt("y"),display:yo.displayShortcut.primaryAlt("y")}},Ta=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).selectAll=e.selectAll.bind(Object(Hr.a)(Object(Hr.a)(e))),e.undoOrRedo=e.undoOrRedo.bind(Object(Hr.a)(Object(Hr.a)(e))),e.save=e.save.bind(Object(Hr.a)(Object(Hr.a)(e))),e.deleteSelectedBlocks=e.deleteSelectedBlocks.bind(Object(Hr.a)(Object(Hr.a)(e))),e.clearMultiSelection=e.clearMultiSelection.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"selectAll",value:function(e){var t=this.props,n=t.rootBlocksClientIds,r=t.onMultiSelect;e.preventDefault(),r(Object(O.first)(n),Object(O.last)(n))}},{key:"undoOrRedo",value:function(e){var t=this.props,n=t.onRedo,r=t.onUndo;e.shiftKey?n():r(),e.preventDefault()}},{key:"save",value:function(e){e.preventDefault(),this.props.onSave()}},{key:"deleteSelectedBlocks",value:function(e){var t=this.props,n=t.selectedBlockClientIds,r=t.hasMultiSelection,o=t.onRemove,i=t.isLocked;r&&(e.preventDefault(),i||o(n))}},{key:"clearMultiSelection",value:function(){var e=this.props,t=e.hasMultiSelection,n=e.clearSelectedBlock;t&&(n(),window.getSelection().removeAllRanges())}},{key:"render",value:function(){var e,t=this.props.selectedBlockClientIds;return Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(Vr.KeyboardShortcuts,{shortcuts:(e={},Object(f.a)(e,yo.rawShortcut.primary("a"),this.selectAll),Object(f.a)(e,yo.rawShortcut.primary("z"),this.undoOrRedo),Object(f.a)(e,yo.rawShortcut.primaryShift("z"),this.undoOrRedo),Object(f.a)(e,"backspace",this.deleteSelectedBlocks),Object(f.a)(e,"del",this.deleteSelectedBlocks),Object(f.a)(e,"escape",this.clearMultiSelection),e)}),Object(Br.createElement)(Vr.KeyboardShortcuts,{bindGlobal:!0,shortcuts:Object(f.a)({},yo.rawShortcut.primary("s"),this.save)}),t.length>0&&Object(Br.createElement)(Sa,{clientIds:t},function(e){var t,n=e.onDuplicate,r=e.onRemove,o=e.onInsertAfter,i=e.onInsertBefore;return Object(Br.createElement)(Vr.KeyboardShortcuts,{bindGlobal:!0,shortcuts:(t={},Object(f.a)(t,wa.duplicate.raw,Object(O.flow)(Ca,n)),Object(f.a)(t,wa.removeBlock.raw,Object(O.flow)(Ca,r)),Object(f.a)(t,wa.insertBefore.raw,Object(O.flow)(Ca,i)),Object(f.a)(t,wa.insertAfter.raw,Object(O.flow)(Ca,o)),t)})}))}}]),t}(Br.Component),Pa=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getBlockOrder,r=t.getMultiSelectedBlockClientIds,o=t.hasMultiSelection,i=t.isEditedPostDirty,c=t.getBlockRootClientId,a=t.getTemplateLock,s=(0,t.getSelectedBlockClientId)(),l=s?[s]:r();return{rootBlocksClientIds:n(),hasMultiSelection:o(),isLocked:Object(O.some)(l,function(e){return!!a(c(e))}),isDirty:i(),selectedBlockClientIds:l}}),Object(l.withDispatch)(function(e,t){var n=e("core/editor"),r=n.clearSelectedBlock,o=n.multiSelect,i=n.redo,c=n.undo,a=n.removeBlocks,s=n.savePost;return{onSave:function(){t.isDirty&&s()},clearSelectedBlock:r,onMultiSelect:o,onRedo:i,onUndo:c,onRemove:a}})])(Ta);var Ia=Object(Ar.compose)([Object(l.withSelect)(function(e){return{hasRedo:e("core/editor").hasEditorRedo()}}),Object(l.withDispatch)(function(e){return{redo:e("core/editor").redo}})])(function(e){var t=e.hasRedo,n=e.redo;return Object(Br.createElement)(Vr.IconButton,{icon:"redo",label:Object(_.__)("Redo"),shortcut:yo.displayShortcut.primaryShift("z"),"aria-disabled":!t,onClick:t?n:void 0,className:"editor-history__redo"})});var Ba=Object(Ar.compose)([Object(l.withSelect)(function(e){return{hasUndo:e("core/editor").hasEditorUndo()}}),Object(l.withDispatch)(function(e){return{undo:e("core/editor").undo}})])(function(e){var t=e.hasUndo,n=e.undo;return Object(Br.createElement)(Vr.IconButton,{icon:"undo",label:Object(_.__)("Undo"),shortcut:yo.displayShortcut.primary("z"),"aria-disabled":!t,onClick:t?n:void 0,className:"editor-history__undo"})});var xa=Object(Ar.compose)([Object(l.withSelect)(function(e){return{isValid:e("core/editor").isValidTemplate()}}),Object(l.withDispatch)(function(e){var t=e("core/editor"),n=t.setTemplateValidity;return{resetTemplateValidity:function(){return n(!0)},synchronizeTemplate:t.synchronizeTemplate}})])(function(e){var t=e.isValid,n=Object(p.a)(e,["isValid"]);return t?null:Object(Br.createElement)(Vr.Notice,{className:"editor-template-validation-notice",isDismissible:!1,status:"warning"},Object(Br.createElement)("p",null,Object(_.__)("The content of your post doesn’t match the template assigned to your post type.")),Object(Br.createElement)("div",null,Object(Br.createElement)(Vr.Button,{isDefault:!0,onClick:n.resetTemplateValidity},Object(_.__)("Keep it as is")),Object(Br.createElement)(Vr.Button,{onClick:function(){window.confirm(Object(_.__)("Resetting the template may result in loss of content, do you want to continue?"))&&n.synchronizeTemplate()},isPrimary:!0},Object(_.__)("Reset the template"))))});var La=Object(Ar.compose)([Object(l.withSelect)(function(e){return{notices:e("core/notices").getNotices()}}),Object(l.withDispatch)(function(e){return{onRemove:e("core/notices").removeNotice}})])(function(e){return Object(Br.createElement)(Vr.NoticeList,e,Object(Br.createElement)(xa,null))});var Aa=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=t.getEditorSettings,o=e("core").getPostType,i=r().availableTemplates;return{postType:o(n("type")),availableTemplates:i}})(function(e){var t=e.availableTemplates,n=e.postType,r=e.children;return!Object(O.get)(n,["supports","page-attributes"],!1)&&Object(O.isEmpty)(t)?null:r});var Na=Object(l.withSelect)(function(e){var t=e("core/editor").getEditedPostAttribute;return{postType:(0,e("core").getPostType)(t("type"))}})(function(e){var t=e.postType,n=e.children,r=e.supportKeys,o=!0;return t&&(o=Object(O.some)(Object(O.castArray)(r),function(e){return!!t.supports[e]})),o?n:null}),Ra=Object(Ar.withState)({orderInput:null})(function(e){var t=e.onUpdateOrder,n=e.order,r=void 0===n?0:n,o=e.orderInput,i=e.setState,c=null===o?r:o;return Object(Br.createElement)(Vr.TextControl,{className:"editor-page-attributes__order",type:"number",label:Object(_.__)("Order"),value:c,onChange:function(e){i({orderInput:e});var n=Number(e);Number.isInteger(n)&&""!==Object(O.invoke)(e,["trim"])&&t(Number(e))},size:6,onBlur:function(){i({orderInput:null})}})});var Da=Object(Ar.compose)([Object(l.withSelect)(function(e){return{order:e("core/editor").getEditedPostAttribute("menu_order")}}),Object(l.withDispatch)(function(e){return{onUpdateOrder:function(t){e("core/editor").editPost({menu_order:t})}}})])(function(e){return Object(Br.createElement)(Na,{supportKeys:"page-attributes"},Object(Br.createElement)(Ra,e))});function Fa(e){var t=Object(O.groupBy)(e,"parent");return function e(n){return n.map(function(n){var r=t[n.id];return Object(d.a)({},n,{children:r&&r.length?e(r):[]})})}(t[0]||[])}var Ma=Object(l.withSelect)(function(e){var t=e("core"),n=t.getPostType,r=t.getEntityRecords,o=e("core/editor"),i=o.getCurrentPostId,c=o.getEditedPostAttribute,a=c("type"),s=n(a),l=i(),u=Object(O.get)(s,["hierarchical"],!1),d={per_page:-1,exclude:l,parent_exclude:l,orderby:"menu_order",order:"asc"};return{parent:c("parent"),items:u?r("postType",a,d):[],postType:s}}),Ua=Object(l.withDispatch)(function(e){var t=e("core/editor").editPost;return{onUpdateParent:function(e){t({parent:e||0})}}}),Ha=Object(Ar.compose)([Ma,Ua])(function(e){var t=e.parent,n=e.postType,r=e.items,o=e.onUpdateParent,i=Object(O.get)(n,["hierarchical"],!1),c=Object(O.get)(n,["labels","parent_item_colon"]),a=r||[];if(!i||!c||!a.length)return null;var s=Fa(a.map(function(e){return{id:e.id,parent:e.parent,name:e.title.raw?e.title.raw:"#".concat(e.id," (").concat(Object(_.__)("no title"),")")}}));return Object(Br.createElement)(Vr.TreeSelect,{label:c,noOptionLabel:"(".concat(Object(_.__)("no parent"),")"),tree:s,selectedId:t,onChange:o})});var Va=Object(Ar.compose)(Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=(0,t.getEditorSettings)().availableTemplates;return{selectedTemplate:n("template"),availableTemplates:r}}),Object(l.withDispatch)(function(e){return{onUpdate:function(t){e("core/editor").editPost({template:t||""})}}}))(function(e){var t=e.availableTemplates,n=e.selectedTemplate,r=e.onUpdate;return Object(O.isEmpty)(t)?null:Object(Br.createElement)(Vr.SelectControl,{label:Object(_.__)("Template:"),value:n,onChange:r,className:"editor-page-attributes__template",options:Object(O.map)(t,function(e,t){return{value:t,label:e}})})});var Ka=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor").getCurrentPost();return{hasAssignAuthorAction:Object(O.get)(t,["_links","wp:action-assign-author"],!1),postType:e("core/editor").getCurrentPostType(),authors:e("core").getAuthors()}}),Ar.withInstanceId])(function(e){var t=e.hasAssignAuthorAction,n=e.authors,r=e.children;return!t||n.length<2?null:Object(Br.createElement)(Na,{supportKeys:"author"},r)}),za=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).setAuthorId=e.setAuthorId.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"setAuthorId",value:function(e){var t=this.props.onUpdateAuthor,n=e.target.value;t(Number(n))}},{key:"render",value:function(){var e=this.props,t=e.postAuthor,n=e.instanceId,r=e.authors,o="post-author-selector-"+n;return Object(Br.createElement)(Ka,null,Object(Br.createElement)("label",{htmlFor:o},Object(_.__)("Author")),Object(Br.createElement)("select",{id:o,value:t,onChange:this.setAuthorId,className:"editor-post-author__select"},r.map(function(e){return Object(Br.createElement)("option",{key:e.id,value:e.id},e.name)})))}}]),t}(Br.Component),Wa=Object(Ar.compose)([Object(l.withSelect)(function(e){return{postAuthor:e("core/editor").getEditedPostAttribute("author"),authors:e("core").getAuthors()}}),Object(l.withDispatch)(function(e){return{onUpdateAuthor:function(t){e("core/editor").editPost({author:t})}}}),Ar.withInstanceId])(za);var qa=Object(Ar.compose)([Object(l.withSelect)(function(e){return{commentStatus:e("core/editor").getEditedPostAttribute("comment_status")}}),Object(l.withDispatch)(function(e){return{editPost:e("core/editor").editPost}})])(function(e){var t=e.commentStatus,n=void 0===t?"open":t,r=Object(p.a)(e,["commentStatus"]);return Object(Br.createElement)(Vr.CheckboxControl,{label:Object(_.__)("Allow Comments"),checked:"open"===n,onChange:function(){return r.editPost({comment_status:"open"===n?"closed":"open"})}})});var Ga=Object(Ar.compose)([Object(l.withSelect)(function(e){return{excerpt:e("core/editor").getEditedPostAttribute("excerpt")}}),Object(l.withDispatch)(function(e){return{onUpdateExcerpt:function(t){e("core/editor").editPost({excerpt:t})}}})])(function(e){var t=e.excerpt,n=e.onUpdateExcerpt;return Object(Br.createElement)("div",{className:"editor-post-excerpt"},Object(Br.createElement)(Vr.TextareaControl,{label:Object(_.__)("Write an excerpt (optional)"),className:"editor-post-excerpt__textarea",onChange:function(e){return n(e)},value:t}),Object(Br.createElement)(Vr.ExternalLink,{href:"https://codex.wordpress.org/Excerpt"},Object(_.__)("Learn more about manual excerpts")))});var $a=function(e){return Object(Br.createElement)(Na,Object(Ir.a)({},e,{supportKeys:"excerpt"}))};var Ya=Object(l.withSelect)(function(e){var t=e("core").getThemeSupports;return{postType:(0,e("core/editor").getEditedPostAttribute)("type"),themeSupports:t()}})(function(e){var t=e.themeSupports,n=e.children,r=e.postType,o=e.supportKeys;return Object(O.some)(Object(O.castArray)(o),function(e){var n=Object(O.get)(t,[e],!1);return"post-thumbnails"===e&&Object(O.isArray)(n)?Object(O.includes)(n,r):n})?n:null});var Qa=function(e){return Object(Br.createElement)(Ya,{supportKeys:"post-thumbnails"},Object(Br.createElement)(Na,Object(Ir.a)({},e,{supportKeys:"thumbnail"})))},Xa=["image"],Za=Object(_.__)("Featured Image"),Ja=Object(_.__)("Set featured image"),es=Object(_.__)("Remove image");var ts=Object(l.withSelect)(function(e){var t=e("core"),n=t.getMedia,r=t.getPostType,o=e("core/editor"),i=o.getCurrentPostId,c=o.getEditedPostAttribute,a=c("featured_media");return{media:a?n(a):null,currentPostId:i(),postType:r(c("type")),featuredImageId:a}}),ns=Object(l.withDispatch)(function(e){var t=e("core/editor").editPost;return{onUpdateImage:function(e){t({featured_media:e.id})},onRemoveImage:function(){t({featured_media:0})}}}),rs=Object(Ar.compose)(ts,ns,Object(Vr.withFilters)("editor.PostFeaturedImage"))(function(e){var t,n,r,o=e.currentPostId,i=e.featuredImageId,c=e.onUpdateImage,a=e.onRemoveImage,s=e.media,l=e.postType,u=Object(O.get)(l,["labels"],{}),d=Object(Br.createElement)("p",null,Object(_.__)("To edit the featured image, you need permission to upload media."));if(s){var p=Object(Nr.applyFilters)("editor.PostFeaturedImage.imageSize","post-thumbnail",s.id,o);Object(O.has)(s,["media_details","sizes",p])?(t=s.media_details.sizes[p].width,n=s.media_details.sizes[p].height,r=s.media_details.sizes[p].source_url):(t=s.media_details.width,n=s.media_details.height,r=s.source_url)}return Object(Br.createElement)(Qa,null,Object(Br.createElement)("div",{className:"editor-post-featured-image"},!!i&&Object(Br.createElement)(Xo,{fallback:d},Object(Br.createElement)(ea,{title:u.featured_image||Za,onSelect:c,allowedTypes:Xa,modalClass:"editor-post-featured-image__media-modal",render:function(e){var o=e.open;return Object(Br.createElement)(Vr.Button,{className:"editor-post-featured-image__preview",onClick:o,"aria-label":Object(_.__)("Edit or update the image")},s&&Object(Br.createElement)(Vr.ResponsiveWrapper,{naturalWidth:t,naturalHeight:n},Object(Br.createElement)("img",{src:r,alt:""})),!s&&Object(Br.createElement)(Vr.Spinner,null))},value:i})),!!i&&s&&!s.isLoading&&Object(Br.createElement)(Xo,null,Object(Br.createElement)(ea,{title:u.featured_image||Za,onSelect:c,allowedTypes:Xa,modalClass:"editor-post-featured-image__media-modal",render:function(e){var t=e.open;return Object(Br.createElement)(Vr.Button,{onClick:t,isDefault:!0,isLarge:!0},Object(_.__)("Replace image"))}})),!i&&Object(Br.createElement)("div",null,Object(Br.createElement)(Xo,{fallback:d},Object(Br.createElement)(ea,{title:u.featured_image||Za,onSelect:c,allowedTypes:Xa,modalClass:"editor-post-featured-image__media-modal",render:function(e){var t=e.open;return Object(Br.createElement)(Vr.Button,{className:"editor-post-featured-image__toggle",onClick:t},u.set_featured_image||Ja)}}))),!!i&&Object(Br.createElement)(Xo,null,Object(Br.createElement)(Vr.Button,{onClick:a,isLink:!0,isDestructive:!0},u.remove_featured_image||es))))});var os=Object(l.withSelect)(function(e){return{disablePostFormats:e("core/editor").getEditorSettings().disablePostFormats}})(function(e){var t=e.disablePostFormats,n=Object(p.a)(e,["disablePostFormats"]);return!t&&Object(Br.createElement)(Na,Object(Ir.a)({},n,{supportKeys:"post-formats"}))}),is=[{id:"aside",caption:Object(_.__)("Aside")},{id:"gallery",caption:Object(_.__)("Gallery")},{id:"link",caption:Object(_.__)("Link")},{id:"image",caption:Object(_.__)("Image")},{id:"quote",caption:Object(_.__)("Quote")},{id:"standard",caption:Object(_.__)("Standard")},{id:"status",caption:Object(_.__)("Status")},{id:"video",caption:Object(_.__)("Video")},{id:"audio",caption:Object(_.__)("Audio")},{id:"chat",caption:Object(_.__)("Chat")}];var cs=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=t.getSuggestedPostFormat,o=n("format"),i=e("core").getThemeSupports();return{postFormat:o,supportedFormats:Object(O.union)([o],Object(O.get)(i,["formats"],[])),suggestedFormat:r()}}),Object(l.withDispatch)(function(e){return{onUpdatePostFormat:function(t){e("core/editor").editPost({format:t})}}}),Ar.withInstanceId])(function(e){var t=e.onUpdatePostFormat,n=e.postFormat,r=void 0===n?"standard":n,o=e.supportedFormats,i=e.suggestedFormat,c="post-format-selector-"+e.instanceId,a=is.filter(function(e){return Object(O.includes)(o,e.id)}),s=Object(O.find)(a,function(e){return e.id===i});return Object(Br.createElement)(os,null,Object(Br.createElement)("div",{className:"editor-post-format"},Object(Br.createElement)("div",{className:"editor-post-format__content"},Object(Br.createElement)("label",{htmlFor:c},Object(_.__)("Post Format")),Object(Br.createElement)(Vr.SelectControl,{value:r,onChange:function(e){return t(e)},id:c,options:a.map(function(e){return{label:e.caption,value:e.id}})})),s&&s.id!==r&&Object(Br.createElement)("div",{className:"editor-post-format__suggestion"},Object(_.__)("Suggestion:")," ",Object(Br.createElement)(Vr.Button,{isLink:!0,onClick:function(){return t(s.id)}},s.caption))))});var as=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getCurrentPostLastRevisionId,r=t.getCurrentPostRevisionsCount;return{lastRevisionId:n(),revisionsCount:r()}})(function(e){var t=e.lastRevisionId,n=e.revisionsCount,r=e.children;return!t||n<2?null:Object(Br.createElement)(Na,{supportKeys:"revisions"},r)});var ss=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getCurrentPostLastRevisionId,r=t.getCurrentPostRevisionsCount;return{lastRevisionId:n(),revisionsCount:r()}})(function(e){var t=e.lastRevisionId,n=e.revisionsCount;return Object(Br.createElement)(as,null,Object(Br.createElement)(Vr.IconButton,{href:ia("revision.php",{revision:t,gutenberg:!0}),className:"editor-post-last-revision__title",icon:"backup"},Object(_.sprintf)(Object(_._n)("%d Revision","%d Revisions",n),n)))}),ls=n(99),us=n.n(ls);var ds=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).openPreviewWindow=e.openPreviewWindow.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidUpdate",value:function(e){var t=this.props.previewLink;t&&!e.previewLink&&this.setPreviewWindowLink(t)}},{key:"setPreviewWindowLink",value:function(e){var t=this.previewWindow;t&&!t.closed&&(t.location=e)}},{key:"getWindowTarget",value:function(){var e=this.props.postId;return"wp-preview-".concat(e)}},{key:"openPreviewWindow",value:function(e){var t,n;(e.preventDefault(),this.previewWindow&&!this.previewWindow.closed||(this.previewWindow=window.open("",this.getWindowTarget())),this.previewWindow.focus(),this.props.isAutosaveable)?(this.props.isDraft?this.props.savePost({isPreview:!0}):this.props.autosave({isPreview:!0}),t=this.previewWindow.document,n=Object(Br.renderToString)(Object(Br.createElement)("div",{className:"editor-post-preview-button__interstitial-message"},Object(Br.createElement)(Vr.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 96 96"},Object(Br.createElement)(Vr.Path,{className:"outer",d:"M48 12c19.9 0 36 16.1 36 36S67.9 84 48 84 12 67.9 12 48s16.1-36 36-36",fill:"none"}),Object(Br.createElement)(Vr.Path,{className:"inner",d:"M69.5 46.4c0-3.9-1.4-6.7-2.6-8.8-1.6-2.6-3.1-4.9-3.1-7.5 0-2.9 2.2-5.7 5.4-5.7h.4C63.9 19.2 56.4 16 48 16c-11.2 0-21 5.7-26.7 14.4h2.1c3.3 0 8.5-.4 8.5-.4 1.7-.1 1.9 2.4.2 2.6 0 0-1.7.2-3.7.3L40 67.5l7-20.9L42 33c-1.7-.1-3.3-.3-3.3-.3-1.7-.1-1.5-2.7.2-2.6 0 0 5.3.4 8.4.4 3.3 0 8.5-.4 8.5-.4 1.7-.1 1.9 2.4.2 2.6 0 0-1.7.2-3.7.3l11.5 34.3 3.3-10.4c1.6-4.5 2.4-7.8 2.4-10.5zM16.1 48c0 12.6 7.3 23.5 18 28.7L18.8 35c-1.7 4-2.7 8.4-2.7 13zm32.5 2.8L39 78.6c2.9.8 5.9 1.3 9 1.3 3.7 0 7.3-.6 10.6-1.8-.1-.1-.2-.3-.2-.4l-9.8-26.9zM76.2 36c0 3.2-.6 6.9-2.4 11.4L64 75.6c9.5-5.5 15.9-15.8 15.9-27.6 0-5.5-1.4-10.8-3.9-15.3.1 1 .2 2.1.2 3.3z",fill:"none"})),Object(Br.createElement)("p",null,Object(_.__)("Generating preview…")))),n+='\n\t\t<style>\n\t\t\tbody {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t\t.editor-post-preview-button__interstitial-message {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\theight: 100vh;\n\t\t\t\twidth: 100vw;\n\t\t\t}\n\t\t\t@-webkit-keyframes paint {\n\t\t\t\t0% {\n\t\t\t\t\tstroke-dashoffset: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t@-moz-keyframes paint {\n\t\t\t\t0% {\n\t\t\t\t\tstroke-dashoffset: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t@-o-keyframes paint {\n\t\t\t\t0% {\n\t\t\t\t\tstroke-dashoffset: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t@keyframes paint {\n\t\t\t\t0% {\n\t\t\t\t\tstroke-dashoffset: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t.editor-post-preview-button__interstitial-message svg {\n\t\t\t\twidth: 192px;\n\t\t\t\theight: 192px;\n\t\t\t\tstroke: #555d66;\n\t\t\t\tstroke-width: 0.75;\n\t\t\t}\n\t\t\t.editor-post-preview-button__interstitial-message svg .outer,\n\t\t\t.editor-post-preview-button__interstitial-message svg .inner {\n\t\t\t\tstroke-dasharray: 280;\n\t\t\t\tstroke-dashoffset: 280;\n\t\t\t\t-webkit-animation: paint 1.5s ease infinite alternate;\n\t\t\t\t-moz-animation: paint 1.5s ease infinite alternate;\n\t\t\t\t-o-animation: paint 1.5s ease infinite alternate;\n\t\t\t\tanimation: paint 1.5s ease infinite alternate;\n\t\t\t}\n\t\t\tp {\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;\n\t\t\t}\n\t\t</style>\n\t',t.write(n),t.title=Object(_.__)("Generating preview…"),t.close()):this.setPreviewWindowLink(e.target.href)}},{key:"render",value:function(){var e=this.props,t=e.previewLink,n=e.currentPostLink,r=e.isSaveable,o=t||n;return Object(Br.createElement)(Vr.Button,{isLarge:!0,className:"editor-post-preview",href:o,target:this.getWindowTarget(),disabled:!r,onClick:this.openPreviewWindow},Object(_._x)("Preview","imperative verb"),Object(Br.createElement)("span",{className:"screen-reader-text"},Object(_.__)("(opens in a new tab)")),Object(Br.createElement)(c.DotTip,{tipId:"core/editor.preview"},Object(_.__)("Click “Preview” to load a preview of this page, so you can make sure you’re happy with your blocks.")))}}]),t}(Br.Component),ps=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.forcePreviewLink,r=t.forceIsAutosaveable,o=e("core/editor"),i=o.getCurrentPostId,c=o.getCurrentPostAttribute,a=o.getEditedPostAttribute,s=o.isEditedPostSaveable,l=o.isEditedPostAutosaveable,u=o.getEditedPostPreviewLink,d=e("core").getPostType,p=u(),b=d(a("type"));return{postId:i(),currentPostLink:c("link"),previewLink:void 0!==n?n:p,isSaveable:s(),isAutosaveable:r||l(),isViewable:Object(O.get)(b,["viewable"],!1),isDraft:-1!==["draft","auto-draft"].indexOf(a("status"))}}),Object(l.withDispatch)(function(e){return{autosave:e("core/editor").autosave,savePost:e("core/editor").savePost}}),Object(Ar.ifCondition)(function(e){return e.isViewable})])(ds),bs=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).sendPostLock=e.sendPostLock.bind(Object(Hr.a)(Object(Hr.a)(e))),e.receivePostLock=e.receivePostLock.bind(Object(Hr.a)(Object(Hr.a)(e))),e.releasePostLock=e.releasePostLock.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){us()(document).on("heartbeat-send.refresh-lock",this.sendPostLock).on("heartbeat-tick.refresh-lock",this.receivePostLock)}},{key:"componentWillUnmount",value:function(){us()(document).off("heartbeat-send.refresh-lock",this.sendPostLock).off("heartbeat-tick.refresh-lock",this.receivePostLock)}},{key:"sendPostLock",value:function(e,t){var n=this.props,r=n.isLocked,o=n.activePostLock,i=n.postId;r||(t["wp-refresh-post-lock"]={lock:o,post_id:i})}},{key:"receivePostLock",value:function(e,t){if(t["wp-refresh-post-lock"]){var n=this.props,r=n.autosave,o=n.updatePostLock,i=t["wp-refresh-post-lock"];i.lock_error?(r(),o({isLocked:!0,isTakeover:!0,user:{avatar:i.lock_error.avatar_src}})):i.new_lock&&o({isLocked:!1,activePostLock:i.new_lock})}}},{key:"releasePostLock",value:function(){var e=this.props,t=e.isLocked,n=e.activePostLock,r=e.postLockUtils,o=e.postId;if(!t&&n){var i={action:"wp-remove-post-lock",_wpnonce:r.unlockNonce,post_ID:o,active_post_lock:n};us.a.post({async:!1,url:r.ajaxUrl,data:i})}}},{key:"render",value:function(){var e=this.props,t=e.user,n=e.postId,r=e.isLocked,o=e.isTakeover,i=e.postLockUtils,c=e.postType;if(!r)return null;var a=t.name,s=t.avatar,l=Object(g.addQueryArgs)("post.php",{"get-post-lock":"1",lockKey:!0,post:n,action:"edit",_wpnonce:i.nonce}),u=ia("edit.php",{post_type:Object(O.get)(c,["slug"])}),d=Object(O.get)(c,["labels","all_items"]);return Object(Br.createElement)(Vr.Modal,{title:o?Object(_.__)("Someone else has taken over this post."):Object(_.__)("This post is already being edited."),focusOnMount:!0,shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!1,isDismissable:!1,className:"editor-post-locked-modal"},!!s&&Object(Br.createElement)("img",{src:s,alt:Object(_.__)("Avatar"),className:"editor-post-locked-modal__avatar"}),!!o&&Object(Br.createElement)("div",null,Object(Br.createElement)("div",null,a?Object(_.sprintf)(Object(_.__)("%s now has editing control of this post. Don’t worry, your changes up to this moment have been saved."),a):Object(_.__)("Another user now has editing control of this post. Don’t worry, your changes up to this moment have been saved.")),Object(Br.createElement)("div",{className:"editor-post-locked-modal__buttons"},Object(Br.createElement)(Vr.Button,{isPrimary:!0,isLarge:!0,href:u},d))),!o&&Object(Br.createElement)("div",null,Object(Br.createElement)("div",null,a?Object(_.sprintf)(Object(_.__)("%s is currently working on this post, which means you cannot make changes, unless you take over."),a):Object(_.__)("Another user is currently working on this post, which means you cannot make changes, unless you take over.")),Object(Br.createElement)("div",{className:"editor-post-locked-modal__buttons"},Object(Br.createElement)(Vr.Button,{isDefault:!0,isLarge:!0,href:u},d),Object(Br.createElement)(ps,null),Object(Br.createElement)(Vr.Button,{isPrimary:!0,isLarge:!0,href:l},Object(_.__)("Take Over")))))}}]),t}(Br.Component),fs=Object(Ar.compose)(Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditorSettings,r=t.isPostLocked,o=t.isPostLockTakeover,i=t.getPostLockUser,c=t.getCurrentPostId,a=t.getActivePostLock,s=t.getEditedPostAttribute,l=e("core").getPostType;return{isLocked:r(),isTakeover:o(),user:i(),postId:c(),postLockUtils:n().postLockUtils,activePostLock:a(),postType:l(s("type"))}}),Object(l.withDispatch)(function(e){var t=e("core/editor");return{autosave:t.autosave,updatePostLock:t.updatePostLock}}),Object(Ar.withGlobalEvents)({beforeunload:"releasePostLock"}))(bs);var hs=Object(Ar.compose)(Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.isCurrentPostPublished,r=t.getCurrentPostType,o=t.getCurrentPost;return{hasPublishAction:Object(O.get)(o(),["_links","wp:action-publish"],!1),isPublished:n(),postType:r()}}))(function(e){var t=e.hasPublishAction,n=e.isPublished,r=e.children;return n||!t?null:r});var ms=Object(Ar.compose)(Object(l.withSelect)(function(e){return{status:e("core/editor").getEditedPostAttribute("status")}}),Object(l.withDispatch)(function(e){return{onUpdateStatus:function(t){e("core/editor").editPost({status:t})}}}))(function(e){var t=e.status,n=e.onUpdateStatus;return Object(Br.createElement)(hs,null,Object(Br.createElement)(Vr.CheckboxControl,{label:Object(_.__)("Pending Review"),checked:"pending"===t,onChange:function(){n("pending"===t?"draft":"pending")}}))});var vs=Object(Ar.compose)([Object(l.withSelect)(function(e){return{pingStatus:e("core/editor").getEditedPostAttribute("ping_status")}}),Object(l.withDispatch)(function(e){return{editPost:e("core/editor").editPost}})])(function(e){var t=e.pingStatus,n=void 0===t?"open":t,r=Object(p.a)(e,["pingStatus"]);return Object(Br.createElement)(Vr.CheckboxControl,{label:Object(_.__)("Allow Pingbacks & Trackbacks"),checked:"open"===n,onChange:function(){return r.editPost({ping_status:"open"===n?"closed":"open"})}})});var Os=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.forceIsSaving,r=e("core/editor"),o=r.isCurrentPostPublished,i=r.isEditedPostBeingScheduled,c=r.isSavingPost,a=r.isPublishingPost,s=r.getCurrentPost,l=r.getCurrentPostType,u=r.isAutosavingPost;return{isPublished:o(),isBeingScheduled:i(),isSaving:n||c(),isPublishing:a(),hasPublishAction:Object(O.get)(s(),["_links","wp:action-publish"],!1),postType:l(),isAutosaving:u()}})])(function(e){var t=e.isPublished,n=e.isBeingScheduled,r=e.isSaving,o=e.isPublishing,i=e.hasPublishAction,c=e.isAutosaving;return o?Object(_.__)("Publishing…"):t&&r&&!c?Object(_.__)("Updating…"):n&&r&&!c?Object(_.__)("Scheduling…"):i?t?Object(_.__)("Update"):n?Object(_.__)("Schedule"):Object(_.__)("Publish"):Object(_.__)("Submit for Review")}),gs=function(e){function t(e){var n;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).call(this,e))).buttonNode=Object(Br.createRef)(),n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){this.props.focusOnMount&&this.buttonNode.current.focus()}},{key:"render",value:function(){var e,t=this.props,n=t.forceIsDirty,r=t.forceIsSaving,o=t.hasPublishAction,i=t.isBeingScheduled,a=t.isOpen,s=t.isPostSavingLocked,l=t.isPublishable,u=t.isPublished,d=t.isSaveable,p=t.isSaving,b=t.isToggle,f=t.onSave,h=t.onStatusChange,m=t.onSubmit,v=void 0===m?O.noop:m,g=t.onToggle,j=t.visibility,y=p||r||!d||s||!l&&!n;e=o?i?"future":"private"===j?"private":"publish":"pending";var k={"aria-disabled":y,className:"editor-post-publish-button",isBusy:p&&u,isLarge:!0,isPrimary:!0,onClick:function(){y||(v(),h(e),f())}},E={"aria-disabled":u||p||r||!d||!l&&!n,"aria-expanded":a,className:"editor-post-publish-panel__toggle",isBusy:p&&u,isPrimary:!0,onClick:g},S=i?Object(_.__)("Schedule…"):Object(_.__)("Publish…"),C=Object(Br.createElement)(Os,{forceIsSaving:r}),w=b?E:k,T=b?S:C;return Object(Br.createElement)(Vr.Button,Object(Ir.a)({ref:this.buttonNode},w),T,Object(Br.createElement)(c.DotTip,{tipId:"core/editor.publish"},Object(_.__)("Finished writing? That’s great, let’s get this published right now. Just click “Publish” and you’re good to go.")))}}]),t}(Br.Component),js=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.isSavingPost,r=t.isEditedPostBeingScheduled,o=t.getEditedPostVisibility,i=t.isCurrentPostPublished,c=t.isEditedPostSaveable,a=t.isEditedPostPublishable,s=t.isPostSavingLocked,l=t.getCurrentPost,u=t.getCurrentPostType;return{isSaving:n(),isBeingScheduled:r(),visibility:o(),isSaveable:c(),isPostSavingLocked:s(),isPublishable:a(),isPublished:i(),hasPublishAction:Object(O.get)(l(),["_links","wp:action-publish"],!1),postType:u()}}),Object(l.withDispatch)(function(e){var t=e("core/editor"),n=t.editPost;return{onStatusChange:function(e){return n({status:e})},onSave:t.savePost}})])(gs),ys=[{value:"public",label:Object(_.__)("Public"),info:Object(_.__)("Visible to everyone.")},{value:"private",label:Object(_.__)("Private"),info:Object(_.__)("Only visible to site admins and editors.")},{value:"password",label:Object(_.__)("Password Protected"),info:Object(_.__)("Protected with a password you choose. Only those with the password can view this post.")}],ks=function(e){function t(e){var n;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).setPublic=n.setPublic.bind(Object(Hr.a)(Object(Hr.a)(n))),n.setPrivate=n.setPrivate.bind(Object(Hr.a)(Object(Hr.a)(n))),n.setPasswordProtected=n.setPasswordProtected.bind(Object(Hr.a)(Object(Hr.a)(n))),n.updatePassword=n.updatePassword.bind(Object(Hr.a)(Object(Hr.a)(n))),n.state={hasPassword:!!e.password},n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"setPublic",value:function(){var e=this.props,t=e.visibility,n=e.onUpdateVisibility,r=e.status;n("private"===t?"draft":r),this.setState({hasPassword:!1})}},{key:"setPrivate",value:function(){if(window.confirm(Object(_.__)("Would you like to privately publish this post now?"))){var e=this.props,t=e.onUpdateVisibility,n=e.onSave;t("private"),this.setState({hasPassword:!1}),n()}}},{key:"setPasswordProtected",value:function(){var e=this.props,t=e.visibility,n=e.onUpdateVisibility,r=e.status;n("private"===t?"draft":r,e.password||""),this.setState({hasPassword:!0})}},{key:"updatePassword",value:function(e){var t=this.props,n=t.status;(0,t.onUpdateVisibility)(n,e.target.value)}},{key:"render",value:function(){var e=this.props,t=e.visibility,n=e.password,r=e.instanceId,o={public:{onSelect:this.setPublic,checked:"public"===t&&!this.state.hasPassword},private:{onSelect:this.setPrivate,checked:"private"===t},password:{onSelect:this.setPasswordProtected,checked:this.state.hasPassword}};return[Object(Br.createElement)("fieldset",{key:"visibility-selector",className:"editor-post-visibility__dialog-fieldset"},Object(Br.createElement)("legend",{className:"editor-post-visibility__dialog-legend"},Object(_.__)("Post Visibility")),ys.map(function(e){var t=e.value,n=e.label,i=e.info;return Object(Br.createElement)("div",{key:t,className:"editor-post-visibility__choice"},Object(Br.createElement)("input",{type:"radio",name:"editor-post-visibility__setting-".concat(r),value:t,onChange:o[t].onSelect,checked:o[t].checked,id:"editor-post-".concat(t,"-").concat(r),"aria-describedby":"editor-post-".concat(t,"-").concat(r,"-description"),className:"editor-post-visibility__dialog-radio"}),Object(Br.createElement)("label",{htmlFor:"editor-post-".concat(t,"-").concat(r),className:"editor-post-visibility__dialog-label"},n),Object(Br.createElement)("p",{id:"editor-post-".concat(t,"-").concat(r,"-description"),className:"editor-post-visibility__dialog-info"},i))})),this.state.hasPassword&&Object(Br.createElement)("div",{className:"editor-post-visibility__dialog-password",key:"password-selector"},Object(Br.createElement)("label",{htmlFor:"editor-post-visibility__dialog-password-input-".concat(r),className:"screen-reader-text"},Object(_.__)("Create password")),Object(Br.createElement)("input",{className:"editor-post-visibility__dialog-password-input",id:"editor-post-visibility__dialog-password-input-".concat(r),type:"text",onChange:this.updatePassword,value:n,placeholder:Object(_.__)("Use a secure password")}))]}}]),t}(Br.Component),_s=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=t.getEditedPostVisibility;return{status:n("status"),visibility:r(),password:n("password")}}),Object(l.withDispatch)(function(e){var t=e("core/editor"),n=t.savePost,r=t.editPost;return{onSave:n,onUpdateVisibility:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;r({status:e,password:t})}}}),Ar.withInstanceId])(ks);var Es=Object(l.withSelect)(function(e){return{visibility:e("core/editor").getEditedPostVisibility()}})(function(e){var t=e.visibility;return Object(O.find)(ys,{value:t}).label});var Ss=Object(Ar.compose)([Object(l.withSelect)(function(e){return{date:e("core/editor").getEditedPostAttribute("date")}}),Object(l.withDispatch)(function(e){return{onUpdateDate:function(t){e("core/editor").editPost({date:t})}}})])(function(e){var t=e.date,n=e.onUpdateDate,r=Object(Ze.__experimentalGetSettings)(),o=/a(?!\\)/i.test(r.formats.time.toLowerCase().replace(/\\\\/g,"").split("").reverse().join(""));return Object(Br.createElement)(Vr.DateTimePicker,{key:"date-time-picker",currentDate:t,onChange:n,is12Hour:o})});var Cs=Object(l.withSelect)(function(e){return{date:e("core/editor").getEditedPostAttribute("date"),isFloating:e("core/editor").isEditedPostDateFloating()}})(function(e){var t=e.date,n=e.isFloating,r=Object(Ze.__experimentalGetSettings)();return t&&!n?Object(Ze.dateI18n)(r.formats.datetimeAbbreviated,t):Object(_.__)("Immediately")}),ws={per_page:-1,orderby:"count",order:"desc",_fields:"id,name"},Ts=function(e,t){return e.toLowerCase()===t.toLowerCase()},Ps=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onChange=e.onChange.bind(Object(Hr.a)(Object(Hr.a)(e))),e.searchTerms=Object(O.throttle)(e.searchTerms.bind(Object(Hr.a)(Object(Hr.a)(e))),500),e.findOrCreateTerm=e.findOrCreateTerm.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={loading:!1,availableTerms:[],selectedTerms:[]},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){var e=this;Object(O.isEmpty)(this.props.terms)||(this.setState({loading:!1}),this.initRequest=this.fetchTerms({include:this.props.terms.join(","),per_page:-1}),this.initRequest.then(function(){e.setState({loading:!1})},function(t){"abort"!==t.statusText&&e.setState({loading:!1})}))}},{key:"componentWillUnmount",value:function(){Object(O.invoke)(this.initRequest,["abort"]),Object(O.invoke)(this.searchRequest,["abort"])}},{key:"componentDidUpdate",value:function(e){e.terms!==this.props.terms&&this.updateSelectedTerms(this.props.terms)}},{key:"fetchTerms",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.props.taxonomy,r=Object(d.a)({},ws,t),o=vr()({path:Object(g.addQueryArgs)("/wp/v2/".concat(n.rest_base),r)});return o.then(function(t){e.setState(function(e){return{availableTerms:e.availableTerms.concat(t.filter(function(t){return!Object(O.find)(e.availableTerms,function(e){return e.id===t.id})}))}}),e.updateSelectedTerms(e.props.terms)}),o}},{key:"updateSelectedTerms",value:function(){var e=this,t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(function(t,n){var r=Object(O.find)(e.state.availableTerms,function(e){return e.id===n});return r&&t.push(r.name),t},[]);this.setState({selectedTerms:t})}},{key:"findOrCreateTerm",value:function(e){var t=this,n=this.props.taxonomy;return vr()({path:"/wp/v2/".concat(n.rest_base),method:"POST",data:{name:e}}).catch(function(r){return"term_exists"===r.code?(t.addRequest=vr()({path:Object(g.addQueryArgs)("/wp/v2/".concat(n.rest_base),Object(d.a)({},ws,{search:e}))}),t.addRequest.then(function(t){return Object(O.find)(t,function(t){return Ts(t.name,e)})})):Promise.reject(r)})}},{key:"onChange",value:function(e){var t=this,n=Object(O.uniqBy)(e,function(e){return e.toLowerCase()});this.setState({selectedTerms:n});var r=n.filter(function(e){return!Object(O.find)(t.state.availableTerms,function(t){return Ts(t.name,e)})}),o=function(e,t){return e.map(function(e){return Object(O.find)(t,function(t){return Ts(t.name,e)}).id})};if(0===r.length)return this.props.onUpdateTerms(o(n,this.state.availableTerms),this.props.taxonomy.rest_base);Promise.all(r.map(this.findOrCreateTerm)).then(function(e){var r=t.state.availableTerms.concat(e);return t.setState({availableTerms:r}),t.props.onUpdateTerms(o(n,r),t.props.taxonomy.rest_base)})}},{key:"searchTerms",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";Object(O.invoke)(this.searchRequest,["abort"]),this.searchRequest=this.fetchTerms({search:e})}},{key:"render",value:function(){var e=this.props,t=e.slug,n=e.taxonomy;if(!e.hasAssignAction)return null;var r=this.state,o=r.loading,i=r.availableTerms,c=r.selectedTerms,a=i.map(function(e){return e.name}),s=Object(O.get)(n,["labels","add_new_item"],"post_tag"===t?Object(_.__)("Add New Tag"):Object(_.__)("Add New Term")),l=Object(O.get)(n,["labels","singular_name"],"post_tag"===t?Object(_.__)("Tag"):Object(_.__)("Term")),u=Object(_.sprintf)(Object(_._x)("%s added","term"),l),d=Object(_.sprintf)(Object(_._x)("%s removed","term"),l),p=Object(_.sprintf)(Object(_._x)("Remove %s","term"),l);return Object(Br.createElement)(Vr.FormTokenField,{value:c,displayTransform:O.unescape,suggestions:a,onChange:this.onChange,onInputChange:this.searchTerms,maxSuggestions:20,disabled:o,label:s,messages:{added:u,removed:d,remove:p}})}}]),t}(Br.Component),Is=Object(Ar.compose)(Object(l.withSelect)(function(e,t){var n=t.slug,r=e("core/editor").getCurrentPost,o=(0,e("core").getTaxonomy)(n);return{hasCreateAction:!!o&&Object(O.get)(r(),["_links","wp:action-create-"+o.rest_base],!1),hasAssignAction:!!o&&Object(O.get)(r(),["_links","wp:action-assign-"+o.rest_base],!1),terms:o?e("core/editor").getEditedPostAttribute(o.rest_base):[],taxonomy:o}}),Object(l.withDispatch)(function(e){return{onUpdateTerms:function(t,n){e("core/editor").editPost(Object(f.a)({},n,t))}}}),Object(Vr.withFilters)("editor.PostTaxonomyType"))(Ps),Bs=function(){var e=[Object(_.__)("Suggestion:"),Object(Br.createElement)("span",{className:"editor-post-publish-panel__link",key:"label"},Object(_.__)("Add tags"))];return Object(Br.createElement)(Vr.PanelBody,{initialOpen:!1,title:e},Object(Br.createElement)("p",null,Object(_.__)("Tags help users and search engines navigate your site and find your content. Add a few keywords to describe your post.")),Object(Br.createElement)(Is,{slug:"post_tag"}))},xs=function(e){function t(e){var n;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).call(this,e))).state={hadTagsWhenOpeningThePanel:e.hasTags},n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"render",value:function(){return this.state.hadTagsWhenOpeningThePanel?null:Object(Br.createElement)(Bs,null)}}]),t}(Br.Component),Ls=Object(Ar.compose)(Object(l.withSelect)(function(e){var t=e("core/editor").getCurrentPostType(),n=e("core").getTaxonomy("post_tag"),r=n&&e("core/editor").getEditedPostAttribute(n.rest_base);return{areTagsFetched:void 0!==n,isPostTypeSupported:n&&Object(O.some)(n.types,function(e){return e===t}),hasTags:r&&r.length}}),Object(Ar.ifCondition)(function(e){var t=e.areTagsFetched;return e.isPostTypeSupported&&t}))(xs),As=function(e){var t=e.suggestedPostFormat,n=e.suggestionText,r=e.onUpdatePostFormat;return Object(Br.createElement)(Vr.Button,{isLink:!0,onClick:function(){return r(t)}},n)},Ns=function(e,t){var n=is.filter(function(t){return Object(O.includes)(e,t.id)});return Object(O.find)(n,function(e){return e.id===t})},Rs=Object(Ar.compose)(Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=t.getSuggestedPostFormat,o=Object(O.get)(e("core").getThemeSupports(),["formats"],[]);return{currentPostFormat:n("format"),suggestion:Ns(o,r())}}),Object(l.withDispatch)(function(e){return{onUpdatePostFormat:function(t){e("core/editor").editPost({format:t})}}}),Object(Ar.ifCondition)(function(e){var t=e.suggestion,n=e.currentPostFormat;return t&&t.id!==n}))(function(e){var t=e.suggestion,n=e.onUpdatePostFormat,r=[Object(_.__)("Suggestion:"),Object(Br.createElement)("span",{className:"editor-post-publish-panel__link",key:"label"},Object(_.__)("Use a post format"))];return Object(Br.createElement)(Vr.PanelBody,{initialOpen:!1,title:r},Object(Br.createElement)("p",null,Object(_.__)("Your theme uses post formats to highlight different kinds of content, like images or videos. Apply a post format to see this special styling.")),Object(Br.createElement)("p",null,Object(Br.createElement)(As,{onUpdatePostFormat:n,suggestedPostFormat:t.id,suggestionText:Object(_.sprintf)(Object(_.__)('Apply the "%1$s" format.'),t.caption)})))});var Ds=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getCurrentPost,r=t.isEditedPostBeingScheduled;return{hasPublishAction:Object(O.get)(n(),["_links","wp:action-publish"],!1),isBeingScheduled:r()}})(function(e){var t,n,r=e.hasPublishAction,o=e.isBeingScheduled,i=e.children;return r?o?(t=Object(_.__)("Are you ready to schedule?"),n=Object(_.__)("Your work will be published at the specified date and time.")):(t=Object(_.__)("Are you ready to publish?"),n=Object(_.__)("Double-check your settings before publishing.")):(t=Object(_.__)("Are you ready to submit for review?"),n=Object(_.__)("When you’re ready, submit your work for review, and an Editor will be able to approve it for you.")),Object(Br.createElement)("div",{className:"editor-post-publish-panel__prepublish"},Object(Br.createElement)("div",null,Object(Br.createElement)("strong",null,t)),Object(Br.createElement)("p",null,n),r&&Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(Vr.PanelBody,{initialOpen:!1,title:[Object(_.__)("Visibility:"),Object(Br.createElement)("span",{className:"editor-post-publish-panel__link",key:"label"},Object(Br.createElement)(Es,null))]},Object(Br.createElement)(_s,null)),Object(Br.createElement)(Vr.PanelBody,{initialOpen:!1,title:[Object(_.__)("Publish:"),Object(Br.createElement)("span",{className:"editor-post-publish-panel__link",key:"label"},Object(Br.createElement)(Cs,null))]},Object(Br.createElement)(Ss,null)),Object(Br.createElement)(Rs,null),Object(Br.createElement)(Ls,null),i))}),Fs=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={showCopyConfirmation:!1},e.onCopy=e.onCopy.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onSelectInput=e.onSelectInput.bind(Object(Hr.a)(Object(Hr.a)(e))),e.postLink=Object(Br.createRef)(),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){this.props.focusOnMount&&this.postLink.current.focus()}},{key:"componentWillUnmount",value:function(){clearTimeout(this.dismissCopyConfirmation)}},{key:"onCopy",value:function(){var e=this;this.setState({showCopyConfirmation:!0}),clearTimeout(this.dismissCopyConfirmation),this.dismissCopyConfirmation=setTimeout(function(){e.setState({showCopyConfirmation:!1})},4e3)}},{key:"onSelectInput",value:function(e){e.target.select()}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.isScheduled,r=e.post,o=e.postType,i=Object(O.get)(o,["labels","singular_name"]),c=Object(O.get)(o,["labels","view_item"]),a=n?Object(Br.createElement)(Br.Fragment,null,Object(_.__)("is now scheduled. It will go live on")," ",Object(Br.createElement)(Cs,null),"."):Object(_.__)("is now live.");return Object(Br.createElement)("div",{className:"post-publish-panel__postpublish"},Object(Br.createElement)(Vr.PanelBody,{className:"post-publish-panel__postpublish-header"},Object(Br.createElement)("a",{ref:this.postLink,href:r.link},r.title||Object(_.__)("(no title)"))," ",a),Object(Br.createElement)(Vr.PanelBody,null,Object(Br.createElement)("p",{className:"post-publish-panel__postpublish-subheader"},Object(Br.createElement)("strong",null,Object(_.__)("What’s next?"))),Object(Br.createElement)(Vr.TextControl,{className:"post-publish-panel__postpublish-post-address",readOnly:!0,label:Object(_.sprintf)(Object(_.__)("%s address"),i),value:r.link,onFocus:this.onSelectInput}),Object(Br.createElement)("div",{className:"post-publish-panel__postpublish-buttons"},!n&&Object(Br.createElement)(Vr.Button,{isDefault:!0,href:r.link},c),Object(Br.createElement)(Vr.ClipboardButton,{isDefault:!0,text:r.link,onCopy:this.onCopy},this.state.showCopyConfirmation?Object(_.__)("Copied!"):Object(_.__)("Copy Link")))),t)}}]),t}(Br.Component),Ms=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=t.getCurrentPost,o=t.isCurrentPostScheduled,i=e("core").getPostType;return{post:r(),postType:i(n("type")),isScheduled:o()}})(Fs),Us=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onSubmit=e.onSubmit.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidUpdate",value:function(e){e.isPublished&&!this.props.isSaving&&this.props.isDirty&&this.props.onClose()}},{key:"onSubmit",value:function(){var e=this.props,t=e.onClose,n=e.hasPublishAction,r=e.isPostTypeViewable;n&&r||t()}},{key:"render",value:function(){var e=this.props,t=e.forceIsDirty,n=e.forceIsSaving,r=e.isBeingScheduled,o=e.isPublished,i=e.isPublishSidebarEnabled,c=e.isScheduled,a=e.isSaving,s=e.onClose,l=e.onTogglePublishSidebar,u=e.PostPublishExtension,d=e.PrePublishExtension,b=Object(p.a)(e,["forceIsDirty","forceIsSaving","isBeingScheduled","isPublished","isPublishSidebarEnabled","isScheduled","isSaving","onClose","onTogglePublishSidebar","PostPublishExtension","PrePublishExtension"]),f=Object(O.omit)(b,["hasPublishAction","isDirty","isPostTypeViewable"]),h=o||c&&r,m=!h&&!a,v=h&&!a;return Object(Br.createElement)("div",Object(Ir.a)({className:"editor-post-publish-panel"},f),Object(Br.createElement)("div",{className:"editor-post-publish-panel__header"},v?Object(Br.createElement)("div",{className:"editor-post-publish-panel__header-published"},c?Object(_.__)("Scheduled"):Object(_.__)("Published")):Object(Br.createElement)("div",{className:"editor-post-publish-panel__header-publish-button"},Object(Br.createElement)(js,{focusOnMount:!0,onSubmit:this.onSubmit,forceIsDirty:t,forceIsSaving:n}),Object(Br.createElement)("span",{className:"editor-post-publish-panel__spacer"})),Object(Br.createElement)(Vr.IconButton,{"aria-expanded":!0,onClick:s,icon:"no-alt",label:Object(_.__)("Close panel")})),Object(Br.createElement)("div",{className:"editor-post-publish-panel__content"},m&&Object(Br.createElement)(Ds,null,d&&Object(Br.createElement)(d,null)),v&&Object(Br.createElement)(Ms,{focusOnMount:!0},u&&Object(Br.createElement)(u,null)),a&&Object(Br.createElement)(Vr.Spinner,null)),Object(Br.createElement)("div",{className:"editor-post-publish-panel__footer"},Object(Br.createElement)(Vr.CheckboxControl,{label:Object(_.__)("Always show pre-publish checks."),checked:i,onChange:l})))}}]),t}(Br.Component),Hs=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core").getPostType,n=e("core/editor"),r=n.getCurrentPost,o=n.getEditedPostAttribute,i=n.isCurrentPostPublished,c=n.isCurrentPostScheduled,a=n.isEditedPostBeingScheduled,s=n.isEditedPostDirty,l=n.isSavingPost,u=e("core/editor").isPublishSidebarEnabled,d=t(o("type"));return{hasPublishAction:Object(O.get)(r(),["_links","wp:action-publish"],!1),isPostTypeViewable:Object(O.get)(d,["viewable"],!1),isBeingScheduled:a(),isDirty:s(),isPublished:i(),isPublishSidebarEnabled:u(),isSaving:l(),isScheduled:c()}}),Object(l.withDispatch)(function(e,t){var n=t.isPublishSidebarEnabled,r=e("core/editor"),o=r.disablePublishSidebar,i=r.enablePublishSidebar;return{onTogglePublishSidebar:function(){n?o():i()}}}),Vr.withFocusReturn,Vr.withConstrainedTabbing])(Us);var Vs=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.isSavingPost,r=t.isCurrentPostPublished,o=t.isCurrentPostScheduled;return{isSaving:n(),isPublished:r(),isScheduled:o()}}),Object(l.withDispatch)(function(e){var t=e("core/editor"),n=t.editPost,r=t.savePost;return{onClick:function(){n({status:"draft"}),r()}}})])(function(e){var t=e.isSaving,n=e.isPublished,r=e.isScheduled,o=e.onClick;return n||r?Object(Br.createElement)(Vr.Button,{className:"editor-post-switch-to-draft",onClick:function(){var e;n?e=Object(_.__)("Are you sure you want to unpublish this post?"):r&&(e=Object(_.__)("Are you sure you want to unschedule this post?")),window.confirm(e)&&o()},disabled:t,isTertiary:!0},Object(_.__)("Switch to Draft")):null}),Ks=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={forceSavedMessage:!1},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidUpdate",value:function(e){var t=this;e.isSaving&&!this.props.isSaving&&(this.setState({forceSavedMessage:!0}),this.props.setTimeout(function(){t.setState({forceSavedMessage:!1})},1e3))}},{key:"render",value:function(){var e=this.props,t=e.post,n=e.isNew,r=e.isScheduled,o=e.isPublished,i=e.isDirty,c=e.isSaving,a=e.isSaveable,s=e.onSave,l=e.isAutosaving,u=e.isPending,d=e.isLargeViewport,p=this.state.forceSavedMessage;if(c){var b=Lr()("editor-post-saved-state","is-saving",{"is-autosaving":l});return Object(Br.createElement)("span",{className:b},Object(Br.createElement)(Vr.Dashicon,{icon:"cloud"}),l?Object(_.__)("Autosaving"):Object(_.__)("Saving"))}if(o||r)return Object(Br.createElement)(Vs,null);if(!a)return null;if(p||!n&&!i)return Object(Br.createElement)("span",{className:"editor-post-saved-state is-saved"},Object(Br.createElement)(Vr.Dashicon,{icon:"saved"}),Object(_.__)("Saved"));if(!Object(O.get)(t,["_links","wp:action-publish"],!1)&&u)return null;var f=u?Object(_.__)("Save as Pending"):Object(_.__)("Save Draft");return d?Object(Br.createElement)(Vr.Button,{className:"editor-post-save-draft",onClick:s,shortcut:yo.displayShortcut.primary("s"),isTertiary:!0},f):Object(Br.createElement)(Vr.IconButton,{className:"editor-post-save-draft",label:f,onClick:s,shortcut:yo.displayShortcut.primary("s"),icon:"cloud-upload"})}}]),t}(Br.Component),zs=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.forceIsDirty,r=t.forceIsSaving,o=e("core/editor"),i=o.isEditedPostNew,c=o.isCurrentPostPublished,a=o.isCurrentPostScheduled,s=o.isEditedPostDirty,l=o.isSavingPost,u=o.isEditedPostSaveable,d=o.getCurrentPost,p=o.isAutosavingPost,b=o.getEditedPostAttribute;return{post:d(),isNew:i(),isPublished:c(),isScheduled:a(),isDirty:n||s(),isSaving:r||l(),isSaveable:u(),isAutosaving:p(),isPending:"pending"===b("status")}}),Object(l.withDispatch)(function(e){return{onSave:e("core/editor").savePost}}),Ar.withSafeTimeout,Object(s.withViewportMatch)({isLargeViewport:"medium"})])(Ks);var Ws=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getCurrentPost,r=t.getCurrentPostType;return{hasPublishAction:Object(O.get)(n(),["_links","wp:action-publish"],!1),postType:r()}})])(function(e){var t=e.hasPublishAction,n=e.children;return t?n:null});var qs=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor").getCurrentPost();return{hasStickyAction:Object(O.get)(t,["_links","wp:action-sticky"],!1),postType:e("core/editor").getCurrentPostType()}})])(function(e){var t=e.hasStickyAction,n=e.postType,r=e.children;return"post"===n&&t?r:null});var Gs=Object(Ar.compose)([Object(l.withSelect)(function(e){return{postSticky:e("core/editor").getEditedPostAttribute("sticky")}}),Object(l.withDispatch)(function(e){return{onUpdateSticky:function(t){e("core/editor").editPost({sticky:t})}}})])(function(e){var t=e.onUpdateSticky,n=e.postSticky,r=void 0!==n&&n;return Object(Br.createElement)(qs,null,Object(Br.createElement)(Vr.CheckboxControl,{label:Object(_.__)("Stick to the Front Page"),checked:r,onChange:function(){return t(!r)}}))}),$s={per_page:-1,orderby:"name",order:"asc",_fields:"id,name,parent"},Ys=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).findTerm=e.findTerm.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onChange=e.onChange.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onChangeFormName=e.onChangeFormName.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onChangeFormParent=e.onChangeFormParent.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onAddTerm=e.onAddTerm.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onToggleForm=e.onToggleForm.bind(Object(Hr.a)(Object(Hr.a)(e))),e.setFilterValue=e.setFilterValue.bind(Object(Hr.a)(Object(Hr.a)(e))),e.sortBySelected=e.sortBySelected.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={loading:!0,availableTermsTree:[],availableTerms:[],adding:!1,formName:"",formParent:"",showForm:!1,filterValue:"",filteredTermsTree:[]},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onChange",value:function(e){var t=this.props,n=t.onUpdateTerms,r=t.terms,o=void 0===r?[]:r,i=t.taxonomy,c=parseInt(e.target.value,10);n(-1!==o.indexOf(c)?Object(O.without)(o,c):Object(b.a)(o).concat([c]),i.rest_base)}},{key:"onChangeFormName",value:function(e){var t=""===e.target.value.trim()?"":e.target.value;this.setState({formName:t})}},{key:"onChangeFormParent",value:function(e){this.setState({formParent:e})}},{key:"onToggleForm",value:function(){this.setState(function(e){return{showForm:!e.showForm}})}},{key:"findTerm",value:function(e,t,n){return Object(O.find)(e,function(e){return(!e.parent&&!t||parseInt(e.parent)===parseInt(t))&&e.name.toLowerCase()===n.toLowerCase()})}},{key:"onAddTerm",value:function(e){var t=this;e.preventDefault();var n=this.props,r=n.onUpdateTerms,o=n.taxonomy,i=n.terms,c=n.slug,a=this.state,s=a.formName,l=a.formParent,u=a.adding,p=a.availableTerms;if(""!==s&&!u){var f=this.findTerm(p,l,s);if(f)return Object(O.some)(i,function(e){return e===f.id})||r(Object(b.a)(i).concat([f.id]),o.rest_base),void this.setState({formName:"",formParent:""});this.setState({adding:!0}),this.addRequest=vr()({path:"/wp/v2/".concat(o.rest_base),method:"POST",data:{name:s,parent:l||void 0}}),this.addRequest.catch(function(e){return"term_exists"===e.code?(t.addRequest=vr()({path:Object(g.addQueryArgs)("/wp/v2/".concat(o.rest_base),Object(d.a)({},$s,{parent:l||0,search:s}))}),t.addRequest.then(function(e){return t.findTerm(e,l,s)})):Promise.reject(e)}).then(function(e){var n=!!Object(O.find)(t.state.availableTerms,function(t){return t.id===e.id})?t.state.availableTerms:[e].concat(Object(b.a)(t.state.availableTerms)),a=Object(_.sprintf)(Object(_._x)("%s added","term"),Object(O.get)(t.props.taxonomy,["labels","singular_name"],"category"===c?Object(_.__)("Category"):Object(_.__)("Term")));t.props.speak(a,"assertive"),t.addRequest=null,t.setState({adding:!1,formName:"",formParent:"",availableTerms:n,availableTermsTree:t.sortBySelected(Fa(n))}),r(Object(b.a)(i).concat([e.id]),o.rest_base)},function(e){"abort"!==e.statusText&&(t.addRequest=null,t.setState({adding:!1}))})}}},{key:"componentDidMount",value:function(){this.fetchTerms()}},{key:"componentWillUnmount",value:function(){Object(O.invoke)(this.fetchRequest,["abort"]),Object(O.invoke)(this.addRequest,["abort"])}},{key:"componentDidUpdate",value:function(e){this.props.taxonomy!==e.taxonomy&&this.fetchTerms()}},{key:"fetchTerms",value:function(){var e=this,t=this.props.taxonomy;t&&(this.fetchRequest=vr()({path:Object(g.addQueryArgs)("/wp/v2/".concat(t.rest_base),$s)}),this.fetchRequest.then(function(t){var n=e.sortBySelected(Fa(t));e.fetchRequest=null,e.setState({loading:!1,availableTermsTree:n,availableTerms:t})},function(t){"abort"!==t.statusText&&(e.fetchRequest=null,e.setState({loading:!1}))}))}},{key:"sortBySelected",value:function(e){var t=this.props.terms,n=function e(n){return-1!==t.indexOf(n.id)||void 0!==n.children&&!!(n.children.map(e).filter(function(e){return e}).length>0)};return e.sort(function(e,t){var r=n(e),o=n(t);return r===o?0:r&&!o?-1:!r&&o?1:0}),e}},{key:"setFilterValue",value:function(e){var t=this.state.availableTermsTree,n=e.target.value,r=t.map(this.getFilterMatcher(n)).filter(function(e){return e});this.setState({filterValue:n,filteredTermsTree:r});var o=function e(t){for(var n=0,r=0;r<t.length;r++)n++,void 0!==t[r].children&&(n+=e(t[r].children));return n}(r),i=Object(_.sprintf)(Object(_._n)("%d result found.","%d results found.",o),o);this.props.debouncedSpeak(i,"assertive")}},{key:"getFilterMatcher",value:function(e){return function t(n){if(""===e)return n;var r=Object(d.a)({},n);return r.children.length>0&&(r.children=r.children.map(t).filter(function(e){return e})),(-1!==r.name.toLowerCase().indexOf(e)||r.children.length>0)&&r}}},{key:"renderTerms",value:function(e){var t=this,n=this.props.terms,r=void 0===n?[]:n;return e.map(function(e){var n="editor-post-taxonomies-hierarchical-term-".concat(e.id);return Object(Br.createElement)("div",{key:e.id,className:"editor-post-taxonomies__hierarchical-terms-choice"},Object(Br.createElement)("input",{id:n,className:"editor-post-taxonomies__hierarchical-terms-input",type:"checkbox",checked:-1!==r.indexOf(e.id),value:e.id,onChange:t.onChange}),Object(Br.createElement)("label",{htmlFor:n},Object(O.unescape)(e.name)),!!e.children.length&&Object(Br.createElement)("div",{className:"editor-post-taxonomies__hierarchical-terms-subchoices"},t.renderTerms(e.children)))})}},{key:"render",value:function(){var e=this.props,t=e.slug,n=e.taxonomy,r=e.instanceId,o=e.hasCreateAction;if(!e.hasAssignAction)return null;var i=this.state,c=i.availableTermsTree,a=i.availableTerms,s=i.filteredTermsTree,l=i.formName,u=i.formParent,d=i.loading,p=i.showForm,b=i.filterValue,f=function(e,r,o){return Object(O.get)(n,["labels",e],"category"===t?r:o)},h=f("add_new_item",Object(_.__)("Add new category"),Object(_.__)("Add new term")),m=f("new_item_name",Object(_.__)("Add new category"),Object(_.__)("Add new term")),v=f("parent_item",Object(_.__)("Parent Category"),Object(_.__)("Parent Term")),g="— ".concat(v," —"),j=h,y="editor-post-taxonomies__hierarchical-terms-input-".concat(r),k="editor-post-taxonomies__hierarchical-terms-filter-".concat(r),E=Object(_.sprintf)(Object(_._x)("Search %s","term"),Object(O.get)(this.props.taxonomy,["name"],"category"===t?Object(_.__)("Categories"):Object(_.__)("Terms"))),S=Object(_.sprintf)(Object(_._x)("Available %s","term"),Object(O.get)(this.props.taxonomy,["name"],"category"===t?Object(_.__)("Categories"):Object(_.__)("Terms"))),C=a.length>=8;return[C&&Object(Br.createElement)("label",{key:"filter-label",htmlFor:k},E),C&&Object(Br.createElement)("input",{type:"search",id:k,value:b,onChange:this.setFilterValue,className:"editor-post-taxonomies__hierarchical-terms-filter",key:"term-filter-input"}),Object(Br.createElement)("div",{className:"editor-post-taxonomies__hierarchical-terms-list",key:"term-list",tabIndex:"0",role:"group","aria-label":S},this.renderTerms(""!==b?s:c)),!d&&o&&Object(Br.createElement)(Vr.Button,{key:"term-add-button",onClick:this.onToggleForm,className:"editor-post-taxonomies__hierarchical-terms-add","aria-expanded":p,isLink:!0},h),p&&Object(Br.createElement)("form",{onSubmit:this.onAddTerm,key:"hierarchical-terms-form"},Object(Br.createElement)("label",{htmlFor:y,className:"editor-post-taxonomies__hierarchical-terms-label"},m),Object(Br.createElement)("input",{type:"text",id:y,className:"editor-post-taxonomies__hierarchical-terms-input",value:l,onChange:this.onChangeFormName,required:!0}),!!a.length&&Object(Br.createElement)(Vr.TreeSelect,{label:v,noOptionLabel:g,onChange:this.onChangeFormParent,selectedId:u,tree:c}),Object(Br.createElement)(Vr.Button,{isDefault:!0,type:"submit",className:"editor-post-taxonomies__hierarchical-terms-submit"},j))]}}]),t}(Br.Component),Qs=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.slug,r=e("core/editor").getCurrentPost,o=(0,e("core").getTaxonomy)(n);return{hasCreateAction:!!o&&Object(O.get)(r(),["_links","wp:action-create-"+o.rest_base],!1),hasAssignAction:!!o&&Object(O.get)(r(),["_links","wp:action-assign-"+o.rest_base],!1),terms:o?e("core/editor").getEditedPostAttribute(o.rest_base):[],taxonomy:o}}),Object(l.withDispatch)(function(e){return{onUpdateTerms:function(t,n){e("core/editor").editPost(Object(f.a)({},n,t))}}}),Vr.withSpokenMessages,Ar.withInstanceId,Object(Vr.withFilters)("editor.PostTaxonomyType")])(Ys);var Xs=Object(Ar.compose)([Object(l.withSelect)(function(e){return{postType:e("core/editor").getCurrentPostType(),taxonomies:e("core").getTaxonomies({per_page:-1})}})])(function(e){var t=e.postType,n=e.taxonomies,r=e.taxonomyWrapper,o=void 0===r?O.identity:r,i=Object(O.filter)(n,function(e){return Object(O.includes)(e.types,t)});return Object(O.filter)(i,function(e){return e.visibility.show_ui}).map(function(e){var t=e.hierarchical?Qs:Is;return Object(Br.createElement)(Br.Fragment,{key:"taxonomy-".concat(e.slug)},o(Object(Br.createElement)(t,{slug:e.slug}),e))})});var Zs=Object(Ar.compose)([Object(l.withSelect)(function(e){return{postType:e("core/editor").getCurrentPostType(),taxonomies:e("core").getTaxonomies({per_page:-1})}})])(function(e){var t=e.postType,n=e.taxonomies,r=e.children;return Object(O.some)(n,function(e){return Object(O.includes)(e.types,t)})?r:null}),Js=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).edit=e.edit.bind(Object(Hr.a)(Object(Hr.a)(e))),e.stopEditing=e.stopEditing.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"edit",value:function(e){var t=e.target.value;this.props.onChange(t),this.setState({value:t,isDirty:!0})}},{key:"stopEditing",value:function(){this.state.isDirty&&(this.props.onPersist(this.state.value),this.setState({isDirty:!1}))}},{key:"render",value:function(){var e=this.state.value,t=this.props.instanceId;return Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)("label",{htmlFor:"post-content-".concat(t),className:"screen-reader-text"},Object(_.__)("Type text or HTML")),Object(Br.createElement)(di.a,{autoComplete:"off",dir:"auto",value:e,onChange:this.edit,onBlur:this.stopEditing,className:"editor-post-text-editor",id:"post-content-".concat(t),placeholder:Object(_.__)("Start writing with text or HTML")}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){return t.isDirty?null:{value:e.value,isDirty:!1}}}]),t}(Br.Component),el=Object(Ar.compose)([Object(l.withSelect)(function(e){return{value:(0,e("core/editor").getEditedPostContent)()}}),Object(l.withDispatch)(function(e){var t=e("core/editor"),n=t.editPost,r=t.resetBlocks;return{onChange:function(e){n({content:e})},onPersist:function(e){r(Object(i.parse)(e))}}}),Ar.withInstanceId])(Js),tl=function(e){function t(e){var n,r=e.permalinkParts,o=e.slug;return Object(Rr.a)(this,t),(n=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={editedPostName:o||r.postName},n.onSavePermalink=n.onSavePermalink.bind(Object(Hr.a)(Object(Hr.a)(n))),n}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onSavePermalink",value:function(e){var t=ca(this.state.editedPostName);e.preventDefault(),this.props.onSave(),t!==this.props.postName&&(this.props.editPost({slug:t}),this.setState({editedPostName:t}))}},{key:"render",value:function(){var e=this,t=this.props.permalinkParts,n=t.prefix,r=t.suffix,o=this.state.editedPostName;return Object(Br.createElement)("form",{className:"editor-post-permalink-editor",onSubmit:this.onSavePermalink},Object(Br.createElement)("span",{className:"editor-post-permalink__editor-container"},Object(Br.createElement)("span",{className:"editor-post-permalink-editor__prefix"},n),Object(Br.createElement)("input",{className:"editor-post-permalink-editor__edit","aria-label":Object(_.__)("Edit post permalink"),value:o,onChange:function(t){return e.setState({editedPostName:t.target.value})},type:"text",autoFocus:!0}),Object(Br.createElement)("span",{className:"editor-post-permalink-editor__suffix"},r),"‎"),Object(Br.createElement)(Vr.Button,{className:"editor-post-permalink-editor__save",isLarge:!0,onClick:this.onSavePermalink},Object(_.__)("Save")))}}]),t}(Br.Component),nl=Object(Ar.compose)([Object(l.withSelect)(function(e){return{permalinkParts:(0,e("core/editor").getPermalinkParts)()}}),Object(l.withDispatch)(function(e){return{editPost:e("core/editor").editPost}})])(tl),rl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).addVisibilityCheck=e.addVisibilityCheck.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onVisibilityChange=e.onVisibilityChange.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={isCopied:!1,isEditingPermalink:!1},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"addVisibilityCheck",value:function(){window.addEventListener("visibilitychange",this.onVisibilityChange)}},{key:"onVisibilityChange",value:function(){var e=this.props,t=e.isEditable,n=e.refreshPost;t||"visible"!==document.visibilityState||n()}},{key:"componentDidUpdate",value:function(e,t){t.isEditingPermalink&&!this.state.isEditingPermalink&&this.linkElement.focus()}},{key:"componentWillUnmount",value:function(){window.removeEventListener("visibilitychange",this.addVisibilityCheck)}},{key:"render",value:function(){var e=this,t=this.props,n=t.isEditable,r=t.isNew,o=t.isPublished,i=t.isViewable,c=t.permalinkParts,a=t.postLink,s=t.postSlug,l=t.postID,u=t.postTitle;if(r||!i||!c||!a)return null;var d=this.state,p=d.isCopied,b=d.isEditingPermalink,f=p?Object(_.__)("Permalink copied"):Object(_.__)("Copy the permalink"),h=c.prefix,m=c.suffix,v=s||ca(u)||l,O=n?h+v+m:h;return Object(Br.createElement)("div",{className:"editor-post-permalink"},Object(Br.createElement)(Vr.ClipboardButton,{className:Lr()("editor-post-permalink__copy",{"is-copied":p}),text:O,label:f,onCopy:function(){return e.setState({isCopied:!0})},"aria-disabled":p,icon:"admin-links"}),Object(Br.createElement)("span",{className:"editor-post-permalink__label"},Object(_.__)("Permalink:")),!b&&Object(Br.createElement)(Vr.ExternalLink,{className:"editor-post-permalink__link",href:o?O:a,target:"_blank",ref:function(t){return e.linkElement=t}},Object(g.safeDecodeURI)(O),"‎"),b&&Object(Br.createElement)(nl,{slug:v,onSave:function(){return e.setState({isEditingPermalink:!1})}}),n&&!b&&Object(Br.createElement)(Vr.Button,{className:"editor-post-permalink__edit",isLarge:!0,onClick:function(){return e.setState({isEditingPermalink:!0})}},Object(_.__)("Edit")),!n&&Object(Br.createElement)(Vr.Button,{className:"editor-post-permalink__change",isLarge:!0,href:ia("options-permalink.php"),onClick:this.addVisibilityCheck,target:"_blank"},Object(_.__)("Change Permalinks")))}}]),t}(Br.Component),ol=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.isEditedPostNew,r=t.isPermalinkEditable,o=t.getCurrentPost,i=t.getPermalinkParts,c=t.getEditedPostAttribute,a=t.isCurrentPostPublished,s=e("core").getPostType,l=o(),u=l.id,d=l.link,p=s(c("type"));return{isNew:n(),postLink:d,permalinkParts:i(),postSlug:c("slug"),isEditable:r(),isPublished:a(),postTitle:c("title"),postID:u,isViewable:Object(O.get)(p,["viewable"],!1)}}),Object(l.withDispatch)(function(e){return{refreshPost:e("core/editor").refreshPost}})])(rl),il=/[\r\n]+/g,cl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onChange=e.onChange.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onSelect=e.onSelect.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onUnselect=e.onUnselect.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onKeyDown=e.onKeyDown.bind(Object(Hr.a)(Object(Hr.a)(e))),e.redirectHistory=e.redirectHistory.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={isSelected:!1},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"handleFocusOutside",value:function(){this.onUnselect()}},{key:"onSelect",value:function(){this.setState({isSelected:!0}),this.props.clearSelectedBlock()}},{key:"onUnselect",value:function(){this.setState({isSelected:!1})}},{key:"onChange",value:function(e){var t=e.target.value.replace(il," ");this.props.onUpdate(t)}},{key:"onKeyDown",value:function(e){e.keyCode===yo.ENTER&&(e.preventDefault(),this.props.onEnterPress())}},{key:"redirectHistory",value:function(e){e.shiftKey?this.props.onRedo():this.props.onUndo(),e.preventDefault()}},{key:"render",value:function(){var e=this.props,t=e.hasFixedToolbar,n=e.isCleanNewPost,r=e.isFocusMode,o=e.isPostTypeViewable,i=e.instanceId,c=e.placeholder,a=e.title,s=this.state.isSelected,l=Lr()("wp-block editor-post-title__block",{"is-selected":s,"is-focus-mode":r,"has-fixed-toolbar":t}),u=Object(Zi.decodeEntities)(c);return Object(Br.createElement)(Na,{supportKeys:"title"},Object(Br.createElement)("div",{className:"editor-post-title"},Object(Br.createElement)("div",{className:l},Object(Br.createElement)(Vr.KeyboardShortcuts,{shortcuts:{"mod+z":this.redirectHistory,"mod+shift+z":this.redirectHistory}},Object(Br.createElement)("label",{htmlFor:"post-title-".concat(i),className:"screen-reader-text"},u||Object(_.__)("Add title")),Object(Br.createElement)(di.a,{id:"post-title-".concat(i),className:"editor-post-title__input",value:a,onChange:this.onChange,placeholder:u||Object(_.__)("Add title"),onFocus:this.onSelect,onKeyDown:this.onKeyDown,onKeyPress:this.onUnselect,autoFocus:n})),s&&o&&Object(Br.createElement)(ol,null))))}}]),t}(Br.Component),al=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getEditedPostAttribute,r=t.getEditorSettings,o=t.isCleanNewPost,i=(0,e("core").getPostType)(n("type")),c=r(),a=c.titlePlaceholder,s=c.focusMode,l=c.hasFixedToolbar;return{isCleanNewPost:o(),title:n("title"),isPostTypeViewable:Object(O.get)(i,["viewable"],!1),placeholder:a,isFocusMode:s,hasFixedToolbar:l}}),sl=Object(l.withDispatch)(function(e){var t=e("core/editor"),n=t.insertDefaultBlock,r=t.editPost,o=t.clearSelectedBlock;return{onEnterPress:function(){n(void 0,void 0,0)},onUpdate:function(e){r({title:e})},onUndo:t.undo,onRedo:t.redo,clearSelectedBlock:o}}),ll=Object(Ar.compose)(al,sl,Ar.withInstanceId,Vr.withFocusOutside)(cl);var ul=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.isEditedPostNew,r=t.getCurrentPostId,o=t.getCurrentPostType;return{isNew:n(),postId:r(),postType:o()}}),Object(l.withDispatch)(function(e){return{trashPost:e("core/editor").trashPost}})])(function(e){var t=e.isNew,n=e.postId,r=e.postType,o=Object(p.a)(e,["isNew","postId","postType"]);return t||!n?null:Object(Br.createElement)(Vr.Button,{className:"editor-post-trash button-link-delete",onClick:function(){return o.trashPost(n,r)},isDefault:!0,isLarge:!0},Object(_.__)("Move to trash"))});var dl=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.isEditedPostNew,r=t.getCurrentPostId;return{isNew:n(),postId:r()}})(function(e){var t=e.isNew,n=e.postId,r=e.children;return t||!n?null:r});var pl=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getCurrentPost,r=t.getCurrentPostType;return{hasPublishAction:Object(O.get)(n(),["_links","wp:action-publish"],!1),postType:r()}})])(function(e){var t=e.hasPublishAction;return(0,e.render)({canEdit:t})}),bl=n(97);var fl=Object(l.withSelect)(function(e){return{content:e("core/editor").getEditedPostAttribute("content")}})(function(e){var t=e.content,n=Object(_._x)("words","Word count type. Do not translate!");return Object(Br.createElement)("span",{className:"word-count"},Object(bl.count)(t,n))});var hl=Object(l.withSelect)(function(e){var t=e("core/editor").getGlobalBlockCount;return{headingCount:t("core/heading"),paragraphCount:t("core/paragraph"),numberOfBlocks:t()}})(function(e){var t=e.headingCount,n=e.paragraphCount,r=e.numberOfBlocks;return Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)("div",{className:"table-of-contents__counts",role:"note","aria-label":Object(_.__)("Document Statistics"),tabIndex:"0"},Object(Br.createElement)("div",{className:"table-of-contents__count"},Object(_.__)("Words"),Object(Br.createElement)(fl,null)),Object(Br.createElement)("div",{className:"table-of-contents__count"},Object(_.__)("Headings"),Object(Br.createElement)("span",{className:"table-of-contents__number"},t)),Object(Br.createElement)("div",{className:"table-of-contents__count"},Object(_.__)("Paragraphs"),Object(Br.createElement)("span",{className:"table-of-contents__number"},n)),Object(Br.createElement)("div",{className:"table-of-contents__count"},Object(_.__)("Blocks"),Object(Br.createElement)("span",{className:"table-of-contents__number"},r))),t>0&&Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)("hr",null),Object(Br.createElement)("span",{className:"table-of-contents__title"},Object(_.__)("Document Outline")),Object(Br.createElement)(_a,null)))});var ml=Object(l.withSelect)(function(e){return{hasBlocks:!!e("core/editor").getBlockCount()}})(function(e){var t=e.hasBlocks;return Object(Br.createElement)(Vr.Dropdown,{position:"bottom",className:"table-of-contents",contentClassName:"table-of-contents__popover",renderToggle:function(e){var n=e.isOpen,r=e.onToggle;return Object(Br.createElement)(Vr.IconButton,{onClick:t?r:void 0,icon:"info-outline","aria-expanded":n,label:Object(_.__)("Content structure"),labelPosition:"bottom","aria-disabled":!t})},renderContent:function(){return Object(Br.createElement)(hl,null)}})}),vl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).warnIfUnsavedChanges=e.warnIfUnsavedChanges.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){window.addEventListener("beforeunload",this.warnIfUnsavedChanges)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("beforeunload",this.warnIfUnsavedChanges)}},{key:"warnIfUnsavedChanges",value:function(e){if(this.props.isDirty)return e.returnValue=Object(_.__)("You have unsaved changes. If you proceed, they will be lost."),e.returnValue}},{key:"render",value:function(){return null}}]),t}(Br.Component),Ol=Object(l.withSelect)(function(e){return{isDirty:e("core/editor").isEditedPostDirty()}})(vl),gl=Object(l.withSelect)(function(e){return{selectedBlockClientId:e("core/editor").getBlockSelectionStart()}})(function(e){var t=e.selectedBlockClientId;return t&&Object(Br.createElement)(Vr.Button,{isDefault:!0,type:"button",className:"editor-skip-to-selected-block",onClick:function(){Gi(t).closest(".editor-block-list__block").focus()}},Object(_.__)("Skip to the selected block"))}),jl=n(124),yl=n.n(jl);function kl(e,t,n){var r=new yl.a(e);return t&&r.remove("is-style-"+t.name),r.add("is-style-"+n.name),r.value}var _l=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.clientId,r=e("core/editor").getBlock,o=e("core/blocks").getBlockStyles,i=r(n);return{name:i.name,attributes:i.attributes,className:i.attributes.className||"",styles:o(i.name)}}),Object(l.withDispatch)(function(e,t){var n=t.clientId;return{onChangeClassName:function(t){e("core/editor").updateBlockAttributes(n,{className:t})}}})])(function(e){var t=e.styles,n=e.className,r=e.onChangeClassName,o=e.name,i=e.attributes,c=e.onSwitch,a=void 0===c?O.noop:c,s=e.onHoverClassName,l=void 0===s?O.noop:s;if(!t||0===t.length)return null;var u=function(e,t){var n=!0,r=!1,o=void 0;try{for(var i,c=new yl.a(t).values()[Symbol.iterator]();!(n=(i=c.next()).done);n=!0){var a=i.value;if(-1!==a.indexOf("is-style-")){var s=a.substring(9),l=Object(O.find)(e,{name:s});if(l)return l}}}catch(e){r=!0,o=e}finally{try{n||null==c.return||c.return()}finally{if(r)throw o}}return Object(O.find)(e,"isDefault")}(t,n);function p(e){var t=kl(n,u,e);r(t),a()}return Object(Br.createElement)("div",{className:"editor-block-styles"},t.map(function(e){var t=kl(n,u,e);return Object(Br.createElement)("div",{key:e.name,className:Lr()("editor-block-styles__item",{"is-active":u===e}),onClick:function(){return p(e)},onKeyDown:function(t){yo.ENTER!==t.keyCode&&yo.SPACE!==t.keyCode||(t.preventDefault(),p(e))},onMouseEnter:function(){return l(t)},onMouseLeave:function(){return l(null)},role:"button",tabIndex:"0","aria-label":e.label||e.name},Object(Br.createElement)("div",{className:"editor-block-styles__item-preview"},Object(Br.createElement)(Si,{name:o,attributes:Object(d.a)({},i,{className:t})})),Object(Br.createElement)("div",{className:"editor-block-styles__item-label"},e.label||e.name))}))});var El=Object(l.withSelect)(function(e){return{blocks:(0,e("core/editor").getMultiSelectedBlocks)()}})(function(e){var t=e.blocks,n=Object(bl.count)(Object(i.serialize)(t),"words");return Object(Br.createElement)("div",{className:"editor-multi-selection-inspector__card"},Object(Br.createElement)(Qr,{icon:Object(Br.createElement)(Vr.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(Br.createElement)(Vr.Path,{d:"M3 5H1v16c0 1.1.9 2 2 2h16v-2H3V5zm18-4H7c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 16H7V3h14v14z"})),showColors:!0}),Object(Br.createElement)("div",{className:"editor-multi-selection-inspector__card-content"},Object(Br.createElement)("div",{className:"editor-multi-selection-inspector__card-title"},Object(_.sprintf)(Object(_._n)("%d block","%d blocks",t.length),t.length)),Object(Br.createElement)("div",{className:"editor-multi-selection-inspector__card-description"},Object(_.sprintf)(Object(_._n)("%d word","%d words",n),n))))}),Sl=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getSelectedBlockClientId,r=t.getSelectedBlockCount,o=t.getBlockName,c=e("core/blocks").getBlockStyles,a=n(),s=a&&o(a),l=a&&Object(i.getBlockType)(s),u=a&&c(s);return{count:r(),hasBlockStyles:u&&u.length>0,selectedBlockName:s,selectedBlockClientId:a,blockType:l}})(function(e){var t=e.selectedBlockClientId,n=e.selectedBlockName,r=e.blockType,o=e.count,c=e.hasBlockStyles;if(o>1)return Object(Br.createElement)(El,null);var a=n===Object(i.getUnregisteredTypeHandlerName)();return r&&t&&!a?Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)("div",{className:"editor-block-inspector__card"},Object(Br.createElement)(Qr,{icon:r.icon,showColors:!0}),Object(Br.createElement)("div",{className:"editor-block-inspector__card-content"},Object(Br.createElement)("div",{className:"editor-block-inspector__card-title"},r.title),Object(Br.createElement)("div",{className:"editor-block-inspector__card-description"},r.description))),c&&Object(Br.createElement)("div",null,Object(Br.createElement)(Vr.PanelBody,{title:Object(_.__)("Styles"),initialOpen:!1},Object(Br.createElement)(_l,{clientId:t}))),Object(Br.createElement)("div",null,Object(Br.createElement)(fc.Slot,null)),Object(Br.createElement)("div",null,Object(Br.createElement)(lc.Slot,null,function(e){return!Object(O.isEmpty)(e)&&Object(Br.createElement)(Vr.PanelBody,{className:"editor-block-inspector__advanced",title:Object(_.__)("Advanced"),initialOpen:!1},e)})),Object(Br.createElement)(gl,{key:"back"})):Object(Br.createElement)("span",{className:"editor-block-inspector__no-blocks"},Object(_.__)("No block selected."))}),Cl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).bindContainer=e.bindContainer.bind(Object(Hr.a)(Object(Hr.a)(e))),e.clearSelectionIfFocusTarget=e.clearSelectionIfFocusTarget.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"bindContainer",value:function(e){this.container=e}},{key:"clearSelectionIfFocusTarget",value:function(e){var t=this.props,n=t.hasSelectedBlock,r=t.hasMultiSelection,o=t.clearSelectedBlock,i=n||r;e.target===this.container&&i&&o()}},{key:"render",value:function(){return Object(Br.createElement)("div",Object(Ir.a)({tabIndex:-1,onFocus:this.clearSelectionIfFocusTarget,ref:this.bindContainer},Object(O.omit)(this.props,["clearSelectedBlock","hasSelectedBlock","hasMultiSelection"])))}}]),t}(Br.Component),wl=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.hasSelectedBlock,r=t.hasMultiSelection;return{hasSelectedBlock:n(),hasMultiSelection:r()}}),Object(l.withDispatch)(function(e){return{clearSelectedBlock:e("core/editor").clearSelectedBlock}})])(Cl);var Tl=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.clientId,r=e("core/editor"),o=r.getBlock,c=r.getBlockMode,a=o(n);return{mode:c(n),blockType:a?Object(i.getBlockType)(a.name):null}}),Object(l.withDispatch)(function(e,t){var n=t.onToggle,r=void 0===n?O.noop:n,o=t.clientId;return{onToggleMode:function(){e("core/editor").toggleBlockMode(o),r()}}})])(function(e){var t=e.blockType,n=e.mode,r=e.onToggleMode,o=e.small,c=void 0!==o&&o;if(!Object(i.hasBlockSupport)(t,"html",!0))return null;var a="visual"===n?Object(_.__)("Edit as HTML"):Object(_.__)("Edit visually");return Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",onClick:r,icon:"html",label:c?a:void 0},!c&&a)});var Pl=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.clientIds,r=e("core/editor"),o=r.getBlocksByClientId,c=r.canInsertBlockType,a=r.__experimentalGetReusableBlock,s=o(n),l=c("core/block")&&Object(O.every)(s,function(e){return!!e&&e.isValid&&Object(i.hasBlockSupport)(e.name,"reusable",!0)});return{isVisible:l,isStaticBlock:l&&(1!==s.length||!Object(i.isReusableBlock)(s[0])||!a(s[0].attributes.ref))}}),Object(l.withDispatch)(function(e,t){var n=t.clientIds,r=t.onToggle,o=void 0===r?O.noop:r,i=e("core/editor"),c=i.__experimentalConvertBlockToReusable,a=i.__experimentalConvertBlockToStatic;return{onConvertToStatic:function(){1===n.length&&(a(n[0]),o())},onConvertToReusable:function(){c(n),o()}}})])(function(e){var t=e.isVisible,n=e.isStaticBlock,r=e.onConvertToStatic,o=e.onConvertToReusable;return t?Object(Br.createElement)(Br.Fragment,null,n&&Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",icon:"controls-repeat",onClick:o},Object(_.__)("Add to Reusable Blocks")),!n&&Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",icon:"controls-repeat",onClick:r},Object(_.__)("Convert to Regular Block"))):null});var Il=Object(Ar.compose)([Object(l.withSelect)(function(e,t){var n=t.clientId,r=e("core/editor"),o=r.getBlock,c=r.__experimentalGetReusableBlock,a=o(n);return{reusableBlock:a&&Object(i.isReusableBlock)(a)?c(a.attributes.ref):null}}),Object(l.withDispatch)(function(e,t){var n=t.onToggle,r=void 0===n?O.noop:n,o=e("core/editor").__experimentalDeleteReusableBlock;return{onDelete:function(e){window.confirm(Object(_.__)("Are you sure you want to delete this Reusable Block?\n\nIt will be permanently removed from all posts and pages that use it."))&&(o(e),r())}}})])(function(e){var t=e.reusableBlock,n=e.onDelete;return t?Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",icon:"no",disabled:t.isTemporary,onClick:function(){return n(t.id)}},Object(_.__)("Remove from Reusable Blocks")):null});function Bl(e){var t=e.shouldRender,n=e.onClick,r=e.small;if(!t)return null;var o=Object(_.__)("Convert to Blocks");return Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",onClick:n,icon:"screenoptions",label:r?o:void 0},!r&&o)}var xl=Object(Ar.compose)(Object(l.withSelect)(function(e,t){var n=t.clientId,r=e("core/editor").getBlock(n);return{block:r,shouldRender:r&&"core/html"===r.name}}),Object(l.withDispatch)(function(e,t){var n=t.block;return{onClick:function(){return e("core/editor").replaceBlocks(n.clientId,Object(i.rawHandler)({HTML:Object(i.getBlockContent)(n)}))}}}))(Bl),Ll=Object(Ar.compose)(Object(l.withSelect)(function(e,t){var n=t.clientId,r=e("core/editor").getBlock(n);return{block:r,shouldRender:r&&r.name===Object(i.getFreeformContentHandlerName)()}}),Object(l.withDispatch)(function(e,t){var n=t.block;return{onClick:function(){return e("core/editor").replaceBlocks(n.clientId,Object(i.rawHandler)({HTML:Object(i.serialize)(n)}))}}}))(Bl),Al=Object(Vr.createSlotFill)("_BlockSettingsMenuFirstItem"),Nl=Al.Fill,Rl=Al.Slot;Nl.Slot=Rl;var Dl=Nl,Fl=Object(Vr.createSlotFill)("_BlockSettingsMenuPluginsExtension"),Ml=Fl.Fill,Ul=Fl.Slot;Ml.Slot=Ul;var Hl=Ml;var Vl=Object(l.withDispatch)(function(e){var t=e("core/editor").selectBlock;return{onSelect:function(e){t(e)}}})(function(e){var t=e.clientIds,n=e.onSelect,r=Object(O.castArray)(t),o=r.length,i=r[0];return Object(Br.createElement)(Sa,{clientIds:t},function(e){var r=e.onDuplicate,c=e.onRemove,a=e.onInsertAfter,s=e.onInsertBefore,l=e.canDuplicate,u=e.isLocked;return Object(Br.createElement)(Vr.Dropdown,{contentClassName:"editor-block-settings-menu__popover",position:"bottom right",renderToggle:function(e){var t=e.onToggle,r=e.isOpen,c=Lr()("editor-block-settings-menu__toggle",{"is-opened":r}),a=r?Object(_.__)("Hide options"):Object(_.__)("More options");return Object(Br.createElement)(Vr.Toolbar,{controls:[{icon:"ellipsis",title:a,onClick:function(){1===o&&n(i),t()},className:c,extraProps:{"aria-expanded":r}}]})},renderContent:function(e){var n=e.onClose;return Object(Br.createElement)(Vr.NavigableMenu,{className:"editor-block-settings-menu__content"},Object(Br.createElement)(Dl.Slot,{fillProps:{onClose:n}}),1===o&&Object(Br.createElement)(Ll,{clientId:i}),1===o&&Object(Br.createElement)(xl,{clientId:i}),!u&&l&&Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",onClick:r,icon:"admin-page",shortcut:wa.duplicate.display},Object(_.__)("Duplicate")),!u&&Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",onClick:s,icon:"insert-before",shortcut:wa.insertBefore.display},Object(_.__)("Insert Before")),Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",onClick:a,icon:"insert-after",shortcut:wa.insertAfter.display},Object(_.__)("Insert After"))),1===o&&Object(Br.createElement)(Tl,{clientId:i,onToggle:n}),Object(Br.createElement)(Pl,{clientIds:t,onToggle:n}),Object(Br.createElement)(Hl.Slot,{fillProps:{clientIds:t,onClose:n}}),Object(Br.createElement)("div",{className:"editor-block-settings-menu__separator"}),1===o&&Object(Br.createElement)(Il,{clientId:i,onToggle:n}),!u&&Object(Br.createElement)(Vr.MenuItem,{className:"editor-block-settings-menu__control",onClick:c,icon:"trash",shortcut:wa.removeBlock.display},Object(_.__)("Remove Block")))}})})}),Kl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).state={hoveredClassName:null},e.onHoverClassName=e.onHoverClassName.bind(Object(Hr.a)(Object(Hr.a)(e))),e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"onHoverClassName",value:function(e){this.setState({hoveredClassName:e})}},{key:"render",value:function(){var e=this,t=this.props,n=t.blocks,r=t.onTransform,o=t.inserterItems,c=t.hasBlockStyles,a=this.state.hoveredClassName;if(!n||!n.length)return null;var s=Object(O.mapKeys)(o,function(e){return e.name}),l=Object(O.orderBy)(Object(O.filter)(Object(i.getPossibleBlockTransformations)(n),function(e){return e&&!!s[e.name]}),function(e){return s[e.name].frecency},"desc"),u=n[0].name,p=Object(i.getBlockType)(u);return c||l.length?Object(Br.createElement)(Vr.Dropdown,{position:"bottom right",className:"editor-block-switcher",contentClassName:"editor-block-switcher__popover",renderToggle:function(e){var t=e.onToggle,r=e.isOpen,o=1===n.length?Object(_.__)("Change block type"):Object(_.sprintf)(Object(_._n)("Change type of %d block","Change type of %d blocks",n.length),n.length);return Object(Br.createElement)(Vr.Toolbar,null,Object(Br.createElement)(Vr.IconButton,{className:"editor-block-switcher__toggle",onClick:t,"aria-haspopup":"true","aria-expanded":r,label:o,tooltip:o,onKeyDown:function(e){r||e.keyCode!==yo.DOWN||(e.preventDefault(),e.stopPropagation(),t())}},Object(Br.createElement)(Qr,{icon:p.icon,showColors:!0}),Object(Br.createElement)(Vr.SVG,{className:"editor-block-switcher__transform",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(Br.createElement)(Vr.Path,{d:"M6.5 8.9c.6-.6 1.4-.9 2.2-.9h6.9l-1.3 1.3 1.4 1.4L19.4 7l-3.7-3.7-1.4 1.4L15.6 6H8.7c-1.4 0-2.6.5-3.6 1.5l-2.8 2.8 1.4 1.4 2.8-2.8zm13.8 2.4l-2.8 2.8c-.6.6-1.3.9-2.1.9h-7l1.3-1.3-1.4-1.4L4.6 16l3.7 3.7 1.4-1.4L8.4 17h6.9c1.3 0 2.6-.5 3.5-1.5l2.8-2.8-1.3-1.4z"}))))},renderContent:function(t){var o=t.onClose;return Object(Br.createElement)(Br.Fragment,null,c&&Object(Br.createElement)(Vr.PanelBody,{title:Object(_.__)("Block Styles"),initialOpen:!0},Object(Br.createElement)(_l,{clientId:n[0].clientId,onSwitch:o,onHoverClassName:e.onHoverClassName})),0!==l.length&&Object(Br.createElement)(Vr.PanelBody,{title:Object(_.__)("Transform To:"),initialOpen:!0},Object(Br.createElement)(Ti,{items:l.map(function(e){return{id:e.name,icon:e.icon,title:e.title,hasChildBlocksWithInserterSupport:Object(i.hasChildBlocksWithInserterSupport)(e.name)}}),onSelect:function(e){r(n,e.id),o()}})),null!==a&&Object(Br.createElement)(Ci,{name:n[0].name,attributes:Object(d.a)({},n[0].attributes,{className:a})}))}}):n.length>1?null:Object(Br.createElement)(Vr.Toolbar,null,Object(Br.createElement)(Vr.IconButton,{disabled:!0,className:"editor-block-switcher__no-switcher-icon",label:Object(_.__)("Block icon")},Object(Br.createElement)(Qr,{icon:p.icon,showColors:!0})))}}]),t}(Br.Component),zl=Object(Ar.compose)(Object(l.withSelect)(function(e,t){var n=t.clientIds,r=e("core/editor"),o=r.getBlocksByClientId,i=r.getBlockRootClientId,c=r.getInserterItems,a=e("core/blocks").getBlockStyles,s=i(Object(O.first)(Object(O.castArray)(n))),l=o(n),u=l&&1===l.length?l[0]:null,d=u&&a(u.name);return{blocks:l,inserterItems:c(s),hasBlockStyles:d&&d.length>0}}),Object(l.withDispatch)(function(e,t){return{onTransform:function(n,r){e("core/editor").replaceBlocks(t.clientIds,Object(i.switchToBlockType)(n,r))}}}))(Kl);var Wl=Object(l.withSelect)(function(e){var t=e("core/editor").getMultiSelectedBlockClientIds();return{isMultiBlockSelection:t.length>1,selectedBlockClientIds:t}})(function(e){var t=e.isMultiBlockSelection,n=e.selectedBlockClientIds;return t?Object(Br.createElement)(zl,{key:"switcher",clientIds:n}):null});var ql=Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getSelectedBlockClientId,r=t.getBlockMode,o=t.getMultiSelectedBlockClientIds,i=t.isBlockValid,c=n();return{blockClientIds:c?[c]:o(),isValid:c?i(c):null,mode:c?r(c):null}})(function(e){var t=e.blockClientIds,n=e.isValid,r=e.mode;return 0===t.length?null:t.length>1?Object(Br.createElement)("div",{className:"editor-block-toolbar"},Object(Br.createElement)(Wl,null),Object(Br.createElement)(Vl,{clientIds:t})):Object(Br.createElement)("div",{className:"editor-block-toolbar"},"visual"===r&&n&&Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(zl,{clientIds:t}),Object(Br.createElement)(bo.Slot,null),Object(Br.createElement)(jo.Slot,null)),Object(Br.createElement)(Vl,{clientIds:t}))}),Gl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onCopy=function(t){return e.props.onCopy(t)},e.onCut=function(t){return e.props.onCut(t)},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){document.addEventListener("copy",this.onCopy),document.addEventListener("cut",this.onCut)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("copy",this.onCopy),document.removeEventListener("cut",this.onCut)}},{key:"render",value:function(){return null}}]),t}(Br.Component),$l=Object(Ar.compose)([Object(l.withDispatch)(function(e,t,n){var r=(0,n.select)("core/editor"),o=r.getBlocksByClientId,c=r.getMultiSelectedBlockClientIds,a=r.getSelectedBlockClientId,s=r.hasMultiSelection,l=e("core/editor").removeBlocks,u=function(e){var t=a()?[a()]:c();if(0!==t.length&&(s()||!Object(Vo.documentHasSelection)())){var n=Object(i.serialize)(o(t));e.clipboardData.setData("text/plain",n),e.clipboardData.setData("text/html",n),e.preventDefault()}};return{onCopy:u,onCut:function(e){if(u(e),s()){var t=a()?[a()]:c();l(t)}}}})])(Gl),Yl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).reboot=e.reboot.bind(Object(Hr.a)(Object(Hr.a)(e))),e.getContent=e.getContent.bind(Object(Hr.a)(Object(Hr.a)(e))),e.state={error:null},e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidCatch",value:function(e){this.setState({error:e})}},{key:"reboot",value:function(){this.props.onError()}},{key:"getContent",value:function(){try{return Object(l.select)("core/editor").getEditedPostContent()}catch(e){}}},{key:"render",value:function(){var e=this.state.error;return e?Object(Br.createElement)(ei,{className:"editor-error-boundary",actions:[Object(Br.createElement)(Vr.Button,{key:"recovery",onClick:this.reboot,isLarge:!0},Object(_.__)("Attempt Recovery")),Object(Br.createElement)(Vr.ClipboardButton,{key:"copy-post",text:this.getContent,isLarge:!0},Object(_.__)("Copy Post Text")),Object(Br.createElement)(Vr.ClipboardButton,{key:"copy-error",text:e.stack,isLarge:!0},Object(_.__)("Copy Error"))]},Object(_.__)("The editor has encountered an unexpected error.")):this.props.children}}]),t}(Br.Component),Ql=function(e){function t(){return Object(Rr.a)(this,t),Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidUpdate",value:function(){this.scrollIntoView()}},{key:"scrollIntoView",value:function(){var e=this.props.extentClientId;if(e){var t=Gi(e);if(t){var n=Object(Vo.getScrollContainer)(t);n&&Ei()(t,n,{onlyScrollIfNeeded:!0})}}}},{key:"render",value:function(){return null}}]),t}(Br.Component),Xl=Object(l.withSelect)(function(e){return{extentClientId:(0,e("core/editor").getLastMultiSelectedBlockClientId)()}})(Ql),Zl=[yo.UP,yo.RIGHT,yo.DOWN,yo.LEFT,yo.ENTER,yo.BACKSPACE];var Jl=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).stopTypingOnSelectionUncollapse=e.stopTypingOnSelectionUncollapse.bind(Object(Hr.a)(Object(Hr.a)(e))),e.stopTypingOnMouseMove=e.stopTypingOnMouseMove.bind(Object(Hr.a)(Object(Hr.a)(e))),e.startTypingInTextField=e.startTypingInTextField.bind(Object(Hr.a)(Object(Hr.a)(e))),e.stopTypingOnNonTextField=e.stopTypingOnNonTextField.bind(Object(Hr.a)(Object(Hr.a)(e))),e.stopTypingOnEscapeKey=e.stopTypingOnEscapeKey.bind(Object(Hr.a)(Object(Hr.a)(e))),e.onKeyDown=Object(O.over)([e.startTypingInTextField,e.stopTypingOnEscapeKey]),e.lastMouseMove=null,e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){this.toggleEventBindings(this.props.isTyping)}},{key:"componentDidUpdate",value:function(e){this.props.isTyping!==e.isTyping&&this.toggleEventBindings(this.props.isTyping)}},{key:"componentWillUnmount",value:function(){this.toggleEventBindings(!1)}},{key:"toggleEventBindings",value:function(e){var t=e?"addEventListener":"removeEventListener";document[t]("selectionchange",this.stopTypingOnSelectionUncollapse),document[t]("mousemove",this.stopTypingOnMouseMove)}},{key:"stopTypingOnMouseMove",value:function(e){var t=e.clientX,n=e.clientY;if(this.lastMouseMove){var r=this.lastMouseMove,o=r.clientX,i=r.clientY;o===t&&i===n||this.props.onStopTyping()}this.lastMouseMove={clientX:t,clientY:n}}},{key:"stopTypingOnSelectionUncollapse",value:function(){var e=window.getSelection();e.rangeCount>0&&e.getRangeAt(0).collapsed||this.props.onStopTyping()}},{key:"stopTypingOnEscapeKey",value:function(e){this.props.isTyping&&e.keyCode===yo.ESCAPE&&this.props.onStopTyping()}},{key:"startTypingInTextField",value:function(e){var t=this.props,n=t.isTyping,r=t.onStartTyping,o=e.type,i=e.target;n||!Object(Vo.isTextField)(i)||i.closest(".editor-block-toolbar")||("keydown"!==o||function(e){var t=e.keyCode;return!e.shiftKey&&Object(O.includes)(Zl,t)}(e))&&r()}},{key:"stopTypingOnNonTextField",value:function(e){var t=this;e.persist(),this.props.setTimeout(function(){var n=t.props,r=n.isTyping,o=n.onStopTyping,i=e.target;r&&!Object(Vo.isTextField)(i)&&o()})}},{key:"render",value:function(){var e=this.props.children;return Object(Br.createElement)("div",{onFocus:this.stopTypingOnNonTextField,onKeyPress:this.startTypingInTextField,onKeyDown:this.onKeyDown},e)}}]),t}(Br.Component),eu=Object(Ar.compose)([Object(l.withSelect)(function(e){return{isTyping:(0,e("core/editor").isTyping)()}}),Object(l.withDispatch)(function(e){var t=e("core/editor");return{onStartTyping:t.startTyping,onStopTyping:t.stopTyping}}),Ar.withSafeTimeout])(Jl),tu=function(e){function t(){return Object(Rr.a)(this,t),Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"getSnapshotBeforeUpdate",value:function(e){var t=this.props,n=t.blockOrder,r=t.selectionStart;return n!==e.blockOrder&&r?this.getOffset(r):null}},{key:"componentDidUpdate",value:function(e,t,n){n&&this.restorePreviousOffset(n)}},{key:"getOffset",value:function(e){var t=Gi(e);return t?t.getBoundingClientRect().top:null}},{key:"restorePreviousOffset",value:function(e){var t=Gi(this.props.selectionStart);if(t){var n=Object(Vo.getScrollContainer)(t);n&&(n.scrollTop=n.scrollTop+t.getBoundingClientRect().top-e)}}},{key:"render",value:function(){return null}}]),t}(Br.Component),nu=Object(l.withSelect)(function(e){return{blockOrder:e("core/editor").getBlockOrder(),selectionStart:e("core/editor").getBlockSelectionStart()}})(tu),ru=window.getSelection,ou=Object(O.overEvery)([Vo.isTextField,Vo.focus.tabbable.isTabbableIndex]);var iu=function(e){function t(){var e;return Object(Rr.a)(this,t),(e=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments))).onKeyDown=e.onKeyDown.bind(Object(Hr.a)(Object(Hr.a)(e))),e.bindContainer=e.bindContainer.bind(Object(Hr.a)(Object(Hr.a)(e))),e.clearVerticalRect=e.clearVerticalRect.bind(Object(Hr.a)(Object(Hr.a)(e))),e.focusLastTextField=e.focusLastTextField.bind(Object(Hr.a)(Object(Hr.a)(e))),e.verticalRect=null,e}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"bindContainer",value:function(e){this.container=e}},{key:"clearVerticalRect",value:function(){this.verticalRect=null}},{key:"getClosestTabbable",value:function(e,t){var n=Vo.focus.focusable.find(this.container);return t&&(n=Object(O.reverse)(n)),n=n.slice(n.indexOf(e)+1),Object(O.find)(n,function t(n,r,o){if(!Vo.focus.tabbable.isTabbableIndex(n))return!1;if(Object(Vo.isTextField)(n))return!0;if(!n.classList.contains("editor-block-list__block"))return!1;if(function(e){return!!e.querySelector(".editor-block-list__layout")}(n))return!0;if(n.contains(e))return!1;for(var i,c=1;(i=o[r+c])&&n.contains(i);c++)if(t(i,r+c,o))return!1;return!0})}},{key:"expandSelection",value:function(e){var t=this.props,n=t.selectedBlockClientId,r=t.selectionStartClientId,o=t.selectionBeforeEndClientId,i=t.selectionAfterEndClientId,c=e?o:i;c&&this.props.onMultiSelect(r||n,c)}},{key:"moveSelection",value:function(e){var t=this.props,n=t.selectedFirstClientId,r=t.selectedLastClientId,o=e?n:r;o&&this.props.onSelectBlock(o)}},{key:"isTabbableEdge",value:function(e,t){var n,r,o=this.getClosestTabbable(e,t);return!(o&&(n=e,r=o,n.closest("[data-block]")===r.closest("[data-block]")))}},{key:"onKeyDown",value:function(e){var t=this.props,n=t.hasMultiSelection,r=t.onMultiSelect,o=t.blocks,i=e.keyCode,c=e.target,a=i===yo.UP,s=i===yo.DOWN,l=i===yo.LEFT,u=i===yo.RIGHT,d=a||l,p=l||u,b=a||s,f=p||b,h=e.shiftKey,m=h||e.ctrlKey||e.altKey||e.metaKey,v=b?Vo.isVerticalEdge:Vo.isHorizontalEdge;if(!f)return yo.isKeyboardEvent.primary(e)&&(this.isEntirelySelected=Object(Vo.isEntirelySelected)(c)),void(yo.isKeyboardEvent.primary(e,"a")&&((c.isContentEditable?this.isEntirelySelected:Object(Vo.isEntirelySelected)(c))&&(r(Object(O.first)(o),Object(O.last)(o)),e.preventDefault()),this.isEntirelySelected=!0));if(!e.nativeEvent.defaultPrevented&&function(e,t,n){if((t===yo.UP||t===yo.DOWN)&&!n)return!0;var r=e.tagName;return"INPUT"!==r&&"TEXTAREA"!==r}(c,i,m))if(b?this.verticalRect||(this.verticalRect=Object(Vo.computeCaretRect)(c)):this.verticalRect=null,h&&(n||this.isTabbableEdge(c,d)&&v(c,d)))this.expandSelection(d),e.preventDefault();else if(n)this.moveSelection(d),e.preventDefault();else if(b&&Object(Vo.isVerticalEdge)(c,d)){var g=this.getClosestTabbable(c,d);g&&(Object(Vo.placeCaretAtVerticalEdge)(g,d,this.verticalRect),e.preventDefault())}else if(p&&ru().isCollapsed&&Object(Vo.isHorizontalEdge)(c,d)){var j=this.getClosestTabbable(c,d);Object(Vo.placeCaretAtHorizontalEdge)(j,d),e.preventDefault()}}},{key:"focusLastTextField",value:function(){var e=Vo.focus.focusable.find(this.container),t=Object(O.findLast)(e,ou);t&&Object(Vo.placeCaretAtHorizontalEdge)(t,!0)}},{key:"render",value:function(){var e=this.props.children;return Object(Br.createElement)("div",{className:"editor-writing-flow"},Object(Br.createElement)("div",{ref:this.bindContainer,onKeyDown:this.onKeyDown,onMouseDown:this.clearVerticalRect},e),Object(Br.createElement)("div",{"aria-hidden":!0,tabIndex:-1,onClick:this.focusLastTextField,className:"wp-block editor-writing-flow__click-redirect"}))}}]),t}(Br.Component),cu=Object(Ar.compose)([Object(l.withSelect)(function(e){var t=e("core/editor"),n=t.getSelectedBlockClientId,r=t.getMultiSelectedBlocksStartClientId,o=t.getMultiSelectedBlocksEndClientId,i=t.getPreviousBlockClientId,c=t.getNextBlockClientId,a=t.getFirstMultiSelectedBlockClientId,s=t.getLastMultiSelectedBlockClientId,l=t.hasMultiSelection,u=t.getBlockOrder,d=n(),p=r(),b=o();return{selectedBlockClientId:d,selectionStartClientId:p,selectionBeforeEndClientId:i(b||d),selectionAfterEndClientId:c(b||d),selectedFirstClientId:a(),selectedLastClientId:s(),hasMultiSelection:l(),blocks:u()}}),Object(l.withDispatch)(function(e){var t=e("core/editor");return{onMultiSelect:t.multiSelect,onSelectBlock:t.selectBlock}})])(iu),au=/\/\*[^*]*\*+([^\/*][^*]*\*+)*\//g,su=function(e,t){t=t||{};var n=1,r=1;function o(e){var t=e.match(/\n/g);t&&(n+=t.length);var o=e.lastIndexOf("\n");r=~o?e.length-o:r+e.length}function i(){var e={line:n,column:r};return function(t){return t.position=new c(e),b(),t}}function c(e){this.start=e,this.end={line:n,column:r},this.source=t.source}c.prototype.content=e;var a=[];function s(o){var i=new Error(t.source+":"+n+":"+r+": "+o);if(i.reason=o,i.filename=t.source,i.line=n,i.column=r,i.source=e,!t.silent)throw i;a.push(i)}function l(){return p(/^{\s*/)}function u(){return p(/^}/)}function d(){var t,n=[];for(b(),f(n);e.length&&"}"!==e.charAt(0)&&(t=C()||w());)!1!==t&&(n.push(t),f(n));return n}function p(t){var n=t.exec(e);if(n){var r=n[0];return o(r),e=e.slice(r.length),n}}function b(){p(/^\s*/)}function f(e){var t;for(e=e||[];t=m();)!1!==t&&e.push(t);return e}function m(){var t=i();if("/"===e.charAt(0)&&"*"===e.charAt(1)){for(var n=2;""!==e.charAt(n)&&("*"!==e.charAt(n)||"/"!==e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return s("End of comment missing");var c=e.slice(2,n-2);return r+=2,o(c),e=e.slice(n),r+=2,t({type:"comment",comment:c})}}function v(){var e=p(/^([^{]+)/);if(e)return lu(e[0]).replace(/\/\*([^*]|[\r\n]|(\*+([^*\/]|[\r\n])))*\*\/+/g,"").replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g,function(e){return e.replace(/,/g,"‌")}).split(/\s*(?![^(]*\)),\s*/).map(function(e){return e.replace(/\u200C/g,",")})}function O(){var e=i(),t=p(/^(\*?[-#\/\*\\\w]+(\[[0-9a-z_-]+\])?)\s*/);if(t){if(t=lu(t[0]),!p(/^:\s*/))return s("property missing ':'");var n=p(/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/),r=e({type:"declaration",property:t.replace(au,""),value:n?lu(n[0]).replace(au,""):""});return p(/^[;\s]*/),r}}function g(){var e,t=[];if(!l())return s("missing '{'");for(f(t);e=O();)!1!==e&&(t.push(e),f(t));return u()?t:s("missing '}'")}function j(){for(var e,t=[],n=i();e=p(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/);)t.push(e[1]),p(/^,\s*/);if(t.length)return n({type:"keyframe",values:t,declarations:g()})}var y,k=S("import"),_=S("charset"),E=S("namespace");function S(e){var t=new RegExp("^@"+e+"\\s*([^;]+);");return function(){var n=i(),r=p(t);if(r){var o={type:e};return o[e]=r[1].trim(),n(o)}}}function C(){if("@"===e[0])return function(){var e=i(),t=p(/^@([-\w]+)?keyframes\s*/);if(t){var n=t[1];if(!(t=p(/^([-\w]+)\s*/)))return s("@keyframes missing name");var r,o=t[1];if(!l())return s("@keyframes missing '{'");for(var c=f();r=j();)c.push(r),c=c.concat(f());return u()?e({type:"keyframes",name:o,vendor:n,keyframes:c}):s("@keyframes missing '}'")}}()||function(){var e=i(),t=p(/^@media *([^{]+)/);if(t){var n=lu(t[1]);if(!l())return s("@media missing '{'");var r=f().concat(d());return u()?e({type:"media",media:n,rules:r}):s("@media missing '}'")}}()||function(){var e=i(),t=p(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/);if(t)return e({type:"custom-media",name:lu(t[1]),media:lu(t[2])})}()||function(){var e=i(),t=p(/^@supports *([^{]+)/);if(t){var n=lu(t[1]);if(!l())return s("@supports missing '{'");var r=f().concat(d());return u()?e({type:"supports",supports:n,rules:r}):s("@supports missing '}'")}}()||k()||_()||E()||function(){var e=i(),t=p(/^@([-\w]+)?document *([^{]+)/);if(t){var n=lu(t[1]),r=lu(t[2]);if(!l())return s("@document missing '{'");var o=f().concat(d());return u()?e({type:"document",document:r,vendor:n,rules:o}):s("@document missing '}'")}}()||function(){var e=i();if(p(/^@page */)){var t=v()||[];if(!l())return s("@page missing '{'");for(var n,r=f();n=O();)r.push(n),r=r.concat(f());return u()?e({type:"page",selectors:t,declarations:r}):s("@page missing '}'")}}()||function(){var e=i();if(p(/^@host\s*/)){if(!l())return s("@host missing '{'");var t=f().concat(d());return u()?e({type:"host",rules:t}):s("@host missing '}'")}}()||function(){var e=i();if(p(/^@font-face\s*/)){if(!l())return s("@font-face missing '{'");for(var t,n=f();t=O();)n.push(t),n=n.concat(f());return u()?e({type:"font-face",declarations:n}):s("@font-face missing '}'")}}()}function w(){var e=i(),t=v();return t?(f(),e({type:"rule",selectors:t,declarations:g()})):s("selector missing")}return function e(t,n){var r=t&&"string"==typeof t.type;var o=r?t:n;for(var i in t){var c=t[i];Array.isArray(c)?c.forEach(function(t){e(t,o)}):c&&"object"===Object(h.a)(c)&&e(c,o)}r&&Object.defineProperty(t,"parent",{configurable:!0,writable:!0,enumerable:!1,value:n||null});return t}((y=d(),{type:"stylesheet",stylesheet:{source:t.source,rules:y,parsingErrors:a}}))};function lu(e){return e?e.replace(/^\s+|\s+$/g,""):""}var uu=n(98),du=n.n(uu),pu=bu;function bu(e){this.options=e||{}}bu.prototype.emit=function(e){return e},bu.prototype.visit=function(e){return this[e.type](e)},bu.prototype.mapVisit=function(e,t){var n="";t=t||"";for(var r=0,o=e.length;r<o;r++)n+=this.visit(e[r]),t&&r<o-1&&(n+=this.emit(t));return n};var fu=hu;function hu(e){pu.call(this,e)}du()(hu,pu),hu.prototype.compile=function(e){return e.stylesheet.rules.map(this.visit,this).join("")},hu.prototype.comment=function(e){return this.emit("",e.position)},hu.prototype.import=function(e){return this.emit("@import "+e.import+";",e.position)},hu.prototype.media=function(e){return this.emit("@media "+e.media,e.position)+this.emit("{")+this.mapVisit(e.rules)+this.emit("}")},hu.prototype.document=function(e){var t="@"+(e.vendor||"")+"document "+e.document;return this.emit(t,e.position)+this.emit("{")+this.mapVisit(e.rules)+this.emit("}")},hu.prototype.charset=function(e){return this.emit("@charset "+e.charset+";",e.position)},hu.prototype.namespace=function(e){return this.emit("@namespace "+e.namespace+";",e.position)},hu.prototype.supports=function(e){return this.emit("@supports "+e.supports,e.position)+this.emit("{")+this.mapVisit(e.rules)+this.emit("}")},hu.prototype.keyframes=function(e){return this.emit("@"+(e.vendor||"")+"keyframes "+e.name,e.position)+this.emit("{")+this.mapVisit(e.keyframes)+this.emit("}")},hu.prototype.keyframe=function(e){var t=e.declarations;return this.emit(e.values.join(","),e.position)+this.emit("{")+this.mapVisit(t)+this.emit("}")},hu.prototype.page=function(e){var t=e.selectors.length?e.selectors.join(", "):"";return this.emit("@page "+t,e.position)+this.emit("{")+this.mapVisit(e.declarations)+this.emit("}")},hu.prototype["font-face"]=function(e){return this.emit("@font-face",e.position)+this.emit("{")+this.mapVisit(e.declarations)+this.emit("}")},hu.prototype.host=function(e){return this.emit("@host",e.position)+this.emit("{")+this.mapVisit(e.rules)+this.emit("}")},hu.prototype["custom-media"]=function(e){return this.emit("@custom-media "+e.name+" "+e.media+";",e.position)},hu.prototype.rule=function(e){var t=e.declarations;return t.length?this.emit(e.selectors.join(","),e.position)+this.emit("{")+this.mapVisit(t)+this.emit("}"):""},hu.prototype.declaration=function(e){return this.emit(e.property+":"+e.value,e.position)+this.emit(";")};var mu=vu;function vu(e){e=e||{},pu.call(this,e),this.indentation=e.indent}du()(vu,pu),vu.prototype.compile=function(e){return this.stylesheet(e)},vu.prototype.stylesheet=function(e){return this.mapVisit(e.stylesheet.rules,"\n\n")},vu.prototype.comment=function(e){return this.emit(this.indent()+"/*"+e.comment+"*/",e.position)},vu.prototype.import=function(e){return this.emit("@import "+e.import+";",e.position)},vu.prototype.media=function(e){return this.emit("@media "+e.media,e.position)+this.emit(" {\n"+this.indent(1))+this.mapVisit(e.rules,"\n\n")+this.emit(this.indent(-1)+"\n}")},vu.prototype.document=function(e){var t="@"+(e.vendor||"")+"document "+e.document;return this.emit(t,e.position)+this.emit("  {\n"+this.indent(1))+this.mapVisit(e.rules,"\n\n")+this.emit(this.indent(-1)+"\n}")},vu.prototype.charset=function(e){return this.emit("@charset "+e.charset+";",e.position)},vu.prototype.namespace=function(e){return this.emit("@namespace "+e.namespace+";",e.position)},vu.prototype.supports=function(e){return this.emit("@supports "+e.supports,e.position)+this.emit(" {\n"+this.indent(1))+this.mapVisit(e.rules,"\n\n")+this.emit(this.indent(-1)+"\n}")},vu.prototype.keyframes=function(e){return this.emit("@"+(e.vendor||"")+"keyframes "+e.name,e.position)+this.emit(" {\n"+this.indent(1))+this.mapVisit(e.keyframes,"\n")+this.emit(this.indent(-1)+"}")},vu.prototype.keyframe=function(e){var t=e.declarations;return this.emit(this.indent())+this.emit(e.values.join(", "),e.position)+this.emit(" {\n"+this.indent(1))+this.mapVisit(t,"\n")+this.emit(this.indent(-1)+"\n"+this.indent()+"}\n")},vu.prototype.page=function(e){var t=e.selectors.length?e.selectors.join(", ")+" ":"";return this.emit("@page "+t,e.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(e.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")},vu.prototype["font-face"]=function(e){return this.emit("@font-face ",e.position)+this.emit("{\n")+this.emit(this.indent(1))+this.mapVisit(e.declarations,"\n")+this.emit(this.indent(-1))+this.emit("\n}")},vu.prototype.host=function(e){return this.emit("@host",e.position)+this.emit(" {\n"+this.indent(1))+this.mapVisit(e.rules,"\n\n")+this.emit(this.indent(-1)+"\n}")},vu.prototype["custom-media"]=function(e){return this.emit("@custom-media "+e.name+" "+e.media+";",e.position)},vu.prototype.rule=function(e){var t=this.indent(),n=e.declarations;return n.length?this.emit(e.selectors.map(function(e){return t+e}).join(",\n"),e.position)+this.emit(" {\n")+this.emit(this.indent(1))+this.mapVisit(n,"\n")+this.emit(this.indent(-1))+this.emit("\n"+this.indent()+"}"):""},vu.prototype.declaration=function(e){return this.emit(this.indent())+this.emit(e.property+": "+e.value,e.position)+this.emit(";")},vu.prototype.indent=function(e){return this.level=this.level||1,null!==e?(this.level+=e,""):Array(this.level).join(this.indentation||"  ")};var Ou=function(e,t){return((t=t||{}).compress?new fu(t):new mu(t)).compile(e)},gu=n(193),ju=n.n(gu);var yu=function(e,t){try{var n=su(e),r=ju.a.map(n,function(e){if(!e)return e;var n=t(e);return this.update(n)});return Ou(r)}catch(e){return console.warn("Error while traversing the CSS: "+e),null}},ku=n(75);function _u(e){return 0!==e.value.indexOf("data:")&&0!==e.value.indexOf("#")&&(t=e.value,!/^\/(?!\/)/.test(t)&&!function(e){return/^(?:https?:)?\/\//.test(e)}(e.value));var t}function Eu(e){return function(t){var n=function(e,t){var n=Object(ku.parse)(e).pathname;return Object(ku.resolve)(t,n)}(t.value,e);return Object(d.a)({},t,{newUrl:"url("+t.before+t.quote+n+t.quote+t.after+")"})}}var Su=function(e){return function(t){if("declaration"===t.type){var n=function(e){for(var t,n=/url\((\s*)(['"]?)(.+?)\2(\s*)\)/g,r=[];null!==(t=n.exec(e));){var o={source:t[0],before:t[1],quote:t[2],value:t[3],after:t[4]};_u(o)&&r.push(o)}return r}(t.value).map(Eu(e));return Object(d.a)({},t,{value:(r=t.value,o=n,o.forEach(function(e){r=r.replace(e.source,e.newUrl)}),r)})}var r,o;return t}},Cu=/^(body|html).*$/,wu=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return function(n){return"rule"===n.type?Object(d.a)({},n,{selectors:n.selectors.map(function(n){return Object(O.includes)(t,n.trim())?n:n.match(Cu)?n.replace(/^(body|html)/,e):e+" "+n})}):n}},Tu=function(e){function t(e){var n;return Object(Rr.a)(this,t),n=Object(Fr.a)(this,Object(Mr.a)(t).apply(this,arguments)),e.recovery?Object(Fr.a)(n):(e.updateEditorSettings(e.settings),e.updatePostLock(e.settings.postLock),e.setupEditor(e.post,e.initialEdits),e.settings.autosave&&e.createWarningNotice(Object(_.__)("There is an autosave of this post that is more recent than the version below."),{id:"autosave-exists",actions:[{label:Object(_.__)("View the autosave"),url:e.settings.autosave.editLink}]}),n)}return Object(Ur.a)(t,e),Object(Dr.a)(t,[{key:"componentDidMount",value:function(){this.props.settings.styles&&Object(O.map)(this.props.settings.styles,function(e){var t=e.css,n=e.baseURL,r=[wu(".editor-styles-wrapper")];n&&r.push(Su(n));var o=yu(t,Object(Ar.compose)(r));if(o){var i=document.createElement("style");i.innerHTML=o,document.body.appendChild(i)}})}},{key:"componentDidUpdate",value:function(e){this.props.settings!==e.settings&&this.props.updateEditorSettings(this.props.settings)}},{key:"render",value:function(){var e=this.props.children,t=[[Vr.SlotFillProvider],[Vr.DropZoneProvider]];return Object(O.flow)(t.map(function(e){var t=Object(u.a)(e,2),n=t[0],r=t[1];return function(e){return Object(Br.createElement)(n,r,e)}}))(e)}}]),t}(Br.Component),Pu=Object(l.withDispatch)(function(e){var t=e("core/editor");return{setupEditor:t.setupEditor,updateEditorSettings:t.updateEditorSettings,updatePostLock:t.updatePostLock,createWarningNotice:e("core/notices").createWarningNotice}})(Tu),Iu=["left","center","right","wide","full"],Bu=["wide","full"];function xu(e){var t,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return t=Array.isArray(e)?e:!0===e?Iu:[],!r||!0===e&&!n?O.without.apply(void 0,[t].concat(Bu)):t}var Lu=Object(Ar.createHigherOrderComponent)(function(e){return function(t){var n=t.name,r=xu(Object(i.getBlockSupport)(n,"align"),Object(i.hasBlockSupport)(n,"alignWide",!0));return[r.length>0&&t.isSelected&&Object(Br.createElement)(bo,{key:"align-controls"},Object(Br.createElement)(ao,{value:t.attributes.align,onChange:function(e){if(!e){var n=Object(i.getBlockType)(t.name);Object(O.get)(n,["attributes","align","default"])&&(e="")}t.setAttributes({align:e})},controls:r})),Object(Br.createElement)(e,Object(Ir.a)({key:"edit"},t))]}},"withToolbarControls"),Au=Object(Ar.createHigherOrderComponent)(Object(Ar.compose)([Object(l.withSelect)(function(e){return{hasWideEnabled:!!(0,e("core/editor").getEditorSettings)().alignWide}}),function(e){return function(t){var n=t.name,r=t.attributes,o=t.hasWideEnabled,c=r.align,a=xu(Object(i.getBlockSupport)(n,"align"),Object(i.hasBlockSupport)(n,"alignWide",!0),o),s=t.wrapperProps;return Object(O.includes)(a,c)&&(s=Object(d.a)({},s,{"data-align":c})),Object(Br.createElement)(e,Object(Ir.a)({},t,{wrapperProps:s}))}}]));Object(Nr.addFilter)("blocks.registerBlockType","core/align/addAttribute",function(e){return Object(O.has)(e.attributes,["align","type"])?e:(Object(i.hasBlockSupport)(e,"align")&&(e.attributes=Object(O.assign)(e.attributes,{align:{type:"string"}})),e)}),Object(Nr.addFilter)("editor.BlockListBlock","core/editor/align/with-data-align",Au),Object(Nr.addFilter)("editor.BlockEdit","core/editor/align/with-toolbar-controls",Lu),Object(Nr.addFilter)("blocks.getSaveContent.extraProps","core/align/addAssignedAlign",function(e,t,n){var r=n.align,o=Object(i.getBlockSupport)(t,"align"),c=Object(i.hasBlockSupport)(t,"alignWide",!0);return Object(O.includes)(xu(o,c),r)&&(e.className=Lr()("align".concat(r),e.className)),e});var Nu=/[\s#]/g;var Ru=Object(Ar.createHigherOrderComponent)(function(e){return function(t){return Object(i.hasBlockSupport)(t.name,"anchor")&&t.isSelected?Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(e,t),Object(Br.createElement)(lc,null,Object(Br.createElement)(Vr.TextControl,{label:Object(_.__)("HTML Anchor"),help:Object(_.__)("Anchors lets you link directly to a section on a page."),value:t.attributes.anchor||"",onChange:function(e){e=e.replace(Nu,"-"),t.setAttributes({anchor:e})}}))):Object(Br.createElement)(e,t)}},"withInspectorControl");Object(Nr.addFilter)("blocks.registerBlockType","core/anchor/attribute",function(e){return Object(i.hasBlockSupport)(e,"anchor")&&(e.attributes=Object(O.assign)(e.attributes,{anchor:{type:"string",source:"attribute",attribute:"id",selector:"*"}})),e}),Object(Nr.addFilter)("editor.BlockEdit","core/editor/anchor/with-inspector-control",Ru),Object(Nr.addFilter)("blocks.getSaveContent.extraProps","core/anchor/save-props",function(e,t,n){return Object(i.hasBlockSupport)(t,"anchor")&&(e.id=""===n.anchor?null:n.anchor),e});var Du=Object(Ar.createHigherOrderComponent)(function(e){return function(t){return Object(i.hasBlockSupport)(t.name,"customClassName",!0)&&t.isSelected?Object(Br.createElement)(Br.Fragment,null,Object(Br.createElement)(e,t),Object(Br.createElement)(lc,null,Object(Br.createElement)(Vr.TextControl,{label:Object(_.__)("Additional CSS Class"),value:t.attributes.className||"",onChange:function(e){t.setAttributes({className:e})}}))):Object(Br.createElement)(e,t)}},"withInspectorControl");function Fu(e){e="<div data-custom-class-name>".concat(e,"</div>");var t=Object(i.parseWithAttributeSchema)(e,{type:"string",source:"attribute",selector:"[data-custom-class-name] > *",attribute:"class"});return t?t.trim().split(/\s+/):[]}Object(Nr.addFilter)("blocks.registerBlockType","core/custom-class-name/attribute",function(e){return Object(i.hasBlockSupport)(e,"customClassName",!0)&&(e.attributes=Object(O.assign)(e.attributes,{className:{type:"string"}})),e}),Object(Nr.addFilter)("editor.BlockEdit","core/editor/custom-class-name/with-inspector-control",Du),Object(Nr.addFilter)("blocks.getSaveContent.extraProps","core/custom-class-name/save-props",function(e,t,n){return Object(i.hasBlockSupport)(t,"customClassName",!0)&&n.className&&(e.className=Lr()(e.className,n.className)),e}),Object(Nr.addFilter)("blocks.getBlockAttributes","core/custom-class-name/addParsedDifference",function(e,t,n){if(Object(i.hasBlockSupport)(t,"customClassName",!0)){var r=Object(O.omit)(e,["className"]),o=Object(i.getSaveContent)(t,r),c=Fu(o),a=Fu(n),s=Object(O.difference)(a,c);s.length?e.className=s.join(" "):o&&delete e.className}return e});var Mu=[to],Uu=Object(O.once)(function(){return Object(l.dispatch)("core/editor").__experimentalFetchReusableBlocks()});Object(Nr.addFilter)("editor.Autocomplete.completers","editor/autocompleters/set-default-completers",function(e,t){return e||(e=Mu.map(O.clone),t===Object(i.getDefaultBlockName)()&&(e.push(Object(O.clone)(eo)),Uu())),e}),Object(Nr.addFilter)("blocks.getSaveContent.extraProps","core/generated-class-name/save-props",function(e,t){return Object(i.hasBlockSupport)(t,"className",!0)&&("string"==typeof e.className?e.className=Object(O.uniq)([Object(i.getBlockDefaultClassName)(t.name)].concat(Object(b.a)(e.className.split(" ")))).join(" ").trim():e.className=Object(i.getBlockDefaultClassName)(t.name)),e}),n.d(t,"Autocomplete",function(){return Yr}),n.d(t,"AlignmentToolbar",function(){return ro}),n.d(t,"BlockAlignmentToolbar",function(){return ao}),n.d(t,"BlockControls",function(){return bo}),n.d(t,"BlockEdit",function(){return ho}),n.d(t,"BlockFormatControls",function(){return jo}),n.d(t,"BlockNavigationDropdown",function(){return So}),n.d(t,"BlockIcon",function(){return Qr}),n.d(t,"ColorPalette",function(){return wo}),n.d(t,"withColorContext",function(){return Co}),n.d(t,"ContrastChecker",function(){return No}),n.d(t,"InnerBlocks",function(){return oc}),n.d(t,"InspectorAdvancedControls",function(){return lc}),n.d(t,"InspectorControls",function(){return fc}),n.d(t,"PanelColorSettings",function(){return gc}),n.d(t,"PlainText",function(){return jc}),n.d(t,"RichText",function(){return Zc}),n.d(t,"RichTextShortcut",function(){return Rc}),n.d(t,"RichTextToolbarButton",function(){return Gc}),n.d(t,"RichTextInserterItem",function(){return $c}),n.d(t,"ServerSideRender",function(){return Jc}),n.d(t,"MediaPlaceholder",function(){return ua}),n.d(t,"MediaUpload",function(){return ea}),n.d(t,"MediaUploadCheck",function(){return Xo}),n.d(t,"URLInput",function(){return ba}),n.d(t,"URLInputButton",function(){return fa}),n.d(t,"URLPopover",function(){return ta}),n.d(t,"AutosaveMonitor",function(){return ma}),n.d(t,"DocumentOutline",function(){return _a}),n.d(t,"DocumentOutlineCheck",function(){return Ea}),n.d(t,"EditorGlobalKeyboardShortcuts",function(){return Pa}),n.d(t,"EditorHistoryRedo",function(){return Ia}),n.d(t,"EditorHistoryUndo",function(){return Ba}),n.d(t,"EditorNotices",function(){return La}),n.d(t,"PageAttributesCheck",function(){return Aa}),n.d(t,"PageAttributesOrder",function(){return Da}),n.d(t,"PageAttributesParent",function(){return Ha}),n.d(t,"PageTemplate",function(){return Va}),n.d(t,"PostAuthor",function(){return Wa}),n.d(t,"PostAuthorCheck",function(){return Ka}),n.d(t,"PostComments",function(){return qa}),n.d(t,"PostExcerpt",function(){return Ga}),n.d(t,"PostExcerptCheck",function(){return $a}),n.d(t,"PostFeaturedImage",function(){return rs}),n.d(t,"PostFeaturedImageCheck",function(){return Qa}),n.d(t,"PostFormat",function(){return cs}),n.d(t,"PostFormatCheck",function(){return os}),n.d(t,"PostLastRevision",function(){return ss}),n.d(t,"PostLastRevisionCheck",function(){return as}),n.d(t,"PostLockedModal",function(){return fs}),n.d(t,"PostPendingStatus",function(){return ms}),n.d(t,"PostPendingStatusCheck",function(){return hs}),n.d(t,"PostPingbacks",function(){return vs}),n.d(t,"PostPreviewButton",function(){return ps}),n.d(t,"PostPublishButton",function(){return js}),n.d(t,"PostPublishButtonLabel",function(){return Os}),n.d(t,"PostPublishPanel",function(){return Hs}),n.d(t,"PostSavedState",function(){return zs}),n.d(t,"PostSchedule",function(){return Ss}),n.d(t,"PostScheduleCheck",function(){return Ws}),n.d(t,"PostScheduleLabel",function(){return Cs}),n.d(t,"PostSticky",function(){return Gs}),n.d(t,"PostStickyCheck",function(){return qs}),n.d(t,"PostSwitchToDraftButton",function(){return Vs}),n.d(t,"PostTaxonomies",function(){return Xs}),n.d(t,"PostTaxonomiesCheck",function(){return Zs}),n.d(t,"PostTextEditor",function(){return el}),n.d(t,"PostTitle",function(){return ll}),n.d(t,"PostTrash",function(){return ul}),n.d(t,"PostTrashCheck",function(){return dl}),n.d(t,"PostTypeSupportCheck",function(){return Na}),n.d(t,"PostVisibility",function(){return _s}),n.d(t,"PostVisibilityLabel",function(){return Es}),n.d(t,"PostVisibilityCheck",function(){return pl}),n.d(t,"TableOfContents",function(){return ml}),n.d(t,"UnsavedChangesWarning",function(){return Ol}),n.d(t,"WordCount",function(){return fl}),n.d(t,"BlockInspector",function(){return Sl}),n.d(t,"BlockList",function(){return nc}),n.d(t,"BlockMover",function(){return Qo}),n.d(t,"BlockSelectionClearer",function(){return wl}),n.d(t,"BlockSettingsMenu",function(){return Vl}),n.d(t,"_BlockSettingsMenuFirstItem",function(){return Dl}),n.d(t,"_BlockSettingsMenuPluginsExtension",function(){return Hl}),n.d(t,"BlockTitle",function(){return fi}),n.d(t,"BlockToolbar",function(){return ql}),n.d(t,"CopyHandler",function(){return $l}),n.d(t,"DefaultBlockAppender",function(){return Ji}),n.d(t,"ErrorBoundary",function(){return Yl}),n.d(t,"Inserter",function(){return Di}),n.d(t,"MultiBlocksSwitcher",function(){return Wl}),n.d(t,"MultiSelectScrollIntoView",function(){return Xl}),n.d(t,"NavigableToolbar",function(){return ji}),n.d(t,"ObserveTyping",function(){return eu}),n.d(t,"PreserveScrollInReorder",function(){return nu}),n.d(t,"SkipToSelectedBlock",function(){return gl}),n.d(t,"Warning",function(){return ei}),n.d(t,"WritingFlow",function(){return cu}),n.d(t,"EditorProvider",function(){return Pu}),n.d(t,"blockAutocompleter",function(){return eo}),n.d(t,"userAutocompleter",function(){return to}),n.d(t,"getColorClassName",function(){return xo}),n.d(t,"getColorObjectByAttributeValues",function(){return Io}),n.d(t,"getColorObjectByColorValue",function(){return Bo}),n.d(t,"withColors",function(){return Ao}),n.d(t,"getFontSize",function(){return Ro}),n.d(t,"getFontSizeClass",function(){return Do}),n.d(t,"FontSizePicker",function(){return Fo}),n.d(t,"withFontSizes",function(){return Mo}),n.d(t,"mediaUpload",function(){return oa}),n.d(t,"cleanForSlug",function(){return ca})}]);