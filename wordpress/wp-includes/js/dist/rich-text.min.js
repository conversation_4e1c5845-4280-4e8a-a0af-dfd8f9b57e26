this.wp=this.wp||{},this.wp.richText=function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=307)}({0:function(e,t){!function(){e.exports=this.wp.element}()},15:function(e,t,r){"use strict";function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,"a",function(){return n})},18:function(e,t,r){"use strict";function n(){return(n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}r.d(t,"a",function(){return n})},19:function(e,t,r){"use strict";var n=r(33);function a(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||Object(n.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}r.d(t,"a",function(){return a})},2:function(e,t){!function(){e.exports=this.lodash}()},23:function(e,t){!function(){e.exports=this.wp.hooks}()},28:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e){return(a="function"==typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return n(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":n(e)})(e)}r.d(t,"a",function(){return a})},307:function(e,t,r){"use strict";r.r(t);var n={};r.r(n),r.d(n,"getFormatTypes",function(){return s}),r.d(n,"getFormatType",function(){return f}),r.d(n,"getFormatTypeForBareElement",function(){return d}),r.d(n,"getFormatTypeForClassName",function(){return p});var a={};r.r(a),r.d(a,"addFormatTypes",function(){return m}),r.d(a,"removeFormatTypes",function(){return v});var o=r(5),i=r(8),c=r(2);var u=Object(o.combineReducers)({formatTypes:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"ADD_FORMAT_TYPES":return Object(i.a)({},e,Object(c.keyBy)(t.formatTypes,"name"));case"REMOVE_FORMAT_TYPES":return Object(c.omit)(e,t.names)}return e}}),l=r(31),s=Object(l.a)(function(e){return Object.values(e.formatTypes)},function(e){return[e.formatTypes]});function f(e,t){return e.formatTypes[t]}function d(e,t){return Object(c.find)(s(e),function(e){var r=e.tagName;return t===r})}function p(e,t){return Object(c.find)(s(e),function(e){var r=e.className;return null!==r&&" ".concat(t," ").indexOf(" ".concat(r," "))>=0})}function m(e){return{type:"ADD_FORMAT_TYPES",formatTypes:Object(c.castArray)(e)}}function v(e){return{type:"REMOVE_FORMAT_TYPES",names:Object(c.castArray)(e)}}function g(e,t){if(e===t)return!0;if(!e||!t)return!1;if(e.type!==t.type)return!1;var r=e.attributes,n=t.attributes;if(r===n)return!0;if(!r||!n)return!1;var a=Object.keys(r),o=Object.keys(n);if(a.length!==o.length)return!1;for(var i=a.length,c=0;c<i;c++){var u=a[c];if(r[u]!==n[u])return!1}return!0}function h(e){var t=e.formats,r=e.text,n=e.start,a=e.end,o=[];return{formats:t.map(function(e){return e.map(function(e){var t=Object(c.find)(o,function(t){return g(t,e)});return t||(o.push(e),e)})}),text:r,start:n,end:a}}function b(e,t){var r=e.formats,n=e.text,a=e.start,o=e.end,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:o,l=r.slice(0);if(i===u){var s=Object(c.find)(l[i],{type:t.type});if(!s){var f=l[i-1]||[];return{formats:r,text:n,start:a,end:o,formatPlaceholder:{index:i,format:Object(c.find)(f,{type:t.type})?void 0:t}}}for(;Object(c.find)(l[i],s);)y(l,i,t),i--;for(u++;Object(c.find)(l[u],s);)y(l,u,t),u++}else for(var d=i;d<u;d++)y(l,d,t);return h({formats:l,text:n,start:a,end:o})}function y(e,t,r){if(e[t]){var n=e[t].filter(function(e){return e.type!==r.type});n.push(r),e[t]=n}else e[t]=[r]}function x(e,t){return e.text[t]}function T(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return h(t.reduce(function(e,t){var r=t.formats,n=t.text;return{text:e.text+n,formats:e.formats.concat(r)}}))}Object(o.registerStore)("core/rich-text",{reducer:u,selectors:n,actions:a});var O=r(19),j=r(28),w="\u2028",N="￼",E="\ufeff";function _(e){return 0===e.text.length}function C(e){var t=e.text,r=e.start,n=e.end;return r===n&&(0===t.length||(0===r&&t.slice(0,1)===w||(r===t.length&&t.slice(-1)===w||t.slice(r-1,n+1)==="".concat(w).concat(w))))}function S(e,t){var r=e.implementation;return S.body||(S.body=r.createHTMLDocument("").body),S.body.innerHTML=t,S.body}var F=window.Node,A=F.TEXT_NODE,P=F.ELEMENT_NODE;function D(e,t){for(var r in e)if(e[r]===t)return r}function W(e){var t,r=e.type,n=e.attributes;if(n&&n.class&&(t=Object(o.select)("core/rich-text").getFormatTypeForClassName(n.class))&&(n.class=" ".concat(n.class," ").replace(" ".concat(t.className," ")," ").trim(),n.class||delete n.class),t||(t=Object(o.select)("core/rich-text").getFormatTypeForBareElement(r)),!t)return n?{type:r,attributes:n}:{type:r};if(!n)return{type:t.name};var a={},i={};for(var c in n){var u=D(t.attributes,c);u?a[u]=n[c]:i[c]=n[c]}return{type:t.name,attributes:a,unregisteredAttributes:i}}function L(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.element,r=e.text,n=e.html,a=e.range,o=e.multilineTag,i=e.multilineWrapperTags,c=e.removeNode,u=e.unwrapNode,l=e.filterString,s=e.removeAttribute;return"string"==typeof r&&r.length>0?{formats:Array(r.length),text:r}:("string"==typeof n&&n.length>0&&(t=S(document,n)),"object"!==Object(j.a)(t)?{formats:[],text:""}:o?V({element:t,range:a,multilineTag:o,multilineWrapperTags:i,removeNode:c,unwrapNode:u,filterString:l,removeAttribute:s}):k({element:t,range:a,removeNode:c,unwrapNode:u,filterString:l,removeAttribute:s}))}function I(e,t,r,n){if(r){var a=t.parentNode,o=r.startContainer,i=r.startOffset,c=r.endContainer,u=r.endOffset,l=e.text.length;void 0!==n.start?e.start=l+n.start:t===o&&t.nodeType===A?e.start=l+i:a===o&&t===o.childNodes[i]?e.start=l:a===o&&t===o.childNodes[i-1]?e.start=l+n.text.length:t===o&&(e.start=l),void 0!==n.end?e.end=l+n.end:t===c&&t.nodeType===A?e.end=l+u:a===c&&t===c.childNodes[u-1]?e.end=l+n.text.length:a===c&&t===c.childNodes[u]?e.end=l:t===c&&(e.end=l+u)}}function M(e,t,r){if(t){var n=t.startContainer,a=t.endContainer,o=t.startOffset,i=t.endOffset;return e===n&&(o=r(e.nodeValue.slice(0,o)).length),e===a&&(i=r(e.nodeValue.slice(0,i)).length),{startContainer:n,startOffset:o,endContainer:a,endOffset:i}}}function k(e){var t=e.element,r=e.range,n=e.multilineTag,a=e.multilineWrapperTags,o=e.currentWrapperTags,i=void 0===o?[]:o,c=e.removeNode,u=e.unwrapNode,l=e.filterString,s=e.removeAttribute,f={formats:[],text:""};if(!t)return f;if(!t.hasChildNodes())return I(f,t,r,{formats:[],text:""}),f;for(var d=t.childNodes.length,p=function(e){return e=e.replace(/[\n\r\t]+/g," "),l&&(e=l(e)),e},m=0;m<d;m++){var v=t.childNodes[m],h=v.nodeName.toLowerCase();if(v.nodeType!==A){if(v.nodeType===P)if(c&&c(v)||u&&u(v)&&!v.hasChildNodes())I(f,v,r,{formats:[],text:""});else if("br"!==h){var b=f.formats[f.formats.length-1],y=b&&b[b.length-1],x=void 0,T=void 0;if(!u||!u(v)){var j=W({type:h,attributes:R({element:v,removeAttribute:s})});j&&(x=g(j,y)?y:j)}a&&-1!==a.indexOf(h)?(T=V({element:v,range:r,multilineTag:n,multilineWrapperTags:a,removeNode:c,unwrapNode:u,filterString:l,removeAttribute:s,currentWrapperTags:Object(O.a)(i).concat([x])}),x=void 0):T=k({element:v,range:r,multilineTag:n,multilineWrapperTags:a,removeNode:c,unwrapNode:u,filterString:l,removeAttribute:s});var w=T.text,E=f.text.length;if(I(f,v,r,T),!_(T)||!x||x.attributes){var C=f.formats;if(x&&x.attributes&&0===w.length)x.object=!0,f.text+=N,C[E]?C[E].unshift(x):C[E]=[x];else{f.text+=w,f.formats.length+=w.length;for(var S=T.formats.length;S--;){var F,D=E+S;if(x&&(C[D]?C[D].push(x):C[D]=[x]),T.formats[S])if(C[D])(F=C[D]).push.apply(F,Object(O.a)(T.formats[S]));else C[D]=T.formats[S]}}}}else I(f,v,r,{formats:[],text:""}),f.text+="\n",f.formats.length+=1}else{var L=p(v.nodeValue);I(f,v,r=M(v,r,p),{text:L}),f.text+=L,f.formats.length+=L.length}}return f}function V(e){var t=e.element,r=e.range,n=e.multilineTag,a=e.multilineWrapperTags,o=e.removeNode,i=e.unwrapNode,c=e.filterString,u=e.removeAttribute,l=e.currentWrapperTags,s=void 0===l?[]:l,f={formats:[],text:""};if(!t||!t.hasChildNodes())return f;for(var d=t.children.length,p=0;p<d;p++){var m=t.children[p];if(m.nodeName.toLowerCase()===n){var v=k({element:m,range:r,multilineTag:n,multilineWrapperTags:a,currentWrapperTags:s,removeNode:o,unwrapNode:i,filterString:c,removeAttribute:u});if("\n"===v.text){var g=v.start,h=v.end;v={formats:[],text:""},void 0!==g&&(v.start=0),void 0!==h&&(v.end=0)}if(0!==p||s.length>0){var b=s.length>0?[s]:[,];f.formats=f.formats.concat(b),f.text+=w}I(f,m,r,v),f.formats=f.formats.concat(v.formats),f.text+=v.text}}return f}function R(e){var t=e.element,r=e.removeAttribute;if(t.hasAttributes()){for(var n,a=t.attributes.length,o=0;o<a;o++){var i=t.attributes[o],c=i.name,u=i.value;r&&r(c)||((n=n||{})[c]=u)}return n}}function z(e,t){var r=e.formats,n=e.start;if(void 0!==n)return Object(c.find)(r[n],{type:t})}function B(e){return e.end}function H(e){return e.start}function G(e){return e.text}function Y(e){var t=e.start,r=e.end;if(void 0!==t&&void 0!==r)return t===r}function q(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"string"==typeof t&&(t=L({text:t})),h(e.reduce(function(e,r){var n=r.formats,a=r.text;return{text:e.text+t.text+a,formats:e.formats.concat(t.formats,n)}}))}var $=r(15),U=r(18),X=r(0),Z=r(41),K=r.n(Z),J=r(23),Q=r(7),ee=[];function te(e,t){if("string"==typeof(t=Object(i.a)({name:e},t)).name)if(/^[a-z][a-z0-9-]*\/[a-z][a-z0-9-]*$/.test(t.name))if(Object(o.select)("core/rich-text").getFormatType(t.name))window.console.error('Format "'+t.name+'" is already registered.');else if("string"==typeof t.tagName&&""!==t.tagName)if("string"==typeof t.className&&""!==t.className||null===t.className)if(/^[_a-zA-Z]+[a-zA-Z0-9-]*$/.test(t.className)){if(null===t.className){var r=Object(o.select)("core/rich-text").getFormatTypeForBareElement(t.tagName);if(r)return void window.console.error('Format "'.concat(r.name,'" is already registered to handle bare tag name "').concat(t.tagName,'".'))}else{var n=Object(o.select)("core/rich-text").getFormatTypeForClassName(t.className);if(n)return void window.console.error('Format "'.concat(n.name,'" is already registered to handle class name "').concat(t.className,'".'))}if("title"in t&&""!==t.title)if("keywords"in t&&t.keywords.length>3)window.console.error('The format "'+t.name+'" can have a maximum of 3 keywords.');else{if("string"==typeof t.title){Object(o.dispatch)("core/rich-text").addFormatTypes(t);var a=K()(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ee,t=arguments.length>1?arguments[1]:void 0;return Object(O.a)(e).concat([t])});return t.__experimentalGetPropsForEditableTreePreparation&&Object(J.addFilter)("experimentalRichText",e,function(r){var n=r;(t.__experimentalCreatePrepareEditableTree||t.__experimentalCreateFormatToValue||t.__experimentalCreateValueToFormat)&&(n=function(n){var o={};if(t.__experimentalCreatePrepareEditableTree&&(o.prepareEditableTree=a(n.prepareEditableTree,t.__experimentalCreatePrepareEditableTree(n["format_".concat(e)],{richTextIdentifier:n.identifier,blockClientId:n.clientId}))),t.__experimentalCreateOnChangeEditableValue){var c=Object.keys(n).reduce(function(t,r){var a=n[r],o="format_".concat(e,"_dispatch_");r.startsWith(o)&&(t[r.replace(o,"")]=a);return t},{});o.onChangeEditableValue=a(n.onChangeEditableValue,t.__experimentalCreateOnChangeEditableValue(Object(i.a)({},n["format_".concat(e)],c),{richTextIdentifier:n.identifier,blockClientId:n.clientId}))}return Object(X.createElement)(r,Object(U.a)({},n,o))});var u=[Object(o.withSelect)(function(r,n){var a=n.clientId,o=n.identifier;return Object($.a)({},"format_".concat(e),t.__experimentalGetPropsForEditableTreePreparation(r,{richTextIdentifier:o,blockClientId:a}))})];return t.__experimentalGetPropsForEditableTreeChangeHandler&&u.push(Object(o.withDispatch)(function(r,n){var a=n.clientId,o=n.identifier,i=t.__experimentalGetPropsForEditableTreeChangeHandler(r,{richTextIdentifier:o,blockClientId:a});return Object(c.mapKeys)(i,function(t,r){return"format_".concat(e,"_dispatch_").concat(r)})})),Object(Q.compose)(u)(n)}),t}window.console.error("Format titles must be strings.")}else window.console.error('The format "'+t.name+'" must have a title.')}else window.console.error("A class name must begin with a letter, followed by any number of hyphens, letters, or numbers.");else window.console.error("Format class names must be a string, or null to handle bare elements.");else window.console.error("Format tag names must be a string.");else window.console.error("Format names must contain a namespace prefix, include only lowercase alphanumeric characters or dashes, and start with a letter. Example: my-plugin/my-custom-format");else window.console.error("Format names must be strings.")}function re(e,t){var r=e.formats,n=e.text,a=e.start,o=e.end,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:o,l=r.slice(0);if(i===u){for(var s=Object(c.find)(l[i],{type:t});Object(c.find)(l[i],s);)ne(l,i,t),i--;for(u++;Object(c.find)(l[u],s);)ne(l,u,t),u++}else for(var f=i;f<u;f++)l[f]&&ne(l,f,t);return h({formats:l,text:n,start:a,end:o})}function ne(e,t,r){var n=e[t].filter(function(e){return e.type!==r});n.length?e[t]=n:delete e[t]}function ae(e,t){var r=e.formats,n=e.text,a=e.start,o=e.end,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a,c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:o;"string"==typeof t&&(t=L({text:t}));var u=i+t.text.length;return h({formats:r.slice(0,i).concat(t.formats,r.slice(c)),text:n.slice(0,i)+t.text+n.slice(c),start:u,end:u})}function oe(e,t,r){return ae(e,L(),t,r)}function ie(e,t,r){var n=e.formats,a=e.text,o=e.start,i=e.end;return a=a.replace(t,function(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),c=1;c<t;c++)a[c-1]=arguments[c];var u,l=a[a.length-2],s=r;return"function"==typeof s&&(s=r.apply(void 0,[e].concat(a))),"object"===Object(j.a)(s)?(u=s.formats,s=s.text):(u=Array(s.length),n[l]&&(u=u.fill(n[l]))),n=n.slice(0,l).concat(u,n.slice(l+e.length)),o&&(o=i=l+s.length),s}),h({formats:n,text:a,start:o,end:i})}function ce(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.start,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.end,n=G(e).slice(0,t).lastIndexOf(w),a=e.formats[n],o=[,];return a&&(o=[a]),ae(e,{formats:o,text:w},t,r)}var ue="￼";function le(e,t,r,n){return ae(e,{text:ue,formats:[[Object(i.a)({},t,{object:!0})]]},r,n)}function se(e){var t=e.formats,r=e.text,n=e.start,a=e.end,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a;return void 0===o||void 0===i?{formats:t,text:r}:{formats:t.slice(o,i),text:r.slice(o,i)}}function fe(e,t){var r=e.formats,n=e.text,a=e.start,o=e.end;if("string"!=typeof t)return function(e){var t=e.formats,r=e.text,n=e.start,a=e.end,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a,c={formats:t.slice(0,o),text:r.slice(0,o)},u={formats:t.slice(i),text:r.slice(i),start:0,end:0};return[ie(c,/\u2028+$/,""),ie(u,/^\u2028+/,"")]}.apply(void 0,arguments);var i=0;return n.split(t).map(function(e){var n=i,c={formats:r.slice(n,n+e.length),text:e};return i+=t.length+e.length,void 0!==a&&void 0!==o&&(a>=n&&a<i?c.start=a-n:a<n&&o>n&&(c.start=0),o>=n&&o<i?c.end=o-n:a<i&&o>i&&(c.end=e.length)),c})}function de(e){var t=e.type,r=e.attributes,n=e.unregisteredAttributes,a=e.object,c=function(e){return Object(o.select)("core/rich-text").getFormatType(e)}(t);if(!c)return{type:t,attributes:r,object:a};var u=Object(i.a)({},n);for(var l in r){var s=c.attributes[l];s?u[s]=r[l]:u[l]=r[l]}return c.className&&(u.class?u.class="".concat(c.className," ").concat(u.class):u.class=c.className),{type:c.tagName,object:c.object,attributes:u}}function pe(e){var t,r,n,a=e.value,o=e.multilineTag,i=e.multilineWrapperTags,c=void 0===i?[]:i,u=e.createEmpty,l=e.append,s=e.getLastChild,f=e.getParent,d=e.isText,p=e.getText,m=e.remove,v=e.appendText,g=e.onStartIndex,h=e.onEndIndex,b=e.isEditableTree,y=a.formats,x=a.text,T=a.start,j=a.end,_=a.formatPlaceholder,C=y.length+1,S=u(),F={type:o};function A(e,t){if(b&&_&&_.index===t){var r=f(e);e=void 0===_.format?f(r):l(r,de(_.format)),e=l(e,E)}return e}o?(l(l(S,{type:o}),""),r=t=[F]):l(S,"");for(var P=function(e){var a=x.charAt(e),i=y[e];o&&(i=a===w?t=(i||[]).reduce(function(e,t){return a===w&&-1!==c.indexOf(t.type)&&(e.push(t),e.push(F)),e},[F]):Object(O.a)(t).concat(Object(O.a)(i||[])));var u=s(S);if(n===w){for(var b=u;!d(b);)b=s(b);g&&T===e&&g(S,b),h&&j===e&&h(S,b)}if(i&&i.forEach(function(e,t){if(!u||!r||e!==r[t]||a===w&&i.length-1===t){var n=f(u),o=l(n,de(e));d(u)&&0===p(u).length&&m(u),u=l(e.object?n:o,"")}else u=s(u)}),a===w)return r=i,n=a,"continue";u=A(u,0),0===e&&(g&&0===T&&g(S,u),h&&0===j&&h(S,u)),a!==N&&("\n"===a?(u=l(f(u),{type:"br",object:!0}),u=l(f(u),"")):d(u)?v(u,a):u=l(f(u),a)),u=A(u,e+1),g&&T===e+1&&g(S,u),h&&j===e+1&&h(S,u),r=i,n=a},D=0;D<C;D++)P(D);return S}var me=window.Node,ve=me.TEXT_NODE,ge=me.ELEMENT_NODE;function he(e,t,r){for(var n=e.parentNode,a=0;e=e.previousSibling;)a++;return r=[a].concat(Object(O.a)(r)),n!==t&&(r=he(n,t,r)),r}function be(e,t){for(t=Object(O.a)(t);e&&t.length>1;)e=e.childNodes[t.shift()];return{node:e,offset:t[0]}}var ye=function(){return S(document,"")};function xe(e,t){"string"==typeof t&&(t=e.ownerDocument.createTextNode(t));var r=t,n=r.type,a=r.attributes;if(n)for(var o in t=e.ownerDocument.createElement(n),a)t.setAttribute(o,a[o]);return e.appendChild(t)}function Te(e,t){e.appendData(t)}function Oe(e){return e.lastChild}function je(e){return e.parentNode}function we(e){return e.nodeType===ve}function Ne(e){return e.nodeValue}function Ee(e){return e.parentNode.removeChild(e)}function _e(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;return e.reduce(function(e,r){return r(e,t.text)},t.formats)}function Ce(e){var t=e.value,r=e.multilineTag,n=e.multilineWrapperTags,a=e.createLinePadding,o=e.prepareEditableTree,c=[],u=[],l=pe({value:Object(i.a)({},t,{formats:_e(o,t)}),multilineTag:r,multilineWrapperTags:n,createEmpty:ye,append:xe,getLastChild:Oe,getParent:je,isText:we,getText:Ne,remove:Ee,appendText:Te,onStartIndex:function(e,t){c=he(t,e,[t.nodeValue.length])},onEndIndex:function(e,t){u=he(t,e,[t.nodeValue.length])},isEditableTree:!0});return a&&function e(t){for(var r=t.element,n=t.createLinePadding,a=t.multilineWrapperTags,o=r.childNodes.length,i=r.ownerDocument,c=0;c<o;c++){var u=r.childNodes[c];u.nodeType===ve?1!==o||u.nodeValue||r.appendChild(n(i)):(a&&!u.previousSibling&&-1!==a.indexOf(u.nodeName.toLowerCase())&&r.insertBefore(n(i),u),e({element:u,createLinePadding:n,multilineWrapperTags:a}))}}({element:l,createLinePadding:a,multilineWrapperTags:n}),{body:l,selection:{startPath:c,endPath:u}}}function Se(e){var t=e.value,r=e.current,n=Ce({value:t,multilineTag:e.multilineTag,multilineWrapperTags:e.multilineWrapperTags,createLinePadding:e.createLinePadding,prepareEditableTree:e.prepareEditableTree}),a=n.body,o=n.selection;!function(e,t){var r,n=0;for(;r=e.firstChild;){var a=t.childNodes[n];a?a.isEqualNode(r)?e.removeChild(r):t.replaceChild(r,a):t.appendChild(r),n++}for(;t.childNodes[n];)t.removeChild(t.childNodes[n])}(a,r),void 0!==t.start&&function(e,t){var r=be(t,e.startPath),n=r.node,a=r.offset,o=be(t,e.endPath),i=o.node,c=o.offset,u=window.getSelection(),l=t.ownerDocument.createRange(),s=n===i&&a===c;s&&0===a&&n.previousSibling&&n.previousSibling.nodeType===ge&&"BR"!==n.previousSibling.nodeName?(n.insertData(0,"\ufeff"),l.setStart(n,1),l.setEnd(i,1)):s&&0===a&&n===ve&&0===n.nodeValue.length?(n.insertData(0,"\ufeff"),l.setStart(n,1),l.setEnd(i,1)):(l.setStart(n,a),l.setEnd(i,c));if(u.rangeCount>0){if(f=l,d=u.getRangeAt(0),f.startContainer===d.startContainer&&f.startOffset===d.startOffset&&f.endContainer===d.endContainer&&f.endOffset===d.endOffset)return;u.removeAllRanges()}var f,d;u.addRange(l)}(o,r)}var Fe=r(61);function Ae(e){return Re(pe({value:e.value,multilineTag:e.multilineTag,multilineWrapperTags:e.multilineWrapperTags,createEmpty:Pe,append:We,getLastChild:De,getParent:Ie,isText:Me,getText:ke,remove:Ve,appendText:Le}).children)}function Pe(){return{}}function De(e){var t=e.children;return t&&t[t.length-1]}function We(e,t){return"string"==typeof t&&(t={text:t}),t.parent=e,e.children=e.children||[],e.children.push(t),t}function Le(e,t){e.text+=t}function Ie(e){return e.parent}function Me(e){return"string"==typeof e.text}function ke(e){return e.text}function Ve(e){var t=e.parent.children.indexOf(e);return-1!==t&&e.parent.children.splice(t,1),e}function Re(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map(function(e){return void 0===e.text?function(e){var t=e.type,r=e.attributes,n=e.object,a=e.children,o="";for(var i in r)Object(Fe.isValidAttributeName)(i)&&(o+=" ".concat(i,'="').concat(Object(Fe.escapeAttribute)(r[i]),'"'));return n?"<".concat(t).concat(o,">"):"<".concat(t).concat(o,">").concat(Re(a),"</").concat(t,">")}(e):Object(Fe.escapeHTML)(e.text)}).join("")}function ze(e,t){return z(e,t.type)?re(e,t.type):b(e,t)}function Be(e){var t=Object(o.select)("core/rich-text").getFormatType(e);if(t)return t.__experimentalCreatePrepareEditableTree&&t.__experimentalGetPropsForEditableTreePreparation&&Object(J.removeFilter)("experimentalRichText",e),Object(o.dispatch)("core/rich-text").removeFormatTypes(e),t;window.console.error("Format ".concat(e," is not registered."))}function He(e){for(var t=e.start,r=e.text,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t;n--;)if(r[n]===w)return n}function Ge(e,t){var r=He(e);if(void 0===r)return e;var n=e.text,a=e.formats,o=e.start,i=e.end,c=He(e,r),u=a[r]||[],l=a[c]||[];if(u.length>l.length)return e;for(var s=a.slice(),f=function(e,t){for(var r=e.text,n=e.formats,a=n[t]||[],o=t;o-- >=0;)if(r[o]===w){var i=n[o]||[];if(i.length===a.length+1)return o;if(i.length<=a.length)return}}(e,r),d=r;d<i;d++)if(n[d]===w)if(f){var p=a[f]||[];s[d]=p.concat((s[d]||[]).slice(p.length-1))}else{var m=a[c]||[],v=m[m.length-1]||t;s[d]=m.concat([v],(s[d]||[]).slice(m.length))}return h({text:n,formats:s,start:o,end:i})}function Ye(e,t){for(var r=e.text,n=e.formats,a=n[t]||[],o=t;o-- >=0;){if(r[o]===w)if((n[o]||[]).length===a.length-1)return o}}function qe(e){var t=e.text,r=e.formats,n=e.start,a=e.end,o=He(e,n);if(void 0===r[o])return e;for(var i=r.slice(0),c=r[Ye(e,o)]||[],u=function(e,t){for(var r=e.text,n=e.formats,a=n[t]||[],o=t,i=t||0;i<r.length;i++)if(r[i]===w){if(!((n[i]||[]).length>=a.length))return o;o=i}return o}(e,He(e,a)),l=o;l<=u;l++)if(t[l]===w){var s=i[l]||[];i[l]=c.concat(s.slice(c.length+1)),0===i[l].length&&delete i[l]}return h({text:t,formats:i,start:n,end:a})}function $e(e,t){for(var r,n=e.text,a=e.formats,o=e.start,i=e.end,c=He(e,o),u=a[c]||[],l=a[He(e,i)]||[],s=Ye(e,c),f=a.slice(0),d=u.length-1,p=l.length-1,m=s+1||0;m<n.length;m++)if(n[m]===w){if((f[m]||[]).length<=d)break;f[m]&&(r=!0,f[m]=f[m].map(function(e,r){return r<d||r>p?e:t}))}return r?h({text:n,formats:f,start:o,end:i}):e}r.d(t,"applyFormat",function(){return b}),r.d(t,"charAt",function(){return x}),r.d(t,"concat",function(){return T}),r.d(t,"create",function(){return L}),r.d(t,"getActiveFormat",function(){return z}),r.d(t,"getSelectionEnd",function(){return B}),r.d(t,"getSelectionStart",function(){return H}),r.d(t,"getTextContent",function(){return G}),r.d(t,"isCollapsed",function(){return Y}),r.d(t,"isEmpty",function(){return _}),r.d(t,"isEmptyLine",function(){return C}),r.d(t,"join",function(){return q}),r.d(t,"registerFormatType",function(){return te}),r.d(t,"removeFormat",function(){return re}),r.d(t,"remove",function(){return oe}),r.d(t,"replace",function(){return ie}),r.d(t,"insert",function(){return ae}),r.d(t,"insertLineSeparator",function(){return ce}),r.d(t,"insertObject",function(){return le}),r.d(t,"slice",function(){return se}),r.d(t,"split",function(){return fe}),r.d(t,"apply",function(){return Se}),r.d(t,"unstableToDom",function(){return Ce}),r.d(t,"toHTMLString",function(){return Ae}),r.d(t,"toggleFormat",function(){return ze}),r.d(t,"LINE_SEPARATOR",function(){return w}),r.d(t,"unregisterFormatType",function(){return Be}),r.d(t,"indentListItems",function(){return Ge}),r.d(t,"outdentListItems",function(){return qe}),r.d(t,"changeListType",function(){return $e})},31:function(e,t,r){"use strict";var n,a;function o(e){return[e]}function i(){var e={clear:function(){e.head=null}};return e}function c(e,t,r){var n;if(e.length!==t.length)return!1;for(n=r;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}n={},a="undefined"!=typeof WeakMap,t.a=function(e,t){var r,u;function l(){r=a?new WeakMap:i()}function s(){var r,n,a,o,i,l=arguments.length;for(o=new Array(l),a=0;a<l;a++)o[a]=arguments[a];for(i=t.apply(null,o),(r=u(i)).isUniqueByDependants||(r.lastDependants&&!c(i,r.lastDependants,0)&&r.clear(),r.lastDependants=i),n=r.head;n;){if(c(n.args,o,1))return n!==r.head&&(n.prev.next=n.next,n.next&&(n.next.prev=n.prev),n.next=r.head,n.prev=null,r.head.prev=n,r.head=n),n.val;n=n.next}return n={val:e.apply(null,o)},o[0]=null,n.args=o,r.head&&(r.head.prev=n,n.next=r.head),r.head=n,n.val}return t||(t=o),u=a?function(e){var t,a,o,c,u,l=r,s=!0;for(t=0;t<e.length;t++){if(a=e[t],!(u=a)||"object"!=typeof u){s=!1;break}l.has(a)?l=l.get(a):(o=new WeakMap,l.set(a,o),l=o)}return l.has(n)||((c=i()).isUniqueByDependants=s,l.set(n,c)),l.get(n)}:function(){return r},s.getDependants=t,s.clear=l,l(),s}},33:function(e,t,r){"use strict";function n(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}r.d(t,"a",function(){return n})},41:function(e,t,r){e.exports=function(e,t){var r,n,a,o=0;function i(){var t,i,c=n,u=arguments.length;e:for(;c;){if(c.args.length===arguments.length){for(i=0;i<u;i++)if(c.args[i]!==arguments[i]){c=c.next;continue e}return c!==n&&(c===a&&(a=c.prev),c.prev.next=c.next,c.next&&(c.next.prev=c.prev),c.next=n,c.prev=null,n.prev=c,n=c),c.val}c=c.next}for(t=new Array(u),i=0;i<u;i++)t[i]=arguments[i];return c={args:t,val:e.apply(null,t)},n?(n.prev=c,c.next=n):a=c,o===r?(a=a.prev).next=null:o++,n=c,c.val}return t&&t.maxSize&&(r=t.maxSize),i.clear=function(){n=null,a=null,o=0},i}},5:function(e,t){!function(){e.exports=this.wp.data}()},61:function(e,t){!function(){e.exports=this.wp.escapeHtml}()},7:function(e,t){!function(){e.exports=this.wp.compose}()},8:function(e,t,r){"use strict";r.d(t,"a",function(){return a});var n=r(15);function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),a.forEach(function(t){Object(n.a)(e,t,r[t])})}return e}}});