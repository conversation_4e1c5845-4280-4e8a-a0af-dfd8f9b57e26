this.wp=this.wp||{},this.wp.blocks=function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=308)}({0:function(e,t){!function(){e.exports=this.wp.element}()},1:function(e,t){!function(){e.exports=this.wp.i18n}()},10:function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}r.d(t,"a",function(){return n})},123:function(e,t){!function(){e.exports=this.wp.shortcode}()},15:function(e,t,r){"use strict";function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,"a",function(){return n})},18:function(e,t,r){"use strict";function n(){return(n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}r.d(t,"a",function(){return n})},183:function(e,t){!function(){e.exports=this.wp.blockSerializationDefaultParser}()},184:function(e,t,r){var n;/*! showdown v 1.9.0 - 10-11-2018 */
(function(){function a(e){"use strict";var t={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(t));var r={};for(var n in t)t.hasOwnProperty(n)&&(r[n]=t[n].defaultValue);return r}var i={},o={},s={},c=a(!0),l="vanilla",u={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:a(!0),allOn:function(){"use strict";var e=a(!0),t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=!0);return t}()};function d(e,t){"use strict";var r=t?"Error in "+t+" extension->":"Error in unnamed extension",n={valid:!0,error:""};i.helper.isArray(e)||(e=[e]);for(var a=0;a<e.length;++a){var o=r+" sub-extension "+a+": ",s=e[a];if("object"!=typeof s)return n.valid=!1,n.error=o+"must be an object, but "+typeof s+" given",n;if(!i.helper.isString(s.type))return n.valid=!1,n.error=o+'property "type" must be a string, but '+typeof s.type+" given",n;var c=s.type=s.type.toLowerCase();if("language"===c&&(c=s.type="lang"),"html"===c&&(c=s.type="output"),"lang"!==c&&"output"!==c&&"listener"!==c)return n.valid=!1,n.error=o+"type "+c+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',n;if("listener"===c){if(i.helper.isUndefined(s.listeners))return n.valid=!1,n.error=o+'. Extensions of type "listener" must have a property called "listeners"',n}else if(i.helper.isUndefined(s.filter)&&i.helper.isUndefined(s.regex))return n.valid=!1,n.error=o+c+' extensions must define either a "regex" property or a "filter" method',n;if(s.listeners){if("object"!=typeof s.listeners)return n.valid=!1,n.error=o+'"listeners" property must be an object but '+typeof s.listeners+" given",n;for(var l in s.listeners)if(s.listeners.hasOwnProperty(l)&&"function"!=typeof s.listeners[l])return n.valid=!1,n.error=o+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+l+" must be a function but "+typeof s.listeners[l]+" given",n}if(s.filter){if("function"!=typeof s.filter)return n.valid=!1,n.error=o+'"filter" must be a function, but '+typeof s.filter+" given",n}else if(s.regex){if(i.helper.isString(s.regex)&&(s.regex=new RegExp(s.regex,"g")),!(s.regex instanceof RegExp))return n.valid=!1,n.error=o+'"regex" property must either be a string or a RegExp object, but '+typeof s.regex+" given",n;if(i.helper.isUndefined(s.replace))return n.valid=!1,n.error=o+'"regex" extensions must implement a replace string or function',n}}return n}function f(e,t){"use strict";return"¨E"+t.charCodeAt(0)+"E"}i.helper={},i.extensions={},i.setOption=function(e,t){"use strict";return c[e]=t,this},i.getOption=function(e){"use strict";return c[e]},i.getOptions=function(){"use strict";return c},i.resetOptions=function(){"use strict";c=a(!0)},i.setFlavor=function(e){"use strict";if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");i.resetOptions();var t=u[e];for(var r in l=e,t)t.hasOwnProperty(r)&&(c[r]=t[r])},i.getFlavor=function(){"use strict";return l},i.getFlavorOptions=function(e){"use strict";if(u.hasOwnProperty(e))return u[e]},i.getDefaultOptions=function(e){"use strict";return a(e)},i.subParser=function(e,t){"use strict";if(i.helper.isString(e)){if(void 0===t){if(o.hasOwnProperty(e))return o[e];throw Error("SubParser named "+e+" not registered!")}o[e]=t}},i.extension=function(e,t){"use strict";if(!i.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=i.helper.stdExtName(e),i.helper.isUndefined(t)){if(!s.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return s[e]}"function"==typeof t&&(t=t()),i.helper.isArray(t)||(t=[t]);var r=d(t,e);if(!r.valid)throw Error(r.error);s[e]=t},i.getAllExtensions=function(){"use strict";return s},i.removeExtension=function(e){"use strict";delete s[e]},i.resetExtensions=function(){"use strict";s={}},i.validateExtension=function(e){"use strict";var t=d(e,null);return!!t.valid||(console.warn(t.error),!1)},i.hasOwnProperty("helper")||(i.helper={}),i.helper.isString=function(e){"use strict";return"string"==typeof e||e instanceof String},i.helper.isFunction=function(e){"use strict";return e&&"[object Function]"==={}.toString.call(e)},i.helper.isArray=function(e){"use strict";return Array.isArray(e)},i.helper.isUndefined=function(e){"use strict";return void 0===e},i.helper.forEach=function(e,t){"use strict";if(i.helper.isUndefined(e))throw new Error("obj param is required");if(i.helper.isUndefined(t))throw new Error("callback param is required");if(!i.helper.isFunction(t))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(t);else if(i.helper.isArray(e))for(var r=0;r<e.length;r++)t(e[r],r,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var n in e)e.hasOwnProperty(n)&&t(e[n],n,e)}},i.helper.stdExtName=function(e){"use strict";return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},i.helper.escapeCharactersCallback=f,i.helper.escapeCharacters=function(e,t,r){"use strict";var n="(["+t.replace(/([\[\]\\])/g,"\\$1")+"])";r&&(n="\\\\"+n);var a=new RegExp(n,"g");return e=e.replace(a,f)},i.helper.unescapeHTMLEntities=function(e){"use strict";return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var h=function(e,t,r,n){"use strict";var a,i,o,s,c,l=n||"",u=l.indexOf("g")>-1,d=new RegExp(t+"|"+r,"g"+l.replace(/g/g,"")),f=new RegExp(t,l.replace(/g/g,"")),h=[];do{for(a=0;o=d.exec(e);)if(f.test(o[0]))a++||(s=(i=d.lastIndex)-o[0].length);else if(a&&!--a){c=o.index+o[0].length;var p={left:{start:s,end:i},match:{start:i,end:o.index},right:{start:o.index,end:c},wholeMatch:{start:s,end:c}};if(h.push(p),!u)return h}}while(a&&(d.lastIndex=i));return h};i.helper.matchRecursiveRegExp=function(e,t,r,n){"use strict";for(var a=h(e,t,r,n),i=[],o=0;o<a.length;++o)i.push([e.slice(a[o].wholeMatch.start,a[o].wholeMatch.end),e.slice(a[o].match.start,a[o].match.end),e.slice(a[o].left.start,a[o].left.end),e.slice(a[o].right.start,a[o].right.end)]);return i},i.helper.replaceRecursiveRegExp=function(e,t,r,n,a){"use strict";if(!i.helper.isFunction(t)){var o=t;t=function(){return o}}var s=h(e,r,n,a),c=e,l=s.length;if(l>0){var u=[];0!==s[0].wholeMatch.start&&u.push(e.slice(0,s[0].wholeMatch.start));for(var d=0;d<l;++d)u.push(t(e.slice(s[d].wholeMatch.start,s[d].wholeMatch.end),e.slice(s[d].match.start,s[d].match.end),e.slice(s[d].left.start,s[d].left.end),e.slice(s[d].right.start,s[d].right.end))),d<l-1&&u.push(e.slice(s[d].wholeMatch.end,s[d+1].wholeMatch.start));s[l-1].wholeMatch.end<e.length&&u.push(e.slice(s[l-1].wholeMatch.end)),c=u.join("")}return c},i.helper.regexIndexOf=function(e,t,r){"use strict";if(!i.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(t instanceof RegExp==!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var n=e.substring(r||0).search(t);return n>=0?n+(r||0):n},i.helper.splitAtIndex=function(e,t){"use strict";if(!i.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,t),e.substring(t)]},i.helper.encodeEmailAddress=function(e){"use strict";var t=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,function(e){if("@"===e)e=t[Math.floor(2*Math.random())](e);else{var r=Math.random();e=r>.9?t[2](e):r>.45?t[1](e):t[0](e)}return e})},i.helper.padEnd=function(e,t,r){"use strict";return t>>=0,r=String(r||" "),e.length>t?String(e):((t-=e.length)>r.length&&(r+=r.repeat(t/r.length)),String(e)+r.slice(0,t))},"undefined"==typeof console&&(console={warn:function(e){"use strict";alert(e)},log:function(e){"use strict";alert(e)},error:function(e){"use strict";throw e}}),i.helper.regexes={asteriskDashAndColon:/([*_:~])/g},i.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},i.Converter=function(e){"use strict";var t={},r=[],n=[],a={},o=l,f={parsed:{},raw:"",format:""};function h(e,t){if(t=t||null,i.helper.isString(e)){if(t=e=i.helper.stdExtName(e),i.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(e,t){"function"==typeof e&&(e=e(new i.Converter));i.helper.isArray(e)||(e=[e]);var a=d(e,t);if(!a.valid)throw Error(a.error);for(var o=0;o<e.length;++o)switch(e[o].type){case"lang":r.push(e[o]);break;case"output":n.push(e[o]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(i.extensions[e],e);if(i.helper.isUndefined(s[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=s[e]}"function"==typeof e&&(e=e()),i.helper.isArray(e)||(e=[e]);var a=d(e,t);if(!a.valid)throw Error(a.error);for(var o=0;o<e.length;++o){switch(e[o].type){case"lang":r.push(e[o]);break;case"output":n.push(e[o])}if(e[o].hasOwnProperty("listeners"))for(var c in e[o].listeners)e[o].listeners.hasOwnProperty(c)&&p(c,e[o].listeners[c])}}function p(e,t){if(!i.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof t)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof t+" given");a.hasOwnProperty(e)||(a[e]=[]),a[e].push(t)}!function(){for(var r in e=e||{},c)c.hasOwnProperty(r)&&(t[r]=c[r]);if("object"!=typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);t.extensions&&i.helper.forEach(t.extensions,h)}(),this._dispatch=function(e,t,r,n){if(a.hasOwnProperty(e))for(var i=0;i<a[e].length;++i){var o=a[e][i](e,t,this,r,n);o&&void 0!==o&&(t=o)}return t},this.listen=function(e,t){return p(e,t),this},this.makeHtml=function(e){if(!e)return e;var a={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:r,outputModifiers:n,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return e=(e=(e=(e=(e=e.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),t.smartIndentationFix&&(e=function(e){var t=e.match(/^\s*/)[0].length,r=new RegExp("^\\s{0,"+t+"}","gm");return e.replace(r,"")}(e)),e="\n\n"+e+"\n\n",e=(e=i.subParser("detab")(e,t,a)).replace(/^[ \t]+$/gm,""),i.helper.forEach(r,function(r){e=i.subParser("runExtension")(r,e,t,a)}),e=i.subParser("metadata")(e,t,a),e=i.subParser("hashPreCodeTags")(e,t,a),e=i.subParser("githubCodeBlocks")(e,t,a),e=i.subParser("hashHTMLBlocks")(e,t,a),e=i.subParser("hashCodeTags")(e,t,a),e=i.subParser("stripLinkDefinitions")(e,t,a),e=i.subParser("blockGamut")(e,t,a),e=i.subParser("unhashHTMLSpans")(e,t,a),e=(e=(e=i.subParser("unescapeSpecialChars")(e,t,a)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),e=i.subParser("completeHTMLDocument")(e,t,a),i.helper.forEach(n,function(r){e=i.subParser("runExtension")(r,e,t,a)}),f=a.metadata,e},this.makeMarkdown=this.makeMd=function(e,t){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!t){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");t=window.document}var r=t.createElement("div");r.innerHTML=e;var n={preList:function(e){for(var t=e.querySelectorAll("pre"),r=[],n=0;n<t.length;++n)if(1===t[n].childElementCount&&"code"===t[n].firstChild.tagName.toLowerCase()){var a=t[n].firstChild.innerHTML.trim(),o=t[n].firstChild.getAttribute("data-language")||"";if(""===o)for(var s=t[n].firstChild.className.split(" "),c=0;c<s.length;++c){var l=s[c].match(/^language-(.+)$/);if(null!==l){o=l[1];break}}a=i.helper.unescapeHTMLEntities(a),r.push(a),t[n].outerHTML='<precode language="'+o+'" precodenum="'+n.toString()+'"></precode>'}else r.push(t[n].innerHTML),t[n].innerHTML="",t[n].setAttribute("prenum",n.toString());return r}(r)};!function e(t){for(var r=0;r<t.childNodes.length;++r){var n=t.childNodes[r];3===n.nodeType?/\S/.test(n.nodeValue)?(n.nodeValue=n.nodeValue.split("\n").join(" "),n.nodeValue=n.nodeValue.replace(/(\s)+/g,"$1")):(t.removeChild(n),--r):1===n.nodeType&&e(n)}}(r);for(var a=r.childNodes,o="",s=0;s<a.length;s++)o+=i.subParser("makeMarkdown.node")(a[s],n);return o},this.setOption=function(e,r){t[e]=r},this.getOption=function(e){return t[e]},this.getOptions=function(){return t},this.addExtension=function(e,t){h(e,t=t||null)},this.useExtension=function(e){h(e)},this.setFlavor=function(e){if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");var r=u[e];for(var n in o=e,r)r.hasOwnProperty(n)&&(t[n]=r[n])},this.getFlavor=function(){return o},this.removeExtension=function(e){i.helper.isArray(e)||(e=[e]);for(var t=0;t<e.length;++t){for(var a=e[t],o=0;o<r.length;++o)r[o]===a&&r[o].splice(o,1);for(;0<n.length;++o)n[0]===a&&n[0].splice(o,1)}},this.getAllExtensions=function(){return{language:r,output:n}},this.getMetadata=function(e){return e?f.raw:f.parsed},this.getMetadataFormat=function(){return f.format},this._setMetadataPair=function(e,t){f.parsed[e]=t},this._setMetadataFormat=function(e){f.format=e},this._setMetadataRaw=function(e){f.raw=e}},i.subParser("anchors",function(e,t,r){"use strict";var n=function(e,n,a,o,s,c,l){if(i.helper.isUndefined(l)&&(l=""),a=a.toLowerCase(),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)o="";else if(!o){if(a||(a=n.toLowerCase().replace(/ ?\n/g," ")),o="#"+a,i.helper.isUndefined(r.gUrls[a]))return e;o=r.gUrls[a],i.helper.isUndefined(r.gTitles[a])||(l=r.gTitles[a])}var u='<a href="'+(o=o.replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'"';return""!==l&&null!==l&&(u+=' title="'+(l=(l=l.replace(/"/g,"&quot;")).replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'"'),t.openLinksInNewWindow&&!/^#/.test(o)&&(u+=' target="¨E95Eblank"'),u+=">"+n+"</a>"};return e=(e=(e=(e=(e=r.converter._dispatch("anchors.before",e,t,r)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[([^\[\]]+)]()()()()()/g,n),t.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,function(e,r,n,a,o){if("\\"===n)return r+a;if(!i.helper.isString(t.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var s=t.ghMentionsLink.replace(/\{u}/g,o),c="";return t.openLinksInNewWindow&&(c=' target="¨E95Eblank"'),r+'<a href="'+s+'"'+c+">"+a+"</a>"})),e=r.converter._dispatch("anchors.after",e,t,r)});var p=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,g=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,m=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,b=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-\/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,_=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,k=function(e){"use strict";return function(t,r,n,a,o,s,c){var l=n=n.replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback),u="",d="",f=r||"",h=c||"";return/^www\./i.test(n)&&(n=n.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&s&&(u=s),e.openLinksInNewWindow&&(d=' target="¨E95Eblank"'),f+'<a href="'+n+'"'+d+">"+l+"</a>"+u+h}},w=function(e,t){"use strict";return function(r,n,a){var o="mailto:";return n=n||"",a=i.subParser("unescapeSpecialChars")(a,e,t),e.encodeEmails?(o=i.helper.encodeEmailAddress(o+a),a=i.helper.encodeEmailAddress(a)):o+=a,n+'<a href="'+o+'">'+a+"</a>"}};i.subParser("autoLinks",function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("autoLinks.before",e,t,r)).replace(m,k(t))).replace(_,w(t,r)),e=r.converter._dispatch("autoLinks.after",e,t,r)}),i.subParser("simplifiedAutoLinks",function(e,t,r){"use strict";return t.simplifiedAutoLink?(e=r.converter._dispatch("simplifiedAutoLinks.before",e,t,r),e=(e=t.excludeTrailingPunctuationFromURLs?e.replace(g,k(t)):e.replace(p,k(t))).replace(b,w(t,r)),e=r.converter._dispatch("simplifiedAutoLinks.after",e,t,r)):e}),i.subParser("blockGamut",function(e,t,r){"use strict";return e=r.converter._dispatch("blockGamut.before",e,t,r),e=i.subParser("blockQuotes")(e,t,r),e=i.subParser("headers")(e,t,r),e=i.subParser("horizontalRule")(e,t,r),e=i.subParser("lists")(e,t,r),e=i.subParser("codeBlocks")(e,t,r),e=i.subParser("tables")(e,t,r),e=i.subParser("hashHTMLBlocks")(e,t,r),e=i.subParser("paragraphs")(e,t,r),e=r.converter._dispatch("blockGamut.after",e,t,r)}),i.subParser("blockQuotes",function(e,t,r){"use strict";e=r.converter._dispatch("blockQuotes.before",e,t,r),e+="\n\n";var n=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return t.splitAdjacentBlockquotes&&(n=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=e.replace(n,function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=i.subParser("githubCodeBlocks")(e,t,r),e=(e=(e=i.subParser("blockGamut")(e,t,r)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,function(e,t){var r=t;return r=(r=r.replace(/^  /gm,"¨0")).replace(/¨0/g,"")}),i.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",t,r)}),e=r.converter._dispatch("blockQuotes.after",e,t,r)}),i.subParser("codeBlocks",function(e,t,r){"use strict";e=r.converter._dispatch("codeBlocks.before",e,t,r);return e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,function(e,n,a){var o=n,s=a,c="\n";return o=i.subParser("outdent")(o,t,r),o=i.subParser("encodeCode")(o,t,r),o=(o=(o=i.subParser("detab")(o,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""),t.omitExtraWLInCodeBlocks&&(c=""),o="<pre><code>"+o+c+"</code></pre>",i.subParser("hashBlock")(o,t,r)+s})).replace(/¨0/,""),e=r.converter._dispatch("codeBlocks.after",e,t,r)}),i.subParser("codeSpans",function(e,t,r){"use strict";return void 0===(e=r.converter._dispatch("codeSpans.before",e,t,r))&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,function(e,n,a,o){var s=o;return s=(s=s.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),s=n+"<code>"+(s=i.subParser("encodeCode")(s,t,r))+"</code>",s=i.subParser("hashHTMLSpans")(s,t,r)}),e=r.converter._dispatch("codeSpans.after",e,t,r)}),i.subParser("completeHTMLDocument",function(e,t,r){"use strict";if(!t.completeHTMLDocument)return e;e=r.converter._dispatch("completeHTMLDocument.before",e,t,r);var n="html",a="<!DOCTYPE HTML>\n",i="",o='<meta charset="utf-8">\n',s="",c="";for(var l in void 0!==r.metadata.parsed.doctype&&(a="<!DOCTYPE "+r.metadata.parsed.doctype+">\n","html"!==(n=r.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==n||(o='<meta charset="utf-8">')),r.metadata.parsed)if(r.metadata.parsed.hasOwnProperty(l))switch(l.toLowerCase()){case"doctype":break;case"title":i="<title>"+r.metadata.parsed.title+"</title>\n";break;case"charset":o="html"===n||"html5"===n?'<meta charset="'+r.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+r.metadata.parsed.charset+'">\n';break;case"language":case"lang":s=' lang="'+r.metadata.parsed[l]+'"',c+='<meta name="'+l+'" content="'+r.metadata.parsed[l]+'">\n';break;default:c+='<meta name="'+l+'" content="'+r.metadata.parsed[l]+'">\n'}return e=a+"<html"+s+">\n<head>\n"+i+o+c+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",e=r.converter._dispatch("completeHTMLDocument.after",e,t,r)}),i.subParser("detab",function(e,t,r){"use strict";return e=(e=(e=(e=(e=(e=r.converter._dispatch("detab.before",e,t,r)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,function(e,t){for(var r=t,n=4-r.length%4,a=0;a<n;a++)r+=" ";return r})).replace(/¨A/g,"    ")).replace(/¨B/g,""),e=r.converter._dispatch("detab.after",e,t,r)}),i.subParser("ellipsis",function(e,t,r){"use strict";return e=(e=r.converter._dispatch("ellipsis.before",e,t,r)).replace(/\.\.\./g,"…"),e=r.converter._dispatch("ellipsis.after",e,t,r)}),i.subParser("emoji",function(e,t,r){"use strict";if(!t.emoji)return e;return e=(e=r.converter._dispatch("emoji.before",e,t,r)).replace(/:([\S]+?):/g,function(e,t){return i.helper.emojis.hasOwnProperty(t)?i.helper.emojis[t]:e}),e=r.converter._dispatch("emoji.after",e,t,r)}),i.subParser("encodeAmpsAndAngles",function(e,t,r){"use strict";return e=(e=(e=(e=(e=r.converter._dispatch("encodeAmpsAndAngles.before",e,t,r)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),e=r.converter._dispatch("encodeAmpsAndAngles.after",e,t,r)}),i.subParser("encodeBackslashEscapes",function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("encodeBackslashEscapes.before",e,t,r)).replace(/\\(\\)/g,i.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|-])/g,i.helper.escapeCharactersCallback),e=r.converter._dispatch("encodeBackslashEscapes.after",e,t,r)}),i.subParser("encodeCode",function(e,t,r){"use strict";return e=(e=r.converter._dispatch("encodeCode.before",e,t,r)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,i.helper.escapeCharactersCallback),e=r.converter._dispatch("encodeCode.after",e,t,r)}),i.subParser("escapeSpecialCharsWithinTagAttributes",function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,t,r)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,i.helper.escapeCharactersCallback)})).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,function(e){return e.replace(/([\\`*_~=|])/g,i.helper.escapeCharactersCallback)}),e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,t,r)}),i.subParser("githubCodeBlocks",function(e,t,r){"use strict";return t.ghCodeBlocks?(e=r.converter._dispatch("githubCodeBlocks.before",e,t,r),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,function(e,n,a,o){var s=t.omitExtraWLInCodeBlocks?"":"\n";return o=i.subParser("encodeCode")(o,t,r),o="<pre><code"+(a?' class="'+a+" language-"+a+'"':"")+">"+(o=(o=(o=i.subParser("detab")(o,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+s+"</code></pre>",o=i.subParser("hashBlock")(o,t,r),"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:o})-1)+"G\n\n"})).replace(/¨0/,""),r.converter._dispatch("githubCodeBlocks.after",e,t,r)):e}),i.subParser("hashBlock",function(e,t,r){"use strict";return e=(e=r.converter._dispatch("hashBlock.before",e,t,r)).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n",e=r.converter._dispatch("hashBlock.after",e,t,r)}),i.subParser("hashCodeTags",function(e,t,r){"use strict";e=r.converter._dispatch("hashCodeTags.before",e,t,r);return e=i.helper.replaceRecursiveRegExp(e,function(e,n,a,o){var s=a+i.subParser("encodeCode")(n,t,r)+o;return"¨C"+(r.gHtmlSpans.push(s)-1)+"C"},"<code\\b[^>]*>","</code>","gim"),e=r.converter._dispatch("hashCodeTags.after",e,t,r)}),i.subParser("hashElement",function(e,t,r){"use strict";return function(e,t){var n=t;return n=(n=(n=n.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),n="\n\n¨K"+(r.gHtmlBlocks.push(n)-1)+"K\n\n"}}),i.subParser("hashHTMLBlocks",function(e,t,r){"use strict";e=r.converter._dispatch("hashHTMLBlocks.before",e,t,r);var n=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],a=function(e,t,n,a){var i=e;return-1!==n.search(/\bmarkdown\b/)&&(i=n+r.converter.makeHtml(t)+a),"\n\n¨K"+(r.gHtmlBlocks.push(i)-1)+"K\n\n"};t.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,function(e,t){return"&lt;"+t+"&gt;"}));for(var o=0;o<n.length;++o)for(var s,c=new RegExp("^ {0,3}(<"+n[o]+"\\b[^>]*>)","im"),l="<"+n[o]+"\\b[^>]*>",u="</"+n[o]+">";-1!==(s=i.helper.regexIndexOf(e,c));){var d=i.helper.splitAtIndex(e,s),f=i.helper.replaceRecursiveRegExp(d[1],a,l,u,"im");if(f===d[1])break;e=d[0].concat(f)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,i.subParser("hashElement")(e,t,r)),e=(e=i.helper.replaceRecursiveRegExp(e,function(e){return"\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n"},"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,i.subParser("hashElement")(e,t,r)),e=r.converter._dispatch("hashHTMLBlocks.after",e,t,r)}),i.subParser("hashHTMLSpans",function(e,t,r){"use strict";function n(e){return"¨C"+(r.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=r.converter._dispatch("hashHTMLSpans.before",e,t,r)).replace(/<[^>]+?\/>/gi,function(e){return n(e)})).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,function(e){return n(e)})).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,function(e){return n(e)})).replace(/<[^>]+?>/gi,function(e){return n(e)}),e=r.converter._dispatch("hashHTMLSpans.after",e,t,r)}),i.subParser("unhashHTMLSpans",function(e,t,r){"use strict";e=r.converter._dispatch("unhashHTMLSpans.before",e,t,r);for(var n=0;n<r.gHtmlSpans.length;++n){for(var a=r.gHtmlSpans[n],i=0;/¨C(\d+)C/.test(a);){var o=RegExp.$1;if(a=a.replace("¨C"+o+"C",r.gHtmlSpans[o]),10===i){console.error("maximum nesting of 10 spans reached!!!");break}++i}e=e.replace("¨C"+n+"C",a)}return e=r.converter._dispatch("unhashHTMLSpans.after",e,t,r)}),i.subParser("hashPreCodeTags",function(e,t,r){"use strict";e=r.converter._dispatch("hashPreCodeTags.before",e,t,r);return e=i.helper.replaceRecursiveRegExp(e,function(e,n,a,o){var s=a+i.subParser("encodeCode")(n,t,r)+o;return"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:s})-1)+"G\n\n"},"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=r.converter._dispatch("hashPreCodeTags.after",e,t,r)}),i.subParser("headers",function(e,t,r){"use strict";e=r.converter._dispatch("headers.before",e,t,r);var n=isNaN(parseInt(t.headerLevelStart))?1:parseInt(t.headerLevelStart),a=t.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,o=t.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=(e=e.replace(a,function(e,a){var o=i.subParser("spanGamut")(a,t,r),s=t.noHeaderId?"":' id="'+c(a)+'"',l="<h"+n+s+">"+o+"</h"+n+">";return i.subParser("hashBlock")(l,t,r)})).replace(o,function(e,a){var o=i.subParser("spanGamut")(a,t,r),s=t.noHeaderId?"":' id="'+c(a)+'"',l=n+1,u="<h"+l+s+">"+o+"</h"+l+">";return i.subParser("hashBlock")(u,t,r)});var s=t.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function c(e){var n,a;if(t.customizedHeaderId){var o=e.match(/\{([^{]+?)}\s*$/);o&&o[1]&&(e=o[1])}return n=e,a=i.helper.isString(t.prefixHeaderId)?t.prefixHeaderId:!0===t.prefixHeaderId?"section-":"",t.rawPrefixHeaderId||(n=a+n),n=t.ghCompatibleHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():t.rawHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():n.replace(/[^\w]/g,"").toLowerCase(),t.rawPrefixHeaderId&&(n=a+n),r.hashLinkCounts[n]?n=n+"-"+r.hashLinkCounts[n]++:r.hashLinkCounts[n]=1,n}return e=e.replace(s,function(e,a,o){var s=o;t.customizedHeaderId&&(s=o.replace(/\s?\{([^{]+?)}\s*$/,""));var l=i.subParser("spanGamut")(s,t,r),u=t.noHeaderId?"":' id="'+c(o)+'"',d=n-1+a.length,f="<h"+d+u+">"+l+"</h"+d+">";return i.subParser("hashBlock")(f,t,r)}),e=r.converter._dispatch("headers.after",e,t,r)}),i.subParser("horizontalRule",function(e,t,r){"use strict";e=r.converter._dispatch("horizontalRule.before",e,t,r);var n=i.subParser("hashBlock")("<hr />",t,r);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,n),e=r.converter._dispatch("horizontalRule.after",e,t,r)}),i.subParser("images",function(e,t,r){"use strict";function n(e,t,n,a,o,s,c,l){var u=r.gUrls,d=r.gTitles,f=r.gDimensions;if(n=n.toLowerCase(),l||(l=""),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)a="";else if(""===a||null===a){if(""!==n&&null!==n||(n=t.toLowerCase().replace(/ ?\n/g," ")),a="#"+n,i.helper.isUndefined(u[n]))return e;a=u[n],i.helper.isUndefined(d[n])||(l=d[n]),i.helper.isUndefined(f[n])||(o=f[n].width,s=f[n].height)}t=t.replace(/"/g,"&quot;").replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback);var h='<img src="'+(a=a.replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'" alt="'+t+'"';return l&&i.helper.isString(l)&&(h+=' title="'+(l=l.replace(/"/g,"&quot;").replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'"'),o&&s&&(h+=' width="'+(o="*"===o?"auto":o)+'"',h+=' height="'+(s="*"===s?"auto":s)+'"'),h+=" />"}return e=(e=(e=(e=(e=(e=r.converter._dispatch("images.before",e,t,r)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+\/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,function(e,t,r,a,i,o,s,c){return n(e,t,r,a=a.replace(/\s/g,""),i,o,0,c)})).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,n)).replace(/!\[([^\[\]]+)]()()()()()/g,n),e=r.converter._dispatch("images.after",e,t,r)}),i.subParser("italicsAndBold",function(e,t,r){"use strict";function n(e,t,r){return t+e+r}return e=r.converter._dispatch("italicsAndBold.before",e,t,r),e=t.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,function(e,t){return n(t,"<strong><em>","</em></strong>")})).replace(/\b__(\S[\s\S]*?)__\b/g,function(e,t){return n(t,"<strong>","</strong>")})).replace(/\b_(\S[\s\S]*?)_\b/g,function(e,t){return n(t,"<em>","</em>")}):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,function(e,t){return/\S$/.test(t)?n(t,"<strong><em>","</em></strong>"):e})).replace(/__(\S[\s\S]*?)__/g,function(e,t){return/\S$/.test(t)?n(t,"<strong>","</strong>"):e})).replace(/_([^\s_][\s\S]*?)_/g,function(e,t){return/\S$/.test(t)?n(t,"<em>","</em>"):e}),e=t.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,function(e,t,r){return n(r,t+"<strong><em>","</em></strong>")})).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,function(e,t,r){return n(r,t+"<strong>","</strong>")})).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,function(e,t,r){return n(r,t+"<em>","</em>")}):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,function(e,t){return/\S$/.test(t)?n(t,"<strong><em>","</em></strong>"):e})).replace(/\*\*(\S[\s\S]*?)\*\*/g,function(e,t){return/\S$/.test(t)?n(t,"<strong>","</strong>"):e})).replace(/\*([^\s*][\s\S]*?)\*/g,function(e,t){return/\S$/.test(t)?n(t,"<em>","</em>"):e}),e=r.converter._dispatch("italicsAndBold.after",e,t,r)}),i.subParser("lists",function(e,t,r){"use strict";function n(e,n){r.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,o=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return t.disableForced4SpacesIndentedSublists&&(a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(a,function(e,n,a,s,c,l,u){u=u&&""!==u.trim();var d=i.subParser("outdent")(c,t,r),f="";return l&&t.tasklists&&(f=' class="task-list-item" style="list-style-type: none;"',d=d.replace(/^[ \t]*\[(x|X| )?]/m,function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return u&&(e+=" checked"),e+=">"})),d=d.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,function(e){return"¨A"+e}),n||d.search(/\n{2,}/)>-1?(d=i.subParser("githubCodeBlocks")(d,t,r),d=i.subParser("blockGamut")(d,t,r)):(d=(d=i.subParser("lists")(d,t,r)).replace(/\n$/,""),d=(d=i.subParser("hashHTMLBlocks")(d,t,r)).replace(/\n\n+/g,"\n\n"),d=o?i.subParser("paragraphs")(d,t,r):i.subParser("spanGamut")(d,t,r)),d="<li"+f+">"+(d=d.replace("¨A",""))+"</li>\n"})).replace(/¨0/g,""),r.gListLevel--,n&&(e=e.replace(/\s+$/,"")),e}function a(e,t){if("ol"===t){var r=e.match(/^ *(\d+)\./);if(r&&"1"!==r[1])return' start="'+r[1]+'"'}return""}function o(e,r,i){var o=t.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,s=t.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,c="ul"===r?o:s,l="";if(-1!==e.search(c))!function t(u){var d=u.search(c),f=a(e,r);-1!==d?(l+="\n\n<"+r+f+">\n"+n(u.slice(0,d),!!i)+"</"+r+">\n",c="ul"===(r="ul"===r?"ol":"ul")?o:s,t(u.slice(d))):l+="\n\n<"+r+f+">\n"+n(u,!!i)+"</"+r+">\n"}(e);else{var u=a(e,r);l="\n\n<"+r+u+">\n"+n(e,!!i)+"</"+r+">\n"}return l}return e=r.converter._dispatch("lists.before",e,t,r),e+="¨0",e=(e=r.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(e,t,r){return o(t,r.search(/[*+-]/g)>-1?"ul":"ol",!0)}):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(e,t,r,n){return o(r,n.search(/[*+-]/g)>-1?"ul":"ol",!1)})).replace(/¨0/,""),e=r.converter._dispatch("lists.after",e,t,r)}),i.subParser("metadata",function(e,t,r){"use strict";if(!t.metadata)return e;function n(e){r.metadata.raw=e,(e=(e=e.replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,function(e,t,n){return r.metadata.parsed[t]=n,""})}return e=(e=(e=(e=r.converter._dispatch("metadata.before",e,t,r)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,function(e,t,r){return n(r),"¨M"})).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,function(e,t,a){return t&&(r.metadata.format=t),n(a),"¨M"})).replace(/¨M/g,""),e=r.converter._dispatch("metadata.after",e,t,r)}),i.subParser("outdent",function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("outdent.before",e,t,r)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),e=r.converter._dispatch("outdent.after",e,t,r)}),i.subParser("paragraphs",function(e,t,r){"use strict";for(var n=(e=(e=(e=r.converter._dispatch("paragraphs.before",e,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),a=[],o=n.length,s=0;s<o;s++){var c=n[s];c.search(/¨(K|G)(\d+)\1/g)>=0?a.push(c):c.search(/\S/)>=0&&(c=(c=i.subParser("spanGamut")(c,t,r)).replace(/^([ \t]*)/g,"<p>"),c+="</p>",a.push(c))}for(o=a.length,s=0;s<o;s++){for(var l="",u=a[s],d=!1;/¨(K|G)(\d+)\1/.test(u);){var f=RegExp.$1,h=RegExp.$2;l=(l="K"===f?r.gHtmlBlocks[h]:d?i.subParser("encodeCode")(r.ghCodeBlocks[h].text,t,r):r.ghCodeBlocks[h].codeblock).replace(/\$/g,"$$$$"),u=u.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,l),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(u)&&(d=!0)}a[s]=u}return e=(e=(e=a.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),r.converter._dispatch("paragraphs.after",e,t,r)}),i.subParser("runExtension",function(e,t,r,n){"use strict";if(e.filter)t=e.filter(t,n.converter,r);else if(e.regex){var a=e.regex;a instanceof RegExp||(a=new RegExp(a,"g")),t=t.replace(a,e.replace)}return t}),i.subParser("spanGamut",function(e,t,r){"use strict";return e=r.converter._dispatch("spanGamut.before",e,t,r),e=i.subParser("codeSpans")(e,t,r),e=i.subParser("escapeSpecialCharsWithinTagAttributes")(e,t,r),e=i.subParser("encodeBackslashEscapes")(e,t,r),e=i.subParser("images")(e,t,r),e=i.subParser("anchors")(e,t,r),e=i.subParser("autoLinks")(e,t,r),e=i.subParser("simplifiedAutoLinks")(e,t,r),e=i.subParser("emoji")(e,t,r),e=i.subParser("underline")(e,t,r),e=i.subParser("italicsAndBold")(e,t,r),e=i.subParser("strikethrough")(e,t,r),e=i.subParser("ellipsis")(e,t,r),e=i.subParser("hashHTMLSpans")(e,t,r),e=i.subParser("encodeAmpsAndAngles")(e,t,r),t.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),e=r.converter._dispatch("spanGamut.after",e,t,r)}),i.subParser("strikethrough",function(e,t,r){"use strict";return t.strikethrough&&(e=(e=r.converter._dispatch("strikethrough.before",e,t,r)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,function(e,n){return function(e){return t.simplifiedAutoLink&&(e=i.subParser("simplifiedAutoLinks")(e,t,r)),"<del>"+e+"</del>"}(n)}),e=r.converter._dispatch("strikethrough.after",e,t,r)),e}),i.subParser("stripLinkDefinitions",function(e,t,r){"use strict";var n=function(e,n,a,o,s,c,l){return n=n.toLowerCase(),a.match(/^data:.+?\/.+?;base64,/)?r.gUrls[n]=a.replace(/\s/g,""):r.gUrls[n]=i.subParser("encodeAmpsAndAngles")(a,t,r),c?c+l:(l&&(r.gTitles[n]=l.replace(/"|'/g,"&quot;")),t.parseImgDimensions&&o&&s&&(r.gDimensions[n]={width:o,height:s}),"")};return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+\/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,n)).replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,n)).replace(/¨0/,"")}),i.subParser("tables",function(e,t,r){"use strict";if(!t.tables)return e;function n(e,n){return"<td"+n+">"+i.subParser("spanGamut")(e,t,r)+"</td>\n"}function a(e){var a,o=e.split("\n");for(a=0;a<o.length;++a)/^ {0,3}\|/.test(o[a])&&(o[a]=o[a].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(o[a])&&(o[a]=o[a].replace(/\|[ \t]*$/,"")),o[a]=i.subParser("codeSpans")(o[a],t,r);var s,c,l,u,d=o[0].split("|").map(function(e){return e.trim()}),f=o[1].split("|").map(function(e){return e.trim()}),h=[],p=[],g=[],m=[];for(o.shift(),o.shift(),a=0;a<o.length;++a)""!==o[a].trim()&&h.push(o[a].split("|").map(function(e){return e.trim()}));if(d.length<f.length)return e;for(a=0;a<f.length;++a)g.push((s=f[a],/^:[ \t]*--*$/.test(s)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(s)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(s)?' style="text-align:center;"':""));for(a=0;a<d.length;++a)i.helper.isUndefined(g[a])&&(g[a]=""),p.push((c=d[a],l=g[a],u=void 0,u="",c=c.trim(),(t.tablesHeaderId||t.tableHeaderId)&&(u=' id="'+c.replace(/ /g,"_").toLowerCase()+'"'),"<th"+u+l+">"+(c=i.subParser("spanGamut")(c,t,r))+"</th>\n"));for(a=0;a<h.length;++a){for(var b=[],_=0;_<p.length;++_)i.helper.isUndefined(h[a][_]),b.push(n(h[a][_],g[_]));m.push(b)}return function(e,t){for(var r="<table>\n<thead>\n<tr>\n",n=e.length,a=0;a<n;++a)r+=e[a];for(r+="</tr>\n</thead>\n<tbody>\n",a=0;a<t.length;++a){r+="<tr>\n";for(var i=0;i<n;++i)r+=t[a][i];r+="</tr>\n"}return r+="</tbody>\n</table>\n"}(p,m)}return e=(e=(e=(e=r.converter._dispatch("tables.before",e,t,r)).replace(/\\(\|)/g,i.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,a)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,a),e=r.converter._dispatch("tables.after",e,t,r)}),i.subParser("underline",function(e,t,r){"use strict";return t.underline?(e=r.converter._dispatch("underline.before",e,t,r),e=(e=t.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,function(e,t){return"<u>"+t+"</u>"})).replace(/\b__(\S[\s\S]*?)__\b/g,function(e,t){return"<u>"+t+"</u>"}):(e=e.replace(/___(\S[\s\S]*?)___/g,function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e})).replace(/__(\S[\s\S]*?)__/g,function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e})).replace(/(_)/g,i.helper.escapeCharactersCallback),e=r.converter._dispatch("underline.after",e,t,r)):e}),i.subParser("unescapeSpecialChars",function(e,t,r){"use strict";return e=(e=r.converter._dispatch("unescapeSpecialChars.before",e,t,r)).replace(/¨E(\d+)E/g,function(e,t){var r=parseInt(t);return String.fromCharCode(r)}),e=r.converter._dispatch("unescapeSpecialChars.after",e,t,r)}),i.subParser("makeMarkdown.blockquote",function(e,t){"use strict";var r="";if(e.hasChildNodes())for(var n=e.childNodes,a=n.length,o=0;o<a;++o){var s=i.subParser("makeMarkdown.node")(n[o],t);""!==s&&(r+=s)}return r="> "+(r=r.trim()).split("\n").join("\n> ")}),i.subParser("makeMarkdown.codeBlock",function(e,t){"use strict";var r=e.getAttribute("language"),n=e.getAttribute("precodenum");return"```"+r+"\n"+t.preList[n]+"\n```"}),i.subParser("makeMarkdown.codeSpan",function(e){"use strict";return"`"+e.innerHTML+"`"}),i.subParser("makeMarkdown.emphasis",function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="*";for(var n=e.childNodes,a=n.length,o=0;o<a;++o)r+=i.subParser("makeMarkdown.node")(n[o],t);r+="*"}return r}),i.subParser("makeMarkdown.header",function(e,t,r){"use strict";var n=new Array(r+1).join("#"),a="";if(e.hasChildNodes()){a=n+" ";for(var o=e.childNodes,s=o.length,c=0;c<s;++c)a+=i.subParser("makeMarkdown.node")(o[c],t)}return a}),i.subParser("makeMarkdown.hr",function(){"use strict";return"---"}),i.subParser("makeMarkdown.image",function(e){"use strict";var t="";return e.hasAttribute("src")&&(t+="!["+e.getAttribute("alt")+"](",t+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(t+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(t+=' "'+e.getAttribute("title")+'"'),t+=")"),t}),i.subParser("makeMarkdown.links",function(e,t){"use strict";var r="";if(e.hasChildNodes()&&e.hasAttribute("href")){var n=e.childNodes,a=n.length;r="[";for(var o=0;o<a;++o)r+=i.subParser("makeMarkdown.node")(n[o],t);r+="](",r+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"}return r}),i.subParser("makeMarkdown.list",function(e,t,r){"use strict";var n="";if(!e.hasChildNodes())return"";for(var a=e.childNodes,o=a.length,s=e.getAttribute("start")||1,c=0;c<o;++c)if(void 0!==a[c].tagName&&"li"===a[c].tagName.toLowerCase()){n+=("ol"===r?s.toString()+". ":"- ")+i.subParser("makeMarkdown.listItem")(a[c],t),++s}return(n+="\n\x3c!-- --\x3e\n").trim()}),i.subParser("makeMarkdown.listItem",function(e,t){"use strict";for(var r="",n=e.childNodes,a=n.length,o=0;o<a;++o)r+=i.subParser("makeMarkdown.node")(n[o],t);return/\n$/.test(r)?r=r.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):r+="\n",r}),i.subParser("makeMarkdown.node",function(e,t,r){"use strict";r=r||!1;var n="";if(3===e.nodeType)return i.subParser("makeMarkdown.txt")(e,t);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":r||(n=i.subParser("makeMarkdown.header")(e,t,1)+"\n\n");break;case"h2":r||(n=i.subParser("makeMarkdown.header")(e,t,2)+"\n\n");break;case"h3":r||(n=i.subParser("makeMarkdown.header")(e,t,3)+"\n\n");break;case"h4":r||(n=i.subParser("makeMarkdown.header")(e,t,4)+"\n\n");break;case"h5":r||(n=i.subParser("makeMarkdown.header")(e,t,5)+"\n\n");break;case"h6":r||(n=i.subParser("makeMarkdown.header")(e,t,6)+"\n\n");break;case"p":r||(n=i.subParser("makeMarkdown.paragraph")(e,t)+"\n\n");break;case"blockquote":r||(n=i.subParser("makeMarkdown.blockquote")(e,t)+"\n\n");break;case"hr":r||(n=i.subParser("makeMarkdown.hr")(e,t)+"\n\n");break;case"ol":r||(n=i.subParser("makeMarkdown.list")(e,t,"ol")+"\n\n");break;case"ul":r||(n=i.subParser("makeMarkdown.list")(e,t,"ul")+"\n\n");break;case"precode":r||(n=i.subParser("makeMarkdown.codeBlock")(e,t)+"\n\n");break;case"pre":r||(n=i.subParser("makeMarkdown.pre")(e,t)+"\n\n");break;case"table":r||(n=i.subParser("makeMarkdown.table")(e,t)+"\n\n");break;case"code":n=i.subParser("makeMarkdown.codeSpan")(e,t);break;case"em":case"i":n=i.subParser("makeMarkdown.emphasis")(e,t);break;case"strong":case"b":n=i.subParser("makeMarkdown.strong")(e,t);break;case"del":n=i.subParser("makeMarkdown.strikethrough")(e,t);break;case"a":n=i.subParser("makeMarkdown.links")(e,t);break;case"img":n=i.subParser("makeMarkdown.image")(e,t);break;default:n=e.outerHTML+"\n\n"}return n}),i.subParser("makeMarkdown.paragraph",function(e,t){"use strict";var r="";if(e.hasChildNodes())for(var n=e.childNodes,a=n.length,o=0;o<a;++o)r+=i.subParser("makeMarkdown.node")(n[o],t);return r=r.trim()}),i.subParser("makeMarkdown.pre",function(e,t){"use strict";var r=e.getAttribute("prenum");return"<pre>"+t.preList[r]+"</pre>"}),i.subParser("makeMarkdown.strikethrough",function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="~~";for(var n=e.childNodes,a=n.length,o=0;o<a;++o)r+=i.subParser("makeMarkdown.node")(n[o],t);r+="~~"}return r}),i.subParser("makeMarkdown.strong",function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="**";for(var n=e.childNodes,a=n.length,o=0;o<a;++o)r+=i.subParser("makeMarkdown.node")(n[o],t);r+="**"}return r}),i.subParser("makeMarkdown.table",function(e,t){"use strict";var r,n,a="",o=[[],[]],s=e.querySelectorAll("thead>tr>th"),c=e.querySelectorAll("tbody>tr");for(r=0;r<s.length;++r){var l=i.subParser("makeMarkdown.tableCell")(s[r],t),u="---";if(s[r].hasAttribute("style"))switch(s[r].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":u=":---";break;case"text-align:right;":u="---:";break;case"text-align:center;":u=":---:"}o[0][r]=l.trim(),o[1][r]=u}for(r=0;r<c.length;++r){var d=o.push([])-1,f=c[r].getElementsByTagName("td");for(n=0;n<s.length;++n){var h=" ";void 0!==f[n]&&(h=i.subParser("makeMarkdown.tableCell")(f[n],t)),o[d].push(h)}}var p=3;for(r=0;r<o.length;++r)for(n=0;n<o[r].length;++n){var g=o[r][n].length;g>p&&(p=g)}for(r=0;r<o.length;++r){for(n=0;n<o[r].length;++n)1===r?":"===o[r][n].slice(-1)?o[r][n]=i.helper.padEnd(o[r][n].slice(-1),p-1,"-")+":":o[r][n]=i.helper.padEnd(o[r][n],p,"-"):o[r][n]=i.helper.padEnd(o[r][n],p);a+="| "+o[r].join(" | ")+" |\n"}return a.trim()}),i.subParser("makeMarkdown.tableCell",function(e,t){"use strict";var r="";if(!e.hasChildNodes())return"";for(var n=e.childNodes,a=n.length,o=0;o<a;++o)r+=i.subParser("makeMarkdown.node")(n[o],t,!0);return r.trim()}),i.subParser("makeMarkdown.txt",function(e){"use strict";var t=e.nodeValue;return t=(t=t.replace(/ +/g," ")).replace(/¨NBSP;/g," "),t=(t=(t=(t=(t=(t=(t=(t=(t=i.helper.unescapeHTMLEntities(t)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")});void 0===(n=function(){"use strict";return i}.call(t,r,t,e))||(e.exports=n)}).call(this)},19:function(e,t,r){"use strict";var n=r(33);function a(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||Object(n.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}r.d(t,"a",function(){return a})},2:function(e,t){!function(){e.exports=this.lodash}()},22:function(e,t){!function(){e.exports=this.wp.dom}()},23:function(e,t){!function(){e.exports=this.wp.hooks}()},25:function(e,t,r){"use strict";var n=r(35);var a=r(36);function i(e,t){return Object(n.a)(e)||function(e,t){var r=[],n=!0,a=!1,i=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){a=!0,i=e}finally{try{n||null==s.return||s.return()}finally{if(a)throw i}}return r}(e,t)||Object(a.a)()}r.d(t,"a",function(){return i})},308:function(e,t,r){"use strict";r.r(t);var n={};r.r(n),r.d(n,"getBlockTypes",function(){return _}),r.d(n,"getBlockType",function(){return k}),r.d(n,"getBlockStyles",function(){return w}),r.d(n,"getCategories",function(){return v}),r.d(n,"getDefaultBlockName",function(){return y}),r.d(n,"getFreeformFallbackBlockName",function(){return j}),r.d(n,"getUnregisteredFallbackBlockName",function(){return O}),r.d(n,"getChildBlockNames",function(){return x}),r.d(n,"getBlockSupport",function(){return C}),r.d(n,"hasBlockSupport",function(){return A}),r.d(n,"hasChildBlocks",function(){return S}),r.d(n,"hasChildBlocksWithInserterSupport",function(){return T});var a={};r.r(a),r.d(a,"addBlockTypes",function(){return E}),r.d(a,"removeBlockTypes",function(){return P}),r.d(a,"addBlockStyles",function(){return M}),r.d(a,"removeBlockStyles",function(){return N}),r.d(a,"setDefaultBlockName",function(){return B}),r.d(a,"setFreeformFallbackBlockName",function(){return L}),r.d(a,"setUnregisteredFallbackBlockName",function(){return z}),r.d(a,"setCategories",function(){return H}),r.d(a,"updateCategory",function(){return D});var i=r(5),o=r(15),s=r(19),c=r(8),l=r(2),u=r(1),d=[{slug:"common",title:Object(u.__)("Common Blocks")},{slug:"formatting",title:Object(u.__)("Formatting")},{slug:"layout",title:Object(u.__)("Layout Elements")},{slug:"widgets",title:Object(u.__)("Widgets")},{slug:"embed",title:Object(u.__)("Embeds")},{slug:"reusable",title:Object(u.__)("Reusable Blocks")}];function f(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1?arguments[1]:void 0;switch(r.type){case"REMOVE_BLOCK_TYPES":return-1!==r.names.indexOf(t)?null:t;case e:return r.name||null}return t}}var h=f("SET_DEFAULT_BLOCK_NAME"),p=f("SET_FREEFORM_FALLBACK_BLOCK_NAME"),g=f("SET_UNREGISTERED_FALLBACK_BLOCK_NAME");var m=Object(i.combineReducers)({blockTypes:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"ADD_BLOCK_TYPES":return Object(c.a)({},e,Object(l.keyBy)(Object(l.map)(t.blockTypes,function(e){return Object(l.omit)(e,"styles ")}),"name"));case"REMOVE_BLOCK_TYPES":return Object(l.omit)(e,t.names)}return e},blockStyles:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"ADD_BLOCK_TYPES":return Object(c.a)({},e,Object(l.mapValues)(Object(l.keyBy)(t.blockTypes,"name"),function(t){return Object(l.uniqBy)(Object(s.a)(Object(l.get)(t,["styles"],[])).concat(Object(s.a)(Object(l.get)(e,[t.name],[]))),function(e){return e.name})}));case"ADD_BLOCK_STYLES":return Object(c.a)({},e,Object(o.a)({},t.blockName,Object(l.uniqBy)(Object(s.a)(Object(l.get)(e,[t.blockName],[])).concat(Object(s.a)(t.styles)),function(e){return e.name})));case"REMOVE_BLOCK_STYLES":return Object(c.a)({},e,Object(o.a)({},t.blockName,Object(l.filter)(Object(l.get)(e,[t.blockName],[]),function(e){return-1===t.styleNames.indexOf(e.name)})))}return e},defaultBlockName:h,freeformFallbackBlockName:p,unregisteredFallbackBlockName:g,categories:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_CATEGORIES":return t.categories||[];case"UPDATE_CATEGORY":if(!t.category||Object(l.isEmpty)(t.category))return e;if(Object(l.find)(e,["slug",t.slug]))return Object(l.map)(e,function(e){return e.slug===t.slug?Object(c.a)({},e,t.category):e})}return e}}),b=r(31),_=Object(b.a)(function(e){return Object.values(e.blockTypes)},function(e){return[e.blockTypes]});function k(e,t){return e.blockTypes[t]}function w(e,t){return e.blockStyles[t]}function v(e){return e.categories}function y(e){return e.defaultBlockName}function j(e){return e.freeformFallbackBlockName}function O(e){return e.unregisteredFallbackBlockName}var x=Object(b.a)(function(e,t){return Object(l.map)(Object(l.filter)(e.blockTypes,function(e){return Object(l.includes)(e.parent,t)}),function(e){return e.name})},function(e){return[e.blockTypes]}),C=function(e,t,r,n){var a="string"==typeof t?k(e,t):t;return Object(l.get)(a,["supports",r],n)};function A(e,t,r,n){return!!C(e,t,r,n)}var S=function(e,t){return x(e,t).length>0},T=function(e,t){return Object(l.some)(x(e,t),function(t){return A(e,t,"inserter",!0)})};function E(e){return{type:"ADD_BLOCK_TYPES",blockTypes:Object(l.castArray)(e)}}function P(e){return{type:"REMOVE_BLOCK_TYPES",names:Object(l.castArray)(e)}}function M(e,t){return{type:"ADD_BLOCK_STYLES",styles:Object(l.castArray)(t),blockName:e}}function N(e,t){return{type:"REMOVE_BLOCK_STYLES",styleNames:Object(l.castArray)(t),blockName:e}}function B(e){return{type:"SET_DEFAULT_BLOCK_NAME",name:e}}function L(e){return{type:"SET_FREEFORM_FALLBACK_BLOCK_NAME",name:e}}function z(e){return{type:"SET_UNREGISTERED_FALLBACK_BLOCK_NAME",name:e}}function H(e){return{type:"SET_CATEGORIES",categories:e}}function D(e,t){return{type:"UPDATE_CATEGORY",slug:e,category:t}}Object(i.registerStore)("core/blocks",{reducer:m,selectors:n,actions:a});var I=r(57),V=r.n(I),R=r(23),F=r(45),q=r.n(F),$=r(0),U=["#191e23","#f8f9f9"];function G(e){var t=ie();if(e.name!==t)return!1;G.block&&G.block.name===t||(G.block=me(t));var r=G.block,n=oe(t);return Object(l.every)(n.attributes,function(t,n){return r.attributes[n]===e.attributes[n]})}function W(e){return!!e&&(Object(l.isString)(e)||Object($.isValidElement)(e)||Object(l.isFunction)(e)||e instanceof $.Component)}function K(e){if(e||(e="block-default"),W(e))return{src:e};if(Object(l.has)(e,["background"])){var t=q()(e.background);return Object(c.a)({},e,{foreground:e.foreground?e.foreground:Object(F.mostReadable)(t,U,{includeFallbackColors:!0,level:"AA",size:"large"}).toHexString(),shadowColor:t.setAlpha(.3).toRgbString()})}return e}function Y(e){return Object(l.isString)(e)?oe(e):e}var Z={};function Q(e){Z=e}function X(e,t){if(t=Object(c.a)({name:e},Object(l.get)(Z,e),t),"string"==typeof e)if(/^[a-z][a-z0-9-]*\/[a-z][a-z0-9-]*$/.test(e))if(Object(i.select)("core/blocks").getBlockType(e))console.error('Block "'+e+'" is already registered.');else if((t=Object(R.applyFilters)("blocks.registerBlockType",t,e))&&Object(l.isFunction)(t.save))if("edit"in t&&!Object(l.isFunction)(t.edit))console.error('The "edit" property must be a valid function.');else if("keywords"in t&&t.keywords.length>3)console.error('The block "'+e+'" can have a maximum of 3 keywords.');else if("category"in t)if("category"in t&&!Object(l.some)(Object(i.select)("core/blocks").getCategories(),{slug:t.category}))console.error('The block "'+e+'" must have a registered category.');else if("title"in t&&""!==t.title)if("string"==typeof t.title){if(t.icon=K(t.icon),W(t.icon.src))return Object(i.dispatch)("core/blocks").addBlockTypes(t),t;console.error("The icon passed is invalid. The icon should be a string, an element, a function, or an object following the specifications documented in https://wordpress.org/gutenberg/handbook/block-api/#icon-optional")}else console.error("Block titles must be strings.");else console.error('The block "'+e+'" must have a title.');else console.error('The block "'+e+'" must have a category.');else console.error('The "save" property must be specified and must be a valid function.');else console.error("Block names must contain a namespace prefix, include only lowercase alphanumeric characters or dashes, and start with a letter. Example: my-plugin/my-custom-block");else console.error("Block names must be strings.")}function J(e){var t=Object(i.select)("core/blocks").getBlockType(e);if(t)return Object(i.dispatch)("core/blocks").removeBlockTypes(e),t;console.error('Block "'+e+'" is not registered.')}function ee(e){Object(i.dispatch)("core/blocks").setFreeformFallbackBlockName(e)}function te(){return Object(i.select)("core/blocks").getFreeformFallbackBlockName()}function re(e){Object(i.dispatch)("core/blocks").setUnregisteredFallbackBlockName(e)}function ne(){return Object(i.select)("core/blocks").getUnregisteredFallbackBlockName()}function ae(e){Object(i.dispatch)("core/blocks").setDefaultBlockName(e)}function ie(){return Object(i.select)("core/blocks").getDefaultBlockName()}function oe(e){return Object(i.select)("core/blocks").getBlockType(e)}function se(){return Object(i.select)("core/blocks").getBlockTypes()}function ce(e,t,r){return Object(i.select)("core/blocks").getBlockSupport(e,t,r)}function le(e,t,r){return Object(i.select)("core/blocks").hasBlockSupport(e,t,r)}function ue(e){return"core/block"===e.name}var de=function(e){return Object(i.select)("core/blocks").getChildBlockNames(e)},fe=function(e){return Object(i.select)("core/blocks").hasChildBlocks(e)},he=function(e){return Object(i.select)("core/blocks").hasChildBlocksWithInserterSupport(e)},pe=function(e,t){Object(i.dispatch)("core/blocks").addBlockStyles(e,t)},ge=function(e,t){Object(i.dispatch)("core/blocks").removeBlockStyles(e,t)};function me(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=oe(e),a=Object(l.reduce)(n.attributes,function(e,r,n){var a=t[n];return void 0!==a?e[n]=a:r.hasOwnProperty("default")&&(e[n]=r.default),-1!==["node","children"].indexOf(r.source)&&("string"==typeof e[n]?e[n]=[e[n]]:Array.isArray(e[n])||(e[n]=[])),e},{});return{clientId:V()(),name:e,isValid:!0,attributes:a,innerBlocks:r}}function be(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,n=V()();return Object(c.a)({},e,{clientId:n,attributes:Object(c.a)({},e.attributes,t),innerBlocks:r||e.innerBlocks.map(function(e){return be(e)})})}var _e=function(e,t,r){if(Object(l.isEmpty)(r))return!1;if(!(!(r.length>1)||e.isMultiBlock))return!1;if(!("block"===e.type))return!1;var n=Object(l.first)(r);if(!("from"!==t||-1!==e.blocks.indexOf(n.name)))return!1;if(Object(l.isFunction)(e.isMatch)){var a=e.isMultiBlock?r.map(function(e){return e.attributes}):n.attributes;if(!e.isMatch(a))return!1}return!0},ke=function(e){if(Object(l.isEmpty)(e))return[];var t=se();return Object(l.filter)(t,function(t){return!!ye(je("from",t.name),function(t){return _e(t,"from",e)})})},we=function(e){if(Object(l.isEmpty)(e))return[];var t=je("to",oe(Object(l.first)(e).name).name),r=Object(l.filter)(t,function(t){return _e(t,"to",e)});return Object(l.flatMap)(r,function(e){return e.blocks}).map(function(e){return oe(e)})};function ve(e){if(Object(l.isEmpty)(e))return[];var t=Object(l.first)(e);if(e.length>1&&!Object(l.every)(e,{name:t.name}))return[];var r=ke(e),n=we(e);return Object(l.uniq)(Object(s.a)(r).concat(Object(s.a)(n)))}function ye(e,t){for(var r=Object(R.createHooks)(),n=function(n){var a=e[n];t(a)&&r.addFilter("transform","transform/"+n.toString(),function(e){return e||a},a.priority)},a=0;a<e.length;a++)n(a);return r.applyFilters("transform",null)}function je(e,t){if(void 0===t)return Object(l.flatMap)(se(),function(t){var r=t.name;return je(e,r)});var r=Y(t)||{},n=r.name,a=r.transforms;return a&&Array.isArray(a[e])?a[e].map(function(e){return Object(c.a)({},e,{blockName:n})}):[]}function Oe(e,t){var r=Object(l.castArray)(e),n=r.length>1,a=r[0],i=a.name;if(n&&!Object(l.every)(r,function(e){return e.name===i}))return null;var o,s=je("from",t),u=ye(je("to",i),function(e){return"block"===e.type&&-1!==e.blocks.indexOf(t)&&(!n||e.isMultiBlock)})||ye(s,function(e){return"block"===e.type&&-1!==e.blocks.indexOf(i)&&(!n||e.isMultiBlock)});if(!u)return null;if(o=u.isMultiBlock?u.transform(r.map(function(e){return e.attributes})):u.transform(a.attributes),!Object(l.isObjectLike)(o))return null;if((o=Object(l.castArray)(o)).some(function(e){return!oe(e.name)}))return null;var d=Object(l.findIndex)(o,function(e){return e.name===t});return d<0?null:o.map(function(t,r){var n=Object(c.a)({},t,{clientId:r===d?a.clientId:t.clientId});return Object(R.applyFilters)("blocks.switchToBlockType.transformedBlock",n,e)})}var xe=r(25);var Ce,Ae=function(){return Ce||(Ce=document.implementation.createHTMLDocument("")),Ce};function Se(e,t){if(t){if("string"==typeof e){var r=Ae();r.body.innerHTML=e,e=r.body}if("function"==typeof t)return t(e);if(Object===t.constructor)return Object.keys(t).reduce(function(r,n){return r[n]=Se(e,t[n]),r},{})}}function Te(e,t){return 1===arguments.length&&(t=e,e=void 0),function(r){var n=r;if(e&&(n=r.querySelector(e)),n)return function(e,t){for(var r,n=t.split(".");r=n.shift();){if(!(r in e))return;e=e[r]}return e}(n,t)}}var Ee=r(58),Pe=r(183),Me=r(35),Ne=r(33),Be=r(36);var Le=r(10),ze=r(9),He=/[\t\n\f ]/,De=/[A-Za-z]/,Ie=/\r\n?/g;function Ve(e){return He.test(e)}function Re(e){return De.test(e)}function Fe(e,t){if(!e)throw new Error((t||"value")+" was null");return e}var qe=function(){function e(e,t){this.delegate=e,this.entityParser=t,this.state=null,this.input=null,this.index=-1,this.tagLine=-1,this.tagColumn=-1,this.line=-1,this.column=-1,this.states={beforeData:function(){"<"===this.peek()?(this.state="tagOpen",this.markTagStart(),this.consume()):(this.state="data",this.delegate.beginData())},data:function(){var e=this.peek();"<"===e?(this.delegate.finishData(),this.state="tagOpen",this.markTagStart(),this.consume()):"&"===e?(this.consume(),this.delegate.appendToData(this.consumeCharRef()||"&")):(this.consume(),this.delegate.appendToData(e))},tagOpen:function(){var e=this.consume();"!"===e?this.state="markupDeclaration":"/"===e?this.state="endTagOpen":Re(e)&&(this.state="tagName",this.delegate.beginStartTag(),this.delegate.appendToTagName(e.toLowerCase()))},markupDeclaration:function(){"-"===this.consume()&&"-"===this.input.charAt(this.index)&&(this.consume(),this.state="commentStart",this.delegate.beginComment())},commentStart:function(){var e=this.consume();"-"===e?this.state="commentStartDash":">"===e?(this.delegate.finishComment(),this.state="beforeData"):(this.delegate.appendToCommentData(e),this.state="comment")},commentStartDash:function(){var e=this.consume();"-"===e?this.state="commentEnd":">"===e?(this.delegate.finishComment(),this.state="beforeData"):(this.delegate.appendToCommentData("-"),this.state="comment")},comment:function(){var e=this.consume();"-"===e?this.state="commentEndDash":this.delegate.appendToCommentData(e)},commentEndDash:function(){var e=this.consume();"-"===e?this.state="commentEnd":(this.delegate.appendToCommentData("-"+e),this.state="comment")},commentEnd:function(){var e=this.consume();">"===e?(this.delegate.finishComment(),this.state="beforeData"):(this.delegate.appendToCommentData("--"+e),this.state="comment")},tagName:function(){var e=this.consume();Ve(e)?this.state="beforeAttributeName":"/"===e?this.state="selfClosingStartTag":">"===e?(this.delegate.finishTag(),this.state="beforeData"):this.delegate.appendToTagName(e)},beforeAttributeName:function(){var e=this.peek();Ve(e)?this.consume():"/"===e?(this.state="selfClosingStartTag",this.consume()):">"===e?(this.consume(),this.delegate.finishTag(),this.state="beforeData"):"="===e?(this.delegate.reportSyntaxError("attribute name cannot start with equals sign"),this.state="attributeName",this.delegate.beginAttribute(),this.consume(),this.delegate.appendToAttributeName(e)):(this.state="attributeName",this.delegate.beginAttribute())},attributeName:function(){var e=this.peek();Ve(e)?(this.state="afterAttributeName",this.consume()):"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.state="selfClosingStartTag"):"="===e?(this.state="beforeAttributeValue",this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.state="beforeData"):'"'===e||"'"===e||"<"===e?(this.delegate.reportSyntaxError(e+" is not a valid character within attribute names"),this.consume(),this.delegate.appendToAttributeName(e)):(this.consume(),this.delegate.appendToAttributeName(e))},afterAttributeName:function(){var e=this.peek();Ve(e)?this.consume():"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.state="selfClosingStartTag"):"="===e?(this.consume(),this.state="beforeAttributeValue"):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.state="beforeData"):(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.state="attributeName",this.delegate.beginAttribute(),this.delegate.appendToAttributeName(e))},beforeAttributeValue:function(){var e=this.peek();Ve(e)?this.consume():'"'===e?(this.state="attributeValueDoubleQuoted",this.delegate.beginAttributeValue(!0),this.consume()):"'"===e?(this.state="attributeValueSingleQuoted",this.delegate.beginAttributeValue(!0),this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.state="beforeData"):(this.state="attributeValueUnquoted",this.delegate.beginAttributeValue(!1),this.consume(),this.delegate.appendToAttributeValue(e))},attributeValueDoubleQuoted:function(){var e=this.consume();'"'===e?(this.delegate.finishAttributeValue(),this.state="afterAttributeValueQuoted"):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef('"')||"&"):this.delegate.appendToAttributeValue(e)},attributeValueSingleQuoted:function(){var e=this.consume();"'"===e?(this.delegate.finishAttributeValue(),this.state="afterAttributeValueQuoted"):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef("'")||"&"):this.delegate.appendToAttributeValue(e)},attributeValueUnquoted:function(){var e=this.peek();Ve(e)?(this.delegate.finishAttributeValue(),this.consume(),this.state="beforeAttributeName"):"&"===e?(this.consume(),this.delegate.appendToAttributeValue(this.consumeCharRef(">")||"&")):">"===e?(this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.state="beforeData"):(this.consume(),this.delegate.appendToAttributeValue(e))},afterAttributeValueQuoted:function(){var e=this.peek();Ve(e)?(this.consume(),this.state="beforeAttributeName"):"/"===e?(this.consume(),this.state="selfClosingStartTag"):">"===e?(this.consume(),this.delegate.finishTag(),this.state="beforeData"):this.state="beforeAttributeName"},selfClosingStartTag:function(){">"===this.peek()?(this.consume(),this.delegate.markTagAsSelfClosing(),this.delegate.finishTag(),this.state="beforeData"):this.state="beforeAttributeName"},endTagOpen:function(){var e=this.consume();Re(e)&&(this.state="tagName",this.delegate.beginEndTag(),this.delegate.appendToTagName(e.toLowerCase()))}},this.reset()}return e.prototype.reset=function(){this.state="beforeData",this.input="",this.index=0,this.line=1,this.column=0,this.tagLine=-1,this.tagColumn=-1,this.delegate.reset()},e.prototype.tokenize=function(e){this.reset(),this.tokenizePart(e),this.tokenizeEOF()},e.prototype.tokenizePart=function(e){for(this.input+=function(e){return e.replace(Ie,"\n")}(e);this.index<this.input.length;)this.states[this.state].call(this)},e.prototype.tokenizeEOF=function(){this.flushData()},e.prototype.flushData=function(){"data"===this.state&&(this.delegate.finishData(),this.state="beforeData")},e.prototype.peek=function(){return this.input.charAt(this.index)},e.prototype.consume=function(){var e=this.peek();return this.index++,"\n"===e?(this.line++,this.column=0):this.column++,e},e.prototype.consumeCharRef=function(){var e=this.input.indexOf(";",this.index);if(-1!==e){var t=this.input.slice(this.index,e),r=this.entityParser.parse(t);if(r){for(var n=t.length;n;)this.consume(),n--;return this.consume(),r}}},e.prototype.markTagStart=function(){this.tagLine=this.line,this.tagColumn=this.column,this.delegate.tagOpen&&this.delegate.tagOpen()},e}(),$e=function(){function e(e,t){void 0===t&&(t={}),this.options=t,this._token=null,this.startLine=1,this.startColumn=0,this.tokens=[],this.currentAttribute=null,this.tokenizer=new qe(this,e)}return Object.defineProperty(e.prototype,"token",{get:function(){return Fe(this._token)},set:function(e){this._token=e},enumerable:!0,configurable:!0}),e.prototype.tokenize=function(e){return this.tokens=[],this.tokenizer.tokenize(e),this.tokens},e.prototype.tokenizePart=function(e){return this.tokens=[],this.tokenizer.tokenizePart(e),this.tokens},e.prototype.tokenizeEOF=function(){return this.tokens=[],this.tokenizer.tokenizeEOF(),this.tokens[0]},e.prototype.reset=function(){this._token=null,this.startLine=1,this.startColumn=0},e.prototype.addLocInfo=function(){this.options.loc&&(this.token.loc={start:{line:this.startLine,column:this.startColumn},end:{line:this.tokenizer.line,column:this.tokenizer.column}}),this.startLine=this.tokenizer.line,this.startColumn=this.tokenizer.column},e.prototype.beginData=function(){this.token={type:"Chars",chars:""},this.tokens.push(this.token)},e.prototype.appendToData=function(e){this.token.chars+=e},e.prototype.finishData=function(){this.addLocInfo()},e.prototype.beginComment=function(){this.token={type:"Comment",chars:""},this.tokens.push(this.token)},e.prototype.appendToCommentData=function(e){this.token.chars+=e},e.prototype.finishComment=function(){this.addLocInfo()},e.prototype.beginStartTag=function(){this.token={type:"StartTag",tagName:"",attributes:[],selfClosing:!1},this.tokens.push(this.token)},e.prototype.beginEndTag=function(){this.token={type:"EndTag",tagName:""},this.tokens.push(this.token)},e.prototype.finishTag=function(){this.addLocInfo()},e.prototype.markTagAsSelfClosing=function(){this.token.selfClosing=!0},e.prototype.appendToTagName=function(e){this.token.tagName+=e},e.prototype.beginAttribute=function(){var e=Fe(this.token.attributes,"current token's attributs");this.currentAttribute=["","",!1],e.push(this.currentAttribute)},e.prototype.appendToAttributeName=function(e){Fe(this.currentAttribute)[0]+=e},e.prototype.beginAttributeValue=function(e){Fe(this.currentAttribute)[2]=e},e.prototype.appendToAttributeValue=function(e){var t=Fe(this.currentAttribute);t[1]=t[1]||"",t[1]+=e},e.prototype.finishAttributeValue=function(){},e.prototype.reportSyntaxError=function(e){this.token.syntaxError=e},e}(),Ue=r(49),Ge=r(40),We=r.n(Ge),Ke=r(18),Ye=r(7),Ze=Object($.createContext)(function(){}),Qe=Ze.Consumer,Xe=Ze.Provider,Je=Object(Ye.createHigherOrderComponent)(function(e){return function(t){return Object($.createElement)(Qe,null,function(r){return Object($.createElement)(e,Object(Ke.a)({},t,{BlockContent:r}))})}},"withBlockContentContext"),et=function(e){var t=e.children,r=e.innerBlocks;return Object($.createElement)(Xe,{value:function(){var e=ct(r);return Object($.createElement)($.RawHTML,null,e)}},t)};function tt(e){var t="wp-block-"+e.replace(/\//,"-").replace(/^core-/,"");return Object(R.applyFilters)("blocks.getBlockDefaultClassName",t,e)}function rt(e){var t="editor-block-list-item-"+e.replace(/\//,"-").replace(/^core-/,"");return Object(R.applyFilters)("blocks.getBlockMenuDefaultClassName",t,e)}function nt(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=Y(e),a=n.save;if(a.prototype instanceof $.Component){var i=new a({attributes:t});a=i.render.bind(i)}var o=a({attributes:t,innerBlocks:r});if(Object(l.isObject)(o)&&Object(R.hasFilter)("blocks.getSaveContent.extraProps")){var s=Object(R.applyFilters)("blocks.getSaveContent.extraProps",Object(c.a)({},o.props),n,t);We()(s,o.props)||(o=Object($.cloneElement)(o,s))}return o=Object(R.applyFilters)("blocks.getSaveElement",o,n,t),Object($.createElement)(et,{innerBlocks:r},o)}function at(e,t,r){var n=Y(e);return Object($.renderToString)(nt(n,t,r))}function it(e){var t=e.originalContent;if(e.isValid||e.innerBlocks.length)try{t=at(e.name,e.attributes,e.innerBlocks)}catch(e){}return t}function ot(e,t,r){var n=Object(l.isEmpty)(t)?"":function(e){return JSON.stringify(e).replace(/--/g,"\\u002d\\u002d").replace(/</g,"\\u003c").replace(/>/g,"\\u003e").replace(/&/g,"\\u0026").replace(/\\"/g,"\\u0022")}(t)+" ",a=Object(l.startsWith)(e,"core/")?e.slice(5):e;return r?"\x3c!-- wp:".concat(a," ").concat(n,"--\x3e\n")+r+"\n\x3c!-- /wp:".concat(a," --\x3e"):"\x3c!-- wp:".concat(a," ").concat(n,"/--\x3e")}function st(e){var t=e.name,r=it(e);switch(t){case te():case ne():return r;default:return ot(t,function(e,t){return Object(l.reduce)(e.attributes,function(e,r,n){var a=t[n];return void 0===a?e:void 0!==r.source?e:"default"in r&&r.default===a?e:(e[n]=a,e)},{})}(oe(t),e.attributes),r)}}function ct(e){return Object(l.castArray)(e).map(st).join("\n\n")}var lt=/[\t\n\r\v\f ]+/g,ut=/^[\t\n\r\v\f ]*$/,dt=/^url\s*\(['"\s]*(.*?)['"\s]*\)$/,ft=["allowfullscreen","allowpaymentrequest","allowusermedia","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","typemustmatch"],ht=ft.concat(["autocapitalize","autocomplete","charset","contenteditable","crossorigin","decoding","dir","draggable","enctype","formenctype","formmethod","http-equiv","inputmode","kind","method","preload","scope","shape","spellcheck","translate","type","wrap"]),pt=[l.identity,function(e){return bt(e).join(" ")}],gt=function(){function e(){Object(Le.a)(this,e)}return Object(ze.a)(e,[{key:"parse",value:function(e){return Object(Ue.decodeEntities)("&"+e+";")}}]),e}(),mt=function(){function e(e){return function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return e.apply(void 0,["Block validation: "+t].concat(n))}}return{error:e(console.error),warning:e(console.warn)}}();function bt(e){return e.trim().split(lt)}function _t(e){return e.attributes.filter(function(e){var t=Object(xe.a)(e,2),r=t[0];return t[1]||0===r.indexOf("data-")||Object(l.includes)(ht,r)})}function kt(e,t){for(var r=e.chars,n=t.chars,a=0;a<pt.length;a++){var i=pt[a];if((r=i(r))===(n=i(n)))return!0}return mt.warning("Expected text `%s`, saw `%s`.",t.chars,e.chars),!1}function wt(e){return e.replace(dt,"url($1)")}function vt(e){var t=e.replace(/;?\s*$/,"").split(";").map(function(e){var t,r=e.split(":"),n=(t=r,Object(Me.a)(t)||Object(Ne.a)(t)||Object(Be.a)()),a=n[0],i=n.slice(1).join(":");return[a.trim(),wt(i.trim())]});return Object(l.fromPairs)(t)}var yt=Object(c.a)({class:function(e,t){return!l.xor.apply(void 0,Object(s.a)([e,t].map(bt))).length},style:function(e,t){return l.isEqual.apply(void 0,Object(s.a)([e,t].map(vt)))}},Object(l.fromPairs)(ft.map(function(e){return[e,l.stubTrue]})));var jt={StartTag:function(e,t){return e.tagName!==t.tagName?(mt.warning("Expected tag name `%s`, instead saw `%s`.",t.tagName,e.tagName),!1):function(e,t){if(e.length!==t.length)return mt.warning("Expected attributes %o, instead saw %o.",t,e),!1;var r=[e,t].map(l.fromPairs),n=Object(xe.a)(r,2),a=n[0],i=n[1];for(var o in a){if(!i.hasOwnProperty(o))return mt.warning("Encountered unexpected attribute `%s`.",o),!1;var s=a[o],c=i[o],u=yt[o];if(u){if(!u(s,c))return mt.warning("Expected attribute `%s` of value `%s`, saw `%s`.",o,c,s),!1}else if(s!==c)return mt.warning("Expected attribute `%s` of value `%s`, saw `%s`.",o,c,s),!1}return!0}.apply(void 0,Object(s.a)([e,t].map(_t)))},Chars:kt,Comment:kt};function Ot(e){for(var t;t=e.shift();){if("Chars"!==t.type)return t;if(!ut.test(t.chars))return t}}function xt(e){try{return new $e(new gt).tokenize(e)}catch(t){mt.warning("Malformed HTML detected: %s",e)}return null}function Ct(e,t){return!!e.selfClosing&&!(!t||t.tagName!==e.tagName||"EndTag"!==t.type)}function At(e,t,r){var n,a=Y(e);try{n=at(a,t)}catch(e){return mt.error("Block validation failed because an error occurred while generating block content:\n\n%s",e.toString()),!1}var i=function(e,t){var r,n,a=[e,t].map(xt),i=Object(xe.a)(a,2),o=i[0],s=i[1];if(!o||!s)return!1;for(;r=Ot(o);){if(!(n=Ot(s)))return mt.warning("Expected end of content, instead saw %o.",r),!1;if(r.type!==n.type)return mt.warning("Expected token of type `%s` (%o), instead saw `%s` (%o).",n.type,n,r.type,r),!1;var c=jt[r.type];if(c&&!c(r,n))return!1;Ct(r,s[0])?Ot(s):Ct(n,o[0])&&Ot(o)}return!(n=Ot(s))||(mt.warning("Expected %o, instead saw end of content.",n),!1)}(r,n);return i||mt.error("Block validation failed for `%s` (%o).\n\nExpected:\n\n%s\n\nActual:\n\n%s",a.name,a,n,r),i}function St(e){for(var t=[],r=0;r<e.length;r++)try{t.push(zt(e[r]))}catch(e){}return t}function Tt(e){var t=function(e){return e}(e);return Object($.renderToString)(t)}function Et(e){return function(t){var r=t;return e&&(r=t.querySelector(e)),r?St(r.childNodes):[]}}var Pt={concat:function(){for(var e=[],t=0;t<arguments.length;t++)for(var r=Object(l.castArray)(t<0||arguments.length<=t?void 0:arguments[t]),n=0;n<r.length;n++){var a=r[n];"string"==typeof a&&"string"==typeof e[e.length-1]?e[e.length-1]+=a:e.push(a)}return e},getChildrenArray:function(e){return e},fromDOM:St,toHTML:Tt,matcher:Et},Mt=window.Node,Nt=Mt.TEXT_NODE,Bt=Mt.ELEMENT_NODE;function Lt(e){for(var t={},r=0;r<e.length;r++){var n=e[r],a=n.name,i=n.value;t[a]=i}return t}function zt(e){if(e.nodeType===Nt)return e.nodeValue;if(e.nodeType!==Bt)throw new TypeError("A block node can only be created from a node of type text or element.");return{type:e.nodeName.toLowerCase(),props:Object(c.a)({},Lt(e.attributes),{children:St(e.childNodes)})}}function Ht(e){return function(t){var r=t;e&&(r=t.querySelector(e));try{return zt(r)}catch(e){return null}}}var Dt={isNodeOfType:function(e,t){return e&&e.type===t},fromDOM:zt,toHTML:function(e){return Tt([e])},matcher:Ht};new Set(["attribute","html","text","tag"]);var It=function(e){return Object(l.flow)([e,function(e){return void 0!==e}])};function Vt(e,t){return t.some(function(t){return function(e,t){switch(t){case"string":return"string"==typeof e;case"boolean":return"boolean"==typeof e;case"object":return!!e&&e.constructor===Object;case"null":return null===e;case"array":return Array.isArray(e);case"integer":case"number":return"number"==typeof e}return!0}(e,t)})}function Rt(e){switch(e.source){case"attribute":var t=function(e,t){return 1===arguments.length&&(t=e,e=void 0),function(r){var n=Te(e,"attributes")(r);if(n&&n.hasOwnProperty(t))return n[t].value}}(e.selector,e.attribute);return"boolean"===e.type&&(t=It(t)),t;case"html":return n=e.selector,a=e.multiline,function(e){var t=e;if(n&&(t=e.querySelector(n)),!t)return"";if(a){for(var r="",i=t.children.length,o=0;o<i;o++){var s=t.children[o];s.nodeName.toLowerCase()===a&&(r+=s.outerHTML)}return r}return t.innerHTML};case"text":return function(e){return Te(e,"textContent")}(e.selector);case"children":return Et(e.selector);case"node":return Ht(e.selector);case"query":var r=Object(l.mapValues)(e.query,Rt);return function(e,t){return function(r){var n=r.querySelectorAll(e);return[].map.call(n,function(e){return Se(e,t)})}}(e.selector,r);case"tag":return Object(l.flow)([Te(e.selector,"nodeName"),function(e){return e.toLowerCase()}]);default:console.error('Unknown source type "'.concat(e.source,'"'))}var n,a}function Ft(e,t){return Se(e,Rt(t))}function qt(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=Y(e),a=Object(l.mapValues)(n.attributes,function(e,n){return function(e,t,r,n){var a,i=t.type;switch(t.source){case void 0:a=n?n[e]:void 0;break;case"attribute":case"property":case"html":case"text":case"children":case"node":case"query":case"tag":a=Ft(r,t)}return void 0===i||Vt(a,Object(l.castArray)(i))||(a=void 0),void 0===a?t.default:a}(n,e,t,r)});return Object(R.applyFilters)("blocks.getBlockAttributes",a,n,t,r)}function $t(e){var t=e.blockName,r=e.attrs,n=e.innerBlocks,a=void 0===n?[]:n,i=e.innerHTML,o=te(),s=ne()||o;r=r||{},i=i.trim();var u=t||o;"core/cover-image"===u&&(u="core/cover"),"core/text"!==u&&"core/cover-text"!==u||(u="core/paragraph"),u===o&&(i=Object(Ee.autop)(i).trim());var d=oe(u);if(!d){var f=i;u&&(i=ot(u,r,i)),r={originalName:t,originalUndelimitedContent:f},d=oe(u=s)}a=a.map($t);var h=u===o||u===s;if(d&&(i||!h)){var p=me(u,qt(d,i,r),a);return h||(p.isValid=At(d,p.attributes,i)),p.originalContent=i,p=function(e){var t=oe(e.name),r=t.deprecated;if(!r||!r.length)return e;for(var n=e,a=n.originalContent,i=n.attributes,o=n.innerBlocks,s=0;s<r.length;s++){var u=r[s].isEligible,d=void 0===u?l.stubFalse:u;if(!e.isValid||d(i,o)){var f=Object.assign(Object(l.omit)(t,["attributes","save","supports"]),r[s]),h=qt(f,a,i);if(At(f,h,a)){e=Object(c.a)({},e,{isValid:!0});var p=o,g=f.migrate;if(g){var m=Object(l.castArray)(g(h,o)),b=Object(xe.a)(m,2),_=b[0];h=void 0===_?i:_;var k=b[1];p=void 0===k?o:k}e.attributes=h,e.innerBlocks=p}}}return e}(p)}}var Ut,Gt=(Ut=Pe.parse,function(e){return Ut(e).reduce(function(e,t){var r=$t(t);return r&&e.push(r),e},[])}),Wt=Gt,Kt=r(22),Yt={strong:{},em:{},del:{},ins:{},a:{attributes:["href","target","rel"]},code:{},abbr:{attributes:["title"]},sub:{},sup:{},br:{},"#text":{}};function Zt(){return Yt}function Qt(e){var t=e.nodeName.toLowerCase();return Zt().hasOwnProperty(t)||"span"===t}["strong","em","del","ins","a","code","abbr","sub","sup"].forEach(function(e){Yt[e].children=Object(l.omit)(Yt,e)});var Xt=window.Node,Jt=Xt.ELEMENT_NODE,er=Xt.TEXT_NODE;function tr(e){var t=e.map(function(e){var t=e.isMatch,r=e.blockName,n=e.schema;if(le(r,"anchor"))for(var a in n)n[a].attributes||(n[a].attributes=[]),n[a].attributes.push("id");if(t)for(var i in n)n[i].isMatch=t;return n});return l.mergeWith.apply(void 0,[{}].concat(Object(s.a)(t),[function(e,t,r){switch(r){case"children":return"*"===e||"*"===t?"*":Object(c.a)({},e,t);case"attributes":case"require":return Object(s.a)(e||[]).concat(Object(s.a)(t||[]));case"isMatch":if(!e||!t)return;return function(){return e.apply(void 0,arguments)||t.apply(void 0,arguments)}}}]))}function rr(e){return!e.hasChildNodes()||Array.from(e.childNodes).every(function(e){return e.nodeType===er?!e.nodeValue.trim():e.nodeType!==Jt||("BR"===e.nodeName||!e.hasAttributes()&&rr(e))})}function nr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0,n=document.implementation.createHTMLDocument("");return n.body.innerHTML=e,function e(t,r,n,a){Array.from(t).forEach(function(t){e(t.childNodes,r,n,a),r.forEach(function(e){n.contains(t)&&e(t,n,a)})})}(n.body.childNodes,t,n,r),n.body.innerHTML}function ar(e,t,r){var n=document.implementation.createHTMLDocument("");return n.body.innerHTML=e,function e(t,r,n,a){Array.from(t).forEach(function(t){var i=t.nodeName.toLowerCase();if(!n.hasOwnProperty(i)||n[i].isMatch&&!n[i].isMatch(t))e(t.childNodes,r,n,a),a&&!Qt(t)&&t.nextElementSibling&&Object(Kt.insertAfter)(r.createElement("br"),t),Object(Kt.unwrap)(t);else if(t.nodeType===Jt){var o=n[i],s=o.attributes,c=void 0===s?[]:s,u=o.classes,d=void 0===u?[]:u,f=o.children,h=o.require,p=void 0===h?[]:h;if(rr(t)&&f)return void Object(Kt.remove)(t);if(t.hasAttributes()&&(Array.from(t.attributes).forEach(function(e){var r=e.name;"class"===r||Object(l.includes)(c,r)||t.removeAttribute(r)}),t.classList.length)){var g=d.map(function(e){return"string"==typeof e?function(t){return t===e}:e instanceof RegExp?function(t){return e.test(t)}:l.noop});Array.from(t.classList).forEach(function(e){g.some(function(t){return t(e)})||t.classList.remove(e)}),t.classList.length||t.removeAttribute("class")}if(t.hasChildNodes()){if("*"===f)return;if(f)p.length&&!t.querySelector(p.join(","))&&(e(t.childNodes,r,n,a),Object(Kt.unwrap)(t)),e(t.childNodes,r,f,a);else for(;t.firstChild;)Object(Kt.remove)(t.firstChild)}}})}(n.body.childNodes,n,t,r),n.body.innerHTML}var ir=window.Node,or=ir.ELEMENT_NODE,sr=ir.TEXT_NODE,cr=function(e){var t=document.implementation.createHTMLDocument(""),r=document.implementation.createHTMLDocument(""),n=t.body,a=r.body;for(n.innerHTML=e;n.firstChild;){var i=n.firstChild;i.nodeType===sr?i.nodeValue.trim()?(a.lastChild&&"P"===a.lastChild.nodeName||a.appendChild(r.createElement("P")),a.lastChild.appendChild(i)):n.removeChild(i):i.nodeType===or?"BR"===i.nodeName?(i.nextSibling&&"BR"===i.nextSibling.nodeName&&(a.appendChild(r.createElement("P")),n.removeChild(i.nextSibling)),a.lastChild&&"P"===a.lastChild.nodeName&&a.lastChild.hasChildNodes()?a.lastChild.appendChild(i):n.removeChild(i)):"P"===i.nodeName?rr(i)?n.removeChild(i):a.appendChild(i):Qt(i)?(a.lastChild&&"P"===a.lastChild.nodeName||a.appendChild(r.createElement("P")),a.lastChild.appendChild(i)):a.appendChild(i):n.removeChild(i)}return a.innerHTML},lr=window.Node.COMMENT_NODE,ur=function(e,t){if(e.nodeType===lr)if("nextpage"!==e.nodeValue){if(0===e.nodeValue.indexOf("more")){for(var r=e.nodeValue.slice(4).trim(),n=e,a=!1;n=n.nextSibling;)if(n.nodeType===lr&&"noteaser"===n.nodeValue){a=!0,Object(Kt.remove)(n);break}Object(Kt.replace)(e,function(e,t,r){var n=r.createElement("wp-block");n.dataset.block="core/more",e&&(n.dataset.customText=e);t&&(n.dataset.noTeaser="");return n}(r,a,t))}}else Object(Kt.replace)(e,function(e){var t=e.createElement("wp-block");return t.dataset.block="core/nextpage",t}(t))};function dr(e,t){return e.every(function(e){return function(e,t){if(Qt(e))return!0;if(!t)return!1;var r=e.nodeName.toLowerCase();return[["ul","li","ol"],["h1","h2","h3","h4","h5","h6"]].some(function(e){return 0===Object(l.difference)([r,t],e).length})}(e,t)&&dr(Array.from(e.children),t)})}function fr(e){return"BR"===e.nodeName&&e.previousSibling&&"BR"===e.previousSibling.nodeName}var hr=function(e,t){var r=document.implementation.createHTMLDocument("");r.body.innerHTML=e;var n=Array.from(r.body.children);return!n.some(fr)&&dr(n,t)};var pr=function(e,t,r){if("SPAN"===e.nodeName){var n=e.style,a=n.fontWeight,i=n.fontStyle,o=n.textDecorationLine,s=n.verticalAlign;"bold"!==a&&"700"!==a||Object(Kt.wrap)(t.createElement("strong"),e),"italic"===i&&Object(Kt.wrap)(t.createElement("em"),e),"line-through"===o&&Object(Kt.wrap)(t.createElement("del"),e),"super"===s?Object(Kt.wrap)(t.createElement("sup"),e):"sub"===s&&Object(Kt.wrap)(t.createElement("sub"),e)}else"B"===e.nodeName?e=Object(Kt.replaceTag)(e,"strong"):"I"===e.nodeName?e=Object(Kt.replaceTag)(e,"em"):"A"===e.nodeName&&("_blank"===e.target.toLowerCase()?e.rel="noreferrer noopener":(e.removeAttribute("target"),e.removeAttribute("rel")));Qt(e)&&e.hasChildNodes()&&Array.from(e.childNodes).some(function(e){return function(e){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).hasOwnProperty(e.nodeName.toLowerCase())}(e,r)})&&Object(Kt.unwrap)(e)},gr=function(e){"SCRIPT"!==e.nodeName&&"NOSCRIPT"!==e.nodeName&&"TEMPLATE"!==e.nodeName&&"STYLE"!==e.nodeName||e.parentNode.removeChild(e)},mr=window.parseInt;function br(e){return"OL"===e.nodeName||"UL"===e.nodeName}var _r=function(e,t){if("P"===e.nodeName){var r=e.getAttribute("style");if(r&&-1!==r.indexOf("mso-list")){var n=/mso-list\s*:[^;]+level([0-9]+)/i.exec(r);if(n){var a=mr(n[1],10)-1||0,i=e.previousElementSibling;if(!i||!br(i)){var o=e.textContent.trim().slice(0,1),s=/[1iIaA]/.test(o),c=t.createElement(s?"ol":"ul");s&&c.setAttribute("type",o),e.parentNode.insertBefore(c,e)}var l=e.previousElementSibling,u=l.nodeName,d=t.createElement("li"),f=l;for(e.removeChild(e.firstElementChild);e.firstChild;)d.appendChild(e.firstChild);for(;a--;)br(f=f.lastElementChild||f)&&(f=f.lastElementChild||f);br(f)||(f=f.appendChild(t.createElement(u))),f.appendChild(d),e.parentNode.removeChild(e)}}}};function kr(e){return"OL"===e.nodeName||"UL"===e.nodeName}var wr=function(e){if(kr(e)){var t=e,r=e.previousElementSibling;if(r&&r.nodeName===e.nodeName&&1===t.children.length){for(;t.firstChild;)r.appendChild(t.firstChild);t.parentNode.removeChild(t)}var n,a=e.parentNode;if(a&&"LI"===a.nodeName&&1===a.children.length&&!/\S/.test((n=a,Object(s.a)(n.childNodes).map(function(e){var t=e.nodeValue;return void 0===t?"":t}).join("")))){var i=a,o=i.previousElementSibling,c=i.parentNode;o?(o.appendChild(t),c.removeChild(i)):(c.parentNode.insertBefore(t,c),c.parentNode.removeChild(c))}if(a&&kr(a)){var l=e.previousElementSibling;l?l.appendChild(e):Object(Kt.unwrap)(e)}}},vr=r(32),yr=window,jr=yr.atob,Or=yr.File,xr=function(e){if("IMG"===e.nodeName){if(0===e.src.indexOf("file:")&&(e.src=""),0===e.src.indexOf("data:")){var t,r=e.src.split(","),n=Object(xe.a)(r,2),a=n[0],i=n[1],o=a.slice(5).split(";"),s=Object(xe.a)(o,1)[0];if(!i||!s)return void(e.src="");try{t=jr(i)}catch(t){return void(e.src="")}for(var c=new Uint8Array(t.length),l=0;l<c.length;l++)c[l]=t.charCodeAt(l);var u=s.replace("/","."),d=new Or([c],u,{type:s});e.src=Object(vr.createBlobURL)(d)}1!==e.height&&1!==e.width||e.parentNode.removeChild(e)}},Cr=function(e){"BLOCKQUOTE"===e.nodeName&&(e.innerHTML=cr(e.innerHTML))};var Ar=function(e,t,r){if(function(e,t){var r=e.nodeName.toLowerCase();return"figcaption"!==r&&!Qt(e)&&Object(l.has)(t,["figure","children",r])}(e,r)){var n=e,a=e.parentNode;(function(e,t){var r=e.nodeName.toLowerCase();return Object(l.has)(t,["figure","children","a","children",r])})(e,r)&&"A"===a.nodeName&&1===a.childNodes.length&&(n=e.parentNode);for(var i=n;i&&"P"!==i.nodeName;)i=i.parentElement;var o=t.createElement("figure");i?i.parentNode.insertBefore(o,i):n.parentNode.insertBefore(o,n),o.appendChild(n)}},Sr=r(123);var Tr=function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=ye(je("from"),function(e){return"shortcode"===e.type&&Object(l.some)(Object(l.castArray)(e.tag),function(e){return Object(Sr.regexp)(e).test(t)})});if(!n)return[t];var a,i=Object(l.castArray)(n.tag),o=Object(l.first)(i);if(a=Object(Sr.next)(o,t,r)){var u=t.substr(0,a.index);if(r=a.index+a.content.length,!Object(l.includes)(a.shortcode.content||"","<")&&!/(\n|<p>)\s*$/.test(u))return e(t,r);var d=Object(l.mapValues)(Object(l.pickBy)(n.attributes,function(e){return e.shortcode}),function(e){return e.shortcode(a.shortcode.attrs,a)});return[u,me(n.blockName,qt(Object(c.a)({},oe(n.blockName),{attributes:n.attributes}),a.shortcode.content,d))].concat(Object(s.a)(e(t.substr(r))))}return[t]},Er=r(184),Pr=new(r.n(Er).a.Converter)({noHeaderId:!0,tables:!0,literalMidWordUnderscores:!0,omitExtraWLInCodeBlocks:!0,simpleLineBreaks:!0,strikethrough:!0});var Mr=function(e){return Pr.makeHtml(function(e){return e.replace(/((?:^|\n)```)([^\n`]+)(```(?:$|\n))/,function(e,t,r,n){return"".concat(t,"\n").concat(r,"\n").concat(n)})}(e))},Nr=function(e){"IFRAME"===e.nodeName&&Object(Kt.remove)(e)},Br=window.console;function Lr(e){return e=ar(e=nr(e,[pr]),Zt(),{inline:!0}),Br.log("Processed inline HTML:\n\n",e),e}function zr(){return Object(l.filter)(je("from"),{type:"raw"}).map(function(e){return e.isMatch?e:Object(c.a)({},e,{isMatch:function(t){return e.selector&&t.matches(e.selector)}})})}function Hr(e){var t=e.html,r=e.rawTransforms,n=document.implementation.createHTMLDocument("");return n.body.innerHTML=t,Array.from(n.body.children).map(function(e){var t=ye(r,function(t){return(0,t.isMatch)(e)});if(!t)return me("core/html",qt("core/html",e.outerHTML));var n=t.transform,a=t.blockName;return n?n(e):me(a,qt(a,e.outerHTML))})}function Dr(e){var t=e.HTML,r=void 0===t?"":t,n=e.plainText,a=void 0===n?"":n,i=e.mode,o=void 0===i?"AUTO":i,s=e.tagName,u=e.canUserUseUnfilteredHTML,d=void 0!==u&&u;if(r=r.replace(/<meta[^>]+>/,""),"INLINE"!==o&&-1!==r.indexOf("\x3c!-- wp:"))return Gt(r);if(String.prototype.normalize&&(r=r.normalize()),!a||r&&!function(e){return!/<(?!br[ \/>])/i.test(e)}(r)||(r=Mr(a),"AUTO"===o&&-1===a.indexOf("\n")&&0!==a.indexOf("<p>")&&0===r.indexOf("<p>")&&(o="INLINE")),"INLINE"===o)return Lr(r);var f=Tr(r),h=f.length>1;if("AUTO"===o&&!h&&hr(r,s))return Lr(r);var p=zr(),g=Zt(),m=tr(p),b=Object(l.compact)(Object(l.flatMap)(f,function(e){if("string"!=typeof e)return e;var t=[_r,gr,wr,xr,pr,ur,Ar,Cr];d||t.unshift(Nr);var r=Object(c.a)({},m,g);return e=ar(e=nr(e,t,m),r),e=cr(e),Br.log("Processed HTML piece:\n\n",e),Hr({html:e,rawTransforms:p})}));if("AUTO"===o&&1===b.length){var _=a.trim();if(""!==_&&-1===_.indexOf("\n"))return ar(it(b[0]),g)}return b}function Ir(e){var t=e.HTML,r=void 0===t?"":t;if(-1!==r.indexOf("\x3c!-- wp:"))return Gt(r);var n=Tr(r),a=zr(),i=tr(a);return Object(l.compact)(Object(l.flatMap)(n,function(e){return"string"!=typeof e?e:(e=nr(e,[wr,ur,Ar,Cr],i),Hr({html:e=cr(e),rawTransforms:a}))}))}function Vr(){return Object(i.select)("core/blocks").getCategories()}function Rr(e){Object(i.dispatch)("core/blocks").setCategories(e)}function Fr(e,t){Object(i.dispatch)("core/blocks").updateCategory(e,t)}function qr(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length===t.length&&Object(l.every)(t,function(t,r){var n=Object(xe.a)(t,3),a=n[0],i=n[2],o=e[r];return a===o.name&&qr(o.innerBlocks,i)})}function $r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;return t?Object(l.map)(t,function(t,r){var n=Object(xe.a)(t,3),a=n[0],i=n[1],o=n[2],s=e[r];if(s&&s.name===a){var u=$r(s.innerBlocks,o);return Object(c.a)({},s,{innerBlocks:u})}var d=oe(a),f=function(e,t){return Object(l.mapValues)(t,function(t,r){return h(e[r],t)})},h=function(e,t){return r=e,"html"===Object(l.get)(r,["source"])&&Object(l.isArray)(t)?Object($.renderToString)(t):function(e){return"query"===Object(l.get)(e,["source"])}(e)&&t?t.map(function(t){return f(e.query,t)}):t;var r};return me(a,f(Object(l.get)(d,["attributes"],{}),i),$r([],o))}):e}r.d(t,"createBlock",function(){return me}),r.d(t,"cloneBlock",function(){return be}),r.d(t,"getPossibleBlockTransformations",function(){return ve}),r.d(t,"switchToBlockType",function(){return Oe}),r.d(t,"getBlockTransforms",function(){return je}),r.d(t,"findTransform",function(){return ye}),r.d(t,"parse",function(){return Wt}),r.d(t,"getBlockAttributes",function(){return qt}),r.d(t,"parseWithAttributeSchema",function(){return Ft}),r.d(t,"pasteHandler",function(){return Dr}),r.d(t,"rawHandler",function(){return Ir}),r.d(t,"getPhrasingContentSchema",function(){return Zt}),r.d(t,"serialize",function(){return ct}),r.d(t,"getBlockContent",function(){return it}),r.d(t,"getBlockDefaultClassName",function(){return tt}),r.d(t,"getBlockMenuDefaultClassName",function(){return rt}),r.d(t,"getSaveElement",function(){return nt}),r.d(t,"getSaveContent",function(){return at}),r.d(t,"isValidBlockContent",function(){return At}),r.d(t,"getCategories",function(){return Vr}),r.d(t,"setCategories",function(){return Rr}),r.d(t,"updateCategory",function(){return Fr}),r.d(t,"registerBlockType",function(){return X}),r.d(t,"unregisterBlockType",function(){return J}),r.d(t,"setFreeformContentHandlerName",function(){return ee}),r.d(t,"getFreeformContentHandlerName",function(){return te}),r.d(t,"setUnregisteredTypeHandlerName",function(){return re}),r.d(t,"getUnregisteredTypeHandlerName",function(){return ne}),r.d(t,"setDefaultBlockName",function(){return ae}),r.d(t,"getDefaultBlockName",function(){return ie}),r.d(t,"getBlockType",function(){return oe}),r.d(t,"getBlockTypes",function(){return se}),r.d(t,"getBlockSupport",function(){return ce}),r.d(t,"hasBlockSupport",function(){return le}),r.d(t,"isReusableBlock",function(){return ue}),r.d(t,"getChildBlockNames",function(){return de}),r.d(t,"hasChildBlocks",function(){return fe}),r.d(t,"hasChildBlocksWithInserterSupport",function(){return he}),r.d(t,"unstable__bootstrapServerSideBlockDefinitions",function(){return Q}),r.d(t,"registerBlockStyle",function(){return pe}),r.d(t,"unregisterBlockStyle",function(){return ge}),r.d(t,"isUnmodifiedDefaultBlock",function(){return G}),r.d(t,"normalizeIconObject",function(){return K}),r.d(t,"isValidIcon",function(){return W}),r.d(t,"doBlocksMatchTemplate",function(){return qr}),r.d(t,"synchronizeBlocksWithTemplate",function(){return $r}),r.d(t,"children",function(){return Pt}),r.d(t,"node",function(){return Dt}),r.d(t,"withBlockContentContext",function(){return Je})},31:function(e,t,r){"use strict";var n,a;function i(e){return[e]}function o(){var e={clear:function(){e.head=null}};return e}function s(e,t,r){var n;if(e.length!==t.length)return!1;for(n=r;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}n={},a="undefined"!=typeof WeakMap,t.a=function(e,t){var r,c;function l(){r=a?new WeakMap:o()}function u(){var r,n,a,i,o,l=arguments.length;for(i=new Array(l),a=0;a<l;a++)i[a]=arguments[a];for(o=t.apply(null,i),(r=c(o)).isUniqueByDependants||(r.lastDependants&&!s(o,r.lastDependants,0)&&r.clear(),r.lastDependants=o),n=r.head;n;){if(s(n.args,i,1))return n!==r.head&&(n.prev.next=n.next,n.next&&(n.next.prev=n.prev),n.next=r.head,n.prev=null,r.head.prev=n,r.head=n),n.val;n=n.next}return n={val:e.apply(null,i)},i[0]=null,n.args=i,r.head&&(r.head.prev=n,n.next=r.head),r.head=n,n.val}return t||(t=i),c=a?function(e){var t,a,i,s,c,l=r,u=!0;for(t=0;t<e.length;t++){if(a=e[t],!(c=a)||"object"!=typeof c){u=!1;break}l.has(a)?l=l.get(a):(i=new WeakMap,l.set(a,i),l=i)}return l.has(n)||((s=o()).isUniqueByDependants=u,l.set(n,s)),l.get(n)}:function(){return r},u.getDependants=t,u.clear=l,l(),u}},32:function(e,t){!function(){e.exports=this.wp.blob}()},33:function(e,t,r){"use strict";function n(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}r.d(t,"a",function(){return n})},35:function(e,t,r){"use strict";function n(e){if(Array.isArray(e))return e}r.d(t,"a",function(){return n})},36:function(e,t,r){"use strict";function n(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}r.d(t,"a",function(){return n})},40:function(e,t){!function(){e.exports=this.wp.isShallowEqual}()},45:function(e,t,r){var n;!function(a){var i=/^\s+/,o=/\s+$/,s=0,c=a.round,l=a.min,u=a.max,d=a.random;function f(e,t){if(t=t||{},(e=e||"")instanceof f)return e;if(!(this instanceof f))return new f(e,t);var r=function(e){var t={r:0,g:0,b:0},r=1,n=null,s=null,c=null,d=!1,f=!1;"string"==typeof e&&(e=function(e){e=e.replace(i,"").replace(o,"").toLowerCase();var t,r=!1;if(E[e])e=E[e],r=!0;else if("transparent"==e)return{r:0,g:0,b:0,a:0,format:"name"};if(t=q.rgb.exec(e))return{r:t[1],g:t[2],b:t[3]};if(t=q.rgba.exec(e))return{r:t[1],g:t[2],b:t[3],a:t[4]};if(t=q.hsl.exec(e))return{h:t[1],s:t[2],l:t[3]};if(t=q.hsla.exec(e))return{h:t[1],s:t[2],l:t[3],a:t[4]};if(t=q.hsv.exec(e))return{h:t[1],s:t[2],v:t[3]};if(t=q.hsva.exec(e))return{h:t[1],s:t[2],v:t[3],a:t[4]};if(t=q.hex8.exec(e))return{r:L(t[1]),g:L(t[2]),b:L(t[3]),a:I(t[4]),format:r?"name":"hex8"};if(t=q.hex6.exec(e))return{r:L(t[1]),g:L(t[2]),b:L(t[3]),format:r?"name":"hex"};if(t=q.hex4.exec(e))return{r:L(t[1]+""+t[1]),g:L(t[2]+""+t[2]),b:L(t[3]+""+t[3]),a:I(t[4]+""+t[4]),format:r?"name":"hex8"};if(t=q.hex3.exec(e))return{r:L(t[1]+""+t[1]),g:L(t[2]+""+t[2]),b:L(t[3]+""+t[3]),format:r?"name":"hex"};return!1}(e));"object"==typeof e&&($(e.r)&&$(e.g)&&$(e.b)?(h=e.r,p=e.g,g=e.b,t={r:255*N(h,255),g:255*N(p,255),b:255*N(g,255)},d=!0,f="%"===String(e.r).substr(-1)?"prgb":"rgb"):$(e.h)&&$(e.s)&&$(e.v)?(n=H(e.s),s=H(e.v),t=function(e,t,r){e=6*N(e,360),t=N(t,100),r=N(r,100);var n=a.floor(e),i=e-n,o=r*(1-t),s=r*(1-i*t),c=r*(1-(1-i)*t),l=n%6;return{r:255*[r,s,o,o,c,r][l],g:255*[c,r,r,s,o,o][l],b:255*[o,o,c,r,r,s][l]}}(e.h,n,s),d=!0,f="hsv"):$(e.h)&&$(e.s)&&$(e.l)&&(n=H(e.s),c=H(e.l),t=function(e,t,r){var n,a,i;function o(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}if(e=N(e,360),t=N(t,100),r=N(r,100),0===t)n=a=i=r;else{var s=r<.5?r*(1+t):r+t-r*t,c=2*r-s;n=o(c,s,e+1/3),a=o(c,s,e),i=o(c,s,e-1/3)}return{r:255*n,g:255*a,b:255*i}}(e.h,n,c),d=!0,f="hsl"),e.hasOwnProperty("a")&&(r=e.a));var h,p,g;return r=M(r),{ok:d,format:e.format||f,r:l(255,u(t.r,0)),g:l(255,u(t.g,0)),b:l(255,u(t.b,0)),a:r}}(e);this._originalInput=e,this._r=r.r,this._g=r.g,this._b=r.b,this._a=r.a,this._roundA=c(100*this._a)/100,this._format=t.format||r.format,this._gradientType=t.gradientType,this._r<1&&(this._r=c(this._r)),this._g<1&&(this._g=c(this._g)),this._b<1&&(this._b=c(this._b)),this._ok=r.ok,this._tc_id=s++}function h(e,t,r){e=N(e,255),t=N(t,255),r=N(r,255);var n,a,i=u(e,t,r),o=l(e,t,r),s=(i+o)/2;if(i==o)n=a=0;else{var c=i-o;switch(a=s>.5?c/(2-i-o):c/(i+o),i){case e:n=(t-r)/c+(t<r?6:0);break;case t:n=(r-e)/c+2;break;case r:n=(e-t)/c+4}n/=6}return{h:n,s:a,l:s}}function p(e,t,r){e=N(e,255),t=N(t,255),r=N(r,255);var n,a,i=u(e,t,r),o=l(e,t,r),s=i,c=i-o;if(a=0===i?0:c/i,i==o)n=0;else{switch(i){case e:n=(t-r)/c+(t<r?6:0);break;case t:n=(r-e)/c+2;break;case r:n=(e-t)/c+4}n/=6}return{h:n,s:a,v:s}}function g(e,t,r,n){var a=[z(c(e).toString(16)),z(c(t).toString(16)),z(c(r).toString(16))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function m(e,t,r,n){return[z(D(n)),z(c(e).toString(16)),z(c(t).toString(16)),z(c(r).toString(16))].join("")}function b(e,t){t=0===t?0:t||10;var r=f(e).toHsl();return r.s-=t/100,r.s=B(r.s),f(r)}function _(e,t){t=0===t?0:t||10;var r=f(e).toHsl();return r.s+=t/100,r.s=B(r.s),f(r)}function k(e){return f(e).desaturate(100)}function w(e,t){t=0===t?0:t||10;var r=f(e).toHsl();return r.l+=t/100,r.l=B(r.l),f(r)}function v(e,t){t=0===t?0:t||10;var r=f(e).toRgb();return r.r=u(0,l(255,r.r-c(-t/100*255))),r.g=u(0,l(255,r.g-c(-t/100*255))),r.b=u(0,l(255,r.b-c(-t/100*255))),f(r)}function y(e,t){t=0===t?0:t||10;var r=f(e).toHsl();return r.l-=t/100,r.l=B(r.l),f(r)}function j(e,t){var r=f(e).toHsl(),n=(r.h+t)%360;return r.h=n<0?360+n:n,f(r)}function O(e){var t=f(e).toHsl();return t.h=(t.h+180)%360,f(t)}function x(e){var t=f(e).toHsl(),r=t.h;return[f(e),f({h:(r+120)%360,s:t.s,l:t.l}),f({h:(r+240)%360,s:t.s,l:t.l})]}function C(e){var t=f(e).toHsl(),r=t.h;return[f(e),f({h:(r+90)%360,s:t.s,l:t.l}),f({h:(r+180)%360,s:t.s,l:t.l}),f({h:(r+270)%360,s:t.s,l:t.l})]}function A(e){var t=f(e).toHsl(),r=t.h;return[f(e),f({h:(r+72)%360,s:t.s,l:t.l}),f({h:(r+216)%360,s:t.s,l:t.l})]}function S(e,t,r){t=t||6,r=r||30;var n=f(e).toHsl(),a=360/r,i=[f(e)];for(n.h=(n.h-(a*t>>1)+720)%360;--t;)n.h=(n.h+a)%360,i.push(f(n));return i}function T(e,t){t=t||6;for(var r=f(e).toHsv(),n=r.h,a=r.s,i=r.v,o=[],s=1/t;t--;)o.push(f({h:n,s:a,v:i})),i=(i+s)%1;return o}f.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},getLuminance:function(){var e,t,r,n=this.toRgb();return e=n.r/255,t=n.g/255,r=n.b/255,.2126*(e<=.03928?e/12.92:a.pow((e+.055)/1.055,2.4))+.7152*(t<=.03928?t/12.92:a.pow((t+.055)/1.055,2.4))+.0722*(r<=.03928?r/12.92:a.pow((r+.055)/1.055,2.4))},setAlpha:function(e){return this._a=M(e),this._roundA=c(100*this._a)/100,this},toHsv:function(){var e=p(this._r,this._g,this._b);return{h:360*e.h,s:e.s,v:e.v,a:this._a}},toHsvString:function(){var e=p(this._r,this._g,this._b),t=c(360*e.h),r=c(100*e.s),n=c(100*e.v);return 1==this._a?"hsv("+t+", "+r+"%, "+n+"%)":"hsva("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHsl:function(){var e=h(this._r,this._g,this._b);return{h:360*e.h,s:e.s,l:e.l,a:this._a}},toHslString:function(){var e=h(this._r,this._g,this._b),t=c(360*e.h),r=c(100*e.s),n=c(100*e.l);return 1==this._a?"hsl("+t+", "+r+"%, "+n+"%)":"hsla("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHex:function(e){return g(this._r,this._g,this._b,e)},toHexString:function(e){return"#"+this.toHex(e)},toHex8:function(e){return function(e,t,r,n,a){var i=[z(c(e).toString(16)),z(c(t).toString(16)),z(c(r).toString(16)),z(D(n))];if(a&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1))return i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0);return i.join("")}(this._r,this._g,this._b,this._a,e)},toHex8String:function(e){return"#"+this.toHex8(e)},toRgb:function(){return{r:c(this._r),g:c(this._g),b:c(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+c(this._r)+", "+c(this._g)+", "+c(this._b)+")":"rgba("+c(this._r)+", "+c(this._g)+", "+c(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:c(100*N(this._r,255))+"%",g:c(100*N(this._g,255))+"%",b:c(100*N(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+c(100*N(this._r,255))+"%, "+c(100*N(this._g,255))+"%, "+c(100*N(this._b,255))+"%)":"rgba("+c(100*N(this._r,255))+"%, "+c(100*N(this._g,255))+"%, "+c(100*N(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(P[g(this._r,this._g,this._b,!0)]||!1)},toFilter:function(e){var t="#"+m(this._r,this._g,this._b,this._a),r=t,n=this._gradientType?"GradientType = 1, ":"";if(e){var a=f(e);r="#"+m(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+t+",endColorstr="+r+")"},toString:function(e){var t=!!e;e=e||this._format;var r=!1,n=this._a<1&&this._a>=0;return t||!n||"hex"!==e&&"hex6"!==e&&"hex3"!==e&&"hex4"!==e&&"hex8"!==e&&"name"!==e?("rgb"===e&&(r=this.toRgbString()),"prgb"===e&&(r=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(r=this.toHexString()),"hex3"===e&&(r=this.toHexString(!0)),"hex4"===e&&(r=this.toHex8String(!0)),"hex8"===e&&(r=this.toHex8String()),"name"===e&&(r=this.toName()),"hsl"===e&&(r=this.toHslString()),"hsv"===e&&(r=this.toHsvString()),r||this.toHexString()):"name"===e&&0===this._a?this.toName():this.toRgbString()},clone:function(){return f(this.toString())},_applyModification:function(e,t){var r=e.apply(null,[this].concat([].slice.call(t)));return this._r=r._r,this._g=r._g,this._b=r._b,this.setAlpha(r._a),this},lighten:function(){return this._applyModification(w,arguments)},brighten:function(){return this._applyModification(v,arguments)},darken:function(){return this._applyModification(y,arguments)},desaturate:function(){return this._applyModification(b,arguments)},saturate:function(){return this._applyModification(_,arguments)},greyscale:function(){return this._applyModification(k,arguments)},spin:function(){return this._applyModification(j,arguments)},_applyCombination:function(e,t){return e.apply(null,[this].concat([].slice.call(t)))},analogous:function(){return this._applyCombination(S,arguments)},complement:function(){return this._applyCombination(O,arguments)},monochromatic:function(){return this._applyCombination(T,arguments)},splitcomplement:function(){return this._applyCombination(A,arguments)},triad:function(){return this._applyCombination(x,arguments)},tetrad:function(){return this._applyCombination(C,arguments)}},f.fromRatio=function(e,t){if("object"==typeof e){var r={};for(var n in e)e.hasOwnProperty(n)&&(r[n]="a"===n?e[n]:H(e[n]));e=r}return f(e,t)},f.equals=function(e,t){return!(!e||!t)&&f(e).toRgbString()==f(t).toRgbString()},f.random=function(){return f.fromRatio({r:d(),g:d(),b:d()})},f.mix=function(e,t,r){r=0===r?0:r||50;var n=f(e).toRgb(),a=f(t).toRgb(),i=r/100;return f({r:(a.r-n.r)*i+n.r,g:(a.g-n.g)*i+n.g,b:(a.b-n.b)*i+n.b,a:(a.a-n.a)*i+n.a})},f.readability=function(e,t){var r=f(e),n=f(t);return(a.max(r.getLuminance(),n.getLuminance())+.05)/(a.min(r.getLuminance(),n.getLuminance())+.05)},f.isReadable=function(e,t,r){var n,a,i=f.readability(e,t);switch(a=!1,(n=function(e){var t,r;t=((e=e||{level:"AA",size:"small"}).level||"AA").toUpperCase(),r=(e.size||"small").toLowerCase(),"AA"!==t&&"AAA"!==t&&(t="AA");"small"!==r&&"large"!==r&&(r="small");return{level:t,size:r}}(r)).level+n.size){case"AAsmall":case"AAAlarge":a=i>=4.5;break;case"AAlarge":a=i>=3;break;case"AAAsmall":a=i>=7}return a},f.mostReadable=function(e,t,r){var n,a,i,o,s=null,c=0;a=(r=r||{}).includeFallbackColors,i=r.level,o=r.size;for(var l=0;l<t.length;l++)(n=f.readability(e,t[l]))>c&&(c=n,s=f(t[l]));return f.isReadable(e,s,{level:i,size:o})||!a?s:(r.includeFallbackColors=!1,f.mostReadable(e,["#fff","#000"],r))};var E=f.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},P=f.hexNames=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t}(E);function M(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function N(e,t){(function(e){return"string"==typeof e&&-1!=e.indexOf(".")&&1===parseFloat(e)})(e)&&(e="100%");var r=function(e){return"string"==typeof e&&-1!=e.indexOf("%")}(e);return e=l(t,u(0,parseFloat(e))),r&&(e=parseInt(e*t,10)/100),a.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function B(e){return l(1,u(0,e))}function L(e){return parseInt(e,16)}function z(e){return 1==e.length?"0"+e:""+e}function H(e){return e<=1&&(e=100*e+"%"),e}function D(e){return a.round(255*parseFloat(e)).toString(16)}function I(e){return L(e)/255}var V,R,F,q=(R="[\\s|\\(]+("+(V="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+V+")[,|\\s]+("+V+")\\s*\\)?",F="[\\s|\\(]+("+V+")[,|\\s]+("+V+")[,|\\s]+("+V+")[,|\\s]+("+V+")\\s*\\)?",{CSS_UNIT:new RegExp(V),rgb:new RegExp("rgb"+R),rgba:new RegExp("rgba"+F),hsl:new RegExp("hsl"+R),hsla:new RegExp("hsla"+F),hsv:new RegExp("hsv"+R),hsva:new RegExp("hsva"+F),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function $(e){return!!q.CSS_UNIT.exec(e)}e.exports?e.exports=f:void 0===(n=function(){return f}.call(t,r,t,e))||(e.exports=n)}(Math)},49:function(e,t){!function(){e.exports=this.wp.htmlEntities}()},5:function(e,t){!function(){e.exports=this.wp.data}()},57:function(e,t,r){var n=r(77),a=r(78);e.exports=function(e,t,r){var i=t&&r||0;"string"==typeof e&&(t="binary"===e?new Array(16):null,e=null);var o=(e=e||{}).random||(e.rng||n)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t)for(var s=0;s<16;++s)t[i+s]=o[s];return t||a(o)}},58:function(e,t){!function(){e.exports=this.wp.autop}()},7:function(e,t){!function(){e.exports=this.wp.compose}()},77:function(e,t){var r="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto);if(r){var n=new Uint8Array(16);e.exports=function(){return r(n),n}}else{var a=new Array(16);e.exports=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),a[t]=e>>>((3&t)<<3)&255;return a}}},78:function(e,t){for(var r=[],n=0;n<256;++n)r[n]=(n+256).toString(16).substr(1);e.exports=function(e,t){var n=t||0,a=r;return[a[e[n++]],a[e[n++]],a[e[n++]],a[e[n++]],"-",a[e[n++]],a[e[n++]],"-",a[e[n++]],a[e[n++]],"-",a[e[n++]],a[e[n++]],"-",a[e[n++]],a[e[n++]],a[e[n++]],a[e[n++]],a[e[n++]],a[e[n++]]].join("")}},8:function(e,t,r){"use strict";r.d(t,"a",function(){return a});var n=r(15);function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),a.forEach(function(t){Object(n.a)(e,t,r[t])})}return e}},9:function(e,t,r){"use strict";function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}r.d(t,"a",function(){return a})}});