this.wp=this.wp||{},this.wp.editPost=function(e){var t={};function n(o){if(t[o])return t[o].exports;var i=t[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(o,i,function(t){return e[t]}.bind(null,i));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=305)}({0:function(e,t){!function(){e.exports=this.wp.element}()},1:function(e,t){!function(){e.exports=this.wp.i18n}()},10:function(e,t,n){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,"a",function(){return o})},11:function(e,t){!function(){e.exports=this.wp.blocks}()},12:function(e,t,n){"use strict";n.d(t,"a",function(){return r});var o=n(28),i=n(3);function r(e,t){return!t||"object"!==Object(o.a)(t)&&"function"!=typeof t?Object(i.a)(e):t}},121:function(e,t){!function(){e.exports=this.wp.notices}()},13:function(e,t,n){"use strict";function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}n.d(t,"a",function(){return o})},14:function(e,t,n){"use strict";function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&o(e,t)}n.d(t,"a",function(){return i})},15:function(e,t,n){"use strict";function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",function(){return o})},16:function(e,t){!function(){e.exports=this.wp.keycodes}()},17:function(e,t,n){var o;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var o=arguments[t];if(o){var r=typeof o;if("string"===r||"number"===r)e.push(o);else if(Array.isArray(o)&&o.length){var c=i.apply(null,o);c&&e.push(c)}else if("object"===r)for(var a in o)n.call(o,a)&&o[a]&&e.push(a)}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(o=function(){return i}.apply(t,[]))||(e.exports=o)}()},18:function(e,t,n){"use strict";function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}n.d(t,"a",function(){return o})},189:function(e,t){!function(){e.exports=this.wp.blockLibrary}()},19:function(e,t,n){"use strict";var o=n(33);function i(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||Object(o.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}n.d(t,"a",function(){return i})},2:function(e,t){!function(){e.exports=this.lodash}()},21:function(e,t,n){"use strict";function o(e,t){if(null==e)return{};var n,o,i=function(e,t){if(null==e)return{};var n,o,i={},r=Object.keys(e);for(o=0;o<r.length;o++)n=r[o],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)n=r[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}n.d(t,"a",function(){return o})},23:function(e,t){!function(){e.exports=this.wp.hooks}()},24:function(e,t){!function(){e.exports=this.wp.url}()},25:function(e,t,n){"use strict";var o=n(35);var i=n(36);function r(e,t){return Object(o.a)(e)||function(e,t){var n=[],o=!0,i=!1,r=void 0;try{for(var c,a=e[Symbol.iterator]();!(o=(c=a.next()).done)&&(n.push(c.value),!t||n.length!==t);o=!0);}catch(e){i=!0,r=e}finally{try{o||null==a.return||a.return()}finally{if(i)throw r}}return n}(e,t)||Object(i.a)()}n.d(t,"a",function(){return r})},28:function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e){return(i="function"==typeof Symbol&&"symbol"===o(Symbol.iterator)?function(e){return o(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":o(e)})(e)}n.d(t,"a",function(){return i})},3:function(e,t,n){"use strict";function o(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n.d(t,"a",function(){return o})},30:function(e,t){!function(){e.exports=this.wp.apiFetch}()},305:function(e,t,n){"use strict";n.r(t);var o={};n.r(o),n.d(o,"openGeneralSidebar",function(){return ee}),n.d(o,"closeGeneralSidebar",function(){return te}),n.d(o,"openModal",function(){return ne}),n.d(o,"closeModal",function(){return oe}),n.d(o,"openPublishSidebar",function(){return ie}),n.d(o,"closePublishSidebar",function(){return re}),n.d(o,"togglePublishSidebar",function(){return ce}),n.d(o,"toggleEditorPanelEnabled",function(){return ae}),n.d(o,"toggleEditorPanelOpened",function(){return le}),n.d(o,"removeEditorPanel",function(){return se}),n.d(o,"toggleFeature",function(){return ue}),n.d(o,"switchEditorMode",function(){return de}),n.d(o,"togglePinnedPluginItem",function(){return be}),n.d(o,"setAvailableMetaBoxesPerLocation",function(){return pe}),n.d(o,"requestMetaBoxUpdates",function(){return me}),n.d(o,"metaBoxUpdatesSuccess",function(){return Oe});var i={};n.r(i),n.d(i,"getEditorMode",function(){return je}),n.d(i,"isEditorSidebarOpened",function(){return he}),n.d(i,"isPluginSidebarOpened",function(){return ge}),n.d(i,"getActiveGeneralSidebarName",function(){return Ee}),n.d(i,"getPreferences",function(){return ve}),n.d(i,"getPreference",function(){return _e}),n.d(i,"isPublishSidebarOpened",function(){return ye}),n.d(i,"isEditorPanelRemoved",function(){return Se}),n.d(i,"isEditorPanelEnabled",function(){return Pe}),n.d(i,"isEditorPanelOpened",function(){return ke}),n.d(i,"isModalActive",function(){return we}),n.d(i,"isFeatureActive",function(){return Ce}),n.d(i,"isPluginItemPinned",function(){return xe}),n.d(i,"getActiveMetaBoxLocations",function(){return Me}),n.d(i,"isMetaBoxLocationVisible",function(){return Te}),n.d(i,"isMetaBoxLocationActive",function(){return Ne}),n.d(i,"getMetaBoxesPerLocation",function(){return Ae}),n.d(i,"getAllMetaBoxes",function(){return Be}),n.d(i,"hasMetaBoxes",function(){return Ie}),n.d(i,"isSavingMetaBoxes",function(){return Le});var r=n(0),c=(n(79),n(6)),a=n(52),l=n(37),s=(n(121),n(189)),u=n(5),d=n(23),b=n(10),p=n(9),m=n(12),O=n(13),f=n(14),j=n(3),h=n(2),g=n(1),E=window.wp,v=function(){return E.media.view.MediaFrame.Post.extend({createStates:function(){this.states.add([new E.media.controller.Library({id:"gallery",title:E.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:E.media.query(Object(h.defaults)({type:"image"},this.options.library))}),new E.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery",displaySettings:!1,multiple:!0}),new E.media.controller.GalleryAdd])}})},_=function(e){return Object(h.pick)(e,["sizes","mime","type","subtype","id","url","alt","link","caption"])},y=function(e){return E.media.query({order:"ASC",orderby:"post__in",post__in:e,posts_per_page:-1,query:!0,type:"image"})},S=function(e){function t(e){var n,o=e.allowedTypes,i=e.multiple,r=void 0!==i&&i,c=e.gallery,a=void 0!==c&&c,l=e.title,s=void 0===l?Object(g.__)("Select or Upload Media"):l,u=e.modalClass,d=e.value;if(Object(b.a)(this,t),(n=Object(m.a)(this,Object(O.a)(t).apply(this,arguments))).openModal=n.openModal.bind(Object(j.a)(Object(j.a)(n))),n.onOpen=n.onOpen.bind(Object(j.a)(Object(j.a)(n))),n.onSelect=n.onSelect.bind(Object(j.a)(Object(j.a)(n))),n.onUpdate=n.onUpdate.bind(Object(j.a)(Object(j.a)(n))),n.onClose=n.onClose.bind(Object(j.a)(Object(j.a)(n))),a){var p=d?"gallery-edit":"gallery",f=v(),h=y(d),_=new E.media.model.Selection(h.models,{props:h.props.toJSON(),multiple:r});n.frame=new f({mimeType:o,state:p,multiple:r,selection:_,editing:!!d}),E.media.frame=n.frame}else{var S={title:s,button:{text:Object(g.__)("Select")},multiple:r};o&&(S.library={type:o}),n.frame=E.media(S)}return u&&n.frame.$el.addClass(u),n.frame.on("select",n.onSelect),n.frame.on("update",n.onUpdate),n.frame.on("open",n.onOpen),n.frame.on("close",n.onClose),n}return Object(f.a)(t,e),Object(p.a)(t,[{key:"componentWillUnmount",value:function(){this.frame.remove()}},{key:"onUpdate",value:function(e){var t=this.props,n=t.onSelect,o=t.multiple,i=void 0!==o&&o,r=this.frame.state(),c=e||r.get("selection");c&&c.models.length&&n(i?c.models.map(function(e){return _(e.toJSON())}):_(c.models[0].toJSON()))}},{key:"onSelect",value:function(){var e=this.props,t=e.onSelect,n=e.multiple,o=void 0!==n&&n,i=this.frame.state().get("selection").toJSON();t(o?i:i[0])}},{key:"onOpen",value:function(){if(this.updateCollection(),this.props.value){if(!this.props.gallery){var e=this.frame.state().get("selection");Object(h.castArray)(this.props.value).map(function(t){e.add(E.media.attachment(t))})}y(Object(h.castArray)(this.props.value)).more()}}},{key:"onClose",value:function(){var e=this.props.onClose;e&&e()}},{key:"updateCollection",value:function(){var e=this.frame.content.get();if(e&&e.collection){var t=e.collection;t.toArray().forEach(function(e){return e.trigger("destroy",e)}),t.mirroring._hasMore=!0,t.more()}}},{key:"openModal",value:function(){this.frame.open()}},{key:"render",value:function(){return this.props.render({open:this.openModal})}}]),t}(r.Component);Object(d.addFilter)("editor.MediaUpload","core/edit-post/components/media-upload/replace-media-upload",function(){return S});var P=n(18),k=n(21),w=n(11),C=n(4),x=n(7),M=Object(x.compose)(Object(u.withSelect)(function(e,t){if(Object(w.hasBlockSupport)(t.name,"multiple",!0))return{};var n=e("core/editor").getBlocks(),o=Object(h.find)(n,function(e){var n=e.name;return t.name===n});return{originalBlockClientId:o&&o.clientId!==t.clientId&&o.clientId}}),Object(u.withDispatch)(function(e,t){var n=t.originalBlockClientId;return{selectFirst:function(){return e("core/editor").selectBlock(n)}}})),T=Object(x.createHigherOrderComponent)(function(e){return M(function(t){var n=t.originalBlockClientId,o=t.selectFirst,i=Object(k.a)(t,["originalBlockClientId","selectFirst"]);if(!n)return Object(r.createElement)(e,i);var a=Object(w.getBlockType)(i.name),l=function(e){var t=Object(w.findTransform)(Object(w.getBlockTransforms)("to",e),function(e){var t=e.type,n=e.blocks;return"block"===t&&1===n.length});if(!t)return null;return Object(w.getBlockType)(t.blocks[0])}(i.name);return[Object(r.createElement)("div",{key:"invalid-preview",style:{minHeight:"60px"}},Object(r.createElement)(e,Object(P.a)({key:"block-edit"},i))),Object(r.createElement)(c.Warning,{key:"multiple-use-warning",actions:[Object(r.createElement)(C.Button,{key:"find-original",isLarge:!0,onClick:o},Object(g.__)("Find original")),Object(r.createElement)(C.Button,{key:"remove",isLarge:!0,onClick:function(){return i.onReplace([])}},Object(g.__)("Remove")),l&&Object(r.createElement)(C.Button,{key:"transform",isLarge:!0,onClick:function(){return i.onReplace(Object(w.createBlock)(l.name,i.attributes))}},Object(g.__)("Transform into:")," ",l.title)]},Object(r.createElement)("strong",null,a.title,": "),Object(g.__)("This block can only be used once."))]})},"withMultipleValidation");Object(d.addFilter)("editor.BlockEdit","core/edit-post/validate-multiple-use/with-multiple-validation",T);var N=n(54);var A=Object(x.compose)(Object(u.withSelect)(function(e){return{editedPostContent:e("core/editor").getEditedPostAttribute("content")}}),Object(x.withState)({hasCopied:!1}))(function(e){var t=e.editedPostContent,n=e.hasCopied,o=e.setState;return Object(r.createElement)(C.ClipboardButton,{text:t,className:"components-menu-item__button",onCopy:function(){return o({hasCopied:!0})},onFinishCopy:function(){return o({hasCopied:!1})}},n?Object(g.__)("Copied!"):Object(g.__)("Copy All Content"))}),B=n(16);var I=Object(u.withDispatch)(function(e){return{openModal:e("core/edit-post").openModal}})(function(e){var t=e.openModal,n=e.onSelect;return Object(r.createElement)(C.MenuItem,{onClick:function(){n(),t("edit-post/keyboard-shortcut-help")},shortcut:B.displayShortcut.access("h")},Object(g.__)("Keyboard Shortcuts"))}),L=Object(C.createSlotFill)("ToolsMoreMenuGroup"),D=L.Fill,F=L.Slot;D.Slot=function(e){var t=e.fillProps;return Object(r.createElement)(F,{fillProps:t},function(e){return!Object(h.isEmpty)(e)&&Object(r.createElement)(C.MenuGroup,{label:Object(g.__)("Tools")},e)})};var R=D;Object(N.registerPlugin)("edit-post",{render:function(){return Object(r.createElement)(r.Fragment,null,Object(r.createElement)(R,null,function(e){var t=e.onClose;return Object(r.createElement)(r.Fragment,null,Object(r.createElement)(C.MenuItem,{role:"menuitem",href:"edit.php?post_type=wp_block"},Object(g.__)("Manage All Reusable Blocks")),Object(r.createElement)(I,{onSelect:t}),Object(r.createElement)(A,null))}))}});var G=n(19),U=n(15),V=n(8),H={editorMode:"visual",isGeneralSidebarDismissed:!1,panels:{"post-status":{opened:!0}},features:{fixedToolbar:!1},pinnedPluginItems:{}},W="edit-post/document",q=Object(u.combineReducers)({isGeneralSidebarDismissed:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"OPEN_GENERAL_SIDEBAR":case"CLOSE_GENERAL_SIDEBAR":return"CLOSE_GENERAL_SIDEBAR"===t.type}return e},panels:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:H.panels,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"TOGGLE_PANEL_ENABLED":var n=t.panelName;return Object(V.a)({},e,Object(U.a)({},n,Object(V.a)({},e[n],{enabled:!Object(h.get)(e,[n,"enabled"],!0)})));case"TOGGLE_PANEL_OPENED":var o=t.panelName,i=!0===e[o]||Object(h.get)(e,[o,"opened"],!1);return Object(V.a)({},e,Object(U.a)({},o,Object(V.a)({},e[o],{opened:!i})))}return e},features:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:H.features,t=arguments.length>1?arguments[1]:void 0;return"TOGGLE_FEATURE"===t.type?Object(V.a)({},e,Object(U.a)({},t.feature,!e[t.feature])):e},editorMode:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:H.editorMode,t=arguments.length>1?arguments[1]:void 0;return"SWITCH_MODE"===t.type?t.mode:e},pinnedPluginItems:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:H.pinnedPluginItems,t=arguments.length>1?arguments[1]:void 0;return"TOGGLE_PINNED_PLUGIN_ITEM"===t.type?Object(V.a)({},e,Object(U.a)({},t.pluginName,!Object(h.get)(e,[t.pluginName],!0))):e}});var Q=Object(u.combineReducers)({isSaving:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];switch((arguments.length>1?arguments[1]:void 0).type){case"REQUEST_META_BOX_UPDATES":return!0;case"META_BOX_UPDATES_SUCCESS":return!1;default:return e}},locations:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_META_BOXES_PER_LOCATIONS":return t.metaBoxesPerLocation}return e}}),X=Object(u.combineReducers)({activeGeneralSidebar:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:W,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"OPEN_GENERAL_SIDEBAR":return t.name}return e},activeModal:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e},metaBoxes:Q,preferences:q,publishSidebarActive:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];switch((arguments.length>1?arguments[1]:void 0).type){case"OPEN_PUBLISH_SIDEBAR":return!0;case"CLOSE_PUBLISH_SIDEBAR":return!1;case"TOGGLE_PUBLISH_SIDEBAR":return!e}return e},removedPanels:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"REMOVE_PANEL":if(!Object(h.includes)(e,t.panelName))return Object(G.a)(e).concat([t.panelName])}return e}}),K=n(87),z=n.n(K),J=n(25),Y=n(44),$=n(30),Z=n.n($);function ee(e){return{type:"OPEN_GENERAL_SIDEBAR",name:e}}function te(){return{type:"CLOSE_GENERAL_SIDEBAR"}}function ne(e){return{type:"OPEN_MODAL",name:e}}function oe(){return{type:"CLOSE_MODAL"}}function ie(){return{type:"OPEN_PUBLISH_SIDEBAR"}}function re(){return{type:"CLOSE_PUBLISH_SIDEBAR"}}function ce(){return{type:"TOGGLE_PUBLISH_SIDEBAR"}}function ae(e){return{type:"TOGGLE_PANEL_ENABLED",panelName:e}}function le(e){return{type:"TOGGLE_PANEL_OPENED",panelName:e}}function se(e){return{type:"REMOVE_PANEL",panelName:e}}function ue(e){return{type:"TOGGLE_FEATURE",feature:e}}function de(e){return{type:"SWITCH_MODE",mode:e}}function be(e){return{type:"TOGGLE_PINNED_PLUGIN_ITEM",pluginName:e}}function pe(e){return{type:"SET_META_BOXES_PER_LOCATIONS",metaBoxesPerLocation:e}}function me(){return{type:"REQUEST_META_BOX_UPDATES"}}function Oe(){return{type:"META_BOX_UPDATES_SUCCESS"}}var fe=n(31);function je(e){return _e(e,"editorMode","visual")}function he(e){var t=Ee(e);return Object(h.includes)(["edit-post/document","edit-post/block"],t)}function ge(e){return!!Ee(e)&&!he(e)}function Ee(e){return _e(e,"isGeneralSidebarDismissed",!1)?null:e.activeGeneralSidebar}function ve(e){return e.preferences}function _e(e,t,n){var o=ve(e)[t];return void 0===o?n:o}function ye(e){return e.publishSidebarActive}function Se(e,t){return Object(h.includes)(e.removedPanels,t)}function Pe(e,t){var n=_e(e,"panels");return!Se(e,t)&&Object(h.get)(n,[t,"enabled"],!0)}function ke(e,t){var n=_e(e,"panels");return!0===n[t]||Object(h.get)(n,[t,"opened"],!1)}function we(e,t){return e.activeModal===t}function Ce(e,t){return!!e.preferences.features[t]}function xe(e,t){var n=_e(e,"pinnedPluginItems",{});return Object(h.get)(n,[t],!0)}var Me=Object(fe.a)(function(e){return Object.keys(e.metaBoxes.locations).filter(function(t){return Ne(e,t)})},function(e){return[e.metaBoxes.locations]});function Te(e,t){return Ne(e,t)&&Object(h.some)(Ae(e,t),function(t){var n=t.id;return Pe(e,"meta-box-".concat(n))})}function Ne(e,t){var n=Ae(e,t);return!!n&&0!==n.length}function Ae(e,t){return e.metaBoxes.locations[t]}var Be=Object(fe.a)(function(e){return Object(h.flatten)(Object(h.values)(e.metaBoxes.locations))},function(e){return[e.metaBoxes.locations]});function Ie(e){return Me(e).length>0}function Le(e){return e.metaBoxes.isSaving}var De=function(e,t){var n=e();return function(){var o=e();o!==n&&(n=o,t(o))}},Fe={SET_META_BOXES_PER_LOCATIONS:function(e,t){setTimeout(function(){var e=Object(u.select)("core/editor").getCurrentPostType();window.postboxes.page!==e&&window.postboxes.add_postbox_toggles(e)});var n=Object(u.select)("core/editor").isSavingPost(),o=Object(u.select)("core/editor").isAutosavingPost(),i=Object(u.select)("core/editor").isPreviewingPost();Object(u.subscribe)(function(){var e=Object(u.select)("core/editor").isSavingPost(),r=Object(u.select)("core/editor").isAutosavingPost(),c=Object(u.select)("core/editor").isPreviewingPost(),a=Object(u.select)("core/edit-post").hasMetaBoxes()&&(n&&!e&&!o||o&&i&&!c);n=e,o=r,i=c,a&&t.dispatch({type:"REQUEST_META_BOX_UPDATES"})})},REQUEST_META_BOX_UPDATES:function(e,t){window.tinyMCE&&window.tinyMCE.triggerSave();var n=t.getState(),o=Object(u.select)("core/editor").getCurrentPost(n),i=[!!o.comment_status&&["comment_status",o.comment_status],!!o.ping_status&&["ping_status",o.ping_status],!!o.sticky&&["sticky",o.sticky],["post_author",o.author]].filter(Boolean),r=[new window.FormData(document.querySelector(".metabox-base-form"))].concat(Object(G.a)(Me(n).map(function(e){return new window.FormData(function(e){var t=document.querySelector(".edit-post-meta-boxes-area.is-".concat(e," .metabox-location-").concat(e));return t||document.querySelector("#metaboxes .metabox-location-"+e)}(e))}))),c=Object(h.reduce)(r,function(e,t){var n=!0,o=!1,i=void 0;try{for(var r,c=t[Symbol.iterator]();!(n=(r=c.next()).done);n=!0){var a=Object(J.a)(r.value,2),l=a[0],s=a[1];e.append(l,s)}}catch(e){o=!0,i=e}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return e},new window.FormData);i.forEach(function(e){var t=Object(J.a)(e,2),n=t[0],o=t[1];return c.append(n,o)}),Z()({url:window._wpMetaBoxUrl,method:"POST",body:c,parse:!1}).then(function(){return t.dispatch({type:"META_BOX_UPDATES_SUCCESS"})})},SWITCH_MODE:function(e){"visual"!==e.mode&&Object(u.dispatch)("core/editor").clearSelectedBlock();var t="visual"===e.mode?Object(g.__)("Visual editor selected"):Object(g.__)("Code editor selected");Object(Y.speak)(t,"assertive")},INIT:function(e,t){Object(u.subscribe)(De(function(){return!!Object(u.select)("core/editor").getBlockSelectionStart()},function(e){Object(u.select)("core/edit-post").isEditorSidebarOpened()&&(e?t.dispatch(ee("edit-post/block")):t.dispatch(ee("edit-post/document")))}));var n,o=function(){return Object(u.select)("core/viewport").isViewportMatch("< medium")},i=(n=null,function(e){e?(n=Ee(t.getState()))&&t.dispatch({type:"CLOSE_GENERAL_SIDEBAR"}):n&&!Ee(t.getState())&&t.dispatch(ee(n))});i(o()),Object(u.subscribe)(De(o,i));Object(u.subscribe)(De(function(){return Object(u.select)("core/editor").getCurrentPost().link},function(e){if(e){var t=document.querySelector("#wp-admin-bar-view a");t&&t.setAttribute("href",e)}}))}};var Re=function(e){var t,n=[z()(Fe)],o=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},i={getState:e.getState,dispatch:function(){return o.apply(void 0,arguments)}};return t=n.map(function(e){return e(i)}),o=h.flowRight.apply(void 0,Object(G.a)(t))(e.dispatch),e.dispatch=o,e},Ge=Object(u.registerStore)("core/edit-post",{reducer:X,actions:o,selectors:i,persist:["preferences"]});Re(Ge),Ge.dispatch({type:"INIT"});var Ue={"t a l e s o f g u t e n b e r g":function(e){(document.activeElement.classList.contains("edit-post-visual-editor")||document.activeElement===document.body)&&(e.preventDefault(),window.wp.data.dispatch("core/editor").insertBlock(window.wp.blocks.createBlock("core/paragraph",{content:"🐡🐢🦀🐤🦋🐘🐧🐹🦁🦄🦍🐼🐿🎃🐴🐝🐆🦕🦔🌱🍇π🍌🐉💧🥨🌌🍂🍠🥦🥚🥝🎟🥥🥒🛵🥖🍒🍯🎾🎲🐺🐚🐮⌛️"})))}},Ve=n(17),He=n.n(Ve),We=n(24);var qe=function(e){function t(){var e;return Object(b.a)(this,t),(e=Object(m.a)(this,Object(O.a)(t).apply(this,arguments))).state={historyId:null},e}return Object(f.a)(t,e),Object(p.a)(t,[{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.postId,o=t.postStatus,i=t.postType,r=this.state.historyId;"trash"!==o?n===e.postId&&n===r||"auto-draft"===o||this.setBrowserURL(n):this.setTrashURL(n,i)}},{key:"setTrashURL",value:function(e,t){window.location.href=function(e,t){return Object(We.addQueryArgs)("edit.php",{trashed:1,post_type:t,ids:e})}(e,t)}},{key:"setBrowserURL",value:function(e){window.history.replaceState({id:e},"Post "+e,function(e){return Object(We.addQueryArgs)("post.php",{post:e,action:"edit"})}(e)),this.setState(function(){return{historyId:e}})}},{key:"render",value:function(){return null}}]),t}(r.Component),Qe=Object(u.withSelect)(function(e){var t=(0,e("core/editor").getCurrentPost)();return{postId:t.id,postStatus:t.status,postType:t.type}})(qe),Xe={toggleEditorMode:{raw:B.rawShortcut.secondary("m"),display:B.displayShortcut.secondary("m")},toggleSidebar:{raw:B.rawShortcut.primaryShift(","),display:B.displayShortcut.primaryShift(","),ariaLabel:B.shortcutAriaLabel.primaryShift(",")}},Ke=[{value:"visual",label:Object(g.__)("Visual Editor")},{value:"text",label:Object(g.__)("Code Editor")}];var ze=Object(x.compose)([Object(u.withSelect)(function(e){return{isRichEditingEnabled:e("core/editor").getEditorSettings().richEditingEnabled,mode:e("core/edit-post").getEditorMode()}}),Object(x.ifCondition)(function(e){return e.isRichEditingEnabled}),Object(u.withDispatch)(function(e,t){return{onSwitch:function(n){e("core/edit-post").switchEditorMode(n),t.onSelect(n)}}})])(function(e){var t=e.onSwitch,n=e.mode,o=Ke.map(function(e){return e.value!==n?Object(V.a)({},e,{shortcut:Xe.toggleEditorMode.display}):e});return Object(r.createElement)(C.MenuGroup,{label:Object(g.__)("Editor")},Object(r.createElement)(C.MenuItemsChoice,{choices:o,value:n,onSelect:t}))}),Je=Object(C.createSlotFill)("PluginsMoreMenuGroup"),Ye=Je.Fill,$e=Je.Slot;Ye.Slot=function(e){var t=e.fillProps;return Object(r.createElement)($e,{fillProps:t},function(e){return!Object(h.isEmpty)(e)&&Object(r.createElement)(C.MenuGroup,{label:Object(g.__)("Plugins")},e)})};var Ze=Ye;var et=Object(u.withDispatch)(function(e){return{openModal:e("core/edit-post").openModal}})(function(e){var t=e.openModal,n=e.onSelect;return Object(r.createElement)(C.MenuItem,{onClick:function(){n(),t("edit-post/options")}},Object(g.__)("Options"))});var tt=Object(x.compose)([Object(u.withSelect)(function(e,t){var n=t.feature;return{isActive:e("core/edit-post").isFeatureActive(n)}}),Object(u.withDispatch)(function(e,t){return{onToggle:function(){e("core/edit-post").toggleFeature(t.feature),t.onToggle()}}})])(function(e){var t=e.onToggle,n=e.isActive,o=e.label,i=e.info;return Object(r.createElement)(C.MenuItem,{icon:n&&"yes",isSelected:n,onClick:t,role:"menuitemcheckbox",label:o,info:i},o)});var nt=Object(l.ifViewportMatches)("medium")(function(e){var t=e.onClose;return Object(r.createElement)(C.MenuGroup,{label:Object(g._x)("View","noun")},Object(r.createElement)(tt,{feature:"fixedToolbar",label:Object(g.__)("Top Toolbar"),info:Object(g.__)("Access all block and document tools in a single place"),onToggle:t}),Object(r.createElement)(tt,{feature:"focusMode",label:Object(g.__)("Spotlight Mode"),info:Object(g.__)("Focus on one block at a time"),onToggle:t}),Object(r.createElement)(tt,{feature:"fullscreenMode",label:Object(g.__)("Fullscreen Mode"),info:Object(g.__)("Work without distraction"),onToggle:t}))}),ot=Object(g.__)("Show more tools & options"),it=Object(g.__)("Hide more tools & options"),rt=function(){return Object(r.createElement)(C.Dropdown,{className:"edit-post-more-menu",contentClassName:"edit-post-more-menu__content",position:"bottom left",renderToggle:function(e){var t=e.isOpen,n=e.onToggle;return Object(r.createElement)(C.IconButton,{icon:"ellipsis",label:t?it:ot,labelPosition:"bottom",onClick:n,"aria-expanded":t})},renderContent:function(e){var t=e.onClose;return Object(r.createElement)(r.Fragment,null,Object(r.createElement)(nt,{onClose:t}),Object(r.createElement)(ze,{onSelect:t}),Object(r.createElement)(Ze.Slot,{fillProps:{onClose:t}}),Object(r.createElement)(R.Slot,{fillProps:{onClose:t}}),Object(r.createElement)(C.MenuGroup,null,Object(r.createElement)(et,{onSelect:t})))}})};var ct=Object(u.withSelect)(function(e){var t=e("core/editor").getCurrentPostType,n=e("core/edit-post").isFeatureActive,o=e("core").getPostType;return{isActive:n("fullscreenMode"),postType:o(t())}})(function(e){var t=e.isActive,n=e.postType;return t&&n?Object(r.createElement)(C.Toolbar,{className:"edit-post-fullscreen-mode-close__toolbar"},Object(r.createElement)(C.IconButton,{icon:"exit",href:Object(We.addQueryArgs)("edit.php",{post_type:n.slug}),label:Object(h.get)(n,["labels","view_items"],Object(g.__)("View Posts"))})):null});var at=Object(x.compose)([Object(u.withSelect)(function(e){return{hasFixedToolbar:e("core/edit-post").isFeatureActive("fixedToolbar"),showInserter:"visual"===e("core/edit-post").getEditorMode()&&e("core/editor").getEditorSettings().richEditingEnabled}}),Object(l.withViewportMatch)({isLargeViewport:"medium"})])(function(e){var t=e.hasFixedToolbar,n=e.isLargeViewport,o=e.showInserter,i=t?Object(g.__)("Document and block tools"):Object(g.__)("Document tools");return Object(r.createElement)(c.NavigableToolbar,{className:"edit-post-header-toolbar","aria-label":i},Object(r.createElement)(ct,null),Object(r.createElement)("div",null,Object(r.createElement)(c.Inserter,{disabled:!o,position:"bottom right"}),Object(r.createElement)(a.DotTip,{tipId:"core/editor.inserter"},Object(g.__)("Welcome to the wonderful world of blocks! Click the “+” (“Add block”) button to add a new block. There are blocks available for all kinds of content: you can insert text, headings, images, lists, and lots more!"))),Object(r.createElement)(c.EditorHistoryUndo,null),Object(r.createElement)(c.EditorHistoryRedo,null),Object(r.createElement)(c.TableOfContents,null),Object(r.createElement)(c.BlockNavigationDropdown,null),t&&n&&Object(r.createElement)("div",{className:"edit-post-header-toolbar__block-toolbar"},Object(r.createElement)(c.BlockToolbar,null)))}),lt=Object(C.createSlotFill)("PinnedPlugins"),st=lt.Fill,ut=lt.Slot;st.Slot=function(e){return Object(r.createElement)(ut,e,function(e){return!Object(h.isEmpty)(e)&&Object(r.createElement)("div",{className:"edit-post-pinned-plugins"},e)})};var dt=st;var bt=Object(x.compose)(Object(u.withSelect)(function(e){return{hasPublishAction:Object(h.get)(e("core/editor").getCurrentPost(),["_links","wp:action-publish"],!1),isBeingScheduled:e("core/editor").isEditedPostBeingScheduled(),isPending:e("core/editor").isCurrentPostPending(),isPublished:e("core/editor").isCurrentPostPublished(),isPublishSidebarEnabled:e("core/editor").isPublishSidebarEnabled(),isPublishSidebarOpened:e("core/edit-post").isPublishSidebarOpened(),isScheduled:e("core/editor").isCurrentPostScheduled()}}),Object(u.withDispatch)(function(e){return{togglePublishSidebar:e("core/edit-post").togglePublishSidebar}}),Object(l.withViewportMatch)({isLessThanMediumViewport:"< medium"}))(function(e){var t,n=e.forceIsDirty,o=e.forceIsSaving,i=e.hasPublishAction,a=e.isBeingScheduled,l=e.isLessThanMediumViewport,s=e.isPending,u=e.isPublished,d=e.isPublishSidebarEnabled,b=e.isPublishSidebarOpened,p=e.isScheduled,m=e.togglePublishSidebar;return t=u||p&&a||s&&!i&&!l?"button":l?"toggle":d?"toggle":"button",Object(r.createElement)(c.PostPublishButton,{forceIsDirty:n,forceIsSaving:o,isOpen:b,isToggle:"toggle"===t,onToggle:m})});var pt=Object(x.compose)(Object(u.withSelect)(function(e){return{hasActiveMetaboxes:e("core/edit-post").hasMetaBoxes(),isEditorSidebarOpened:e("core/edit-post").isEditorSidebarOpened(),isPublishSidebarOpened:e("core/edit-post").isPublishSidebarOpened(),isSaving:e("core/edit-post").isSavingMetaBoxes()}}),Object(u.withDispatch)(function(e,t,n){var o=(0,n.select)("core/editor").getBlockSelectionStart,i=e("core/edit-post"),r=i.openGeneralSidebar;return{openGeneralSidebar:function(){return r(o()?"edit-post/block":"edit-post/document")},closeGeneralSidebar:i.closeGeneralSidebar}}))(function(e){var t=e.closeGeneralSidebar,n=e.hasActiveMetaboxes,o=e.isEditorSidebarOpened,i=e.isPublishSidebarOpened,l=e.isSaving,s=e.openGeneralSidebar,u=o?t:s;return Object(r.createElement)("div",{role:"region","aria-label":Object(g.__)("Editor top bar"),className:"edit-post-header",tabIndex:"-1"},Object(r.createElement)(at,null),Object(r.createElement)("div",{className:"edit-post-header__settings"},!i&&Object(r.createElement)(c.PostSavedState,{forceIsDirty:n,forceIsSaving:l}),Object(r.createElement)(c.PostPreviewButton,{forceIsAutosaveable:n,forcePreviewLink:l?null:void 0}),Object(r.createElement)(bt,{forceIsDirty:n,forceIsSaving:l}),Object(r.createElement)("div",null,Object(r.createElement)(C.IconButton,{icon:"admin-generic",label:Object(g.__)("Settings"),onClick:u,isToggled:o,"aria-expanded":o,shortcut:Xe.toggleSidebar}),Object(r.createElement)(a.DotTip,{tipId:"core/editor.settings"},Object(g.__)("You’ll find more settings for your page and blocks in the sidebar. Click “Settings” to open it."))),Object(r.createElement)(dt.Slot,null),Object(r.createElement)(rt,null)))});var mt=Object(x.compose)(Object(u.withSelect)(function(e){return{isRichEditingEnabled:e("core/editor").getEditorSettings().richEditingEnabled}}),Object(u.withDispatch)(function(e){return{onExit:function(){e("core/edit-post").switchEditorMode("visual")}}}))(function(e){var t=e.onExit,n=e.isRichEditingEnabled;return Object(r.createElement)("div",{className:"edit-post-text-editor"},n&&Object(r.createElement)("div",{className:"edit-post-text-editor__toolbar"},Object(r.createElement)("h2",null,Object(g.__)("Editing Code")),Object(r.createElement)(C.IconButton,{onClick:t,icon:"no-alt",shortcut:B.displayShortcut.secondary("m")},Object(g.__)("Exit Code Editor"))),Object(r.createElement)("div",{className:"edit-post-text-editor__body"},Object(r.createElement)(c.PostTitle,null),Object(r.createElement)(c.PostTextEditor,null)))});var Ot=Object(x.compose)(Object(u.withSelect)(function(e){return{areAdvancedSettingsOpened:"edit-post/block"===e("core/edit-post").getActiveGeneralSidebarName()}}),Object(u.withDispatch)(function(e){return{openEditorSidebar:function(){return e("core/edit-post").openGeneralSidebar("edit-post/block")},closeSidebar:e("core/edit-post").closeGeneralSidebar}}),C.withSpokenMessages)(function(e){var t=e.areAdvancedSettingsOpened,n=e.closeSidebar,o=e.openEditorSidebar,i=e.onClick,c=void 0===i?h.noop:i,a=e.small,l=void 0!==a&&a,s=e.speak,u=t?Object(g.__)("Hide Block Settings"):Object(g.__)("Show Block Settings");return Object(r.createElement)(C.MenuItem,{className:"editor-block-settings-menu__control",onClick:Object(h.flow)(t?n:o,function(){s(t?Object(g.__)("Block settings closed"):Object(g.__)("Additional settings are now available in the Editor block settings sidebar"))},c),icon:"admin-generic",label:l?u:void 0,shortcut:Xe.toggleSidebar},!l&&u)}),ft=Object(C.createSlotFill)("PluginBlockSettingsMenuGroup"),jt=ft.Fill,ht=ft.Slot;jt.Slot=Object(u.withSelect)(function(e,t){var n=t.fillProps.clientIds;return{selectedBlocks:e("core/editor").getBlocksByClientId(n)}})(function(e){var t=e.fillProps,n=e.selectedBlocks;return n=Object(h.map)(n,function(e){return e.name}),Object(r.createElement)(ht,{fillProps:Object(V.a)({},t,{selectedBlocks:n})},function(e){return!Object(h.isEmpty)(e)&&Object(r.createElement)(r.Fragment,null,Object(r.createElement)("div",{className:"editor-block-settings-menu__separator"}),e)})});var gt=jt;var Et=function(){return Object(r.createElement)(c.BlockSelectionClearer,{className:"edit-post-visual-editor editor-styles-wrapper"},Object(r.createElement)(c.EditorGlobalKeyboardShortcuts,null),Object(r.createElement)(c.CopyHandler,null),Object(r.createElement)(c.MultiSelectScrollIntoView,null),Object(r.createElement)(c.WritingFlow,null,Object(r.createElement)(c.ObserveTyping,null,Object(r.createElement)(c.PostTitle,null),Object(r.createElement)(c.BlockList,null))),Object(r.createElement)(c._BlockSettingsMenuFirstItem,null,function(e){var t=e.onClose;return Object(r.createElement)(Ot,{onClick:t})}),Object(r.createElement)(c._BlockSettingsMenuPluginsExtension,null,function(e){var t=e.clientIds,n=e.onClose;return Object(r.createElement)(gt.Slot,{fillProps:{clientIds:t,onClose:n}})}))},vt=function(e){function t(){var e;return Object(b.a)(this,t),(e=Object(m.a)(this,Object(O.a)(t).apply(this,arguments))).toggleMode=e.toggleMode.bind(Object(j.a)(Object(j.a)(e))),e.toggleSidebar=e.toggleSidebar.bind(Object(j.a)(Object(j.a)(e))),e}return Object(f.a)(t,e),Object(p.a)(t,[{key:"toggleMode",value:function(){var e=this.props,t=e.mode,n=e.switchMode;e.isRichEditingEnabled&&n("visual"===t?"text":"visual")}},{key:"toggleSidebar",value:function(e){e.preventDefault();var t=this.props,n=t.isEditorSidebarOpen,o=t.closeSidebar,i=t.openSidebar;n?o():i()}},{key:"render",value:function(){var e;return Object(r.createElement)(C.KeyboardShortcuts,{bindGlobal:!0,shortcuts:(e={},Object(U.a)(e,Xe.toggleEditorMode.raw,this.toggleMode),Object(U.a)(e,Xe.toggleSidebar.raw,this.toggleSidebar),e)})}}]),t}(r.Component),_t=Object(x.compose)([Object(u.withSelect)(function(e){return{isRichEditingEnabled:e("core/editor").getEditorSettings().richEditingEnabled,mode:e("core/edit-post").getEditorMode(),isEditorSidebarOpen:e("core/edit-post").isEditorSidebarOpened(),hasBlockSelection:!!e("core/editor").getBlockSelectionStart()}}),Object(u.withDispatch)(function(e,t){var n=t.hasBlockSelection;return{switchMode:function(t){e("core/edit-post").switchEditorMode(t)},openSidebar:function(){var t=n?"edit-post/block":"edit-post/document";e("core/edit-post").openGeneralSidebar(t)},closeSidebar:e("core/edit-post").closeGeneralSidebar}})])(vt),yt=B.displayShortcutList.primary,St=B.displayShortcutList.primaryShift,Pt=B.displayShortcutList.primaryAlt,kt=B.displayShortcutList.secondary,wt=B.displayShortcutList.access,Ct=B.displayShortcutList.ctrl,xt=B.displayShortcutList.alt,Mt=B.displayShortcutList.ctrlShift,Tt=B.displayShortcutList.shiftAlt,Nt=[{title:Object(g.__)("Global shortcuts"),shortcuts:[{keyCombination:wt("h"),description:Object(g.__)("Display this help.")},{keyCombination:yt("s"),description:Object(g.__)("Save your changes.")},{keyCombination:yt("z"),description:Object(g.__)("Undo your last changes.")},{keyCombination:St("z"),description:Object(g.__)("Redo your last undo.")},{keyCombination:St(","),description:Object(g.__)("Show or hide the settings sidebar."),ariaLabel:B.shortcutAriaLabel.primaryShift(",")},{keyCombination:wt("o"),description:Object(g.__)("Open the block navigation menu.")},{keyCombination:Ct("`"),description:Object(g.__)("Navigate to the next part of the editor."),ariaLabel:B.shortcutAriaLabel.ctrl("`")},{keyCombination:Mt("`"),description:Object(g.__)("Navigate to the previous part of the editor."),ariaLabel:B.shortcutAriaLabel.ctrlShift("`")},{keyCombination:Tt("n"),description:Object(g.__)("Navigate to the next part of the editor (alternative).")},{keyCombination:Tt("p"),description:Object(g.__)("Navigate to the previous part of the editor (alternative).")},{keyCombination:xt("F10"),description:Object(g.__)("Navigate to the nearest toolbar.")},{keyCombination:kt("m"),description:Object(g.__)("Switch between Visual Editor and Code Editor.")}]},{title:Object(g.__)("Selection shortcuts"),shortcuts:[{keyCombination:yt("a"),description:Object(g.__)("Select all text when typing. Press again to select all blocks.")},{keyCombination:"Esc",description:Object(g.__)("Clear selection."),ariaLabel:Object(g.__)("Escape")}]},{title:Object(g.__)("Block shortcuts"),shortcuts:[{keyCombination:St("d"),description:Object(g.__)("Duplicate the selected block(s).")},{keyCombination:wt("z"),description:Object(g.__)("Remove the selected block(s).")},{keyCombination:Pt("t"),description:Object(g.__)("Insert a new block before the selected block(s).")},{keyCombination:Pt("y"),description:Object(g.__)("Insert a new block after the selected block(s).")},{keyCombination:"/",description:Object(g.__)("Change the block type after adding a new paragraph."),ariaLabel:Object(g.__)("Forward-slash")}]},{title:Object(g.__)("Text formatting"),shortcuts:[{keyCombination:yt("b"),description:Object(g.__)("Make the selected text bold.")},{keyCombination:yt("i"),description:Object(g.__)("Make the selected text italic.")},{keyCombination:yt("u"),description:Object(g.__)("Underline the selected text.")},{keyCombination:yt("k"),description:Object(g.__)("Convert the selected text into a link.")},{keyCombination:St("k"),description:Object(g.__)("Remove a link.")},{keyCombination:wt("d"),description:Object(g.__)("Add a strikethrough to the selected text.")},{keyCombination:wt("x"),description:Object(g.__)("Display the selected text in a monospaced font.")}]}],At="edit-post/keyboard-shortcut-help",Bt=function(e){var t=e.shortcuts;return Object(r.createElement)("dl",{className:"edit-post-keyboard-shortcut-help__shortcut-list"},t.map(function(e,t){var n=e.keyCombination,o=e.description,i=e.ariaLabel;return Object(r.createElement)("div",{className:"edit-post-keyboard-shortcut-help__shortcut",key:t},Object(r.createElement)("dt",{className:"edit-post-keyboard-shortcut-help__shortcut-term"},Object(r.createElement)("kbd",{className:"edit-post-keyboard-shortcut-help__shortcut-key-combination","aria-label":i},function(e){return e.map(function(e,t){return"+"===e?Object(r.createElement)(r.Fragment,{key:t},e):Object(r.createElement)("kbd",{key:t,className:"edit-post-keyboard-shortcut-help__shortcut-key"},e)})}(Object(h.castArray)(n)))),Object(r.createElement)("dd",{className:"edit-post-keyboard-shortcut-help__shortcut-description"},o))}))},It=function(e){var t=e.title,n=e.shortcuts;return Object(r.createElement)("section",{className:"edit-post-keyboard-shortcut-help__section"},Object(r.createElement)("h2",{className:"edit-post-keyboard-shortcut-help__section-title"},t),Object(r.createElement)(Bt,{shortcuts:n}))};var Lt=Object(x.compose)([Object(u.withSelect)(function(e){return{isModalActive:e("core/edit-post").isModalActive(At)}}),Object(u.withDispatch)(function(e,t){var n=t.isModalActive,o=e("core/edit-post"),i=o.openModal,r=o.closeModal;return{toggleModal:function(){return n?r():i(At)}}})])(function(e){var t=e.isModalActive,n=e.toggleModal,o=Object(r.createElement)("span",{className:"edit-post-keyboard-shortcut-help__title"},Object(g.__)("Keyboard Shortcuts"));return Object(r.createElement)(r.Fragment,null,Object(r.createElement)(C.KeyboardShortcuts,{bindGlobal:!0,shortcuts:Object(U.a)({},B.rawShortcut.access("h"),n)}),t&&Object(r.createElement)(C.Modal,{className:"edit-post-keyboard-shortcut-help",title:o,closeLabel:Object(g.__)("Close"),onRequestClose:n},Nt.map(function(e,t){return Object(r.createElement)(It,Object(P.a)({key:t},e))})))}),Dt=function(e){var t=e.title,n=e.children;return Object(r.createElement)("section",{className:"edit-post-options-modal__section"},Object(r.createElement)("h2",{className:"edit-post-options-modal__section-title"},t),n)};var Ft=function(e){var t=e.label,n=e.isChecked,o=e.onChange;return Object(r.createElement)(C.CheckboxControl,{className:"edit-post-options-modal__option",label:t,checked:n,onChange:o})},Rt=function(e){function t(e){var n,o=e.isChecked;return Object(b.a)(this,t),(n=Object(m.a)(this,Object(O.a)(t).apply(this,arguments))).toggleCustomFields=n.toggleCustomFields.bind(Object(j.a)(Object(j.a)(n))),n.state={isChecked:o},n}return Object(f.a)(t,e),Object(p.a)(t,[{key:"toggleCustomFields",value:function(){document.getElementById("toggle-custom-fields-form").submit(),this.setState({isChecked:!this.props.isChecked})}},{key:"render",value:function(){var e=this.props.label,t=this.state.isChecked;return Object(r.createElement)(Ft,{label:e,isChecked:t,onChange:this.toggleCustomFields})}}]),t}(r.Component),Gt=Object(u.withSelect)(function(e){return{isChecked:!!e("core/editor").getEditorSettings().enableCustomFields}})(Rt),Ut=Object(x.compose)(Object(u.withSelect)(function(e,t){var n=t.panelName,o=e("core/edit-post"),i=o.isEditorPanelEnabled;return{isRemoved:(0,o.isEditorPanelRemoved)(n),isChecked:i(n)}}),Object(x.ifCondition)(function(e){return!e.isRemoved}),Object(u.withDispatch)(function(e,t){var n=t.panelName;return{onChange:function(){return e("core/edit-post").toggleEditorPanelEnabled(n)}}}))(Ft),Vt=Object(x.compose)(Object(u.withSelect)(function(e){return{isChecked:e("core/editor").isPublishSidebarEnabled()}}),Object(u.withDispatch)(function(e){var t=e("core/editor"),n=t.enablePublishSidebar,o=t.disablePublishSidebar;return{onChange:function(e){return e?n():o()}}}),Object(l.ifViewportMatches)("medium"))(Ft),Ht=function(e){function t(e){var n,o=e.isChecked;return Object(b.a)(this,t),(n=Object(m.a)(this,Object(O.a)(t).apply(this,arguments))).state={isChecked:o},n}return Object(f.a)(t,e),Object(p.a)(t,[{key:"componentWillUnmount",value:function(){this.state.isChecked!==this.props.isChecked&&this.props.onChange(this.state.isChecked)}},{key:"render",value:function(){var e=this;return Object(r.createElement)(Ft,{label:this.props.label,isChecked:this.state.isChecked,onChange:function(t){return e.setState({isChecked:t})}})}}]),t}(r.Component),Wt=Object(x.compose)(Object(u.withSelect)(function(e){return{isChecked:e("core/nux").areTipsEnabled()}}),Object(u.withDispatch)(function(e){var t=e("core/nux"),n=t.enableTips,o=t.disableTips;return{onChange:function(e){return e?n():o()}}}))(Ht);var qt=Object(u.withSelect)(function(e){var t=e("core/editor").getEditorSettings,n=e("core/edit-post").getAllMetaBoxes;return{areCustomFieldsRegistered:void 0!==t().enableCustomFields,metaBoxes:n()}})(function(e){var t=e.areCustomFieldsRegistered,n=e.metaBoxes,o=Object(k.a)(e,["areCustomFieldsRegistered","metaBoxes"]),i=Object(h.filter)(n,function(e){return"postcustom"!==e.id});return t||0!==i.length?Object(r.createElement)(Dt,o,t&&Object(r.createElement)(Gt,{label:Object(g.__)("Custom Fields")}),Object(h.map)(i,function(e){var t=e.id,n=e.title;return Object(r.createElement)(Ut,{key:t,label:n,panelName:"meta-box-".concat(t)})})):null});var Qt=Object(x.compose)(Object(u.withSelect)(function(e){return{isModalActive:e("core/edit-post").isModalActive("edit-post/options")}}),Object(u.withDispatch)(function(e){return{closeModal:function(){return e("core/edit-post").closeModal()}}}))(function(e){var t=e.isModalActive,n=e.closeModal;return t?Object(r.createElement)(C.Modal,{className:"edit-post-options-modal",title:Object(r.createElement)("span",{className:"edit-post-options-modal__title"},Object(g.__)("Options")),closeLabel:Object(g.__)("Close"),onRequestClose:n},Object(r.createElement)(Dt,{title:Object(g.__)("General")},Object(r.createElement)(Vt,{label:Object(g.__)("Enable Pre-publish Checks")}),Object(r.createElement)(Wt,{label:Object(g.__)("Enable Tips")})),Object(r.createElement)(Dt,{title:Object(g.__)("Document Panels")},Object(r.createElement)(Ut,{label:Object(g.__)("Permalink"),panelName:"post-link"}),Object(r.createElement)(c.PostTaxonomies,{taxonomyWrapper:function(e,t){return Object(r.createElement)(Ut,{label:Object(h.get)(t,["labels","menu_name"]),panelName:"taxonomy-panel-".concat(t.slug)})}}),Object(r.createElement)(Ut,{label:Object(g.__)("Featured Image"),panelName:"featured-image"}),Object(r.createElement)(c.PostExcerptCheck,null,Object(r.createElement)(Ut,{label:Object(g.__)("Excerpt"),panelName:"post-excerpt"})),Object(r.createElement)(Ut,{label:Object(g.__)("Discussion"),panelName:"discussion-panel"}),Object(r.createElement)(c.PageAttributesCheck,null,Object(r.createElement)(Ut,{label:Object(g.__)("Page Attributes"),panelName:"page-attributes"}))),Object(r.createElement)(qt,{title:Object(g.__)("Advanced Panels")})):null}),Xt=function(e){function t(){var e;return Object(b.a)(this,t),(e=Object(m.a)(this,Object(O.a)(t).apply(this,arguments))).bindContainerNode=e.bindContainerNode.bind(Object(j.a)(Object(j.a)(e))),e}return Object(f.a)(t,e),Object(p.a)(t,[{key:"componentDidMount",value:function(){this.form=document.querySelector(".metabox-location-"+this.props.location),this.form&&this.container.appendChild(this.form)}},{key:"componentWillUnmount",value:function(){this.form&&document.querySelector("#metaboxes").appendChild(this.form)}},{key:"bindContainerNode",value:function(e){this.container=e}},{key:"render",value:function(){var e=this.props,t=e.location,n=e.isSaving,o=He()("edit-post-meta-boxes-area","is-".concat(t),{"is-loading":n});return Object(r.createElement)("div",{className:o},n&&Object(r.createElement)(C.Spinner,null),Object(r.createElement)("div",{className:"edit-post-meta-boxes-area__container",ref:this.bindContainerNode}),Object(r.createElement)("div",{className:"edit-post-meta-boxes-area__clear"}))}}]),t}(r.Component),Kt=Object(u.withSelect)(function(e){return{isSaving:e("core/edit-post").isSavingMetaBoxes()}})(Xt),zt=function(e){function t(){return Object(b.a)(this,t),Object(m.a)(this,Object(O.a)(t).apply(this,arguments))}return Object(f.a)(t,e),Object(p.a)(t,[{key:"componentDidMount",value:function(){this.updateDOM()}},{key:"componentDidUpdate",value:function(e){this.props.isVisible!==e.isVisible&&this.updateDOM()}},{key:"updateDOM",value:function(){var e=this.props,t=e.id,n=e.isVisible,o=document.getElementById(t);o&&(n?o.classList.remove("is-hidden"):o.classList.add("is-hidden"))}},{key:"render",value:function(){return null}}]),t}(r.Component),Jt=Object(u.withSelect)(function(e,t){var n=t.id;return{isVisible:e("core/edit-post").isEditorPanelEnabled("meta-box-".concat(n))}})(zt);var Yt=Object(u.withSelect)(function(e,t){var n=t.location,o=e("core/edit-post"),i=o.isMetaBoxLocationVisible;return{metaBoxes:(0,o.getMetaBoxesPerLocation)(n),isVisible:i(n)}})(function(e){var t=e.location,n=e.isVisible,o=e.metaBoxes;return Object(r.createElement)(r.Fragment,null,Object(h.map)(o,function(e){var t=e.id;return Object(r.createElement)(Jt,{key:t,id:t})}),n&&Object(r.createElement)(Kt,{location:t}))}),$t=Object(C.createSlotFill)("Sidebar"),Zt=$t.Fill,en=$t.Slot,tn=Object(x.compose)(Object(u.withSelect)(function(e,t){var n=t.name;return{isActive:e("core/edit-post").getActiveGeneralSidebarName()===n}}),Object(x.ifCondition)(function(e){return e.isActive}),C.withFocusReturn)(function(e){var t=e.children,n=e.label;return Object(r.createElement)(Zt,null,Object(r.createElement)("div",{className:"edit-post-sidebar",role:"region","aria-label":n,tabIndex:"-1"},t))});tn.Slot=en;var nn=tn,on=Object(x.compose)(Object(u.withSelect)(function(e){return{title:e("core/editor").getEditedPostAttribute("title")}}),Object(u.withDispatch)(function(e){return{closeSidebar:e("core/edit-post").closeGeneralSidebar}}))(function(e){var t=e.children,n=e.className,o=e.closeLabel,i=e.closeSidebar,c=e.title;return Object(r.createElement)(r.Fragment,null,Object(r.createElement)("div",{className:"components-panel__header edit-post-sidebar-header__small"},Object(r.createElement)("span",{className:"edit-post-sidebar-header__title"},c||Object(g.__)("(no title)")),Object(r.createElement)(C.IconButton,{onClick:i,icon:"no-alt",label:o})),Object(r.createElement)("div",{className:He()("components-panel__header edit-post-sidebar-header",n)},t,Object(r.createElement)(C.IconButton,{onClick:i,icon:"no-alt",label:o,shortcut:Xe.toggleSidebar})))}),rn=Object(u.withDispatch)(function(e){var t=e("core/edit-post").openGeneralSidebar,n=e("core/editor").clearSelectedBlock;return{openDocumentSettings:function(){t("edit-post/document"),n()},openBlockSettings:function(){t("edit-post/block")}}})(function(e){var t=e.openDocumentSettings,n=e.openBlockSettings,o=e.sidebarName,i=Object(g.__)("Block"),c="edit-post/document"===o?[Object(g.__)("Document (selected)"),"is-active"]:[Object(g.__)("Document"),""],a=Object(J.a)(c,2),l=a[0],s=a[1],u="edit-post/block"===o?[Object(g.__)("Block (selected)"),"is-active"]:[Object(g.__)("Block"),""],d=Object(J.a)(u,2),b=d[0],p=d[1];return Object(r.createElement)(on,{className:"edit-post-sidebar__panel-tabs",closeLabel:Object(g.__)("Close settings")},Object(r.createElement)("ul",null,Object(r.createElement)("li",null,Object(r.createElement)("button",{onClick:t,className:"edit-post-sidebar__panel-tab ".concat(s),"aria-label":l,"data-label":Object(g.__)("Document")},Object(g.__)("Document"))),Object(r.createElement)("li",null,Object(r.createElement)("button",{onClick:n,className:"edit-post-sidebar__panel-tab ".concat(p),"aria-label":b,"data-label":i},i))))});var cn=function(){return Object(r.createElement)(c.PostVisibilityCheck,{render:function(e){var t=e.canEdit;return Object(r.createElement)(C.PanelRow,{className:"edit-post-post-visibility"},Object(r.createElement)("span",null,Object(g.__)("Visibility")),!t&&Object(r.createElement)("span",null,Object(r.createElement)(c.PostVisibilityLabel,null)),t&&Object(r.createElement)(C.Dropdown,{position:"bottom left",contentClassName:"edit-post-post-visibility__dialog",renderToggle:function(e){var t=e.isOpen,n=e.onToggle;return Object(r.createElement)(C.Button,{type:"button","aria-expanded":t,className:"edit-post-post-visibility__toggle",onClick:n,isLink:!0},Object(r.createElement)(c.PostVisibilityLabel,null))},renderContent:function(){return Object(r.createElement)(c.PostVisibility,null)}}))}})};function an(){return Object(r.createElement)(c.PostTrashCheck,null,Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PostTrash,null)))}var ln=Object(x.withInstanceId)(function(e){var t=e.instanceId;return Object(r.createElement)(c.PostScheduleCheck,null,Object(r.createElement)(C.PanelRow,{className:"edit-post-post-schedule"},Object(r.createElement)("label",{htmlFor:"edit-post-post-schedule__toggle-".concat(t),id:"edit-post-post-schedule__heading-".concat(t)},Object(g.__)("Publish")),Object(r.createElement)(C.Dropdown,{position:"bottom left",contentClassName:"edit-post-post-schedule__dialog",renderToggle:function(e){var n=e.onToggle,o=e.isOpen;return Object(r.createElement)(r.Fragment,null,Object(r.createElement)("label",{className:"edit-post-post-schedule__label",htmlFor:"edit-post-post-schedule__toggle-".concat(t)},Object(r.createElement)(c.PostScheduleLabel,null)," ",Object(g.__)("Click to change")),Object(r.createElement)(C.Button,{id:"edit-post-post-schedule__toggle-".concat(t),type:"button",className:"edit-post-post-schedule__toggle",onClick:n,"aria-expanded":o,"aria-live":"polite",isLink:!0},Object(r.createElement)(c.PostScheduleLabel,null)))},renderContent:function(){return Object(r.createElement)(c.PostSchedule,null)}})))});var sn=function(){return Object(r.createElement)(c.PostStickyCheck,null,Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PostSticky,null)))};var un=function(){return Object(r.createElement)(c.PostAuthorCheck,null,Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PostAuthor,null)))};var dn=function(){return Object(r.createElement)(c.PostFormatCheck,null,Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PostFormat,null)))};var bn=function(){return Object(r.createElement)(c.PostPendingStatusCheck,null,Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PostPendingStatus,null)))},pn=Object(C.createSlotFill)("PluginPostStatusInfo"),mn=pn.Fill,On=pn.Slot,fn=function(e){var t=e.children,n=e.className;return Object(r.createElement)(mn,null,Object(r.createElement)(C.PanelRow,{className:n},t))};fn.Slot=On;var jn=fn;var hn=Object(x.compose)([Object(u.withSelect)(function(e){return{isOpened:e("core/edit-post").isEditorPanelOpened("post-status")}}),Object(u.withDispatch)(function(e){return{onTogglePanel:function(){return e("core/edit-post").toggleEditorPanelOpened("post-status")}}})])(function(e){var t=e.isOpened,n=e.onTogglePanel;return Object(r.createElement)(C.PanelBody,{className:"edit-post-post-status",title:Object(g.__)("Status & Visibility"),opened:t,onToggle:n},Object(r.createElement)(jn.Slot,null,function(e){return Object(r.createElement)(r.Fragment,null,Object(r.createElement)(cn,null),Object(r.createElement)(ln,null),Object(r.createElement)(dn,null),Object(r.createElement)(sn,null),Object(r.createElement)(bn,null),Object(r.createElement)(un,null),e,Object(r.createElement)(an,null))}))});var gn=function(){return Object(r.createElement)(c.PostLastRevisionCheck,null,Object(r.createElement)(C.PanelBody,{className:"edit-post-last-revision__panel"},Object(r.createElement)(c.PostLastRevision,null)))};var En=Object(x.compose)(Object(u.withSelect)(function(e,t){var n=Object(h.get)(t.taxonomy,["slug"]),o=n?"taxonomy-panel-".concat(n):"";return{panelName:o,isEnabled:!!n&&e("core/edit-post").isEditorPanelEnabled(o),isOpened:!!n&&e("core/edit-post").isEditorPanelOpened(o)}}),Object(u.withDispatch)(function(e,t){return{onTogglePanel:function(){e("core/edit-post").toggleEditorPanelOpened(t.panelName)}}}))(function(e){var t=e.isEnabled,n=e.taxonomy,o=e.isOpened,i=e.onTogglePanel,c=e.children;if(!t)return null;var a=Object(h.get)(n,["labels","menu_name"]);return a?Object(r.createElement)(C.PanelBody,{title:a,opened:o,onToggle:i},c):null});var vn=function(){return Object(r.createElement)(c.PostTaxonomiesCheck,null,Object(r.createElement)(c.PostTaxonomies,{taxonomyWrapper:function(e,t){return Object(r.createElement)(En,{taxonomy:t},e)}}))};var _n=Object(u.withSelect)(function(e){var t=e("core/editor").getEditedPostAttribute,n=e("core").getPostType,o=e("core/edit-post"),i=o.isEditorPanelEnabled,r=o.isEditorPanelOpened;return{postType:n(t("type")),isEnabled:i("featured-image"),isOpened:r("featured-image")}}),yn=Object(u.withDispatch)(function(e){var t=e("core/edit-post").toggleEditorPanelOpened;return{onTogglePanel:Object(h.partial)(t,"featured-image")}}),Sn=Object(x.compose)(_n,yn)(function(e){var t=e.isEnabled,n=e.isOpened,o=e.postType,i=e.onTogglePanel;return t?Object(r.createElement)(c.PostFeaturedImageCheck,null,Object(r.createElement)(C.PanelBody,{title:Object(h.get)(o,["labels","featured_image"],Object(g.__)("Featured Image")),opened:n,onToggle:i},Object(r.createElement)(c.PostFeaturedImage,null))):null});var Pn=Object(x.compose)([Object(u.withSelect)(function(e){return{isEnabled:e("core/edit-post").isEditorPanelEnabled("post-excerpt"),isOpened:e("core/edit-post").isEditorPanelOpened("post-excerpt")}}),Object(u.withDispatch)(function(e){return{onTogglePanel:function(){return e("core/edit-post").toggleEditorPanelOpened("post-excerpt")}}})])(function(e){var t=e.isEnabled,n=e.isOpened,o=e.onTogglePanel;return t?Object(r.createElement)(c.PostExcerptCheck,null,Object(r.createElement)(C.PanelBody,{title:Object(g.__)("Excerpt"),opened:n,onToggle:o},Object(r.createElement)(c.PostExcerpt,null))):null});var kn=Object(x.compose)([Object(u.withSelect)(function(e){var t=e("core/editor"),n=t.isEditedPostNew,o=t.isPermalinkEditable,i=t.getCurrentPost,r=t.isCurrentPostPublished,c=t.getPermalinkParts,a=t.getEditedPostAttribute,l=e("core/edit-post"),s=l.isEditorPanelEnabled,u=l.isEditorPanelOpened,d=e("core").getPostType,b=i(),p=b.link,m=b.id,O=d(a("type"));return{isNew:n(),postLink:p,isEditable:o(),isPublished:r(),isOpened:u("post-link"),permalinkParts:c(),isEnabled:s("post-link"),isViewable:Object(h.get)(O,["viewable"],!1),postTitle:a("title"),postSlug:a("slug"),postID:m}}),Object(x.ifCondition)(function(e){var t=e.isEnabled,n=e.isNew,o=e.postLink,i=e.isViewable,r=e.permalinkParts;return t&&!n&&o&&i&&r}),Object(u.withDispatch)(function(e){var t=e("core/edit-post").toggleEditorPanelOpened,n=e("core/editor").editPost;return{onTogglePanel:function(){return t("post-link")},editPermalink:function(e){n({slug:e})}}}),Object(x.withState)({forceEmptyField:!1})])(function(e){var t,n,o,i=e.isOpened,a=e.onTogglePanel,l=e.isEditable,s=e.postLink,u=e.permalinkParts,d=e.editPermalink,b=e.forceEmptyField,p=e.setState,m=e.postTitle,O=e.postSlug,f=e.postID,j=u.prefix,h=u.suffix,E=O||Object(c.cleanForSlug)(m)||f;return l&&(t=j&&Object(r.createElement)("span",{className:"edit-post-post-link__link-prefix"},j),n=E&&Object(r.createElement)("span",{className:"edit-post-post-link__link-post-name"},E),o=h&&Object(r.createElement)("span",{className:"edit-post-post-link__link-suffix"},h)),Object(r.createElement)(C.PanelBody,{title:Object(g.__)("Permalink"),opened:i,onToggle:a},l&&Object(r.createElement)(C.TextControl,{label:Object(g.__)("URL"),value:b?"":E,onChange:function(e){d(e),e?b&&p({forceEmptyField:!1}):b||p({forceEmptyField:!0})},onBlur:function(e){d(Object(c.cleanForSlug)(e.target.value)),b&&p({forceEmptyField:!1})}}),Object(r.createElement)("p",{className:"edit-post-post-link__preview-label"},Object(g.__)("Preview")),Object(r.createElement)(C.ExternalLink,{className:"edit-post-post-link__link",href:s,target:"_blank"},l?Object(r.createElement)(r.Fragment,null,t,n,o):s))});var wn=Object(x.compose)([Object(u.withSelect)(function(e){return{isEnabled:e("core/edit-post").isEditorPanelEnabled("discussion-panel"),isOpened:e("core/edit-post").isEditorPanelOpened("discussion-panel")}}),Object(u.withDispatch)(function(e){return{onTogglePanel:function(){return e("core/edit-post").toggleEditorPanelOpened("discussion-panel")}}})])(function(e){var t=e.isEnabled,n=e.isOpened,o=e.onTogglePanel;return t?Object(r.createElement)(c.PostTypeSupportCheck,{supportKeys:["comments","trackbacks"]},Object(r.createElement)(C.PanelBody,{title:Object(g.__)("Discussion"),opened:n,onToggle:o},Object(r.createElement)(c.PostTypeSupportCheck,{supportKeys:"comments"},Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PostComments,null))),Object(r.createElement)(c.PostTypeSupportCheck,{supportKeys:"trackbacks"},Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PostPingbacks,null))))):null});var Cn=Object(u.withSelect)(function(e){var t=e("core/editor").getEditedPostAttribute,n=e("core/edit-post"),o=n.isEditorPanelEnabled,i=n.isEditorPanelOpened,r=e("core").getPostType;return{isEnabled:o("page-attributes"),isOpened:i("page-attributes"),postType:r(t("type"))}}),xn=Object(u.withDispatch)(function(e){var t=e("core/edit-post").toggleEditorPanelOpened;return{onTogglePanel:Object(h.partial)(t,"page-attributes")}}),Mn=Object(x.compose)(Cn,xn)(function(e){var t=e.isEnabled,n=e.isOpened,o=e.onTogglePanel,i=e.postType;return t&&i?Object(r.createElement)(c.PageAttributesCheck,null,Object(r.createElement)(C.PanelBody,{title:Object(h.get)(i,["labels","attributes"],Object(g.__)("Page Attributes")),opened:n,onToggle:o},Object(r.createElement)(c.PageTemplate,null),Object(r.createElement)(c.PageAttributesParent,null),Object(r.createElement)(C.PanelRow,null,Object(r.createElement)(c.PageAttributesOrder,null)))):null}),Tn=Object(x.compose)(Object(u.withSelect)(function(e){var t=e("core/edit-post"),n=t.getActiveGeneralSidebarName;return{isEditorSidebarOpened:(0,t.isEditorSidebarOpened)(),sidebarName:n()}}),Object(x.ifCondition)(function(e){return e.isEditorSidebarOpened}))(function(e){var t=e.sidebarName;return Object(r.createElement)(nn,{name:t,label:Object(g.__)("Editor settings")},Object(r.createElement)(rn,{sidebarName:t}),Object(r.createElement)(C.Panel,null,"edit-post/document"===t&&Object(r.createElement)(r.Fragment,null,Object(r.createElement)(hn,null),Object(r.createElement)(gn,null),Object(r.createElement)(kn,null),Object(r.createElement)(vn,null),Object(r.createElement)(Sn,null),Object(r.createElement)(Pn,null),Object(r.createElement)(wn,null),Object(r.createElement)(Mn,null),Object(r.createElement)(Yt,{location:"side"})),"edit-post/block"===t&&Object(r.createElement)(C.PanelBody,{className:"edit-post-settings-sidebar__panel-block"},Object(r.createElement)(c.BlockInspector,null))))}),Nn=Object(C.createSlotFill)("PluginPostPublishPanel"),An=Nn.Fill,Bn=Nn.Slot,In=function(e){var t=e.children,n=e.className,o=e.title,i=e.initialOpen,c=void 0!==i&&i;return Object(r.createElement)(An,null,Object(r.createElement)(C.PanelBody,{className:n,initialOpen:c||!o,title:o},t))};In.Slot=Bn;var Ln=In,Dn=Object(C.createSlotFill)("PluginPrePublishPanel"),Fn=Dn.Fill,Rn=Dn.Slot,Gn=function(e){var t=e.children,n=e.className,o=e.title,i=e.initialOpen,c=void 0!==i&&i;return Object(r.createElement)(Fn,null,Object(r.createElement)(C.PanelBody,{className:n,initialOpen:c||!o,title:o},t))};Gn.Slot=Rn;var Un=Gn,Vn=function(e){function t(){return Object(b.a)(this,t),Object(m.a)(this,Object(O.a)(t).apply(this,arguments))}return Object(f.a)(t,e),Object(p.a)(t,[{key:"componentDidMount",value:function(){this.isSticky=!1,this.sync(),document.body.classList.contains("sticky-menu")&&(this.isSticky=!0,document.body.classList.remove("sticky-menu"))}},{key:"componentWillUnmount",value:function(){this.isSticky&&document.body.classList.add("sticky-menu")}},{key:"componentDidUpdate",value:function(e){this.props.isActive!==e.isActive&&this.sync()}},{key:"sync",value:function(){this.props.isActive?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode")}},{key:"render",value:function(){return null}}]),t}(r.Component),Hn=Object(u.withSelect)(function(e){return{isActive:e("core/edit-post").isFeatureActive("fullscreenMode")}})(Vn);var Wn=Object(x.compose)(Object(u.withSelect)(function(e){return{mode:e("core/edit-post").getEditorMode(),editorSidebarOpened:e("core/edit-post").isEditorSidebarOpened(),pluginSidebarOpened:e("core/edit-post").isPluginSidebarOpened(),publishSidebarOpened:e("core/edit-post").isPublishSidebarOpened(),hasFixedToolbar:e("core/edit-post").isFeatureActive("fixedToolbar"),hasActiveMetaboxes:e("core/edit-post").hasMetaBoxes(),isSaving:e("core/edit-post").isSavingMetaBoxes(),isRichEditingEnabled:e("core/editor").getEditorSettings().richEditingEnabled}}),Object(u.withDispatch)(function(e){var t=e("core/edit-post");return{closePublishSidebar:t.closePublishSidebar,togglePublishSidebar:t.togglePublishSidebar}}),C.navigateRegions,Object(l.withViewportMatch)({isMobileViewport:"< small"}))(function(e){var t=e.mode,n=e.editorSidebarOpened,o=e.pluginSidebarOpened,i=e.publishSidebarOpened,a=e.hasFixedToolbar,l=e.closePublishSidebar,s=e.togglePublishSidebar,u=e.hasActiveMetaboxes,d=e.isSaving,b=e.isMobileViewport,p=e.isRichEditingEnabled,m=n||o||i,O=He()("edit-post-layout",{"is-sidebar-opened":m,"has-fixed-toolbar":a}),f={role:"region","aria-label":Object(g.__)("Editor publish"),tabIndex:-1};return Object(r.createElement)("div",{className:O},Object(r.createElement)(Hn,null),Object(r.createElement)(Qe,null),Object(r.createElement)(c.UnsavedChangesWarning,null),Object(r.createElement)(c.AutosaveMonitor,null),Object(r.createElement)(pt,null),Object(r.createElement)("div",{className:"edit-post-layout__content",role:"region","aria-label":Object(g.__)("Editor content"),tabIndex:"-1"},Object(r.createElement)(c.EditorNotices,null),Object(r.createElement)(c.PreserveScrollInReorder,null),Object(r.createElement)(_t,null),Object(r.createElement)(Lt,null),Object(r.createElement)(Qt,null),("text"===t||!p)&&Object(r.createElement)(mt,null),p&&"visual"===t&&Object(r.createElement)(Et,null),Object(r.createElement)("div",{className:"edit-post-layout__metaboxes"},Object(r.createElement)(Yt,{location:"normal"})),Object(r.createElement)("div",{className:"edit-post-layout__metaboxes"},Object(r.createElement)(Yt,{location:"advanced"}))),i?Object(r.createElement)(c.PostPublishPanel,Object(P.a)({},f,{onClose:l,forceIsDirty:u,forceIsSaving:d,PrePublishExtension:Un.Slot,PostPublishExtension:Ln.Slot})):Object(r.createElement)(r.Fragment,null,Object(r.createElement)("div",Object(P.a)({className:"edit-post-toggle-publish-panel"},f),Object(r.createElement)(C.Button,{isDefault:!0,type:"button",className:"edit-post-toggle-publish-panel__button",onClick:s,"aria-expanded":!1},Object(g.__)("Open publish panel"))),Object(r.createElement)(Tn,null),Object(r.createElement)(nn.Slot,null),b&&m&&Object(r.createElement)(C.ScrollLock,null)),Object(r.createElement)(C.Popover.Slot,null),Object(r.createElement)(N.PluginArea,null))});var qn=Object(u.withSelect)(function(e,t){var n=t.postId,o=t.postType;return{hasFixedToolbar:e("core/edit-post").isFeatureActive("fixedToolbar"),focusMode:e("core/edit-post").isFeatureActive("focusMode"),post:e("core").getEntityRecord("postType",o,n)}})(function(e){var t=e.settings,n=e.hasFixedToolbar,o=e.focusMode,i=e.post,a=e.initialEdits,l=e.onError,s=Object(k.a)(e,["settings","hasFixedToolbar","focusMode","post","initialEdits","onError"]);if(!i)return null;var u=Object(V.a)({},t,{hasFixedToolbar:n,focusMode:o});return Object(r.createElement)(r.StrictMode,null,Object(r.createElement)(c.EditorProvider,Object(P.a)({settings:u,post:i,initialEdits:a},s),Object(r.createElement)(c.ErrorBoundary,{onError:l},Object(r.createElement)(Wn,null),Object(r.createElement)(C.KeyboardShortcuts,{shortcuts:Ue})),Object(r.createElement)(c.PostLockedModal,null)))}),Qn=function(e,t){return!Array.isArray(t)||(n=e,o=t,0===Object(h.difference)(n,o).length);var n,o},Xn=function(e){var t=e.allowedBlocks,n=e.icon,o=e.label,i=e.onClick,c=e.small,a=e.role;return Object(r.createElement)(gt,null,function(e){var l=e.selectedBlocks,s=e.onClose;return Qn(l,t)?Object(r.createElement)(C.IconButton,{className:"editor-block-settings-menu__control",onClick:Object(x.compose)(i,s),icon:n||"admin-plugins",label:c?o:void 0,role:a},!c&&o):null})},Kn=Object(x.compose)(Object(N.withPluginContext)(function(e,t){return{icon:t.icon||e.icon}}))(function(e){var t=e.onClick,n=void 0===t?h.noop:t,o=Object(k.a)(e,["onClick"]);return Object(r.createElement)(Ze,null,function(e){return Object(r.createElement)(C.MenuItem,Object(P.a)({},o,{onClick:Object(x.compose)(n,e.onClose)}))})});var zn=Object(x.compose)(Object(N.withPluginContext)(function(e,t){return{icon:t.icon||e.icon,sidebarName:"".concat(e.name,"/").concat(t.name)}}),Object(u.withSelect)(function(e,t){var n=t.sidebarName,o=e("core/edit-post"),i=o.getActiveGeneralSidebarName,r=o.isPluginItemPinned;return{isActive:i()===n,isPinned:r(n)}}),Object(u.withDispatch)(function(e,t){var n=t.isActive,o=t.sidebarName,i=e("core/edit-post"),r=i.closeGeneralSidebar,c=i.openGeneralSidebar,a=i.togglePinnedPluginItem;return{togglePin:function(){a(o)},toggleSidebar:function(){n?r():c(o)}}}))(function(e){var t=e.children,n=e.icon,o=e.isActive,i=e.isPinnable,c=void 0===i||i,a=e.isPinned,l=e.sidebarName,s=e.title,u=e.togglePin,d=e.toggleSidebar;return Object(r.createElement)(r.Fragment,null,c&&Object(r.createElement)(dt,null,a&&Object(r.createElement)(C.IconButton,{icon:n,label:s,onClick:d,isToggled:o,"aria-expanded":o})),Object(r.createElement)(nn,{name:l,label:Object(g.__)("Editor plugins")},Object(r.createElement)(on,{closeLabel:Object(g.__)("Close plugin")},Object(r.createElement)("strong",null,s),c&&Object(r.createElement)(C.IconButton,{icon:a?"star-filled":"star-empty",label:a?Object(g.__)("Unpin from toolbar"):Object(g.__)("Pin to toolbar"),onClick:u,isToggled:a,"aria-expanded":a})),Object(r.createElement)(C.Panel,null,t)))}),Jn=Object(x.compose)(Object(N.withPluginContext)(function(e,t){return{icon:t.icon||e.icon,sidebarName:"".concat(e.name,"/").concat(t.target)}}),Object(u.withSelect)(function(e,t){var n=t.sidebarName;return{isSelected:(0,e("core/edit-post").getActiveGeneralSidebarName)()===n}}),Object(u.withDispatch)(function(e,t){var n=t.isSelected,o=t.sidebarName,i=e("core/edit-post"),r=i.closeGeneralSidebar,c=i.openGeneralSidebar;return{onClick:n?r:function(){return c(o)}}}))(function(e){var t=e.children,n=e.icon,o=e.isSelected,i=e.onClick;return Object(r.createElement)(Kn,{icon:o?"yes":n,isSelected:o,role:"menuitemcheckbox",onClick:i},t)});function Yn(e,t,n,o,i){Object(r.unmountComponentAtNode)(n);var c=Yn.bind(null,e,t,n,o,i);Object(r.render)(Object(r.createElement)(qn,{settings:o,onError:c,postId:t,postType:e,initialEdits:i,recovery:!0}),n)}function $n(e,t,n,o,i){var c=document.getElementById(e),a=Yn.bind(null,t,n,c,o,i);Object(s.registerCoreBlocks)(),"Standards"!==("CSS1Compat"===document.compatMode?"Standards":"Quirks")&&console.warn("Your browser is using Quirks Mode. \nThis can cause rendering issues such as blocks overlaying meta boxes in the editor. Quirks Mode can be triggered by PHP errors or HTML code appearing before the opening <!DOCTYPE html>. Try checking the raw page source or your site's PHP error log and resolving errors there, removing any HTML before the doctype, or disabling plugins."),Object(u.dispatch)("core/nux").triggerGuide(["core/editor.inserter","core/editor.settings","core/editor.preview","core/editor.publish"]),Object(r.render)(Object(r.createElement)(qn,{settings:o,onError:a,postId:n,postType:t,initialEdits:i}),c)}n.d(t,"reinitializeEditor",function(){return Yn}),n.d(t,"initializeEditor",function(){return $n}),n.d(t,"PluginBlockSettingsMenuItem",function(){return Xn}),n.d(t,"PluginMoreMenuItem",function(){return Kn}),n.d(t,"PluginPostPublishPanel",function(){return Ln}),n.d(t,"PluginPostStatusInfo",function(){return jn}),n.d(t,"PluginPrePublishPanel",function(){return Un}),n.d(t,"PluginSidebar",function(){return zn}),n.d(t,"PluginSidebarMoreMenuItem",function(){return Jn})},31:function(e,t,n){"use strict";var o,i;function r(e){return[e]}function c(){var e={clear:function(){e.head=null}};return e}function a(e,t,n){var o;if(e.length!==t.length)return!1;for(o=n;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}o={},i="undefined"!=typeof WeakMap,t.a=function(e,t){var n,l;function s(){n=i?new WeakMap:c()}function u(){var n,o,i,r,c,s=arguments.length;for(r=new Array(s),i=0;i<s;i++)r[i]=arguments[i];for(c=t.apply(null,r),(n=l(c)).isUniqueByDependants||(n.lastDependants&&!a(c,n.lastDependants,0)&&n.clear(),n.lastDependants=c),o=n.head;o;){if(a(o.args,r,1))return o!==n.head&&(o.prev.next=o.next,o.next&&(o.next.prev=o.prev),o.next=n.head,o.prev=null,n.head.prev=o,n.head=o),o.val;o=o.next}return o={val:e.apply(null,r)},r[0]=null,o.args=r,n.head&&(n.head.prev=o,o.next=n.head),n.head=o,o.val}return t||(t=r),l=i?function(e){var t,i,r,a,l,s=n,u=!0;for(t=0;t<e.length;t++){if(i=e[t],!(l=i)||"object"!=typeof l){u=!1;break}s.has(i)?s=s.get(i):(r=new WeakMap,s.set(i,r),s=r)}return s.has(o)||((a=c()).isUniqueByDependants=u,s.set(o,a)),s.get(o)}:function(){return n},u.getDependants=t,u.clear=s,s(),u}},33:function(e,t,n){"use strict";function o(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}n.d(t,"a",function(){return o})},35:function(e,t,n){"use strict";function o(e){if(Array.isArray(e))return e}n.d(t,"a",function(){return o})},36:function(e,t,n){"use strict";function o(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}n.d(t,"a",function(){return o})},37:function(e,t){!function(){e.exports=this.wp.viewport}()},4:function(e,t){!function(){e.exports=this.wp.components}()},44:function(e,t){!function(){e.exports=this.wp.a11y}()},5:function(e,t){!function(){e.exports=this.wp.data}()},52:function(e,t){!function(){e.exports=this.wp.nux}()},54:function(e,t){!function(){e.exports=this.wp.plugins}()},6:function(e,t){!function(){e.exports=this.wp.editor}()},7:function(e,t){!function(){e.exports=this.wp.compose}()},79:function(e,t){!function(){e.exports=this.wp.coreData}()},8:function(e,t,n){"use strict";n.d(t,"a",function(){return i});var o=n(15);function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),i.forEach(function(t){Object(o.a)(e,t,n[t])})}return e}},87:function(e,t,n){"use strict";e.exports=function(e){var t,n={};return function e(t,n){var o;if(Array.isArray(n))for(o=0;o<n.length;o++)e(t,n[o]);else for(o in n)t[o]=(t[o]||[]).concat(n[o])}(n,e),(t=function(e){return function(t){return function(o){var i,r,c=n[o.type],a=t(o);if(c)for(i=0;i<c.length;i++)(r=c[i](o,e))&&e.dispatch(r);return a}}}).effects=n,t}},9:function(e,t,n){"use strict";function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function i(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}n.d(t,"a",function(){return i})}});