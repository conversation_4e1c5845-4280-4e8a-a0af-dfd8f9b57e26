tinymce.WPWindowManager=tinymce.InlineWindowManager=function(a){return this.wp?this:(this.wp={},this.parent=a.windowManager,this.editor=a,tinymce.extend(this,this.parent),this.open=function(b,c){var d,e=this,f=this.wp;if(!b.wpDialog)return this.parent.open.apply(this,arguments);if(b.id){if("undefined"==typeof jQuery||!jQuery.wp||!jQuery.wp.wpdialog)return void(window.console&&window.console.error&&window.console.error('wpdialog.js is not loaded. Please set "wpdialogs" as dependency for your script when calling wp_enqueue_script(). You may also want to enqueue the "wp-jquery-ui-dialog" stylesheet.'));f.$element=d=jQuery("#"+b.id),d.length&&(window.console&&window.console.log&&window.console.log("tinymce.WPWindowManager is deprecated. Use the default editor.windowManager to open dialogs with inline HTML."),f.features=b,f.params=c,a.nodeChanged(),d.data("wpdialog")||d.wpdialog({title:b.title,width:b.width,height:b.height,modal:!0,dialogClass:"wp-dialog",zIndex:3e5}),d.wpdialog("open"),d.on("wpdialogclose",function(){e.wp.$element&&(e.wp={})}))}},void(this.close=function(){return this.wp.features&&this.wp.features.wpDialog?void this.wp.$element.wpdialog("close"):this.parent.close.apply(this,arguments)}))},tinymce.PluginManager.add("wpdialogs",function(a){a.on("init",function(){a.windowManager=new tinymce.WPWindowManager(a)})});