/* Generic */
body {
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
font-size:13px;
background:#fcfcfc;
padding:0;
margin:8px 8px 0 8px;
}

textarea {resize:none;outline:none;}

a:link, a:hover {
	color: #2B6FB6;
}

a:visited {
	color: #3C2BB6;
}

.nowrap {white-space: nowrap}

/* Forms */
form {margin: 0;}
fieldset {margin:0; padding:4px; border:1px solid #dfdfdf; font-family:Verdana, Arial; font-size:10px;}
legend {color:#2B6FB6; font-weight:bold;}
label.msg {display:none;}
label.invalid {color:#EE0000; display:inline;}
input.invalid {border:1px solid #EE0000;}
input {background:#FFF; border:1px solid #dfdfdf;}
input, select, textarea {font-family:Verdana, Arial, Helvetica, sans-serif; font-size:10px;}
input, select, textarea {border:1px solid #dfdfdf;}
input.radio {border:1px none #000000; background:transparent; vertical-align:middle;}
input.checkbox {border:1px none #000000; background:transparent; vertical-align:middle;}
.input_noborder {border:0;}

/* Buttons */
#insert,
#cancel,
#apply,
.mceActionPanel .button,
input.mceButton,
.updateButton {
	display: inline-block;
	text-decoration: none;
	border: 1px solid #adadad;
	margin: 0;
	padding: 0 10px 1px;
	font-size: 13px;
	height: 24px;
	line-height: 22px;
	color: #333;
	cursor: pointer;
	-webkit-border-radius: 3px;
	-webkit-appearance: none;
	border-radius: 3px;
	white-space: nowrap;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background: #fafafa;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#fafafa), to(#e9e9e9));
	background-image: -webkit-linear-gradient(top, #fafafa, #e9e9e9);
	background-image: -moz-linear-gradient(top, #fafafa, #e9e9e9);
	background-image: -o-linear-gradient(top, #fafafa, #e9e9e9);
	background-image: linear-gradient(to bottom, #fafafa, #e9e9e9);

	text-shadow: 0 1px 0 #fff;
	-webkit-box-shadow: inset 0 1px 0 #fff;
	-moz-box-shadow: inset 0 1px 0 #fff;
	box-shadow: inset 0 1px 0 #fff;
}

#insert {
	background: #2ea2cc;
	background: -webkit-gradient(linear, left top, left bottom, from(#2ea2cc), to(#1e8cbe));
	background: -webkit-linear-gradient(top, #2ea2cc 0%,#1e8cbe 100%);
	background: linear-gradient(top, #2ea2cc 0%,#1e8cbe 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2ea2cc', endColorstr='#1e8cbe',GradientType=0 );
	border-color: #0074a2;
	-webkit-box-shadow: inset 0 1px 0 rgba(120,200,230,0.5);
	box-shadow: inset 0 1px 0 rgba(120,200,230,0.5);
	color: #fff;
	text-decoration: none;
	text-shadow: 0 1px 0 rgba(0,86,132,0.7);
}

#cancel:hover,
input.mceButton:hover,
.updateButton:hover,
#cancel:focus,
input.mceButton:focus,
.updateButton:focus {
	background: #f3f3f3;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#f3f3f3));
	background-image: -webkit-linear-gradient(top, #fff, #f3f3f3);
	background-image: -moz-linear-gradient(top, #fff, #f3f3f3);
	background-image: -ms-linear-gradient(top, #fff, #f3f3f3);
	background-image: -o-linear-gradient(top, #fff, #f3f3f3);
	background-image: linear-gradient(to bottom, #fff, #f3f3f3);
	border-color: #999;
	color: #222;
}

#insert:hover,
#insert:focus {
	background: #1e8cbe;
	background: -webkit-gradient(linear, left top, left bottom, from(#1e8cbe), to(#0074a2));
	background: -webkit-linear-gradient(top, #1e8cbe 0%,#0074a2 100%);
	background: linear-gradient(top, #1e8cbe 0%,#0074a2 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1e8cbe', endColorstr='#0074a2',GradientType=0 );
	border-color: #0074a2;
	-webkit-box-shadow: inset 0 1px 0 rgba(120,200,230,0.6);
	box-shadow: inset 0 1px 0 rgba(120,200,230,0.6);
	color: #fff;
}

.mceActionPanel #insert {
	float: right;
}

/* Browse */
a.pickcolor, a.browse {text-decoration:none}
a.browse span {display:block; width:20px; height:18px; border:1px solid #FFF; margin-left:1px;}
.mceOldBoxModel a.browse span {width:22px; height:20px;}
a.browse:hover span {border:1px solid #0A246A; background-color:#B2BBD0;}
a.browse span.disabled {border:1px solid white; opacity:0.3; -ms-filter:'alpha(opacity=30)'; filter:alpha(opacity=30);}
a.browse:hover span.disabled {border:1px solid white; background-color:transparent;}
a.pickcolor span {display:block; width:20px; height:16px; margin-left:2px;}
.mceOldBoxModel a.pickcolor span {width:21px; height:17px;}
a.pickcolor:hover span {background-color:#B2BBD0;}
div.iframecontainer {background: #fff;}

/* Charmap */
table.charmap {border:1px solid #AAA; text-align:center}
td.charmap, #charmap a {width:18px; height:18px; color:#000; border:1px solid #AAA; text-align:center; font-size:12px; vertical-align:middle; line-height: 18px;}
#charmap a {display:block; color:#000; text-decoration:none; border:0}
#charmap a:hover {background:#CCC;color:#2B6FB6}
#charmap #codeN {font-size:10px; font-family:Arial,Helvetica,sans-serif; text-align:center}
#charmap #codeV {font-size:40px; height:80px; border:1px solid #AAA; text-align:center}
#charmap #charmapView {background-color:#fff;}

/* Source */
.wordWrapCode {vertical-align:middle; border:1px none #000000; background:transparent;}
.mceActionPanel {margin-top:5px;}

/* Tabs classes */
.tabs {width:100%; height:19px; line-height:normal; border-bottom: 1px solid #aaa;}
.tabs ul {margin:0; padding:0; list-style:none;}
.tabs li {float:left; border: 1px solid #aaa; margin:0 2px 0 0; padding:0 0 0 10px; line-height:17px; height:18px; display:block;}
.tabs li.current {border-bottom: 1px solid #fff; margin-right:2px;}
.tabs span {float:left; display:block; padding:0px 10px 0 0;}
.tabs a {text-decoration:none; font-family:Verdana, Arial; font-size:10px;}
.tabs a:link, .tabs a:visited, .tabs a:hover {color:black;}

.wp-core-ui #tabs {
	padding-bottom: 5px;
	background-color: transparent;
}

.wp-core-ui #tabs a {
	padding: 6px 10px;
	margin: 0 2px;
}

/* Panels */
.panel_wrapper div.panel {display:none;}
.panel_wrapper div.current {display:block; width:100%; height:300px; overflow:visible;}
.panel_wrapper {border:1px solid #919B9C; border-top:0px; padding:10px; padding-top:5px; clear:both; background:white;}

/* Columns */
.column {float:left;}
.properties {width:100%;}
.properties .column1 {}
.properties .column2 {text-align:left;}

/* Titles */
h1, h2, h3, h4 {color:#2B6FB6; margin:0; padding:0; padding-top:5px;}
h3 {font-size:14px;}
.title {font-size:12px; font-weight:bold; color:#2B6FB6;}

/* Dialog specific */
#link .panel_wrapper, #link div.current {height:125px;}
#image .panel_wrapper, #image div.current {height:200px;}
#plugintable thead {font-weight:bold; background:#DDD;}
#plugintable, #about #plugintable td {border:1px solid #919B9C;}
#plugintable {width:96%; margin-top:10px;}
#pluginscontainer {height:290px; overflow:auto;}
#colorpicker #preview {display:inline-block; padding-left:40px; height:14px; border:1px solid black; margin-left:5px; margin-right: 5px}
#colorpicker #previewblock {position: relative; top: -3px; padding-left:5px; padding-top: 0px; display:inline}
#colorpicker #preview_wrapper {text-align:center; padding-top:4px; white-space: nowrap; float: right;}
#colorpicker #insert, #colorpicker #cancel {width: 90px}
#colorpicker #colors {float:left; border:1px solid gray; cursor:crosshair;}
#colorpicker #light {border:1px solid gray; margin-left:5px; float:left;width:15px; height:150px; cursor:crosshair;}
#colorpicker #light div {overflow:hidden;}
#colorpicker .panel_wrapper div.current {height:175px;}
#colorpicker #namedcolors {width:150px;}
#colorpicker #namedcolors a {display:block; float:left; width:10px; height:10px; margin:1px 1px 0 0; overflow:hidden;}
#colorpicker #colornamecontainer {margin-top:5px;}
#colorpicker #picker_panel fieldset {margin:auto;width:325px;}


/* Localization */

body[dir="rtl"],
body[dir="rtl"] fieldset,
body[dir="rtl"] input, body[dir="rtl"] select, body[dir="rtl"]  textarea,
body[dir="rtl"]  #charmap #codeN,
body[dir="rtl"] .tabs a {
	font-family: Tahoma, sans-serif;
}
