!function(a,b){function c(a){return a.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}a.Env.ie&&a.Env.ie<9||a.PluginManager.add("wptextpattern",function(d){function e(){var e,f,g,h,i,k=d.selection.getRng(),l=k.startContainer,m=k.startOffset;if(l&&3===l.nodeType&&l.data.length&&m){var n=l.data.slice(0,m),p=l.data.charAt(m-1);a.each(o,function(a){if(p===a.delimiter.slice(-1)){var b=c(a.delimiter),d=a.delimiter.charAt(0),h=new RegExp("(.*)"+b+".+"+b+"$"),i=n.match(h);if(i){e=i[1].length,f=m-a.delimiter.length;var j=n.charAt(e-1),k=n.charAt(e+a.delimiter.length);if(!(e&&/\S/.test(j)&&(/\s/.test(k)||j===d)||new RegExp("^[\\s"+c(d)+"]+$").test(n.slice(e,f))))return g=a,!1}}}),g&&(h=d.formatter.get(g.format),h&&h[0].inline&&(d.undoManager.add(),d.undoManager.transact(function(){l.insertData(m,"\ufeff"),l=l.splitText(e),i=l.splitText(m-e),l.deleteData(0,g.delimiter.length),l.deleteData(l.data.length-g.delimiter.length,g.delimiter.length),d.formatter.apply(g.format,{},l),d.selection.setCursorLocation(i,1)}),b(function(){j="space",d.once("selectionchange",function(){var a;i&&(a=i.data.indexOf("\ufeff"),a!==-1&&i.deleteData(a,a+1))})})))}}function f(a){var b,c=d.dom.getParent(a,"p");if(c){for(;(b=c.firstChild)&&3!==b.nodeType;)c=b;if(b)return b.data||(b=b.nextSibling&&3===b.nextSibling.nodeType?b.nextSibling:null),b}}function g(){var c,e,g=d.selection.getRng(),h=g.startContainer;h&&f(h)===h&&(c=h.parentNode,e=h.data,a.each(m,function(a){var f=e.match(a.regExp);if(f&&g.startOffset===f[0].length)return d.undoManager.add(),d.undoManager.transact(function(){h.deleteData(0,f[0].length),c.innerHTML||c.appendChild(document.createElement("br")),d.selection.setCursorLocation(c),d.execCommand(a.cmd)}),b(function(){j="space"}),!1}))}function h(){var c,e,g,h=d.selection.getRng(),k=h.startContainer,l=f(k),m=n.length;if(l){for(c=l.data;m--;)if(n[m].start){if(0===c.indexOf(n[m].start)){e=n[m];break}}else if(n[m].regExp&&n[m].regExp.test(c)){e=n[m];break}e&&(l===k&&a.trim(c)===e.start||d.once("keyup",function(){d.undoManager.add(),d.undoManager.transact(function(){e.format?(d.formatter.apply(e.format,{},l),l.replaceData(0,l.data.length,i(l.data.slice(e.start.length)))):e.element&&(g=l.parentNode&&l.parentNode.parentNode,g&&g.replaceChild(document.createElement(e.element),l.parentNode))}),b(function(){j="enter"})}))}}function i(a){return a?a.replace(/^\s+/,""):""}var j,k=a.util.VK,l=d.settings.wptextpattern||{},m=l.space||[{regExp:/^[*-]\s/,cmd:"InsertUnorderedList"},{regExp:/^1[.)]\s/,cmd:"InsertOrderedList"}],n=l.enter||[{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:">",format:"blockquote"},{regExp:/^(-){3,}$/,element:"hr"}],o=l.inline||[{delimiter:"`",format:"code"}];d.on("selectionchange",function(){j=null}),d.on("keydown",function(a){(j&&27===a.keyCode||"space"===j&&a.keyCode===k.BACKSPACE)&&(d.undoManager.undo(),a.preventDefault(),a.stopImmediatePropagation()),k.metaKeyPressed(a)||(a.keyCode===k.ENTER?h():a.keyCode===k.SPACEBAR?b(g):a.keyCode>47&&!(a.keyCode>=91&&a.keyCode<=93)&&b(e))},!0)})}(window.tinymce,window.setTimeout);