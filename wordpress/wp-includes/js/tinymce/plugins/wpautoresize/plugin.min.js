tinymce.PluginManager.add("wpautoresize",function(a){function b(){return a.plugins.fullscreen&&a.plugins.fullscreen.isFullscreen()}function c(a){return parseInt(a,10)||0}function d(e){var f,g,k,l,m,n,o,p,q,r,s,t,u=tinymce.DOM;if(j&&(g=a.getDoc())){if(e=e||{},k=g.body,l=g.documentElement,m=h.autoresize_min_height,!k||e&&"setcontent"===e.type&&e.initial||b())return void(k&&l&&(k.style.overflowY="auto",l.style.overflowY="auto"));o=a.dom.getStyle(k,"margin-top",!0),p=a.dom.getStyle(k,"margin-bottom",!0),q=a.dom.getStyle(k,"padding-top",!0),r=a.dom.getStyle(k,"padding-bottom",!0),s=a.dom.getStyle(k,"border-top-width",!0),t=a.dom.getStyle(k,"border-bottom-width",!0),n=k.offsetHeight+c(o)+c(p)+c(q)+c(r)+c(s)+c(t),n&&n<l.offsetHeight&&(n=l.offsetHeight),(isNaN(n)||n<=0)&&(n=tinymce.Env.ie?k.scrollHeight:tinymce.Env.webkit&&0===k.clientHeight?0:k.offsetHeight),n>h.autoresize_min_height&&(m=n),h.autoresize_max_height&&n>h.autoresize_max_height?(m=h.autoresize_max_height,k.style.overflowY="auto",l.style.overflowY="auto"):(k.style.overflowY="hidden",l.style.overflowY="hidden",k.scrollTop=0),m!==i&&(f=m-i,u.setStyle(a.iframeElement,"height",m+"px"),i=m,tinymce.isWebKit&&f<0&&d(e),a.fire("wp-autoresize",{height:m,deltaHeight:"nodechange"===e.type?f:null}))}}function e(a,b,c){setTimeout(function(){d(),a--?e(a,b,c):c&&c()},b)}function f(){a.dom.hasClass(a.getBody(),"wp-autoresize")||(j=!0,a.dom.addClass(a.getBody(),"wp-autoresize"),a.on("nodechange setcontent keyup FullscreenStateChanged",d),d())}function g(){var b;h.wp_autoresize_on||(j=!1,b=a.getDoc(),a.dom.removeClass(a.getBody(),"wp-autoresize"),a.off("nodechange setcontent keyup FullscreenStateChanged",d),b.body.style.overflowY="auto",b.documentElement.style.overflowY="auto",i=0)}var h=a.settings,i=300,j=!1;a.settings.inline||tinymce.Env.iOS||(h.autoresize_min_height=parseInt(a.getParam("autoresize_min_height",a.getElement().offsetHeight),10),h.autoresize_max_height=parseInt(a.getParam("autoresize_max_height",0),10),h.wp_autoresize_on&&(j=!0,a.on("init",function(){a.dom.addClass(a.getBody(),"wp-autoresize")}),a.on("nodechange keyup FullscreenStateChanged",d),a.on("setcontent",function(){e(3,100)}),a.getParam("autoresize_on_init",!0)&&a.on("init",function(){e(10,200,function(){e(5,1e3)})})),a.on("show",function(){i=0}),a.addCommand("wpAutoResize",d),a.addCommand("wpAutoResizeOn",f),a.addCommand("wpAutoResizeOff",g))});