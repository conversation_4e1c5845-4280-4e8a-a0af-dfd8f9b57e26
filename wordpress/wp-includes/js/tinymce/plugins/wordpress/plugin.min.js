!function(a){(!a.ui.FloatPanel.zIndex||a.ui.FloatPanel.zIndex<100100)&&(a.ui.FloatPanel.zIndex=100100),a.PluginManager.add("wordpress",function(b){function c(c){var d,f,i=0,j=a.$(".block-library-classic__toolbar");"hide"===c?d=!0:j.length&&!j.hasClass("has-advanced-toolbar")&&(j.addClass("has-advanced-toolbar"),c="show"),b.theme.panel&&(f=b.theme.panel.find(".toolbar:not(.menubar)")),f&&f.length>1&&(!c&&f[1].visible()&&(c="hide"),h(f,function(a,b){b>0&&("hide"===c?(a.hide(),i+=30):(a.show(),i-=30))})),i&&!a.Env.iOS&&b.iframeElement&&g.setStyle(b.iframeElement,"height",b.iframeElement.clientHeight+i),d||("hide"===c?(setUserSetting("hidetb","0"),e&&e.active(!1)):(setUserSetting("hidetb","1"),e&&e.active(!0))),b.fire("wp-toolbar-toggle")}function d(){}var e,f,g=a.DOM,h=a.each,i=b.editorManager.i18n.translate,j=window.jQuery,k=window.wp,l=k&&k.editor&&k.editor.autop&&b.getParam("wpautop",!0);return j&&j(document).triggerHandler("tinymce-editor-setup",[b]),b.addButton("wp_adv",{tooltip:"Toolbar Toggle",cmd:"WP_Adv",onPostRender:function(){e=this,e.active("1"===getUserSetting("hidetb"))}}),b.on("PostRender",function(){b.getParam("wordpress_adv_hidden",!0)&&"0"===getUserSetting("hidetb","0")?c("hide"):a.$(".block-library-classic__toolbar").addClass("has-advanced-toolbar")}),b.addCommand("WP_Adv",function(){c()}),b.on("focus",function(){window.wpActiveEditor=b.id}),b.on("BeforeSetContent",function(b){var c;b.content&&(b.content.indexOf("<!--more")!==-1&&(c=i("Read more..."),b.content=b.content.replace(/<!--more(.*?)-->/g,function(b,d){return'<img src="'+a.Env.transparentSrc+'" data-wp-more="more" data-wp-more-text="'+d+'" class="wp-more-tag mce-wp-more" alt="" title="'+c+'" data-mce-resize="false" data-mce-placeholder="1" />'})),b.content.indexOf("<!--nextpage-->")!==-1&&(c=i("Page break"),b.content=b.content.replace(/<!--nextpage-->/g,'<img src="'+a.Env.transparentSrc+'" data-wp-more="nextpage" class="wp-more-tag mce-wp-nextpage" alt="" title="'+c+'" data-mce-resize="false" data-mce-placeholder="1" />')),b.load&&"raw"!==b.format&&(l?b.content=k.editor.autop(b.content):b.content=b.content.replace(/-->\s+<!--/g,"--><!--")),b.content.indexOf("<script")===-1&&b.content.indexOf("<style")===-1||(b.content=b.content.replace(/<(script|style)[^>]*>[\s\S]*?<\/\1>/g,function(b,c){return'<img src="'+a.Env.transparentSrc+'" data-wp-preserve="'+encodeURIComponent(b)+'" data-mce-resize="false" data-mce-placeholder="1" class="mce-object" width="20" height="20" alt="&lt;'+c+'&gt;" title="&lt;'+c+'&gt;" />'})))}),b.on("setcontent",function(){b.$("p").each(function(b,c){if(c.innerHTML&&c.innerHTML.length<10){var d=a.trim(c.innerHTML);d&&"&nbsp;"!==d||(c.innerHTML=a.Env.ie&&a.Env.ie<11?"":'<br data-mce-bogus="1">')}})}),b.on("PostProcess",function(a){a.get&&(a.content=a.content.replace(/<img[^>]+>/g,function(a){var b,c,d="";return a.indexOf('data-wp-more="more"')!==-1?((b=a.match(/data-wp-more-text="([^"]+)"/))&&(d=b[1]),c="<!--more"+d+"-->"):a.indexOf('data-wp-more="nextpage"')!==-1?c="<!--nextpage-->":a.indexOf("data-wp-preserve")!==-1&&(b=a.match(/ data-wp-preserve="([^"]+)"/))&&(c=decodeURIComponent(b[1])),c||a}))}),b.on("ResolveName",function(a){var c;"IMG"===a.target.nodeName&&(c=b.dom.getAttrib(a.target,"data-wp-more"))&&(a.name=c)}),b.addCommand("WP_More",function(c){var d,e,f,g="wp-more-tag",h=b.dom,j=b.selection.getNode(),k=b.getBody();return c=c||"more",g+=" mce-wp-"+c,f="more"===c?"Read more...":"Next page",f=i(f),e='<img src="'+a.Env.transparentSrc+'" alt="" title="'+f+'" class="'+g+'" data-wp-more="'+c+'" data-mce-resize="false" data-mce-placeholder="1" />',j===k||"P"===j.nodeName&&j.parentNode===k?void b.insertContent(e):(d=h.getParent(j,function(a){return!(!a.parentNode||a.parentNode!==k)},b.getBody()),void(d&&("P"===d.nodeName?d.appendChild(h.create("p",null,e).firstChild):h.insertAfter(h.create("p",null,e),d),b.nodeChanged())))}),b.addCommand("WP_Code",function(){b.formatter.toggle("code")}),b.addCommand("WP_Page",function(){b.execCommand("WP_More","nextpage")}),b.addCommand("WP_Help",function(){function c(a,b){var c="<tr>",d=0;for(b=b||1,h(a,function(a,b){c+="<td><kbd>"+b+"</kbd></td><td>"+i(a)+"</td>",d++});d<b;)c+="<td></td><td></td>",d++;return c+"</tr>"}var d,e,f,g,j=i(a.Env.mac?"Ctrl + Alt + letter:":"Shift + Alt + letter:"),k=i(a.Env.mac?"Cmd + letter:":"Ctrl + letter:"),l=[],m=[],n={},o={},p=0,q=0,r=b.settings.wp_shortcut_labels;r&&(h(r,function(a,b){var d;a.indexOf("meta")!==-1?(p++,d=a.replace("meta","").toLowerCase(),d&&(n[d]=b,p%2===0&&(l.push(c(n,2)),n={}))):a.indexOf("access")!==-1&&(q++,d=a.replace("access","").toLowerCase(),d&&(o[d]=b,q%2===0&&(m.push(c(o,2)),o={})))}),p%2>0&&l.push(c(n,2)),q%2>0&&m.push(c(o,2)),d=[i("Letter"),i("Action"),i("Letter"),i("Action")],d="<tr><th>"+d.join("</th><th>")+"</th></tr>",e='<div class="wp-editor-help">',e=e+"<h2>"+i("Default shortcuts,")+" "+k+'</h2><table class="wp-help-th-center fixed">'+d+l.join("")+"</table><h2>"+i("Additional shortcuts,")+" "+j+'</h2><table class="wp-help-th-center fixed">'+d+m.join("")+"</table>",b.plugins.wptextpattern&&(!a.Env.ie||a.Env.ie>8)&&(e=e+"<h2>"+i("When starting a new paragraph with one of these formatting shortcuts followed by a space, the formatting will be applied automatically. Press Backspace or Escape to undo.")+'</h2><table class="wp-help-th-center fixed">'+c({"*":"Bullet list","1.":"Numbered list"})+c({"-":"Bullet list","1)":"Numbered list"})+"</table>",e=e+"<h2>"+i("The following formatting shortcuts are replaced when pressing Enter. Press Escape or the Undo button to undo.")+'</h2><table class="wp-help-single">'+c({">":"Blockquote"})+c({"##":"Heading 2"})+c({"###":"Heading 3"})+c({"####":"Heading 4"})+c({"#####":"Heading 5"})+c({"######":"Heading 6"})+c({"---":"Horizontal line"})+"</table>"),e=e+"<h2>"+i("Focus shortcuts:")+'</h2><table class="wp-help-single">'+c({"Alt + F8":"Inline toolbar (when an image, link or preview is selected)"})+c({"Alt + F9":"Editor menu (when enabled)"})+c({"Alt + F10":"Editor toolbar"})+c({"Alt + F11":"Elements path"})+"</table><p>"+i("To move focus to other buttons use Tab or the arrow keys. To return focus to the editor press Escape or use one of the buttons.")+"</p>",e+="</div>",f=b.windowManager.open({title:b.settings.classic_block_editor?"Classic Block Keyboard Shortcuts":"Keyboard Shortcuts",items:{type:"container",classes:"wp-help",html:e},buttons:{text:"Close",onclick:"close"}}),f.$el&&(f.$el.find('div[role="application"]').attr("role","document"),g=f.$el.find(".mce-wp-help"),g[0]&&(g.attr("tabindex","0"),g[0].focus(),g.on("keydown",function(a){a.keyCode>=33&&a.keyCode<=40&&a.stopPropagation()}))))}),b.addCommand("WP_Medialib",function(){k&&k.media&&k.media.editor&&k.media.editor.open(b.id)}),b.addButton("wp_more",{tooltip:"Insert Read More tag",onclick:function(){b.execCommand("WP_More","more")}}),b.addButton("wp_page",{tooltip:"Page break",onclick:function(){b.execCommand("WP_More","nextpage")}}),b.addButton("wp_help",{tooltip:"Keyboard Shortcuts",cmd:"WP_Help"}),b.addButton("wp_code",{tooltip:"Code",cmd:"WP_Code",stateSelector:"code"}),k&&k.media&&k.media.editor&&(b.addButton("wp_add_media",{tooltip:"Add Media",icon:"dashicon dashicons-admin-media",cmd:"WP_Medialib"}),b.addMenuItem("add_media",{text:"Add Media",icon:"wp-media-library",context:"insert",cmd:"WP_Medialib"})),b.addMenuItem("wp_more",{text:"Insert Read More tag",icon:"wp_more",context:"insert",onclick:function(){b.execCommand("WP_More","more")}}),b.addMenuItem("wp_page",{text:"Page break",icon:"wp_page",context:"insert",onclick:function(){b.execCommand("WP_More","nextpage")}}),b.on("BeforeExecCommand",function(c){!a.Env.webkit||"InsertUnorderedList"!==c.command&&"InsertOrderedList"!==c.command||(f||(f=b.dom.create("style",{type:"text/css"},"#tinymce,#tinymce span,#tinymce li,#tinymce li>span,#tinymce p,#tinymce p>span{font:medium sans-serif;color:#000;line-height:normal;}")),b.getDoc().head.appendChild(f))}),b.on("ExecCommand",function(c){a.Env.webkit&&f&&("InsertUnorderedList"===c.command||"InsertOrderedList"===c.command)&&b.dom.remove(f)}),b.on("init",function(){var c=a.Env,d=["mceContentBody"],e=b.getDoc(),f=b.dom;if(c.iOS&&f.addClass(e.documentElement,"ios"),"rtl"===b.getParam("directionality")&&(d.push("rtl"),f.setAttrib(e.documentElement,"dir","rtl")),f.setAttrib(e.documentElement,"lang",b.getParam("wp_lang_attr")),c.ie?9===parseInt(c.ie,10)?d.push("ie9"):8===parseInt(c.ie,10)?d.push("ie8"):c.ie<8&&d.push("ie7"):c.webkit&&d.push("webkit"),d.push("wp-editor"),h(d,function(a){a&&f.addClass(e.body,a)}),b.on("BeforeSetContent",function(a){a.content&&(a.content=a.content.replace(/<p>\s*<(p|div|ul|ol|dl|table|blockquote|h[1-6]|fieldset|pre)( [^>]*)?>/gi,"<$1$2>").replace(/<\/(p|div|ul|ol|dl|table|blockquote|h[1-6]|fieldset|pre)>\s*<\/p>/gi,"</$1>"))}),j&&j(document).triggerHandler("tinymce-editor-init",[b]),window.tinyMCEPreInit&&window.tinyMCEPreInit.dragDropUpload&&f.bind(e,"dragstart dragend dragover drop",function(a){j&&j(document).trigger(new j.Event(a))}),b.getParam("wp_paste_filters",!0)&&(b.on("PastePreProcess",function(b){b.content=b.content.replace(/<br class="?Apple-interchange-newline"?>/gi,""),a.Env.webkit||(b.content=b.content.replace(/(<[^>]+) style="[^"]*"([^>]*>)/gi,"$1$2"),b.content=b.content.replace(/(<[^>]+) data-mce-style=([^>]+>)/gi,"$1 style=$2"))}),b.on("PastePostProcess",function(c){b.$("p",c.node).each(function(a,b){f.isEmpty(b)&&f.remove(b)}),a.isIE&&b.$("a",c.node).find("font, u").each(function(a,b){f.remove(b,!0)})})),b.settings.wp_shortcut_labels&&b.theme.panel){var g={},i="Shift+Alt+",k="Ctrl+";a.Env.mac&&(i="\u2303\u2325",k="\u2318"),h(b.settings.wp_shortcut_labels,function(a,b){g[b]=a.replace("access",i).replace("meta",k)}),h(b.theme.panel.find("button"),function(a){a&&a.settings.tooltip&&g.hasOwnProperty(a.settings.tooltip)&&(a.settings.tooltip=b.translate(a.settings.tooltip)+" ("+g[a.settings.tooltip]+")")}),h(b.theme.panel.find("listbox"),function(a){a&&"Paragraph"===a.settings.text&&h(a.settings.values,function(a){a.text&&g.hasOwnProperty(a.text)&&(a.shortcut="("+g[a.text]+")")})})}}),b.on("SaveContent",function(a){return!b.inline&&b.isHidden()?void(a.content=a.element.value):(a.content=a.content.replace(/<p>(?:<br ?\/?>|\u00a0|\uFEFF| )*<\/p>/g,"<p>&nbsp;</p>"),void(l?a.content=k.editor.removep(a.content):a.content=a.content.replace(/-->\s*<!-- wp:/g,"-->\n\n<!-- wp:")))}),b.on("preInit",function(){var c="@[id|accesskey|class|dir|lang|style|tabindex|title|contenteditable|draggable|dropzone|hidden|spellcheck|translate],i,b,script[src|async|defer|type|charset|crossorigin|integrity]";b.schema.addValidElements(c),a.Env.iOS&&(b.settings.height=300),h({c:"JustifyCenter",r:"JustifyRight",l:"JustifyLeft",j:"JustifyFull",q:"mceBlockQuote",u:"InsertUnorderedList",o:"InsertOrderedList",m:"WP_Medialib",t:"WP_More",d:"Strikethrough",p:"WP_Page",x:"WP_Code"},function(a,c){b.shortcuts.add("access+"+c,"",a)}),b.addShortcut("meta+s","",function(){k&&k.autosave&&k.autosave.server.triggerSave()}),b.settings.classic_block_editor||b.addShortcut("access+z","","WP_Adv"),b.on("keydown",function(a){return!a.shiftKey||!a.altKey||"KeyH"!==a.code||(b.execCommand("WP_Help"),a.stopPropagation(),a.stopImmediatePropagation(),!1)}),window.getUserSetting("editor_plain_text_paste_warning")>1&&(b.settings.paste_plaintext_inform=!1),a.Env.mac&&a.$(b.iframeElement).attr("title",i("Rich Text Area. Press Control-Option-H for help."))}),b.on("PastePlainTextToggle",function(a){if(a.state===!0){var b=parseInt(window.getUserSetting("editor_plain_text_paste_warning"),10)||0;b<2&&window.setUserSetting("editor_plain_text_paste_warning",++b)}}),b.on("preinit",function(){function c(c,d){function e(){if(!f)return this;var b,c,d=window.pageXOffset||document.documentElement.scrollLeft,e=window.pageYOffset||document.documentElement.scrollTop,h=window.innerWidth,i=window.innerHeight,m=q?q.getBoundingClientRect():{top:0,right:h,bottom:i,left:0,width:h,height:i},n=this.getEl(),o=n.offsetWidth,r=n.clientHeight,s=f.getBoundingClientRect(),t=(s.left+s.right)/2,u=5,v=r+u,w=p?p.getBoundingClientRect().bottom:0,x=j?j.getBoundingClientRect().bottom:0,y=k?i-k.getBoundingClientRect().top:0,z=l?i-l.getBoundingClientRect().top:0,A=Math.max(0,w,x,m.top),B=Math.max(0,y,z,i-m.bottom),C=s.top+m.top-A,D=i-m.top-s.bottom-B,E=i-A-B,F="",G=0,H=0;return C>=E||D>=E?(this.scrolling=!0,this.hide(),this.scrolling=!1,this):(a.Env.iOS&&"IMG"===f.nodeName&&(G=54,H=46),this.bottom?D>=v?(F=" mce-arrow-up",b=s.bottom+m.top+e-H):C>=v&&(F=" mce-arrow-down",b=s.top+m.top+e-r+G):C>=v?(F=" mce-arrow-down",b=s.top+m.top+e-r+G):D>=v&&E/2>s.bottom+m.top-A&&(F=" mce-arrow-up",b=s.bottom+m.top+e-H),"undefined"==typeof b&&(b=e+A+u+H),c=t-o/2+m.left+d,s.left<0||s.right>m.width?c=m.left+d+(m.width-o)/2:o>=h?(F+=" mce-arrow-full",c=0):c<0&&s.left+o>h||c+o>h&&s.right-o<0?c=(h-o)/2:c<m.left+d?(F+=" mce-arrow-left",c=s.left+m.left+d):c+o>m.width+m.left+d&&(F+=" mce-arrow-right",c=s.right-o+m.left+d),a.Env.iOS&&"IMG"===f.nodeName&&(F=F.replace(/ ?mce-arrow-(up|down)/g,"")),n.className=n.className.replace(/ ?mce-arrow-[\w]+/g,"")+F,g.setStyles(n,{left:c,top:b}),this)}var i,o,s=[];return h(c,function(a){function c(){var c=b.selection;"bullist"===d&&c.selectorChanged("ul > li",function(b,c){for(var d,e=c.parents.length;e--&&(d=c.parents[e].nodeName,"OL"!==d&&"UL"!=d););a.active(b&&"UL"===d)}),"numlist"===d&&c.selectorChanged("ol > li",function(b,c){for(var d,e=c.parents.length;e--&&(d=c.parents[e].nodeName,"OL"!==d&&"UL"!==d););a.active(b&&"OL"===d)}),a.settings.stateSelector&&c.selectorChanged(a.settings.stateSelector,function(b){a.active(b)},!0),a.settings.disabledStateSelector&&c.selectorChanged(a.settings.disabledStateSelector,function(b){a.disabled(b)})}var d;"|"===a?o=null:m.has(a)?(a={type:a},n.toolbar_items_size&&(a.size=n.toolbar_items_size),s.push(a),o=null):(o||(o={type:"buttongroup",items:[]},s.push(o)),b.buttons[a]&&(d=a,a=b.buttons[d],"function"==typeof a&&(a=a()),a.type=a.type||"button",n.toolbar_items_size&&(a.size=n.toolbar_items_size),a=m.create(a),o.items.push(a),b.initialized?c():b.on("init",c)))}),i=m.create({type:"panel",layout:"stack",classes:"toolbar-grp inline-toolbar-grp",ariaRoot:!0,ariaRemember:!0,items:[{type:"toolbar",layout:"flow",items:s}]}),i.bottom=d,i.on("show",function(){this.reposition(),r&&a.$(".mce-widget.mce-tooltip").addClass("wp-hide-mce-tooltip")}),i.on("hide",function(){r&&a.$(".mce-widget.mce-tooltip").removeClass("wp-hide-mce-tooltip")}),i.on("keydown",function(a){27===a.keyCode&&(this.hide(),b.focus())}),b.on("remove",function(){i.remove()}),i.reposition=e,i.hide().renderTo(document.body),i}function d(a){e&&(e.tempHide||"hide"===a.type||"blur"===a.type?(e.hide(),e=!1):"resizewindow"!==a.type&&"scrollwindow"!==a.type&&"resize"!==a.type&&"scroll"!==a.type||e.blockHide||(clearTimeout(i),i=setTimeout(function(){e&&"function"==typeof e.show&&(e.scrolling=!1,e.show())},250),e.scrolling=!0,e.hide()))}var e,f,i,j,k,l,m=a.ui.Factory,n=b.settings,o=b.getContainer(),p=document.getElementById("wpadminbar"),q=document.getElementById(b.id+"_ifr"),r=b.rtl&&/Chrome/.test(navigator.userAgent);o&&(j=a.$(".mce-toolbar-grp",o)[0],k=a.$(".mce-statusbar",o)[0]),"content"===b.id&&(l=document.getElementById("post-status-info")),b.shortcuts.add("alt+119","",function(){var a;e&&(a=e.find("toolbar")[0],a&&a.focus(!0))}),b.on("nodechange",function(a){var c=b.selection.isCollapsed(),d={element:a.element,parents:a.parents,collapsed:c};b.fire("wptoolbar",d),f=d.selection||d.element,e&&e!==d.toolbar&&e.hide(),d.toolbar?(e=d.toolbar,e.visible()?e.reposition():e.show()):e=!1}),b.on("focus",function(){e&&e.show()}),b.dom.bind(b.getWin(),"resize",d),b.inline?document.addEventListener("scroll",d,!0):(b.dom.bind(b.getWin(),"scroll",d),b.on("resizewindow scrollwindow",d)),b.on("remove",function(){document.removeEventListener("scroll",d,!0),b.off("resizewindow scrollwindow",d),b.dom.unbind(b.getWin(),"resize scroll",d)}),b.on("blur hide",d),b.wp=b.wp||{},b.wp._createToolbar=c},!0),{_showButtons:d,_hideButtons:d,_setEmbed:d,_getEmbed:d}})}(window.tinymce);