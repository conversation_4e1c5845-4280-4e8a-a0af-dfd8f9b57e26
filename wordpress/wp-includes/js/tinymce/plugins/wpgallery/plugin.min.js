tinymce.PluginManager.add("wpgallery",function(a){function b(a){return a.replace(/\[gallery([^\]]*)\]/g,function(a){return c("wp-gallery",a)})}function c(a,b){return b=window.encodeURIComponent(b),'<img src="'+tinymce.Env.transparentSrc+'" class="wp-media mceItem '+a+'" data-wp-media="'+b+'" data-mce-resize="false" data-mce-placeholder="1" alt="" />'}function d(a){function b(a,b){return b=new RegExp(b+'="([^"]+)"').exec(a),b?window.decodeURIComponent(b[1]):""}return a.replace(/(?:<p(?: [^>]+)?>)*(<img [^>]+>)(?:<\/p>)*/g,function(a,c){var d=b(c,"data-wp-media");return d?"<p>"+d+"</p>":a})}function e(b){var c,d,e;"IMG"===b.nodeName&&"undefined"!=typeof wp&&wp.media&&(e=window.decodeURIComponent(a.dom.getAttrib(b,"data-wp-media")),a.dom.hasClass(b,"wp-gallery")&&wp.media.gallery&&(c=wp.media.gallery,d=c.edit(e),d.state("gallery-edit").on("update",function(e){var f=c.shortcode(e).string();a.dom.setAttrib(b,"data-wp-media",window.encodeURIComponent(f)),d.detach()})))}a.addCommand("WP_Gallery",function(){e(a.selection.getNode())}),a.on("mouseup",function(b){function c(){d.removeClass(d.select("img.wp-media-selected"),"wp-media-selected")}var d=a.dom,f=b.target;"IMG"===f.nodeName&&d.getAttrib(f,"data-wp-media")?2!==b.button&&(d.hasClass(f,"wp-media-selected")?e(f):(c(),d.addClass(f,"wp-media-selected"))):c()}),a.on("ResolveName",function(b){var c=a.dom,d=b.target;"IMG"===d.nodeName&&c.getAttrib(d,"data-wp-media")&&c.hasClass(d,"wp-gallery")&&(b.name="gallery")}),a.on("BeforeSetContent",function(c){a.plugins.wpview&&"undefined"!=typeof wp&&wp.mce||(c.content=b(c.content))}),a.on("PostProcess",function(a){a.get&&(a.content=d(a.content))})});