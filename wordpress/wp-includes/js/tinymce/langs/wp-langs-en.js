/**
 * TinyMCE 3.x language strings
 *
 * Loaded only when external plugins are added to TinyMCE.
 */
( function() {
	var main = {}, lang = 'en';

	if ( typeof tinyMCEPreInit !== 'undefined' && tinyMCEPreInit.ref.language !== 'en' ) {
		lang = tinyMCEPreInit.ref.language;
	}

	main[lang] = {
		common: {
			edit_confirm: "Do you want to use the WYSIWYG mode for this textarea?",
			apply: "Apply",
			insert: "Insert",
			update: "Update",
			cancel: "Cancel",
			close: "Close",
			browse: "Browse",
			class_name: "Class",
			not_set: "-- Not set --",
			clipboard_msg: "Copy/Cut/Paste is not available in Mozilla and Firefox.",
			clipboard_no_support: "Currently not supported by your browser, use keyboard shortcuts instead.",
			popup_blocked: "Sorry, but we have noticed that your popup-blocker has disabled a window that provides application functionality. You will need to disable popup blocking on this site in order to fully utilize this tool.",
			invalid_data: "ERROR: Invalid values entered, these are marked in red.",
			invalid_data_number: "{#field} must be a number",
			invalid_data_min: "{#field} must be a number greater than {#min}",
			invalid_data_size: "{#field} must be a number or percentage",
			more_colors: "More colors"
		},
		colors: {
			"000000": "Black",
			"993300": "Burnt orange",
			"333300": "Dark olive",
			"003300": "Dark green",
			"003366": "Dark azure",
			"000080": "Navy Blue",
			"333399": "Indigo",
			"333333": "Very dark gray",
			"800000": "Maroon",
			"FF6600": "Orange",
			"808000": "Olive",
			"008000": "Green",
			"008080": "Teal",
			"0000FF": "Blue",
			"666699": "Grayish blue",
			"808080": "Gray",
			"FF0000": "Red",
			"FF9900": "Amber",
			"99CC00": "Yellow green",
			"339966": "Sea green",
			"33CCCC": "Turquoise",
			"3366FF": "Royal blue",
			"800080": "Purple",
			"999999": "Medium gray",
			"FF00FF": "Magenta",
			"FFCC00": "Gold",
			"FFFF00": "Yellow",
			"00FF00": "Lime",
			"00FFFF": "Aqua",
			"00CCFF": "Sky blue",
			"993366": "Brown",
			"C0C0C0": "Silver",
			"FF99CC": "Pink",
			"FFCC99": "Peach",
			"FFFF99": "Light yellow",
			"CCFFCC": "Pale green",
			"CCFFFF": "Pale cyan",
			"99CCFF": "Light sky blue",
			"CC99FF": "Plum",
			"FFFFFF": "White"
		},
		contextmenu: {
			align: "Alignment",
			left: "Left",
			center: "Center",
			right: "Right",
			full: "Full"
		},
		insertdatetime: {
			date_fmt: "%Y-%m-%d",
			time_fmt: "%H:%M:%S",
			insertdate_desc: "Insert date",
			inserttime_desc: "Insert time",
			months_long: "January,February,March,April,May,June,July,August,September,October,November,December",
			months_short: "Jan_January_abbreviation,Feb_February_abbreviation,Mar_March_abbreviation,Apr_April_abbreviation,May_May_abbreviation,Jun_June_abbreviation,Jul_July_abbreviation,Aug_August_abbreviation,Sep_September_abbreviation,Oct_October_abbreviation,Nov_November_abbreviation,Dec_December_abbreviation",
			day_long: "Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday",
			day_short: "Sun,Mon,Tue,Wed,Thu,Fri,Sat"
		},
		print: {
			print_desc: "Print"
		},
		preview: {
			preview_desc: "Preview"
		},
		directionality: {
			ltr_desc: "Direction left to right",
			rtl_desc: "Direction right to left"
		},
		layer: {
			insertlayer_desc: "Insert new layer",
			forward_desc: "Move forward",
			backward_desc: "Move backward",
			absolute_desc: "Toggle absolute positioning",
			content: "New layer..."
		},
		save: {
			save_desc: "Save",
			cancel_desc: "Cancel all changes"
		},
		nonbreaking: {
			nonbreaking_desc: "Insert non-breaking space character"
		},
		iespell: {
			iespell_desc: "Run spell checking",
			download: "ieSpell not detected. Do you want to install it now?"
		},
		advhr: {
			advhr_desc: "Horizontal rule"
		},
		emotions: {
			emotions_desc: "Emotions"
		},
		searchreplace: {
			search_desc: "Find",
			replace_desc: "Find/Replace"
		},
		advimage: {
			image_desc: "Insert/edit image"
		},
		advlink: {
			link_desc: "Insert/edit link"
		},
		xhtmlxtras: {
			cite_desc: "Citation",
			abbr_desc: "Abbreviation",
			acronym_desc: "Acronym",
			del_desc: "Deletion",
			ins_desc: "Insertion",
			attribs_desc: "Insert/Edit Attributes"
		},
		style: {
			desc: "Edit CSS Style"
		},
		paste: {
			paste_text_desc: "Paste as Plain Text",
			paste_word_desc: "Paste from Word",
			selectall_desc: "Select All",
			plaintext_mode_sticky: "Paste is now in plain text mode. Click again to toggle back to regular paste mode. After you paste something you will be returned to regular paste mode.",
			plaintext_mode: "Paste is now in plain text mode. Click again to toggle back to regular paste mode."
		},
		paste_dlg: {
			text_title: "Use CTRL + V on your keyboard to paste the text into the window.",
			text_linebreaks: "Keep linebreaks",
			word_title: "Use CTRL + V on your keyboard to paste the text into the window."
		},
		table: {
			desc: "Inserts a new table",
			row_before_desc: "Insert row before",
			row_after_desc: "Insert row after",
			delete_row_desc: "Delete row",
			col_before_desc: "Insert column before",
			col_after_desc: "Insert column after",
			delete_col_desc: "Remove column",
			split_cells_desc: "Split merged table cells",
			merge_cells_desc: "Merge table cells",
			row_desc: "Table row properties",
			cell_desc: "Table cell properties",
			props_desc: "Table properties",
			paste_row_before_desc: "Paste table row before",
			paste_row_after_desc: "Paste table row after",
			cut_row_desc: "Cut table row",
			copy_row_desc: "Copy table row",
			del: "Delete table",
			row: "Row",
			col: "Column",
			cell: "Cell"
		},
		autosave: {
			unload_msg: "The changes you made will be lost if you navigate away from this page."
		},
		fullscreen: {
			desc: "Toggle fullscreen mode (Alt + Shift + G)"
		},
		media: {
			desc: "Insert / edit embedded media",
			edit: "Edit embedded media"
		},
		fullpage: {
			desc: "Document properties"
		},
		template: {
			desc: "Insert predefined template content"
		},
		visualchars: {
			desc: "Visual control characters on/off."
		},
		spellchecker: {
			desc: "Toggle spellchecker (Alt + Shift + N)",
			menu: "Spellchecker settings",
			ignore_word: "Ignore word",
			ignore_words: "Ignore all",
			langs: "Languages",
			wait: "Please wait...",
			sug: "Suggestions",
			no_sug: "No suggestions",
			no_mpell: "No misspellings found.",
			learn_word: "Learn word"
		},
		pagebreak: {
			desc: "Insert Page Break"
		},
		advlist:{
			types: "Types",
			def: "Default",
			lower_alpha: "Lower alpha",
			lower_greek: "Lower greek",
			lower_roman: "Lower roman",
			upper_alpha: "Upper alpha",
			upper_roman: "Upper roman",
			circle: "Circle",
			disc: "Disc",
			square: "Square"
		},
		aria: {
			rich_text_area: "Rich Text Area"
		},
		wordcount:{
			words: "Words: "
		}
	};

	tinyMCE.addI18n( main );

	tinyMCE.addI18n( lang + ".advanced", {
		style_select: "Styles",
		font_size: "Font size",
		fontdefault: "Font family",
		block: "Format",
		paragraph: "Paragraph",
		div: "Div",
		address: "Address",
		pre: "Preformatted",
		h1: "Heading 1",
		h2: "Heading 2",
		h3: "Heading 3",
		h4: "Heading 4",
		h5: "Heading 5",
		h6: "Heading 6",
		blockquote: "Blockquote",
		code: "Code",
		samp: "Code sample",
		dt: "Definition term ",
		dd: "Definition description",
		bold_desc: "Bold (Ctrl + B)",
		italic_desc: "Italic (Ctrl + I)",
		underline_desc: "Underline",
		striketrough_desc: "Strikethrough (Alt + Shift + D)",
		justifyleft_desc: "Align Left (Alt + Shift + L)",
		justifycenter_desc: "Align Center (Alt + Shift + C)",
		justifyright_desc: "Align Right (Alt + Shift + R)",
		justifyfull_desc: "Align Full (Alt + Shift + J)",
		bullist_desc: "Unordered list (Alt + Shift + U)",
		numlist_desc: "Ordered list (Alt + Shift + O)",
		outdent_desc: "Outdent",
		indent_desc: "Indent",
		undo_desc: "Undo (Ctrl + Z)",
		redo_desc: "Redo (Ctrl + Y)",
		link_desc: "Insert/edit link (Alt + Shift + A)",
		unlink_desc: "Unlink (Alt + Shift + S)",
		image_desc: "Insert/edit image (Alt + Shift + M)",
		cleanup_desc: "Cleanup messy code",
		code_desc: "Edit HTML Source",
		sub_desc: "Subscript",
		sup_desc: "Superscript",
		hr_desc: "Insert horizontal ruler",
		removeformat_desc: "Remove formatting",
		forecolor_desc: "Select text color",
		backcolor_desc: "Select background color",
		charmap_desc: "Insert custom character",
		visualaid_desc: "Toggle guidelines/invisible elements",
		anchor_desc: "Insert/edit anchor",
		cut_desc: "Cut",
		copy_desc: "Copy",
		paste_desc: "Paste",
		image_props_desc: "Image properties",
		newdocument_desc: "New document",
		help_desc: "Help",
		blockquote_desc: "Blockquote (Alt + Shift + Q)",
		clipboard_msg: "Copy/Cut/Paste is not available in Mozilla and Firefox.",
		path: "Path",
		newdocument: "Are you sure you want to clear all contents?",
		toolbar_focus: "Jump to tool buttons - Alt+Q, Jump to editor - Alt-Z, Jump to element path - Alt-X",
		more_colors: "More colors",
		shortcuts_desc: "Accessibility Help",
		help_shortcut: " Press ALT F10 for toolbar. Press ALT 0 for help.",
		rich_text_area: "Rich Text Area",
		toolbar: "Toolbar"
	});

	tinyMCE.addI18n( lang + ".advanced_dlg", {
		about_title: "About TinyMCE",
		about_general: "About",
		about_help: "Help",
		about_license: "License",
		about_plugins: "Plugins",
		about_plugin: "Plugin",
		about_author: "Author",
		about_version: "Version",
		about_loaded: "Loaded plugins",
		anchor_title: "Insert/edit anchor",
		anchor_name: "Anchor name",
		code_title: "HTML Source Editor",
		code_wordwrap: "Word wrap",
		colorpicker_title: "Select a color",
		colorpicker_picker_tab: "Picker",
		colorpicker_picker_title: "Color picker",
		colorpicker_palette_tab: "Palette",
		colorpicker_palette_title: "Palette colors",
		colorpicker_named_tab: "Named",
		colorpicker_named_title: "Named colors",
		colorpicker_color: "Color: ",
		colorpicker_name: "Name: ",
		charmap_title: "Select custom character",
		charmap_usage: "Use left and right arrows to navigate.",
		image_title: "Insert/edit image",
		image_src: "Image URL",
		image_alt: "Image description",
		image_list: "Image list",
		image_border: "Border",
		image_dimensions: "Dimensions",
		image_vspace: "Vertical space",
		image_hspace: "Horizontal space",
		image_align: "Alignment",
		image_align_baseline: "Baseline",
		image_align_top: "Top",
		image_align_middle: "Middle",
		image_align_bottom: "Bottom",
		image_align_texttop: "Text top",
		image_align_textbottom: "Text bottom",
		image_align_left: "Left",
		image_align_right: "Right",
		link_title: "Insert/edit link",
		link_url: "Link URL",
		link_target: "Target",
		link_target_same: "Open link in the same window",
		link_target_blank: "Open link in a new window",
		link_titlefield: "Title",
		link_is_email: "The URL you entered seems to be an email address, do you want to add the required mailto: prefix?",
		link_is_external: "The URL you entered seems to be an external link, do you want to add the required http:// prefix?",
		link_list: "Link list",
		accessibility_help: "Accessibility Help",
		accessibility_usage_title: "General Usage"
	});

	tinyMCE.addI18n( lang + ".media_dlg", {
		title: "Insert / edit embedded media",
		general: "General",
		advanced: "Advanced",
		file: "File/URL",
		list: "List",
		size: "Dimensions",
		preview: "Preview",
		constrain_proportions: "Constrain proportions",
		type: "Type",
		id: "Id",
		name: "Name",
		class_name: "Class",
		vspace: "V-Space",
		hspace: "H-Space",
		play: "Auto play",
		loop: "Loop",
		menu: "Show menu",
		quality: "Quality",
		scale: "Scale",
		align: "Align",
		salign: "SAlign",
		wmode: "WMode",
		bgcolor: "Background",
		base: "Base",
		flashvars: "Flashvars",
		liveconnect: "SWLiveConnect",
		autohref: "AutoHREF",
		cache: "Cache",
		hidden: "Hidden",
		controller: "Controller",
		kioskmode: "Kiosk mode",
		playeveryframe: "Play every frame",
		targetcache: "Target cache",
		correction: "No correction",
		enablejavascript: "Enable JavaScript",
		starttime: "Start time",
		endtime: "End time",
		href: "href",
		qtsrcchokespeed: "Choke speed",
		target: "Target",
		volume: "Volume",
		autostart: "Auto start",
		enabled: "Enabled",
		fullscreen: "Fullscreen",
		invokeurls: "Invoke URLs",
		mute: "Mute",
		stretchtofit: "Stretch to fit",
		windowlessvideo: "Windowless video",
		balance: "Balance",
		baseurl: "Base URL",
		captioningid: "Captioning id",
		currentmarker: "Current marker",
		currentposition: "Current position",
		defaultframe: "Default frame",
		playcount: "Play count",
		rate: "Rate",
		uimode: "UI Mode",
		flash_options: "Flash options",
		qt_options: "QuickTime options",
		wmp_options: "Windows media player options",
		rmp_options: "Real media player options",
		shockwave_options: "Shockwave options",
		autogotourl: "Auto goto URL",
		center: "Center",
		imagestatus: "Image status",
		maintainaspect: "Maintain aspect",
		nojava: "No java",
		prefetch: "Prefetch",
		shuffle: "Shuffle",
		console: "Console",
		numloop: "Num loops",
		controls: "Controls",
		scriptcallbacks: "Script callbacks",
		swstretchstyle: "Stretch style",
		swstretchhalign: "Stretch H-Align",
		swstretchvalign: "Stretch V-Align",
		sound: "Sound",
		progress: "Progress",
		qtsrc: "QT Src",
		qt_stream_warn: "Streamed rtsp resources should be added to the QT Src field under the advanced tab.",
		align_top: "Top",
		align_right: "Right",
		align_bottom: "Bottom",
		align_left: "Left",
		align_center: "Center",
		align_top_left: "Top left",
		align_top_right: "Top right",
		align_bottom_left: "Bottom left",
		align_bottom_right: "Bottom right",
		flv_options: "Flash video options",
		flv_scalemode: "Scale mode",
		flv_buffer: "Buffer",
		flv_startimage: "Start image",
		flv_starttime: "Start time",
		flv_defaultvolume: "Default volume",
		flv_hiddengui: "Hidden GUI",
		flv_autostart: "Auto start",
		flv_loop: "Loop",
		flv_showscalemodes: "Show scale modes",
		flv_smoothvideo: "Smooth video",
		flv_jscallback: "JS Callback",
		html5_video_options: "HTML5 Video Options",
		altsource1: "Alternative source 1",
		altsource2: "Alternative source 2",
		preload: "Preload",
		poster: "Poster",
		source: "Source"
	});

	tinyMCE.addI18n( lang + ".wordpress", {
		wp_adv_desc: "Show/Hide Kitchen Sink (Alt + Shift + Z)",
		wp_more_desc: "Insert More Tag (Alt + Shift + T)",
		wp_page_desc: "Insert Page break (Alt + Shift + P)",
		wp_help_desc: "Help (Alt + Shift + H)",
		wp_more_alt: "More...",
		wp_page_alt: "Next page...",
		add_media: "Add Media",
		add_image: "Add an Image",
		add_video: "Add Video",
		add_audio: "Add Audio",
		editgallery: "Edit Gallery",
		delgallery: "Delete Gallery",
		wp_fullscreen_desc: "Distraction-free writing mode (Alt + Shift + W)"
	});

	tinyMCE.addI18n( lang + ".wpeditimage", {
		edit_img: "Edit Image",
		del_img: "Delete Image",
		adv_settings: "Advanced Settings",
		none: "None",
		size: "Size",
		thumbnail: "Thumbnail",
		medium: "Medium",
		full_size: "Full Size",
		current_link: "Current Link",
		link_to_img: "Link to Image",
		link_help: "Enter a link URL or click above for presets.",
		adv_img_settings: "Advanced Image Settings",
		source: "Source",
		width: "Width",
		height: "Height",
		orig_size: "Original Size",
		css: "CSS Class",
		adv_link_settings: "Advanced Link Settings",
		link_rel: "Link Rel",
		height: "Height",
		orig_size: "Original Size",
		css: "CSS Class",
		s60: "60%",
		s70: "70%",
		s80: "80%",
		s90: "90%",
		s100: "100%",
		s110: "110%",
		s120: "120%",
		s130: "130%",
		img_title: "Title",
		caption: "Caption",
		alt: "Alternative Text"
	});
}());
