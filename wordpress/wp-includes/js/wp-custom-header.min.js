!function(a,b){function c(b,c){var d;"function"==typeof a.Event?d=new Event(c):(d=document.createEvent("Event"),d.initEvent(c,!0,!0)),b.dispatchEvent(d)}function d(){this.handlers={nativeVideo:new f,youtube:new g}}function e(){}var f,g;a.wp=a.wp||{},"addEventListener"in a&&(d.prototype={initialize:function(){if(this.supportsVideo())for(var a in this.handlers){var d=this.handlers[a];if("test"in d&&d.test(b)){this.activeHandler=d.initialize.call(d,b),c(document,"wp-custom-header-video-loaded");break}}},supportsVideo:function(){return!(a.innerWidth<b.minWidth||a.innerHeight<b.minHeight)},BaseVideoHandler:e},e.prototype={initialize:function(b){var c=this,d=document.createElement("button");this.settings=b,this.container=document.getElementById("wp-custom-header"),this.button=d,d.setAttribute("type","button"),d.setAttribute("id","wp-custom-header-video-button"),d.setAttribute("class","wp-custom-header-video-button wp-custom-header-video-play"),d.innerHTML=b.l10n.play,d.addEventListener("click",function(){c.isPaused()?c.play():c.pause()}),this.container.addEventListener("play",function(){d.className="wp-custom-header-video-button wp-custom-header-video-play",d.innerHTML=b.l10n.pause,"a11y"in a.wp&&a.wp.a11y.speak(b.l10n.playSpeak)}),this.container.addEventListener("pause",function(){d.className="wp-custom-header-video-button wp-custom-header-video-pause",d.innerHTML=b.l10n.play,"a11y"in a.wp&&a.wp.a11y.speak(b.l10n.pauseSpeak)}),this.ready()},ready:function(){},isPaused:function(){},pause:function(){},play:function(){},setVideo:function(a){var b,c=this.container.getElementsByClassName("customize-partial-edit-shortcut");c.length&&(b=this.container.removeChild(c[0])),this.container.innerHTML="",this.container.appendChild(a),b&&this.container.appendChild(b)},showControls:function(){this.container.contains(this.button)||this.container.appendChild(this.button)},test:function(){return!1},trigger:function(a){c(this.container,a)}},e.extend=function(a){function b(){var a=e.apply(this,arguments);return a}var c;b.prototype=Object.create(e.prototype),b.prototype.constructor=b;for(c in a)b.prototype[c]=a[c];return b},f=e.extend({test:function(a){var b=document.createElement("video");return b.canPlayType(a.mimeType)},ready:function(){var a=this,b=document.createElement("video");b.id="wp-custom-header-video",b.autoplay="autoplay",b.loop="loop",b.muted="muted",b.width=this.settings.width,b.height=this.settings.height,b.addEventListener("play",function(){a.trigger("play")}),b.addEventListener("pause",function(){a.trigger("pause")}),b.addEventListener("canplay",function(){a.showControls()}),this.video=b,a.setVideo(b),b.src=this.settings.videoUrl},isPaused:function(){return this.video.paused},pause:function(){this.video.pause()},play:function(){this.video.play()}}),g=e.extend({test:function(a){return"video/x-youtube"===a.mimeType},ready:function(){var b=this;if("YT"in a)YT.ready(b.loadVideo.bind(b));else{var c=document.createElement("script");c.src="https://www.youtube.com/iframe_api",c.onload=function(){YT.ready(b.loadVideo.bind(b))},document.getElementsByTagName("head")[0].appendChild(c)}},loadVideo:function(){var a=this,b=document.createElement("div"),c=/^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;b.id="wp-custom-header-video",a.setVideo(b),a.player=new YT.Player(b,{height:this.settings.height,width:this.settings.width,videoId:this.settings.videoUrl.match(c)[1],events:{onReady:function(b){b.target.mute(),a.showControls()},onStateChange:function(b){YT.PlayerState.PLAYING===b.data?a.trigger("play"):YT.PlayerState.PAUSED===b.data?a.trigger("pause"):YT.PlayerState.ENDED===b.data&&b.target.playVideo()}},playerVars:{autoplay:1,controls:0,disablekb:1,fs:0,iv_load_policy:3,loop:1,modestbranding:1,playsinline:1,rel:0,showinfo:0}})},isPaused:function(){return YT.PlayerState.PAUSED===this.player.getPlayerState()},pause:function(){this.player.pauseVideo()},play:function(){this.player.playVideo()}}),a.wp.customHeader=new d,document.addEventListener("DOMContentLoaded",a.wp.customHeader.initialize.bind(a.wp.customHeader),!1),"customize"in a.wp&&(a.wp.customize.selectiveRefresh.bind("render-partials-response",function(a){"custom_header_settings"in a&&(b=a.custom_header_settings)}),a.wp.customize.selectiveRefresh.bind("partial-content-rendered",function(b){"custom_header"===b.partial.id&&a.wp.customHeader.initialize()})))}(window,window._wpCustomHeaderSettings||{});