window.wp=window.wp||{},function(a,b){var c,d=wp.customize;b.extend(b.support,{history:!(!window.history||!history.pushState),hashchange:"onhashchange"in window&&(void 0===document.documentMode||document.documentMode>7)}),c=b.extend({},d.Events,{initialize:function(){this.body=b(document.body),c.settings&&b.support.postMessage&&(b.support.cors||!c.settings.isCrossDomain)&&(this.window=b(window),this.element=b('<div id="customize-container" />').appendTo(this.body),this.bind("open",this.overlay.show),this.bind("close",this.overlay.hide),b("#wpbody").on("click",".load-customize",function(a){a.preventDefault(),c.link=b(this),c.open(c.link.attr("href"))}),b.support.history&&this.window.on("popstate",c.popstate),b.support.hashchange&&(this.window.on("hashchange",c.hashchange),this.window.triggerHandler("hashchange")))},popstate:function(a){var b=a.originalEvent.state;b&&b.customize?c.open(b.customize):c.active&&c.close()},hashchange:function(){var a=window.location.toString().split("#")[1];a&&0===a.indexOf("wp_customize=on")&&c.open(c.settings.url+"?"+a),a||b.support.history||c.close()},beforeunload:function(){if(!c.saved())return c.settings.l10n.saveAlert},open:function(a){if(!this.active){if(c.settings.browser.mobile)return window.location=a;this.originalDocumentTitle=document.title,this.active=!0,this.body.addClass("customize-loading"),this.saved=new d.Value(!0),this.iframe=b("<iframe />",{src:a,title:c.settings.l10n.mainIframeTitle}).appendTo(this.element),this.iframe.one("load",this.loaded),this.messenger=new d.Messenger({url:a,channel:"loader",targetWindow:this.iframe[0].contentWindow}),history.replaceState&&this.messenger.bind("changeset-uuid",function(a){var c=document.createElement("a");c.href=location.href,c.search=b.param(_.extend(d.utils.parseQueryString(c.search.substr(1)),{changeset_uuid:a})),history.replaceState({customize:c.href},"",c.href)}),this.messenger.bind("ready",function(){c.messenger.send("back")}),this.messenger.bind("close",function(){b.support.history?history.back():b.support.hashchange?window.location.hash="":c.close()}),b(window).on("beforeunload",this.beforeunload),this.messenger.bind("saved",function(){c.saved(!0)}),this.messenger.bind("change",function(){c.saved(!1)}),this.messenger.bind("title",function(a){window.document.title=a}),this.pushState(a),this.trigger("open")}},pushState:function(a){var c=a.split("?")[1];b.support.history&&window.location.href!==a?history.pushState({customize:a},"",a):!b.support.history&&b.support.hashchange&&c&&(window.location.hash="wp_customize=on&"+c),this.trigger("open")},opened:function(){c.body.addClass("customize-active full-overlay-active").attr("aria-busy","true")},close:function(){var a,b=this;b.active&&(a=function(c){c?(b.active=!1,b.trigger("close"),b.originalDocumentTitle&&(document.title=b.originalDocumentTitle)):history.forward(),b.messenger.unbind("confirmed-close",a)},b.messenger.bind("confirmed-close",a),c.messenger.send("confirm-close"))},closed:function(){c.iframe.remove(),c.messenger.destroy(),c.iframe=null,c.messenger=null,c.saved=null,c.body.removeClass("customize-active full-overlay-active").removeClass("customize-loading"),b(window).off("beforeunload",c.beforeunload),c.link&&c.link.focus()},loaded:function(){c.body.removeClass("customize-loading").attr("aria-busy","false")},overlay:{show:function(){this.element.fadeIn(200,c.opened)},hide:function(){this.element.fadeOut(200,c.closed)}}}),b(function(){c.settings=_wpCustomizeLoaderSettings,c.initialize()}),d.Loader=c}(wp,jQuery);