!function(a,b){var c=b.customize;c.HeaderTool={},c.<PERSON>er<PERSON>ool.ImageModel=Backbone.Model.extend({defaults:function(){return{header:{attachment_id:0,url:"",timestamp:_.now(),thumbnail_url:""},choice:"",selected:!1,random:!1}},initialize:function(){this.on("hide",this.hide,this)},hide:function(){this.set("choice",""),c("header_image").set("remove-header"),c("header_image_data").set("remove-header")},destroy:function(){var a=this.get("header"),d=c.HeaderTool.currentHeader.get("header").attachment_id;d&&a.attachment_id===d&&c.HeaderTool.currentHeader.trigger("hide"),b.ajax.post("custom-header-remove",{nonce:_wpCustomizeHeader.nonces.remove,wp_customize:"on",theme:c.settings.theme.stylesheet,attachment_id:a.attachment_id}),this.trigger("destroy",this,this.collection)},save:function(){this.get("random")?(c("header_image").set(this.get("header").random),c("header_image_data").set(this.get("header").random)):this.get("header").defaultName?(c("header_image").set(this.get("header").url),c("header_image_data").set(this.get("header").defaultName)):(c("header_image").set(this.get("header").url),c("header_image_data").set(this.get("header"))),c.HeaderTool.combinedList.trigger("control:setImage",this)},importImage:function(){var a=this.get("header");void 0!==a.attachment_id&&b.ajax.post("custom-header-add",{nonce:_wpCustomizeHeader.nonces.add,wp_customize:"on",theme:c.settings.theme.stylesheet,attachment_id:a.attachment_id})},shouldBeCropped:function(){return(this.get("themeFlexWidth")!==!0||this.get("themeFlexHeight")!==!0)&&((this.get("themeFlexWidth")!==!0||this.get("themeHeight")!==this.get("imageHeight"))&&((this.get("themeFlexHeight")!==!0||this.get("themeWidth")!==this.get("imageWidth"))&&((this.get("themeWidth")!==this.get("imageWidth")||this.get("themeHeight")!==this.get("imageHeight"))&&!(this.get("imageWidth")<=this.get("themeWidth")))))}}),c.HeaderTool.ChoiceList=Backbone.Collection.extend({model:c.HeaderTool.ImageModel,comparator:function(a){return-a.get("header").timestamp},initialize:function(){var a=c.HeaderTool.currentHeader.get("choice").replace(/^https?:\/\//,""),b=this.isRandomChoice(c.get().header_image);this.type||(this.type="uploaded"),"undefined"==typeof this.data&&(this.data=_wpCustomizeHeader.uploads),b&&(a=c.get().header_image),this.on("control:setImage",this.setImage,this),this.on("control:removeImage",this.removeImage,this),this.on("add",this.maybeRemoveOldCrop,this),this.on("add",this.maybeAddRandomChoice,this),_.each(this.data,function(b,c){b.attachment_id||(b.defaultName=c),"undefined"==typeof b.timestamp&&(b.timestamp=0),this.add({header:b,choice:b.url.split("/").pop(),selected:a===b.url.replace(/^https?:\/\//,"")},{silent:!0})},this),this.size()>0&&this.addRandomChoice(a)},maybeRemoveOldCrop:function(a){var b,c=a.get("header").attachment_id||!1;c&&(b=this.find(function(b){return b.cid!==a.cid&&b.get("header").attachment_id===c}),b&&this.remove(b))},maybeAddRandomChoice:function(){1===this.size()&&this.addRandomChoice()},addRandomChoice:function(a){var b=RegExp(this.type).test(a),c="random-"+this.type+"-image";this.add({header:{timestamp:0,random:c,width:245,height:41},choice:c,random:!0,selected:b})},isRandomChoice:function(a){return/^random-(uploaded|default)-image$/.test(a)},shouldHideTitle:function(){return this.size()<2},setImage:function(a){this.each(function(a){a.set("selected",!1)}),a&&a.set("selected",!0)},removeImage:function(){this.each(function(a){a.set("selected",!1)})}}),c.HeaderTool.DefaultsList=c.HeaderTool.ChoiceList.extend({initialize:function(){this.type="default",this.data=_wpCustomizeHeader.defaults,c.HeaderTool.ChoiceList.prototype.initialize.apply(this)}})}(jQuery,window.wp);