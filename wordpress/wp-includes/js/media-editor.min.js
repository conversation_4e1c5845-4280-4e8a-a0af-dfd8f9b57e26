!function(a,b){var c={};wp.media.coerce=function(a,c){return b.isUndefined(a[c])&&!b.isUndefined(this.defaults[c])?a[c]=this.defaults[c]:"true"===a[c]?a[c]=!0:"false"===a[c]&&(a[c]=!1),a[c]},wp.media.string={props:function(a,c){var d,e,f,g,h=wp.media.view.settings.defaultProps;return a=a?b.clone(a):{},c&&c.type&&(a.type=c.type),"image"===a.type&&(a=b.defaults(a||{},{align:h.align||getUserSetting("align","none"),size:h.size||getUserSetting("imgsize","medium"),url:"",classes:[]})),c?(a.title=a.title||c.title,d=a.link||h.link||getUserSetting("urlbutton","file"),"file"===d||"embed"===d?e=c.url:"post"===d?e=c.link:"custom"===d&&(e=a.linkUrl),a.linkUrl=e||"","image"===c.type?(a.classes.push("wp-image-"+c.id),g=c.sizes,f=g&&g[a.size]?g[a.size]:c,b.extend(a,b.pick(c,"align","caption","alt"),{width:f.width,height:f.height,src:f.url,captionId:"attachment_"+c.id})):"video"===c.type||"audio"===c.type?b.extend(a,b.pick(c,"title","type","icon","mime")):(a.title=a.title||c.filename,a.rel=a.rel||"attachment wp-att-"+c.id),a):a},link:function(a,b){var c;return a=wp.media.string.props(a,b),c={tag:"a",content:a.title,attrs:{href:a.linkUrl}},a.rel&&(c.attrs.rel=a.rel),wp.html.string(c)},audio:function(a,b){return wp.media.string._audioVideo("audio",a,b)},video:function(a,b){return wp.media.string._audioVideo("video",a,b)},_audioVideo:function(a,c,d){var e,f,g;return c=wp.media.string.props(c,d),"embed"!==c.link?wp.media.string.link(c):(e={},"video"===a&&(d.image&&-1===d.image.src.indexOf(d.icon)&&(e.poster=d.image.src),d.width&&(e.width=d.width),d.height&&(e.height=d.height)),g=d.filename.split(".").pop(),b.contains(wp.media.view.settings.embedExts,g)?(e[g]=d.url,f=wp.shortcode.string({tag:a,attrs:e})):wp.media.string.link(c))},image:function(a,c){var d,e,f,g,h={};return a.type="image",a=wp.media.string.props(a,c),e=a.classes||[],h.src=b.isUndefined(c)?a.url:c.url,b.extend(h,b.pick(a,"width","height","alt")),a.align&&!a.caption&&e.push("align"+a.align),a.size&&e.push("size-"+a.size),h["class"]=b.compact(e).join(" "),d={tag:"img",attrs:h,single:!0},a.linkUrl&&(d={tag:"a",attrs:{href:a.linkUrl},content:d}),g=wp.html.string(d),a.caption&&(f={},h.width&&(f.width=h.width),a.captionId&&(f.id=a.captionId),a.align&&(f.align="align"+a.align),g=wp.shortcode.string({tag:"caption",attrs:f,content:g+" "+a.caption})),g}},wp.media.embed={coerce:wp.media.coerce,defaults:{url:"",width:"",height:""},edit:function(a,c){var d,e,f={};return c?f.url=a.replace(/<[^>]+>/g,""):(e=wp.shortcode.next("embed",a).shortcode,f=b.defaults(e.attrs.named,this.defaults),e.content&&(f.url=e.content)),d=wp.media({frame:"post",state:"embed",metadata:f})},shortcode:function(a){var c,d=this;return b.each(this.defaults,function(b,c){a[c]=d.coerce(a,c),b===a[c]&&delete a[c]}),c=a.url,delete a.url,new wp.shortcode({tag:"embed",attrs:a,content:c})}},wp.media.collection=function(a){var c={};return b.extend({coerce:wp.media.coerce,attachments:function(a){var d,e,f,g,h=a.string(),i=c[h],j=this;return delete c[h],i?i:(d=b.defaults(a.attrs.named,this.defaults),e=b.pick(d,"orderby","order"),e.type=this.type,e.perPage=-1,void 0!==d.orderby&&(d._orderByField=d.orderby),"rand"===d.orderby&&(d._orderbyRandom=!0),d.orderby&&!/^menu_order(?: ID)?$/i.test(d.orderby)||(e.orderby="menuOrder"),d.ids?(e.post__in=d.ids.split(","),e.orderby="post__in"):d.include&&(e.post__in=d.include.split(",")),d.exclude&&(e.post__not_in=d.exclude.split(",")),e.post__in||(e.uploadedTo=d.id),g=b.omit(d,"id","ids","include","exclude","orderby","order"),b.each(this.defaults,function(a,b){g[b]=j.coerce(g,b)}),f=wp.media.query(e),f[this.tag]=new Backbone.Model(g),f)},shortcode:function(a){var d,e,f=a.props.toJSON(),g=b.pick(f,"orderby","order");return a.type&&(g.type=a.type,delete a.type),a[this.tag]&&b.extend(g,a[this.tag].toJSON()),g.ids=a.pluck("id"),f.uploadedTo&&(g.id=f.uploadedTo),delete g.orderby,g._orderbyRandom?g.orderby="rand":g._orderByField&&"rand"!=g._orderByField&&(g.orderby=g._orderByField),delete g._orderbyRandom,delete g._orderByField,g.ids&&"post__in"===g.orderby&&delete g.orderby,g=this.setDefaults(g),d=new wp.shortcode({tag:this.tag,attrs:g,type:"single"}),e=new wp.media.model.Attachments(a.models,{props:f}),e[this.tag]=a[this.tag],c[d.string()]=e,d},edit:function(a){var c,d,e,f=wp.shortcode.next(this.tag,a),g=this.defaults.id;if(f&&f.content===a)return f=f.shortcode,b.isUndefined(f.get("id"))&&!b.isUndefined(g)&&f.set("id",g),c=this.attachments(f),d=new wp.media.model.Selection(c.models,{props:c.props.toJSON(),multiple:!0}),d[this.tag]=c[this.tag],d.more().done(function(){d.props.set({query:!1}),d.unmirror(),d.props.unset("orderby")}),this.frame&&this.frame.dispose(),e=f.attrs.named.type&&"video"===f.attrs.named.type?"video-"+this.tag+"-edit":this.tag+"-edit",this.frame=wp.media({frame:"post",state:e,title:this.editTitle,editing:!0,multiple:!0,selection:d}).open(),this.frame},setDefaults:function(a){var c=this;return b.each(this.defaults,function(b,d){a[d]=c.coerce(a,d),b===a[d]&&delete a[d]}),a}},a)},wp.media._galleryDefaults={itemtag:"dl",icontag:"dt",captiontag:"dd",columns:"3",link:"post",size:"thumbnail",order:"ASC",id:wp.media.view.settings.post&&wp.media.view.settings.post.id,orderby:"menu_order ID"},wp.media.view.settings.galleryDefaults?wp.media.galleryDefaults=b.extend({},wp.media._galleryDefaults,wp.media.view.settings.galleryDefaults):wp.media.galleryDefaults=wp.media._galleryDefaults,wp.media.gallery=new wp.media.collection({tag:"gallery",type:"image",editTitle:wp.media.view.l10n.editGalleryTitle,defaults:wp.media.galleryDefaults,setDefaults:function(a){var c=this,d=!b.isEqual(wp.media.galleryDefaults,wp.media._galleryDefaults);return b.each(this.defaults,function(b,e){a[e]=c.coerce(a,e),b!==a[e]||d&&b!==wp.media._galleryDefaults[e]||delete a[e]}),a}}),wp.media.featuredImage={get:function(){return wp.media.view.settings.post.featuredImageId},set:function(b){var c=wp.media.view.settings;c.post.featuredImageId=b,wp.media.post("get-post-thumbnail-html",{post_id:c.post.id,thumbnail_id:c.post.featuredImageId,_wpnonce:c.post.nonce}).done(function(b){return"0"==b?void window.alert(window.setPostThumbnailL10n.error):void a(".inside","#postimagediv").html(b)})},remove:function(){wp.media.featuredImage.set(-1)},frame:function(){return this._frame?(wp.media.frame=this._frame,this._frame):(this._frame=wp.media({state:"featured-image",states:[new wp.media.controller.FeaturedImage,new wp.media.controller.EditImage]}),this._frame.on("toolbar:create:featured-image",function(a){this.createSelectToolbar(a,{text:wp.media.view.l10n.setFeaturedImage})},this._frame),this._frame.on("content:render:edit-image",function(){var a=this.state("featured-image").get("selection"),b=new wp.media.view.EditImage({model:a.single(),controller:this}).render();this.content.set(b),b.loadEditor()},this._frame),this._frame.state("featured-image").on("select",this.select),this._frame)},select:function(){var a=this.get("selection").single();wp.media.view.settings.post.featuredImageId&&wp.media.featuredImage.set(a?a.id:-1)},init:function(){a("#postimagediv").on("click","#set-post-thumbnail",function(a){a.preventDefault(),a.stopPropagation(),wp.media.featuredImage.frame().open()}).on("click","#remove-post-thumbnail",function(){return wp.media.featuredImage.remove(),!1})}},a(wp.media.featuredImage.init),wp.media.editor={insert:function(a){var c,d,e=!b.isUndefined(window.tinymce),f=!b.isUndefined(window.QTags);if(d=this.activeEditor?window.wpActiveEditor=this.activeEditor:window.wpActiveEditor,window.send_to_editor)return window.send_to_editor.apply(this,arguments);if(d)e&&(c=tinymce.get(d));else if(e&&tinymce.activeEditor)c=tinymce.activeEditor,d=window.wpActiveEditor=c.id;else if(!f)return!1;if(c&&!c.isHidden()?c.execCommand("mceInsertContent",!1,a):f?QTags.insertContent(a):document.getElementById(d).value+=a,window.tb_remove)try{window.tb_remove()}catch(g){}},add:function(d,e){var f=this.get(d);return f?f:(f=c[d]=wp.media(b.defaults(e||{},{frame:"post",state:"insert",title:wp.media.view.l10n.addMedia,multiple:!0})),f.on("insert",function(c){var d=f.state();c=c||d.get("selection"),c&&a.when.apply(a,c.map(function(a){var b=d.display(a).toJSON();return this.send.attachment(b,a.toJSON())},this)).done(function(){wp.media.editor.insert(b.toArray(arguments).join("\n\n"))})},this),f.state("gallery-edit").on("update",function(a){this.insert(wp.media.gallery.shortcode(a).string())},this),f.state("playlist-edit").on("update",function(a){this.insert(wp.media.playlist.shortcode(a).string())},this),f.state("video-playlist-edit").on("update",function(a){this.insert(wp.media.playlist.shortcode(a).string())},this),f.state("embed").on("select",function(){var a=f.state(),c=a.get("type"),d=a.props.toJSON();d.url=d.url||"","link"===c?(b.defaults(d,{linkText:d.url,linkUrl:d.url}),this.send.link(d).done(function(a){wp.media.editor.insert(a)})):"image"===c&&(b.defaults(d,{title:d.url,linkUrl:"",align:"none",link:"none"}),"none"===d.link?d.linkUrl="":"file"===d.link&&(d.linkUrl=d.url),this.insert(wp.media.string.image(d)))},this),f.state("featured-image").on("select",wp.media.featuredImage.select),f.setState(f.options.state),f)},id:function(a){return a?a:(a=window.wpActiveEditor,a||b.isUndefined(window.tinymce)||!tinymce.activeEditor||(a=tinymce.activeEditor.id),a=a||"")},get:function(a){return a=this.id(a),c[a]},remove:function(a){a=this.id(a),delete c[a]},send:{attachment:function(a,c){var d,e,f=c.caption;return wp.media.view.settings.captions||delete c.caption,a=wp.media.string.props(a,c),d={id:c.id,post_content:c.description,post_excerpt:f},a.linkUrl&&(d.url=a.linkUrl),"image"===c.type?(e=wp.media.string.image(a),b.each({align:"align",size:"image-size",alt:"image_alt"},function(b,c){a[c]&&(d[b]=a[c])})):"video"===c.type?e=wp.media.string.video(a,c):"audio"===c.type?e=wp.media.string.audio(a,c):(e=wp.media.string.link(a),d.post_title=a.title),wp.media.post("send-attachment-to-editor",{nonce:wp.media.view.settings.nonce.sendToEditor,attachment:d,html:e,post_id:wp.media.view.settings.post.id})},link:function(a){return wp.media.post("send-link-to-editor",{nonce:wp.media.view.settings.nonce.sendToEditor,src:a.linkUrl,link_text:a.linkText,html:wp.media.string.link(a),post_id:wp.media.view.settings.post.id})}},open:function(a,b){var c;return b=b||{},a=this.id(a),this.activeEditor=a,c=this.get(a),(!c||c.options&&b.state!==c.options.state)&&(c=this.add(a,b)),wp.media.frame=c,c.open()},init:function(){a(document.body).on("click.add-media-button",".insert-media",function(b){var c=a(b.currentTarget),d=c.data("editor"),e={frame:"post",state:"insert",title:wp.media.view.l10n.addMedia,multiple:!0};b.preventDefault(),c.hasClass("gallery")&&(e.state="gallery",e.title=wp.media.view.l10n.createGalleryTitle),wp.media.editor.open(d,e)}),(new wp.media.view.EditorUploader).render()}},b.bindAll(wp.media.editor,"open"),a(wp.media.editor.init)}(jQuery,_);