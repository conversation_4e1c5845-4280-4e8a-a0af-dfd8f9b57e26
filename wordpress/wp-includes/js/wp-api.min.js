!function(a,b){"use strict";function c(){this.models={},this.collections={},this.views={}}a.wp=a.wp||{},wp.api=wp.api||new c,wp.api.versionString=wp.api.versionString||"wp/v2/",!_.isFunction(_.includes)&&_.isFunction(_.contains)&&(_.includes=_.contains)}(window),function(a,b){"use strict";var c,d;a.wp=a.wp||{},wp.api=wp.api||{},wp.api.utils=wp.api.utils||{},wp.api.getModelByRoute=function(a){return _.find(wp.api.models,function(b){return b.prototype.route&&a===b.prototype.route.index})},wp.api.getCollectionByRoute=function(a){return _.find(wp.api.collections,function(b){return b.prototype.route&&a===b.prototype.route.index})},Date.prototype.toISOString||(c=function(a){return d=String(a),1===d.length&&(d="0"+d),d},Date.prototype.toISOString=function(){return this.getUTCFullYear()+"-"+c(this.getUTCMonth()+1)+"-"+c(this.getUTCDate())+"T"+c(this.getUTCHours())+":"+c(this.getUTCMinutes())+":"+c(this.getUTCSeconds())+"."+String((this.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"}),wp.api.utils.parseISO8601=function(a){var c,d,e,f,g=0,h=[1,4,5,6,7,10,11];if(d=/^(\d{4}|[+\-]\d{6})(?:-(\d{2})(?:-(\d{2}))?)?(?:T(\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?(?:(Z)|([+\-])(\d{2})(?::(\d{2}))?)?)?$/.exec(a)){for(e=0;f=h[e];++e)d[f]=+d[f]||0;d[2]=(+d[2]||1)-1,d[3]=+d[3]||1,"Z"!==d[8]&&b!==d[9]&&(g=60*d[10]+d[11],"+"===d[9]&&(g=0-g)),c=Date.UTC(d[1],d[2],d[3],d[4],d[5]+g,d[6],d[7])}else c=Date.parse?Date.parse(a):NaN;return c},wp.api.utils.getRootUrl=function(){return a.location.origin?a.location.origin+"/":a.location.protocol+"//"+a.location.host+"/"},wp.api.utils.capitalize=function(a){return _.isUndefined(a)?a:a.charAt(0).toUpperCase()+a.slice(1)},wp.api.utils.capitalizeAndCamelCaseDashes=function(a){return _.isUndefined(a)?a:(a=wp.api.utils.capitalize(a),wp.api.utils.camelCaseDashes(a))},wp.api.utils.camelCaseDashes=function(a){return a.replace(/-([a-z])/g,function(a){return a[1].toUpperCase()})},wp.api.utils.extractRoutePart=function(a,b,c,d){var e;return b=b||1,c=c||wp.api.versionString,0===a.indexOf("/"+c)&&(a=a.substr(c.length+1)),e=a.split("/"),d&&(e=e.reverse()),_.isUndefined(e[--b])?"":e[b]},wp.api.utils.extractParentName=function(a){var b,c=a.lastIndexOf("_id>[\\d]+)/");return c<0?"":(b=a.substr(0,c-1),b=b.split("/"),b.pop(),b=b.pop())},wp.api.utils.decorateFromRoute=function(a,b){_.each(a,function(a){_.includes(a.methods,"POST")||_.includes(a.methods,"PUT")?_.isEmpty(a.args)||(_.isEmpty(b.prototype.args)?b.prototype.args=a.args:b.prototype.args=_.extend(b.prototype.args,a.args)):_.includes(a.methods,"GET")&&(_.isEmpty(a.args)||(_.isEmpty(b.prototype.options)?b.prototype.options=a.args:b.prototype.options=_.extend(b.prototype.options,a.args)))})},wp.api.utils.addMixinsAndHelpers=function(a,b,c){var d=!1,e=["date","modified","date_gmt","modified_gmt"],f={setDate:function(a,b){var c=b||"date";return!(_.indexOf(e,c)<0)&&void this.set(c,a.toISOString())},getDate:function(a){var b=a||"date",c=this.get(b);return!(_.indexOf(e,b)<0||_.isNull(c))&&new Date(wp.api.utils.parseISO8601(c))}},g=function(a,b,c,d,e){var f,g,h,i;return i=jQuery.Deferred(),g=a.get("_embedded")||{},_.isNumber(b)&&0!==b?(g[d]&&(h=_.findWhere(g[d],{id:b})),h||(h={id:b}),f=new wp.api.models[c](h),f.get(e)?i.resolve(f):f.fetch({success:function(a){i.resolve(a)},error:function(a,b){i.reject(b)}}),i.promise()):(i.reject(),i)},h=function(a,b,c,d){var e,f,g,h="",j="",k=jQuery.Deferred();return e=a.get("id"),f=a.get("_embedded")||{},_.isNumber(e)&&0!==e?(_.isUndefined(c)||_.isUndefined(f[c])?h={parent:e}:j=_.isUndefined(d)?f[c]:f[c][d],g=new wp.api.collections[b](j,h),_.isUndefined(g.models[0])?g.fetch({success:function(a){i(a,e),k.resolve(a)},error:function(a,b){k.reject(b)}}):(i(g,e),k.resolve(g)),k.promise()):(k.reject(),k)},i=function(a,b){_.each(a.models,function(a){a.set("parent_post",b)})},j={getMeta:function(a){var b=this.get("meta");return b[a]},getMetas:function(){return this.get("meta")},setMetas:function(a){var b=this.get("meta");_.extend(b,a),this.set("meta",b)},setMeta:function(a,b){var c=this.get("meta");c[a]=b,this.set("meta",c)}},k={getRevisions:function(){return h(this,"PostRevisions")}},l={getTags:function(){var a=this.get("tags"),b=new wp.api.collections.Tags;return _.isEmpty(a)?jQuery.Deferred().resolve([]):b.fetch({data:{include:a}})},setTags:function(a){var b,c,d=this,e=[];return!_.isString(a)&&void(_.isArray(a)?(b=new wp.api.collections.Tags,b.fetch({data:{per_page:100},success:function(b){_.each(a,function(a){c=new wp.api.models.Tag(b.findWhere({slug:a})),c.set("parent_post",d.get("id")),e.push(c)}),a=new wp.api.collections.Tags(e),d.setTagsWithCollection(a)}})):this.setTagsWithCollection(a))},setTagsWithCollection:function(a){return this.set("tags",a.pluck("id")),this.save()}},m={getCategories:function(){var a=this.get("categories"),b=new wp.api.collections.Categories;return _.isEmpty(a)?jQuery.Deferred().resolve([]):b.fetch({data:{include:a}})},setCategories:function(a){var b,c,d=this,e=[];return!_.isString(a)&&void(_.isArray(a)?(b=new wp.api.collections.Categories,b.fetch({data:{per_page:100},success:function(b){_.each(a,function(a){c=new wp.api.models.Category(b.findWhere({slug:a})),c.set("parent_post",d.get("id")),e.push(c)}),a=new wp.api.collections.Categories(e),d.setCategoriesWithCollection(a)}})):this.setCategoriesWithCollection(a))},setCategoriesWithCollection:function(a){return this.set("categories",a.pluck("id")),this.save()}},n={getAuthorUser:function(){return g(this,this.get("author"),"User","author","name")}},o={getFeaturedMedia:function(){return g(this,this.get("featured_media"),"Media","wp:featuredmedia","source_url")}};return _.isUndefined(a.prototype.args)?a:(_.each(e,function(b){_.isUndefined(a.prototype.args[b])||(d=!0)}),d&&(a=a.extend(f)),_.isUndefined(a.prototype.args.author)||(a=a.extend(n)),_.isUndefined(a.prototype.args.featured_media)||(a=a.extend(o)),_.isUndefined(a.prototype.args.categories)||(a=a.extend(m)),_.isUndefined(a.prototype.args.meta)||(a=a.extend(j)),_.isUndefined(a.prototype.args.tags)||(a=a.extend(l)),_.isUndefined(c.collections[b+"Revisions"])||(a=a.extend(k)),a)}}(window),function(){"use strict";var a=window.wpApiSettings||{},b=["Comment","Media","Comment","Post","Page","Status","Taxonomy","Type"];wp.api.WPApiBaseModel=Backbone.Model.extend({initialize:function(){-1===_.indexOf(b,this.name)&&(this.requireForceForDelete=!0)},sync:function(a,b,c){var d;return c=c||{},_.isNull(b.get("date_gmt"))&&b.unset("date_gmt"),_.isEmpty(b.get("slug"))&&b.unset("slug"),_.isFunction(b.nonce)&&!_.isEmpty(b.nonce())&&(d=c.beforeSend,c.beforeSend=function(a){if(a.setRequestHeader("X-WP-Nonce",b.nonce()),d)return d.apply(this,arguments)},c.complete=function(a){var c=a.getResponseHeader("X-WP-Nonce");c&&_.isFunction(b.nonce)&&b.nonce()!==c&&b.endpointModel.set("nonce",c)}),this.requireForceForDelete&&"delete"===a&&(b.url=b.url()+"?force=true"),Backbone.sync(a,b,c)},save:function(a,b){return!(!_.includes(this.methods,"PUT")&&!_.includes(this.methods,"POST"))&&Backbone.Model.prototype.save.call(this,a,b)},destroy:function(a){return!!_.includes(this.methods,"DELETE")&&Backbone.Model.prototype.destroy.call(this,a)}}),wp.api.models.Schema=wp.api.WPApiBaseModel.extend({defaults:{_links:{},namespace:null,routes:{}},initialize:function(b,c){var d=this;c=c||{},wp.api.WPApiBaseModel.prototype.initialize.call(d,b,c),d.apiRoot=c.apiRoot||a.root,d.versionString=c.versionString||a.versionString},url:function(){return this.apiRoot+this.versionString}})}(),function(){"use strict";window.wpApiSettings||{};wp.api.WPApiBaseCollection=Backbone.Collection.extend({initialize:function(a,b){this.state={data:{},currentPage:null,totalPages:null,totalObjects:null},_.isUndefined(b)?this.parent="":this.parent=b.parent},sync:function(a,b,c){var d,e,f=this;return c=c||{},_.isFunction(b.nonce)&&!_.isEmpty(b.nonce())&&(d=c.beforeSend,c.beforeSend=function(a){if(a.setRequestHeader("X-WP-Nonce",b.nonce()),d)return d.apply(f,arguments)},c.complete=function(a){var c=a.getResponseHeader("X-WP-Nonce");c&&_.isFunction(b.nonce)&&b.nonce()!==c&&b.endpointModel.set("nonce",c)}),"read"===a&&(c.data?(f.state.data=_.clone(c.data),delete f.state.data.page):f.state.data=c.data={},"undefined"==typeof c.data.page?(f.state.currentPage=null,f.state.totalPages=null,f.state.totalObjects=null):f.state.currentPage=c.data.page-1,e=c.success,c.success=function(a,b,c){if(_.isUndefined(c)||(f.state.totalPages=parseInt(c.getResponseHeader("x-wp-totalpages"),10),f.state.totalObjects=parseInt(c.getResponseHeader("x-wp-total"),10)),null===f.state.currentPage?f.state.currentPage=1:f.state.currentPage++,e)return e.apply(this,arguments)}),Backbone.sync(a,b,c)},more:function(a){if(a=a||{},a.data=a.data||{},_.extend(a.data,this.state.data),"undefined"==typeof a.data.page){if(!this.hasMore())return!1;null===this.state.currentPage||this.state.currentPage<=1?a.data.page=2:a.data.page=this.state.currentPage+1}return this.fetch(a)},hasMore:function(){return null===this.state.totalPages||null===this.state.totalObjects||null===this.state.currentPage?null:this.state.currentPage<this.state.totalPages}})}(),function(){"use strict";var a,b={},c=window.wpApiSettings||{};window.wp=window.wp||{},wp.api=wp.api||{},_.isEmpty(c)&&(c.root=window.location.origin+"/wp-json/"),a=Backbone.Model.extend({defaults:{apiRoot:c.root,versionString:wp.api.versionString,nonce:null,schema:null,models:{},collections:{}},initialize:function(){var a,b=this;Backbone.Model.prototype.initialize.apply(b,arguments),a=jQuery.Deferred(),b.schemaConstructed=a.promise(),b.schemaModel=new wp.api.models.Schema(null,{apiRoot:b.get("apiRoot"),versionString:b.get("versionString"),nonce:b.get("nonce")}),b.schemaModel.once("change",function(){b.constructFromSchema(),a.resolve(b)}),b.get("schema")?b.schemaModel.set(b.schemaModel.parse(b.get("schema"))):!_.isUndefined(sessionStorage)&&(_.isUndefined(c.cacheSchema)||c.cacheSchema)&&sessionStorage.getItem("wp-api-schema-model"+b.get("apiRoot")+b.get("versionString"))?b.schemaModel.set(b.schemaModel.parse(JSON.parse(sessionStorage.getItem("wp-api-schema-model"+b.get("apiRoot")+b.get("versionString"))))):b.schemaModel.fetch({success:function(a){if(!_.isUndefined(sessionStorage)&&(_.isUndefined(c.cacheSchema)||c.cacheSchema))try{sessionStorage.setItem("wp-api-schema-model"+b.get("apiRoot")+b.get("versionString"),JSON.stringify(a))}catch(d){}},error:function(a){window.console.log(a)}})},constructFromSchema:function(){var a,b,d,e,f=this,g=c.mapping||{models:{Categories:"Category",Comments:"Comment",Pages:"Page",PagesMeta:"PageMeta",PagesRevisions:"PageRevision",Posts:"Post",PostsCategories:"PostCategory",PostsRevisions:"PostRevision",PostsTags:"PostTag",Schema:"Schema",Statuses:"Status",Tags:"Tag",Taxonomies:"Taxonomy",Types:"Type",Users:"User"},collections:{PagesMeta:"PageMeta",PagesRevisions:"PageRevisions",PostsCategories:"PostCategories",PostsMeta:"PostMeta",PostsRevisions:"PostRevisions",PostsTags:"PostTags"}},h=f.get("modelEndpoints"),i=new RegExp("(?:.*[+)]|/("+h.join("|")+"))$");a=[],b=[],d=f.get("apiRoot").replace(wp.api.utils.getRootUrl(),""),e={},e.models={},e.collections={},_.each(f.schemaModel.get("routes"),function(c,e){e!==f.get(" versionString")&&e!==d&&e!=="/"+f.get("versionString").slice(0,-1)&&(i.test(e)?a.push({index:e,route:c}):b.push({index:e,route:c}))}),_.each(a,function(a){var b,c=wp.api.utils.extractRoutePart(a.index,2,f.get("versionString"),!0),d=wp.api.utils.extractRoutePart(a.index,1,f.get("versionString"),!1),h=wp.api.utils.extractRoutePart(a.index,1,f.get("versionString"),!0);d===f.get("versionString")&&(d=""),"me"===h&&(c="me"),""!==d&&d!==c?(b=wp.api.utils.capitalizeAndCamelCaseDashes(d)+wp.api.utils.capitalizeAndCamelCaseDashes(c),b=g.models[b]||b,e.models[b]=wp.api.WPApiBaseModel.extend({url:function(){var a=f.get("apiRoot")+f.get("versionString")+d+"/"+(_.isUndefined(this.get("parent"))||0===this.get("parent")?_.isUndefined(this.get("parent_post"))?"":this.get("parent_post")+"/":this.get("parent")+"/")+c;return _.isUndefined(this.get("id"))||(a+="/"+this.get("id")),a},nonce:function(){return f.get("nonce")},endpointModel:f,route:a,name:b,methods:a.route.methods,endpoints:a.route.endpoints})):(b=wp.api.utils.capitalizeAndCamelCaseDashes(c),b=g.models[b]||b,e.models[b]=wp.api.WPApiBaseModel.extend({url:function(){var a=f.get("apiRoot")+f.get("versionString")+("me"===c?"users/me":c);return _.isUndefined(this.get("id"))||(a+="/"+this.get("id")),a},nonce:function(){return f.get("nonce")},endpointModel:f,route:a,name:b,methods:a.route.methods,endpoints:a.route.endpoints})),wp.api.utils.decorateFromRoute(a.route.endpoints,e.models[b],f.get("versionString"))}),_.each(b,function(a){var b,c,d=a.index.slice(a.index.lastIndexOf("/")+1),h=wp.api.utils.extractRoutePart(a.index,1,f.get("versionString"),!1);""!==h&&h!==d&&f.get("versionString")!==h?(b=wp.api.utils.capitalizeAndCamelCaseDashes(h)+wp.api.utils.capitalizeAndCamelCaseDashes(d),c=g.models[b]||b,b=g.collections[b]||b,e.collections[b]=wp.api.WPApiBaseCollection.extend({url:function(){return f.get("apiRoot")+f.get("versionString")+h+"/"+this.parent+"/"+d},model:function(a,b){return new e.models[c](a,b)},nonce:function(){return f.get("nonce")},endpointModel:f,name:b,route:a,methods:a.route.methods})):(b=wp.api.utils.capitalizeAndCamelCaseDashes(d),c=g.models[b]||b,b=g.collections[b]||b,e.collections[b]=wp.api.WPApiBaseCollection.extend({url:function(){return f.get("apiRoot")+f.get("versionString")+d},model:function(a,b){return new e.models[c](a,b)},nonce:function(){return f.get("nonce")},endpointModel:f,name:b,route:a,methods:a.route.methods})),wp.api.utils.decorateFromRoute(a.route.endpoints,e.collections[b])}),_.each(e.models,function(a,b){e.models[b]=wp.api.utils.addMixinsAndHelpers(a,b,e)}),f.set("models",e.models),f.set("collections",e.collections)}}),wp.api.endpoints=new Backbone.Collection,wp.api.init=function(d){var e,f,g,h={};return d=d||{},h.nonce=_.isString(d.nonce)?d.nonce:c.nonce||"",h.apiRoot=d.apiRoot||c.root||"/wp-json",h.versionString=d.versionString||c.versionString||"wp/v2/",h.schema=d.schema||null,h.modelEndpoints=d.modelEndpoints||["me","settings"],h.schema||h.apiRoot!==c.root||h.versionString!==c.versionString||(h.schema=c.schema),b[h.apiRoot+h.versionString]||(e=wp.api.endpoints.findWhere({apiRoot:h.apiRoot,versionString:h.versionString}),e||(e=new a(h)),f=jQuery.Deferred(),g=f.promise(),e.schemaConstructed.done(function(a){wp.api.endpoints.add(a),wp.api.models=_.extend(wp.api.models,a.get("models")),wp.api.collections=_.extend(wp.api.collections,a.get("collections")),f.resolve(a)}),b[h.apiRoot+h.versionString]=g),b[h.apiRoot+h.versionString]},wp.api.loadPromise=wp.api.init()}();