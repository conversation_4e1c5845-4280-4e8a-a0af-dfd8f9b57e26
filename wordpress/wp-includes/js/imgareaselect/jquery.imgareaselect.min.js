!function(a){function b(){return a("<div/>")}var c=Math.abs,d=Math.max,e=Math.min,f=Math.round;a.imgAreaSelect=function(g,h){function i(a){return a+ra.left-sa.left}function j(a){return a+ra.top-sa.top}function k(a){return a-ra.left+sa.left}function l(a){return a-ra.top+sa.top}function m(a){return d(a.pageX||0,o(a).x)-sa.left}function n(a){return d(a.pageY||0,o(a).y)-sa.top}function o(a){var b=a.originalEvent||{};return b.touches&&b.touches.length?{x:b.touches[0].pageX,y:b.touches[0].pageY}:{x:0,y:0}}function p(a){var b=a||T,c=a||U;return{x1:f(va.x1*b),y1:f(va.y1*c),x2:f(va.x2*b),y2:f(va.y2*c),width:f(va.x2*b)-f(va.x1*b),height:f(va.y2*c)-f(va.y1*c)}}function q(a,b,c,d,e){var g=e||T,h=e||U;va={x1:f(a/g||0),y1:f(b/h||0),x2:f(c/g||0),y2:f(d/h||0)},va.width=va.x2-va.x1,va.height=va.y2-va.y1}function r(){K&&la.width()&&(ra={left:f(la.offset().left),top:f(la.offset().top)},O=la.innerWidth(),P=la.innerHeight(),ra.top+=la.outerHeight()-P>>1,ra.left+=la.outerWidth()-O>>1,W=f(h.minWidth/T)||0,X=f(h.minHeight/U)||0,Y=f(e(h.maxWidth/T||1<<24,O)),Z=f(e(h.maxHeight/U||1<<24,P)),"1.3.2"!=a().jquery||"fixed"!=ua||wa.getBoundingClientRect||(ra.top+=d(document.body.scrollTop,wa.scrollTop),ra.left+=d(document.body.scrollLeft,wa.scrollLeft)),sa=/absolute|relative/.test(Q.css("position"))?{left:f(Q.offset().left)-Q.scrollLeft(),top:f(Q.offset().top)-Q.scrollTop()}:"fixed"==ua?{left:a(document).scrollLeft(),top:a(document).scrollTop()}:{left:0,top:0},M=i(0),N=j(0),(va.x2>O||va.y2>P)&&z())}function s(b){if(_){switch(ma.css({left:i(va.x1),top:j(va.y1)}).add(na).width(ia=va.width).height(ja=va.height),na.add(oa).add(qa).css({left:0,top:0}),oa.width(d(ia-oa.outerWidth()+oa.innerWidth(),0)).height(d(ja-oa.outerHeight()+oa.innerHeight(),0)),a(pa[0]).css({left:M,top:N,width:va.x1,height:P}),a(pa[1]).css({left:M+va.x1,top:N,width:ia,height:va.y1}),a(pa[2]).css({left:M+va.x2,top:N,width:O-va.x2,height:P}),a(pa[3]).css({left:M+va.x1,top:N+va.y2,width:ia,height:P-va.y2}),ia-=qa.outerWidth(),ja-=qa.outerHeight(),qa.length){case 8:a(qa[4]).css({left:ia>>1}),a(qa[5]).css({left:ia,top:ja>>1}),a(qa[6]).css({left:ia>>1,top:ja}),a(qa[7]).css({top:ja>>1});case 4:qa.slice(1,3).css({left:ia}),qa.slice(2,4).css({top:ja})}b!==!1&&(a.imgAreaSelect.onKeyPress!=ya&&a(document).unbind(a.imgAreaSelect.keyPress,a.imgAreaSelect.onKeyPress),h.keys&&a(document)[a.imgAreaSelect.keyPress](a.imgAreaSelect.onKeyPress=ya)),za&&oa.outerWidth()-oa.innerWidth()==2&&(oa.css("margin",0),setTimeout(function(){oa.css("margin","auto")},0))}}function t(a){r(),s(a),aa=i(va.x1),ba=j(va.y1),ca=i(va.x2),da=j(va.y2)}function u(a,b){h.fadeSpeed?a.fadeOut(h.fadeSpeed,b):a.hide()}function v(a){var b=k(m(a))-va.x1,c=l(n(a))-va.y1;ka||(r(),ka=!0,ma.one("mouseout",function(){ka=!1})),V="",h.resizable&&(c<=h.resizeMargin?V="n":c>=va.height-h.resizeMargin&&(V="s"),b<=h.resizeMargin?V+="w":b>=va.width-h.resizeMargin&&(V+="e")),ma.css("cursor",V?V+"-resize":h.movable?"move":""),L&&L.toggle()}function w(b){a("body").css("cursor",""),(h.autoHide||va.width*va.height==0)&&u(ma.add(pa),function(){a(this).hide()}),a(document).off("mousemove touchmove",A),ma.on("mousemove touchmove",v),h.onSelectEnd(g,p())}function x(b){return("mousedown"!=b.type||1==b.which)&&(v(b),r(),V?(a("body").css("cursor",V+"-resize"),aa=i(va[/w/.test(V)?"x2":"x1"]),ba=j(va[/n/.test(V)?"y2":"y1"]),a(document).on("mousemove touchmove",A).one("mouseup touchend",w),ma.off("mousemove touchmove",v)):h.movable?(R=M+va.x1-m(b),S=N+va.y1-n(b),ma.off("mousemove touchmove",v),a(document).on("mousemove touchmove",C).one("mouseup touchend",function(){h.onSelectEnd(g,p()),a(document).off("mousemove touchmove",C),ma.on("mousemove touchmove",v)})):la.mousedown(b),!1)}function y(a){$&&(a?(ca=d(M,e(M+O,aa+c(da-ba)*$*(ca>aa||-1))),da=f(d(N,e(N+P,ba+c(ca-aa)/$*(da>ba||-1)))),ca=f(ca)):(da=d(N,e(N+P,ba+c(ca-aa)/$*(da>ba||-1))),ca=f(d(M,e(M+O,aa+c(da-ba)*$*(ca>aa||-1)))),da=f(da)))}function z(){aa=e(aa,M+O),ba=e(ba,N+P),c(ca-aa)<W&&(ca=aa-W*(ca<aa||-1),ca<M?aa=M+W:ca>M+O&&(aa=M+O-W)),c(da-ba)<X&&(da=ba-X*(da<ba||-1),da<N?ba=N+X:da>N+P&&(ba=N+P-X)),ca=d(M,e(ca,M+O)),da=d(N,e(da,N+P)),y(c(ca-aa)<c(da-ba)*$),c(ca-aa)>Y&&(ca=aa-Y*(ca<aa||-1),y()),c(da-ba)>Z&&(da=ba-Z*(da<ba||-1),y(!0)),va={x1:k(e(aa,ca)),x2:k(d(aa,ca)),y1:l(e(ba,da)),y2:l(d(ba,da)),width:c(ca-aa),height:c(da-ba)},s(),h.onSelectChange(g,p())}function A(a){return ca=/w|e|^$/.test(V)||$?m(a):i(va.x2),da=/n|s|^$/.test(V)||$?n(a):j(va.y2),z(),!1}function B(b,c){ca=(aa=b)+va.width,da=(ba=c)+va.height,a.extend(va,{x1:k(aa),y1:l(ba),x2:k(ca),y2:l(da)}),s(),h.onSelectChange(g,p())}function C(a){return aa=d(M,e(R+m(a),M+O-va.width)),ba=d(N,e(S+n(a),N+P-va.height)),B(aa,ba),a.preventDefault(),!1}function D(){a(document).off("mousemove touchmove",D),r(),ca=aa,da=ba,z(),V="",pa.is(":visible")||ma.add(pa).hide().fadeIn(h.fadeSpeed||0),_=!0,a(document).off("mouseup touchend",E).on("mousemove touchmove",A).one("mouseup touchend",w),ma.off("mousemove touchmove",v),h.onSelectStart(g,p())}function E(){a(document).off("mousemove touchmove",D).off("mouseup touchend",E),u(ma.add(pa)),q(k(aa),l(ba),k(aa),l(ba)),this instanceof a.imgAreaSelect||(h.onSelectChange(g,p()),h.onSelectEnd(g,p()))}function F(b){return!(b.which>1||pa.is(":animated"))&&(r(),R=aa=m(b),S=ba=n(b),a(document).on({"mousemove touchmove":D,"mouseup touchend":E}),!1)}function G(){t(!1)}function H(){K=!0,J(h=a.extend({classPrefix:"imgareaselect",movable:!0,parent:"body",resizable:!0,resizeMargin:10,onInit:function(){},onSelectStart:function(){},onSelectChange:function(){},onSelectEnd:function(){}},h)),ma.add(pa).css({visibility:""}),h.show&&(_=!0,r(),s(),ma.add(pa).hide().fadeIn(h.fadeSpeed||0)),setTimeout(function(){h.onInit(g,p())},0)}function I(a,b){for(var c in b)void 0!==h[c]&&a.css(b[c],h[c])}function J(c){if(c.parent&&(Q=a(c.parent)).append(ma.add(pa)),a.extend(h,c),r(),null!=c.handles){for(qa.remove(),qa=a([]),ga=c.handles?"corners"==c.handles?4:8:0;ga--;)qa=qa.add(b());qa.addClass(h.classPrefix+"-handle").css({position:"absolute",fontSize:0,zIndex:ta+1||1}),!parseInt(qa.css("width"))>=0&&qa.width(5).height(5),(ha=h.borderWidth)&&qa.css({borderWidth:ha,borderStyle:"solid"}),I(qa,{borderColor1:"border-color",borderColor2:"background-color",borderOpacity:"opacity"})}for(T=h.imageWidth/O||1,U=h.imageHeight/P||1,null!=c.x1&&(q(c.x1,c.y1,c.x2,c.y2),c.show=!c.hide),c.keys&&(h.keys=a.extend({shift:1,ctrl:"resize"},c.keys)),pa.addClass(h.classPrefix+"-outer"),na.addClass(h.classPrefix+"-selection"),ga=0;ga++<4;)a(oa[ga-1]).addClass(h.classPrefix+"-border"+ga);I(na,{selectionColor:"background-color",selectionOpacity:"opacity"}),I(oa,{borderOpacity:"opacity",borderWidth:"border-width"}),I(pa,{outerColor:"background-color",outerOpacity:"opacity"}),(ha=h.borderColor1)&&a(oa[0]).css({borderStyle:"solid",borderColor:ha}),(ha=h.borderColor2)&&a(oa[1]).css({borderStyle:"dashed",borderColor:ha}),ma.append(na.add(oa).add(L)).append(qa),za&&((ha=(pa.css("filter")||"").match(/opacity=(\d+)/))&&pa.css("opacity",ha[1]/100),(ha=(oa.css("filter")||"").match(/opacity=(\d+)/))&&oa.css("opacity",ha[1]/100)),c.hide?u(ma.add(pa)):c.show&&K&&(_=!0,ma.add(pa).fadeIn(h.fadeSpeed||0),t()),$=(fa=(h.aspectRatio||"").split(/:/))[0]/fa[1],la.add(pa).unbind("mousedown",F),h.disable||h.enable===!1?(ma.off({"mousemove touchmove":v,"mousedown touchstart":x}),a(window).off("resize",G)):((h.enable||h.disable===!1)&&((h.resizable||h.movable)&&ma.on({"mousemove touchmove":v,"mousedown touchstart":x}),a(window).resize(G)),h.persistent||la.add(pa).on("mousedown touchstart",F)),h.enable=h.disable=void 0}var K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la=a(g),ma=b(),na=b(),oa=b().add(b()).add(b()).add(b()),pa=b().add(b()).add(b()).add(b()),qa=a([]),ra={left:0,top:0},sa={left:0,top:0},ta=0,ua="absolute",va={x1:0,y1:0,x2:0,y2:0,width:0,height:0},wa=document.documentElement,xa=navigator.userAgent,ya=function(a){var b,c,f=h.keys,g=a.keyCode;if(b=isNaN(f.alt)||!a.altKey&&!a.originalEvent.altKey?!isNaN(f.ctrl)&&a.ctrlKey?f.ctrl:!isNaN(f.shift)&&a.shiftKey?f.shift:isNaN(f.arrows)?10:f.arrows:f.alt,"resize"==f.arrows||"resize"==f.shift&&a.shiftKey||"resize"==f.ctrl&&a.ctrlKey||"resize"==f.alt&&(a.altKey||a.originalEvent.altKey)){switch(g){case 37:b=-b;case 39:c=d(aa,ca),aa=e(aa,ca),ca=d(c+b,aa),y();break;case 38:b=-b;case 40:c=d(ba,da),ba=e(ba,da),da=d(c+b,ba),y(!0);break;default:return}z()}else switch(aa=e(aa,ca),ba=e(ba,da),g){case 37:B(d(aa-b,M),ba);break;case 38:B(aa,d(ba-b,N));break;case 39:B(aa+e(b,O-k(ca)),ba);break;case 40:B(aa,ba+e(b,P-l(da)));break;default:return}return!1};this.remove=function(){J({disable:!0}),ma.add(pa).remove()},this.getOptions=function(){return h},this.setOptions=J,this.getSelection=p,this.setSelection=q,this.cancelSelection=E,this.update=t;var za=(/msie ([\w.]+)/i.exec(xa)||[])[1],Aa=/opera/i.test(xa),Ba=/webkit/i.test(xa)&&!/chrome/i.test(xa);for(ea=la;ea.length;)ta=d(ta,isNaN(ea.css("z-index"))?ta:ea.css("z-index")),"fixed"==ea.css("position")&&(ua="fixed"),ea=ea.parent(":not(body)");ta=h.zIndex||ta,za&&la.attr("unselectable","on"),a.imgAreaSelect.keyPress=za||Ba?"keydown":"keypress",Aa&&(L=b().css({width:"100%",height:"100%",position:"absolute",zIndex:ta+2||2})),ma.add(pa).css({visibility:"hidden",position:ua,overflow:"hidden",zIndex:ta||"0"}),ma.css({zIndex:ta+2||2}),na.add(oa).css({position:"absolute",fontSize:0}),g.complete||"complete"==g.readyState||!la.is("img")?H():la.one("load",H),!K&&za&&za>=7&&(g.src=g.src)},a.fn.imgAreaSelect=function(b){return b=b||{},this.each(function(){a(this).data("imgAreaSelect")?b.remove?(a(this).data("imgAreaSelect").remove(),a(this).removeData("imgAreaSelect")):a(this).data("imgAreaSelect").setOptions(b):b.remove||(void 0===b.enable&&void 0===b.disable&&(b.enable=!0),a(this).data("imgAreaSelect",new a.imgAreaSelect(this,b)))}),b.instance?a(this).data("imgAreaSelect"):this}}(jQuery);