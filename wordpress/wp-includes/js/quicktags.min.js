window.edButtons=[],window.edAddTag=function(){},window.edCheckOpenTags=function(){},window.edCloseAllTags=function(){},window.edInsertImage=function(){},window.edInsertLink=function(){},window.edInsertTag=function(){},window.edLink=function(){},window.edQuickLink=function(){},window.edRemoveTag=function(){},window.edShowButton=function(){},window.edShowLinks=function(){},window.edSpell=function(){},window.edToolbar=function(){},function(){function a(a){return a=a||"",a=a.replace(/&([^#])(?![a-z1-4]{1,8};)/gi,"&#038;$1"),a.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}var b=function(a){var c,d,e,f;"undefined"!=typeof jQuery?jQuery(document).ready(a):(c=b,c.funcs=[],c.ready=function(){if(!c.isReady)for(c.isReady=!0,d=0;d<c.funcs.length;d++)c.funcs[d]()},c.isReady?a():c.funcs.push(a),c.eventAttached||(document.addEventListener?(e=function(){document.removeEventListener("DOMContentLoaded",e,!1),c.ready()},document.addEventListener("DOMContentLoaded",e,!1),window.addEventListener("load",c.ready,!1)):document.attachEvent&&(e=function(){"complete"===document.readyState&&(document.detachEvent("onreadystatechange",e),c.ready())},document.attachEvent("onreadystatechange",e),window.attachEvent("onload",c.ready),(f=function(){try{document.documentElement.doScroll("left")}catch(a){return void setTimeout(f,50)}c.ready()})()),c.eventAttached=!0))},c=function(){var a,b=new Date;return a=function(a){var b=a.toString();return b.length<2&&(b="0"+b),b},b.getUTCFullYear()+"-"+a(b.getUTCMonth()+1)+"-"+a(b.getUTCDate())+"T"+a(b.getUTCHours())+":"+a(b.getUTCMinutes())+":"+a(b.getUTCSeconds())+"+00:00"}(),d=window.QTags=function(a){if("string"==typeof a)a={id:a};else if("object"!=typeof a)return!1;var c,e,f,g,h,i=this,j=a.id,k=document.getElementById(j),l="qt_"+j;return!(!j||!k)&&(i.name=l,i.id=j,i.canvas=k,i.settings=a,"content"!==j||"string"!=typeof adminpage||"post-new-php"!==adminpage&&"post-php"!==adminpage?f=l+"_toolbar":(window.edCanvas=k,f="ed_toolbar"),c=document.getElementById(f),c||(c=document.createElement("div"),c.id=f,c.className="quicktags-toolbar"),k.parentNode.insertBefore(c,k),i.toolbar=c,e=function(a){a=a||window.event;var b,c=a.target||a.srcElement,d=c.clientWidth||c.offsetWidth;d&&/ ed_button /.test(" "+c.className+" ")&&(i.canvas=k=document.getElementById(j),b=c.id.replace(l+"_",""),i.theButtons[b]&&i.theButtons[b].callback.call(i.theButtons[b],c,k,i))},h=function(){window.wpActiveEditor=j},g=document.getElementById("wp-"+j+"-wrap"),c.addEventListener?(c.addEventListener("click",e,!1),g&&g.addEventListener("click",h,!1)):c.attachEvent&&(c.attachEvent("onclick",e),g&&g.attachEvent("onclick",h)),i.getButton=function(a){return i.theButtons[a]},i.getButtonElement=function(a){return document.getElementById(l+"_"+a)},i.init=function(){b(function(){d._buttonsInit(j)})},i.remove=function(){delete d.instances[j],c&&c.parentNode&&c.parentNode.removeChild(c)},d.instances[j]=i,void i.init())};d.instances={},d.getInstance=function(a){return d.instances[a]},d._buttonsInit=function(a){function b(a){var b,e,f,g,h,i,j,k,l,m=",strong,em,link,block,del,ins,img,ul,ol,li,code,more,close,";i=c.instances[a],b=i.canvas,e=i.name,f=i.settings,h="",g={},l="",f.buttons&&(l=","+f.buttons+",");for(k in edButtons)edButtons[k]&&(j=edButtons[k].id,l&&m.indexOf(","+j+",")!==-1&&l.indexOf(","+j+",")===-1||edButtons[k].instance&&edButtons[k].instance!==a||(g[j]=edButtons[k],edButtons[k].html&&(h+=edButtons[k].html(e+"_"))));l&&l.indexOf(",dfw,")!==-1&&(g.dfw=new d.DFWButton,h+=g.dfw.html(e+"_")),"rtl"===document.getElementsByTagName("html")[0].dir&&(g.textdirection=new d.TextDirectionButton,h+=g.textdirection.html(e+"_")),i.toolbar.innerHTML=h,i.theButtons=g,"undefined"!=typeof jQuery&&jQuery(document).triggerHandler("quicktags-init",[i])}var c=this;if(a)b(a);else for(a in c.instances)b(a);c.buttonsInitDone=!0},d.addButton=function(a,b,c,e,f,g,h,i,j){var k;if(a&&b){if(h=h||0,e=e||"",j=j||{},"function"==typeof c)k=new d.Button(a,b,f,g,i,j),k.callback=c;else{if("string"!=typeof c)return;k=new d.TagButton(a,b,c,e,f,g,i,j)}if(h===-1)return k;if(h>0){for(;"undefined"!=typeof edButtons[h];)h++;edButtons[h]=k}else edButtons[edButtons.length]=k;this.buttonsInitDone&&this._buttonsInit()}},d.insertContent=function(a){var b,c,d,e,f,g,h=document.getElementById(wpActiveEditor);return!!h&&(document.selection?(h.focus(),b=document.selection.createRange(),b.text=a,h.focus()):h.selectionStart||0===h.selectionStart?(f=h.value,c=h.selectionStart,d=h.selectionEnd,e=h.scrollTop,h.value=f.substring(0,c)+a+f.substring(d,f.length),h.selectionStart=c+a.length,h.selectionEnd=c+a.length,h.scrollTop=e,h.focus()):(h.value+=a,h.focus()),document.createEvent?(g=document.createEvent("HTMLEvents"),g.initEvent("change",!1,!0),h.dispatchEvent(g)):h.fireEvent&&h.fireEvent("onchange"),!0)},d.Button=function(a,b,c,d,e,f){this.id=a,this.display=b,this.access="",this.title=d||"",this.instance=e||"",this.attr=f||{}},d.Button.prototype.html=function(b){var c,d,e,f=this.title?' title="'+a(this.title)+'"':"",g=this.attr&&this.attr.ariaLabel?' aria-label="'+a(this.attr.ariaLabel)+'"':"",h=this.display?' value="'+a(this.display)+'"':"",i=this.id?' id="'+a(b+this.id)+'"':"",j=(e=window.wp)&&e.editor&&e.editor.dfw;return"fullscreen"===this.id?'<button type="button"'+i+' class="ed_button qt-dfw qt-fullscreen"'+f+g+"></button>":"dfw"===this.id?(c=j&&j.isActive()?"":' disabled="disabled"',d=j&&j.isOn()?" active":"",'<button type="button"'+i+' class="ed_button qt-dfw'+d+'"'+f+g+c+"></button>"):'<input type="button"'+i+' class="ed_button button button-small"'+f+g+h+" />"},d.Button.prototype.callback=function(){},d.TagButton=function(a,b,c,e,f,g,h,i){var j=this;d.Button.call(j,a,b,f,g,h,i),j.tagStart=c,j.tagEnd=e},d.TagButton.prototype=new d.Button,d.TagButton.prototype.openTag=function(a,b){b.openTags||(b.openTags=[]),this.tagEnd&&(b.openTags.push(this.id),a.value="/"+a.value,this.attr.ariaLabelClose&&a.setAttribute("aria-label",this.attr.ariaLabelClose))},d.TagButton.prototype.closeTag=function(a,b){var c=this.isOpen(b);c!==!1&&b.openTags.splice(c,1),a.value=this.display,this.attr.ariaLabel&&a.setAttribute("aria-label",this.attr.ariaLabel)},d.TagButton.prototype.isOpen=function(a){var b=this,c=0,d=!1;if(a.openTags)for(;d===!1&&c<a.openTags.length;)d=a.openTags[c]===b.id&&c,c++;else d=!1;return d},d.TagButton.prototype.callback=function(a,b,c){var d,e,f,g,h,i,j,k,l,m=this,n=b.value,o=n?m.tagEnd:"";document.selection?(b.focus(),k=document.selection.createRange(),k.text.length>0?m.tagEnd?k.text=m.tagStart+k.text+o:k.text=k.text+m.tagStart:m.tagEnd?m.isOpen(c)===!1?(k.text=m.tagStart,m.openTag(a,c)):(k.text=o,m.closeTag(a,c)):k.text=m.tagStart,b.focus()):b.selectionStart||0===b.selectionStart?(d=b.selectionStart,e=b.selectionEnd,d<e&&"\n"===n.charAt(e-1)&&(e-=1),f=e,g=b.scrollTop,h=n.substring(0,d),i=n.substring(e,n.length),j=n.substring(d,e),d!==e?m.tagEnd?(b.value=h+m.tagStart+j+o+i,f+=m.tagStart.length+o.length):(b.value=h+j+m.tagStart+i,f+=m.tagStart.length):m.tagEnd?m.isOpen(c)===!1?(b.value=h+m.tagStart+i,m.openTag(a,c),f=d+m.tagStart.length):(b.value=h+o+i,f=d+o.length,m.closeTag(a,c)):(b.value=h+m.tagStart+i,f=d+m.tagStart.length),b.selectionStart=f,b.selectionEnd=f,b.scrollTop=g,b.focus()):(o?m.isOpen(c)!==!1?(b.value+=m.tagStart,m.openTag(a,c)):(b.value+=o,m.closeTag(a,c)):b.value+=m.tagStart,b.focus()),document.createEvent?(l=document.createEvent("HTMLEvents"),l.initEvent("change",!1,!0),b.dispatchEvent(l)):b.fireEvent&&b.fireEvent("onchange")},d.SpellButton=function(){},d.CloseButton=function(){d.Button.call(this,"close",quicktagsL10n.closeTags,"",quicktagsL10n.closeAllOpenTags)},d.CloseButton.prototype=new d.Button,d._close=function(a,b,c){var d,e,f=c.openTags;if(f)for(;f.length>0;)d=c.getButton(f[f.length-1]),e=document.getElementById(c.name+"_"+d.id),a?d.callback.call(d,e,b,c):d.closeTag(e,c)},d.CloseButton.prototype.callback=d._close,d.closeAllTags=function(a){var b=this.getInstance(a);b&&d._close("",b.canvas,b)},d.LinkButton=function(){var a={ariaLabel:quicktagsL10n.link};d.TagButton.call(this,"link","link","","</a>","","","",a)},d.LinkButton.prototype=new d.TagButton,d.LinkButton.prototype.callback=function(a,b,c,e){var f,g=this;return"undefined"!=typeof wpLink?void wpLink.open(c.id):(e||(e="http://"),void(g.isOpen(c)===!1?(f=prompt(quicktagsL10n.enterURL,e),f&&(g.tagStart='<a href="'+f+'">',d.TagButton.prototype.callback.call(g,a,b,c))):d.TagButton.prototype.callback.call(g,a,b,c)))},d.ImgButton=function(){var a={ariaLabel:quicktagsL10n.image};d.TagButton.call(this,"img","img","","","","","",a)},d.ImgButton.prototype=new d.TagButton,d.ImgButton.prototype.callback=function(a,b,c,e){e||(e="http://");var f,g=prompt(quicktagsL10n.enterImageURL,e);g&&(f=prompt(quicktagsL10n.enterImageDescription,""),this.tagStart='<img src="'+g+'" alt="'+f+'" />',d.TagButton.prototype.callback.call(this,a,b,c))},d.DFWButton=function(){d.Button.call(this,"dfw","","f",quicktagsL10n.dfw)},d.DFWButton.prototype=new d.Button,d.DFWButton.prototype.callback=function(){var a;(a=window.wp)&&a.editor&&a.editor.dfw&&window.wp.editor.dfw.toggle()},d.TextDirectionButton=function(){d.Button.call(this,"textdirection",quicktagsL10n.textdirection,"",quicktagsL10n.toggleTextdirection)},d.TextDirectionButton.prototype=new d.Button,d.TextDirectionButton.prototype.callback=function(a,b){var c="rtl"===document.getElementsByTagName("html")[0].dir,d=b.style.direction;d||(d=c?"rtl":"ltr"),b.style.direction="rtl"===d?"ltr":"rtl",b.focus()},edButtons[10]=new d.TagButton("strong","b","<strong>","</strong>","","","",{ariaLabel:quicktagsL10n.strong,ariaLabelClose:quicktagsL10n.strongClose}),edButtons[20]=new d.TagButton("em","i","<em>","</em>","","","",{ariaLabel:quicktagsL10n.em,ariaLabelClose:quicktagsL10n.emClose}),edButtons[30]=new d.LinkButton,edButtons[40]=new d.TagButton("block","b-quote","\n\n<blockquote>","</blockquote>\n\n","","","",{ariaLabel:quicktagsL10n.blockquote,ariaLabelClose:quicktagsL10n.blockquoteClose}),edButtons[50]=new d.TagButton("del","del",'<del datetime="'+c+'">',"</del>","","","",{ariaLabel:quicktagsL10n.del,ariaLabelClose:quicktagsL10n.delClose}),edButtons[60]=new d.TagButton("ins","ins",'<ins datetime="'+c+'">',"</ins>","","","",{ariaLabel:quicktagsL10n.ins,ariaLabelClose:quicktagsL10n.insClose}),edButtons[70]=new d.ImgButton,edButtons[80]=new d.TagButton("ul","ul","<ul>\n","</ul>\n\n","","","",{ariaLabel:quicktagsL10n.ul,ariaLabelClose:quicktagsL10n.ulClose}),edButtons[90]=new d.TagButton("ol","ol","<ol>\n","</ol>\n\n","","","",{ariaLabel:quicktagsL10n.ol,ariaLabelClose:quicktagsL10n.olClose}),edButtons[100]=new d.TagButton("li","li","\t<li>","</li>\n","","","",{ariaLabel:quicktagsL10n.li,ariaLabelClose:quicktagsL10n.liClose}),edButtons[110]=new d.TagButton("code","code","<code>","</code>","","","",{ariaLabel:quicktagsL10n.code,ariaLabelClose:quicktagsL10n.codeClose}),edButtons[120]=new d.TagButton("more","more","<!--more-->\n\n","","","","",{ariaLabel:quicktagsL10n.more}),edButtons[140]=new d.CloseButton}(),window.quicktags=function(a){return new window.QTags(a)},window.edInsertContent=function(a,b){return window.QTags.insertContent(b)},window.edButton=function(a,b,c,d,e){return window.QTags.addButton(a,b,c,d,e,"",-1)};