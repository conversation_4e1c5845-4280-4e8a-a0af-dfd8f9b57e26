!function(a,b,c){if(b&&b.customize){var d=b.customize;d.HeaderTool.CurrentView=b.Backbone.View.extend({template:b.template("header-current"),initialize:function(){this.listenTo(this.model,"change",this.render),this.render()},render:function(){return this.$el.html(this.template(this.model.toJSON())),this.setButtons(),this},setButtons:function(){var b=a("#customize-control-header_image .actions .remove");this.model.get("choice")?b.show():b.hide()}}),d.HeaderTool.ChoiceView=b.Backbone.View.extend({template:b.template("header-choice"),className:"header-view",events:{"click .choice,.random":"select","click .close":"removeImage"},initialize:function(){var a=[this.model.get("header").url,this.model.get("choice")];this.listenTo(this.model,"change:selected",this.toggleSelected),c.contains(a,d.get().header_image)&&d.HeaderTool.currentHeader.set(this.extendedModel())},render:function(){return this.$el.html(this.template(this.extendedModel())),this.toggleSelected(),this},toggleSelected:function(){this.$el.toggleClass("selected",this.model.get("selected"))},extendedModel:function(){var a=this.model.get("collection");return c.extend(this.model.toJSON(),{type:a.type})},select:function(){this.preventJump(),this.model.save(),d.HeaderTool.currentHeader.set(this.extendedModel())},preventJump:function(){var b=a(".wp-full-overlay-sidebar-content"),d=b.scrollTop();c.defer(function(){b.scrollTop(d)})},removeImage:function(a){a.stopPropagation(),this.model.destroy(),this.remove()}}),d.HeaderTool.ChoiceListView=b.Backbone.View.extend({initialize:function(){this.listenTo(this.collection,"add",this.addOne),this.listenTo(this.collection,"remove",this.render),this.listenTo(this.collection,"sort",this.render),this.listenTo(this.collection,"change",this.toggleList),this.render()},render:function(){this.$el.empty(),this.collection.each(this.addOne,this),this.toggleList()},addOne:function(a){var b;a.set({collection:this.collection}),b=new d.HeaderTool.ChoiceView({model:a}),this.$el.append(b.render().el)},toggleList:function(){var a=this.$el.parents().prev(".customize-control-title"),b=this.$el.find(".random").parent();this.collection.shouldHideTitle()?a.add(b).hide():a.add(b).show()}}),d.HeaderTool.CombinedList=b.Backbone.View.extend({initialize:function(a){this.collections=a,this.on("all",this.propagate,this)},propagate:function(a,b){c.each(this.collections,function(c){c.trigger(a,b)})}})}}(jQuery,window.wp,_);