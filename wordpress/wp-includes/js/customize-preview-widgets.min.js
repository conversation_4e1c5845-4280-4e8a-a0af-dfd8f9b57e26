wp.customize.widgetsPreview=wp.customize.WidgetCustomizerPreview=function(a,b,c,d){var e;return e={renderedSidebars:{},renderedWidgets:{},registeredSidebars:[],registeredWidgets:{},widgetSelectors:[],preview:null,l10n:{widgetTooltip:""},selectiveRefreshableWidgets:{}},e.init=function(){var a=this;a.preview=d.preview,b.isEmpty(a.selectiveRefreshableWidgets)||a.addPartials(),a.buildWidgetSelectors(),a.highlightControls(),a.preview.bind("highlight-widget",a.highlightWidget),d.preview.bind("active",function(){a.highlightControls()}),d.preview.bind("refresh-widget-partial",function(b){var c="widget["+b+"]";d.selectiveRefresh.partial.has(c)?d.selectiveRefresh.partial(c).refresh():a.renderedWidgets[b]&&d.preview.send("refresh")})},e.WidgetPartial=d.selectiveRefresh.Partial.extend({initialize:function(a,c){var f,g=this;if(f=a.match(/^widget\[(.+)]$/),!f)throw new Error("Illegal id for widget partial.");g.widgetId=f[1],g.widgetIdParts=e.parseWidgetId(g.widgetId),c=c||{},c.params=b.extend({settings:[e.getWidgetSettingId(g.widgetId)],containerInclusive:!0},c.params||{}),d.selectiveRefresh.Partial.prototype.initialize.call(g,a,c)},refresh:function(){var b,c=this;return e.selectiveRefreshableWidgets[c.widgetIdParts.idBase]?d.selectiveRefresh.Partial.prototype.refresh.call(c):(b=a.Deferred(),b.reject(),c.fallback(),b.promise())},renderContent:function(a){var b=this;d.selectiveRefresh.Partial.prototype.renderContent.call(b,a)&&(d.preview.send("widget-updated",b.widgetId),d.selectiveRefresh.trigger("widget-updated",b))}}),e.SidebarPartial=d.selectiveRefresh.Partial.extend({initialize:function(a,c){var e,f=this;if(e=a.match(/^sidebar\[(.+)]$/),!e)throw new Error("Illegal id for sidebar partial.");if(f.sidebarId=e[1],c=c||{},c.params=b.extend({settings:["sidebars_widgets["+f.sidebarId+"]"]},c.params||{}),d.selectiveRefresh.Partial.prototype.initialize.call(f,a,c),!f.params.sidebarArgs)throw new Error("The sidebarArgs param was not provided.");if(f.params.settings.length>1)throw new Error("Expected SidebarPartial to only have one associated setting")},ready:function(){var a=this;b.each(a.settings(),function(c){d(c).bind(b.bind(a.handleSettingChange,a))}),d.selectiveRefresh.bind("partial-content-rendered",function(c){var f=c.partial.extended(e.WidgetPartial)&&-1!==b.indexOf(a.getWidgetIds(),c.partial.widgetId);f&&d.selectiveRefresh.trigger("sidebar-updated",a)}),d.bind("change",function(c){var d,f;f=e.parseWidgetSettingId(c.id),f&&(d=f.idBase,f.number&&(d+="-"+String(f.number)),-1!==b.indexOf(a.getWidgetIds(),d)&&a.ensureWidgetPlacementContainers(d))})},findDynamicSidebarBoundaryNodes:function(){var a,c,d=this,e={};return a=/^(dynamic_sidebar_before|dynamic_sidebar_after):(.+):(\d+)$/,c=function(f){b.each(f,function(f){var g;if(8===f.nodeType){if(g=f.nodeValue.match(a),!g||g[2]!==d.sidebarId)return;b.isUndefined(e[g[3]])&&(e[g[3]]={before:null,after:null,instanceNumber:parseInt(g[3],10)}),"dynamic_sidebar_before"===g[1]?e[g[3]].before=f:e[g[3]].after=f}else 1===f.nodeType&&c(f.childNodes)})},c(document.body.childNodes),b.values(e)},placements:function(){var a=this;return b.map(a.findDynamicSidebarBoundaryNodes(),function(b){return new d.selectiveRefresh.Placement({partial:a,container:null,startNode:b.before,endNode:b.after,context:{instanceNumber:b.instanceNumber}})})},getWidgetIds:function(){var a,c,e=this;if(a=e.settings()[0],!a)throw new Error("Missing associated setting.");if(!d.has(a))throw new Error("Setting does not exist.");if(c=d(a).get(),!b.isArray(c))throw new Error("Expected setting to be array of widget IDs");return c.slice(0)},reflowWidgets:function(){var a,c,e,f=this,g=[];return c=f.getWidgetIds(),a=f.placements(),e={},b.each(c,function(a){var b=d.selectiveRefresh.partial("widget["+a+"]");b&&(e[a]=b)}),b.each(a,function(a){var c,f=[],h=!1,i=-1;b.each(e,function(d){b.each(d.placements(),function(b){a.context.instanceNumber===b.context.sidebar_instance_number&&(c=b.container.index(),f.push({partial:d,placement:b,position:c}),c<i&&(h=!0),i=c)})}),h&&(b.each(f,function(b){a.endNode.parentNode.insertBefore(b.placement.container[0],a.endNode),d.selectiveRefresh.trigger("partial-content-moved",b.placement)}),g.push(a))}),g.length>0&&d.selectiveRefresh.trigger("sidebar-updated",f),g},ensureWidgetPlacementContainers:function(c){var f,g=this,h=!1,i="widget["+c+"]";return f=d.selectiveRefresh.partial(i),f||(f=new e.WidgetPartial(i,{params:{}})),b.each(g.placements(),function(d){var e,i;e=b.find(f.placements(),function(a){return a.context.sidebar_instance_number===d.context.instanceNumber}),e||(i=a(g.params.sidebarArgs.before_widget.replace(/%1\$s/g,c).replace(/%2\$s/g,"widget")+g.params.sidebarArgs.after_widget),i[0]&&(i.attr("data-customize-partial-id",f.id),i.attr("data-customize-partial-type","widget"),i.attr("data-customize-widget-id",c),i.data("customize-partial-placement-context",{sidebar_id:g.sidebarId,sidebar_instance_number:d.context.instanceNumber}),d.endNode.parentNode.insertBefore(i[0],d.endNode),h=!0))}),d.selectiveRefresh.partial.add(f),h&&g.reflowWidgets(),f},handleSettingChange:function(a,c){var f,g,h,i=this,j=[];return(f=c.length>0&&0===a.length||a.length>0&&0===c.length)?void i.fallback():(g=b.difference(c,a),b.each(g,function(a){var c=d.selectiveRefresh.partial("widget["+a+"]");c&&b.each(c.placements(),function(a){var b=a.context.sidebar_id===i.sidebarId||a.context.sidebar_args&&a.context.sidebar_args.id===i.sidebarId;b&&a.container.remove()}),delete e.renderedWidgets[a]}),h=b.difference(a,c),b.each(h,function(a){var b=i.ensureWidgetPlacementContainers(a);j.push(b),e.renderedWidgets[a]=!0}),b.each(j,function(a){a.refresh()}),void d.selectiveRefresh.trigger("sidebar-updated",i))},refresh:function(){var c=this,e=a.Deferred();return e.fail(function(){c.fallback()}),0===c.placements().length?e.reject():(b.each(c.reflowWidgets(),function(a){d.selectiveRefresh.trigger("partial-content-rendered",a)}),e.resolve()),e.promise()}}),d.selectiveRefresh.partialConstructor.sidebar=e.SidebarPartial,d.selectiveRefresh.partialConstructor.widget=e.WidgetPartial,e.addPartials=function(){b.each(e.registeredSidebars,function(a){var b,c="sidebar["+a.id+"]";b=d.selectiveRefresh.partial(c),b||(b=new e.SidebarPartial(c,{params:{sidebarArgs:a}}),d.selectiveRefresh.partial.add(b))})},e.buildWidgetSelectors=function(){var b=this;a.each(b.registeredSidebars,function(c,d){var e,f,g,h=[d.before_widget,d.before_title,d.after_title,d.after_widget].join("");e=a(h),f=e.prop("tagName")||"",g=e.prop("className")||"",g&&(g=g.replace(/\S*%[12]\$s\S*/g,""),g=g.replace(/^\s+|\s+$/g,""),g&&(f+="."+g.split(/\s+/).join(".")),b.widgetSelectors.push(f))})},e.highlightWidget=function(b){var c=a(document.body),d=a("#"+b);c.find(".widget-customizer-highlighted-widget").removeClass("widget-customizer-highlighted-widget"),d.addClass("widget-customizer-highlighted-widget"),setTimeout(function(){d.removeClass("widget-customizer-highlighted-widget")},500)},e.highlightControls=function(){var b=this,c=this.widgetSelectors.join(",");d.settings.channel&&(a(c).attr("title",this.l10n.widgetTooltip),a(document).on("mouseenter",c,function(){b.preview.send("highlight-widget-control",a(this).prop("id"))}),a(document).on("click",c,function(c){c.shiftKey&&(c.preventDefault(),b.preview.send("focus-widget-control",a(this).prop("id")))}))},e.parseWidgetId=function(a){var b,c={idBase:"",number:null};return b=a.match(/^(.+)-(\d+)$/),b?(c.idBase=b[1],c.number=parseInt(b[2],10)):c.idBase=a,c},e.parseWidgetSettingId=function(a){var b,c={idBase:"",number:null};return(b=a.match(/^widget_([^\[]+?)(?:\[(\d+)])?$/))?(c.idBase=b[1],b[2]&&(c.number=parseInt(b[2],10)),c):null},e.getWidgetSettingId=function(a){var b,c=this.parseWidgetId(a);return b="widget_"+c.idBase,c.number&&(b+="["+String(c.number)+"]"),b},d.bind("preview-ready",function(){a.extend(e,_wpWidgetCustomizerPreviewSettings),e.init()}),e}(jQuery,_,wp,wp.customize);