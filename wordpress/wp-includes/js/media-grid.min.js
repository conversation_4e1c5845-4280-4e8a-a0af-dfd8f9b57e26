!function(a){function b(d){if(c[d])return c[d].exports;var e=c[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,b),e.l=!0,e.exports}var c={};return b.m=a,b.c=c,b.d=function(a,c,d){b.o(a,c)||Object.defineProperty(a,c,{enumerable:!0,get:d})},b.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},b.t=function(a,c){if(1&c&&(a=b(a)),8&c)return a;if(4&c&&"object"==typeof a&&a&&a.__esModule)return a;var d=Object.create(null);if(b.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&c&&"string"!=typeof a)for(var e in a)b.d(d,e,function(b){return a[b]}.bind(null,e));return d},b.n=function(a){var c=a&&a.__esModule?function(){return a["default"]}:function(){return a};return b.d(c,"a",c),c},b.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},b.p="",b(b.s=11)}([,,,,,,,,,,,function(a,b,c){a.exports=c(12)},function(a,b,c){var d=wp.media;d.controller.EditAttachmentMetadata=c(13),d.view.MediaFrame.Manage=c(14),d.view.Attachment.Details.TwoColumn=c(15),d.view.MediaFrame.Manage.Router=c(16),d.view.EditImage.Details=c(17),d.view.MediaFrame.EditAttachments=c(18),d.view.SelectModeToggleButton=c(19),d.view.DeleteSelectedButton=c(20),d.view.DeleteSelectedPermanentlyButton=c(21)},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.controller.State.extend({defaults:{id:"edit-attachment",title:d.attachmentDetails,content:"edit-metadata",menu:!1,toolbar:!1,router:!1}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaFrame,e=wp.media.controller.Library,f=Backbone.$;c=d.extend({initialize:function(){_.defaults(this.options,{title:"",modal:!1,selection:[],library:{},multiple:"add",state:"library",uploader:!0,mode:["grid","edit"]}),this.$body=f(document.body),this.$window=f(window),this.$adminBar=f("#wpadminbar"),this.$uploaderToggler=f(".page-title-action").attr("aria-expanded","false").on("click",_.bind(this.addNewClickHandler,this)),this.$window.on("scroll resize",_.debounce(_.bind(this.fixPosition,this),15)),this.$el.addClass("wp-core-ui"),!wp.Uploader.limitExceeded&&wp.Uploader.browser.supported||(this.options.uploader=!1),this.options.uploader&&(this.uploader=new wp.media.view.UploaderWindow({controller:this,uploader:{dropzone:document.body,container:document.body}}).render(),this.uploader.ready(),f("body").append(this.uploader.el),this.options.uploader=!1),this.gridRouter=new wp.media.view.MediaFrame.Manage.Router,d.prototype.initialize.apply(this,arguments),this.$el.appendTo(this.options.container),this.createStates(),this.bindRegionModeHandlers(),this.render(),this.bindSearchHandler(),wp.media.frames.browse=this},bindSearchHandler:function(){var a=this.$("#media-search-input"),b=this.browserView.toolbar.get("search").$el,c=this.$(".view-list"),d=_.throttle(function(a){var b=f(a.currentTarget).val(),c="";b&&(c+="?search="+b,this.gridRouter.navigate(this.gridRouter.baseUrl(c),{replace:!0}))},1e3);a.on("input",_.bind(d,this)),this.gridRouter.on("route:search",function(){var a=window.location.href;a.indexOf("mode=")>-1?a=a.replace(/mode=[^&]+/g,"mode=list"):a+=a.indexOf("?")>-1?"&mode=list":"?mode=list",a=a.replace("search=","s="),c.prop("href",a)}).on("route:reset",function(){b.val("").trigger("input")})},createStates:function(){var a=this.options;this.options.states||this.states.add([new e({library:wp.media.query(a.library),multiple:a.multiple,title:a.title,content:"browse",toolbar:"select",contentUserSetting:!1,filterable:"all",autoSelect:!1})])},bindRegionModeHandlers:function(){this.on("content:create:browse",this.browseContent,this),this.on("edit:attachment",this.openEditAttachmentModal,this),this.on("select:activate",this.bindKeydown,this),this.on("select:deactivate",this.unbindKeydown,this)},handleKeydown:function(a){27===a.which&&(a.preventDefault(),this.deactivateMode("select").activateMode("edit"))},bindKeydown:function(){this.$body.on("keydown.select",_.bind(this.handleKeydown,this))},unbindKeydown:function(){this.$body.off("keydown.select")},fixPosition:function(){var a,b;this.isModeActive("select")&&(a=this.$(".attachments-browser"),b=a.find(".media-toolbar"),a.offset().top+16<this.$window.scrollTop()+this.$adminBar.height()?(a.addClass("fixed"),b.css("width",a.width()+"px")):(a.removeClass("fixed"),b.css("width","")))},addNewClickHandler:function(a){a.preventDefault(),this.trigger("toggle:upload:attachment"),this.uploader&&this.uploader.refresh()},openEditAttachmentModal:function(a){wp.media.frames.edit?wp.media.frames.edit.open().trigger("refresh",a):wp.media.frames.edit=wp.media({frame:"edit-attachments",controller:this,library:this.state().get("library"),model:a})},browseContent:function(a){var b=this.state();this.browserView=a.view=new wp.media.view.AttachmentsBrowser({controller:this,collection:b.get("library"),selection:b.get("selection"),model:b,sortable:b.get("sortable"),search:b.get("searchable"),filters:b.get("filterable"),date:b.get("date"),display:b.get("displaySettings"),dragInfo:b.get("dragInfo"),sidebar:"errors",suggestedWidth:b.get("suggestedWidth"),suggestedHeight:b.get("suggestedHeight"),AttachmentView:b.get("AttachmentView"),scrollElement:document}),this.browserView.on("ready",_.bind(this.bindDeferred,this)),this.errors=wp.Uploader.errors,this.errors.on("add remove reset",this.sidebarVisibility,this)},sidebarVisibility:function(){this.browserView.$(".media-sidebar").toggle(!!this.errors.length)},bindDeferred:function(){this.browserView.dfd&&this.browserView.dfd.done(_.bind(this.startHistory,this))},startHistory:function(){window.history&&window.history.pushState&&(Backbone.History.started&&Backbone.history.stop(),Backbone.history.start({root:window._wpMediaGridSettings.adminUrl,pushState:!0}))}}),a.exports=c},function(a,b){var c,d=wp.media.view.Attachment.Details;c=d.extend({template:wp.template("attachment-details-two-column"),initialize:function(){this.controller.on("content:activate:edit-details",_.bind(this.editAttachment,this)),d.prototype.initialize.apply(this,arguments)},editAttachment:function(a){a&&a.preventDefault(),this.controller.content.mode("edit-image")},toggleSelectionHandler:function(){},render:function(){d.prototype.render.apply(this,arguments),wp.media.mixin.removeAllPlayers(),this.$("audio, video").each(function(a,b){var c=wp.media.view.MediaDetails.prepareSrc(b);new window.MediaElementPlayer(c,wp.media.mixin.mejsSettings)})}}),a.exports=c},function(a,b){var c=Backbone.Router.extend({routes:{"upload.php?item=:slug&mode=edit":"editItem","upload.php?item=:slug":"showItem","upload.php?search=:query":"search","upload.php":"reset"},baseUrl:function(a){return"upload.php"+a},reset:function(){var a=wp.media.frames.edit;a&&a.close()},search:function(a){jQuery("#media-search-input").val(a).trigger("input")},showItem:function(a){var b,c=wp.media,d=c.frames.browse,e=d.state().get("library");b=e.findWhere({id:parseInt(a,10)}),b.set("skipHistory",!0),b?d.trigger("edit:attachment",b):(b=c.attachment(a),d.listenTo(b,"change",function(a){d.stopListening(b),d.trigger("edit:attachment",a)}),b.fetch())},editItem:function(a){this.showItem(a),wp.media.frames.edit.content.mode("edit-details")}});a.exports=c},function(a,b){var c,d=wp.media.View,e=wp.media.view.EditImage;c=e.extend({initialize:function(a){this.editor=window.imageEdit,this.frame=a.frame,this.controller=a.controller,d.prototype.initialize.apply(this,arguments)},back:function(){this.frame.content.mode("edit-metadata")},save:function(){this.model.fetch().done(_.bind(function(){this.frame.content.mode("edit-metadata")},this))}}),a.exports=c},function(a,b){var c,d=wp.media.view.Frame,e=wp.media.view.MediaFrame,f=jQuery;c=e.extend({className:"edit-attachment-frame",template:wp.template("edit-attachment-frame"),regions:["title","content"],events:{"click .left":"previousMediaItem","click .right":"nextMediaItem"},initialize:function(){d.prototype.initialize.apply(this,arguments),_.defaults(this.options,{modal:!0,state:"edit-attachment"}),this.controller=this.options.controller,this.gridRouter=this.controller.gridRouter,this.library=this.options.library,this.options.model&&(this.model=this.options.model),this.bindHandlers(),this.createStates(),this.createModal(),this.title.mode("default"),this.toggleNav()},bindHandlers:function(){this.on("title:create:default",this.createTitle,this),this.on("content:create:edit-metadata",this.editMetadataMode,this),this.on("content:create:edit-image",this.editImageMode,this),this.on("content:render:edit-image",this.editImageModeRender,this),this.on("refresh",this.rerender,this),this.on("close",this.detach),this.bindModelHandlers(),this.listenTo(this.gridRouter,"route:search",this.close,this)},bindModelHandlers:function(){this.listenTo(this.model,"change:status destroy",this.close,this)},createModal:function(){this.options.modal&&(this.modal=new wp.media.view.Modal({controller:this,title:this.options.title}),this.modal.on("open",_.bind(function(){f("body").on("keydown.media-modal",_.bind(this.keyEvent,this))},this)),this.modal.on("close",_.bind(function(){f("body").off("keydown.media-modal"),f('li.attachment[data-id="'+this.model.get("id")+'"]').focus(),this.resetRoute()},this)),this.modal.content(this),this.modal.open())},createStates:function(){this.states.add([new wp.media.controller.EditAttachmentMetadata({model:this.model,library:this.library})])},editMetadataMode:function(a){a.view=new wp.media.view.Attachment.Details.TwoColumn({controller:this,model:this.model}),a.view.views.set(".attachment-compat",new wp.media.view.AttachmentCompat({controller:this,model:this.model})),this.model&&!this.model.get("skipHistory")&&this.gridRouter.navigate(this.gridRouter.baseUrl("?item="+this.model.id))},editImageMode:function(a){var b=new wp.media.controller.EditImage({model:this.model,frame:this});b._toolbar=function(){},b._router=function(){},b._menu=function(){},a.view=new wp.media.view.EditImage.Details({model:this.model,frame:this,controller:b}),this.gridRouter.navigate(this.gridRouter.baseUrl("?item="+this.model.id+"&mode=edit"))},editImageModeRender:function(a){a.on("ready",a.loadEditor)},toggleNav:function(){this.$(".left").toggleClass("disabled",!this.hasPrevious()),this.$(".right").toggleClass("disabled",!this.hasNext())},rerender:function(a){this.stopListening(this.model),this.model=a,this.bindModelHandlers(),"edit-metadata"!==this.content.mode()?this.content.mode("edit-metadata"):this.content.render(),this.toggleNav()},previousMediaItem:function(){this.hasPrevious()&&(this.trigger("refresh",this.library.at(this.getCurrentIndex()-1)),this.$(".left").focus())},nextMediaItem:function(){this.hasNext()&&(this.trigger("refresh",this.library.at(this.getCurrentIndex()+1)),this.$(".right").focus())},getCurrentIndex:function(){return this.library.indexOf(this.model)},hasNext:function(){return this.getCurrentIndex()+1<this.library.length},hasPrevious:function(){return this.getCurrentIndex()-1>-1},keyEvent:function(a){("INPUT"!==a.target.nodeName&&"TEXTAREA"!==a.target.nodeName||a.target.readOnly||a.target.disabled)&&(39===a.keyCode&&this.nextMediaItem(),37===a.keyCode&&this.previousMediaItem())},resetRoute:function(){var a=this.controller.browserView.toolbar.get("search").$el.val(),b=""!==a?"?search="+a:"";this.gridRouter.navigate(this.gridRouter.baseUrl(b),{replace:!0})}}),a.exports=c},function(a,b){var c,d=wp.media.view.Button,e=wp.media.view.l10n;c=d.extend({initialize:function(){_.defaults(this.options,{size:""}),d.prototype.initialize.apply(this,arguments),this.controller.on("select:activate select:deactivate",this.toggleBulkEditHandler,this),this.controller.on("selection:action:done",this.back,this)},back:function(){this.controller.deactivateMode("select").activateMode("edit")},click:function(){d.prototype.click.apply(this,arguments),this.controller.isModeActive("select")?this.back():this.controller.deactivateMode("edit").activateMode("select")},render:function(){return d.prototype.render.apply(this,arguments),this.$el.addClass("select-mode-toggle-button"),this},toggleBulkEditHandler:function(){var a,b=this.controller.content.get().toolbar;a=b.$(".media-toolbar-secondary > *, .media-toolbar-primary > *"),this.controller.isModeActive("select")?(this.model.set({size:"large",text:e.cancelSelection}),a.not(".spinner, .media-button").hide(),this.$el.show(),b.$(".delete-selected-button").removeClass("hidden")):(this.model.set({size:"",text:e.bulkSelect}),this.controller.content.get().$el.removeClass("fixed"),b.$el.css("width",""),b.$(".delete-selected-button").addClass("hidden"),a.not(".media-button").show(),this.controller.state().get("selection").reset())}}),a.exports=c},function(a,b){var c,d=wp.media.view.Button,e=wp.media.view.l10n;c=d.extend({initialize:function(){d.prototype.initialize.apply(this,arguments),this.options.filters&&this.options.filters.model.on("change",this.filterChange,this),this.controller.on("selection:toggle",this.toggleDisabled,this)},filterChange:function(a){"trash"===a.get("status")?this.model.set("text",e.untrashSelected):wp.media.view.settings.mediaTrash?this.model.set("text",e.trashSelected):this.model.set("text",e.deleteSelected)},toggleDisabled:function(){this.model.set("disabled",!this.controller.state().get("selection").length)},render:function(){return d.prototype.render.apply(this,arguments),this.controller.isModeActive("select")?this.$el.addClass("delete-selected-button"):this.$el.addClass("delete-selected-button hidden"),this.toggleDisabled(),this}}),a.exports=c},function(a,b){var c,d=wp.media.view.Button,e=wp.media.view.DeleteSelectedButton;c=e.extend({initialize:function(){e.prototype.initialize.apply(this,arguments),this.controller.on("select:activate",this.selectActivate,this),this.controller.on("select:deactivate",this.selectDeactivate,this)},filterChange:function(a){this.canShow="trash"===a.get("status")},selectActivate:function(){this.toggleDisabled(),this.$el.toggleClass("hidden",!this.canShow)},selectDeactivate:function(){this.toggleDisabled(),this.$el.addClass("hidden")},render:function(){return d.prototype.render.apply(this,arguments),this.selectActivate(),this}}),a.exports=c}]);