wp.customize.navMenusPreview=wp.customize.MenusCustomizerPreview=function(a,b,c,d){"use strict";var e={data:{navMenuInstanceArgs:{}}};return"undefined"!=typeof _wpCustomizePreviewNavMenusExports&&b.extend(e.data,_wpCustomizePreviewNavMenusExports),e.init=function(){var a=this,c=!1;d.preview.bind("sync",function(){c=!0}),d.selectiveRefresh&&(d.each(function(b){a.bindSettingListener(b)}),d.bind("add",function(b){b.get()&&!b.get()._invalid&&a.bindSettingListener(b,{fire:c})}),d.bind("remove",function(b){a.unbindSettingListener(b)}),d.selectiveRefresh.bind("render-partials-response",function(c){c.nav_menu_instance_args&&b.extend(a.data.navMenuInstanceArgs,c.nav_menu_instance_args)})),d.preview.bind("active",function(){a.highlightControls()})},d.selectiveRefresh&&(e.NavMenuInstancePartial=d.selectiveRefresh.Partial.extend({initialize:function(a,c){var e,f,g=this;if(e=a.match(/^nav_menu_instance\[([0-9a-f]{32})]$/),!e)throw new Error("Illegal id for nav_menu_instance partial. The key corresponds with the args HMAC.");if(f=e[1],c=c||{},c.params=b.extend({selector:'[data-customize-partial-id="'+a+'"]',navMenuArgs:c.constructingContainerContext||{},containerInclusive:!0},c.params||{}),d.selectiveRefresh.Partial.prototype.initialize.call(g,a,c),!b.isObject(g.params.navMenuArgs))throw new Error("Missing navMenuArgs");if(g.params.navMenuArgs.args_hmac!==f)throw new Error("args_hmac mismatch with id")},isRelatedSetting:function(a,c,e){var f,g,h,i,j,k,l=this;if(b.isString(a)&&(a=d(a)),h=/^nav_menu_item\[/.test(a.id),h&&b.isObject(c)&&b.isObject(e)&&(i=b.clone(c),j=b.clone(e),delete i.type_label,delete j.type_label,"https"===d.preview.scheme.get()&&(k=document.createElement("a"),k.href=i.url,k.protocol="https:",i.url=k.href,k.href=j.url,k.protocol="https:",j.url=k.href),c.title&&(delete j.original_title,delete i.original_title),b.isEqual(j,i)))return!1;if(l.params.navMenuArgs.theme_location){if("nav_menu_locations["+l.params.navMenuArgs.theme_location+"]"===a.id)return!0;f=d("nav_menu_locations["+l.params.navMenuArgs.theme_location+"]")}return g=l.params.navMenuArgs.menu,!g&&f&&(g=f()),!!g&&("nav_menu["+g+"]"===a.id||h&&(c&&c.nav_menu_term_id===g||e&&e.nav_menu_term_id===g))},refresh:function(){var c,e=this,f=a.Deferred();return b.isNumber(e.params.navMenuArgs.menu)?c=e.params.navMenuArgs.menu:e.params.navMenuArgs.theme_location&&d.has("nav_menu_locations["+e.params.navMenuArgs.theme_location+"]")&&(c=d("nav_menu_locations["+e.params.navMenuArgs.theme_location+"]").get()),c?d.selectiveRefresh.Partial.prototype.refresh.call(e):(e.fallback(),f.reject(),f.promise())},renderContent:function(b){var c=this,e=b.container;""===b.addedContent&&b.partial.fallback(),d.selectiveRefresh.Partial.prototype.renderContent.call(c,b)&&a(document).trigger("customize-preview-menu-refreshed",[{instanceNumber:null,wpNavArgs:b.context,wpNavMenuArgs:b.context,oldContainer:e,newContainer:b.container}])}}),d.selectiveRefresh.partialConstructor.nav_menu_instance=e.NavMenuInstancePartial,e.handleUnplacedNavMenuInstances=function(a){var c;return c=b.filter(b.values(e.data.navMenuInstanceArgs),function(a){return!d.selectiveRefresh.partial.has("nav_menu_instance["+a.args_hmac+"]")}),!!b.findWhere(c,a)&&(d.selectiveRefresh.requestFullRefresh(),!0)},e.bindSettingListener=function(a,b){var c;return b=b||{},(c=a.id.match(/^nav_menu\[(-?\d+)]$/))?(a._navMenuId=parseInt(c[1],10),a.bind(this.onChangeNavMenuSetting),b.fire&&this.onChangeNavMenuSetting.call(a,a(),!1),!0):(c=a.id.match(/^nav_menu_item\[(-?\d+)]$/))?(a._navMenuItemId=parseInt(c[1],10),a.bind(this.onChangeNavMenuItemSetting),b.fire&&this.onChangeNavMenuItemSetting.call(a,a(),!1),!0):(c=a.id.match(/^nav_menu_locations\[(.+?)]/),!!c&&(a._navMenuThemeLocation=c[1],a.bind(this.onChangeNavMenuLocationsSetting),b.fire&&this.onChangeNavMenuLocationsSetting.call(a,a(),!1),!0))},e.unbindSettingListener=function(a){a.unbind(this.onChangeNavMenuSetting),a.unbind(this.onChangeNavMenuItemSetting),a.unbind(this.onChangeNavMenuLocationsSetting)},e.onChangeNavMenuSetting=function(){var a=this;e.handleUnplacedNavMenuInstances({menu:a._navMenuId}),d.each(function(b){b._navMenuThemeLocation&&a._navMenuId===b()&&e.handleUnplacedNavMenuInstances({theme_location:b._navMenuThemeLocation})})},e.onChangeNavMenuItemSetting=function(a,b){var c,f=a||b;c=d("nav_menu["+String(f.nav_menu_term_id)+"]"),c&&e.onChangeNavMenuSetting.call(c)},e.onChangeNavMenuLocationsSetting=function(){var a,c=this;e.handleUnplacedNavMenuInstances({theme_location:c._navMenuThemeLocation}),a=!!b.findWhere(b.values(e.data.navMenuInstanceArgs),{theme_location:c._navMenuThemeLocation}),a||d.selectiveRefresh.requestFullRefresh()}),e.highlightControls=function(){var b=".menu-item";d.settings.channel&&a(document).on("click",b,function(b){var c;b.shiftKey&&(c=a(this).attr("class").match(/(?:^|\s)menu-item-(-?\d+)(?:\s|$)/),c&&(b.preventDefault(),b.stopPropagation(),d.preview.send("focus-nav-menu-item-control",parseInt(c[1],10))))})},d.bind("preview-ready",function(){e.init()}),e}(jQuery,_,wp,wp.customize);