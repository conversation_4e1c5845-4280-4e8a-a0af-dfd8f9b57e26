wp.customize.selectiveRefresh=function(a,b){"use strict";var c,d,e;return c={ready:a.Deferred(),editShortcutVisibility:new b.Value,data:{partials:{},renderQueryVar:"",l10n:{shiftClickToEdit:""}},currentRequest:null},_.extend(c,b.Events),d=c.Partial=b.Class.extend({id:null,defaults:{selector:null,primarySetting:null,containerInclusive:!1,fallbackRefresh:!0},initialize:function(b,c){var d=this;c=c||{},d.id=b,d.params=_.extend({settings:[]},d.defaults,c.params||c),d.deferred={},d.deferred.ready=a.Deferred(),d.deferred.ready.done(function(){d.ready()})},ready:function(){var b=this;_.each(b.placements(),function(d){a(d.container).attr("title",c.data.l10n.shiftClickToEdit),b.createEditShortcutForPlacement(d)}),a(document).on("click",b.params.selector,function(c){c.shiftKey&&(c.preventDefault(),_.each(b.placements(),function(d){a(d.container).is(c.currentTarget)&&b.showControl()}))})},createEditShortcutForPlacement:function(b){var c,d,e,f,g=this;b.container&&(d=a(b.container),e="head",f="area, audio, base, bdi, bdo, br, button, canvas, col, colgroup, command, datalist, embed, head, hr, html, iframe, img, input, keygen, label, link, map, math, menu, meta, noscript, object, optgroup, option, param, progress, rp, rt, ruby, script, select, source, style, svg, table, tbody, textarea, tfoot, thead, title, tr, track, video, wbr",!d.length||d.is(f)||d.closest(e).length||(c=g.createEditShortcut(),c.on("click",function(a){a.preventDefault(),a.stopPropagation(),g.showControl()}),g.addEditShortcutToPlacement(b,c)))},addEditShortcutToPlacement:function(b,c){var d=a(b.container);d.prepend(c),d.is(":visible")&&"none"!==d.css("display")||c.addClass("customize-partial-edit-shortcut-hidden")},getEditShortcutClassName:function(){var a,b=this;return a=b.id.replace(/]/g,"").replace(/\[/g,"-"),"customize-partial-edit-shortcut-"+a},getEditShortcutTitle:function(){var a=this,b=c.data.l10n;switch(a.getType()){case"widget":return b.clickEditWidget;case"blogname":return b.clickEditTitle;case"blogdescription":return b.clickEditTitle;case"nav_menu":return b.clickEditMenu;default:return b.clickEditMisc}},getType:function(){var a,b=this;return a=b.params.primarySetting||_.first(b.settings())||"unknown",b.params.type?b.params.type:a.match(/^nav_menu_instance\[/)?"nav_menu":a.match(/^widget_.+\[\d+]$/)?"widget":a},createEditShortcut:function(){var b,c,d,e,f=this;return b=f.getEditShortcutTitle(),c=a("<span>",{"class":"customize-partial-edit-shortcut "+f.getEditShortcutClassName()}),d=a("<button>",{"aria-label":b,title:b,"class":"customize-partial-edit-shortcut-button"}),e=a('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M13.89 3.39l2.71 2.72c.46.46.42 1.24.03 1.64l-8.01 8.02-5.56 1.16 1.16-5.58s7.6-7.63 7.99-8.03c.39-.39 1.22-.39 1.68.07zm-2.73 2.79l-5.59 5.61 1.11 1.11 5.54-5.65zm-2.97 8.23l5.58-5.6-1.07-1.08-5.59 5.6z"/></svg>'),d.append(e),c.append(d),c},placements:function(){var b,c=this;return b=c.params.selector||"",b&&(b+=", "),b+='[data-customize-partial-id="'+c.id+'"]',a(b).map(function(){var b,d=a(this);if(b=d.data("customize-partial-placement-context"),_.isString(b)&&"{"===b.substr(0,1))throw new Error("context JSON parse error");return new e({partial:c,container:d,context:b})}).get()},settings:function(){var a=this;return a.params.settings&&0!==a.params.settings.length?a.params.settings:a.params.primarySetting?[a.params.primarySetting]:[a.id]},isRelatedSetting:function(a){var c=this;return _.isString(a)&&(a=b(a)),!!a&&-1!==_.indexOf(c.settings(),a.id)},showControl:function(){var a=this,c=a.params.primarySetting;c||(c=_.first(a.settings())),"nav_menu"===a.getType()&&(a.params.navMenuArgs.theme_location?c="nav_menu_locations["+a.params.navMenuArgs.theme_location+"]":a.params.navMenuArgs.menu&&(c="nav_menu["+String(a.params.navMenuArgs.menu)+"]")),b.preview.send("focus-control-for-setting",c)},preparePlacement:function(b){a(b.container).addClass("customize-partial-refreshing")},_pendingRefreshPromise:null,refresh:function(){var a,b=this;return a=c.requestPartial(b),b._pendingRefreshPromise||(_.each(b.placements(),function(a){b.preparePlacement(a)}),a.done(function(a){_.each(a,function(a){b.renderContent(a)})}),a.fail(function(a,c){b.fallback(a,c)}),b._pendingRefreshPromise=a,a.always(function(){b._pendingRefreshPromise=null})),a},renderContent:function(b){var d,e,f=this;if(!b.container)return f.fallback(new Error("no_container"),[b]),!1;if(b.container=a(b.container),!1===b.addedContent)return f.fallback(new Error("missing_render"),[b]),!1;if(!_.isString(b.addedContent))return f.fallback(new Error("non_string_content"),[b]),!1;c.orginalDocumentWrite=document.write,document.write=function(){throw new Error(c.data.l10n.badDocumentWrite)};try{if(d=b.addedContent,wp.emoji&&wp.emoji.parse&&!a.contains(document.head,b.container[0])&&(d=wp.emoji.parse(d)),f.params.containerInclusive)e=a(d),b.context=_.extend(b.context,e.data("customize-partial-placement-context")||{}),e.data("customize-partial-placement-context",b.context),b.removedNodes=b.container,b.container=e,b.removedNodes.replaceWith(b.container),b.container.attr("title",c.data.l10n.shiftClickToEdit);else{for(b.removedNodes=document.createDocumentFragment();b.container[0].firstChild;)b.removedNodes.appendChild(b.container[0].firstChild);b.container.html(d)}b.container.removeClass("customize-render-content-error")}catch(g){"undefined"!=typeof console&&console.error&&console.error(f.id,g),f.fallback(g,[b])}return document.write=c.orginalDocumentWrite,c.orginalDocumentWrite=null,f.createEditShortcutForPlacement(b),b.container.removeClass("customize-partial-refreshing"),b.container.data("customize-partial-content-rendered",!0),wp.mediaelement&&wp.mediaelement.initialize(),wp.playlist&&wp.playlist.initialize(),c.trigger("partial-content-rendered",b),!0},fallback:function(){var a=this;a.params.fallbackRefresh&&c.requestFullRefresh()}}),c.Placement=e=b.Class.extend({partial:null,container:null,startNode:null,endNode:null,context:null,addedContent:null,removedNodes:null,initialize:function(b){var c=this;if(b=_.extend({},b||{}),!b.partial||!b.partial.extended(d))throw new Error("Missing partial");b.context=b.context||{},b.container&&(b.container=a(b.container)),_.extend(c,b)}}),c.partialConstructor={},c.partial=new b.Values({defaultConstructor:d}),c.getCustomizeQuery=function(){var a={};return b.each(function(b,c){b._dirty&&(a[c]=b())}),{wp_customize:"on",nonce:b.settings.nonce.preview,customize_theme:b.settings.theme.stylesheet,customized:JSON.stringify(a),customize_changeset_uuid:b.settings.changeset.uuid}},c._pendingPartialRequests={},c._debouncedTimeoutId=null,c._currentRequest=null,c.requestFullRefresh=function(){b.preview.send("refresh")},c.requestPartial=function(d){var f;return c._debouncedTimeoutId&&(clearTimeout(c._debouncedTimeoutId),c._debouncedTimeoutId=null),c._currentRequest&&(c._currentRequest.abort(),c._currentRequest=null),f=c._pendingPartialRequests[d.id],f&&"pending"===f.deferred.state()||(f={deferred:a.Deferred(),partial:d},c._pendingPartialRequests[d.id]=f),d=null,c._debouncedTimeoutId=setTimeout(function(){var a,d,f,g;c._debouncedTimeoutId=null,a=c.getCustomizeQuery(),f={},d={},_.each(c._pendingPartialRequests,function(a,b){f[b]=a.partial.placements(),c.partial.has(b)?d[b]=_.map(f[b],function(a){return a.context||{}}):a.deferred.rejectWith(a.partial,[new Error("partial_removed"),f[b]])}),a.partials=JSON.stringify(d),a[c.data.renderQueryVar]="1",g=c._currentRequest=wp.ajax.send(null,{data:a,url:b.settings.url.self}),g.done(function(a){c.trigger("render-partials-response",a),a.errors&&"undefined"!=typeof console&&console.warn&&_.each(a.errors,function(a){console.warn(a)}),_.each(c._pendingPartialRequests,function(b,c){var d;_.isArray(a.contents[c])?(d=_.map(a.contents[c],function(a,d){var g=f[c][d];return g?g.addedContent=a:g=new e({partial:b.partial,addedContent:a}),g}),b.deferred.resolveWith(b.partial,[d])):b.deferred.rejectWith(b.partial,[new Error("unrecognized_partial"),f[c]])}),c._pendingPartialRequests={}}),g.fail(function(a,b){"abort"!==b&&(_.each(c._pendingPartialRequests,function(b,c){b.deferred.rejectWith(b.partial,[a,f[c]])}),c._pendingPartialRequests={})})},b.settings.timeouts.selectiveRefresh),f.deferred.promise()},c.addPartials=function(b,d){var f;b||(b=document.documentElement),b=a(b),d=_.extend({triggerRendered:!0},d||{}),f=b.find("[data-customize-partial-id]"),b.is("[data-customize-partial-id]")&&(f=f.add(b)),f.each(function(){var b,f,g,h,i,j,k=a(this);g=k.data("customize-partial-id"),g&&(j=k.data("customize-partial-placement-context")||{},b=c.partial(g),b||(i=k.data("customize-partial-options")||{},i.constructingContainerContext=k.data("customize-partial-placement-context")||{},h=c.partialConstructor[k.data("customize-partial-type")]||c.Partial,b=new h(g,i),c.partial.add(b)),d.triggerRendered&&!k.data("customize-partial-content-rendered")&&(f=new e({partial:b,context:j,container:k}),a(f.container).attr("title",c.data.l10n.shiftClickToEdit),b.createEditShortcutForPlacement(f),c.trigger("partial-content-rendered",f)),k.data("customize-partial-content-rendered",!0))})},b.bind("preview-ready",function(){var d,e,f;_.extend(c.data,_customizePartialRefreshExports),_.each(c.data.partials,function(a,b){var d,e=c.partial(b);e?_.extend(e.params,a):(d=c.partialConstructor[a.type]||c.Partial,e=new d(b,_.extend({params:a},a)),c.partial.add(e))}),d=function(a,b){var d=this;c.partial.each(function(c){c.isRelatedSetting(d,a,b)&&c.refresh()})},e=function(a){d.call(a,a(),null),a.bind(d)},f=function(a){d.call(a,null,a()),a.unbind(d)},b.bind("add",e),b.bind("remove",f),b.each(function(a){a.bind(d)}),c.addPartials(document.documentElement,{triggerRendered:!1}),"undefined"!=typeof MutationObserver&&(c.mutationObserver=new MutationObserver(function(b){_.each(b,function(b){c.addPartials(a(b.target))})}),c.mutationObserver.observe(document.documentElement,{childList:!0,subtree:!0})),b.selectiveRefresh.bind("partial-content-rendered",function(a){a.container&&c.addPartials(a.container)}),b.selectiveRefresh.bind("render-partials-response",function(a){a.setting_validities&&b.preview.send("selective-refresh-setting-validities",a.setting_validities)}),b.preview.bind("edit-shortcut-visibility",function(a){b.selectiveRefresh.editShortcutVisibility.set(a)}),b.selectiveRefresh.editShortcutVisibility.bind(function(b){var c,d=a(document.body);c="hidden"===b&&d.hasClass("customize-partial-edit-shortcuts-shown")&&!d.hasClass("customize-partial-edit-shortcuts-hidden"),d.toggleClass("customize-partial-edit-shortcuts-hidden",c),d.toggleClass("customize-partial-edit-shortcuts-shown","visible"===b)}),b.preview.bind("active",function(){c.partial.each(function(a){a.deferred.ready.resolve()}),c.partial.bind("add",function(a){a.deferred.ready.resolve()})})}),c}(jQuery,wp.customize);