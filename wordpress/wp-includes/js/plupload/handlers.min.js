function fileQueued(a){jQuery(".media-blank").remove();var b=jQuery("#media-items").children(),c=post_id||0;1==b.length&&b.removeClass("open").find(".slidetoggle").slideUp(200),jQuery('<div class="media-item">').attr("id","media-item-"+a.id).addClass("child-of-"+c).append('<div class="progress"><div class="percent">0%</div><div class="bar"></div></div>',jQuery('<div class="filename original">').text(" "+a.name)).appendTo(jQuery("#media-items")),jQuery("#insert-gallery").prop("disabled",!0)}function uploadStart(){try{"undefined"!=typeof topWin.tb_remove&&topWin.jQuery("#TB_overlay").unbind("click",topWin.tb_remove)}catch(a){}return!0}function uploadProgress(a,b){var c=jQuery("#media-item-"+b.id);jQuery(".bar",c).width(200*b.loaded/b.size),jQuery(".percent",c).html(b.percent+"%")}function fileUploading(a,b){var c=104857600,d=parseInt(a.settings.max_file_size,10);d>c&&b.size>c&&setTimeout(function(){b.status<3&&0===b.loaded&&(wpFileError(b,pluploadL10n.big_upload_failed.replace("%1$s",'<a class="uploader-html" href="#">').replace("%2$s","</a>")),a.stop(),a.removeFile(b),a.start())},1e4)}function updateMediaForm(){var a=jQuery("#media-items").children();1==a.length?(a.addClass("open").find(".slidetoggle").show(),jQuery(".insert-gallery").hide()):a.length>1&&(a.removeClass("open"),jQuery(".insert-gallery").show()),a.not(".media-blank").length>0?jQuery(".savebutton").show():jQuery(".savebutton").hide()}function uploadSuccess(a,b){var c=jQuery("#media-item-"+a.id);return b=b.replace(/^<pre>(\d+)<\/pre>$/,"$1"),b.match(/media-upload-error|error-div/)?void c.html(b):(jQuery(".percent",c).html(pluploadL10n.crunching),prepareMediaItem(a,b),updateMediaForm(),void(post_id&&c.hasClass("child-of-"+post_id)&&jQuery("#attachments-count").text(1*jQuery("#attachments-count").text()+1)))}function setResize(a){a?window.resize_width&&window.resize_height?uploader.settings.resize={enabled:!0,width:window.resize_width,height:window.resize_height,quality:100}:uploader.settings.multipart_params.image_resize=!0:delete uploader.settings.multipart_params.image_resize}function prepareMediaItem(a,b){var c="undefined"==typeof shortform?1:2,d=jQuery("#media-item-"+a.id);2==c&&shortform>2&&(c=shortform);try{"undefined"!=typeof topWin.tb_remove&&topWin.jQuery("#TB_overlay").click(topWin.tb_remove)}catch(e){}isNaN(b)||!b?(d.append(b),prepareMediaItemInit(a)):d.load("async-upload.php",{attachment_id:b,fetch:c},function(){prepareMediaItemInit(a),updateMediaForm()})}function prepareMediaItemInit(a){var b=jQuery("#media-item-"+a.id);jQuery(".thumbnail",b).clone().attr("class","pinkynail toggle").prependTo(b),jQuery(".filename.original",b).replaceWith(jQuery(".filename.new",b)),jQuery("a.delete",b).click(function(){return jQuery.ajax({url:ajaxurl,type:"post",success:deleteSuccess,error:deleteError,id:a.id,data:{id:this.id.replace(/[^0-9]/g,""),action:"trash-post",_ajax_nonce:this.href.replace(/^.*wpnonce=/,"")}}),!1}),jQuery("a.undo",b).click(function(){return jQuery.ajax({url:ajaxurl,type:"post",id:a.id,data:{id:this.id.replace(/[^0-9]/g,""),action:"untrash-post",_ajax_nonce:this.href.replace(/^.*wpnonce=/,"")},success:function(){var b,c=jQuery("#media-item-"+a.id);(b=jQuery("#type-of-"+a.id).val())&&jQuery("#"+b+"-counter").text(jQuery("#"+b+"-counter").text()-0+1),post_id&&c.hasClass("child-of-"+post_id)&&jQuery("#attachments-count").text(jQuery("#attachments-count").text()-0+1),jQuery(".filename .trashnotice",c).remove(),jQuery(".filename .title",c).css("font-weight","normal"),jQuery("a.undo",c).addClass("hidden"),jQuery(".menu_order_input",c).show(),c.css({backgroundColor:"#ceb"}).animate({backgroundColor:"#fff"},{queue:!1,duration:500,complete:function(){jQuery(this).css({backgroundColor:""})}}).removeClass("undo")}}),!1}),jQuery("#media-item-"+a.id+".startopen").removeClass("startopen").addClass("open").find("slidetoggle").fadeIn()}function wpQueueError(a){jQuery("#media-upload-error").show().html('<div class="error"><p>'+a+"</p></div>")}function wpFileError(a,b){itemAjaxError(a.id,b)}function itemAjaxError(a,b){var c=jQuery("#media-item-"+a),d=c.find(".filename").text(),e=c.data("last-err");e!=a&&c.html('<div class="error-div"><a class="dismiss" href="#">'+pluploadL10n.dismiss+"</a><strong>"+pluploadL10n.error_uploading.replace("%s",jQuery.trim(d))+"</strong> "+b+"</div>").data("last-err",a)}function deleteSuccess(a){var b,c,d;return"-1"==a?itemAjaxError(this.id,"You do not have permission. Has your session expired?"):"0"==a?itemAjaxError(this.id,"Could not be deleted. Has it been deleted already?"):(c=this.id,d=jQuery("#media-item-"+c),(b=jQuery("#type-of-"+c).val())&&jQuery("#"+b+"-counter").text(jQuery("#"+b+"-counter").text()-1),post_id&&d.hasClass("child-of-"+post_id)&&jQuery("#attachments-count").text(jQuery("#attachments-count").text()-1),1==jQuery("form.type-form #media-items").children().length&&jQuery(".hidden","#media-items").length>0&&(jQuery(".toggle").toggle(),jQuery(".slidetoggle").slideUp(200).siblings().removeClass("hidden")),jQuery(".toggle",d).toggle(),jQuery(".slidetoggle",d).slideUp(200).siblings().removeClass("hidden"),d.css({backgroundColor:"#faa"}).animate({backgroundColor:"#f4f4f4"},{queue:!1,duration:500}).addClass("undo"),jQuery(".filename:empty",d).remove(),jQuery(".filename .title",d).css("font-weight","bold"),jQuery(".filename",d).append('<span class="trashnotice"> '+pluploadL10n.deleted+" </span>").siblings("a.toggle").hide(),jQuery(".filename",d).append(jQuery("a.undo",d).removeClass("hidden")),void jQuery(".menu_order_input",d).hide())}function deleteError(){}function uploadComplete(){jQuery("#insert-gallery").prop("disabled",!1)}function switchUploader(a){a?(deleteUserSetting("uploader"),jQuery(".media-upload-form").removeClass("html-uploader"),"object"==typeof uploader&&uploader.refresh()):(setUserSetting("uploader","1"),jQuery(".media-upload-form").addClass("html-uploader"))}function uploadError(a,b,c,d){var e,f=104857600;switch(b){case plupload.FAILED:wpFileError(a,pluploadL10n.upload_failed);break;case plupload.FILE_EXTENSION_ERROR:wpFileExtensionError(d,a,pluploadL10n.invalid_filetype);break;case plupload.FILE_SIZE_ERROR:uploadSizeError(d,a);break;case plupload.IMAGE_FORMAT_ERROR:wpFileError(a,pluploadL10n.not_an_image);break;case plupload.IMAGE_MEMORY_ERROR:wpFileError(a,pluploadL10n.image_memory_exceeded);break;case plupload.IMAGE_DIMENSIONS_ERROR:wpFileError(a,pluploadL10n.image_dimensions_exceeded);break;case plupload.GENERIC_ERROR:wpQueueError(pluploadL10n.upload_failed);break;case plupload.IO_ERROR:e=parseInt(d.settings.filters.max_file_size,10),e>f&&a.size>f?wpFileError(a,pluploadL10n.big_upload_failed.replace("%1$s",'<a class="uploader-html" href="#">').replace("%2$s","</a>")):wpQueueError(pluploadL10n.io_error);break;case plupload.HTTP_ERROR:wpQueueError(pluploadL10n.http_error);break;case plupload.INIT_ERROR:jQuery(".media-upload-form").addClass("html-uploader");break;case plupload.SECURITY_ERROR:wpQueueError(pluploadL10n.security_error);break;default:wpFileError(a,pluploadL10n.default_error)}}function uploadSizeError(a,b){var c,d;c=pluploadL10n.file_exceeds_size_limit.replace("%s",b.name),d=jQuery("<div />").attr({id:"media-item-"+b.id,"class":"media-item error"}).append(jQuery("<p />").text(c)),jQuery("#media-items").append(d),a.removeFile(b)}function wpFileExtensionError(a,b,c){jQuery("#media-items").append('<div id="media-item-'+b.id+'" class="media-item error"><p>'+c+"</p></div>"),a.removeFile(b)}var topWin=window.dialogArguments||opener||parent||top,uploader,uploader_init;jQuery(document).ready(function(a){a(".media-upload-form").bind("click.uploader",function(b){var c,d,e=a(b.target);e.is('input[type="radio"]')?(c=e.closest("tr"),c.hasClass("align")?setUserSetting("align",e.val()):c.hasClass("image-size")&&setUserSetting("imgsize",e.val())):e.is("button.button")?(d=b.target.className||"",d=d.match(/url([^ '"]+)/),d&&d[1]&&(setUserSetting("urlbutton",d[1]),e.siblings(".urlfield").val(e.data("link-url")))):e.is("a.dismiss")?e.parents(".media-item").fadeOut(200,function(){a(this).remove()}):e.is(".upload-flash-bypass a")||e.is("a.uploader-html")?(a("#media-items, p.submit, span.big-file-warning").css("display","none"),switchUploader(0),b.preventDefault()):e.is(".upload-html-bypass a")?(a("#media-items, p.submit, span.big-file-warning").css("display",""),switchUploader(1),b.preventDefault()):e.is("a.describe-toggle-on")?(e.parent().addClass("open"),e.siblings(".slidetoggle").fadeIn(250,function(){var b,c,d=a(window).scrollTop(),e=a(window).height(),f=a(this).offset().top,g=a(this).height();e&&f&&g&&(b=f+g,c=d+e,b>c&&(b-c<f-d?window.scrollBy(0,b-c+10):window.scrollBy(0,f-d-40)))}),b.preventDefault()):e.is("a.describe-toggle-off")&&(e.siblings(".slidetoggle").fadeOut(250,function(){e.parent().removeClass("open")}),b.preventDefault())}),uploader_init=function(){var b=navigator.userAgent.indexOf("Trident/")!=-1||navigator.userAgent.indexOf("MSIE ")!=-1;b||"flash"!==plupload.predictRuntime(wpUploaderInit)||wpUploaderInit.required_features&&wpUploaderInit.required_features.hasOwnProperty("send_binary_string")||(wpUploaderInit.required_features=wpUploaderInit.required_features||{},wpUploaderInit.required_features.send_binary_string=!0),uploader=new plupload.Uploader(wpUploaderInit),a("#image_resize").bind("change",function(){var b=a(this).prop("checked");setResize(b),b?setUserSetting("upload_resize","1"):deleteUserSetting("upload_resize")}),uploader.bind("Init",function(b){var c=a("#plupload-upload-ui");setResize(getUserSetting("upload_resize",!1)),b.features.dragdrop&&!a(document.body).hasClass("mobile")?(c.addClass("drag-drop"),a("#drag-drop-area").on("dragover.wp-uploader",function(){c.addClass("drag-over")}).on("dragleave.wp-uploader, drop.wp-uploader",function(){c.removeClass("drag-over")})):(c.removeClass("drag-drop"),a("#drag-drop-area").off(".wp-uploader")),"html4"===b.runtime&&a(".upload-flash-bypass").hide()}),uploader.bind("postinit",function(a){a.refresh()}),uploader.init(),uploader.bind("FilesAdded",function(b,c){a("#media-upload-error").empty(),uploadStart(),plupload.each(c,function(a){fileQueued(a)}),b.refresh(),b.start()}),uploader.bind("UploadFile",function(a,b){fileUploading(a,b)}),uploader.bind("UploadProgress",function(a,b){uploadProgress(a,b)}),uploader.bind("Error",function(a,b){uploadError(b.file,b.code,b.message,a),a.refresh()}),uploader.bind("FileUploaded",function(a,b,c){uploadSuccess(b,c.response)}),uploader.bind("UploadComplete",function(){uploadComplete()})},"object"==typeof wpUploaderInit&&uploader_init()});