var MXI_DEBUG=!1;!function(a,b){"use strict";function c(a,b){for(var c,d=[],f=0;f<a.length;++f){if(c=g[a[f]]||e(a[f]),!c)throw"module definition dependecy not found: "+a[f];d.push(c)}b.apply(null,d)}function d(a,d,e){if("string"!=typeof a)throw"invalid module definition, module id must be defined and be a string";if(d===b)throw"invalid module definition, dependencies must be specified";if(e===b)throw"invalid module definition, definition function must be specified";c(d,function(){g[a]=e.apply(null,arguments)})}function e(b){for(var c=a,d=b.split(/[.\/]/),e=0;e<d.length;++e){if(!c[d[e]])return;c=c[d[e]]}return c}function f(c){for(var d=0;d<c.length;d++){for(var e=a,f=c[d],h=f.split(/[.\/]/),i=0;i<h.length-1;++i)e[h[i]]===b&&(e[h[i]]={}),e=e[h[i]];e[h[h.length-1]]=g[f]}}var g={};d("moxie/core/utils/Basic",[],function(){var a=function(a){var b;return a===b?"undefined":null===a?"null":a.nodeType?"node":{}.toString.call(a).match(/\s([a-z|A-Z]+)/)[1].toLowerCase()},b=function(d){var e;return c(arguments,function(f,h){h>0&&c(f,function(c,f){c!==e&&(a(d[f])===a(c)&&~g(a(c),["array","object"])?b(d[f],c):d[f]=c)})}),d},c=function(b,c){var d,e,f;if(b)if("number"===a(b.length)){for(f=0,d=b.length;f<d;f++)if(c(b[f],f)===!1)return}else if("object"===a(b))for(e in b)if(b.hasOwnProperty(e)&&c(b[e],e)===!1)return},d=function(b){var c;if(!b||"object"!==a(b))return!0;for(c in b)return!1;return!0},e=function(b,c){function d(e){"function"===a(b[e])&&b[e](function(a){++e<f&&!a?d(e):c(a)})}var e=0,f=b.length;"function"!==a(c)&&(c=function(){}),b&&b.length||c(),d(e)},f=function(a,b){var d=0,e=a.length,f=new Array(e);c(a,function(a,c){a(function(a){if(a)return b(a);var g=[].slice.call(arguments);g.shift(),f[c]=g,d++,d===e&&(f.unshift(null),b.apply(this,f))})})},g=function(a,b){if(b){if(Array.prototype.indexOf)return Array.prototype.indexOf.call(b,a);for(var c=0,d=b.length;c<d;c++)if(b[c]===a)return c}return-1},h=function(b,c){var d=[];"array"!==a(b)&&(b=[b]),"array"!==a(c)&&(c=[c]);for(var e in b)g(b[e],c)===-1&&d.push(b[e]);return!!d.length&&d},i=function(a,b){var d=[];return c(a,function(a){g(a,b)!==-1&&d.push(a)}),d.length?d:null},j=function(a){var b,c=[];for(b=0;b<a.length;b++)c[b]=a[b];return c},k=function(){var a=0;return function(b){var c,d=(new Date).getTime().toString(32);for(c=0;c<5;c++)d+=Math.floor(65535*Math.random()).toString(32);return(b||"o_")+d+(a++).toString(32)}}(),l=function(a){return a?String.prototype.trim?String.prototype.trim.call(a):a.toString().replace(/^\s*/,"").replace(/\s*$/,""):a},m=function(a){if("string"!=typeof a)return a;var b,c={t:1099511627776,g:1073741824,m:1048576,k:1024};return a=/^([0-9\.]+)([tmgk]?)$/.exec(a.toLowerCase().replace(/[^0-9\.tmkg]/g,"")),b=a[2],a=+a[1],c.hasOwnProperty(b)&&(a*=c[b]),Math.floor(a)},n=function(b){var c=[].slice.call(arguments,1);return b.replace(/%[a-z]/g,function(){var b=c.shift();return"undefined"!==a(b)?b:""})};return{guid:k,typeOf:a,extend:b,each:c,isEmptyObj:d,inSeries:e,inParallel:f,inArray:g,arrayDiff:h,arrayIntersect:i,toArray:j,trim:l,sprintf:n,parseSizeStr:m}}),d("moxie/core/utils/Env",["moxie/core/utils/Basic"],function(a){function b(a,b,c){var d=0,e=0,f=0,g={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1},h=function(a){return a=(""+a).replace(/[_\-+]/g,"."),a=a.replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,"."),a.length?a.split("."):[-8]},i=function(a){return a?isNaN(a)?g[a]||-7:parseInt(a,10):0};for(a=h(a),b=h(b),e=Math.max(a.length,b.length),d=0;d<e;d++)if(a[d]!=b[d]){if(a[d]=i(a[d]),b[d]=i(b[d]),a[d]<b[d]){f=-1;break}if(a[d]>b[d]){f=1;break}}if(!c)return f;switch(c){case">":case"gt":return f>0;case">=":case"ge":return f>=0;case"<=":case"le":return f<=0;case"==":case"=":case"eq":return 0===f;case"<>":case"!=":case"ne":return 0!==f;case"":case"<":case"lt":return f<0;default:return null}}var c=function(a){var b="",c="?",d="function",e="undefined",f="object",g="name",h="version",i={has:function(a,b){return b.toLowerCase().indexOf(a.toLowerCase())!==-1},lowerize:function(a){return a.toLowerCase()}},j={rgx:function(){for(var b,c,g,h,i,j,k,l=0,m=arguments;l<m.length;l+=2){var n=m[l],o=m[l+1];if(typeof b===e){b={};for(h in o)i=o[h],typeof i===f?b[i[0]]=a:b[i]=a}for(c=g=0;c<n.length;c++)if(j=n[c].exec(this.getUA())){for(h=0;h<o.length;h++)k=j[++g],i=o[h],typeof i===f&&i.length>0?2==i.length?typeof i[1]==d?b[i[0]]=i[1].call(this,k):b[i[0]]=i[1]:3==i.length?typeof i[1]!==d||i[1].exec&&i[1].test?b[i[0]]=k?k.replace(i[1],i[2]):a:b[i[0]]=k?i[1].call(this,k,i[2]):a:4==i.length&&(b[i[0]]=k?i[3].call(this,k.replace(i[1],i[2])):a):b[i]=k?k:a;break}if(j)break}return b},str:function(b,d){for(var e in d)if(typeof d[e]===f&&d[e].length>0){for(var g=0;g<d[e].length;g++)if(i.has(d[e][g],b))return e===c?a:e}else if(i.has(d[e],b))return e===c?a:e;return b}},k={browser:{oldsafari:{major:{1:["/8","/1","/3"],2:"/4","?":"/"},version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2000:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",RT:"ARM"}}}},l={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[g,h],[/\s(opr)\/([\w\.]+)/i],[[g,"Opera"],h],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]+)*/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?([\w\.]*)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]+)*/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi)\/([\w\.-]+)/i],[g,h],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[[g,"IE"],h],[/(edge)\/((\d+)?[\w\.]+)/i],[g,h],[/(yabrowser)\/([\w\.]+)/i],[[g,"Yandex"],h],[/(comodo_dragon)\/([\w\.]+)/i],[[g,/_/g," "],h],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i,/(uc\s?browser|qqbrowser)[\/\s]?([\w\.]+)/i],[g,h],[/(dolfin)\/([\w\.]+)/i],[[g,"Dolphin"],h],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[[g,"Chrome"],h],[/XiaoMi\/MiuiBrowser\/([\w\.]+)/i],[h,[g,"MIUI Browser"]],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)/i],[h,[g,"Android Browser"]],[/FBAV\/([\w\.]+);/i],[h,[g,"Facebook"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],[h,[g,"Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],[h,g],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[g,[h,j.str,k.browser.oldsafari.version]],[/(konqueror)\/([\w\.]+)/i,/(webkit|khtml)\/([\w\.]+)/i],[g,h],[/(navigator|netscape)\/([\w\.-]+)/i],[[g,"Netscape"],h],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\/([\w\.-]+)/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]+)*/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[g,h]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[h,[g,"EdgeHTML"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[g,h],[/rv\:([\w\.]+).*(gecko)/i],[h,g]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[g,h],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*|windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[g,[h,j.str,k.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[g,"Windows"],[h,j.str,k.os.windows.version]],[/\((bb)(10);/i],[[g,"BlackBerry"],h],[/(blackberry)\w*\/?([\w\.]+)*/i,/(tizen)[\/\s]([\w\.]+)/i,/(android|webos|palm\os|qnx|bada|rim\stablet\sos|meego|contiki)[\/\s-]?([\w\.]+)*/i,/linux;.+(sailfish);/i],[g,h],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]+)*/i],[[g,"Symbian"],h],[/\((series40);/i],[g],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[g,"Firefox OS"],h],[/(nintendo|playstation)\s([wids3portablevu]+)/i,/(mint)[\/\s\(]?(\w+)*/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?([\w\.-]+)*/i,/(hurd|linux)\s?([\w\.]+)*/i,/(gnu)\s?([\w\.]+)*/i],[g,h],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[g,"Chromium OS"],h],[/(sunos)\s?([\w\.]+\d)*/i],[[g,"Solaris"],h],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]+)*/i],[g,h],[/(ip[honead]+)(?:.*os\s*([\w]+)*\slike\smac|;\sopera)/i],[[g,"iOS"],[h,/_/g,"."]],[/(mac\sos\sx)\s?([\w\s\.]+\w)*/i,/(macintosh|mac(?=_powerpc)\s)/i],[[g,"Mac OS"],[h,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]+)*/i,/(haiku)\s(\w+)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.]*)*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms)/i,/(unix)\s?([\w\.]+)*/i],[g,h]]},m=function(a){var c=a||(window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:b);this.getBrowser=function(){return j.rgx.apply(this,l.browser)},this.getEngine=function(){return j.rgx.apply(this,l.engine)},this.getOS=function(){return j.rgx.apply(this,l.os)},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS()}},this.getUA=function(){return c},this.setUA=function(a){return c=a,this},this.setUA(c)};return m}(),d=function(){var b={define_property:function(){return!1}(),create_canvas:function(){var a=document.createElement("canvas");return!(!a.getContext||!a.getContext("2d"))}(),return_response_type:function(b){try{if(a.inArray(b,["","text","document"])!==-1)return!0;if(window.XMLHttpRequest){var c=new XMLHttpRequest;if(c.open("get","/"),"responseType"in c)return c.responseType=b,c.responseType===b}}catch(d){}return!1},use_data_uri:function(){var a=new Image;return a.onload=function(){b.use_data_uri=1===a.width&&1===a.height},setTimeout(function(){a.src="data:image/gif;base64,R0lGODlhAQABAIAAAP8AAAAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="},1),!1}(),use_data_uri_over32kb:function(){return b.use_data_uri&&("IE"!==f.browser||f.version>=9)},use_data_uri_of:function(a){return b.use_data_uri&&a<33e3||b.use_data_uri_over32kb()},use_fileinput:function(){if(navigator.userAgent.match(/(Android (1.0|1.1|1.5|1.6|2.0|2.1))|(Windows Phone (OS 7|8.0))|(XBLWP)|(ZuneWP)|(w(eb)?OSBrowser)|(webOS)|(Kindle\/(1.0|2.0|2.5|3.0))/))return!1;var a=document.createElement("input");return a.setAttribute("type","file"),!a.disabled}};return function(c){var d=[].slice.call(arguments);return d.shift(),"function"===a.typeOf(b[c])?b[c].apply(this,d):!!b[c]}}(),e=(new c).getResult(),f={can:d,uaParser:c,browser:e.browser.name,version:e.browser.version,os:e.os.name,osVersion:e.os.version,verComp:b,global_event_dispatcher:"moxie.core.EventTarget.instance.dispatchEvent"};return f.OS=f.os,MXI_DEBUG&&(f.debug={runtime:!0,events:!1},f.log=function(){function b(a){d.appendChild(document.createTextNode(a+"\n"))}var c=arguments[0];if("string"===a.typeOf(c)&&(c=a.sprintf.apply(this,arguments)),window&&window.console&&window.console.log)window.console.log(c);else if(document){var d=document.getElementById("moxie-console");d||(d=document.createElement("pre"),d.id="moxie-console",document.body.appendChild(d)),a.inArray(a.typeOf(c),["object","array"])!==-1?b(c):d.appendChild(document.createTextNode(c+"\n"))}}),f}),d("moxie/core/I18n",["moxie/core/utils/Basic"],function(a){var b={};return{addI18n:function(c){return a.extend(b,c)},translate:function(a){return b[a]||a},_:function(a){return this.translate(a)},sprintf:function(b){var c=[].slice.call(arguments,1);return b.replace(/%[a-z]/g,function(){var b=c.shift();return"undefined"!==a.typeOf(b)?b:""})}}}),d("moxie/core/utils/Mime",["moxie/core/utils/Basic","moxie/core/I18n"],function(a,b){var c="application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats-officedocument.wordprocessingml.document,docx,application/vnd.openxmlformats-officedocument.wordprocessingml.template,dotx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,xlsx,application/vnd.openxmlformats-officedocument.presentationml.presentation,pptx,application/vnd.openxmlformats-officedocument.presentationml.template,potx,application/vnd.openxmlformats-officedocument.presentationml.slideshow,ppsx,application/x-javascript,js,application/json,json,audio/mpeg,mp3 mpga mpega mp2,audio/x-wav,wav,audio/x-m4a,m4a,audio/ogg,oga ogg,audio/aiff,aiff aif,audio/flac,flac,audio/aac,aac,audio/ac3,ac3,audio/x-ms-wma,wma,image/bmp,bmp,image/gif,gif,image/jpeg,jpg jpeg jpe,image/photoshop,psd,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/plain,asc txt text diff log,text/html,htm html xhtml,text/css,css,text/csv,csv,text/rtf,rtf,video/mpeg,mpeg mpg mpe m2v,video/quicktime,qt mov,video/mp4,mp4,video/x-m4v,m4v,video/x-flv,flv,video/x-ms-wmv,wmv,video/avi,avi,video/webm,webm,video/3gpp,3gpp 3gp,video/3gpp2,3g2,video/vnd.rn-realvideo,rv,video/ogg,ogv,video/x-matroska,mkv,application/vnd.oasis.opendocument.formula-template,otf,application/octet-stream,exe",d={mimes:{},extensions:{},addMimeType:function(a){var b,c,d,e=a.split(/,/);for(b=0;b<e.length;b+=2){for(d=e[b+1].split(/ /),c=0;c<d.length;c++)this.mimes[d[c]]=e[b];this.extensions[e[b]]=d}},extList2mimes:function(b,c){var d,e,f,g,h=this,i=[];for(e=0;e<b.length;e++)for(d=b[e].extensions.split(/\s*,\s*/),f=0;f<d.length;f++){if("*"===d[f])return[];if(g=h.mimes[d[f]],g&&a.inArray(g,i)===-1&&i.push(g),c&&/^\w+$/.test(d[f]))i.push("."+d[f]);else if(!g)return[]}return i},mimes2exts:function(b){var c=this,d=[];return a.each(b,function(b){if("*"===b)return d=[],!1;var e=b.match(/^(\w+)\/(\*|\w+)$/);e&&("*"===e[2]?a.each(c.extensions,function(a,b){new RegExp("^"+e[1]+"/").test(b)&&[].push.apply(d,c.extensions[b])}):c.extensions[b]&&[].push.apply(d,c.extensions[b]))}),d},mimes2extList:function(c){var d=[],e=[];return"string"===a.typeOf(c)&&(c=a.trim(c).split(/\s*,\s*/)),e=this.mimes2exts(c),d.push({title:b.translate("Files"),extensions:e.length?e.join(","):"*"}),d.mimes=c,d},getFileExtension:function(a){var b=a&&a.match(/\.([^.]+)$/);return b?b[1].toLowerCase():""},getFileMime:function(a){return this.mimes[this.getFileExtension(a)]||""}};return d.addMimeType(c),d}),d("moxie/core/utils/Dom",["moxie/core/utils/Env"],function(a){var b=function(a){return"string"!=typeof a?a:document.getElementById(a)},c=function(a,b){if(!a.className)return!1;var c=new RegExp("(^|\\s+)"+b+"(\\s+|$)");return c.test(a.className)},d=function(a,b){c(a,b)||(a.className=a.className?a.className.replace(/\s+$/,"")+" "+b:b)},e=function(a,b){if(a.className){var c=new RegExp("(^|\\s+)"+b+"(\\s+|$)");a.className=a.className.replace(c,function(a,b,c){return" "===b&&" "===c?" ":""})}},f=function(a,b){return a.currentStyle?a.currentStyle[b]:window.getComputedStyle?window.getComputedStyle(a,null)[b]:void 0},g=function(b,c){function d(a){var b,c,d=0,e=0;return a&&(c=a.getBoundingClientRect(),b="CSS1Compat"===j.compatMode?j.documentElement:j.body,d=c.left+b.scrollLeft,e=c.top+b.scrollTop),{x:d,y:e}}var e,f,g,h=0,i=0,j=document;if(b=b,c=c||j.body,b&&b.getBoundingClientRect&&"IE"===a.browser&&(!j.documentMode||j.documentMode<8))return f=d(b),g=d(c),{x:f.x-g.x,y:f.y-g.y};for(e=b;e&&e!=c&&e.nodeType;)h+=e.offsetLeft||0,i+=e.offsetTop||0,e=e.offsetParent;for(e=b.parentNode;e&&e!=c&&e.nodeType;)h-=e.scrollLeft||0,i-=e.scrollTop||0,e=e.parentNode;return{x:h,y:i}},h=function(a){return{w:a.offsetWidth||a.clientWidth,h:a.offsetHeight||a.clientHeight}};return{get:b,hasClass:c,addClass:d,removeClass:e,getStyle:f,getPos:g,getSize:h}}),d("moxie/core/Exceptions",["moxie/core/utils/Basic"],function(a){function b(a,b){var c;for(c in a)if(a[c]===b)return c;return null}return{RuntimeError:function(){function c(a){this.code=a,this.name=b(d,a),this.message=this.name+": RuntimeError "+this.code}var d={NOT_INIT_ERR:1,NOT_SUPPORTED_ERR:9,JS_ERR:4};return a.extend(c,d),c.prototype=Error.prototype,c}(),OperationNotAllowedException:function(){function b(a){this.code=a,this.name="OperationNotAllowedException"}return a.extend(b,{NOT_ALLOWED_ERR:1}),b.prototype=Error.prototype,b}(),ImageError:function(){function c(a){this.code=a,this.name=b(d,a),this.message=this.name+": ImageError "+this.code}var d={WRONG_FORMAT:1,MAX_RESOLUTION_ERR:2,INVALID_META_ERR:3};return a.extend(c,d),c.prototype=Error.prototype,c}(),FileException:function(){function c(a){this.code=a,this.name=b(d,a),this.message=this.name+": FileException "+this.code}var d={NOT_FOUND_ERR:1,SECURITY_ERR:2,ABORT_ERR:3,NOT_READABLE_ERR:4,ENCODING_ERR:5,NO_MODIFICATION_ALLOWED_ERR:6,INVALID_STATE_ERR:7,SYNTAX_ERR:8};return a.extend(c,d),c.prototype=Error.prototype,c}(),DOMException:function(){function c(a){this.code=a,this.name=b(d,a),this.message=this.name+": DOMException "+this.code}var d={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};return a.extend(c,d),c.prototype=Error.prototype,c}(),EventException:function(){function b(a){this.code=a,this.name="EventException"}return a.extend(b,{UNSPECIFIED_EVENT_TYPE_ERR:0}),b.prototype=Error.prototype,b}()}}),d("moxie/core/EventTarget",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic"],function(a,b,c){function d(){var d={};c.extend(this,{uid:null,init:function(){this.uid||(this.uid=c.guid("uid_"))},addEventListener:function(a,b,e,f){var g,h=this;return this.hasOwnProperty("uid")||(this.uid=c.guid("uid_")),a=c.trim(a),/\s/.test(a)?void c.each(a.split(/\s+/),function(a){h.addEventListener(a,b,e,f)}):(a=a.toLowerCase(),e=parseInt(e,10)||0,g=d[this.uid]&&d[this.uid][a]||[],g.push({fn:b,priority:e,scope:f||this}),d[this.uid]||(d[this.uid]={}),void(d[this.uid][a]=g))},hasEventListener:function(a){var b=a?d[this.uid]&&d[this.uid][a]:d[this.uid];return!!b&&b},removeEventListener:function(a,b){a=a.toLowerCase();var e,f=d[this.uid]&&d[this.uid][a];if(f){if(b){for(e=f.length-1;e>=0;e--)if(f[e].fn===b){f.splice(e,1);break}}else f=[];f.length||(delete d[this.uid][a],c.isEmptyObj(d[this.uid])&&delete d[this.uid])}},removeAllEventListeners:function(){d[this.uid]&&delete d[this.uid]},dispatchEvent:function(e){var f,g,h,i,j,k={},l=!0;if("string"!==c.typeOf(e)){if(i=e,"string"!==c.typeOf(i.type))throw new b.EventException(b.EventException.UNSPECIFIED_EVENT_TYPE_ERR);e=i.type,i.total!==j&&i.loaded!==j&&(k.total=i.total,k.loaded=i.loaded),k.async=i.async||!1}if(e.indexOf("::")!==-1?!function(a){f=a[0],e=a[1]}(e.split("::")):f=this.uid,e=e.toLowerCase(),g=d[f]&&d[f][e]){g.sort(function(a,b){return b.priority-a.priority}),h=[].slice.call(arguments),h.shift(),k.type=e,h.unshift(k),MXI_DEBUG&&a.debug.events&&a.log("Event '%s' fired on %u",k.type,f);var m=[];c.each(g,function(a){h[0].target=a.scope,k.async?m.push(function(b){setTimeout(function(){b(a.fn.apply(a.scope,h)===!1)},1)}):m.push(function(b){b(a.fn.apply(a.scope,h)===!1)})}),m.length&&c.inSeries(m,function(a){l=!a})}return l},bind:function(){this.addEventListener.apply(this,arguments)},unbind:function(){this.removeEventListener.apply(this,arguments)},unbindAll:function(){this.removeAllEventListeners.apply(this,arguments)},trigger:function(){return this.dispatchEvent.apply(this,arguments)},handleEventProps:function(a){var b=this;this.bind(a.join(" "),function(a){var b="on"+a.type.toLowerCase();"function"===c.typeOf(this[b])&&this[b].apply(this,arguments)}),c.each(a,function(a){a="on"+a.toLowerCase(a),"undefined"===c.typeOf(b[a])&&(b[a]=null)})}})}return d.instance=new d,d}),d("moxie/runtime/Runtime",["moxie/core/utils/Env","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/EventTarget"],function(a,b,c,d){function e(d,f,h,i,j){var k,l=this,m=b.guid(f+"_"),n=j||"browser";d=d||{},g[m]=this,h=b.extend({access_binary:!1,access_image_binary:!1,display_media:!1,do_cors:!1,drag_and_drop:!1,filter_by_extension:!0,resize_image:!1,report_upload_progress:!1,return_response_headers:!1,return_response_type:!1,return_status_code:!0,send_custom_headers:!1,select_file:!1,select_folder:!1,select_multiple:!0,send_binary_string:!1,send_browser_cookies:!0,send_multipart:!0,slice_blob:!1,stream_upload:!1,summon_file_dialog:!1,upload_filesize:!0,use_http_method:!0},h),d.preferred_caps&&(n=e.getMode(i,d.preferred_caps,n)),MXI_DEBUG&&a.debug.runtime&&a.log("\tdefault mode: %s",n),k=function(){var a={};return{exec:function(b,c,d,e){if(k[c]&&(a[b]||(a[b]={context:this,instance:new k[c]}),a[b].instance[d]))return a[b].instance[d].apply(this,e)},removeInstance:function(b){delete a[b]},removeAllInstances:function(){var c=this;b.each(a,function(a,d){"function"===b.typeOf(a.instance.destroy)&&a.instance.destroy.call(a.context),c.removeInstance(d)})}}}(),b.extend(this,{initialized:!1,uid:m,type:f,mode:e.getMode(i,d.required_caps,n),shimid:m+"_container",clients:0,options:d,can:function(a,c){var d=arguments[2]||h;if("string"===b.typeOf(a)&&"undefined"===b.typeOf(c)&&(a=e.parseCaps(a)),"object"===b.typeOf(a)){for(var f in a)if(!this.can(f,a[f],d))return!1;return!0}return"function"===b.typeOf(d[a])?d[a].call(this,c):c===d[a]},getShimContainer:function(){var a,d=c.get(this.shimid);return d||(a=this.options.container?c.get(this.options.container):document.body,d=document.createElement("div"),d.id=this.shimid,d.className="moxie-shim moxie-shim-"+this.type,b.extend(d.style,{position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),a.appendChild(d),a=null),d},getShim:function(){return k},shimExec:function(a,b){var c=[].slice.call(arguments,2);return l.getShim().exec.call(this,this.uid,a,b,c)},exec:function(a,b){var c=[].slice.call(arguments,2);return l[a]&&l[a][b]?l[a][b].apply(this,c):l.shimExec.apply(this,arguments)},destroy:function(){if(l){var a=c.get(this.shimid);a&&a.parentNode.removeChild(a),k&&k.removeAllInstances(),this.unbindAll(),delete g[this.uid],this.uid=null,m=l=k=a=null}}}),this.mode&&d.required_caps&&!this.can(d.required_caps)&&(this.mode=!1)}var f={},g={};return e.order="html5,html4",e.getRuntime=function(a){return!!g[a]&&g[a]},e.addConstructor=function(a,b){b.prototype=d.instance,f[a]=b},e.getConstructor=function(a){return f[a]||null},e.getInfo=function(a){var b=e.getRuntime(a);return b?{uid:b.uid,type:b.type,mode:b.mode,can:function(){return b.can.apply(b,arguments)}}:null},e.parseCaps=function(a){var c={};return"string"!==b.typeOf(a)?a||{}:(b.each(a.split(","),function(a){c[a]=!0}),c)},e.can=function(a,b){var c,d,f=e.getConstructor(a);return!!f&&(c=new f({required_caps:b}),d=c.mode,c.destroy(),!!d)},e.thatCan=function(a,b){var c=(b||e.order).split(/\s*,\s*/);for(var d in c)if(e.can(c[d],a))return c[d];return null},e.getMode=function(c,d,e){var f=null;if("undefined"===b.typeOf(e)&&(e="browser"),d&&!b.isEmptyObj(c)){if(b.each(d,function(d,e){if(c.hasOwnProperty(e)){var g=c[e](d);if("string"==typeof g&&(g=[g]),f){if(!(f=b.arrayIntersect(f,g)))return MXI_DEBUG&&a.debug.runtime&&a.log("\t\t%c: %v (conflicting mode requested: %s)",e,d,g),f=!1}else f=g}MXI_DEBUG&&a.debug.runtime&&a.log("\t\t%c: %v (compatible modes: %s)",e,d,f)}),f)return b.inArray(e,f)!==-1?e:f[0];if(f===!1)return!1}return e},e.capTrue=function(){return!0},e.capFalse=function(){return!1},e.capTest=function(a){return function(){return!!a}},e}),d("moxie/runtime/RuntimeClient",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/Runtime"],function(a,b,c,d){return function(){var e;c.extend(this,{connectRuntime:function(f){function g(c){var h,j;return c.length?(h=c.shift().toLowerCase(),(j=d.getConstructor(h))?(MXI_DEBUG&&a.debug.runtime&&(a.log("Trying runtime: %s",h),a.log(f)),e=new j(f),e.bind("Init",function(){e.initialized=!0,MXI_DEBUG&&a.debug.runtime&&a.log("Runtime '%s' initialized",e.type),setTimeout(function(){e.clients++,i.trigger("RuntimeInit",e)},1)}),e.bind("Error",function(){MXI_DEBUG&&a.debug.runtime&&a.log("Runtime '%s' failed to initialize",e.type),e.destroy(),g(c)}),MXI_DEBUG&&a.debug.runtime&&a.log("\tselected mode: %s",e.mode),e.mode?void e.init():void e.trigger("Error")):void g(c)):(i.trigger("RuntimeError",new b.RuntimeError(b.RuntimeError.NOT_INIT_ERR)),void(e=null))}var h,i=this;if("string"===c.typeOf(f)?h=f:"string"===c.typeOf(f.ruid)&&(h=f.ruid),h){if(e=d.getRuntime(h))return e.clients++,e;throw new b.RuntimeError(b.RuntimeError.NOT_INIT_ERR)}g((f.runtime_order||d.order).split(/\s*,\s*/))},disconnectRuntime:function(){e&&--e.clients<=0&&e.destroy(),e=null},getRuntime:function(){return e&&e.uid?e:e=null},exec:function(){return e?e.exec.apply(this,arguments):null}})}}),d("moxie/file/FileInput",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Mime","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/I18n","moxie/runtime/Runtime","moxie/runtime/RuntimeClient"],function(a,b,c,d,e,f,g,h,i){function j(f){MXI_DEBUG&&b.log("Instantiating FileInput...");var j,l,m,n=this;if(a.inArray(a.typeOf(f),["string","node"])!==-1&&(f={browse_button:f}),l=d.get(f.browse_button),!l)throw new e.DOMException(e.DOMException.NOT_FOUND_ERR);m={accept:[{title:g.translate("All Files"),extensions:"*"}],name:"file",multiple:!1,required_caps:!1,container:l.parentNode||document.body},f=a.extend({},m,f),"string"==typeof f.required_caps&&(f.required_caps=h.parseCaps(f.required_caps)),"string"==typeof f.accept&&(f.accept=c.mimes2extList(f.accept)),j=d.get(f.container),j||(j=document.body),"static"===d.getStyle(j,"position")&&(j.style.position="relative"),j=l=null,i.call(n),a.extend(n,{uid:a.guid("uid_"),ruid:null,shimid:null,files:null,init:function(){n.bind("RuntimeInit",function(b,c){n.ruid=c.uid,n.shimid=c.shimid,n.bind("Ready",function(){n.trigger("Refresh")},999),n.bind("Refresh",function(){var b,e,g,h;g=d.get(f.browse_button),h=d.get(c.shimid),g&&(b=d.getPos(g,d.get(f.container)),e=d.getSize(g),h&&a.extend(h.style,{top:b.y+"px",left:b.x+"px",width:e.w+"px",height:e.h+"px"})),h=g=null}),c.exec.call(n,"FileInput","init",f)}),n.connectRuntime(a.extend({},f,{required_caps:{select_file:!0}}))},disable:function(b){var c=this.getRuntime();c&&c.exec.call(this,"FileInput","disable","undefined"===a.typeOf(b)||b)},refresh:function(){n.trigger("Refresh")},destroy:function(){var b=this.getRuntime();b&&(b.exec.call(this,"FileInput","destroy"),this.disconnectRuntime()),"array"===a.typeOf(this.files)&&a.each(this.files,function(a){a.destroy()}),this.files=null,this.unbindAll()}}),this.handleEventProps(k)}var k=["ready","change","cancel","mouseenter","mouseleave","mousedown","mouseup"];return j.prototype=f.instance,j}),d("moxie/core/utils/Encode",[],function(){var a=function(a){return unescape(encodeURIComponent(a))},b=function(a){return decodeURIComponent(escape(a))},c=function(a,c){if("function"==typeof window.atob)return c?b(window.atob(a)):window.atob(a);var d,e,f,g,h,i,j,k,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",m=0,n=0,o="",p=[];if(!a)return a;a+="";do g=l.indexOf(a.charAt(m++)),h=l.indexOf(a.charAt(m++)),i=l.indexOf(a.charAt(m++)),j=l.indexOf(a.charAt(m++)),k=g<<18|h<<12|i<<6|j,d=k>>16&255,e=k>>8&255,f=255&k,64==i?p[n++]=String.fromCharCode(d):64==j?p[n++]=String.fromCharCode(d,e):p[n++]=String.fromCharCode(d,e,f);while(m<a.length);return o=p.join(""),c?b(o):o},d=function(b,c){if(c&&(b=a(b)),"function"==typeof window.btoa)return window.btoa(b);var d,e,f,g,h,i,j,k,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",m=0,n=0,o="",p=[];if(!b)return b;do d=b.charCodeAt(m++),e=b.charCodeAt(m++),f=b.charCodeAt(m++),k=d<<16|e<<8|f,g=k>>18&63,h=k>>12&63,i=k>>6&63,j=63&k,p[n++]=l.charAt(g)+l.charAt(h)+l.charAt(i)+l.charAt(j);while(m<b.length);o=p.join("");var q=b.length%3;return(q?o.slice(0,q-3):o)+"===".slice(q||3)};return{utf8_encode:a,utf8_decode:b,atob:c,btoa:d}}),d("moxie/file/Blob",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient"],function(a,b,c){function d(f,g){function h(b,c,f){var g,h=e[this.uid];return"string"===a.typeOf(h)&&h.length?(g=new d(null,{type:f,size:c-b}),g.detach(h.substr(b,g.size)),g):null}c.call(this),f&&this.connectRuntime(f),g?"string"===a.typeOf(g)&&(g={data:g}):g={},a.extend(this,{uid:g.uid||a.guid("uid_"),ruid:f,size:g.size||0,type:g.type||"",slice:function(a,b,c){return this.isDetached()?h.apply(this,arguments):this.getRuntime().exec.call(this,"Blob","slice",this.getSource(),a,b,c)},getSource:function(){return e[this.uid]?e[this.uid]:null},detach:function(a){if(this.ruid&&(this.getRuntime().exec.call(this,"Blob","destroy"),this.disconnectRuntime(),this.ruid=null),a=a||"","data:"==a.substr(0,5)){var c=a.indexOf(";base64,");this.type=a.substring(5,c),a=b.atob(a.substring(c+8))}this.size=a.length,e[this.uid]=a},isDetached:function(){return!this.ruid&&"string"===a.typeOf(e[this.uid])},destroy:function(){this.detach(),delete e[this.uid]}}),g.data?this.detach(g.data):e[this.uid]=g}var e={};return d}),d("moxie/file/File",["moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/file/Blob"],function(a,b,c){function d(d,e){e||(e={}),c.apply(this,arguments),this.type||(this.type=b.getFileMime(e.name));var f;if(e.name)f=e.name.replace(/\\/g,"/"),f=f.substr(f.lastIndexOf("/")+1);else if(this.type){var g=this.type.split("/")[0];f=a.guid((""!==g?g:"file")+"_"),b.extensions[this.type]&&(f+="."+b.extensions[this.type][0])}a.extend(this,{name:f||a.guid("file_"),relativePath:"",lastModifiedDate:e.lastModifiedDate||(new Date).toLocaleString()})}return d.prototype=c.prototype,d}),d("moxie/file/FileDrop",["moxie/core/I18n","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/core/utils/Env","moxie/file/File","moxie/runtime/RuntimeClient","moxie/core/EventTarget","moxie/core/utils/Mime"],function(a,b,c,d,e,f,g,h,i){function j(c){MXI_DEBUG&&e.log("Instantiating FileDrop...");var f,h=this;"string"==typeof c&&(c={drop_zone:c}),f={accept:[{title:a.translate("All Files"),extensions:"*"}],required_caps:{drag_and_drop:!0}},c="object"==typeof c?d.extend({},f,c):f,c.container=b.get(c.drop_zone)||document.body,"static"===b.getStyle(c.container,"position")&&(c.container.style.position="relative"),"string"==typeof c.accept&&(c.accept=i.mimes2extList(c.accept)),g.call(h),d.extend(h,{uid:d.guid("uid_"),ruid:null,files:null,init:function(){h.bind("RuntimeInit",function(a,b){h.ruid=b.uid,b.exec.call(h,"FileDrop","init",c),h.dispatchEvent("ready")}),h.connectRuntime(c)},destroy:function(){var a=this.getRuntime();a&&(a.exec.call(this,"FileDrop","destroy"),this.disconnectRuntime()),this.files=null,this.unbindAll()}}),this.handleEventProps(k)}var k=["ready","dragenter","dragleave","drop","error"];return j.prototype=h.instance,j}),d("moxie/file/FileReader",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/core/Exceptions","moxie/core/EventTarget","moxie/file/Blob","moxie/runtime/RuntimeClient"],function(a,b,c,d,e,f){function g(){function d(a,d){if(this.trigger("loadstart"),this.readyState===g.LOADING)return this.trigger("error",new c.DOMException(c.DOMException.INVALID_STATE_ERR)),void this.trigger("loadend");if(!(d instanceof e))return this.trigger("error",new c.DOMException(c.DOMException.NOT_FOUND_ERR)),void this.trigger("loadend");if(this.result=null,this.readyState=g.LOADING,d.isDetached()){var f=d.getSource();switch(a){case"readAsText":case"readAsBinaryString":this.result=f;break;case"readAsDataURL":this.result="data:"+d.type+";base64,"+b.btoa(f)}this.readyState=g.DONE,this.trigger("load"),this.trigger("loadend")}else this.connectRuntime(d.ruid),this.exec("FileReader","read",a,d);
}f.call(this),a.extend(this,{uid:a.guid("uid_"),readyState:g.EMPTY,result:null,error:null,readAsBinaryString:function(a){d.call(this,"readAsBinaryString",a)},readAsDataURL:function(a){d.call(this,"readAsDataURL",a)},readAsText:function(a){d.call(this,"readAsText",a)},abort:function(){this.result=null,a.inArray(this.readyState,[g.EMPTY,g.DONE])===-1&&(this.readyState===g.LOADING&&(this.readyState=g.DONE),this.exec("FileReader","abort"),this.trigger("abort"),this.trigger("loadend"))},destroy:function(){this.abort(),this.exec("FileReader","destroy"),this.disconnectRuntime(),this.unbindAll()}}),this.handleEventProps(h),this.bind("Error",function(a,b){this.readyState=g.DONE,this.error=b},999),this.bind("Load",function(a){this.readyState=g.DONE},999)}var h=["loadstart","progress","load","abort","error","loadend"];return g.EMPTY=0,g.LOADING=1,g.DONE=2,g.prototype=d.instance,g}),d("moxie/core/utils/Url",[],function(){var a=function(b,c){for(var d=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],e=d.length,f={http:80,https:443},g={},h=/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,i=h.exec(b||"");e--;)i[e]&&(g[d[e]]=i[e]);if(!g.scheme){c&&"string"!=typeof c||(c=a(c||document.location.href)),g.scheme=c.scheme,g.host=c.host,g.port=c.port;var j="";/^[^\/]/.test(g.path)&&(j=c.path,j=/\/[^\/]*\.[^\/]*$/.test(j)?j.replace(/\/[^\/]+$/,"/"):j.replace(/\/?$/,"/")),g.path=j+(g.path||"")}return g.port||(g.port=f[g.scheme]||80),g.port=parseInt(g.port,10),g.path||(g.path="/"),delete g.source,g},b=function(b){var c={http:80,https:443},d="object"==typeof b?b:a(b);return d.scheme+"://"+d.host+(d.port!==c[d.scheme]?":"+d.port:"")+d.path+(d.query?d.query:"")},c=function(b){function c(a){return[a.scheme,a.host,a.port].join("/")}return"string"==typeof b&&(b=a(b)),c(a())===c(b)};return{parseUrl:a,resolveUrl:b,hasSameOrigin:c}}),d("moxie/runtime/RuntimeTarget",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(a,b,c){function d(){this.uid=a.guid("uid_"),b.call(this),this.destroy=function(){this.disconnectRuntime(),this.unbindAll()}}return d.prototype=c.instance,d}),d("moxie/file/FileReaderSync",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/utils/Encode"],function(a,b,c){return function(){function d(a,b){if(!b.isDetached()){var d=this.connectRuntime(b.ruid).exec.call(this,"FileReaderSync","read",a,b);return this.disconnectRuntime(),d}var e=b.getSource();switch(a){case"readAsBinaryString":return e;case"readAsDataURL":return"data:"+b.type+";base64,"+c.btoa(e);case"readAsText":for(var f="",g=0,h=e.length;g<h;g++)f+=String.fromCharCode(e[g]);return f}}b.call(this),a.extend(this,{uid:a.guid("uid_"),readAsBinaryString:function(a){return d.call(this,"readAsBinaryString",a)},readAsDataURL:function(a){return d.call(this,"readAsDataURL",a)},readAsText:function(a){return d.call(this,"readAsText",a)}})}}),d("moxie/xhr/FormData",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/file/Blob"],function(a,b,c){function d(){var a,d=[];b.extend(this,{append:function(e,f){var g=this,h=b.typeOf(f);f instanceof c?a={name:e,value:f}:"array"===h?(e+="[]",b.each(f,function(a){g.append(e,a)})):"object"===h?b.each(f,function(a,b){g.append(e+"["+b+"]",a)}):"null"===h||"undefined"===h||"number"===h&&isNaN(f)?g.append(e,"false"):d.push({name:e,value:f.toString()})},hasBlob:function(){return!!this.getBlob()},getBlob:function(){return a&&a.value||null},getBlobName:function(){return a&&a.name||null},each:function(c){b.each(d,function(a){c(a.value,a.name)}),a&&c(a.value,a.name)},destroy:function(){a=null,d=[]}})}return d}),d("moxie/xhr/XMLHttpRequest",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/utils/Encode","moxie/core/utils/Url","moxie/runtime/Runtime","moxie/runtime/RuntimeTarget","moxie/file/Blob","moxie/file/FileReaderSync","moxie/xhr/FormData","moxie/core/utils/Env","moxie/core/utils/Mime"],function(a,b,c,d,e,f,g,h,i,j,k,l){function m(){this.uid=a.guid("uid_")}function n(){function c(a,b){if(A.hasOwnProperty(a))return 1===arguments.length?k.can("define_property")?A[a]:z[a]:void(k.can("define_property")?A[a]=b:z[a]=b)}function i(b){function d(){x&&(x.destroy(),x=null),h.dispatchEvent("loadend"),h=null}function e(e){x.bind("LoadStart",function(a){c("readyState",n.LOADING),h.dispatchEvent("readystatechange"),h.dispatchEvent(a),H&&h.upload.dispatchEvent(a)}),x.bind("Progress",function(a){c("readyState")!==n.LOADING&&(c("readyState",n.LOADING),h.dispatchEvent("readystatechange")),h.dispatchEvent(a)}),x.bind("UploadProgress",function(a){H&&h.upload.dispatchEvent({type:"progress",lengthComputable:!1,total:a.total,loaded:a.loaded})}),x.bind("Load",function(b){c("readyState",n.DONE),c("status",Number(e.exec.call(x,"XMLHttpRequest","getStatus")||0)),c("statusText",o[c("status")]||""),c("response",e.exec.call(x,"XMLHttpRequest","getResponse",c("responseType"))),~a.inArray(c("responseType"),["text",""])?c("responseText",c("response")):"document"===c("responseType")&&c("responseXML",c("response")),O=e.exec.call(x,"XMLHttpRequest","getAllResponseHeaders"),h.dispatchEvent("readystatechange"),c("status")>0?(H&&h.upload.dispatchEvent(b),h.dispatchEvent(b)):(J=!0,h.dispatchEvent("error")),d()}),x.bind("Abort",function(a){h.dispatchEvent(a),d()}),x.bind("Error",function(a){J=!0,c("readyState",n.DONE),h.dispatchEvent("readystatechange"),I=!0,h.dispatchEvent(a),d()}),e.exec.call(x,"XMLHttpRequest","send",{url:r,method:s,async:B,user:t,password:u,headers:C,mimeType:E,encoding:D,responseType:h.responseType,withCredentials:h.withCredentials,options:N},b)}var h=this;v=(new Date).getTime(),x=new g,"string"==typeof N.required_caps&&(N.required_caps=f.parseCaps(N.required_caps)),N.required_caps=a.extend({},N.required_caps,{return_response_type:h.responseType}),b instanceof j&&(N.required_caps.send_multipart=!0),a.isEmptyObj(C)||(N.required_caps.send_custom_headers=!0),K||(N.required_caps.do_cors=!0),N.ruid?e(x.connectRuntime(N)):(x.bind("RuntimeInit",function(a,b){e(b)}),x.bind("RuntimeError",function(a,b){h.dispatchEvent("RuntimeError",b)}),x.connectRuntime(N))}function q(){c("responseText",""),c("responseXML",null),c("response",null),c("status",0),c("statusText",""),v=w=null}var r,s,t,u,v,w,x,y,z=this,A={timeout:0,readyState:n.UNSENT,withCredentials:!1,status:0,statusText:"",responseType:"",responseXML:null,responseText:null,response:null},B=!0,C={},D=null,E=null,F=!1,G=!1,H=!1,I=!1,J=!1,K=!1,L=null,M=null,N={},O="";a.extend(this,A,{uid:a.guid("uid_"),upload:new m,open:function(f,g,h,i,j){var k;if(!f||!g)throw new b.DOMException(b.DOMException.SYNTAX_ERR);if(/[\u0100-\uffff]/.test(f)||d.utf8_encode(f)!==f)throw new b.DOMException(b.DOMException.SYNTAX_ERR);if(~a.inArray(f.toUpperCase(),["CONNECT","DELETE","GET","HEAD","OPTIONS","POST","PUT","TRACE","TRACK"])&&(s=f.toUpperCase()),~a.inArray(s,["CONNECT","TRACE","TRACK"]))throw new b.DOMException(b.DOMException.SECURITY_ERR);if(g=d.utf8_encode(g),k=e.parseUrl(g),K=e.hasSameOrigin(k),r=e.resolveUrl(g),(i||j)&&!K)throw new b.DOMException(b.DOMException.INVALID_ACCESS_ERR);if(t=i||k.user,u=j||k.pass,B=h||!0,B===!1&&(c("timeout")||c("withCredentials")||""!==c("responseType")))throw new b.DOMException(b.DOMException.INVALID_ACCESS_ERR);F=!B,G=!1,C={},q.call(this),c("readyState",n.OPENED),this.dispatchEvent("readystatechange")},setRequestHeader:function(e,f){var g=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","content-transfer-encoding","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"];if(c("readyState")!==n.OPENED||G)throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);if(/[\u0100-\uffff]/.test(e)||d.utf8_encode(e)!==e)throw new b.DOMException(b.DOMException.SYNTAX_ERR);return e=a.trim(e).toLowerCase(),!~a.inArray(e,g)&&!/^(proxy\-|sec\-)/.test(e)&&(C[e]?C[e]+=", "+f:C[e]=f,!0)},getAllResponseHeaders:function(){return O||""},getResponseHeader:function(b){return b=b.toLowerCase(),J||~a.inArray(b,["set-cookie","set-cookie2"])?null:O&&""!==O&&(y||(y={},a.each(O.split(/\r\n/),function(b){var c=b.split(/:\s+/);2===c.length&&(c[0]=a.trim(c[0]),y[c[0].toLowerCase()]={header:c[0],value:a.trim(c[1])})})),y.hasOwnProperty(b))?y[b].header+": "+y[b].value:null},overrideMimeType:function(d){var e,f;if(~a.inArray(c("readyState"),[n.LOADING,n.DONE]))throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);if(d=a.trim(d.toLowerCase()),/;/.test(d)&&(e=d.match(/^([^;]+)(?:;\scharset\=)?(.*)$/))&&(d=e[1],e[2]&&(f=e[2])),!l.mimes[d])throw new b.DOMException(b.DOMException.SYNTAX_ERR);L=d,M=f},send:function(c,e){if(N="string"===a.typeOf(e)?{ruid:e}:e?e:{},this.readyState!==n.OPENED||G)throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);if(c instanceof h)N.ruid=c.ruid,E=c.type||"application/octet-stream";else if(c instanceof j){if(c.hasBlob()){var f=c.getBlob();N.ruid=f.ruid,E=f.type||"application/octet-stream"}}else"string"==typeof c&&(D="UTF-8",E="text/plain;charset=UTF-8",c=d.utf8_encode(c));this.withCredentials||(this.withCredentials=N.required_caps&&N.required_caps.send_browser_cookies&&!K),H=!F&&this.upload.hasEventListener(),J=!1,I=!c,F||(G=!0),i.call(this,c)},abort:function(){if(J=!0,F=!1,~a.inArray(c("readyState"),[n.UNSENT,n.OPENED,n.DONE]))c("readyState",n.UNSENT);else{if(c("readyState",n.DONE),G=!1,!x)throw new b.DOMException(b.DOMException.INVALID_STATE_ERR);x.getRuntime().exec.call(x,"XMLHttpRequest","abort",I),I=!0}},destroy:function(){x&&("function"===a.typeOf(x.destroy)&&x.destroy(),x=null),this.unbindAll(),this.upload&&(this.upload.unbindAll(),this.upload=null)}}),this.handleEventProps(p.concat(["readystatechange"])),this.upload.handleEventProps(p)}var o={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Reserved",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",426:"Upgrade Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",510:"Not Extended"};m.prototype=c.instance;var p=["loadstart","progress","abort","error","load","timeout","loadend"];return n.UNSENT=0,n.OPENED=1,n.HEADERS_RECEIVED=2,n.LOADING=3,n.DONE=4,n.prototype=c.instance,n}),d("moxie/runtime/Transporter",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(a,b,c,d){function e(){function d(){k=l=0,j=this.result=null}function f(b,c){var d=this;i=c,d.bind("TransportingProgress",function(b){l=b.loaded,l<k&&a.inArray(d.state,[e.IDLE,e.DONE])===-1&&g.call(d)},999),d.bind("TransportingComplete",function(){l=k,d.state=e.DONE,j=null,d.result=i.exec.call(d,"Transporter","getAsBlob",b||"")},999),d.state=e.BUSY,d.trigger("TransportingStarted"),g.call(d)}function g(){var a,c=this,d=k-l;m>d&&(m=d),a=b.btoa(j.substr(l,m)),i.exec.call(c,"Transporter","receive",a,k)}var h,i,j,k,l,m;c.call(this),a.extend(this,{uid:a.guid("uid_"),state:e.IDLE,result:null,transport:function(b,c,e){var g=this;if(e=a.extend({chunk_size:204798},e),(h=e.chunk_size%3)&&(e.chunk_size+=3-h),m=e.chunk_size,d.call(this),j=b,k=b.length,"string"===a.typeOf(e)||e.ruid)f.call(g,c,this.connectRuntime(e));else{var i=function(a,b){g.unbind("RuntimeInit",i),f.call(g,c,b)};this.bind("RuntimeInit",i),this.connectRuntime(e)}},abort:function(){var a=this;a.state=e.IDLE,i&&(i.exec.call(a,"Transporter","clear"),a.trigger("TransportingAborted")),d.call(a)},destroy:function(){this.unbindAll(),i=null,this.disconnectRuntime(),d.call(this)}})}return e.IDLE=0,e.BUSY=1,e.DONE=2,e.prototype=d.instance,e}),d("moxie/image/Image",["moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/file/FileReaderSync","moxie/xhr/XMLHttpRequest","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/runtime/Transporter","moxie/core/utils/Env","moxie/core/EventTarget","moxie/file/Blob","moxie/file/File","moxie/core/utils/Encode"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){function n(){function d(a){a||(a=this.exec("Image","getInfo")),this.size=a.size,this.width=a.width,this.height=a.height,this.type=a.type,this.meta=a.meta,""===this.name&&(this.name=a.name)}function j(b){var d=a.typeOf(b);try{if(b instanceof n){if(!b.size)throw new c.DOMException(c.DOMException.INVALID_STATE_ERR);p.apply(this,arguments)}else if(b instanceof k){if(!~a.inArray(b.type,["image/jpeg","image/png"]))throw new c.ImageError(c.ImageError.WRONG_FORMAT);q.apply(this,arguments)}else if(a.inArray(d,["blob","file"])!==-1)j.call(this,new l(null,b),arguments[1]);else if("string"===d)"data:"===b.substr(0,5)?j.call(this,new k(null,{data:b}),arguments[1]):r.apply(this,arguments);else{if("node"!==d||"img"!==b.nodeName.toLowerCase())throw new c.DOMException(c.DOMException.TYPE_MISMATCH_ERR);j.call(this,b.src,arguments[1])}}catch(e){this.trigger("error",e.code)}}function p(b,c){var d=this.connectRuntime(b.ruid);this.ruid=d.uid,d.exec.call(this,"Image","loadFromImage",b,"undefined"===a.typeOf(c)||c)}function q(b,c){function d(a){e.ruid=a.uid,a.exec.call(e,"Image","loadFromBlob",b)}var e=this;e.name=b.name||"",b.isDetached()?(this.bind("RuntimeInit",function(a,b){d(b)}),c&&"string"==typeof c.required_caps&&(c.required_caps=f.parseCaps(c.required_caps)),this.connectRuntime(a.extend({required_caps:{access_image_binary:!0,resize_image:!0}},c))):d(this.connectRuntime(b.ruid))}function r(a,b){var c,d=this;c=new e,c.open("get",a),c.responseType="blob",c.onprogress=function(a){d.trigger(a)},c.onload=function(){q.call(d,c.response,!0)},c.onerror=function(a){d.trigger(a)},c.onloadend=function(){c.destroy()},c.bind("RuntimeError",function(a,b){d.trigger("RuntimeError",b)}),c.send(null,b)}g.call(this),a.extend(this,{uid:a.guid("uid_"),ruid:null,name:"",size:0,width:0,height:0,type:"",meta:{},clone:function(){this.load.apply(this,arguments)},load:function(){j.apply(this,arguments)},downsize:function(b){var d={width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90,crop:!1,preserveHeaders:!0,resample:!1};b="object"==typeof b?a.extend(d,b):a.extend(d,{width:arguments[0],height:arguments[1],crop:arguments[2],preserveHeaders:arguments[3]});try{if(!this.size)throw new c.DOMException(c.DOMException.INVALID_STATE_ERR);if(this.width>n.MAX_RESIZE_WIDTH||this.height>n.MAX_RESIZE_HEIGHT)throw new c.ImageError(c.ImageError.MAX_RESOLUTION_ERR);this.exec("Image","downsize",b.width,b.height,b.crop,b.preserveHeaders)}catch(e){this.trigger("error",e.code)}},crop:function(a,b,c){this.downsize(a,b,!0,c)},getAsCanvas:function(){if(!i.can("create_canvas"))throw new c.RuntimeError(c.RuntimeError.NOT_SUPPORTED_ERR);var a=this.connectRuntime(this.ruid);return a.exec.call(this,"Image","getAsCanvas")},getAsBlob:function(a,b){if(!this.size)throw new c.DOMException(c.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsBlob",a||"image/jpeg",b||90)},getAsDataURL:function(a,b){if(!this.size)throw new c.DOMException(c.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsDataURL",a||"image/jpeg",b||90)},getAsBinaryString:function(a,b){var c=this.getAsDataURL(a,b);return m.atob(c.substring(c.indexOf("base64,")+7))},embed:function(d,e){function f(b,e){var f=this;if(i.can("create_canvas")){var k=f.getAsCanvas();if(k)return d.appendChild(k),k=null,f.destroy(),void j.trigger("embedded")}var l=f.getAsDataURL(b,e);if(!l)throw new c.ImageError(c.ImageError.WRONG_FORMAT);if(i.can("use_data_uri_of",l.length))d.innerHTML='<img src="'+l+'" width="'+f.width+'" height="'+f.height+'" />',f.destroy(),j.trigger("embedded");else{var n=new h;n.bind("TransportingComplete",function(){g=j.connectRuntime(this.result.ruid),j.bind("Embedded",function(){a.extend(g.getShimContainer().style,{top:"0px",left:"0px",width:f.width+"px",height:f.height+"px"}),g=null},999),g.exec.call(j,"ImageView","display",this.result.uid,width,height),f.destroy()}),n.transport(m.atob(l.substring(l.indexOf("base64,")+7)),b,{required_caps:{display_media:!0},runtime_order:"flash,silverlight",container:d})}}var g,j=this;e=a.extend({width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90},e||{});try{if(!(d=b.get(d)))throw new c.DOMException(c.DOMException.INVALID_NODE_TYPE_ERR);if(!this.size)throw new c.DOMException(c.DOMException.INVALID_STATE_ERR);this.width>n.MAX_RESIZE_WIDTH||this.height>n.MAX_RESIZE_HEIGHT;var k=new n;return k.bind("Resize",function(){f.call(this,e.type,e.quality)}),k.bind("Load",function(){k.downsize(e)}),this.meta.thumb&&this.meta.thumb.width>=e.width&&this.meta.thumb.height>=e.height?k.load(this.meta.thumb.data):k.clone(this,!1),k}catch(l){this.trigger("error",l.code)}},destroy:function(){this.ruid&&(this.getRuntime().exec.call(this,"Image","destroy"),this.disconnectRuntime()),this.unbindAll()}}),this.handleEventProps(o),this.bind("Load Resize",function(){d.call(this)},999)}var o=["progress","load","error","resize","embedded"];return n.MAX_RESIZE_WIDTH=8192,n.MAX_RESIZE_HEIGHT=8192,n.prototype=j.instance,n}),d("moxie/runtime/html5/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(a,b,c,d){function e(b){var e=this,h=c.capTest,i=c.capTrue,j=a.extend({access_binary:h(window.FileReader||window.File&&window.File.getAsDataURL),access_image_binary:function(){return e.can("access_binary")&&!!g.Image},display_media:h(d.can("create_canvas")||d.can("use_data_uri_over32kb")),do_cors:h(window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest),drag_and_drop:h(function(){var a=document.createElement("div");return("draggable"in a||"ondragstart"in a&&"ondrop"in a)&&("IE"!==d.browser||d.verComp(d.version,9,">"))}()),filter_by_extension:h(function(){return"Chrome"===d.browser&&d.verComp(d.version,28,">=")||"IE"===d.browser&&d.verComp(d.version,10,">=")||"Safari"===d.browser&&d.verComp(d.version,7,">=")}()),return_response_headers:i,return_response_type:function(a){return!("json"!==a||!window.JSON)||d.can("return_response_type",a)},return_status_code:i,report_upload_progress:h(window.XMLHttpRequest&&(new XMLHttpRequest).upload),resize_image:function(){return e.can("access_binary")&&d.can("create_canvas")},select_file:function(){return d.can("use_fileinput")&&window.File},select_folder:function(){return e.can("select_file")&&"Chrome"===d.browser&&d.verComp(d.version,21,">=")},select_multiple:function(){return e.can("select_file")&&!("Safari"===d.browser&&"Windows"===d.os)&&!("iOS"===d.os&&d.verComp(d.osVersion,"7.0.0",">")&&d.verComp(d.osVersion,"8.0.0","<"))},send_binary_string:h(window.XMLHttpRequest&&((new XMLHttpRequest).sendAsBinary||window.Uint8Array&&window.ArrayBuffer)),send_custom_headers:h(window.XMLHttpRequest),send_multipart:function(){return!!(window.XMLHttpRequest&&(new XMLHttpRequest).upload&&window.FormData)||e.can("send_binary_string")},slice_blob:h(window.File&&(File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice)),stream_upload:function(){return e.can("slice_blob")&&e.can("send_multipart")},summon_file_dialog:function(){return e.can("select_file")&&("Firefox"===d.browser&&d.verComp(d.version,4,">=")||"Opera"===d.browser&&d.verComp(d.version,12,">=")||"IE"===d.browser&&d.verComp(d.version,10,">=")||!!~a.inArray(d.browser,["Chrome","Safari"]))},upload_filesize:i},arguments[2]);c.call(this,b,arguments[1]||f,j),a.extend(this,{init:function(){this.trigger("Init")},destroy:function(a){return function(){a.call(e),a=e=null}}(this.destroy)}),a.extend(this.getShim(),g)}var f="html5",g={};return c.addConstructor(f,e),g}),d("moxie/core/utils/Events",["moxie/core/utils/Basic"],function(a){function b(){this.returnValue=!1}function c(){this.cancelBubble=!0}var d={},e="moxie_"+a.guid(),f=function(f,g,h,i){var j,k;g=g.toLowerCase(),f.addEventListener?(j=h,f.addEventListener(g,j,!1)):f.attachEvent&&(j=function(){var a=window.event;a.target||(a.target=a.srcElement),a.preventDefault=b,a.stopPropagation=c,h(a)},f.attachEvent("on"+g,j)),f[e]||(f[e]=a.guid()),d.hasOwnProperty(f[e])||(d[f[e]]={}),k=d[f[e]],k.hasOwnProperty(g)||(k[g]=[]),k[g].push({func:j,orig:h,key:i})},g=function(b,c,f){var g,h;if(c=c.toLowerCase(),b[e]&&d[b[e]]&&d[b[e]][c]){g=d[b[e]][c];for(var i=g.length-1;i>=0&&(g[i].orig!==f&&g[i].key!==f||(b.removeEventListener?b.removeEventListener(c,g[i].func,!1):b.detachEvent&&b.detachEvent("on"+c,g[i].func),g[i].orig=null,g[i].func=null,g.splice(i,1),f===h));i--);if(g.length||delete d[b[e]][c],a.isEmptyObj(d[b[e]])){delete d[b[e]];try{delete b[e]}catch(j){b[e]=h}}}},h=function(b,c){b&&b[e]&&a.each(d[b[e]],function(a,d){g(b,d,c)})};return{addEvent:f,removeEvent:g,removeAllEvents:h}}),d("moxie/runtime/html5/file/FileInput",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(a,b,c,d,e,f,g){function h(){var a;c.extend(this,{init:function(h){var i,j,k,l,m,n,o=this,p=o.getRuntime();a=h,k=a.accept.mimes||f.extList2mimes(a.accept,p.can("filter_by_extension")),j=p.getShimContainer(),j.innerHTML='<input id="'+p.uid+'" type="file" style="font-size:999px;opacity:0;"'+(a.multiple&&p.can("select_multiple")?"multiple":"")+(a.directory&&p.can("select_folder")?"webkitdirectory directory":"")+(k?' accept="'+k.join(",")+'"':"")+" />",i=d.get(p.uid),c.extend(i.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),l=d.get(a.browse_button),p.can("summon_file_dialog")&&("static"===d.getStyle(l,"position")&&(l.style.position="relative"),m=parseInt(d.getStyle(l,"z-index"),10)||1,l.style.zIndex=m,j.style.zIndex=m-1,e.addEvent(l,"click",function(a){var b=d.get(p.uid);b&&!b.disabled&&b.click(),a.preventDefault()},o.uid)),n=p.can("summon_file_dialog")?l:j,e.addEvent(n,"mouseover",function(){o.trigger("mouseenter")},o.uid),e.addEvent(n,"mouseout",function(){o.trigger("mouseleave")},o.uid),e.addEvent(n,"mousedown",function(){o.trigger("mousedown")},o.uid),e.addEvent(d.get(a.container),"mouseup",function(){o.trigger("mouseup")},o.uid),i.onchange=function q(d){if(o.files=[],c.each(this.files,function(c){var d="";return!(!a.directory||"."!=c.name)||(c.webkitRelativePath&&(d="/"+c.webkitRelativePath.replace(/^\//,"")),c=new b(p.uid,c),c.relativePath=d,void o.files.push(c))}),"IE"!==g.browser&&"IEMobile"!==g.browser)this.value="";else{var e=this.cloneNode(!0);this.parentNode.replaceChild(e,this),e.onchange=q}o.files.length&&o.trigger("change")},o.trigger({type:"ready",async:!0}),j=null},disable:function(a){var b,c=this.getRuntime();(b=d.get(c.uid))&&(b.disabled=!!a)},destroy:function(){var b=this.getRuntime(),c=b.getShim(),f=b.getShimContainer();e.removeAllEvents(f,this.uid),e.removeAllEvents(a&&d.get(a.container),this.uid),e.removeAllEvents(a&&d.get(a.browse_button),this.uid),f&&(f.innerHTML=""),c.removeInstance(this.uid),a=f=c=null}})}return a.FileInput=h}),d("moxie/runtime/html5/file/Blob",["moxie/runtime/html5/Runtime","moxie/file/Blob"],function(a,b){function c(){function a(a,b,c){var d;if(!window.File.prototype.slice)return(d=window.File.prototype.webkitSlice||window.File.prototype.mozSlice)?d.call(a,b,c):null;try{return a.slice(),a.slice(b,c)}catch(e){return a.slice(b,c-b)}}this.slice=function(){return new b(this.getRuntime().uid,a.apply(this,arguments))}}return a.Blob=c}),d("moxie/runtime/html5/file/FileDrop",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime"],function(a,b,c,d,e,f){function g(){function a(a){if(!a.dataTransfer||!a.dataTransfer.types)return!1;var b=c.toArray(a.dataTransfer.types||[]);return c.inArray("Files",b)!==-1||c.inArray("public.file-url",b)!==-1||c.inArray("application/x-moz-file",b)!==-1}function g(a,c){if(i(a)){var d=new b(o,a);d.relativePath=c||"",p.push(d)}}function h(a){for(var b=[],d=0;d<a.length;d++)[].push.apply(b,a[d].extensions.split(/\s*,\s*/));return c.inArray("*",b)===-1?b:[]}function i(a){if(!q.length)return!0;var b=f.getFileExtension(a.name);return!b||c.inArray(b,q)!==-1}function j(a,b){var d=[];c.each(a,function(a){var b=a.webkitGetAsEntry();b&&(b.isFile?g(a.getAsFile(),b.fullPath):d.push(b))}),d.length?k(d,b):b()}function k(a,b){var d=[];c.each(a,function(a){d.push(function(b){l(a,b)})}),c.inSeries(d,function(){b()})}function l(a,b){a.isFile?a.file(function(c){g(c,a.fullPath),b()},function(){b()}):a.isDirectory?m(a,b):b()}function m(a,b){function c(a){e.readEntries(function(b){b.length?([].push.apply(d,b),c(a)):a()},a)}var d=[],e=a.createReader();c(function(){k(d,b)})}var n,o,p=[],q=[];c.extend(this,{init:function(b){var d,f=this;n=b,o=f.ruid,q=h(n.accept),d=n.container,e.addEvent(d,"dragover",function(b){a(b)&&(b.preventDefault(),b.dataTransfer.dropEffect="copy")},f.uid),e.addEvent(d,"drop",function(b){a(b)&&(b.preventDefault(),p=[],b.dataTransfer.items&&b.dataTransfer.items[0].webkitGetAsEntry?j(b.dataTransfer.items,function(){f.files=p,f.trigger("drop")}):(c.each(b.dataTransfer.files,function(a){g(a)}),f.files=p,f.trigger("drop")))},f.uid),e.addEvent(d,"dragenter",function(a){f.trigger("dragenter")},f.uid),e.addEvent(d,"dragleave",function(a){f.trigger("dragleave")},f.uid)},destroy:function(){e.removeAllEvents(n&&d.get(n.container),this.uid),o=p=q=n=null}})}return a.FileDrop=g}),d("moxie/runtime/html5/file/FileReader",["moxie/runtime/html5/Runtime","moxie/core/utils/Encode","moxie/core/utils/Basic"],function(a,b,c){function d(){function a(a){return b.atob(a.substring(a.indexOf("base64,")+7))}var d,e=!1;c.extend(this,{read:function(b,f){var g=this;g.result="",d=new window.FileReader,d.addEventListener("progress",function(a){g.trigger(a)}),d.addEventListener("load",function(b){g.result=e?a(d.result):d.result,g.trigger(b)}),d.addEventListener("error",function(a){g.trigger(a,d.error)}),d.addEventListener("loadend",function(a){d=null,g.trigger(a)}),"function"===c.typeOf(d[b])?(e=!1,d[b](f.getSource())):"readAsBinaryString"===b&&(e=!0,d.readAsDataURL(f.getSource()))},abort:function(){d&&d.abort()},destroy:function(){d=null}})}return a.FileReader=d}),d("moxie/runtime/html5/xhr/XMLHttpRequest",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/core/utils/Url","moxie/file/File","moxie/file/Blob","moxie/xhr/FormData","moxie/core/Exceptions","moxie/core/utils/Env"],function(a,b,c,d,e,f,g,h,i){function j(){function a(a,b){var c,d,e=this;c=b.getBlob().getSource(),d=new window.FileReader,d.onload=function(){b.append(b.getBlobName(),new f(null,{type:c.type,data:d.result})),o.send.call(e,a,b)},d.readAsBinaryString(c)}function j(){return!window.XMLHttpRequest||"IE"===i.browser&&i.verComp(i.version,8,"<")?function(){for(var a=["Msxml2.XMLHTTP.6.0","Microsoft.XMLHTTP"],b=0;b<a.length;b++)try{return new ActiveXObject(a[b])}catch(c){}}():new window.XMLHttpRequest}function k(a){var b=a.responseXML,c=a.responseText;return"IE"===i.browser&&c&&b&&!b.documentElement&&/[^\/]+\/[^\+]+\+xml/.test(a.getResponseHeader("Content-Type"))&&(b=new window.ActiveXObject("Microsoft.XMLDOM"),b.async=!1,b.validateOnParse=!1,b.loadXML(c)),b&&("IE"===i.browser&&0!==b.parseError||!b.documentElement||"parsererror"===b.documentElement.tagName)?null:b}function l(a){var b="----moxieboundary"+(new Date).getTime(),c="--",d="\r\n",e="",g=this.getRuntime();if(!g.can("send_binary_string"))throw new h.RuntimeError(h.RuntimeError.NOT_SUPPORTED_ERR);return m.setRequestHeader("Content-Type","multipart/form-data; boundary="+b),a.each(function(a,g){e+=a instanceof f?c+b+d+'Content-Disposition: form-data; name="'+g+'"; filename="'+unescape(encodeURIComponent(a.name||"blob"))+'"'+d+"Content-Type: "+(a.type||"application/octet-stream")+d+d+a.getSource()+d:c+b+d+'Content-Disposition: form-data; name="'+g+'"'+d+d+unescape(encodeURIComponent(a))+d}),e+=c+b+c+d}var m,n,o=this;b.extend(this,{send:function(c,e){var h=this,k="Mozilla"===i.browser&&i.verComp(i.version,4,">=")&&i.verComp(i.version,7,"<"),o="Android Browser"===i.browser,p=!1;if(n=c.url.replace(/^.+?\/([\w\-\.]+)$/,"$1").toLowerCase(),m=j(),m.open(c.method,c.url,c.async,c.user,c.password),e instanceof f)e.isDetached()&&(p=!0),e=e.getSource();else if(e instanceof g){if(e.hasBlob())if(e.getBlob().isDetached())e=l.call(h,e),p=!0;else if((k||o)&&"blob"===b.typeOf(e.getBlob().getSource())&&window.FileReader)return void a.call(h,c,e);if(e instanceof g){var q=new window.FormData;e.each(function(a,b){a instanceof f?q.append(b,a.getSource()):q.append(b,a)}),e=q}}m.upload?(c.withCredentials&&(m.withCredentials=!0),m.addEventListener("load",function(a){h.trigger(a)}),m.addEventListener("error",function(a){h.trigger(a)}),m.addEventListener("progress",function(a){h.trigger(a)}),m.upload.addEventListener("progress",function(a){h.trigger({type:"UploadProgress",loaded:a.loaded,total:a.total})})):m.onreadystatechange=function(){switch(m.readyState){case 1:break;case 2:break;case 3:var a,b;try{d.hasSameOrigin(c.url)&&(a=m.getResponseHeader("Content-Length")||0),m.responseText&&(b=m.responseText.length)}catch(e){a=b=0}h.trigger({type:"progress",lengthComputable:!!a,total:parseInt(a,10),loaded:b});break;case 4:m.onreadystatechange=function(){},0===m.status?h.trigger("error"):h.trigger("load")}},b.isEmptyObj(c.headers)||b.each(c.headers,function(a,b){m.setRequestHeader(b,a)}),""!==c.responseType&&"responseType"in m&&("json"!==c.responseType||i.can("return_response_type","json")?m.responseType=c.responseType:m.responseType="text"),p?m.sendAsBinary?m.sendAsBinary(e):!function(){for(var a=new Uint8Array(e.length),b=0;b<e.length;b++)a[b]=255&e.charCodeAt(b);m.send(a.buffer)}():m.send(e),h.trigger("loadstart")},getStatus:function(){try{if(m)return m.status}catch(a){}return 0},getResponse:function(a){var b=this.getRuntime();try{switch(a){case"blob":var d=new e(b.uid,m.response),f=m.getResponseHeader("Content-Disposition");if(f){var g=f.match(/filename=([\'\"'])([^\1]+)\1/);g&&(n=g[2])}return d.name=n,d.type||(d.type=c.getFileMime(n)),d;case"json":return i.can("return_response_type","json")?m.response:200===m.status&&window.JSON?JSON.parse(m.responseText):null;case"document":return k(m);default:return""!==m.responseText?m.responseText:null}}catch(h){return null}},getAllResponseHeaders:function(){try{return m.getAllResponseHeaders()}catch(a){}return""},abort:function(){m&&m.abort()},destroy:function(){o=n=null}})}return a.XMLHttpRequest=j}),d("moxie/runtime/html5/utils/BinaryReader",["moxie/core/utils/Basic"],function(a){function b(a){a instanceof ArrayBuffer?c.apply(this,arguments):d.apply(this,arguments)}function c(b){var c=new DataView(b);a.extend(this,{readByteAt:function(a){return c.getUint8(a)},writeByteAt:function(a,b){c.setUint8(a,b)},SEGMENT:function(a,d,e){switch(arguments.length){case 2:return b.slice(a,a+d);case 1:return b.slice(a);case 3:if(null===e&&(e=new ArrayBuffer),e instanceof ArrayBuffer){var f=new Uint8Array(this.length()-d+e.byteLength);a>0&&f.set(new Uint8Array(b.slice(0,a)),0),f.set(new Uint8Array(e),a),f.set(new Uint8Array(b.slice(a+d)),a+e.byteLength),this.clear(),b=f.buffer,c=new DataView(b);
break}default:return b}},length:function(){return b?b.byteLength:0},clear:function(){c=b=null}})}function d(b){function c(a,c,d){d=3===arguments.length?d:b.length-c-1,b=b.substr(0,c)+a+b.substr(d+c)}a.extend(this,{readByteAt:function(a){return b.charCodeAt(a)},writeByteAt:function(a,b){c(String.fromCharCode(b),a,1)},SEGMENT:function(a,d,e){switch(arguments.length){case 1:return b.substr(a);case 2:return b.substr(a,d);case 3:c(null!==e?e:"",a,d);break;default:return b}},length:function(){return b?b.length:0},clear:function(){b=null}})}return a.extend(b.prototype,{littleEndian:!1,read:function(a,b){var c,d,e;if(a+b>this.length())throw new Error("You are trying to read outside the source boundaries.");for(d=this.littleEndian?0:-8*(b-1),e=0,c=0;e<b;e++)c|=this.readByteAt(a+e)<<Math.abs(d+8*e);return c},write:function(a,b,c){var d,e;if(a>this.length())throw new Error("You are trying to write outside the source boundaries.");for(d=this.littleEndian?0:-8*(c-1),e=0;e<c;e++)this.writeByteAt(a+e,b>>Math.abs(d+8*e)&255)},BYTE:function(a){return this.read(a,1)},SHORT:function(a){return this.read(a,2)},LONG:function(a){return this.read(a,4)},SLONG:function(a){var b=this.read(a,4);return b>2147483647?b-4294967296:b},CHAR:function(a){return String.fromCharCode(this.read(a,1))},STRING:function(a,b){return this.asArray("CHAR",a,b).join("")},asArray:function(a,b,c){for(var d=[],e=0;e<c;e++)d[e]=this[a](b+e);return d}}),b}),d("moxie/runtime/html5/image/JPEGHeaders",["moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(a,b){return function c(d){var e,f,g,h=[],i=0;if(e=new a(d),65496!==e.SHORT(0))throw e.clear(),new b.ImageError(b.ImageError.WRONG_FORMAT);for(f=2;f<=e.length();)if(g=e.SHORT(f),g>=65488&&g<=65495)f+=2;else{if(65498===g||65497===g)break;i=e.SHORT(f+2)+2,g>=65505&&g<=65519&&h.push({hex:g,name:"APP"+(15&g),start:f,length:i,segment:e.SEGMENT(f,i)}),f+=i}return e.clear(),{headers:h,restore:function(b){var c,d,e;for(e=new a(b),f=65504==e.SHORT(2)?4+e.SHORT(4):2,d=0,c=h.length;d<c;d++)e.SEGMENT(f,0,h[d].segment),f+=h[d].length;return b=e.SEGMENT(),e.clear(),b},strip:function(b){var d,e,f,g;for(f=new c(b),e=f.headers,f.purge(),d=new a(b),g=e.length;g--;)d.SEGMENT(e[g].start,e[g].length,"");return b=d.SEGMENT(),d.clear(),b},get:function(a){for(var b=[],c=0,d=h.length;c<d;c++)h[c].name===a.toUpperCase()&&b.push(h[c].segment);return b},set:function(a,b){var c,d,e,f=[];for("string"==typeof b?f.push(b):f=b,c=d=0,e=h.length;c<e&&(h[c].name===a.toUpperCase()&&(h[c].segment=f[d],h[c].length=f[d].length,d++),!(d>=f.length));c++);},purge:function(){this.headers=h=[]}}}}),d("moxie/runtime/html5/image/ExifParser",["moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(a,c,d){function e(f){function g(c,e){var f,g,h,i,j,m,n,o,p=this,q=[],r={},s={1:"BYTE",7:"UNDEFINED",2:"ASCII",3:"SHORT",4:"LONG",5:"RATIONAL",9:"SLONG",10:"SRATIONAL"},t={BYTE:1,UNDEFINED:1,ASCII:1,SHORT:2,LONG:4,RATIONAL:8,SLONG:4,SRATIONAL:8};for(f=p.SHORT(c),g=0;g<f;g++)if(q=[],n=c+2+12*g,h=e[p.SHORT(n)],h!==b){if(i=s[p.SHORT(n+=2)],j=p.LONG(n+=2),m=t[i],!m)throw new d.ImageError(d.ImageError.INVALID_META_ERR);if(n+=4,m*j>4&&(n=p.LONG(n)+l.tiffHeader),n+m*j>=this.length())throw new d.ImageError(d.ImageError.INVALID_META_ERR);"ASCII"!==i?(q=p.asArray(i,n,j),o=1==j?q[0]:q,k.hasOwnProperty(h)&&"object"!=typeof o?r[h]=k[h][o]:r[h]=o):r[h]=a.trim(p.STRING(n,j).replace(/\0$/,""))}return r}function h(a,b,c){var d,e,f,g=0;if("string"==typeof b){var h=j[a.toLowerCase()];for(var i in h)if(h[i]===b){b=i;break}}d=l[a.toLowerCase()+"IFD"],e=this.SHORT(d);for(var k=0;k<e;k++)if(f=d+12*k+2,this.SHORT(f)==b){g=f+8;break}if(!g)return!1;try{this.write(g,c,4)}catch(m){return!1}return!0}var i,j,k,l,m,n;if(c.call(this,f),j={tiff:{274:"Orientation",270:"ImageDescription",271:"Make",272:"Model",305:"Software",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer"},exif:{36864:"ExifVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",36867:"DateTimeOriginal",33434:"ExposureTime",33437:"FNumber",34855:"ISOSpeedRatings",37377:"ShutterSpeedValue",37378:"ApertureValue",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37386:"FocalLength",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",41988:"DigitalZoomRatio",41992:"Contrast",41993:"Saturation",41994:"Sharpness"},gps:{0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude"},thumb:{513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength"}},k={ColorSpace:{1:"sRGB",0:"Uncalibrated"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{1:"Daylight",2:"Fliorescent",3:"Tungsten",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 -5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},ExposureMode:{0:"Auto exposure",1:"Manual exposure",2:"Auto bracket"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},GPSLatitudeRef:{N:"North latitude",S:"South latitude"},GPSLongitudeRef:{E:"East longitude",W:"West longitude"}},l={tiffHeader:10},m=l.tiffHeader,i={clear:this.clear},a.extend(this,{read:function(){try{return e.prototype.read.apply(this,arguments)}catch(a){throw new d.ImageError(d.ImageError.INVALID_META_ERR)}},write:function(){try{return e.prototype.write.apply(this,arguments)}catch(a){throw new d.ImageError(d.ImageError.INVALID_META_ERR)}},UNDEFINED:function(){return this.BYTE.apply(this,arguments)},RATIONAL:function(a){return this.LONG(a)/this.LONG(a+4)},SRATIONAL:function(a){return this.SLONG(a)/this.SLONG(a+4)},ASCII:function(a){return this.CHAR(a)},TIFF:function(){return n||null},EXIF:function(){var b=null;if(l.exifIFD){try{b=g.call(this,l.exifIFD,j.exif)}catch(c){return null}if(b.ExifVersion&&"array"===a.typeOf(b.ExifVersion)){for(var d=0,e="";d<b.ExifVersion.length;d++)e+=String.fromCharCode(b.ExifVersion[d]);b.ExifVersion=e}}return b},GPS:function(){var b=null;if(l.gpsIFD){try{b=g.call(this,l.gpsIFD,j.gps)}catch(c){return null}b.GPSVersionID&&"array"===a.typeOf(b.GPSVersionID)&&(b.GPSVersionID=b.GPSVersionID.join("."))}return b},thumb:function(){if(l.IFD1)try{var a=g.call(this,l.IFD1,j.thumb);if("JPEGInterchangeFormat"in a)return this.SEGMENT(l.tiffHeader+a.JPEGInterchangeFormat,a.JPEGInterchangeFormatLength)}catch(b){}return null},setExif:function(a,b){return("PixelXDimension"===a||"PixelYDimension"===a)&&h.call(this,"exif",a,b)},clear:function(){i.clear(),f=j=k=n=l=i=null}}),65505!==this.SHORT(0)||"EXIF\0"!==this.STRING(4,5).toUpperCase())throw new d.ImageError(d.ImageError.INVALID_META_ERR);if(this.littleEndian=18761==this.SHORT(m),42!==this.SHORT(m+=2))throw new d.ImageError(d.ImageError.INVALID_META_ERR);l.IFD0=l.tiffHeader+this.LONG(m+=2),n=g.call(this,l.IFD0,j.tiff),"ExifIFDPointer"in n&&(l.exifIFD=l.tiffHeader+n.ExifIFDPointer,delete n.ExifIFDPointer),"GPSInfoIFDPointer"in n&&(l.gpsIFD=l.tiffHeader+n.GPSInfoIFDPointer,delete n.GPSInfoIFDPointer),a.isEmptyObj(n)&&(n=null);var o=this.LONG(l.IFD0+12*this.SHORT(l.IFD0)+2);o&&(l.IFD1=l.tiffHeader+o)}return e.prototype=c.prototype,e}),d("moxie/runtime/html5/image/JPEG",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEGHeaders","moxie/runtime/html5/utils/BinaryReader","moxie/runtime/html5/image/ExifParser"],function(a,b,c,d,e){function f(f){function g(a){var b,c,d=0;for(a||(a=j);d<=a.length();){if(b=a.SHORT(d+=2),b>=65472&&b<=65475)return d+=5,{height:a.SHORT(d),width:a.SHORT(d+=2)};c=a.SHORT(d+=2),d+=c-2}return null}function h(){var a,b,c=l.thumb();return c&&(a=new d(c),b=g(a),a.clear(),b)?(b.data=c,b):null}function i(){l&&k&&j&&(l.clear(),k.purge(),j.clear(),m=k=l=j=null)}var j,k,l,m;if(j=new d(f),65496!==j.SHORT(0))throw new b.ImageError(b.ImageError.WRONG_FORMAT);k=new c(f);try{l=new e(k.get("app1")[0])}catch(n){}m=g.call(this),a.extend(this,{type:"image/jpeg",size:j.length(),width:m&&m.width||0,height:m&&m.height||0,setExif:function(b,c){return!!l&&("object"===a.typeOf(b)?a.each(b,function(a,b){l.setExif(b,a)}):l.setExif(b,c),void k.set("app1",l.SEGMENT()))},writeHeaders:function(){return arguments.length?k.restore(arguments[0]):k.restore(f)},stripHeaders:function(a){return k.strip(a)},purge:function(){i.call(this)}}),l&&(this.meta={tiff:l.TIFF(),exif:l.EXIF(),gps:l.GPS(),thumb:h()})}return f}),d("moxie/runtime/html5/image/PNG",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader"],function(a,b,c){function d(d){function e(){var a,b;return a=g.call(this,8),"IHDR"==a.type?(b=a.start,{width:h.LONG(b),height:h.LONG(b+=4)}):null}function f(){h&&(h.clear(),d=k=i=j=h=null)}function g(a){var b,c,d,e;return b=h.LONG(a),c=h.STRING(a+=4,4),d=a+=4,e=h.LONG(a+b),{length:b,type:c,start:d,CRC:e}}var h,i,j,k;h=new c(d),function(){var b=0,c=0,d=[35152,20039,3338,6666];for(c=0;c<d.length;c++,b+=2)if(d[c]!=h.SHORT(b))throw new a.ImageError(a.ImageError.WRONG_FORMAT)}(),k=e.call(this),b.extend(this,{type:"image/png",size:h.length(),width:k.width,height:k.height,purge:function(){f.call(this)}}),f.call(this)}return d}),d("moxie/runtime/html5/image/ImageInfo",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEG","moxie/runtime/html5/image/PNG"],function(a,b,c,d){return function(e){var f,g=[c,d];f=function(){for(var a=0;a<g.length;a++)try{return new g[a](e)}catch(c){}throw new b.ImageError(b.ImageError.WRONG_FORMAT)}(),a.extend(this,{type:"",size:0,width:0,height:0,setExif:function(){},writeHeaders:function(a){return a},stripHeaders:function(a){return a},purge:function(){e=null}}),a.extend(this,f),this.purge=function(){f.purge(),f=null}}}),d("moxie/runtime/html5/image/MegaPixel",[],function(){function a(a,d,e){var f=a.naturalWidth,g=a.naturalHeight,h=e.width,i=e.height,j=e.x||0,k=e.y||0,l=d.getContext("2d");b(a)&&(f/=2,g/=2);var m=1024,n=document.createElement("canvas");n.width=n.height=m;for(var o=n.getContext("2d"),p=c(a,f,g),q=0;q<g;){for(var r=q+m>g?g-q:m,s=0;s<f;){var t=s+m>f?f-s:m;o.clearRect(0,0,m,m),o.drawImage(a,-s,-q);var u=s*h/f+j<<0,v=Math.ceil(t*h/f),w=q*i/g/p+k<<0,x=Math.ceil(r*i/g/p);l.drawImage(n,0,0,t,r,u,w,v,x),s+=m}q+=m}n=o=null}function b(a){var b=a.naturalWidth,c=a.naturalHeight;if(b*c>1048576){var d=document.createElement("canvas");d.width=d.height=1;var e=d.getContext("2d");return e.drawImage(a,-b+1,0),0===e.getImageData(0,0,1,1).data[3]}return!1}function c(a,b,c){var d=document.createElement("canvas");d.width=1,d.height=c;var e=d.getContext("2d");e.drawImage(a,0,0);for(var f=e.getImageData(0,0,1,c).data,g=0,h=c,i=c;i>g;){var j=f[4*(i-1)+3];0===j?h=i:g=i,i=h+g>>1}d=null;var k=i/c;return 0===k?1:k}return{isSubsampled:b,renderTo:a}}),d("moxie/runtime/html5/image/Image",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/File","moxie/runtime/html5/image/ImageInfo","moxie/runtime/html5/image/MegaPixel","moxie/core/utils/Mime","moxie/core/utils/Env"],function(a,b,c,d,e,f,g,h,i,j){function k(){function a(){if(!u&&!s)throw new c.ImageError(c.DOMException.INVALID_STATE_ERR);return u||s}function k(a){return d.atob(a.substring(a.indexOf("base64,")+7))}function l(a,b){return"data:"+(b||"")+";base64,"+d.btoa(a)}function m(a){var b=this;s=new Image,s.onerror=function(){r.call(this),b.trigger("error",c.ImageError.WRONG_FORMAT)},s.onload=function(){b.trigger("load")},s.src="data:"==a.substr(0,5)?a:l(a,w.type)}function n(a,b){var d,e=this;return window.FileReader?(d=new FileReader,d.onload=function(){b(this.result)},d.onerror=function(){e.trigger("error",c.ImageError.WRONG_FORMAT)},d.readAsDataURL(a),void 0):b(a.getAsDataURL())}function o(c,d,e,f){var g,h,i,j,k,l=this,m=0,n=0;if(z=f,k=this.meta&&this.meta.tiff&&this.meta.tiff.Orientation||1,b.inArray(k,[5,6,7,8])!==-1){var o=c;c=d,d=o}return h=a(),e?(c=Math.min(c,h.width),d=Math.min(d,h.height),g=Math.max(c/h.width,d/h.height)):g=Math.min(c/h.width,d/h.height),g>1&&!e&&f?void this.trigger("Resize"):(u||(u=document.createElement("canvas")),i=Math.round(h.width*g),j=Math.round(h.height*g),e?(u.width=c,u.height=d,i>c&&(m=Math.round((i-c)/2)),j>d&&(n=Math.round((j-d)/2))):(u.width=i,u.height=j),z||q(u.width,u.height,k),p.call(this,h,u,-m,-n,i,j),this.width=u.width,this.height=u.height,y=!0,void l.trigger("Resize"))}function p(a,b,c,d,e,f){if("iOS"===j.OS)h.renderTo(a,b,{width:e,height:f,x:c,y:d});else{var g=b.getContext("2d");g.drawImage(a,c,d,e,f)}}function q(a,b,c){switch(c){case 5:case 6:case 7:case 8:u.width=b,u.height=a;break;default:u.width=a,u.height=b}var d=u.getContext("2d");switch(c){case 2:d.translate(a,0),d.scale(-1,1);break;case 3:d.translate(a,b),d.rotate(Math.PI);break;case 4:d.translate(0,b),d.scale(1,-1);break;case 5:d.rotate(.5*Math.PI),d.scale(1,-1);break;case 6:d.rotate(.5*Math.PI),d.translate(0,-b);break;case 7:d.rotate(.5*Math.PI),d.translate(a,-b),d.scale(-1,1);break;case 8:d.rotate(-.5*Math.PI),d.translate(-a,0)}}function r(){t&&(t.purge(),t=null),v=s=u=w=null,y=!1}var s,t,u,v,w,x=this,y=!1,z=!0;b.extend(this,{loadFromBlob:function(a){var b=this,d=b.getRuntime(),e=!(arguments.length>1)||arguments[1];if(!d.can("access_binary"))throw new c.RuntimeError(c.RuntimeError.NOT_SUPPORTED_ERR);return w=a,a.isDetached()?(v=a.getSource(),void m.call(this,v)):void n.call(this,a.getSource(),function(a){e&&(v=k(a)),m.call(b,a)})},loadFromImage:function(a,b){this.meta=a.meta,w=new f(null,{name:a.name,size:a.size,type:a.type}),m.call(this,b?v=a.getAsBinaryString():a.getAsDataURL())},getInfo:function(){var b,c=this.getRuntime();return!t&&v&&c.can("access_image_binary")&&(t=new g(v)),b={width:a().width||0,height:a().height||0,type:w.type||i.getFileMime(w.name),size:v&&v.length||w.size||0,name:w.name||"",meta:t&&t.meta||this.meta||{}},!b.meta||!b.meta.thumb||b.meta.thumb.data instanceof e||(b.meta.thumb.data=new e(null,{type:"image/jpeg",data:b.meta.thumb.data})),b},downsize:function(){o.apply(this,arguments)},getAsCanvas:function(){return u&&(u.id=this.uid+"_canvas"),u},getAsBlob:function(a,b){return a!==this.type&&o.call(this,this.width,this.height,!1),new f(null,{name:w.name||"",type:a,data:x.getAsBinaryString.call(this,a,b)})},getAsDataURL:function(a){var b=arguments[1]||90;if(!y)return s.src;if("image/jpeg"!==a)return u.toDataURL("image/png");try{return u.toDataURL("image/jpeg",b/100)}catch(c){return u.toDataURL("image/jpeg")}},getAsBinaryString:function(a,b){if(!y)return v||(v=k(x.getAsDataURL(a,b))),v;if("image/jpeg"!==a)v=k(x.getAsDataURL(a,b));else{var c;b||(b=90);try{c=u.toDataURL("image/jpeg",b/100)}catch(d){c=u.toDataURL("image/jpeg")}v=k(c),t&&(v=t.stripHeaders(v),z&&(t.meta&&t.meta.exif&&t.setExif({PixelXDimension:this.width,PixelYDimension:this.height}),v=t.writeHeaders(v)),t.purge(),t=null)}return y=!1,v},destroy:function(){x=null,r.call(this),this.getRuntime().getShim().removeInstance(this.uid)}})}return a.Image=k}),d("moxie/runtime/flash/Runtime",[],function(){return{}}),d("moxie/runtime/silverlight/Runtime",[],function(){return{}}),d("moxie/runtime/html4/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(a,b,c,d){function e(b){var e=this,h=c.capTest,i=c.capTrue;c.call(this,b,f,{access_binary:h(window.FileReader||window.File&&File.getAsDataURL),access_image_binary:!1,display_media:h(g.Image&&(d.can("create_canvas")||d.can("use_data_uri_over32kb"))),do_cors:!1,drag_and_drop:!1,filter_by_extension:h(function(){return"Chrome"===d.browser&&d.verComp(d.version,28,">=")||"IE"===d.browser&&d.verComp(d.version,10,">=")||"Safari"===d.browser&&d.verComp(d.version,7,">=")}()),resize_image:function(){return g.Image&&e.can("access_binary")&&d.can("create_canvas")},report_upload_progress:!1,return_response_headers:!1,return_response_type:function(b){return!("json"!==b||!window.JSON)||!!~a.inArray(b,["text","document",""])},return_status_code:function(b){return!a.arrayDiff(b,[200,404])},select_file:function(){return d.can("use_fileinput")},select_multiple:!1,send_binary_string:!1,send_custom_headers:!1,send_multipart:!0,slice_blob:!1,stream_upload:function(){return e.can("select_file")},summon_file_dialog:function(){return e.can("select_file")&&("Firefox"===d.browser&&d.verComp(d.version,4,">=")||"Opera"===d.browser&&d.verComp(d.version,12,">=")||"IE"===d.browser&&d.verComp(d.version,10,">=")||!!~a.inArray(d.browser,["Chrome","Safari"]))},upload_filesize:i,use_http_method:function(b){return!a.arrayDiff(b,["GET","POST"])}}),a.extend(this,{init:function(){this.trigger("Init")},destroy:function(a){return function(){a.call(e),a=e=null}}(this.destroy)}),a.extend(this.getShim(),g)}var f="html4",g={};return c.addConstructor(f,e),g}),d("moxie/runtime/html4/file/FileInput",["moxie/runtime/html4/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(a,b,c,d,e,f,g){function h(){function a(){var f,k,l,m,n,o,p=this,q=p.getRuntime();o=c.guid("uid_"),f=q.getShimContainer(),h&&(l=d.get(h+"_form"),l&&c.extend(l.style,{top:"100%"})),m=document.createElement("form"),m.setAttribute("id",o+"_form"),m.setAttribute("method","post"),m.setAttribute("enctype","multipart/form-data"),m.setAttribute("encoding","multipart/form-data"),c.extend(m.style,{overflow:"hidden",position:"absolute",top:0,left:0,width:"100%",height:"100%"}),n=document.createElement("input"),n.setAttribute("id",o),n.setAttribute("type","file"),n.setAttribute("name",i.name||"Filedata"),n.setAttribute("accept",j.join(",")),c.extend(n.style,{fontSize:"999px",opacity:0}),m.appendChild(n),f.appendChild(m),c.extend(n.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),"IE"===g.browser&&g.verComp(g.version,10,"<")&&c.extend(n.style,{filter:"progid:DXImageTransform.Microsoft.Alpha(opacity=0)"}),n.onchange=function(){var c;if(this.value){if(this.files){if(c=this.files[0],0===c.size)return void m.parentNode.removeChild(m)}else c={name:this.value};c=new b(q.uid,c),this.onchange=function(){},a.call(p),p.files=[c],n.setAttribute("id",c.uid),m.setAttribute("id",c.uid+"_form"),p.trigger("change"),n=m=null}},q.can("summon_file_dialog")&&(k=d.get(i.browse_button),e.removeEvent(k,"click",p.uid),e.addEvent(k,"click",function(a){n&&!n.disabled&&n.click(),a.preventDefault()},p.uid)),h=o,f=l=k=null}var h,i,j=[];c.extend(this,{init:function(b){var c,g=this,h=g.getRuntime();i=b,j=b.accept.mimes||f.extList2mimes(b.accept,h.can("filter_by_extension")),c=h.getShimContainer(),function(){var a,f,i;a=d.get(b.browse_button),h.can("summon_file_dialog")&&("static"===d.getStyle(a,"position")&&(a.style.position="relative"),f=parseInt(d.getStyle(a,"z-index"),10)||1,a.style.zIndex=f,c.style.zIndex=f-1),i=h.can("summon_file_dialog")?a:c,e.addEvent(i,"mouseover",function(){g.trigger("mouseenter")},g.uid),e.addEvent(i,"mouseout",function(){g.trigger("mouseleave")},g.uid),e.addEvent(i,"mousedown",function(){g.trigger("mousedown")},g.uid),e.addEvent(d.get(b.container),"mouseup",function(){g.trigger("mouseup")},g.uid),a=null}(),a.call(this),c=null,g.trigger({type:"ready",async:!0})},disable:function(a){var b;(b=d.get(h))&&(b.disabled=!!a)},destroy:function(){var a=this.getRuntime(),b=a.getShim(),c=a.getShimContainer();e.removeAllEvents(c,this.uid),e.removeAllEvents(i&&d.get(i.container),this.uid),e.removeAllEvents(i&&d.get(i.browse_button),this.uid),c&&(c.innerHTML=""),b.removeInstance(this.uid),h=j=i=c=b=null}})}return a.FileInput=h}),d("moxie/runtime/html4/file/FileReader",["moxie/runtime/html4/Runtime","moxie/runtime/html5/file/FileReader"],function(a,b){return a.FileReader=b}),d("moxie/runtime/html4/xhr/XMLHttpRequest",["moxie/runtime/html4/Runtime","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Url","moxie/core/Exceptions","moxie/core/utils/Events","moxie/file/Blob","moxie/xhr/FormData"],function(a,b,c,d,e,f,g,h){function i(){function a(a){var b,d,e,g,h=this,i=!1;if(k){if(b=k.id.replace(/_iframe$/,""),d=c.get(b+"_form")){for(e=d.getElementsByTagName("input"),g=e.length;g--;)switch(e[g].getAttribute("type")){case"hidden":e[g].parentNode.removeChild(e[g]);break;case"file":i=!0}e=[],i||d.parentNode.removeChild(d),d=null}setTimeout(function(){f.removeEvent(k,"load",h.uid),k.parentNode&&k.parentNode.removeChild(k);var b=h.getRuntime().getShimContainer();b.children.length||b.parentNode.removeChild(b),b=k=null,a()},1)}}var i,j,k;b.extend(this,{send:function(l,m){function n(){var c=t.getShimContainer()||document.body,e=document.createElement("div");e.innerHTML='<iframe id="'+o+'_iframe" name="'+o+'_iframe" src="javascript:&quot;&quot;" style="display:none"></iframe>',k=e.firstChild,c.appendChild(k),f.addEvent(k,"load",function(){var c;try{c=k.contentWindow.document||k.contentDocument||window.frames[k.id].document,/^4(0[0-9]|1[0-7]|2[2346])\s/.test(c.title)?i=c.title.replace(/^(\d+).*$/,"$1"):(i=200,j=b.trim(c.body.innerHTML),s.trigger({type:"progress",loaded:j.length,total:j.length}),r&&s.trigger({type:"uploadprogress",loaded:r.size||1025,total:r.size||1025}))}catch(e){if(!d.hasSameOrigin(l.url))return void a.call(s,function(){s.trigger("error")});i=404}a.call(s,function(){s.trigger("load")})},s.uid)}var o,p,q,r,s=this,t=s.getRuntime();if(i=j=null,m instanceof h&&m.hasBlob()){if(r=m.getBlob(),o=r.uid,q=c.get(o),p=c.get(o+"_form"),!p)throw new e.DOMException(e.DOMException.NOT_FOUND_ERR)}else o=b.guid("uid_"),p=document.createElement("form"),p.setAttribute("id",o+"_form"),p.setAttribute("method",l.method),p.setAttribute("enctype","multipart/form-data"),p.setAttribute("encoding","multipart/form-data"),t.getShimContainer().appendChild(p);p.setAttribute("target",o+"_iframe"),m instanceof h&&m.each(function(a,c){if(a instanceof g)q&&q.setAttribute("name",c);else{var d=document.createElement("input");b.extend(d,{type:"hidden",name:c,value:a}),q?p.insertBefore(d,q):p.appendChild(d)}}),p.setAttribute("action",l.url),n(),p.submit(),s.trigger("loadstart")},getStatus:function(){return i},getResponse:function(a){if("json"===a&&"string"===b.typeOf(j)&&window.JSON)try{return JSON.parse(j.replace(/^\s*<pre[^>]*>/,"").replace(/<\/pre>\s*$/,""))}catch(c){return null}return j},abort:function(){var b=this;k&&k.contentWindow&&(k.contentWindow.stop?k.contentWindow.stop():k.contentWindow.document.execCommand?k.contentWindow.document.execCommand("Stop"):k.src="about:blank"),a.call(this,function(){b.dispatchEvent("abort")})}})}return a.XMLHttpRequest=i}),d("moxie/runtime/html4/image/Image",["moxie/runtime/html4/Runtime","moxie/runtime/html5/image/Image"],function(a,b){return a.Image=b}),f(["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/I18n","moxie/core/utils/Mime","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/EventTarget","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/file/FileInput","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/File","moxie/file/FileDrop","moxie/file/FileReader","moxie/core/utils/Url","moxie/runtime/RuntimeTarget","moxie/file/FileReaderSync","moxie/xhr/FormData","moxie/xhr/XMLHttpRequest","moxie/runtime/Transporter","moxie/image/Image","moxie/core/utils/Events"])}(this),function(a){"use strict";var b={},c=a.moxie.core.utils.Basic.inArray;return function d(a){var e,f;for(e in a)f=typeof a[e],"object"!==f||~c(e,["Exceptions","Env","Mime"])?"function"===f&&(b[e]=a[e]):d(a[e])}(a.moxie),b.Env=a.moxie.core.utils.Env,b.Mime=a.moxie.core.utils.Mime,b.Exceptions=a.moxie.core.Exceptions,a.mOxie=b,a.o||(a.o=b),b}(this);