!function(a,b,c){function d(a){function b(a,b,c){var e={chunks:"slice_blob",jpgresize:"send_binary_string",pngresize:"send_binary_string",progress:"report_upload_progress",multi_selection:"select_multiple",dragdrop:"drag_and_drop",drop_element:"drag_and_drop",headers:"send_custom_headers",urlstream_upload:"send_binary_string",canSendBinary:"send_binary",triggerDialog:"summon_file_dialog"};e[a]?d[e[a]]=b:c||(d[a]=b)}var c=a.required_features,d={};return"string"==typeof c?g.each(c.split(/\s*,\s*/),function(a){b(a,!0)}):"object"==typeof c?g.each(c,function(a,c){b(c,a)}):c===!0&&(a.chunk_size>0&&(d.slice_blob=!0),!a.resize.enabled&&a.multipart||(d.send_binary_string=!0),g.each(a,function(a,c){b(c,!!a,!0)})),a.runtimes="html5,html4",d}var e=a.setTimeout,f={},g={VERSION:"2.1.9",STOPPED:1,STARTED:2,QUEUED:1,UPLOADING:2,FAILED:4,DONE:5,GENERIC_ERROR:-100,HTTP_ERROR:-200,IO_ERROR:-300,SECURITY_ERROR:-400,INIT_ERROR:-500,FILE_SIZE_ERROR:-600,FILE_EXTENSION_ERROR:-601,FILE_DUPLICATE_ERROR:-602,IMAGE_FORMAT_ERROR:-700,MEMORY_ERROR:-701,IMAGE_DIMENSIONS_ERROR:-702,mimeTypes:b.mimes,ua:b.ua,typeOf:b.typeOf,extend:b.extend,guid:b.guid,getAll:function(a){var b,c=[];"array"!==g.typeOf(a)&&(a=[a]);for(var d=a.length;d--;)b=g.get(a[d]),b&&c.push(b);return c.length?c:null},get:b.get,each:b.each,getPos:b.getPos,getSize:b.getSize,xmlEncode:function(a){var b={"<":"lt",">":"gt","&":"amp",'"':"quot","'":"#39"},c=/[<>&\"\']/g;return a?(""+a).replace(c,function(a){return b[a]?"&"+b[a]+";":a}):a},toArray:b.toArray,inArray:b.inArray,addI18n:b.addI18n,translate:b.translate,isEmptyObj:b.isEmptyObj,hasClass:b.hasClass,addClass:b.addClass,removeClass:b.removeClass,getStyle:b.getStyle,addEvent:b.addEvent,removeEvent:b.removeEvent,removeAllEvents:b.removeAllEvents,cleanName:function(a){var b,c;for(c=[/[\300-\306]/g,"A",/[\340-\346]/g,"a",/\307/g,"C",/\347/g,"c",/[\310-\313]/g,"E",/[\350-\353]/g,"e",/[\314-\317]/g,"I",/[\354-\357]/g,"i",/\321/g,"N",/\361/g,"n",/[\322-\330]/g,"O",/[\362-\370]/g,"o",/[\331-\334]/g,"U",/[\371-\374]/g,"u"],b=0;b<c.length;b+=2)a=a.replace(c[b],c[b+1]);return a=a.replace(/\s+/g,"_"),a=a.replace(/[^a-z0-9_\-\.]+/gi,"")},buildUrl:function(a,b){var c="";return g.each(b,function(a,b){c+=(c?"&":"")+encodeURIComponent(b)+"="+encodeURIComponent(a)}),c&&(a+=(a.indexOf("?")>0?"&":"?")+c),a},formatSize:function(a){function b(a,b){return Math.round(a*Math.pow(10,b))/Math.pow(10,b)}if(a===c||/\D/.test(a))return g.translate("N/A");var d=Math.pow(1024,4);return a>d?b(a/d,1)+" "+g.translate("tb"):a>(d/=1024)?b(a/d,1)+" "+g.translate("gb"):a>(d/=1024)?b(a/d,1)+" "+g.translate("mb"):a>1024?Math.round(a/1024)+" "+g.translate("kb"):a+" "+g.translate("b")},parseSize:b.parseSizeStr,predictRuntime:function(a,c){var d,e;return d=new g.Uploader(a),e=b.Runtime.thatCan(d.getOption().required_features,c||a.runtimes),d.destroy(),e},addFileFilter:function(a,b){f[a]=b}};g.addFileFilter("mime_types",function(a,b,c){a.length&&!a.regexp.test(b.name)?(this.trigger("Error",{code:g.FILE_EXTENSION_ERROR,message:g.translate("File extension error."),file:b}),c(!1)):c(!0)}),g.addFileFilter("max_file_size",function(a,b,c){var d;a=g.parseSize(a),b.size!==d&&a&&b.size>a?(this.trigger("Error",{code:g.FILE_SIZE_ERROR,message:g.translate("File size error."),file:b}),c(!1)):c(!0)}),g.addFileFilter("prevent_duplicates",function(a,b,c){if(a)for(var d=this.files.length;d--;)if(b.name===this.files[d].name&&b.size===this.files[d].size)return this.trigger("Error",{code:g.FILE_DUPLICATE_ERROR,message:g.translate("Duplicate file error."),file:b}),void c(!1);c(!0)}),g.Uploader=function(a){function h(){var a,b,c=0;if(this.state==g.STARTED){for(b=0;b<D.length;b++)a||D[b].status!=g.QUEUED?c++:(a=D[b],this.trigger("BeforeUpload",a)&&(a.status=g.UPLOADING,this.trigger("UploadFile",a)));c==D.length&&(this.state!==g.STOPPED&&(this.state=g.STOPPED,this.trigger("StateChanged")),this.trigger("UploadComplete",D))}}function i(a){a.percent=a.size>0?Math.ceil(a.loaded/a.size*100):100,j()}function j(){var a,b;for(A.reset(),a=0;a<D.length;a++)b=D[a],b.size!==c?(A.size+=b.origSize,A.loaded+=b.loaded*b.origSize/b.size):A.size=c,b.status==g.DONE?A.uploaded++:b.status==g.FAILED?A.failed++:A.queued++;A.size===c?A.percent=D.length>0?Math.ceil(A.uploaded/D.length*100):0:(A.bytesPerSec=Math.ceil(A.loaded/((+new Date-z||1)/1e3)),A.percent=A.size>0?Math.ceil(A.loaded/A.size*100):0)}function k(){var a=F[0]||G[0];return!!a&&a.getRuntime().uid}function l(a,c){if(a.ruid){var d=b.Runtime.getInfo(a.ruid);if(d)return d.can(c)}return!1}function m(){this.bind("FilesAdded FilesRemoved",function(a){a.trigger("QueueChanged"),a.refresh()}),this.bind("CancelUpload",u),this.bind("BeforeUpload",q),this.bind("UploadFile",r),this.bind("UploadProgress",s),this.bind("StateChanged",t),this.bind("QueueChanged",j),this.bind("Error",w),this.bind("FileUploaded",v),this.bind("Destroy",x)}function n(a,c){var d=this,e=0,f=[],h={runtime_order:a.runtimes,required_caps:a.required_features,preferred_caps:E};g.each(a.runtimes.split(/\s*,\s*/),function(b){a[b]&&(h[b]=a[b])}),a.browse_button&&g.each(a.browse_button,function(c){f.push(function(f){var i=new b.FileInput(g.extend({},h,{accept:a.filters.mime_types,name:a.file_data_name,multiple:a.multi_selection,container:a.container,browse_button:c}));i.onready=function(){var a=b.Runtime.getInfo(this.ruid);b.extend(d.features,{chunks:a.can("slice_blob"),multipart:a.can("send_multipart"),multi_selection:a.can("select_multiple")}),e++,F.push(this),f()},i.onchange=function(){d.addFile(this.files)},i.bind("mouseenter mouseleave mousedown mouseup",function(d){H||(a.browse_button_hover&&("mouseenter"===d.type?b.addClass(c,a.browse_button_hover):"mouseleave"===d.type&&b.removeClass(c,a.browse_button_hover)),a.browse_button_active&&("mousedown"===d.type?b.addClass(c,a.browse_button_active):"mouseup"===d.type&&b.removeClass(c,a.browse_button_active)))}),i.bind("mousedown",function(){d.trigger("Browse")}),i.bind("error runtimeerror",function(){i=null,f()}),i.init()})}),a.drop_element&&g.each(a.drop_element,function(a){f.push(function(c){var f=new b.FileDrop(g.extend({},h,{drop_zone:a}));f.onready=function(){var a=b.Runtime.getInfo(this.ruid);b.extend(d.features,{chunks:a.can("slice_blob"),multipart:a.can("send_multipart"),dragdrop:a.can("drag_and_drop")}),e++,G.push(this),c()},f.ondrop=function(){d.addFile(this.files)},f.bind("error runtimeerror",function(){f=null,c()}),f.init()})}),b.inSeries(f,function(){"function"==typeof c&&c(e)})}function o(a,d,e){var f=new b.Image;try{f.onload=function(){return d.width>this.width&&d.height>this.height&&d.quality===c&&d.preserve_headers&&!d.crop?(this.destroy(),e(a)):void f.downsize(d.width,d.height,d.crop,d.preserve_headers)},f.onresize=function(){e(this.getAsBlob(a.type,d.quality)),this.destroy()},f.onerror=function(){e(a)},f.load(a)}catch(g){e(a)}}function p(a,c,e){function f(a,b,c){var d=y[a];switch(a){case"max_file_size":"max_file_size"===a&&(y.max_file_size=y.filters.max_file_size=b);break;case"chunk_size":(b=g.parseSize(b))&&(y[a]=b,y.send_file_name=!0);break;case"multipart":y[a]=b,b||(y.send_file_name=!0);break;case"unique_names":y[a]=b,b&&(y.send_file_name=!0);break;case"filters":"array"===g.typeOf(b)&&(b={mime_types:b}),c?g.extend(y.filters,b):y.filters=b,b.mime_types&&(y.filters.mime_types.regexp=function(a){var b=[];return g.each(a,function(a){g.each(a.extensions.split(/,/),function(a){/^\s*\*\s*$/.test(a)?b.push("\\.*"):b.push("\\."+a.replace(new RegExp("["+"/^$.*+?|()[]{}\\".replace(/./g,"\\$&")+"]","g"),"\\$&"))})}),new RegExp("("+b.join("|")+")$","i")}(y.filters.mime_types));break;case"resize":c?g.extend(y.resize,b,{enabled:!0}):y.resize=b;break;case"prevent_duplicates":y.prevent_duplicates=y.filters.prevent_duplicates=!!b;break;case"container":case"browse_button":case"drop_element":b="container"===a?g.get(b):g.getAll(b);case"runtimes":case"multi_selection":y[a]=b,c||(i=!0);break;default:y[a]=b}c||h.trigger("OptionChanged",a,b,d)}var h=this,i=!1;"object"==typeof a?g.each(a,function(a,b){f(b,a,e)}):f(a,c,e),e?(y.required_features=d(g.extend({},y)),E=d(g.extend({},y,{required_features:!0}))):i&&(h.trigger("Destroy"),n.call(h,y,function(a){a?(h.runtime=b.Runtime.getInfo(k()).type,h.trigger("Init",{runtime:h.runtime}),h.trigger("PostInit")):h.trigger("Error",{code:g.INIT_ERROR,message:g.translate("Init error.")})}))}function q(a,b){if(a.settings.unique_names){var c=b.name.match(/\.([^.]+)$/),d="part";c&&(d=c[1]),b.target_name=b.id+"."+d}}function r(a,c){function d(){k-- >0?e(f,1e3):(c.loaded=n,a.trigger("Error",{code:g.HTTP_ERROR,message:g.translate("HTTP Error."),file:c,response:B.responseText,status:B.status,responseHeaders:B.getAllResponseHeaders()}))}function f(){var l,o,p,q={};c.status===g.UPLOADING&&a.state!==g.STOPPED&&(a.settings.send_file_name&&(q.name=c.target_name||c.name),j&&m.chunks&&h.size>j?(p=Math.min(j,h.size-n),l=h.slice(n,n+p)):(p=h.size,l=h),j&&m.chunks&&(a.settings.send_chunk_number?(q.chunk=Math.ceil(n/j),q.chunks=Math.ceil(h.size/j)):(q.offset=n,q.total=h.size)),B=new b.XMLHttpRequest,B.upload&&(B.upload.onprogress=function(b){c.loaded=Math.min(c.size,n+b.loaded),a.trigger("UploadProgress",c)}),B.onload=function(){return B.status>=400?void d():(k=a.settings.max_retries,p<h.size?(l.destroy(),n+=p,c.loaded=Math.min(n,h.size),a.trigger("ChunkUploaded",c,{offset:c.loaded,total:h.size,response:B.responseText,status:B.status,responseHeaders:B.getAllResponseHeaders()}),"Android Browser"===b.Env.browser&&a.trigger("UploadProgress",c)):c.loaded=c.size,l=o=null,void(!n||n>=h.size?(c.size!=c.origSize&&(h.destroy(),h=null),a.trigger("UploadProgress",c),c.status=g.DONE,a.trigger("FileUploaded",c,{response:B.responseText,status:B.status,responseHeaders:B.getAllResponseHeaders()})):e(f,1)))},B.onerror=function(){d()},B.onloadend=function(){this.destroy(),B=null},a.settings.multipart&&m.multipart?(B.open("post",i,!0),g.each(a.settings.headers,function(a,b){B.setRequestHeader(b,a)}),o=new b.FormData,g.each(g.extend(q,a.settings.multipart_params),function(a,b){o.append(b,a)}),o.append(a.settings.file_data_name,l),B.send(o,{runtime_order:a.settings.runtimes,required_caps:a.settings.required_features,preferred_caps:E})):(i=g.buildUrl(a.settings.url,g.extend(q,a.settings.multipart_params)),B.open("post",i,!0),B.setRequestHeader("Content-Type","application/octet-stream"),g.each(a.settings.headers,function(a,b){B.setRequestHeader(b,a)}),B.send(l,{runtime_order:a.settings.runtimes,required_caps:a.settings.required_features,preferred_caps:E})))}var h,i=a.settings.url,j=a.settings.chunk_size,k=a.settings.max_retries,m=a.features,n=0;c.loaded&&(n=c.loaded=j?j*Math.floor(c.loaded/j):0),h=c.getSource(),a.settings.resize.enabled&&l(h,"send_binary_string")&&~b.inArray(h.type,["image/jpeg","image/png"])?o.call(this,h,a.settings.resize,function(a){h=a,c.size=a.size,f()}):f()}function s(a,b){i(b)}function t(a){if(a.state==g.STARTED)z=+new Date;else if(a.state==g.STOPPED)for(var b=a.files.length-1;b>=0;b--)a.files[b].status==g.UPLOADING&&(a.files[b].status=g.QUEUED,j())}function u(){B&&B.abort()}function v(a){j(),e(function(){h.call(a)},1)}function w(a,b){b.code===g.INIT_ERROR?a.destroy():b.code===g.HTTP_ERROR&&(b.file.status=g.FAILED,i(b.file),a.state==g.STARTED&&(a.trigger("CancelUpload"),e(function(){h.call(a)},1)))}function x(a){a.stop(),g.each(D,function(a){a.destroy()}),D=[],F.length&&(g.each(F,function(a){a.destroy()}),F=[]),G.length&&(g.each(G,function(a){a.destroy()}),G=[]),E={},H=!1,z=B=null,A.reset()}var y,z,A,B,C=g.guid(),D=[],E={},F=[],G=[],H=!1;y={runtimes:b.Runtime.order,max_retries:0,chunk_size:0,multipart:!0,multi_selection:!0,file_data_name:"file",filters:{mime_types:[],prevent_duplicates:!1,max_file_size:0},resize:{enabled:!1,preserve_headers:!0,crop:!1},send_file_name:!0,send_chunk_number:!0},p.call(this,a,null,!0),A=new g.QueueProgress,g.extend(this,{id:C,uid:C,state:g.STOPPED,features:{},runtime:null,files:D,settings:y,total:A,init:function(){var a,c,d=this;return a=d.getOption("preinit"),"function"==typeof a?a(d):g.each(a,function(a,b){d.bind(b,a)}),m.call(d),g.each(["container","browse_button","drop_element"],function(a){if(null===d.getOption(a))return c={code:g.INIT_ERROR,message:g.translate("'%' specified, but cannot be found.")},!1}),c?d.trigger("Error",c):y.browse_button||y.drop_element?void n.call(d,y,function(a){var c=d.getOption("init");"function"==typeof c?c(d):g.each(c,function(a,b){d.bind(b,a)}),a?(d.runtime=b.Runtime.getInfo(k()).type,d.trigger("Init",{runtime:d.runtime}),d.trigger("PostInit")):d.trigger("Error",{code:g.INIT_ERROR,message:g.translate("Init error.")})}):d.trigger("Error",{code:g.INIT_ERROR,message:g.translate("You must specify either 'browse_button' or 'drop_element'.")})},setOption:function(a,b){p.call(this,a,b,!this.runtime)},getOption:function(a){return a?y[a]:y},refresh:function(){F.length&&g.each(F,function(a){a.trigger("Refresh")}),this.trigger("Refresh")},start:function(){this.state!=g.STARTED&&(this.state=g.STARTED,this.trigger("StateChanged"),h.call(this))},stop:function(){this.state!=g.STOPPED&&(this.state=g.STOPPED,this.trigger("StateChanged"),this.trigger("CancelUpload"))},disableBrowse:function(){H=arguments[0]===c||arguments[0],F.length&&g.each(F,function(a){a.disable(H)}),this.trigger("DisableBrowse",H)},getFile:function(a){var b;for(b=D.length-1;b>=0;b--)if(D[b].id===a)return D[b]},addFile:function(a,c){function d(a,c){var d=[];b.each(j.settings.filters,function(b,c){f[c]&&d.push(function(d){f[c].call(j,b,a,function(a){d(!a)})})}),b.inSeries(d,c)}function h(a){var f=b.typeOf(a);if(a instanceof b.File){if(!a.ruid&&!a.isDetached()){if(!i)return!1;a.ruid=i,a.connectRuntime(i)}h(new g.File(a))}else a instanceof b.Blob?(h(a.getSource()),a.destroy()):a instanceof g.File?(c&&(a.name=c),l.push(function(b){d(a,function(c){c||(D.push(a),m.push(a),j.trigger("FileFiltered",a)),e(b,1)})})):b.inArray(f,["file","blob"])!==-1?h(new b.File(null,a)):"node"===f&&"filelist"===b.typeOf(a.files)?b.each(a.files,h):"array"===f&&(c=null,b.each(a,h))}var i,j=this,l=[],m=[];i=k(),h(a),l.length&&b.inSeries(l,function(){m.length&&j.trigger("FilesAdded",m)})},removeFile:function(a){for(var b="string"==typeof a?a:a.id,c=D.length-1;c>=0;c--)if(D[c].id===b)return this.splice(c,1)[0]},splice:function(a,b){var d=D.splice(a===c?0:a,b===c?D.length:b),e=!1;return this.state==g.STARTED&&(g.each(d,function(a){if(a.status===g.UPLOADING)return e=!0,!1}),e&&this.stop()),this.trigger("FilesRemoved",d),g.each(d,function(a){a.destroy()}),e&&this.start(),d},dispatchEvent:function(a){var b,c;if(a=a.toLowerCase(),b=this.hasEventListener(a)){b.sort(function(a,b){return b.priority-a.priority}),c=[].slice.call(arguments),c.shift(),c.unshift(this);for(var d=0;d<b.length;d++)if(b[d].fn.apply(b[d].scope,c)===!1)return!1}return!0},bind:function(a,b,c,d){g.Uploader.prototype.bind.call(this,a,b,d,c)},destroy:function(){this.trigger("Destroy"),y=A=null,this.unbindAll()}})},g.Uploader.prototype=b.EventTarget.instance,g.File=function(){function a(a){g.extend(this,{id:g.guid(),name:a.name||a.fileName,type:a.type||"",size:a.size||a.fileSize,origSize:a.size||a.fileSize,loaded:0,percent:0,status:g.QUEUED,lastModifiedDate:a.lastModifiedDate||(new Date).toLocaleString(),getNative:function(){var a=this.getSource().getSource();return b.inArray(b.typeOf(a),["blob","file"])!==-1?a:null},getSource:function(){return c[this.id]?c[this.id]:null},destroy:function(){var a=this.getSource();a&&(a.destroy(),delete c[this.id])}}),c[this.id]=a}var c={};return a}(),g.QueueProgress=function(){var a=this;a.size=0,a.loaded=0,a.uploaded=0,a.failed=0,a.queued=0,a.percent=0,a.bytesPerSec=0,a.reset=function(){a.size=a.loaded=a.uploaded=a.failed=a.queued=a.percent=a.bytesPerSec=0}},a.plupload=g}(window,mOxie);