!function(a){function b(d){if(c[d])return c[d].exports;var e=c[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,b),e.l=!0,e.exports}var c={};return b.m=a,b.c=c,b.d=function(a,c,d){b.o(a,c)||Object.defineProperty(a,c,{enumerable:!0,get:d})},b.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},b.t=function(a,c){if(1&c&&(a=b(a)),8&c)return a;if(4&c&&"object"==typeof a&&a&&a.__esModule)return a;var d=Object.create(null);if(b.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&c&&"string"!=typeof a)for(var e in a)b.d(d,e,function(b){return a[b]}.bind(null,e));return d},b.n=function(a){var c=a&&a.__esModule?function(){return a["default"]}:function(){return a};return b.d(c,"a",c),c},b.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},b.p="",b(b.s=22)}({22:function(a,b,c){a.exports=c(23)},23:function(a,b,c){var d,e,f,g,h=jQuery;window.wp=window.wp||{},g=wp.media=function(a){var b,c=g.view.MediaFrame;if(c)return a=_.defaults(a||{},{frame:"select"}),"select"===a.frame&&c.Select?b=new c.Select(a):"post"===a.frame&&c.Post?b=new c.Post(a):"manage"===a.frame&&c.Manage?b=new c.Manage(a):"image"===a.frame&&c.ImageDetails?b=new c.ImageDetails(a):"audio"===a.frame&&c.AudioDetails?b=new c.AudioDetails(a):"video"===a.frame&&c.VideoDetails?b=new c.VideoDetails(a):"edit-attachments"===a.frame&&c.EditAttachments&&(b=new c.EditAttachments(a)),delete a.frame,g.frame=b,b},_.extend(g,{model:{},view:{},controller:{},frames:{}}),f=g.model.l10n=window._wpMediaModelsL10n||{},g.model.settings=f.settings||{},delete f.settings,d=g.model.Attachment=c(24),e=g.model.Attachments=c(25),g.model.Query=c(26),g.model.PostImage=c(27),g.model.Selection=c(28),g.compare=function(a,b,c,d){return _.isEqual(a,b)?c===d?0:c>d?-1:1:a>b?-1:1},_.extend(g,{template:wp.template,post:wp.ajax.post,ajax:wp.ajax.send,fit:function(a){var b,c=a.width,d=a.height,e=a.maxWidth,f=a.maxHeight;return _.isUndefined(e)||_.isUndefined(f)?_.isUndefined(f)?b="width":_.isUndefined(e)&&d>f&&(b="height"):b=c/d>e/f?"width":"height","width"===b&&c>e?{width:e,height:Math.round(e*d/c)}:"height"===b&&d>f?{width:Math.round(f*c/d),height:f}:{width:c,height:d}},truncate:function(a,b,c){return b=b||30,c=c||"&hellip;",a.length<=b?a:a.substr(0,b/2)+c+a.substr(-1*b/2)}}),g.attachment=function(a){return d.get(a)},e.all=new e,g.query=function(a){return new e(null,{props:_.extend(_.defaults(a||{},{orderby:"date"}),{query:!0})})},h(window).on("unload",function(){window.wp=null})},24:function(a,b){var c,d=Backbone.$;c=Backbone.Model.extend({sync:function(a,b,c){return _.isUndefined(this.id)?d.Deferred().rejectWith(this).promise():"read"===a?(c=c||{},c.context=this,c.data=_.extend(c.data||{},{action:"get-attachment",id:this.id}),wp.media.ajax(c)):"update"===a?this.get("nonces")&&this.get("nonces").update?(c=c||{},c.context=this,c.data=_.extend(c.data||{},{action:"save-attachment",id:this.id,nonce:this.get("nonces").update,post_id:wp.media.model.settings.post.id}),b.hasChanged()&&(c.data.changes={},_.each(b.changed,function(a,b){c.data.changes[b]=this.get(b)},this)),wp.media.ajax(c)):d.Deferred().rejectWith(this).promise():"delete"===a?(c=c||{},c.wait||(this.destroyed=!0),c.context=this,c.data=_.extend(c.data||{},{action:"delete-post",id:this.id,_wpnonce:this.get("nonces")["delete"]}),wp.media.ajax(c).done(function(){this.destroyed=!0}).fail(function(){this.destroyed=!1})):Backbone.Model.prototype.sync.apply(this,arguments)},parse:function(a){return a?(a.date=new Date(a.date),a.modified=new Date(a.modified),a):a},saveCompat:function(a,b){var c=this;return this.get("nonces")&&this.get("nonces").update?wp.media.post("save-attachment-compat",_.defaults({id:this.id,nonce:this.get("nonces").update,post_id:wp.media.model.settings.post.id},a)).done(function(a,d,e){c.set(c.parse(a,e),b)}):d.Deferred().rejectWith(this).promise()}},{create:function(a){var b=wp.media.model.Attachments;return b.all.push(a)},get:_.memoize(function(a,b){var c=wp.media.model.Attachments;return c.all.push(b||{id:a})})}),a.exports=c},25:function(a,b){var c=Backbone.Collection.extend({model:wp.media.model.Attachment,initialize:function(a,b){b=b||{},this.props=new Backbone.Model,this.filters=b.filters||{},this.props.on("change",this._changeFilteredProps,this),this.props.on("change:order",this._changeOrder,this),this.props.on("change:orderby",this._changeOrderby,this),this.props.on("change:query",this._changeQuery,this),this.props.set(_.defaults(b.props||{})),b.observe&&this.observe(b.observe)},_changeOrder:function(){this.comparator&&this.sort()},_changeOrderby:function(a,b){this.comparator&&this.comparator!==c.comparator||(b&&"post__in"!==b?this.comparator=c.comparator:delete this.comparator)},_changeQuery:function(a,b){b?(this.props.on("change",this._requery,this),this._requery()):this.props.off("change",this._requery,this)},_changeFilteredProps:function(a){if(!this.props.get("query")){var b=_.chain(a.changed).map(function(b,d){var e=c.filters[d],f=a.get(d);if(e){if(f&&!this.filters[d])this.filters[d]=e;else{if(f||this.filters[d]!==e)return;delete this.filters[d]}return!0}},this).any().value();b&&(this._source||(this._source=new c(this.models)),this.reset(this._source.filter(this.validator,this)))}},validateDestroyed:!1,validator:function(a){return!(!_.isUndefined(a.attributes.context)&&""!==a.attributes.context)&&(!(!this.validateDestroyed&&a.destroyed)&&_.all(this.filters,function(b){return!!b.call(this,a)},this))},validate:function(a,b){var c=this.validator(a),d=!!this.get(a.cid);return!c&&d?this.remove(a,b):c&&!d&&this.add(a,b),this},validateAll:function(a,b){return b=b||{},_.each(a.models,function(a){this.validate(a,{silent:!0})},this),b.silent||this.trigger("reset",this,b),this},observe:function(a){return this.observers=this.observers||[],this.observers.push(a),a.on("add change remove",this._validateHandler,this),a.on("reset",this._validateAllHandler,this),this.validateAll(a),this},unobserve:function(a){return a?(a.off(null,null,this),this.observers=_.without(this.observers,a)):(_.each(this.observers,function(a){a.off(null,null,this)},this),delete this.observers),this},_validateHandler:function(a,b,c){return c=b===this.mirroring?c:{silent:c&&c.silent},this.validate(a,c)},_validateAllHandler:function(a,b){return this.validateAll(a,b)},mirror:function(a){return this.mirroring&&this.mirroring===a?this:(this.unmirror(),this.mirroring=a,this.reset([],{silent:!0}),this.observe(a),this)},unmirror:function(){this.mirroring&&(this.unobserve(this.mirroring),delete this.mirroring)},more:function(a){var b=jQuery.Deferred(),c=this.mirroring,d=this;return c&&c.more?(c.more(a).done(function(){this===d.mirroring&&b.resolveWith(this)}),b.promise()):b.resolveWith(this).promise()},hasMore:function(){return!!this.mirroring&&this.mirroring.hasMore()},parse:function(a,b){return _.isArray(a)||(a=[a]),_.map(a,function(a){var c,d,e;return a instanceof Backbone.Model?(c=a.get("id"),a=a.attributes):c=a.id,d=wp.media.model.Attachment.get(c),e=d.parse(a,b),_.isEqual(d.attributes,e)||d.set(e),d})},_requery:function(a){var b;this.props.get("query")&&(b=this.props.toJSON(),b.cache=!0!==a,this.mirror(wp.media.model.Query.get(b)))},saveMenuOrder:function(){if("menuOrder"===this.props.get("orderby")){var a=this.chain().filter(function(a){return!_.isUndefined(a.id)}).map(function(a,b){return b+=1,a.set("menuOrder",b),[a.id,b]}).object().value();if(!_.isEmpty(a))return wp.media.post("save-attachment-order",{nonce:wp.media.model.settings.post.nonce,post_id:wp.media.model.settings.post.id,attachments:a})}}},{comparator:function(a,b,c){var d=this.props.get("orderby"),e=this.props.get("order")||"DESC",f=a.cid,g=b.cid;return a=a.get(d),b=b.get(d),"date"!==d&&"modified"!==d||(a=a||new Date,b=b||new Date),c&&c.ties&&(f=g=null),"DESC"===e?wp.media.compare(a,b,f,g):wp.media.compare(b,a,g,f)},filters:{search:function(a){return!this.props.get("search")||_.any(["title","filename","description","caption","name"],function(b){var c=a.get(b);return c&&-1!==c.search(this.props.get("search"))},this)},type:function(a){var b,c,d=this.props.get("type"),e=a.toJSON();return!(d&&(!_.isArray(d)||d.length))||(b=e.mime||e.file&&e.file.type||"",c=_.isArray(d)?_.find(d,function(a){return-1!==b.indexOf(a)}):-1!==b.indexOf(d))},uploadedTo:function(a){var b=this.props.get("uploadedTo");return!!_.isUndefined(b)||b===a.get("uploadedTo")},status:function(a){var b=this.props.get("status");return!!_.isUndefined(b)||b===a.get("status")}}});a.exports=c},26:function(a,b){var c,d=wp.media.model.Attachments;c=d.extend({initialize:function(a,b){var c;b=b||{},d.prototype.initialize.apply(this,arguments),this.args=b.args,this._hasMore=!0,this.created=new Date,this.filters.order=function(a){var b=this.props.get("orderby"),c=this.props.get("order");return!this.comparator||(this.length?1!==this.comparator(a,this.last(),{ties:!0}):"DESC"!==c||"date"!==b&&"modified"!==b?"ASC"===c&&"menuOrder"===b&&0===a.get(b):a.get(b)>=this.created)},c=["s","order","orderby","posts_per_page","post_mime_type","post_parent","author"],wp.Uploader&&_(this.args).chain().keys().difference(c).isEmpty().value()&&this.observe(wp.Uploader.queue)},hasMore:function(){return this._hasMore},more:function(a){var b=this;return this._more&&"pending"===this._more.state()?this._more:this.hasMore()?(a=a||{},a.remove=!1,this._more=this.fetch(a).done(function(a){(_.isEmpty(a)||-1===this.args.posts_per_page||a.length<this.args.posts_per_page)&&(b._hasMore=!1)})):jQuery.Deferred().resolveWith(this).promise()},sync:function(a,b,c){var e,f;return"read"===a?(c=c||{},c.context=this,c.data=_.extend(c.data||{},{action:"query-attachments",post_id:wp.media.model.settings.post.id}),e=_.clone(this.args),-1!==e.posts_per_page&&(e.paged=Math.round(this.length/e.posts_per_page)+1),c.data.query=e,wp.media.ajax(c)):(f=d.prototype.sync?d.prototype:Backbone,f.sync.apply(this,arguments))}},{defaultProps:{orderby:"date",order:"DESC"},defaultArgs:{posts_per_page:40},orderby:{allowed:["name","author","date","title","modified","uploadedTo","id","post__in","menuOrder"],valuemap:{id:"ID",uploadedTo:"parent",menuOrder:"menu_order ID"}},propmap:{search:"s",type:"post_mime_type",perPage:"posts_per_page",menuOrder:"menu_order",uploadedTo:"post_parent",status:"post_status",include:"post__in",exclude:"post__not_in",author:"author"},get:function(){var a=[];return function(b,d){var e,f={},g=c.orderby,h=c.defaultProps,i=!!b.cache||_.isUndefined(b.cache);return delete b.query,delete b.cache,_.defaults(b,h),b.order=b.order.toUpperCase(),"DESC"!==b.order&&"ASC"!==b.order&&(b.order=h.order.toUpperCase()),_.contains(g.allowed,b.orderby)||(b.orderby=h.orderby),_.each(["include","exclude"],function(a){b[a]&&!_.isArray(b[a])&&(b[a]=[b[a]])}),_.each(b,function(a,b){_.isNull(a)||(f[c.propmap[b]||b]=a)}),_.defaults(f,c.defaultArgs),f.orderby=g.valuemap[b.orderby]||b.orderby,i?e=_.find(a,function(a){return _.isEqual(a.args,f)}):a=[],e||(e=new c([],_.extend(d||{},{props:b,args:f})),a.push(e)),e}}()}),a.exports=c},27:function(a,b){var c=Backbone.Model.extend({initialize:function(a){var b=wp.media.model.Attachment;this.attachment=!1,a.attachment_id&&(this.attachment=b.get(a.attachment_id),this.attachment.get("url")?(this.dfd=jQuery.Deferred(),this.dfd.resolve()):this.dfd=this.attachment.fetch(),this.bindAttachmentListeners()),this.on("change:link",this.updateLinkUrl,this),this.on("change:size",this.updateSize,this),this.setLinkTypeFromUrl(),this.setAspectRatio(),this.set("originalUrl",a.url)},bindAttachmentListeners:function(){this.listenTo(this.attachment,"sync",this.setLinkTypeFromUrl),this.listenTo(this.attachment,"sync",this.setAspectRatio),this.listenTo(this.attachment,"change",this.updateSize)},changeAttachment:function(a,b){this.stopListening(this.attachment),this.attachment=a,this.bindAttachmentListeners(),this.set("attachment_id",this.attachment.get("id")),this.set("caption",this.attachment.get("caption")),this.set("alt",this.attachment.get("alt")),this.set("size",b.get("size")),this.set("align",b.get("align")),this.set("link",b.get("link")),this.updateLinkUrl(),this.updateSize()},setLinkTypeFromUrl:function(){var a,b=this.get("linkUrl");return b?(a="custom",this.attachment?this.attachment.get("url")===b?a="file":this.attachment.get("link")===b&&(a="post"):this.get("url")===b&&(a="file"),void this.set("link",a)):void this.set("link","none")},updateLinkUrl:function(){var a,b=this.get("link");switch(b){case"file":a=this.attachment?this.attachment.get("url"):this.get("url"),this.set("linkUrl",a);break;case"post":this.set("linkUrl",this.attachment.get("link"));break;case"none":this.set("linkUrl","")}},updateSize:function(){var a;if(this.attachment){if("custom"===this.get("size"))return this.set("width",this.get("customWidth")),this.set("height",this.get("customHeight")),void this.set("url",this.get("originalUrl"));a=this.attachment.get("sizes")[this.get("size")],a&&(this.set("url",a.url),this.set("width",a.width),this.set("height",a.height))}},setAspectRatio:function(){var a;return this.attachment&&this.attachment.get("sizes")&&(a=this.attachment.get("sizes").full)?void this.set("aspectRatio",a.width/a.height):void this.set("aspectRatio",this.get("customWidth")/this.get("customHeight"))}});a.exports=c},28:function(a,b){var c,d=wp.media.model.Attachments;c=d.extend({initialize:function(a,b){d.prototype.initialize.apply(this,arguments),this.multiple=b&&b.multiple,this.on("add remove reset",_.bind(this.single,this,!1))},add:function(a,b){return this.multiple||this.remove(this.models),d.prototype.add.call(this,a,b)},single:function(a){var b=this._single;return a&&(this._single=a),this._single&&!this.get(this._single.cid)&&delete this._single,this._single=this._single||this.last(),this._single!==b&&(b&&(b.trigger("selection:unsingle",b,this),this.get(b.cid)||this.trigger("selection:unsingle",b,this)),this._single&&this._single.trigger("selection:single",this._single,this)),this._single}}),a.exports=c}});