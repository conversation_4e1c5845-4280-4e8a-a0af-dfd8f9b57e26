!function(a){function b(d){if(c[d])return c[d].exports;var e=c[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,b),e.l=!0,e.exports}var c={};return b.m=a,b.c=c,b.d=function(a,c,d){b.o(a,c)||Object.defineProperty(a,c,{enumerable:!0,get:d})},b.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},b.t=function(a,c){if(1&c&&(a=b(a)),8&c)return a;if(4&c&&"object"==typeof a&&a&&a.__esModule)return a;var d=Object.create(null);if(b.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&c&&"string"!=typeof a)for(var e in a)b.d(d,e,function(b){return a[b]}.bind(null,e));return d},b.n=function(a){var c=a&&a.__esModule?function(){return a["default"]}:function(){return a};return b.d(c,"a",c),c},b.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},b.p="",b(b.s=29)}(Array(29).concat([function(a,b,c){a.exports=c(30)},function(a,b,c){var d,e=wp.media,f=jQuery;e.isTouchDevice="ontouchend"in document,d=e.view.l10n=window._wpMediaViewsL10n||{},e.view.settings=d.settings||{},delete d.settings,e.model.settings.post=e.view.settings.post,f.support.transition=function(){var a,b=document.documentElement.style,c={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};return a=_.find(_.keys(c),function(a){return!_.isUndefined(b[a])}),a&&{end:c[a]}}(),e.events=_.extend({},Backbone.Events),e.transition=function(a,b){var c=f.Deferred();return b=b||2e3,f.support.transition?(a instanceof f||(a=f(a)),a.first().one(f.support.transition.end,c.resolve),_.delay(c.resolve,b)):c.resolve(),c.promise()},e.controller.Region=c(31),e.controller.StateMachine=c(32),e.controller.State=c(33),e.selectionSync=c(34),e.controller.Library=c(35),e.controller.ImageDetails=c(36),e.controller.GalleryEdit=c(37),e.controller.GalleryAdd=c(38),e.controller.CollectionEdit=c(39),e.controller.CollectionAdd=c(40),e.controller.FeaturedImage=c(41),e.controller.ReplaceImage=c(42),e.controller.EditImage=c(43),e.controller.MediaLibrary=c(44),e.controller.Embed=c(45),e.controller.Cropper=c(46),e.controller.CustomizeImageCropper=c(47),e.controller.SiteIconCropper=c(48),e.View=c(49),e.view.Frame=c(50),e.view.MediaFrame=c(51),e.view.MediaFrame.Select=c(52),e.view.MediaFrame.Post=c(53),e.view.MediaFrame.ImageDetails=c(54),e.view.Modal=c(55),e.view.FocusManager=c(56),e.view.UploaderWindow=c(57),e.view.EditorUploader=c(58),e.view.UploaderInline=c(59),e.view.UploaderStatus=c(60),e.view.UploaderStatusError=c(61),e.view.Toolbar=c(62),e.view.Toolbar.Select=c(63),e.view.Toolbar.Embed=c(64),e.view.Button=c(65),e.view.ButtonGroup=c(66),e.view.PriorityList=c(67),e.view.MenuItem=c(68),e.view.Menu=c(69),e.view.RouterItem=c(70),e.view.Router=c(71),e.view.Sidebar=c(72),e.view.Attachment=c(73),e.view.Attachment.Library=c(74),e.view.Attachment.EditLibrary=c(75),e.view.Attachments=c(76),e.view.Search=c(77),e.view.AttachmentFilters=c(78),e.view.DateFilter=c(79),e.view.AttachmentFilters.Uploaded=c(80),e.view.AttachmentFilters.All=c(81),e.view.AttachmentsBrowser=c(82),e.view.Selection=c(83),e.view.Attachment.Selection=c(84),e.view.Attachments.Selection=c(85),e.view.Attachment.EditSelection=c(86),e.view.Settings=c(87),e.view.Settings.AttachmentDisplay=c(88),e.view.Settings.Gallery=c(89),e.view.Settings.Playlist=c(90),e.view.Attachment.Details=c(91),e.view.AttachmentCompat=c(92),e.view.Iframe=c(93),e.view.Embed=c(94),e.view.Label=c(95),e.view.EmbedUrl=c(96),e.view.EmbedLink=c(97),e.view.EmbedImage=c(98),e.view.ImageDetails=c(99),e.view.Cropper=c(100),e.view.SiteIconCropper=c(101),e.view.SiteIconPreview=c(102),e.view.EditImage=c(103),e.view.Spinner=c(104)},function(a,b){var c=function(a){_.extend(this,_.pick(a||{},"id","view","selector"))};c.extend=Backbone.Model.extend,_.extend(c.prototype,{mode:function(a){return a?a===this._mode?this:(this.trigger("deactivate"),this._mode=a,this.render(a),this.trigger("activate"),this):this._mode},render:function(a){if(a&&a!==this._mode)return this.mode(a);var b,c={view:null};return this.trigger("create",c),b=c.view,this.trigger("render",b),b&&this.set(b),this},get:function(){return this.view.views.first(this.selector)},set:function(a,b){return b&&(b.add=!1),this.view.views.set(this.selector,a,b)},trigger:function(a){var b,c;if(this._mode)return c=_.toArray(arguments),b=this.id+":"+a,c[0]=b+":"+this._mode,this.view.trigger.apply(this.view,c),c[0]=b,this.view.trigger.apply(this.view,c),this}}),a.exports=c},function(a,b){var c=function(a){this.states=new Backbone.Collection(a)};c.extend=Backbone.Model.extend,_.extend(c.prototype,Backbone.Events,{state:function(a){return this.states=this.states||new Backbone.Collection,a=a||this._state,a&&!this.states.get(a)&&this.states.add({id:a}),this.states.get(a)},setState:function(a){var b=this.state();return b&&a===b.id||!this.states||!this.states.get(a)?this:(b&&(b.trigger("deactivate"),this._lastState=b.id),this._state=a,this.state().trigger("activate"),this)},lastState:function(){if(this._lastState)return this.state(this._lastState)}}),_.each(["on","off","trigger"],function(a){c.prototype[a]=function(){return this.states=this.states||new Backbone.Collection,this.states[a].apply(this.states,arguments),this}}),a.exports=c},function(a,b){var c=Backbone.Model.extend({constructor:function(){this.on("activate",this._preActivate,this),this.on("activate",this.activate,this),this.on("activate",this._postActivate,this),this.on("deactivate",this._deactivate,this),this.on("deactivate",this.deactivate,this),this.on("reset",this.reset,this),this.on("ready",this._ready,this),this.on("ready",this.ready,this),Backbone.Model.apply(this,arguments),this.on("change:menu",this._updateMenu,this)},ready:function(){},activate:function(){},deactivate:function(){},reset:function(){},_ready:function(){this._updateMenu()},_preActivate:function(){this.active=!0},_postActivate:function(){this.on("change:menu",this._menu,this),this.on("change:titleMode",this._title,this),this.on("change:content",this._content,this),this.on("change:toolbar",this._toolbar,this),this.frame.on("title:render:default",this._renderTitle,this),this._title(),this._menu(),this._toolbar(),this._content(),this._router()},_deactivate:function(){this.active=!1,this.frame.off("title:render:default",this._renderTitle,this),this.off("change:menu",this._menu,this),this.off("change:titleMode",this._title,this),this.off("change:content",this._content,this),this.off("change:toolbar",this._toolbar,this)},_title:function(){this.frame.title.render(this.get("titleMode")||"default")},_renderTitle:function(a){a.$el.text(this.get("title")||"")},_router:function(){var a,b=this.frame.router,c=this.get("router");this.frame.$el.toggleClass("hide-router",!c),c&&(this.frame.router.render(c),a=b.get(),a&&a.select&&a.select(this.frame.content.mode()))},_menu:function(){var a,b=this.frame.menu,c=this.get("menu");this.frame.$el.toggleClass("hide-menu",!c),c&&(b.mode(c),a=b.get(),a&&a.select&&a.select(this.id))},_updateMenu:function(){var a=this.previous("menu"),b=this.get("menu");a&&this.frame.off("menu:render:"+a,this._renderMenu,this),b&&this.frame.on("menu:render:"+b,this._renderMenu,this)},_renderMenu:function(a){var b=this.get("menuItem"),c=this.get("title"),d=this.get("priority");!b&&c&&(b={text:c},d&&(b.priority=d)),b&&a.set(this.id,b)}});_.each(["toolbar","content"],function(a){c.prototype["_"+a]=function(){var b=this.get(a);b&&this.frame[a].render(b)}}),a.exports=c},function(a,b){var c={syncSelection:function(){var a=this.get("selection"),b=this.frame._selection;this.get("syncSelection")&&b&&a&&(a.multiple&&(a.reset([],{silent:!0}),a.validateAll(b.attachments),b.difference=_.difference(b.attachments.models,a.models)),a.single(b.single))},recordSelection:function(){var a=this.get("selection"),b=this.frame._selection;this.get("syncSelection")&&b&&a&&(a.multiple?(b.attachments.reset(a.toArray().concat(b.difference)),b.difference=[]):b.attachments.add(a.toArray()),b.single=a._single)}};a.exports=c},function(a,b){var c,d=wp.media.view.l10n,e=window.getUserSetting,f=window.setUserSetting;c=wp.media.controller.State.extend({defaults:{id:"library",title:d.mediaLibraryTitle,multiple:!1,content:"upload",menu:"default",router:"browse",toolbar:"select",searchable:!0,filterable:!1,sortable:!0,autoSelect:!0,describe:!1,contentUserSetting:!0,syncSelection:!0},initialize:function(){var a,b=this.get("selection");this.get("library")||this.set("library",wp.media.query()),b instanceof wp.media.model.Selection||(a=b,a||(a=this.get("library").props.toJSON(),a=_.omit(a,"orderby","query")),this.set("selection",new wp.media.model.Selection(null,{multiple:this.get("multiple"),props:a}))),this.resetDisplays()},activate:function(){this.syncSelection(),wp.Uploader.queue.on("add",this.uploading,this),this.get("selection").on("add remove reset",this.refreshContent,this),this.get("router")&&this.get("contentUserSetting")&&(this.frame.on("content:activate",this.saveContentMode,this),this.set("content",e("libraryContent",this.get("content"))))},deactivate:function(){this.recordSelection(),this.frame.off("content:activate",this.saveContentMode,this),this.get("selection").off(null,null,this),wp.Uploader.queue.off(null,null,this)},reset:function(){this.get("selection").reset(),this.resetDisplays(),this.refreshContent()},resetDisplays:function(){var a=wp.media.view.settings.defaultProps;this._displays=[],this._defaultDisplaySettings={align:e("align",a.align)||"none",size:e("imgsize",a.size)||"medium",link:e("urlbutton",a.link)||"none"}},display:function(a){var b=this._displays;return b[a.cid]||(b[a.cid]=new Backbone.Model(this.defaultDisplaySettings(a))),b[a.cid]},defaultDisplaySettings:function(a){var b=_.clone(this._defaultDisplaySettings);return(b.canEmbed=this.canEmbed(a))?b.link="embed":this.isImageAttachment(a)||"none"!==b.link||(b.link="file"),b},isImageAttachment:function(a){return a.get("uploading")?/\.(jpe?g|png|gif)$/i.test(a.get("filename")):"image"===a.get("type")},canEmbed:function(a){if(!a.get("uploading")){var b=a.get("type");if("audio"!==b&&"video"!==b)return!1}return _.contains(wp.media.view.settings.embedExts,a.get("filename").split(".").pop())},refreshContent:function(){var a=this.get("selection"),b=this.frame,c=b.router.get(),d=b.content.mode();this.active&&!a.length&&c&&!c.get(d)&&this.frame.content.render(this.get("content"))},uploading:function(a){var b=this.frame.content;"upload"===b.mode()&&this.frame.content.mode("browse"),this.get("autoSelect")&&(this.get("selection").add(a),this.frame.trigger("library:selection:add"))},saveContentMode:function(){if("browse"===this.get("router")){var a=this.frame.content.mode(),b=this.frame.router.get();b&&b.get(a)&&f("libraryContent",a)}}}),_.extend(c.prototype,wp.media.selectionSync),a.exports=c},function(a,b){var c,d=wp.media.controller.State,e=wp.media.controller.Library,f=wp.media.view.l10n;c=d.extend({defaults:_.defaults({id:"image-details",title:f.imageDetailsTitle,content:"image-details",menu:!1,router:!1,toolbar:"image-details",editing:!1,priority:60},e.prototype.defaults),initialize:function(a){this.image=a.image,d.prototype.initialize.apply(this,arguments)},activate:function(){this.frame.modal.$el.addClass("image-details")}}),a.exports=c},function(a,b){var c,d=wp.media.controller.Library,e=wp.media.view.l10n;c=d.extend({defaults:{id:"gallery-edit",title:e.editGalleryTitle,multiple:!1,searchable:!1,sortable:!0,date:!1,display:!1,content:"browse",toolbar:"gallery-edit",describe:!0,displaySettings:!0,dragInfo:!0,idealColumnWidth:170,editing:!1,priority:60,syncSelection:!1},initialize:function(){this.get("library")||this.set("library",new wp.media.model.Selection),this.get("AttachmentView")||this.set("AttachmentView",wp.media.view.Attachment.EditLibrary),d.prototype.initialize.apply(this,arguments)},activate:function(){var a=this.get("library");a.props.set("type","image"),this.get("library").observe(wp.Uploader.queue),this.frame.on("content:render:browse",this.gallerySettings,this),d.prototype.activate.apply(this,arguments)},deactivate:function(){this.get("library").unobserve(wp.Uploader.queue),this.frame.off("content:render:browse",this.gallerySettings,this),d.prototype.deactivate.apply(this,arguments)},gallerySettings:function(a){if(this.get("displaySettings")){var b=this.get("library");b&&a&&(b.gallery=b.gallery||new Backbone.Model,a.sidebar.set({gallery:new wp.media.view.Settings.Gallery({controller:this,model:b.gallery,priority:40})}),a.toolbar.set("reverse",{text:e.reverseOrder,priority:80,click:function(){b.reset(b.toArray().reverse())}}))}}}),a.exports=c},function(a,b){var c,d=wp.media.model.Selection,e=wp.media.controller.Library,f=wp.media.view.l10n;c=e.extend({defaults:_.defaults({id:"gallery-library",title:f.addToGalleryTitle,multiple:"add",filterable:"uploaded",menu:"gallery",toolbar:"gallery-add",priority:100,syncSelection:!1},e.prototype.defaults),initialize:function(){this.get("library")||this.set("library",wp.media.query({type:"image"})),e.prototype.initialize.apply(this,arguments)},activate:function(){var a=this.get("library"),b=this.frame.state("gallery-edit").get("library");this.editLibrary&&this.editLibrary!==b&&a.unobserve(this.editLibrary),a.validator=function(a){return!!this.mirroring.get(a.cid)&&!b.get(a.cid)&&d.prototype.validator.apply(this,arguments)},a.reset(a.mirroring.models,{silent:!0}),a.observe(b),this.editLibrary=b,e.prototype.activate.apply(this,arguments)}}),a.exports=c},function(a,b){var c,d=wp.media.controller.Library,e=wp.media.view.l10n,f=jQuery;c=d.extend({defaults:{multiple:!1,sortable:!0,date:!1,searchable:!1,content:"browse",describe:!0,dragInfo:!0,idealColumnWidth:170,editing:!1,priority:60,SettingsView:!1,syncSelection:!1},initialize:function(){var a=this.get("collectionType");"video"===this.get("type")&&(a="video-"+a),this.set("id",a+"-edit"),this.set("toolbar",a+"-edit"),this.get("library")||this.set("library",new wp.media.model.Selection),this.get("AttachmentView")||this.set("AttachmentView",wp.media.view.Attachment.EditLibrary),d.prototype.initialize.apply(this,arguments)},activate:function(){var a=this.get("library");a.props.set("type",this.get("type")),this.get("library").observe(wp.Uploader.queue),this.frame.on("content:render:browse",this.renderSettings,this),d.prototype.activate.apply(this,arguments)},deactivate:function(){this.get("library").unobserve(wp.Uploader.queue),this.frame.off("content:render:browse",this.renderSettings,this),d.prototype.deactivate.apply(this,arguments)},renderSettings:function(a){var b=this.get("library"),c=this.get("collectionType"),d=this.get("dragInfoText"),g=this.get("SettingsView"),h={};b&&a&&(b[c]=b[c]||new Backbone.Model,h[c]=new g({controller:this,model:b[c],priority:40}),a.sidebar.set(h),d&&a.toolbar.set("dragInfo",new wp.media.View({el:f('<div class="instructions">'+d+"</div>")[0],priority:-40})),a.toolbar.set("reverse",{text:e.reverseOrder,priority:80,click:function(){b.reset(b.toArray().reverse())}}))}}),a.exports=c},function(a,b){var c,d=wp.media.model.Selection,e=wp.media.controller.Library;c=e.extend({defaults:_.defaults({multiple:"add",filterable:"uploaded",priority:100,syncSelection:!1},e.prototype.defaults),initialize:function(){var a=this.get("collectionType");"video"===this.get("type")&&(a="video-"+a),this.set("id",a+"-library"),this.set("toolbar",a+"-add"),this.set("menu",a),this.get("library")||this.set("library",wp.media.query({type:this.get("type")})),e.prototype.initialize.apply(this,arguments)},activate:function(){var a=this.get("library"),b=this.get("editLibrary"),c=this.frame.state(this.get("collectionType")+"-edit").get("library");b&&b!==c&&a.unobserve(b),a.validator=function(a){return!!this.mirroring.get(a.cid)&&!c.get(a.cid)&&d.prototype.validator.apply(this,arguments)},a.reset(a.mirroring.models,{silent:!0}),a.observe(c),this.set("editLibrary",c),e.prototype.activate.apply(this,arguments)}}),a.exports=c},function(a,b){var c,d=wp.media.model.Attachment,e=wp.media.controller.Library,f=wp.media.view.l10n;c=e.extend({defaults:_.defaults({id:"featured-image",title:f.setFeaturedImageTitle,multiple:!1,filterable:"uploaded",toolbar:"featured-image",priority:60,syncSelection:!0},e.prototype.defaults),initialize:function(){var a,b;this.get("library")||this.set("library",wp.media.query({type:"image"})),e.prototype.initialize.apply(this,arguments),a=this.get("library"),b=a.comparator,a.comparator=function(a,c){var d=!!this.mirroring.get(a.cid),e=!!this.mirroring.get(c.cid);return!d&&e?-1:d&&!e?1:b.apply(this,arguments)},a.observe(this.get("selection"))},activate:function(){this.updateSelection(),this.frame.on("open",this.updateSelection,this),e.prototype.activate.apply(this,arguments)},deactivate:function(){this.frame.off("open",this.updateSelection,this),e.prototype.deactivate.apply(this,arguments)},updateSelection:function(){var a,b=this.get("selection"),c=wp.media.view.settings.post.featuredImageId;""!==c&&-1!==c&&(a=d.get(c),a.fetch()),b.reset(a?[a]:[])}}),a.exports=c},function(a,b){var c,d=wp.media.controller.Library,e=wp.media.view.l10n;c=d.extend({defaults:_.defaults({id:"replace-image",title:e.replaceImageTitle,multiple:!1,filterable:"uploaded",toolbar:"replace",menu:!1,priority:60,syncSelection:!0},d.prototype.defaults),initialize:function(a){var b,c;this.image=a.image,this.get("library")||this.set("library",wp.media.query({type:"image"})),d.prototype.initialize.apply(this,arguments),b=this.get("library"),c=b.comparator,b.comparator=function(a,b){var d=!!this.mirroring.get(a.cid),e=!!this.mirroring.get(b.cid);return!d&&e?-1:d&&!e?1:c.apply(this,arguments)},b.observe(this.get("selection"))},activate:function(){this.updateSelection(),d.prototype.activate.apply(this,arguments)},updateSelection:function(){var a=this.get("selection"),b=this.image.attachment;a.reset(b?[b]:[])}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.controller.State.extend({defaults:{id:"edit-image",title:d.editImage,menu:!1,toolbar:"edit-image",content:"edit-image",url:""},activate:function(){this.frame.on("toolbar:render:edit-image",_.bind(this.toolbar,this))},deactivate:function(){this.frame.off("toolbar:render:edit-image")},toolbar:function(){var a=this.frame,b=a.lastState(),c=b&&b.id;a.toolbar.set(new wp.media.view.Toolbar({controller:a,items:{back:{style:"primary",text:d.back,priority:20,click:function(){c?a.setState(c):a.close()}}}}))}}),a.exports=c},function(a,b){var c,d=wp.media.controller.Library;c=d.extend({defaults:_.defaults({filterable:"uploaded",displaySettings:!1,priority:80,syncSelection:!1},d.prototype.defaults),initialize:function(a){this.media=a.media,this.type=a.type,this.set("library",wp.media.query({type:this.type})),d.prototype.initialize.apply(this,arguments)},activate:function(){wp.media.frame.lastMime&&(this.set("library",wp.media.query({type:wp.media.frame.lastMime})),delete wp.media.frame.lastMime),d.prototype.activate.apply(this,arguments)}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n,e=Backbone.$;c=wp.media.controller.State.extend({defaults:{id:"embed",title:d.insertFromUrlTitle,content:"embed",menu:"default",toolbar:"main-embed",priority:120,type:"link",url:"",metadata:{}},sensitivity:400,initialize:function(a){this.metadata=a.metadata,this.debouncedScan=_.debounce(_.bind(this.scan,this),this.sensitivity),this.props=new Backbone.Model(this.metadata||{url:""}),this.props.on("change:url",this.debouncedScan,this),this.props.on("change:url",this.refresh,this),this.on("scan",this.scanImage,this)},scan:function(){var a,b=this,c={type:"link",scanners:[]};this.props.get("url")&&this.trigger("scan",c),c.scanners.length?(a=c.scanners=e.when.apply(e,c.scanners),a.always(function(){b.get("scanners")===a&&b.set("loading",!1)})):c.scanners=null,c.loading=!!c.scanners,this.set(c)},scanImage:function(a){var b=this.frame,c=this,d=this.props.get("url"),f=new Image,g=e.Deferred();a.scanners.push(g.promise()),f.onload=function(){g.resolve(),c===b.state()&&d===c.props.get("url")&&(c.set({type:"image"}),c.props.set({width:f.width,height:f.height}))},f.onerror=g.reject,f.src=d},refresh:function(){this.frame.toolbar.get().refresh()},reset:function(){this.props.clear().set({url:""}),this.active&&this.refresh()}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.controller.State.extend({defaults:{id:"cropper",title:d.cropImage,toolbar:"crop",content:"crop",router:!1,canSkipCrop:!1,doCropArgs:{}},activate:function(){this.frame.on("content:create:crop",this.createCropContent,this),this.frame.on("close",this.removeCropper,this),this.set("selection",new Backbone.Collection(this.frame._selection.single))},deactivate:function(){this.frame.toolbar.mode("browse")},createCropContent:function(){this.cropperView=new wp.media.view.Cropper({controller:this,attachment:this.get("selection").first()}),this.cropperView.on("image-loaded",this.createCropToolbar,this),this.frame.content.set(this.cropperView)},removeCropper:function(){this.imgSelect.cancelSelection(),this.imgSelect.setOptions({remove:!0}),this.imgSelect.update(),this.cropperView.remove()},createCropToolbar:function(){var a,b;a=this.get("canSkipCrop")||!1,b={controller:this.frame,items:{insert:{style:"primary",text:d.cropImage,priority:80,requires:{library:!1,selection:!1},click:function(){var a,b=this.controller;a=b.state().get("selection").first(),a.set({cropDetails:b.state().imgSelect.getSelection()}),this.$el.text(d.cropping),this.$el.attr("disabled",!0),b.state().doCrop(a).done(function(a){b.trigger("cropped",a),b.close()}).fail(function(){b.trigger("content:error:crop")})}}}},a&&_.extend(b.items,{skip:{style:"secondary",text:d.skipCropping,priority:70,requires:{library:!1,selection:!1},click:function(){var a=this.controller.state().get("selection").first();this.controller.state().cropperView.remove(),this.controller.trigger("skippedcrop",a),this.controller.close()}}}),this.frame.toolbar.set(new wp.media.view.Toolbar(b))},doCrop:function(a){return wp.ajax.post("custom-header-crop",_.extend({},this.defaults.doCropArgs,{nonce:a.get("nonces").edit,id:a.get("id"),cropDetails:a.get("cropDetails")}))}}),a.exports=c},function(a,b){var c,d=wp.media.controller;c=d.Cropper.extend({doCrop:function(a){var b=a.get("cropDetails"),c=this.get("control"),d=b.width/b.height;return c.params.flex_width&&c.params.flex_height?(b.dst_width=b.width,b.dst_height=b.height):(b.dst_width=c.params.flex_width?c.params.height*d:c.params.width,b.dst_height=c.params.flex_height?c.params.width/d:c.params.height),wp.ajax.post("crop-image",{wp_customize:"on",nonce:a.get("nonces").edit,id:a.get("id"),context:c.id,cropDetails:b})}}),a.exports=c},function(a,b){var c,d=wp.media.controller;c=d.Cropper.extend({activate:function(){this.frame.on("content:create:crop",this.createCropContent,this),this.frame.on("close",this.removeCropper,this),this.set("selection",new Backbone.Collection(this.frame._selection.single))},createCropContent:function(){this.cropperView=new wp.media.view.SiteIconCropper({controller:this,attachment:this.get("selection").first()}),this.cropperView.on("image-loaded",this.createCropToolbar,this),this.frame.content.set(this.cropperView)},doCrop:function(a){var b=a.get("cropDetails"),c=this.get("control");return b.dst_width=c.params.width,b.dst_height=c.params.height,wp.ajax.post("crop-image",{nonce:a.get("nonces").edit,id:a.get("id"),context:"site-icon",cropDetails:b})}}),a.exports=c},function(a,b){var c=wp.Backbone.View.extend({constructor:function(a){a&&a.controller&&(this.controller=a.controller),wp.Backbone.View.apply(this,arguments)},dispose:function(){return this.undelegateEvents(),this.model&&this.model.off&&this.model.off(null,null,this),this.collection&&this.collection.off&&this.collection.off(null,null,this),this.controller&&this.controller.off&&this.controller.off(null,null,this),this},remove:function(){return this.dispose(),wp.Backbone.View.prototype.remove.apply(this,arguments)}});a.exports=c},function(a,b){var c=wp.media.View.extend({initialize:function(){_.defaults(this.options,{mode:["select"]}),this._createRegions(),this._createStates(),this._createModes()},_createRegions:function(){this.regions=this.regions?this.regions.slice():[],_.each(this.regions,function(a){this[a]=new wp.media.controller.Region({view:this,id:a,selector:".media-frame-"+a})},this)},_createStates:function(){this.states=new Backbone.Collection(null,{model:wp.media.controller.State}),this.states.on("add",function(a){a.frame=this,a.trigger("ready")},this),this.options.states&&this.states.add(this.options.states)},_createModes:function(){this.activeModes=new Backbone.Collection,this.activeModes.on("add remove reset",_.bind(this.triggerModeEvents,this)),_.each(this.options.mode,function(a){this.activateMode(a)},this)},reset:function(){return this.states.invoke("trigger","reset"),this},triggerModeEvents:function(a,b,c){var d,e,f={add:"activate",remove:"deactivate"};_.each(c,function(a,b){a&&(d=b)}),_.has(f,d)&&(e=a.get("id")+":"+f[d],this.trigger(e))},activateMode:function(a){if(!this.isModeActive(a))return this.activeModes.add([{id:a}]),this.$el.addClass("mode-"+a),this},deactivateMode:function(a){return this.isModeActive(a)?(this.activeModes.remove(this.activeModes.where({id:a})),this.$el.removeClass("mode-"+a),this.trigger(a+":deactivate"),this):this},isModeActive:function(a){return Boolean(this.activeModes.where({id:a}).length)}});_.extend(c.prototype,wp.media.controller.StateMachine.prototype),a.exports=c},function(a,b){var c,d=wp.media.view.Frame,e=jQuery;c=d.extend({className:"media-frame",template:wp.template("media-frame"),regions:["menu","title","content","toolbar","router"],events:{"click div.media-frame-title h1":"toggleMenu"},initialize:function(){d.prototype.initialize.apply(this,arguments),_.defaults(this.options,{title:"",modal:!0,uploader:!0}),this.$el.addClass("wp-core-ui"),this.options.modal&&(this.modal=new wp.media.view.Modal({controller:this,title:this.options.title}),this.modal.content(this)),!wp.Uploader.limitExceeded&&wp.Uploader.browser.supported||(this.options.uploader=!1),this.options.uploader&&(this.uploader=new wp.media.view.UploaderWindow({controller:this,uploader:{dropzone:this.modal?this.modal.$el:this.$el,container:this.$el}}),this.views.set(".media-frame-uploader",this.uploader)),this.on("attach",_.bind(this.views.ready,this.views),this),this.on("title:create:default",this.createTitle,this),this.title.mode("default"),this.on("title:render",function(a){a.$el.append('<span class="dashicons dashicons-arrow-down"></span>')}),this.on("menu:create:default",this.createMenu,this)},render:function(){return!this.state()&&this.options.state&&this.setState(this.options.state),d.prototype.render.apply(this,arguments)},createTitle:function(a){a.view=new wp.media.View({controller:this,tagName:"h1"})},createMenu:function(a){a.view=new wp.media.view.Menu({controller:this})},toggleMenu:function(){this.$el.find(".media-menu").toggleClass("visible")},createToolbar:function(a){a.view=new wp.media.view.Toolbar({controller:this})},createRouter:function(a){a.view=new wp.media.view.Router({controller:this})},createIframeStates:function(a){var b,c=wp.media.view.settings,d=c.tabs,f=c.tabUrl;d&&f&&(b=e("#post_ID"),b.length&&(f+="&post_id="+b.val()),_.each(d,function(b,c){this.state("iframe:"+c).set(_.defaults({tab:c,src:f+"&tab="+c,title:b,content:"iframe",menu:"default"},a))},this),this.on("content:create:iframe",this.iframeContent,this),this.on("content:deactivate:iframe",this.iframeContentCleanup,this),this.on("menu:render:default",this.iframeMenu,this),this.on("open",this.hijackThickbox,this),this.on("close",this.restoreThickbox,this))},iframeContent:function(a){this.$el.addClass("hide-toolbar"),a.view=new wp.media.view.Iframe({controller:this})},iframeContentCleanup:function(){this.$el.removeClass("hide-toolbar")},iframeMenu:function(a){var b={};a&&(_.each(wp.media.view.settings.tabs,function(a,c){b["iframe:"+c]={text:this.state("iframe:"+c).get("title"),priority:200}},this),a.set(b))},hijackThickbox:function(){var a=this;window.tb_remove&&!this._tb_remove&&(this._tb_remove=window.tb_remove,window.tb_remove=function(){a.close(),a.reset(),a.setState(a.options.state),a._tb_remove.call(window)})},restoreThickbox:function(){this._tb_remove&&(window.tb_remove=this._tb_remove,delete this._tb_remove)}}),_.each(["open","close","attach","detach","escape"],function(a){c.prototype[a]=function(){return this.modal&&this.modal[a].apply(this.modal,arguments),this}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaFrame,e=wp.media.view.l10n;c=d.extend({initialize:function(){d.prototype.initialize.apply(this,arguments),_.defaults(this.options,{selection:[],library:{},multiple:!1,state:"library"}),this.createSelection(),this.createStates(),this.bindHandlers()},createSelection:function(){var a=this.options.selection;a instanceof wp.media.model.Selection||(this.options.selection=new wp.media.model.Selection(a,{multiple:this.options.multiple})),this._selection={attachments:new wp.media.model.Attachments,difference:[]}},createStates:function(){var a=this.options;this.options.states||this.states.add([new wp.media.controller.Library({library:wp.media.query(a.library),multiple:a.multiple,title:a.title,priority:20})])},bindHandlers:function(){this.on("router:create:browse",this.createRouter,this),this.on("router:render:browse",this.browseRouter,this),this.on("content:create:browse",this.browseContent,this),this.on("content:render:upload",this.uploadContent,this),this.on("toolbar:create:select",this.createSelectToolbar,this)},browseRouter:function(a){a.set({upload:{text:e.uploadFilesTitle,priority:20},browse:{text:e.mediaLibraryTitle,priority:40}})},browseContent:function(a){var b=this.state();this.$el.removeClass("hide-toolbar"),a.view=new wp.media.view.AttachmentsBrowser({controller:this,collection:b.get("library"),selection:b.get("selection"),model:b,sortable:b.get("sortable"),search:b.get("searchable"),filters:b.get("filterable"),date:b.get("date"),display:b.has("display")?b.get("display"):b.get("displaySettings"),dragInfo:b.get("dragInfo"),idealColumnWidth:b.get("idealColumnWidth"),suggestedWidth:b.get("suggestedWidth"),suggestedHeight:b.get("suggestedHeight"),AttachmentView:b.get("AttachmentView")})},uploadContent:function(){this.$el.removeClass("hide-toolbar"),this.content.set(new wp.media.view.UploaderInline({controller:this}))},createSelectToolbar:function(a,b){b=b||this.options.button||{},b.controller=this,a.view=new wp.media.view.Toolbar.Select(b)}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaFrame.Select,e=wp.media.controller.Library,f=wp.media.view.l10n;c=d.extend({initialize:function(){this.counts={audio:{count:wp.media.view.settings.attachmentCounts.audio,state:"playlist"},video:{count:wp.media.view.settings.attachmentCounts.video,state:"video-playlist"}},_.defaults(this.options,{multiple:!0,editing:!1,state:"insert",metadata:{}}),d.prototype.initialize.apply(this,arguments),this.createIframeStates()},createStates:function(){var a=this.options;this.states.add([new e({id:"insert",title:f.insertMediaTitle,priority:20,toolbar:"main-insert",filterable:"all",library:wp.media.query(a.library),multiple:!!a.multiple&&"reset",editable:!0,allowLocalEdits:!0,displaySettings:!0,displayUserSettings:!0}),new e({id:"gallery",title:f.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:wp.media.query(_.defaults({type:"image"},a.library))}),new wp.media.controller.Embed({metadata:a.metadata}),new wp.media.controller.EditImage({model:a.editImage}),new wp.media.controller.GalleryEdit({library:a.selection,editing:a.editing,menu:"gallery"}),new wp.media.controller.GalleryAdd,new e({id:"playlist",title:f.createPlaylistTitle,priority:60,toolbar:"main-playlist",filterable:"uploaded",multiple:"add",editable:!1,library:wp.media.query(_.defaults({type:"audio"},a.library))}),new wp.media.controller.CollectionEdit({type:"audio",
collectionType:"playlist",title:f.editPlaylistTitle,SettingsView:wp.media.view.Settings.Playlist,library:a.selection,editing:a.editing,menu:"playlist",dragInfoText:f.playlistDragInfo,dragInfo:!1}),new wp.media.controller.CollectionAdd({type:"audio",collectionType:"playlist",title:f.addToPlaylistTitle}),new e({id:"video-playlist",title:f.createVideoPlaylistTitle,priority:60,toolbar:"main-video-playlist",filterable:"uploaded",multiple:"add",editable:!1,library:wp.media.query(_.defaults({type:"video"},a.library))}),new wp.media.controller.CollectionEdit({type:"video",collectionType:"playlist",title:f.editVideoPlaylistTitle,SettingsView:wp.media.view.Settings.Playlist,library:a.selection,editing:a.editing,menu:"video-playlist",dragInfoText:f.videoPlaylistDragInfo,dragInfo:!1}),new wp.media.controller.CollectionAdd({type:"video",collectionType:"playlist",title:f.addToVideoPlaylistTitle})]),wp.media.view.settings.post.featuredImageId&&this.states.add(new wp.media.controller.FeaturedImage)},bindHandlers:function(){var a,b;d.prototype.bindHandlers.apply(this,arguments),this.on("activate",this.activate,this),b=_.find(this.counts,function(a){return 0===a.count}),"undefined"!=typeof b&&this.listenTo(wp.media.model.Attachments.all,"change:type",this.mediaTypeCounts),this.on("menu:create:gallery",this.createMenu,this),this.on("menu:create:playlist",this.createMenu,this),this.on("menu:create:video-playlist",this.createMenu,this),this.on("toolbar:create:main-insert",this.createToolbar,this),this.on("toolbar:create:main-gallery",this.createToolbar,this),this.on("toolbar:create:main-playlist",this.createToolbar,this),this.on("toolbar:create:main-video-playlist",this.createToolbar,this),this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.on("toolbar:create:main-embed",this.mainEmbedToolbar,this),a={menu:{"default":"mainMenu",gallery:"galleryMenu",playlist:"playlistMenu","video-playlist":"videoPlaylistMenu"},content:{embed:"embedContent","edit-image":"editImageContent","edit-selection":"editSelectionContent"},toolbar:{"main-insert":"mainInsertToolbar","main-gallery":"mainGalleryToolbar","gallery-edit":"galleryEditToolbar","gallery-add":"galleryAddToolbar","main-playlist":"mainPlaylistToolbar","playlist-edit":"playlistEditToolbar","playlist-add":"playlistAddToolbar","main-video-playlist":"mainVideoPlaylistToolbar","video-playlist-edit":"videoPlaylistEditToolbar","video-playlist-add":"videoPlaylistAddToolbar"}},_.each(a,function(a,b){_.each(a,function(a,c){this.on(b+":render:"+c,this[a],this)},this)},this)},activate:function(){_.each(this.counts,function(a){a.count<1&&this.menuItemVisibility(a.state,"hide")},this)},mediaTypeCounts:function(a,b){"undefined"!=typeof this.counts[b]&&this.counts[b].count<1&&(this.counts[b].count++,this.menuItemVisibility(this.counts[b].state,"show"))},mainMenu:function(a){a.set({"library-separator":new wp.media.View({className:"separator",priority:100})})},menuItemVisibility:function(a,b){var c=this.menu.get();"hide"===b?c.hide(a):"show"===b&&c.show(a)},galleryMenu:function(a){var b=this.lastState(),c=b&&b.id,d=this;a.set({cancel:{text:f.cancelGalleryTitle,priority:20,click:function(){c?d.setState(c):d.close(),this.controller.modal.focusManager.focus()}},separateCancel:new wp.media.View({className:"separator",priority:40})})},playlistMenu:function(a){var b=this.lastState(),c=b&&b.id,d=this;a.set({cancel:{text:f.cancelPlaylistTitle,priority:20,click:function(){c?d.setState(c):d.close()}},separateCancel:new wp.media.View({className:"separator",priority:40})})},videoPlaylistMenu:function(a){var b=this.lastState(),c=b&&b.id,d=this;a.set({cancel:{text:f.cancelVideoPlaylistTitle,priority:20,click:function(){c?d.setState(c):d.close()}},separateCancel:new wp.media.View({className:"separator",priority:40})})},embedContent:function(){var a=new wp.media.view.Embed({controller:this,model:this.state()}).render();this.content.set(a),wp.media.isTouchDevice||a.url.focus()},editSelectionContent:function(){var a,b=this.state(),c=b.get("selection");a=new wp.media.view.AttachmentsBrowser({controller:this,collection:c,selection:c,model:b,sortable:!0,search:!1,date:!1,dragInfo:!0,AttachmentView:wp.media.view.Attachments.EditSelection}).render(),a.toolbar.set("backToLibrary",{text:f.returnToLibrary,priority:-100,click:function(){this.controller.content.mode("browse")}}),this.content.set(a),this.trigger("edit:selection",this)},editImageContent:function(){var a=this.state().get("image"),b=new wp.media.view.EditImage({model:a,controller:this}).render();this.content.set(b),b.loadEditor()},selectionStatusToolbar:function(a){var b=this.state().get("editable");a.set("selection",new wp.media.view.Selection({controller:this,collection:this.state().get("selection"),priority:-40,editable:b&&function(){this.controller.content.mode("edit-selection")}}).render())},mainInsertToolbar:function(a){var b=this;this.selectionStatusToolbar(a),a.set("insert",{style:"primary",priority:80,text:f.insertIntoPost,requires:{selection:!0},click:function(){var a=b.state(),c=a.get("selection");b.close(),a.trigger("insert",c).reset()}})},mainGalleryToolbar:function(a){var b=this;this.selectionStatusToolbar(a),a.set("gallery",{style:"primary",text:f.createNewGallery,priority:60,requires:{selection:!0},click:function(){var a=b.state().get("selection"),c=b.state("gallery-edit"),d=a.where({type:"image"});c.set("library",new wp.media.model.Selection(d,{props:a.props.toJSON(),multiple:!0})),this.controller.setState("gallery-edit"),this.controller.modal.focusManager.focus()}})},mainPlaylistToolbar:function(a){var b=this;this.selectionStatusToolbar(a),a.set("playlist",{style:"primary",text:f.createNewPlaylist,priority:100,requires:{selection:!0},click:function(){var a=b.state().get("selection"),c=b.state("playlist-edit"),d=a.where({type:"audio"});c.set("library",new wp.media.model.Selection(d,{props:a.props.toJSON(),multiple:!0})),this.controller.setState("playlist-edit"),this.controller.modal.focusManager.focus()}})},mainVideoPlaylistToolbar:function(a){var b=this;this.selectionStatusToolbar(a),a.set("video-playlist",{style:"primary",text:f.createNewVideoPlaylist,priority:100,requires:{selection:!0},click:function(){var a=b.state().get("selection"),c=b.state("video-playlist-edit"),d=a.where({type:"video"});c.set("library",new wp.media.model.Selection(d,{props:a.props.toJSON(),multiple:!0})),this.controller.setState("video-playlist-edit"),this.controller.modal.focusManager.focus()}})},featuredImageToolbar:function(a){this.createSelectToolbar(a,{text:f.setFeaturedImage,state:this.options.state})},mainEmbedToolbar:function(a){a.view=new wp.media.view.Toolbar.Embed({controller:this})},galleryEditToolbar:function(){var a=this.state().get("editing");this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:a?f.updateGallery:f.insertGallery,priority:80,requires:{library:!0},click:function(){var a=this.controller,b=a.state();a.close(),b.trigger("update",b.get("library")),a.setState(a.options.state),a.reset()}}}}))},galleryAddToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:f.addToGallery,priority:80,requires:{selection:!0},click:function(){var a=this.controller,b=a.state(),c=a.state("gallery-edit");c.get("library").add(b.get("selection").models),b.trigger("reset"),a.setState("gallery-edit")}}}}))},playlistEditToolbar:function(){var a=this.state().get("editing");this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:a?f.updatePlaylist:f.insertPlaylist,priority:80,requires:{library:!0},click:function(){var a=this.controller,b=a.state();a.close(),b.trigger("update",b.get("library")),a.setState(a.options.state),a.reset()}}}}))},playlistAddToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:f.addToPlaylist,priority:80,requires:{selection:!0},click:function(){var a=this.controller,b=a.state(),c=a.state("playlist-edit");c.get("library").add(b.get("selection").models),b.trigger("reset"),a.setState("playlist-edit")}}}}))},videoPlaylistEditToolbar:function(){var a=this.state().get("editing");this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:a?f.updateVideoPlaylist:f.insertVideoPlaylist,priority:140,requires:{library:!0},click:function(){var a=this.controller,b=a.state(),c=b.get("library");c.type="video",a.close(),b.trigger("update",c),a.setState(a.options.state),a.reset()}}}}))},videoPlaylistAddToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:f.addToVideoPlaylist,priority:140,requires:{selection:!0},click:function(){var a=this.controller,b=a.state(),c=a.state("video-playlist-edit");c.get("library").add(b.get("selection").models),b.trigger("reset"),a.setState("video-playlist-edit")}}}}))}}),a.exports=c},function(a,b){var c,d=wp.media.view.MediaFrame.Select,e=wp.media.view.l10n;c=d.extend({defaults:{id:"image",url:"",menu:"image-details",content:"image-details",toolbar:"image-details",type:"link",title:e.imageDetailsTitle,priority:120},initialize:function(a){this.image=new wp.media.model.PostImage(a.metadata),this.options.selection=new wp.media.model.Selection(this.image.attachment,{multiple:!1}),d.prototype.initialize.apply(this,arguments)},bindHandlers:function(){d.prototype.bindHandlers.apply(this,arguments),this.on("menu:create:image-details",this.createMenu,this),this.on("content:create:image-details",this.imageDetailsContent,this),this.on("content:render:edit-image",this.editImageContent,this),this.on("toolbar:render:image-details",this.renderImageDetailsToolbar,this),this.on("toolbar:render:replace",this.renderReplaceImageToolbar,this)},createStates:function(){this.states.add([new wp.media.controller.ImageDetails({image:this.image,editable:!1}),new wp.media.controller.ReplaceImage({id:"replace-image",library:wp.media.query({type:"image"}),image:this.image,multiple:!1,title:e.imageReplaceTitle,toolbar:"replace",priority:80,displaySettings:!0}),new wp.media.controller.EditImage({image:this.image,selection:this.options.selection})])},imageDetailsContent:function(a){a.view=new wp.media.view.ImageDetails({controller:this,model:this.state().image,attachment:this.state().image.attachment})},editImageContent:function(){var a,b=this.state(),c=b.get("image");c&&(a=new wp.media.view.EditImage({model:c,controller:this}).render(),this.content.set(a),a.loadEditor())},renderImageDetailsToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{select:{style:"primary",text:e.update,priority:80,click:function(){var a=this.controller,b=a.state();a.close(),b.trigger("update",a.image.toJSON()),a.setState(a.options.state),a.reset()}}}}))},renderReplaceImageToolbar:function(){var a=this,b=a.lastState(),c=b&&b.id;this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{back:{text:e.back,priority:20,click:function(){c?a.setState(c):a.close()}},replace:{style:"primary",text:e.replace,priority:80,requires:{selection:!0},click:function(){var a=this.controller,b=a.state(),c=b.get("selection"),d=c.single();a.close(),a.image.changeAttachment(d,b.display(d)),b.trigger("replace",a.image.toJSON()),a.setState(a.options.state),a.reset()}}}}))}}),a.exports=c},function(a,b){var c,d=jQuery;c=wp.media.View.extend({tagName:"div",template:wp.template("media-modal"),events:{"click .media-modal-backdrop, .media-modal-close":"escapeHandler",keydown:"keydown"},clickedOpenerEl:null,initialize:function(){_.defaults(this.options,{container:document.body,title:"",propagate:!0}),this.focusManager=new wp.media.view.FocusManager({el:this.el})},prepare:function(){return{title:this.options.title}},attach:function(){return this.views.attached?this:(this.views.rendered||this.render(),this.$el.appendTo(this.options.container),this.views.attached=!0,this.views.ready(),this.propagate("attach"))},detach:function(){return this.$el.is(":visible")&&this.close(),this.$el.detach(),this.views.attached=!1,this.propagate("detach")},open:function(){var a,b=this.$el;return b.is(":visible")?this:(this.clickedOpenerEl=document.activeElement,this.views.attached||this.attach(),d("body").addClass("modal-open"),b.show(),"ontouchend"in document&&(a=window.tinymce&&window.tinymce.activeEditor)&&!a.isHidden()&&a.iframeElement&&(a.iframeElement.focus(),a.iframeElement.blur(),setTimeout(function(){a.iframeElement.blur()},100)),this.$(".media-modal").focus(),this.propagate("open"))},close:function(a){return this.views.attached&&this.$el.is(":visible")?(d("body").removeClass("modal-open"),this.$el.hide().undelegate("keydown"),null!==this.clickedOpenerEl?this.clickedOpenerEl.focus():d("#wpbody-content").attr("tabindex","-1").focus(),this.propagate("close"),a&&a.escape&&this.propagate("escape"),this):this},escape:function(){return this.close({escape:!0})},escapeHandler:function(a){a.preventDefault(),this.escape()},content:function(a){return this.views.set(".media-modal-content",a),this},propagate:function(a){return this.trigger(a),this.options.propagate&&this.controller.trigger(a),this},keydown:function(a){27===a.which&&this.$el.is(":visible")&&(this.escape(),a.stopImmediatePropagation())}}),a.exports=c},function(a,b){var c=wp.media.View.extend({events:{keydown:"constrainTabbing"},focus:function(){this.$(".media-menu-item").first().focus()},constrainTabbing:function(a){var b;if(9===a.keyCode)return b=this.$(":tabbable").not('.moxie-shim input[type="file"]'),b.last()[0]!==a.target||a.shiftKey?b.first()[0]===a.target&&a.shiftKey?(b.last().focus(),!1):void 0:(b.first().focus(),!1)}});a.exports=c},function(a,b){var c,d=jQuery;c=wp.media.View.extend({tagName:"div",className:"uploader-window",template:wp.template("uploader-window"),initialize:function(){var a;this.$browser=d('<button type="button" class="browser" />').hide().appendTo("body"),a=this.options.uploader=_.defaults(this.options.uploader||{},{dropzone:this.$el,browser:this.$browser,params:{}}),!a.dropzone||a.dropzone instanceof d||(a.dropzone=d(a.dropzone)),this.controller.on("activate",this.refresh,this),this.controller.on("detach",function(){this.$browser.remove()},this)},refresh:function(){this.uploader&&this.uploader.refresh()},ready:function(){var a,b=wp.media.view.settings.post.id;this.uploader||(b&&(this.options.uploader.params.post_id=b),this.uploader=new wp.Uploader(this.options.uploader),a=this.uploader.dropzone,a.on("dropzone:enter",_.bind(this.show,this)),a.on("dropzone:leave",_.bind(this.hide,this)),d(this.uploader).on("uploader:ready",_.bind(this._ready,this)))},_ready:function(){this.controller.trigger("uploader:ready")},show:function(){var a=this.$el.show();_.defer(function(){a.css({opacity:1})})},hide:function(){var a=this.$el.css({opacity:0});wp.media.transition(a).done(function(){"0"===a.css("opacity")&&a.hide()}),_.delay(function(){"0"===a.css("opacity")&&a.is(":visible")&&a.hide()},500)}}),a.exports=c},function(a,b){var c,d=wp.media.View,e=wp.media.view.l10n,f=jQuery;c=d.extend({tagName:"div",className:"uploader-editor",template:wp.template("uploader-editor"),localDrag:!1,overContainer:!1,overDropzone:!1,draggingFile:null,initialize:function(){return this.initialized=!1,window.tinyMCEPreInit&&window.tinyMCEPreInit.dragDropUpload&&this.browserSupport()?(this.$document=f(document),this.dropzones=[],this.files=[],this.$document.on("drop",".uploader-editor",_.bind(this.drop,this)),this.$document.on("dragover",".uploader-editor",_.bind(this.dropzoneDragover,this)),this.$document.on("dragleave",".uploader-editor",_.bind(this.dropzoneDragleave,this)),this.$document.on("click",".uploader-editor",_.bind(this.click,this)),this.$document.on("dragover",_.bind(this.containerDragover,this)),this.$document.on("dragleave",_.bind(this.containerDragleave,this)),this.$document.on("dragstart dragend drop",_.bind(function(a){this.localDrag="dragstart"===a.type,"drop"===a.type&&this.containerDragleave()},this)),this.initialized=!0,this):this},browserSupport:function(){var a=!1,b=document.createElement("div");return a="draggable"in b||"ondragstart"in b&&"ondrop"in b,a=a&&!!(window.File&&window.FileList&&window.FileReader)},isDraggingFile:function(a){return null!==this.draggingFile?this.draggingFile:!_.isUndefined(a.originalEvent)&&!_.isUndefined(a.originalEvent.dataTransfer)&&(this.draggingFile=_.indexOf(a.originalEvent.dataTransfer.types,"Files")>-1&&_.indexOf(a.originalEvent.dataTransfer.types,"text/plain")===-1,this.draggingFile)},refresh:function(a){var b;for(b in this.dropzones)this.dropzones[b].toggle(this.overContainer||this.overDropzone);return _.isUndefined(a)||f(a.target).closest(".uploader-editor").toggleClass("droppable",this.overDropzone),this.overContainer||this.overDropzone||(this.draggingFile=null),this},render:function(){return this.initialized?(d.prototype.render.apply(this,arguments),f(".wp-editor-wrap").each(_.bind(this.attach,this)),this):this},attach:function(a,b){var c=this.$el.clone();return this.dropzones.push(c),f(b).append(c),this},drop:function(a){var b,c;if(this.containerDragleave(a),this.dropzoneDragleave(a),this.files=a.originalEvent.dataTransfer.files,!(this.files.length<1))return b=f(a.target).parents(".wp-editor-wrap"),b.length>0&&b[0].id&&(window.wpActiveEditor=b[0].id.slice(3,-5)),this.workflow?(this.workflow.state().reset(),this.addFiles.apply(this),this.workflow.open()):(this.workflow=wp.media.editor.open(window.wpActiveEditor,{frame:"post",state:"insert",title:e.addMedia,multiple:!0}),c=this.workflow.uploader,c.uploader&&c.uploader.ready?this.addFiles.apply(this):this.workflow.on("uploader:ready",this.addFiles,this)),!1},addFiles:function(){return this.files.length&&(this.workflow.uploader.uploader.uploader.addFile(_.toArray(this.files)),this.files=[]),this},containerDragover:function(a){!this.localDrag&&this.isDraggingFile(a)&&(this.overContainer=!0,this.refresh())},containerDragleave:function(){this.overContainer=!1,_.delay(_.bind(this.refresh,this),50)},dropzoneDragover:function(a){if(!this.localDrag&&this.isDraggingFile(a))return this.overDropzone=!0,this.refresh(a),!1},dropzoneDragleave:function(a){this.overDropzone=!1,_.delay(_.bind(this.refresh,this,a),50)},click:function(a){this.containerDragleave(a),this.dropzoneDragleave(a),this.localDrag=!1}}),a.exports=c},function(a,b){var c,d=wp.media.View;c=d.extend({tagName:"div",className:"uploader-inline",template:wp.template("uploader-inline"),events:{"click .close":"hide"},initialize:function(){_.defaults(this.options,{message:"",status:!0,canClose:!1}),!this.options.$browser&&this.controller.uploader&&(this.options.$browser=this.controller.uploader.$browser),_.isUndefined(this.options.postId)&&(this.options.postId=wp.media.view.settings.post.id),this.options.status&&this.views.set(".upload-inline-status",new wp.media.view.UploaderStatus({controller:this.controller}))},prepare:function(){var a=this.controller.state().get("suggestedWidth"),b=this.controller.state().get("suggestedHeight"),c={};return c.message=this.options.message,c.canClose=this.options.canClose,a&&b&&(c.suggestedWidth=a,c.suggestedHeight=b),c},dispose:function(){return this.disposing?d.prototype.dispose.apply(this,arguments):(this.disposing=!0,this.remove())},remove:function(){var a=d.prototype.remove.apply(this,arguments);return _.defer(_.bind(this.refresh,this)),a},refresh:function(){var a=this.controller.uploader;a&&a.refresh()},ready:function(){var a,b=this.options.$browser;if(this.controller.uploader){if(a=this.$(".browser"),a[0]===b[0])return;b.detach().text(a.text()),b[0].className=a[0].className,a.replaceWith(b.show())}return this.refresh(),this},show:function(){this.$el.removeClass("hidden"),this.controller.$uploaderToggler&&this.controller.$uploaderToggler.length&&this.controller.$uploaderToggler.attr("aria-expanded","true")},hide:function(){this.$el.addClass("hidden"),this.controller.$uploaderToggler&&this.controller.$uploaderToggler.length&&this.controller.$uploaderToggler.attr("aria-expanded","false").focus()}}),a.exports=c},function(a,b){var c,d=wp.media.View;c=d.extend({className:"media-uploader-status",template:wp.template("uploader-status"),events:{"click .upload-dismiss-errors":"dismiss"},initialize:function(){this.queue=wp.Uploader.queue,this.queue.on("add remove reset",this.visibility,this),this.queue.on("add remove reset change:percent",this.progress,this),this.queue.on("add remove reset change:uploading",this.info,this),this.errors=wp.Uploader.errors,this.errors.reset(),this.errors.on("add remove reset",this.visibility,this),this.errors.on("add",this.error,this)},dispose:function(){return wp.Uploader.queue.off(null,null,this),d.prototype.dispose.apply(this,arguments),this},visibility:function(){this.$el.toggleClass("uploading",!!this.queue.length),this.$el.toggleClass("errors",!!this.errors.length),this.$el.toggle(!!this.queue.length||!!this.errors.length)},ready:function(){_.each({$bar:".media-progress-bar div",$index:".upload-index",$total:".upload-total",$filename:".upload-filename"},function(a,b){this[b]=this.$(a)},this),this.visibility(),this.progress(),this.info()},progress:function(){var a=this.queue,b=this.$bar;b&&a.length&&b.width(a.reduce(function(a,b){if(!b.get("uploading"))return a+100;var c=b.get("percent");return a+(_.isNumber(c)?c:100)},0)/a.length+"%")},info:function(){var a,b=this.queue,c=0;b.length&&(a=this.queue.find(function(a,b){return c=b,a.get("uploading")}),this.$index.text(c+1),this.$total.text(b.length),this.$filename.html(a?this.filename(a.get("filename")):""))},filename:function(a){return _.escape(a)},error:function(a){this.views.add(".upload-errors",new wp.media.view.UploaderStatusError({filename:this.filename(a.get("file").name),message:a.get("message")}),{at:0})},dismiss:function(a){var b=this.views.get(".upload-errors");a.preventDefault(),b&&_.invoke(b,"remove"),wp.Uploader.errors.reset()}}),a.exports=c},function(a,b){var c=wp.media.View.extend({className:"upload-error",template:wp.template("uploader-status-error")});a.exports=c},function(a,b){var c,d=wp.media.View;c=d.extend({tagName:"div",className:"media-toolbar",initialize:function(){var a=this.controller.state(),b=this.selection=a.get("selection"),c=this.library=a.get("library");this._views={},this.primary=new wp.media.view.PriorityList,this.secondary=new wp.media.view.PriorityList,this.primary.$el.addClass("media-toolbar-primary search-form"),this.secondary.$el.addClass("media-toolbar-secondary"),this.views.set([this.secondary,this.primary]),this.options.items&&this.set(this.options.items,{silent:!0}),this.options.silent||this.render(),b&&b.on("add remove reset",this.refresh,this),c&&c.on("add remove reset",this.refresh,this)},dispose:function(){return this.selection&&this.selection.off(null,null,this),this.library&&this.library.off(null,null,this),d.prototype.dispose.apply(this,arguments)},ready:function(){this.refresh()},set:function(a,b,c){var d;return c=c||{},_.isObject(a)?_.each(a,function(a,b){this.set(b,a,{silent:!0})},this):(b instanceof Backbone.View||(b.classes=["media-button-"+a].concat(b.classes||[]),b=new wp.media.view.Button(b).render()),b.controller=b.controller||this.controller,this._views[a]=b,d=b.options.priority<0?"secondary":"primary",this[d].set(a,b,c)),c.silent||this.refresh(),this},get:function(a){return this._views[a]},unset:function(a,b){return delete this._views[a],this.primary.unset(a,b),this.secondary.unset(a,b),b&&b.silent||this.refresh(),this},refresh:function(){var a=this.controller.state(),b=a.get("library"),c=a.get("selection");_.each(this._views,function(a){if(a.model&&a.options&&a.options.requires){var d=a.options.requires,e=!1;c&&c.models&&(e=_.some(c.models,function(a){return a.get("uploading")===!0})),d.selection&&c&&!c.length?e=!0:d.library&&b&&!b.length&&(e=!0),a.model.set("disabled",e)}})}}),a.exports=c},function(a,b){var c,d=wp.media.view.Toolbar,e=wp.media.view.l10n;c=d.extend({initialize:function(){var a=this.options;_.bindAll(this,"clickSelect"),_.defaults(a,{event:"select",state:!1,reset:!0,close:!0,text:e.select,requires:{selection:!0}}),a.items=_.defaults(a.items||{},{select:{style:"primary",text:a.text,priority:80,click:this.clickSelect,requires:a.requires}}),d.prototype.initialize.apply(this,arguments)},clickSelect:function(){var a=this.options,b=this.controller;a.close&&b.close(),a.event&&b.state().trigger(a.event),a.state&&b.setState(a.state),a.reset&&b.reset()}}),a.exports=c},function(a,b){var c,d=wp.media.view.Toolbar.Select,e=wp.media.view.l10n;c=d.extend({initialize:function(){_.defaults(this.options,{text:e.insertIntoPost,requires:!1}),d.prototype.initialize.apply(this,arguments)},refresh:function(){var a=this.controller.state().props.get("url");this.get("select").model.set("disabled",!a||"http://"===a),d.prototype.refresh.apply(this,arguments)}}),a.exports=c},function(a,b){var c=wp.media.View.extend({tagName:"button",className:"media-button",attributes:{type:"button"},events:{click:"click"},defaults:{text:"",style:"",size:"large",disabled:!1},initialize:function(){this.model=new Backbone.Model(this.defaults),_.each(this.defaults,function(a,b){var c=this.options[b];_.isUndefined(c)||(this.model.set(b,c),delete this.options[b])},this),this.listenTo(this.model,"change",this.render)},render:function(){var a=["button",this.className],b=this.model.toJSON();return b.style&&a.push("button-"+b.style),b.size&&a.push("button-"+b.size),a=_.uniq(a.concat(this.options.classes)),this.el.className=a.join(" "),this.$el.attr("disabled",b.disabled),this.$el.text(this.model.get("text")),this},click:function(a){"#"===this.attributes.href&&a.preventDefault(),this.options.click&&!this.model.get("disabled")&&this.options.click.apply(this,arguments)}});a.exports=c},function(a,b){var c,d=Backbone.$;c=wp.media.View.extend({tagName:"div",className:"button-group button-large media-button-group",initialize:function(){this.buttons=_.map(this.options.buttons||[],function(a){return a instanceof Backbone.View?a:new wp.media.view.Button(a).render()}),delete this.options.buttons,this.options.classes&&this.$el.addClass(this.options.classes)},render:function(){return this.$el.html(d(_.pluck(this.buttons,"el")).detach()),this}}),a.exports=c},function(a,b){var c=wp.media.View.extend({tagName:"div",initialize:function(){this._views={},this.set(_.extend({},this._views,this.options.views),{silent:!0}),delete this.options.views,this.options.silent||this.render()},set:function(a,b,c){var d,e,f;return c=c||{},_.isObject(a)?(_.each(a,function(a,b){this.set(b,a)},this),this):(b instanceof Backbone.View||(b=this.toView(b,a,c)),b.controller=b.controller||this.controller,this.unset(a),d=b.options.priority||10,e=this.views.get()||[],_.find(e,function(a,b){if(a.options.priority>d)return f=b,!0}),this._views[a]=b,this.views.add(b,{at:_.isNumber(f)?f:e.length||0}),this)},get:function(a){return this._views[a]},unset:function(a){var b=this.get(a);return b&&b.remove(),delete this._views[a],this},toView:function(a){return new wp.media.View(a)}});a.exports=c},function(a,b){var c,d=jQuery;c=wp.media.View.extend({tagName:"a",className:"media-menu-item",attributes:{href:"#"},events:{click:"_click"},_click:function(a){var b=this.options.click;a&&a.preventDefault(),b?b.call(this):this.click(),wp.media.isTouchDevice||d(".media-frame-content input").first().focus()},click:function(){var a=this.options.state;a&&(this.controller.setState(a),this.views.parent.$el.removeClass("visible"))},render:function(){var a=this.options;return a.text?this.$el.text(a.text):a.html&&this.$el.html(a.html),this}}),a.exports=c},function(a,b){var c,d=wp.media.view.MenuItem,e=wp.media.view.PriorityList;c=e.extend({tagName:"div",className:"media-menu",property:"state",ItemView:d,region:"menu",toView:function(a,b){return a=a||{},a[this.property]=a[this.property]||b,new this.ItemView(a).render()},ready:function(){e.prototype.ready.apply(this,arguments),this.visibility()},set:function(){e.prototype.set.apply(this,arguments),this.visibility()},unset:function(){e.prototype.unset.apply(this,arguments),this.visibility()},visibility:function(){var a=this.region,b=this.controller[a].get(),c=this.views.get(),d=!c||c.length<2;this===b&&this.controller.$el.toggleClass("hide-"+a,d)},select:function(a){var b=this.get(a);b&&(this.deselect(),b.$el.addClass("active"))},deselect:function(){this.$el.children().removeClass("active")},hide:function(a){var b=this.get(a);b&&b.$el.addClass("hidden")},show:function(a){var b=this.get(a);b&&b.$el.removeClass("hidden")}}),a.exports=c},function(a,b){var c=wp.media.view.MenuItem.extend({click:function(){var a=this.options.contentMode;a&&this.controller.content.mode(a)}});a.exports=c},function(a,b){var c,d=wp.media.view.Menu;c=d.extend({tagName:"div",className:"media-router",property:"contentMode",ItemView:wp.media.view.RouterItem,region:"router",initialize:function(){this.controller.on("content:render",this.update,this),d.prototype.initialize.apply(this,arguments)},update:function(){var a=this.controller.content.mode();a&&this.select(a)}}),a.exports=c},function(a,b){var c=wp.media.view.PriorityList.extend({className:"media-sidebar"});a.exports=c},function(a,b){var c,d=wp.media.View,e=jQuery;c=d.extend({tagName:"li",className:"attachment",template:wp.template("attachment"),attributes:function(){return{tabIndex:0,role:"checkbox","aria-label":this.model.get("title"),"aria-checked":!1,"data-id":this.model.get("id")}},events:{click:"toggleSelectionHandler","change [data-setting]":"updateSetting","change [data-setting] input":"updateSetting","change [data-setting] select":"updateSetting","change [data-setting] textarea":"updateSetting","click .attachment-close":"removeFromLibrary","click .check":"checkClickHandler",keydown:"toggleSelectionHandler"},buttons:{},initialize:function(){var a=this.options.selection,b=_.defaults(this.options,{rerenderOnModelChange:!0});b.rerenderOnModelChange?this.listenTo(this.model,"change",this.render):this.listenTo(this.model,"change:percent",this.progress),this.listenTo(this.model,"change:title",this._syncTitle),this.listenTo(this.model,"change:caption",this._syncCaption),this.listenTo(this.model,"change:artist",this._syncArtist),this.listenTo(this.model,"change:album",this._syncAlbum),this.listenTo(this.model,"add",this.select),this.listenTo(this.model,"remove",this.deselect),a&&(a.on("reset",this.updateSelect,this),this.listenTo(this.model,"selection:single selection:unsingle",this.details),this.details(this.model,this.controller.state().get("selection"))),this.listenTo(this.controller,"attachment:compat:waiting attachment:compat:ready",this.updateSave)},dispose:function(){var a=this.options.selection;return this.updateAll(),a&&a.off(null,null,this),d.prototype.dispose.apply(this,arguments),this},render:function(){var a=_.defaults(this.model.toJSON(),{orientation:"landscape",uploading:!1,type:"",subtype:"",icon:"",filename:"",caption:"",title:"",dateFormatted:"",width:"",height:"",compat:!1,alt:"",description:""},this.options);return a.buttons=this.buttons,a.describe=this.controller.state().get("describe"),"image"===a.type&&(a.size=this.imageSize()),a.can={},a.nonces&&(a.can.remove=!!a.nonces["delete"],a.can.save=!!a.nonces.update),this.controller.state().get("allowLocalEdits")&&(a.allowLocalEdits=!0),a.uploading&&!a.percent&&(a.percent=0),this.views.detach(),this.$el.html(this.template(a)),this.$el.toggleClass("uploading",a.uploading),a.uploading?this.$bar=this.$(".media-progress-bar div"):delete this.$bar,this.updateSelect(),this.updateSave(),this.views.render(),this},progress:function(){this.$bar&&this.$bar.length&&this.$bar.width(this.model.get("percent")+"%")},toggleSelectionHandler:function(a){var b;if("INPUT"!==a.target.nodeName&&"BUTTON"!==a.target.nodeName){if(37===a.keyCode||38===a.keyCode||39===a.keyCode||40===a.keyCode)return void this.controller.trigger("attachment:keydown:arrow",a);if("keydown"!==a.type||13===a.keyCode||32===a.keyCode){if(a.preventDefault(),this.controller.isModeActive("grid")){if(this.controller.isModeActive("edit"))return void this.controller.trigger("edit:attachment",this.model,a.currentTarget);
this.controller.isModeActive("select")&&(b="toggle")}a.shiftKey?b="between":(a.ctrlKey||a.metaKey)&&(b="toggle"),this.toggleSelection({method:b}),this.controller.trigger("selection:toggle")}}},toggleSelection:function(a){var b,c,d,e,f=this.collection,g=this.options.selection,h=this.model,i=a&&a.method;if(g){if(b=g.single(),i=_.isUndefined(i)?g.multiple:i,"between"===i&&b&&g.multiple){if(b===h)return;return d=f.indexOf(b),e=f.indexOf(this.model),c=d<e?f.models.slice(d,e+1):f.models.slice(e,d+1),g.add(c),void g.single(h)}if("toggle"===i)return g[this.selected()?"remove":"add"](h),void g.single(h);if("add"===i)return g.add(h),void g.single(h);i||(i="add"),"add"!==i&&(i="reset"),this.selected()?g[b===h?"remove":"single"](h):(g[i](h),g.single(h))}},updateSelect:function(){this[this.selected()?"select":"deselect"]()},selected:function(){var a=this.options.selection;if(a)return!!a.get(this.model.cid)},select:function(a,b){var c=this.options.selection,d=this.controller;!c||b&&b!==c||this.$el.hasClass("selected")||(this.$el.addClass("selected").attr("aria-checked",!0),d.isModeActive("grid")&&d.isModeActive("select")||this.$(".check").attr("tabindex","0"))},deselect:function(a,b){var c=this.options.selection;!c||b&&b!==c||this.$el.removeClass("selected").attr("aria-checked",!1).find(".check").attr("tabindex","-1")},details:function(a,b){var c,d=this.options.selection;d===b&&(c=d.single(),this.$el.toggleClass("details",c===this.model))},imageSize:function(a){var b=this.model.get("sizes"),c=!1;return a=a||"medium",b&&(b[a]?c=b[a]:b.large?c=b.large:b.thumbnail?c=b.thumbnail:b.full&&(c=b.full),c)?_.clone(c):{url:this.model.get("url"),width:this.model.get("width"),height:this.model.get("height"),orientation:this.model.get("orientation")}},updateSetting:function(a){var b,c,d=e(a.target).closest("[data-setting]");d.length&&(b=d.data("setting"),c=a.target.value,this.model.get(b)!==c&&this.save(b,c))},save:function(){var a=this,b=this._save=this._save||{status:"ready"},c=this.model.save.apply(this.model,arguments),d=b.requests?e.when(c,b.requests):c;b.savedTimer&&clearTimeout(b.savedTimer),this.updateSave("waiting"),b.requests=d,d.always(function(){b.requests===d&&(a.updateSave("resolved"===d.state()?"complete":"error"),b.savedTimer=setTimeout(function(){a.updateSave("ready"),delete b.savedTimer},2e3))})},updateSave:function(a){var b=this._save=this._save||{status:"ready"};return a&&a!==b.status&&(this.$el.removeClass("save-"+b.status),b.status=a),this.$el.addClass("save-"+b.status),this},updateAll:function(){var a,b=this.$("[data-setting]"),c=this.model;a=_.chain(b).map(function(a){var b,d,f=e("input, textarea, select, [value]",a);if(f.length)return b=e(a).data("setting"),d=f.val(),c.get(b)!==d?[b,d]:void 0}).compact().object().value(),_.isEmpty(a)||c.save(a)},removeFromLibrary:function(a){"keydown"===a.type&&13!==a.keyCode&&32!==a.keyCode||(a.stopPropagation(),this.collection.remove(this.model))},checkClickHandler:function(a){var b=this.options.selection;b&&(a.stopPropagation(),b.where({id:this.model.get("id")}).length?(b.remove(this.model),this.$el.focus()):b.add(this.model),this.controller.trigger("selection:toggle"))}}),_.each({caption:"_syncCaption",title:"_syncTitle",artist:"_syncArtist",album:"_syncAlbum"},function(a,b){c.prototype[a]=function(a,c){var d=this.$('[data-setting="'+b+'"]');return d.length?c===d.find("input, textarea, select, [value]").val()?this:this.render():this}}),a.exports=c},function(a,b){var c=wp.media.view.Attachment.extend({buttons:{check:!0}});a.exports=c},function(a,b){var c=wp.media.view.Attachment.extend({buttons:{close:!0}});a.exports=c},function(a,b){var c,d=wp.media.View,e=jQuery;c=d.extend({tagName:"ul",className:"attachments",attributes:{tabIndex:-1},initialize:function(){this.el.id=_.uniqueId("__attachments-view-"),_.defaults(this.options,{refreshSensitivity:wp.media.isTouchDevice?300:200,refreshThreshold:3,AttachmentView:wp.media.view.Attachment,sortable:!1,resize:!0,idealColumnWidth:e(window).width()<640?135:150}),this._viewsByCid={},this.$window=e(window),this.resizeEvent="resize.media-modal-columns",this.collection.on("add",function(a){this.views.add(this.createAttachmentView(a),{at:this.collection.indexOf(a)})},this),this.collection.on("remove",function(a){var b=this._viewsByCid[a.cid];delete this._viewsByCid[a.cid],b&&b.remove()},this),this.collection.on("reset",this.render,this),this.listenTo(this.controller,"library:selection:add",this.attachmentFocus),this.scroll=_.chain(this.scroll).bind(this).throttle(this.options.refreshSensitivity).value(),this.options.scrollElement=this.options.scrollElement||this.el,e(this.options.scrollElement).on("scroll",this.scroll),this.initSortable(),_.bindAll(this,"setColumns"),this.options.resize&&(this.on("ready",this.bindEvents),this.controller.on("open",this.setColumns),_.defer(this.setColumns,this))},bindEvents:function(){this.$window.off(this.resizeEvent).on(this.resizeEvent,_.debounce(this.setColumns,50))},attachmentFocus:function(){this.$("li:first").focus()},restoreFocus:function(){this.$("li.selected:first").focus()},arrowEvent:function(a){var b=this.$el.children("li"),c=this.columns,d=b.filter(":focus").index(),e=d+1<=c?1:Math.ceil((d+1)/c);if(d!==-1){if(37===a.keyCode){if(0===d)return;b.eq(d-1).focus()}if(38===a.keyCode){if(1===e)return;b.eq(d-c).focus()}if(39===a.keyCode){if(b.length===d)return;b.eq(d+1).focus()}if(40===a.keyCode){if(Math.ceil(b.length/c)===e)return;b.eq(d+c).focus()}}},dispose:function(){this.collection.props.off(null,null,this),this.options.resize&&this.$window.off(this.resizeEvent),d.prototype.dispose.apply(this,arguments)},setColumns:function(){var a=this.columns,b=this.$el.width();b&&(this.columns=Math.min(Math.round(b/this.options.idealColumnWidth),12)||1,a&&a===this.columns||this.$el.closest(".media-frame-content").attr("data-columns",this.columns))},initSortable:function(){var a=this.collection;this.options.sortable&&e.fn.sortable&&(this.$el.sortable(_.extend({disabled:!!a.comparator,tolerance:"pointer",start:function(a,b){b.item.data("sortableIndexStart",b.item.index())},update:function(b,c){var d=a.at(c.item.data("sortableIndexStart")),e=a.comparator;delete a.comparator,a.remove(d,{silent:!0}),a.add(d,{silent:!0,at:c.item.index()}),a.comparator=e,a.trigger("reset",a),a.saveMenuOrder()}},this.options.sortable)),a.props.on("change:orderby",function(){this.$el.sortable("option","disabled",!!a.comparator)},this),this.collection.props.on("change:orderby",this.refreshSortable,this),this.refreshSortable())},refreshSortable:function(){if(this.options.sortable&&e.fn.sortable){var a=this.collection,b=a.props.get("orderby"),c="menuOrder"===b||!a.comparator;this.$el.sortable("option","disabled",!c)}},createAttachmentView:function(a){var b=new this.options.AttachmentView({controller:this.controller,model:a,collection:this.collection,selection:this.options.selection});return this._viewsByCid[a.cid]=b},prepare:function(){this.collection.length?this.views.set(this.collection.map(this.createAttachmentView,this)):(this.views.unset(),this.collection.more().done(this.scroll))},ready:function(){this.scroll()},scroll:function(){var a,b=this,c=this.options.scrollElement,d=c.scrollTop;c===document&&(c=document.body,d=e(document).scrollTop()),e(c).is(":visible")&&this.collection.hasMore()&&(a=this.views.parent.toolbar,c.scrollHeight-(d+c.clientHeight)<c.clientHeight/3&&a.get("spinner").show(),c.scrollHeight<d+c.clientHeight*this.options.refreshThreshold&&this.collection.more().done(function(){b.scroll(),a.get("spinner").hide()}))}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.View.extend({tagName:"input",className:"search",id:"media-search-input",attributes:{type:"search",placeholder:d.searchMediaPlaceholder},events:{input:"search",keyup:"search"},render:function(){return this.el.value=this.model.escape("search"),this},search:_.debounce(function(a){a.target.value?this.model.set("search",a.target.value):this.model.unset("search")},300)}),a.exports=c},function(a,b){var c,d=jQuery;c=wp.media.View.extend({tagName:"select",className:"attachment-filters",id:"media-attachment-filters",events:{change:"change"},keys:[],initialize:function(){this.createFilters(),_.extend(this.filters,this.options.filters),this.$el.html(_.chain(this.filters).map(function(a,b){return{el:d("<option></option>").val(b).html(a.text)[0],priority:a.priority||50}},this).sortBy("priority").pluck("el").value()),this.listenTo(this.model,"change",this.select),this.select()},createFilters:function(){this.filters={}},change:function(){var a=this.filters[this.el.value];a&&this.model.set(a.props)},select:function(){var a=this.model,b="all",c=a.toJSON();_.find(this.filters,function(a,d){var e=_.all(a.props,function(a,b){return a===(_.isUndefined(c[b])?null:c[b])});if(e)return b=d}),this.$el.val(b)}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.view.AttachmentFilters.extend({id:"media-attachment-date-filters",createFilters:function(){var a={};_.each(wp.media.view.settings.months||{},function(b,c){a[c]={text:b.text,props:{year:b.year,monthnum:b.month}}}),a.all={text:d.allDates,props:{monthnum:!1,year:!1},priority:10},this.filters=a}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.view.AttachmentFilters.extend({createFilters:function(){var a,b=this.model.get("type"),c=wp.media.view.settings.mimeTypes,e=window.userSettings?parseInt(window.userSettings.uid,10):0;c&&b&&(a=c[b]),this.filters={all:{text:a||d.allMediaItems,props:{uploadedTo:null,orderby:"date",order:"DESC",author:null},priority:10},uploaded:{text:d.uploadedToThisPost,props:{uploadedTo:wp.media.view.settings.post.id,orderby:"menuOrder",order:"ASC",author:null},priority:20},unattached:{text:d.unattached,props:{uploadedTo:0,orderby:"menuOrder",order:"ASC",author:null},priority:50}},e&&(this.filters.mine={text:d.mine,props:{orderby:"date",order:"DESC",author:e},priority:50})}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.view.AttachmentFilters.extend({createFilters:function(){var a={},b=window.userSettings?parseInt(window.userSettings.uid,10):0;_.each(wp.media.view.settings.mimeTypes||{},function(b,c){a[c]={text:b,props:{status:null,type:c,uploadedTo:null,orderby:"date",order:"DESC",author:null}}}),a.all={text:d.allMediaItems,props:{status:null,type:null,uploadedTo:null,orderby:"date",order:"DESC",author:null},priority:10},wp.media.view.settings.post.id&&(a.uploaded={text:d.uploadedToThisPost,props:{status:null,type:null,uploadedTo:wp.media.view.settings.post.id,orderby:"menuOrder",order:"ASC",author:null},priority:20}),a.unattached={text:d.unattached,props:{status:null,uploadedTo:0,type:null,orderby:"menuOrder",order:"ASC",author:null},priority:50},b&&(a.mine={text:d.mine,props:{status:null,type:null,uploadedTo:null,orderby:"date",order:"DESC",author:b},priority:50}),wp.media.view.settings.mediaTrash&&this.controller.isModeActive("grid")&&(a.trash={text:d.trash,props:{uploadedTo:null,status:"trash",type:null,orderby:"date",order:"DESC",author:null},priority:50}),this.filters=a}}),a.exports=c},function(a,b){var c,d=wp.media.View,e=wp.media.view.settings.mediaTrash,f=wp.media.view.l10n,g=jQuery;c=d.extend({tagName:"div",className:"attachments-browser",initialize:function(){_.defaults(this.options,{filters:!1,search:!0,date:!0,display:!1,sidebar:!0,AttachmentView:wp.media.view.Attachment.Library}),this.controller.on("toggle:upload:attachment",this.toggleUploader,this),this.controller.on("edit:selection",this.editSelection),this.options.sidebar&&"errors"===this.options.sidebar&&this.createSidebar(),this.createUploader(),this.createToolbar(),this.createAttachments(),this.options.sidebar&&"errors"!==this.options.sidebar&&this.createSidebar(),this.updateContent(),this.options.sidebar&&"errors"!==this.options.sidebar||(this.$el.addClass("hide-sidebar"),"errors"===this.options.sidebar&&this.$el.addClass("sidebar-for-errors")),this.collection.on("add remove reset",this.updateContent,this)},editSelection:function(a){a.$(".media-button-backToLibrary").focus()},dispose:function(){return this.options.selection.off(null,null,this),d.prototype.dispose.apply(this,arguments),this},createToolbar:function(){var a,b,c;c={controller:this.controller},this.controller.isModeActive("grid")&&(c.className="media-toolbar wp-filter"),this.toolbar=new wp.media.view.Toolbar(c),this.views.add(this.toolbar),this.toolbar.set("spinner",new wp.media.view.Spinner({priority:-60})),-1!==g.inArray(this.options.filters,["uploaded","all"])&&(this.toolbar.set("filtersLabel",new wp.media.view.Label({value:f.filterByType,attributes:{"for":"media-attachment-filters"},priority:-80}).render()),"uploaded"===this.options.filters?this.toolbar.set("filters",new wp.media.view.AttachmentFilters.Uploaded({controller:this.controller,model:this.collection.props,priority:-80}).render()):(b=new wp.media.view.AttachmentFilters.All({controller:this.controller,model:this.collection.props,priority:-80}),this.toolbar.set("filters",b.render()))),this.controller.isModeActive("grid")?(a=d.extend({className:"view-switch media-grid-view-switch",template:wp.template("media-library-view-switcher")}),this.toolbar.set("libraryViewSwitcher",new a({controller:this.controller,priority:-90}).render()),this.toolbar.set("dateFilterLabel",new wp.media.view.Label({value:f.filterByDate,attributes:{"for":"media-attachment-date-filters"},priority:-75}).render()),this.toolbar.set("dateFilter",new wp.media.view.DateFilter({controller:this.controller,model:this.collection.props,priority:-75}).render()),this.toolbar.set("selectModeToggleButton",new wp.media.view.SelectModeToggleButton({text:f.bulkSelect,controller:this.controller,priority:-70}).render()),this.toolbar.set("deleteSelectedButton",new wp.media.view.DeleteSelectedButton({filters:b,style:"primary",disabled:!0,text:e?f.trashSelected:f.deleteSelected,controller:this.controller,priority:-60,click:function(){var a=[],b=[],c=this.controller.state().get("selection"),d=this.controller.state().get("library");c.length&&(e||window.confirm(f.warnBulkDelete))&&(e&&"trash"!==c.at(0).get("status")&&!window.confirm(f.warnBulkTrash)||(c.each(function(c){return c.get("nonces")["delete"]?void(e&&"trash"===c.get("status")?(c.set("status","inherit"),a.push(c.save()),b.push(c)):e?(c.set("status","trash"),a.push(c.save()),b.push(c)):c.destroy({wait:!0})):void b.push(c)}),a.length?(c.remove(b),g.when.apply(null,a).then(_.bind(function(){d._requery(!0),this.controller.trigger("selection:action:done")},this))):this.controller.trigger("selection:action:done")))}}).render()),e&&this.toolbar.set("deleteSelectedPermanentlyButton",new wp.media.view.DeleteSelectedPermanentlyButton({filters:b,style:"primary",disabled:!0,text:f.deleteSelected,controller:this.controller,priority:-55,click:function(){var a=[],b=[],c=this.controller.state().get("selection");c.length&&window.confirm(f.warnBulkDelete)&&(c.each(function(c){return c.get("nonces")["delete"]?void b.push(c):void a.push(c)}),a.length&&c.remove(a),b.length&&g.when.apply(null,b.map(function(a){return a.destroy()})).then(_.bind(function(){this.controller.trigger("selection:action:done")},this)))}}).render())):this.options.date&&(this.toolbar.set("dateFilterLabel",new wp.media.view.Label({value:f.filterByDate,attributes:{"for":"media-attachment-date-filters"},priority:-75}).render()),this.toolbar.set("dateFilter",new wp.media.view.DateFilter({controller:this.controller,model:this.collection.props,priority:-75}).render())),this.options.search&&(this.toolbar.set("searchLabel",new wp.media.view.Label({value:f.searchMediaLabel,attributes:{"for":"media-search-input"},priority:60}).render()),this.toolbar.set("search",new wp.media.view.Search({controller:this.controller,model:this.collection.props,priority:60}).render())),this.options.dragInfo&&this.toolbar.set("dragInfo",new d({el:g('<div class="instructions">'+f.dragInfo+"</div>")[0],priority:-40})),this.options.suggestedWidth&&this.options.suggestedHeight&&this.toolbar.set("suggestedDimensions",new d({el:g('<div class="instructions">'+f.suggestedDimensions.replace("%1$s",this.options.suggestedWidth).replace("%2$s",this.options.suggestedHeight)+"</div>")[0],priority:-40}))},updateContent:function(){var a,b=this;a=this.controller.isModeActive("grid")?b.attachmentsNoResults:b.uploader,this.collection.length?(a.$el.addClass("hidden"),b.toolbar.get("spinner").hide()):(this.toolbar.get("spinner").show(),this.dfd=this.collection.more().done(function(){b.collection.length?a.$el.addClass("hidden"):a.$el.removeClass("hidden"),b.toolbar.get("spinner").hide()}))},createUploader:function(){this.uploader=new wp.media.view.UploaderInline({controller:this.controller,status:!1,message:this.controller.isModeActive("grid")?"":f.noItemsFound,canClose:this.controller.isModeActive("grid")}),this.uploader.$el.addClass("hidden"),this.views.add(this.uploader)},toggleUploader:function(){this.uploader.$el.hasClass("hidden")?this.uploader.show():this.uploader.hide()},createAttachments:function(){this.attachments=new wp.media.view.Attachments({controller:this.controller,collection:this.collection,selection:this.options.selection,model:this.model,sortable:this.options.sortable,scrollElement:this.options.scrollElement,idealColumnWidth:this.options.idealColumnWidth,AttachmentView:this.options.AttachmentView}),this.controller.on("attachment:keydown:arrow",_.bind(this.attachments.arrowEvent,this.attachments)),this.controller.on("attachment:details:shift-tab",_.bind(this.attachments.restoreFocus,this.attachments)),this.views.add(this.attachments),this.controller.isModeActive("grid")&&(this.attachmentsNoResults=new d({controller:this.controller,tagName:"p"}),this.attachmentsNoResults.$el.addClass("hidden no-media"),this.attachmentsNoResults.$el.html(f.noMedia),this.views.add(this.attachmentsNoResults))},createSidebar:function(){var a=this.options,b=a.selection,c=this.sidebar=new wp.media.view.Sidebar({controller:this.controller});this.views.add(c),this.controller.uploader&&c.set("uploads",new wp.media.view.UploaderStatus({controller:this.controller,priority:40})),b.on("selection:single",this.createSingle,this),b.on("selection:unsingle",this.disposeSingle,this),b.single()&&this.createSingle()},createSingle:function(){var a=this.sidebar,b=this.options.selection.single();a.set("details",new wp.media.view.Attachment.Details({controller:this.controller,model:b,priority:80})),a.set("compat",new wp.media.view.AttachmentCompat({controller:this.controller,model:b,priority:120})),this.options.display&&a.set("display",new wp.media.view.Settings.AttachmentDisplay({controller:this.controller,model:this.model.display(b),attachment:b,priority:160,userSettings:this.model.get("displayUserSettings")})),"insert"===this.model.id&&a.$el.addClass("visible")},disposeSingle:function(){var a=this.sidebar;a.unset("details"),a.unset("compat"),a.unset("display"),a.$el.removeClass("visible")}}),a.exports=c},function(a,b){var c,d=wp.media.view.l10n;c=wp.media.View.extend({tagName:"div",className:"media-selection",template:wp.template("media-selection"),events:{"click .edit-selection":"edit","click .clear-selection":"clear"},initialize:function(){_.defaults(this.options,{editable:!1,clearable:!0}),this.attachments=new wp.media.view.Attachments.Selection({controller:this.controller,collection:this.collection,selection:this.collection,model:new Backbone.Model}),this.views.set(".selection-view",this.attachments),this.collection.on("add remove reset",this.refresh,this),this.controller.on("content:activate",this.refresh,this)},ready:function(){this.refresh()},refresh:function(){if(this.$el.children().length){var a=this.collection,b="edit-selection"===this.controller.content.mode();this.$el.toggleClass("empty",!a.length),this.$el.toggleClass("one",1===a.length),this.$el.toggleClass("editing",b),this.$(".count").text(d.selected.replace("%d",a.length))}},edit:function(a){a.preventDefault(),this.options.editable&&this.options.editable.call(this,this.collection)},clear:function(a){a.preventDefault(),this.collection.reset(),this.controller.modal.focusManager.focus()}}),a.exports=c},function(a,b){var c=wp.media.view.Attachment.extend({className:"attachment selection",toggleSelection:function(){this.options.selection.single(this.model)}});a.exports=c},function(a,b){var c,d=wp.media.view.Attachments;c=d.extend({events:{},initialize:function(){return _.defaults(this.options,{sortable:!1,resize:!1,AttachmentView:wp.media.view.Attachment.Selection}),d.prototype.initialize.apply(this,arguments)}}),a.exports=c},function(a,b){var c=wp.media.view.Attachment.Selection.extend({buttons:{close:!0}});a.exports=c},function(a,b){var c,d=wp.media.View,e=Backbone.$;c=d.extend({events:{"click button":"updateHandler","change input":"updateHandler","change select":"updateHandler","change textarea":"updateHandler"},initialize:function(){this.model=this.model||new Backbone.Model,this.listenTo(this.model,"change",this.updateChanges)},prepare:function(){return _.defaults({model:this.model.toJSON()},this.options)},render:function(){return d.prototype.render.apply(this,arguments),_(this.model.attributes).chain().keys().each(this.update,this),this},update:function(a){var b,c,d=this.model.get(a),e=this.$('[data-setting="'+a+'"]');e.length&&(e.is("select")?(c=e.find('[value="'+d+'"]'),c.length?(e.find("option").prop("selected",!1),c.prop("selected",!0)):this.model.set(a,e.find(":selected").val())):e.hasClass("button-group")?(b=e.find("button").removeClass("active"),b.filter('[value="'+d+'"]').addClass("active")):e.is('input[type="text"], textarea')?e.is(":focus")||e.val(d):e.is('input[type="checkbox"]')&&e.prop("checked",!!d&&"false"!==d))},updateHandler:function(a){var b,c=e(a.target).closest("[data-setting]"),d=a.target.value;a.preventDefault(),c.length&&(c.is('input[type="checkbox"]')&&(d=c[0].checked),this.model.set(c.data("setting"),d),(b=c.data("userSetting"))&&window.setUserSetting(b,d))},updateChanges:function(a){a.hasChanged()&&_(a.changed).chain().keys().each(this.update,this)}}),a.exports=c},function(a,b){var c,d=wp.media.view.Settings;c=d.extend({className:"attachment-display-settings",template:wp.template("attachment-display-settings"),initialize:function(){var a=this.options.attachment;_.defaults(this.options,{userSettings:!1}),d.prototype.initialize.apply(this,arguments),this.listenTo(this.model,"change:link",this.updateLinkTo),a&&a.on("change:uploading",this.render,this)},dispose:function(){var a=this.options.attachment;a&&a.off(null,null,this),d.prototype.dispose.apply(this,arguments)},render:function(){var a=this.options.attachment;return a&&_.extend(this.options,{sizes:a.get("sizes"),type:a.get("type")}),d.prototype.render.call(this),this.updateLinkTo(),this},updateLinkTo:function(){var a=this.model.get("link"),b=this.$(".link-to-custom"),c=this.options.attachment;return"none"===a||"embed"===a||!c&&"custom"!==a?void b.addClass("hidden"):(c&&("post"===a?b.val(c.get("link")):"file"===a?b.val(c.get("url")):this.model.get("linkUrl")||b.val("http://"),b.prop("readonly","custom"!==a)),b.removeClass("hidden"),void(!wp.media.isTouchDevice&&b.is(":visible")&&b.focus()[0].select()))}}),a.exports=c},function(a,b){var c=wp.media.view.Settings.extend({className:"collection-settings gallery-settings",template:wp.template("gallery-settings")});a.exports=c},function(a,b){var c=wp.media.view.Settings.extend({className:"collection-settings playlist-settings",template:wp.template("playlist-settings")});a.exports=c},function(a,b){var c,d=wp.media.view.Attachment,e=wp.media.view.l10n;c=d.extend({tagName:"div",className:"attachment-details",template:wp.template("attachment-details"),attributes:function(){return{tabIndex:0,"data-id":this.model.get("id")}},events:{"change [data-setting]":"updateSetting","change [data-setting] input":"updateSetting","change [data-setting] select":"updateSetting","change [data-setting] textarea":"updateSetting","click .delete-attachment":"deleteAttachment","click .trash-attachment":"trashAttachment","click .untrash-attachment":"untrashAttachment","click .edit-attachment":"editAttachment",keydown:"toggleSelectionHandler"},initialize:function(){this.options=_.defaults(this.options,{rerenderOnModelChange:!1}),this.on("ready",this.initialFocus),d.prototype.initialize.apply(this,arguments)},initialFocus:function(){wp.media.isTouchDevice||this.$('input[type="text"]').eq(0).focus()},deleteAttachment:function(a){a.preventDefault(),window.confirm(e.warnDelete)&&(this.model.destroy(),this.controller.modal.focusManager.focus())},trashAttachment:function(a){var b=this.controller.library;a.preventDefault(),wp.media.view.settings.mediaTrash&&"edit-metadata"===this.controller.content.mode()?(this.model.set("status","trash"),this.model.save().done(function(){b._requery(!0)})):this.model.destroy()},untrashAttachment:function(a){var b=this.controller.library;a.preventDefault(),this.model.set("status","inherit"),this.model.save().done(function(){b._requery(!0)})},editAttachment:function(a){var b=this.controller.states.get("edit-image");window.imageEdit&&b?(a.preventDefault(),b.set("image",this.model),this.controller.setState("edit-image")):this.$el.addClass("needs-refresh")},toggleSelectionHandler:function(a){return"keydown"===a.type&&9===a.keyCode&&a.shiftKey&&a.target===this.$(":tabbable").get(0)?(this.controller.trigger("attachment:details:shift-tab",a),!1):37===a.keyCode||38===a.keyCode||39===a.keyCode||40===a.keyCode?void this.controller.trigger("attachment:keydown:arrow",a):void 0}}),a.exports=c},function(a,b){var c,d=wp.media.View;c=d.extend({tagName:"form",className:"compat-item",events:{submit:"preventDefault","change input":"save","change select":"save","change textarea":"save"},initialize:function(){this.listenTo(this.model,"change:compat",this.render)},dispose:function(){return this.$(":focus").length&&this.save(),d.prototype.dispose.apply(this,arguments)},render:function(){var a=this.model.get("compat");if(a&&a.item)return this.views.detach(),this.$el.html(a.item),this.views.render(),this},preventDefault:function(a){a.preventDefault()},save:function(a){var b={};a&&a.preventDefault(),_.each(this.$el.serializeArray(),function(a){b[a.name]=a.value}),this.controller.trigger("attachment:compat:waiting",["waiting"]),this.model.saveCompat(b).always(_.bind(this.postSave,this))},postSave:function(){this.controller.trigger("attachment:compat:ready",["ready"])}}),a.exports=c},function(a,b){var c=wp.media.View.extend({className:"media-iframe",render:function(){return this.views.detach(),this.$el.html('<iframe src="'+this.controller.state().get("src")+'" />'),this.views.render(),this}});a.exports=c},function(a,b){var c=wp.media.View.extend({className:"media-embed",initialize:function(){this.url=new wp.media.view.EmbedUrl({controller:this.controller,model:this.model.props}).render(),this.views.set([this.url]),this.refresh(),this.listenTo(this.model,"change:type",this.refresh),this.listenTo(this.model,"change:loading",this.loading)},settings:function(a){this._settings&&this._settings.remove(),this._settings=a,this.views.add(a)},refresh:function(){var a,b=this.model.get("type");if("image"===b)a=wp.media.view.EmbedImage;else{if("link"!==b)return;a=wp.media.view.EmbedLink}this.settings(new a({controller:this.controller,model:this.model.props,priority:40}))},loading:function(){this.$el.toggleClass("embed-loading",this.model.get("loading"))}});a.exports=c},function(a,b){var c=wp.media.View.extend({tagName:"label",className:"screen-reader-text",initialize:function(){this.value=this.options.value},render:function(){return this.$el.html(this.value),this}});a.exports=c},function(a,b){var c,d=wp.media.View,e=jQuery;c=d.extend({tagName:"label",className:"embed-url",events:{input:"url",keyup:"url",change:"url"},initialize:function(){this.$input=e('<input id="embed-url-field" type="url" />').val(this.model.get("url")),this.input=this.$input[0],this.spinner=e('<span class="spinner" />')[0],this.$el.append([this.input,this.spinner]),this.listenTo(this.model,"change:url",this.render),this.model.get("url")&&_.delay(_.bind(function(){this.model.trigger("change:url")},this),500)},render:function(){var a=this.$input;if(!a.is(":focus"))return this.input.value=this.model.get("url")||"http://",d.prototype.render.apply(this,arguments),this},ready:function(){wp.media.isTouchDevice||this.focus()},url:function(a){this.model.set("url",e.trim(a.target.value))},focus:function(){var a=this.$input;a.is(":visible")&&a.focus()[0].select()}}),a.exports=c},function(a,b){var c,d=jQuery;c=wp.media.view.Settings.extend({className:"embed-link-settings",template:wp.template("embed-link-settings"),initialize:function(){this.listenTo(this.model,"change:url",this.updateoEmbed)},updateoEmbed:_.debounce(function(){var a=this.model.get("url");this.$(".embed-container").hide().find(".embed-preview").empty(),this.$(".setting").hide(),a&&(a.length<11||!a.match(/^http(s)?:\/\//))||this.fetch()},wp.media.controller.Embed.sensitivity),fetch:function(){var a,b,c=this.model.get("url");d("#embed-url-field").val()===c&&(this.dfd&&"pending"===this.dfd.state()&&this.dfd.abort(),a=/https?:\/\/www\.youtube\.com\/embed\/([^\/]+)/,b=a.exec(c),b&&(c="https://www.youtube.com/watch?v="+b[1]),this.dfd=wp.apiRequest({url:wp.media.view.settings.oEmbedProxyUrl,data:{url:c,maxwidth:this.model.get("width"),maxheight:this.model.get("height")},type:"GET",dataType:"json",context:this}).done(function(a){this.renderoEmbed({data:{body:a.html||""}})}).fail(this.renderFail))},renderFail:function(a,b){"abort"!==b&&this.$(".link-text").show()},renderoEmbed:function(a){var b=a&&a.data&&a.data.body||"";b?this.$(".embed-container").show().find(".embed-preview").html(b):this.renderFail()}}),a.exports=c},function(a,b){var c,d=wp.media.view.Settings.AttachmentDisplay;c=d.extend({className:"embed-media-settings",template:wp.template("embed-image-settings"),initialize:function(){d.prototype.initialize.apply(this,arguments),this.listenTo(this.model,"change:url",this.updateImage)},updateImage:function(){this.$("img").attr("src",this.model.get("url"))}}),a.exports=c},function(a,b){var c,d=wp.media.view.Settings.AttachmentDisplay,e=jQuery;c=d.extend({className:"image-details",template:wp.template("image-details"),events:_.defaults(d.prototype.events,{"click .edit-attachment":"editAttachment","click .replace-attachment":"replaceAttachment","click .advanced-toggle":"onToggleAdvanced",'change [data-setting="customWidth"]':"onCustomSize",'change [data-setting="customHeight"]':"onCustomSize",'keyup [data-setting="customWidth"]':"onCustomSize",'keyup [data-setting="customHeight"]':"onCustomSize"}),initialize:function(){this.options.attachment=this.model.attachment,this.listenTo(this.model,"change:url",this.updateUrl),this.listenTo(this.model,"change:link",this.toggleLinkSettings),this.listenTo(this.model,"change:size",this.toggleCustomSize),d.prototype.initialize.apply(this,arguments)},prepare:function(){var a=!1;return this.model.attachment&&(a=this.model.attachment.toJSON()),_.defaults({model:this.model.toJSON(),attachment:a},this.options)},render:function(){var a=arguments;return this.model.attachment&&"pending"===this.model.dfd.state()?this.model.dfd.done(_.bind(function(){d.prototype.render.apply(this,a),this.postRender()},this)).fail(_.bind(function(){this.model.attachment=!1,d.prototype.render.apply(this,a),this.postRender()},this)):(d.prototype.render.apply(this,arguments),this.postRender()),this},postRender:function(){setTimeout(_.bind(this.resetFocus,this),10),this.toggleLinkSettings(),"show"===window.getUserSetting("advImgDetails")&&this.toggleAdvanced(!0),this.trigger("post-render")},resetFocus:function(){this.$(".link-to-custom").blur(),this.$(".embed-media-settings").scrollTop(0)},updateUrl:function(){this.$(".image img").attr("src",this.model.get("url")),this.$(".url").val(this.model.get("url"))},toggleLinkSettings:function(){"none"===this.model.get("link")?this.$(".link-settings").addClass("hidden"):this.$(".link-settings").removeClass("hidden")},toggleCustomSize:function(){
"custom"!==this.model.get("size")?this.$(".custom-size").addClass("hidden"):this.$(".custom-size").removeClass("hidden")},onCustomSize:function(a){var b,c=e(a.target).data("setting"),d=e(a.target).val();return!/^\d+/.test(d)||parseInt(d,10)<1?void a.preventDefault():void("customWidth"===c?(b=Math.round(1/this.model.get("aspectRatio")*d),this.model.set("customHeight",b,{silent:!0}),this.$('[data-setting="customHeight"]').val(b)):(b=Math.round(this.model.get("aspectRatio")*d),this.model.set("customWidth",b,{silent:!0}),this.$('[data-setting="customWidth"]').val(b)))},onToggleAdvanced:function(a){a.preventDefault(),this.toggleAdvanced()},toggleAdvanced:function(a){var b,c=this.$el.find(".advanced-section");c.hasClass("advanced-visible")||a===!1?(c.removeClass("advanced-visible"),c.find(".advanced-settings").addClass("hidden"),b="hide"):(c.addClass("advanced-visible"),c.find(".advanced-settings").removeClass("hidden"),b="show"),window.setUserSetting("advImgDetails",b)},editAttachment:function(a){var b=this.controller.states.get("edit-image");window.imageEdit&&b&&(a.preventDefault(),b.set("image",this.model.attachment),this.controller.setState("edit-image"))},replaceAttachment:function(a){a.preventDefault(),this.controller.setState("replace-image")}}),a.exports=c},function(a,b){var c,d=wp.media.View,e=wp.media.view.UploaderStatus,f=wp.media.view.l10n,g=jQuery;c=d.extend({className:"crop-content",template:wp.template("crop-content"),initialize:function(){_.bindAll(this,"onImageLoad")},ready:function(){this.controller.frame.on("content:error:crop",this.onError,this),this.$image=this.$el.find(".crop-image"),this.$image.on("load",this.onImageLoad),g(window).on("resize.cropper",_.debounce(this.onImageLoad,250))},remove:function(){g(window).off("resize.cropper"),this.$el.remove(),this.$el.off(),d.prototype.remove.apply(this,arguments)},prepare:function(){return{title:f.cropYourImage,url:this.options.attachment.get("url")}},onImageLoad:function(){var a,b=this.controller.get("imgSelectOptions");"function"==typeof b&&(b=b(this.options.attachment,this.controller)),b=_.extend(b,{parent:this.$el,onInit:function(){var b=a.getOptions().aspectRatio;this.parent.children().on("mousedown touchstart",function(c){!b&&c.shiftKey&&a.setOptions({aspectRatio:"1:1"})}),this.parent.children().on("mouseup touchend",function(){a.setOptions({aspectRatio:!!b&&b})})}}),this.trigger("image-loaded"),a=this.controller.imgSelect=this.$image.imgAreaSelect(b)},onError:function(){var a=this.options.attachment.get("filename");this.views.add(".upload-errors",new wp.media.view.UploaderStatusError({filename:e.prototype.filename(a),message:window._wpMediaViewsL10n.cropError}),{at:0})}}),a.exports=c},function(a,b){var c,d=wp.media.view;c=d.Cropper.extend({className:"crop-content site-icon",ready:function(){d.Cropper.prototype.ready.apply(this,arguments),this.$(".crop-image").on("load",_.bind(this.addSidebar,this))},addSidebar:function(){this.sidebar=new wp.media.view.Sidebar({controller:this.controller}),this.sidebar.set("preview",new wp.media.view.SiteIconPreview({controller:this.controller,attachment:this.options.attachment})),this.controller.cropperView.views.add(this.sidebar)}}),a.exports=c},function(a,b){var c,d=wp.media.View,e=jQuery;c=d.extend({className:"site-icon-preview",template:wp.template("site-icon-preview"),ready:function(){this.controller.imgSelect.setOptions({onInit:this.updatePreview,onSelectChange:this.updatePreview})},prepare:function(){return{url:this.options.attachment.get("url")}},updatePreview:function(a,b){var c=64/b.width,d=64/b.height,f=16/b.width,g=16/b.height;e("#preview-app-icon").css({width:Math.round(c*this.imageWidth)+"px",height:Math.round(d*this.imageHeight)+"px",marginLeft:"-"+Math.round(c*b.x1)+"px",marginTop:"-"+Math.round(d*b.y1)+"px"}),e("#preview-favicon").css({width:Math.round(f*this.imageWidth)+"px",height:Math.round(g*this.imageHeight)+"px",marginLeft:"-"+Math.round(f*b.x1)+"px",marginTop:"-"+Math.floor(g*b.y1)+"px"})}}),a.exports=c},function(a,b){var c,d=wp.media.View;c=d.extend({className:"image-editor",template:wp.template("image-editor"),initialize:function(a){this.editor=window.imageEdit,this.controller=a.controller,d.prototype.initialize.apply(this,arguments)},prepare:function(){return this.model.toJSON()},loadEditor:function(){var a=this.editor.open(this.model.get("id"),this.model.get("nonces").edit,this);a.done(_.bind(this.focus,this))},focus:function(){this.$(".imgedit-submit .button").eq(0).focus()},back:function(){var a=this.controller.lastState();this.controller.setState(a)},refresh:function(){this.model.fetch()},save:function(){var a=this.controller.lastState();this.model.fetch().done(_.bind(function(){this.controller.setState(a)},this))}}),a.exports=c},function(a,b){var c=wp.media.View.extend({tagName:"span",className:"spinner",spinnerTimeout:!1,delay:400,show:function(){return this.spinnerTimeout||(this.spinnerTimeout=_.delay(function(a){a.addClass("is-active")},this.delay,this.$el)),this},hide:function(){return this.$el.removeClass("is-active"),this.spinnerTimeout=clearTimeout(this.spinnerTimeout),this}});a.exports=c}]));