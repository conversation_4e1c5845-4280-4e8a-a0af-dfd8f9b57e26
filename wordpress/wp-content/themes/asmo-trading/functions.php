<?php
 add_theme_support( 'post-formats', array( 'aside', 'gallery','image') );
 function VnKings_change_page_permalink() {
      global $wp_rewrite;
      if ( strstr($wp_rewrite->get_page_permastruct(), '.html') != '.html' )
      $wp_rewrite->page_structure = $wp_rewrite->page_structure . '.html';
  }
  add_action('init', 'VnKings_change_page_permalink', -1);

    // Load in our CSS
    function wptags_enqueue_styles() {
      wp_enqueue_style( 'hover-effect', get_stylesheet_directory_uri() . '/css/hover.css', time(), 'all' );
      wp_enqueue_style( 'common-style', get_stylesheet_directory_uri() . '/css/common.css', time(), 'all' );
      wp_enqueue_style( 'bxslider-style', get_stylesheet_directory_uri() . '/css/jquery.bxslider.css', time(), 'all' );
      wp_enqueue_style( 'smart-style', get_stylesheet_directory_uri() . '/css/smart.css', time(), 'all' );
      wp_enqueue_style( 'main-style', get_stylesheet_directory_uri() . '/css/style.css' , time(), 'all' );
      wp_enqueue_style( 'slick', get_stylesheet_directory_uri() . '/css/slick.css', time(), 'all' );
      wp_enqueue_style( 'slick-theme-style', get_stylesheet_directory_uri() . '/css/slick-theme.css', time(), 'all' );
      wp_enqueue_script( 'jquery-js', get_template_directory_uri() . '/js/jquery.js', [], time(), false );
      wp_enqueue_script( 'bxslider-js', get_template_directory_uri() . '/js/jquery.bxslider.min.js', [], time(), true );
      wp_enqueue_script( 'jquery-inview-js', get_template_directory_uri() . '/js/jquery.inview.js', [], time(), true );
      wp_enqueue_script( 'jquery-jpostal-js', get_template_directory_uri() . '/js/jquery.jpostal.js', [], time(), true );
      wp_enqueue_script( 'tel-js', get_template_directory_uri() . '/js/tel.js', [], time(), true );
      wp_enqueue_script( 'drawr-js', get_template_directory_uri() . '/js/drawr.js', [], time(), true );
      wp_enqueue_script( 'pagetop-js', get_template_directory_uri() . '/js/pagetop.js', [], time(), true );
      wp_enqueue_script( 'lightbox-js', get_template_directory_uri() . '/js/slick.min.js', [], time(), true );
      wp_enqueue_style( 'header-style', get_stylesheet_directory_uri() . '/css/header.css', time(), 'all' );
      wp_enqueue_style( 'footer-style', get_stylesheet_directory_uri() . '/css/footer.css', time(), 'all' );
      wp_enqueue_style( 'layout-style', get_stylesheet_directory_uri() . '/css/layout.css', time(), 'all' );
    }
    add_action( 'wp_enqueue_scripts', 'wptags_enqueue_styles' );
    /**
  * スライドを作る
  * @param  [type] $slider_id [description]
  * @return [type]            [description]
  */
  function get_master_slider($slider_id){
     global $mspdb;
     $panel_data = $mspdb->get_slider_field_val( $slider_id, 'params' );
     if ( ! $panel_data ){
       $sliders = '';
     }else{
       $parser = msp_get_parser();
       $parser->set_data( $panel_data, $slider_id );
       $sliders = $parser->get_results()['slides'];
     }
     return $sliders;
  }
    /**
   * スライドを処理する
   * @param  [type] $slider_id [description]
   * @param  [type] $args      [description]
   * @return [type]            [description]
   */
  function blockSlider( $slider_id, $args = NULL ){
     $sliders = get_master_slider($slider_id);
     require(get_template_directory().'/block/block_slider_'.$slider_id.'.php');
  }

 ?>