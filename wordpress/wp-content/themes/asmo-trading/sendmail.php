<?php
/**
 * Date: 2/23/17
 * Time: 14:28
 */
require_once('vendor/phpmailer/phpmailer/PHPMailerAutoload.php');
/**
 * todo send mail
 * @param $subject
 * @param $body
 * @return bool
 */
function toSendMail($subject, $body)
{
    $config = require_once(get_template_directory()."/config.php");
    $mail = new PHPMailer();
    $mail->isSMTP();
    $mail->CharSet = $config["SMTP_CHARSET"];
    $mail->Host = $config["SMTP_HOST"];
    $mail->SMTPAuth = true;
    $mail->Username = $config["SMTP_USERNAME"];
    $mail->Password = $config["SMTP_PASSWORD"];
    $mail->SMTPSecure = $config["SMTP_SERCURE"];
    $mail->Port = $config["SMTP_PORT"];
    $mail->setFrom($config["EMAIL_FROM"], 'asmo-trading.co.jp ');
    $mail->addAddress($config["SMTP_EMAIL_ADMIN"], 'asmo-trading.co.jp Admin');
    $mail->addBCC($config["BCC_USERNAME"]);
    $mail->isHTML(true);
    $mail->Subject = $subject;
    $mail->Body = $body;
    $mail->AltBody = $body;
    $mail->SMTPOptions = array('ssl' => array('verify_peer' => false, 'verify_peer_name' => false, 'allow_self_signed' => true));
    return $mail->send();
}