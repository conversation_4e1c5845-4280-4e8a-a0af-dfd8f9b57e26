AuthUserFile  /home/<USER>/site3/web/.htpasswd
AuthGroupFile /dev/null
AuthName "Username and Password"
AuthType Basic
require valid-user

# BEGIN WordPress
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteCond %{HTTP:Authorization} ^(.*)
RewriteRule ^(.*) - [E=HTTP_AUTHORIZATION:%1]
RewriteBase /
RewriteRule ^index\.php$ - [L]

# add a trailing slash to /wp-admin
RewriteRule ^wp-admin$ wp-admin/ [R=301,L]

RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]
RewriteRule ^(wp-(content|admin|includes).*) $1 [L]
RewriteRule ^(.*\.php)$ $1 [L]
RewriteRule . index.php [L]

</IfModule>

 #AuthUserFile C:/xampp/htdocs/wordpress/wp-content/themes/asmo/.htpasswd
 #AuthGroupFile /dev/nul
 #AuthName "Username and Password"
 #AuthType Basic
 #require valid-user


<Files admin-ajax.php>
    Order allow,deny
    Allow from all
    Satisfy any
</Files>
# END WordPress

