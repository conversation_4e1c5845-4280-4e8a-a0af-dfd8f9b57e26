// ドロワーメニュー
$(function() {
	function initializeDrawrMenu() {
		if (window.matchMedia('(max-width: 1120px)').matches) {
			const headerHeight = $('header').outerHeight(); // Use outerHeight for better accuracy
			const windowHeight = $(window).height() - headerHeight; // Calculate available height
			$('.drawr').height(windowHeight); // Set menu height
			$('.menu-btn').off('click').on('click', function() { // Ensure no duplicate event bindings
				$(this).toggleClass('active'); // Toggle active class
				$('.drawr').stop(true, true).animate({ width: 'toggle' }, 300); // Smooth animation
				$('#bg_blur').toggleClass('active'); // Toggle blur background
			});
		}
	}

	$(window).on('resize', initializeDrawrMenu); // Reinitialize on window resize
	initializeDrawrMenu(); // Initial call
});
