$(document).ready(function() {
    //todo validate format of email
    function isEmail(email){
        const regex = /^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/
        return regex.test(email)
     }

    //todo validate format of phone
     
     function isPhone(phone) {
        const regex = /^(0\d{1,4}-?\d{1,4}-?\d{4})$/
        return regex.test(phone)
     }

    //todo check blur input
    $("input[name='name']").blur(function() {
        if ($(this).val()=="") {
            $(this).parent().css('border-color', '#EB0F2C')
        } else {
            $(this).parent().css('border-color', '#C9C9C9')
        }
    });
    $("input[name='email']").blur(function() {
        if (!isEmail($(this).val())) {
            $(this).parent().css('border-color', '#EB0F2C')
        } else {
            $(this).parent().css('border-color', '#C9C9C9')
        }
    });
    $("input[name='phone']").blur(function() {
        if (!isPhone($(this).val())) {
            $(this).parent().css('border-color', '#EB0F2C')
        } else {
            $(this).parent().css('border-color', '#C9C9C9')
        }
    });
    $("input[name='agree']").blur(function() {
        if ($(this).val()==0) {
            $(this).css('border-color', '#EB0F2C')
        } else {
            $(this).css('border-color', '#C9C9C9')
        }
    });

    $('.btn-submit').on('click', function(e){
        e.preventDefault()
        if ($("input[name='name']").val()=="") {
            return false
        }
        if (!isEmail($("input[name='email']").val())) {
            return false
        }
        if (!isPhone($("input[name='phone']").val())) {
            return false
        }
        if ($("input[name='agree']:checked").val()==0) {
            return false
        }
        if ($("input[name='token']").val()=="") {
            console.log('first', $("input[name='token']").val())
            return false
        }
        $('.jobs-form').submit()
    })
});