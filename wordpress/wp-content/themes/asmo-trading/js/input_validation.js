/**
 * Created on 2/13/17.
 */
$(document).ready(function() {
     //todo validate format of email
     function isEmail(email){
          const regex = /^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/
          return regex.test(email)
      }

     //todo validate format of phone
      
      function isPhone(phone) {
          const regex = /^(0\d{1,4}-?\d{1,4}-?\d{4})$/
          return regex.test(phone)
      }

     //todo check blur input
     $("select[name='type']").blur(function() {
          if ($(this).val()=="") {
               $(this).parent().css('border-color', '#EB0F2C')
          } else {
               $(this).parent().css('border-color', '#C9C9C9')
          }
     });
     $("input[name='name']").blur(function() {
          if ($(this).val()=="") {
               $(this).parent().css('border-color', '#EB0F2C')
          } else {
               $(this).parent().css('border-color', '#C9C9C9')
          }
     });
     $("input[name='email']").blur(function() {
          if (!isEmail($(this).val())) {
               $(this).parent().css('border-color', '#EB0F2C')
          } else {
               $(this).parent().css('border-color', '#C9C9C9')
          }
     });
     $("input[name='phone']").blur(function() {
          if (!isPhone($(this).val())) {
               $(this).parent().css('border-color', '#EB0F2C')
          } else {
               $(this).parent().css('border-color', '#C9C9C9')
          }
     });
     $("textarea[name='comment']").blur(function() {
          if ($(this).val()=="") {
               $(this).parent().css('border-color', '#EB0F2C')
          } else {
               $(this).parent().css('border-color', '#C9C9C9')
          }
     });
     $("input[name='agree']").blur(function() {
          if ($(this).is(':checked')) {
               $(this).css('border-color', '#EB0F2C')
          } else {
               $(this).css('border-color', '#C9C9C9')
          }
     });

     $('.btn-submit').on('click', function(e){
          e.preventDefault()
          if ($("select[name='type']").val()=="") {
               return false
          }
          if ($("input[name='name']").val()=="") {
               return false
          }
          if (!isEmail($("input[name='email']").val())) {
               return false
          }
          if (!isPhone($("input[name='phone']").val())) {
               return false
          }
          if ($("textarea[name='comment']").val()=="") {
               return false
          }
          if ($("input[name='agree']").val()=="") {
               return false
          }
          if ($("input[name='token']").val()=="") {
               return false
          }
          $('.inquiry-form').submit()
     })
});
