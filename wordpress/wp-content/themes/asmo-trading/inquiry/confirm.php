<?php 
if ($_SERVER['REQUEST_METHOD'] !== "POST") {
    wp_redirect( get_home_url(), 301 );
}
$success = true;
// Check recaptcha
$url = "https://www.google.com/recaptcha/api/siteverify";
$data= [
    'secret' => '6LeyulUnAAAAAAcpYJLCIQFeDs840uTyw_Lx2gW8',
    'response' => $_POST["token"]
];
$curl = curl_init($url);
curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($curl);
curl_close($curl);
if (!json_decode($response)->success) { 
    $success = false;
}

$type = isset($_POST['type']) ? $_POST['type'] : "";
$company = isset($_POST['company']) ? $_POST['company'] : "";
$name = isset($_POST['name']) ? $_POST['name'] : "";
$email = isset($_POST['email']) ? $_POST['email'] : "";
$phone = isset($_POST['phone']) ? $_POST['phone'] : "";
$zip1 = isset($_POST['zip1']) ? $_POST['zip1'] : "";
$zip2 = isset($_POST['zip2']) ? $_POST['zip2'] : "";
$pref = isset($_POST['pref']) ? $_POST['pref'] : "";
$city = isset($_POST['city']) ? $_POST['city'] : "";
$address = isset($_POST['address']) ? $_POST['address'] : "";
$comment = isset($_POST['comment']) ? $_POST['comment'] : "";

get_header(); ?>

<div class="wrapper">
    <div class="content inquiry-confirm">
        <div class="title">
            <h3>お問い合わせフォーム</h3>
            <div class="img">
                <img src="<?php echo get_template_directory_uri().'/images/CATTLE-CHARCOAL.png'?>" alt="株式会社アスモトレーディング">
            </div>
        </div>
        <div class="description">
            <?php
                if ($success) {
                    echo '入力内容に問題がなければ「送信する」ボタンを押してください。';
                } else {
                    echo 'お手数をお掛け致しますがお問い合わせ入力ページへ戻り、最初から入力し直して下さい。';
                }
            ?>
        </div>
        <?php if ($success) { ?>
        <form method="post" action="/inquiry/complete">
            <div class="row-group">
                <div class="label">
                    お問い合わせ種別：
                </div>
                <div class="value">
                    <?php echo $type;  ?>
                </div>
                <input type="hidden" name="type" value="<?php echo $type;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    会社名：
                </div>
                <div class="value">
                    <?php echo $company;  ?>
                </div>
                <input type="hidden" name="company" value="<?php echo $company;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    お名前：
                </div>
                <div class="value">
                    <?php echo $name;  ?>
                </div>
                <input type="hidden" name="name" value="<?php echo $name;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    メールアドレス：
                </div>
                <div class="value">
                    <?php echo $email;  ?>
                </div>
                <input type="hidden" name="email" value="<?php echo $email;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    電話番号：
                </div>
                <div class="value">
                    <?php echo $phone;  ?>
                </div>
                <input type="hidden" name="phone" value="<?php echo $phone;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    住所：
                </div>
                <div class="value">
                    〒<?php echo $zip1;  ?> ー <?php echo $zip2;  ?>
                </div>
                <input type="hidden" name="zip1" value="<?php echo $zip1;  ?>">
                <input type="hidden" name="zip2" value="<?php echo $zip2;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    都道府県：
                </div>
                <div class="value">
                    <?php echo $pref;  ?>
                </div>
                <input type="hidden"name="pref" value="<?php echo $pref;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    市区町村・番地：
                </div>
                <div class="value">
                    <?php echo $city;  ?>
                </div>
                <input type="hidden" name="city" value="<?php echo $city;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    建物名称他：
                </div>
                <div class="value">
                    <?php echo $address;  ?>
                </div>
                <input type="hidden" name="address" value="<?php echo $address;  ?>">
            </div>
            <div class="row-group">
                <div class="label">
                    お問い合わせ内容：
                </div>
                <div class="value">
                    <?php echo $comment;  ?>
                </div>
                <input type="hidden" name="comment" value="<?php echo $comment;  ?>">
            </div>
            <div class="row-action">
                <input type="button" onclick="history.back()" value="入力画面に戻る" class="btn-back"/>
                <button class="btn-send" type="submit">
                    <div class="img">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"/></svg>
                    </div>
                    <div class="text">
                        送信する
                    </div>
                </button>
            </div>
        </form>
        <?php }  else { ?>
            <script>
                setTimeout(() => {
                    window.history.back(); // Quay lại trang trước
                }, 4000);
            </script>
        <?php } ?>
        <br>
    </div>
</div>

<?php get_footer(); ?>