<?php
get_header(); 
if ($_SERVER['REQUEST_METHOD'] === "POST") {
  
    require_once(get_template_directory()."/sendmail.php");

    //todo get info from index form
	$type = isset($_POST['type']) ? $_POST['type'] : "";
	$company = isset($_POST['company']) ? $_POST['company'] : "";
	$name = isset($_POST['name']) ? $_POST['name'] : "";
	$email = isset($_POST['email']) ? $_POST['email'] : "";
	$phone = isset($_POST['phone']) ? $_POST['phone'] : "";
	$zip1 = isset($_POST['zip1']) ? $_POST['zip1'] : "";
	$zip2 = isset($_POST['zip2']) ? $_POST['zip2'] : "";
	$pref = isset($_POST['pref']) ? $_POST['pref'] : "";
	$city = isset($_POST['city']) ? $_POST['city'] : "";
	$address = isset($_POST['address']) ? $_POST['address'] : "";
	$comment = isset($_POST['comment']) ? $_POST['comment'] : "";

    $bodyContent = "お問い合わせフォーム <br/>";
    $bodyContent .= "<b>お問い合わせ種別 : </b>" . $type . "<br/>";
    $bodyContent .= "<b>会社名 : </b>" . $company . "<br/>";
    $bodyContent .= "<b>お名前 : </b>" . $name . "<br/>";
    $bodyContent .= "<b>メールアドレス : </b>" . $email . "<br/>";
    $bodyContent .= "<b>電話番号 :</b>" . $phone . "<br/>";
    if (!empty($address)) {
        $bodyContent .= "<b>住所 : </b> 〒" . $zip1 . "-" . $zip2 .  "<br/>" . $address . "<br/>";
    }
    $bodyContent .= "<b>お問い合わせ内容 : </b>" . $comment . "<br/>";
    
    // todo send mail
    $success = toSendMail('この度は、お問い合わせいただき、誠にありがとうございます。', $bodyContent);
?>

<div class="wrapper">
    <div class="content inquiry-complete">
      	<div class="title">
			<h3>お問い合わせフォーム</h3>
			<div class="img">
				<img src="<?php echo get_template_directory_uri().'/images/CATTLE-CHARCOAL.png'?>" alt="株式会社アスモトレーディング">
			</div>
		</div>
		<div class="description">
			<?php if ($success) { ?>
				<h4>送信が完了しました</h4>
				<p>この度は、お問い合わせいただき、誠にありがとうございます。<br>後日、折り返しご連絡致しますのでしばらくお待ちください。</p>
				<br>
			<?php } else { ?>
				<h4>送信エラーが発生しました</h4>
				<p>お手数をお掛け致しますがお問い合わせ入力ページへ戻り、最初から入力し直して下さい。</p>
				<br>
			<?php } ?>
		</div>
		<a href="<?php echo get_home_url(); ?>" class="btn-home">
			<div class="img">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"/></svg>
			</div>
			<div class="text">
				トップページへ
			</div>
		</a>
    </div>
</div>
<?php } else {
    wp_redirect( get_home_url(), 301 );
}
get_footer();