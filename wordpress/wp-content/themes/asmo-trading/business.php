<?php
$bg_image = isset($args['bg_image']) ? $args['bg_image'] : get_template_directory_uri() . '/images/business/BUSINESS CONTENTS BG.png';
?>

<div class="business" style="background-image: url('<?php echo esc_url($bg_image); ?>');">
    <div class="business__content">
        <div class="business__main">
            <div class="business__group business__group--menu-main">
                <?php
                $menu_items = [
                    ['title' => '長年にわたる輸入経験を活かした商品輸入', 'id' => 'si'],
                    ['title' => 'メキシコ産冷蔵牛肉の輸入・販売', 'id' => 'mb'],
                    ['title' => 'メキシコ産牛肉の購入元（動画）', 'id' => 'mv'],
                    ['title' => 'あらゆる種類の食肉販売', 'id' => 'sm'],
                    ['title' => 'お取引の流れ', 'id' => 'fl'],
                ];

                foreach ($menu_items as $item) : ?>
                    <div class="business__group--menu">
                        <img src="<?php echo get_template_directory_uri() . '/images/business/icon/Icon awesome-chevron-circle-right.png' ?>"
                             alt="商品輸入"/>
                        <div class="business__group--menu-contents">
                            <p>
                                <a href="#<?php echo esc_attr($item['id']); ?>"><?php echo esc_html($item['title']); ?></a>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <?php
        $sections = [
            [
                'circle_image' => get_template_directory_uri() . '/images/business/pc/Business details-2.png',
                'mobile_circle_image' => get_template_directory_uri() . '/images/business/mobile/Business details-2.png',
                'headerTitle' => '長年にわたる輸入経験を活かした商品輸入',
                'content' => 'アスモトレーディングは食肉の輸入において食肉の自由化以後様々な商品を輸入してきました。<br />
                              直接現地に出向いて生産される工程を確認し、日本のマーケットに見合った商品を厳選し輸入をしております。<br />
                              これまでの経験から特に牛肉においては牛の種類、給餌する飼料の改良や無駄のない商品の規格作りを行い、
                              日本の食肉<br>マーケットに見合う商品開発に取り組んでまいりました。<br />
                              国ごとに特徴のある食肉ですが世界情勢や為替変動、BSE（牛海綿状脳症）などの影響を受けながらその時点
                              での最適な商品を厳選し、商品輸入を行ってきた経験から私たちは「良質な産地からお客様へ」をテーマに
                              「おいしさ」はもとより「安心」「安全」であることにこだわり、お客様の満足度を高める商品を輸入し販売
                              して行くことを続けてまいります。',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png',
                'title' => '長年にわたる輸入経験を活かした商品輸入',
                'class' => '',
                'id' => sanitize_title('si'),
            ],
            [
                'circle_image' => get_template_directory_uri() . '/images/business/pc/pasture-6715954_1280.png',
                'mobile_circle_image' => get_template_directory_uri() . '/images/business/mobile/pasture-6715954_1280.png',
                'headerTitle' => '長年にわたる輸入経験を活かした商品輸入',
                'content' => '当社はアメリカ合衆国におけるＢＳＥ発生の後、メキシコ産牛肉に着目し、2004年6月には日本で初めて <br />
                   メキシコ産冷蔵牛肉の取り扱いを開始いたしました。<br />
                   冷凍の商品と違い、コンテナによる海上輸送中に商品が熟成されることで商品に柔らかさと美味しさを <br />
                   もたらしております。当初は知名度もなく商品の優位性をお客様に伝えることが難しく苦労しましたが、 <br />
                   お客様に商品の良さを知っていただくことで継続的にご使用いただいております。一言にメキシコ産牛肉 <br />
                   と言っても様々な商品がありますので、その中で品質や規格にこだわりを持っております。<br />
                   お客様に美味しさや価格の違いを説明することでメキシコ産牛肉の中でもお客様に満足いただける商品を<br />
                   厳選して輸入しております。',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png',
                'title' => 'メキシコ産冷蔵牛肉の輸入・販売',
                'class' => 'row-reverse',
                'id' => sanitize_title('mb'),
            ],
            [
                'video' => get_template_directory_uri() . '/video/ボナプライム字幕付きビデオ.mp4',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-GOLD.png',
                'title' => 'メキシコ産牛肉の輸入元',
                'text' => '当社では、「ＢＯＮＡ　ＰＲＩＭＥ（ボナプライム）」ブランドのメキシコ産牛肉を輸入しております。<br/> こちらの動画では、グループの特徴や牧場・工場のこだわりについてご紹介いたします。',
                'class' => 'business__main--video',
                'id' => sanitize_title('mv'),
            ],
            [
                'circle_image' => get_template_directory_uri() . '/images/business/pc/32708743_871505966371209_730019701584822272_n.png',
                'mobile_circle_image' => get_template_directory_uri() . '/images/business/mobile/32708743_871505966371209_730019701584822272_n.png',
                'headerTitle' => '長年にわたる輸入経験を活かした商品輸入',
                'content' => '食肉の販売においては牛肉の取扱いだけでなく豚肉・鶏肉をはじめお客様の望む様々な種類の食肉の販売
                              を行っております。輸入・国産を問わず、長年の経験を生かしてお客様に最適な商品を提案しております。<br />
                              これまでの経験から特に牛肉においては牛の種類、給餌する飼料の改良や無駄のない商品の規格作りを行い、
                              お客様のニーズを把握することでマーケットの動きを察知し、価格訴求だけでなくお客様にとっての最善の方法を一緒に考えて行くことでお客様の満足度を上げていく販売を行ってまいります。<br>
                              提携している加工工場において製造された商品の販売も行っており、カタログ販売やWEB販売などの通信販売事業にも力を入れております。お中元やお歳暮などライフスタイルの変化から縮小している分野もありますが逆に配送手段などが進化して拡大している分野もあり、お客様の購買意欲を高める商品を開発することで販売の拡大をはかってまいります。特に銘柄牛を使用した商品を多く取り扱っており、法令順守すべき点など管理方法にも常に注意をはらっております。',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png',
                'title' => 'あらゆる種類の食肉販売',
                'class' => '',
                'image_foot' => get_template_directory_uri() . '/images/business/pc/繧｢繧ｹ繝｢繝槭・繝医Ο繧ｴ3.png',
                'image_foot_mobile' => get_template_directory_uri() . '/images/business/mobile/繧｢繧ｹ繝｢繝槭・繝医Ο繧ｴ1-online.png',
                'id' => sanitize_title('sm'),
            ]
        ];

        foreach ($sections as $section) : ?>
            <div id="<?php echo esc_attr($section['id']); ?>"
                 class="business__main <?php echo esc_attr($section['class']); ?>">
                <div class="business__group">
                    <?php if (!empty($section['circle_image'])) : ?>
                        <?php
                        set_query_var('circle_image', $section['circle_image']);
                        set_query_var('mobile_circle_image', $section['mobile_circle_image']);
                        error_log('Mobile Circle Image: ' . $section['mobile_circle_image']);
                        set_query_var('headerTitle', $section['headerTitle']);
                        get_template_part('group');
                        ?>
                    <?php endif; ?>

                    <?php if (!empty($section['video'])) : ?>
                        <div class="video_section--business__main--video">
                            <center class="business__group--CATTLE-BLACK video_section">
                                <h2><?php echo esc_html($section['title']); ?></h2>
                                <img src="<?php echo esc_url($section['image']); ?>" alt="商品輸入"/>
                            </center>
                        </div>

                        <div class="business__main--video-link">
                            <video class="youtube_links" loop="true" autoplay="autoplay" controls="controls" id="vid"
                                   muted>
                                <source src="<?php echo esc_url($section['video']); ?>" type="video/mp4">
                            </video>
                        </div>
                    <?php endif; ?>

                    <div class="business__group-content">
                        <?php if (!empty($section['title'])) : ?>
                            <center class="business__group--CATTLE-BLACK">
                                <h2><?php echo esc_html($section['title']); ?></h2>
                                <img src="<?php echo esc_url($section['image']); ?>" alt="商品輸入"/>
                            </center>
                        <?php endif; ?>

                        <?php if (!empty($section['content'])) : ?>
                            <p><?php echo $section['content']; ?>
                            <br>
                            <?php if (!empty($section['image_foot'])) : ?>
                                <div style="width: 100%; text-align: center;display: flex; justify-content: center;">
                                    <img class="image_foot" src="<?php echo esc_url($section['image_foot']); ?>"
                                         alt="商品輸入"/>
                                    <img class="image_foot_mobile"
                                         src="<?php echo esc_url($section['image_foot_mobile']); ?>" alt="商品輸入"/>
                                </div>
                            <?php endif; ?>
                            </p>
                        <?php endif; ?>

                        <?php if (!empty($section['text'])) : ?>
                            <div class="business__group--text">
                                <!-- <?php echo esc_html($section['text']); ?> -->
                                <?php echo wp_kses_post($section['text']); ?>
                            </div>
                            メキシコ産　チルドビーフ
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <div class="main_step">
        <center class="business__group--CATTLE-BLACK">
            <h2>お取引の流れ</h2>
            <img src="<?php echo get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png'; ?>"
                 alt="商品輸入">
        </center>
        <div class="business_groups">
            <div class="business_step-group">
                <div class="image-container">
                    <img class="pc_step"
                         draggable="false"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 1.png'; ?>"
                         alt="商品輸入">
                    <img class="sp_step"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 1_SP.png'; ?>"
                         draggable="false"
                         alt="商品輸入">
                    <div class="overlay-text">
                        <p class="text-header">お取引の流れ</p>
                        <p>
                            まずは、当ホームページのお問い合わせフォームにご記入の上、送信して下さい。<br>
                            商品に関するご質問やお見積りの依頼等、何でも構いません。<br>
                            お気軽にお問い合わせください。<br>
                            内容確認後、担当者よりご連絡させて頂きます。
                        </p>
                    </div>
                </div>
            </div>

            <div class="business_step-group business_step-group-left">
                <div class="image-container">
                    <img class="pc_step"
                         draggable="false"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 2.png'; ?>"
                         alt="商品輸入">
                    <img class="sp_step"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 2_SP.png'; ?>"
                         alt="商品輸入">
                    <div class="overlay-text">
                        <p class="text-header">お打ち合わせ</p>
                        <p>
                            メールまたはお電話にてお問い合わせ内容にお答えいたします。<br>
                            お打ち合わせの内容によっては、訪問させて頂き、対面での打ち合わせ<br>
                            をする事も可能です。<br>
                            気になる商品やお困り事などがありましたら、遠慮なくお尋ねください。<br>
                        </p>
                    </div>
                </div>
            </div>

            <div class="business_step-group">
                <div class="image-container">
                    <img class="pc_step"
                         draggable="false"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 3.png'; ?>"
                         alt="商品輸入">
                    <img class="sp_step"
                         draggable="false"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 3_SP.png'; ?>"
                         alt="商品輸入">
                    <div class="overlay-text">
                        <p class="text-header">商品提案・見積書提出</p>
                        <p>
                            お打ち合わせいただいた内容を基に、お客様のニーズに合った商品をご <br>
                            提案させて頂きます。
                        </p>
                    </div>
                </div>
            </div>

            <div class="business_step-group business_step-group-left">
                <div class="image-container">
                    <img class="pc_step"
                         draggable="false"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 4.png'; ?>"
                         alt="商品輸入">
                    <img class="sp_step"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 4_SP.png'; ?>"
                         alt="商品輸入">
                    <div class="overlay-text">
                        <p class="text-header">ご注文・ご契約</p>
                        <p>
                            お打ち合わせ内容に基づいてご注文をお願いいたします。<br>
                            お取引時のお支払いは、事前入金となります。<br>
                            継続的なお取引の場合は、決済条件などの諸条件を確認後、売買基本契約<br>
                            書を締結させていただきます。
                        </p>
                    </div>
                </div>
            </div>

            <div class="business_step-group">
                <div class="image-container">
                    <img class="pc_step"
                         draggable="false"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 5.png'; ?>"
                         alt="商品輸入">
                    <img class="sp_step"
                         draggable="false"
                         src="<?php echo get_template_directory_uri() . '/images/step/STEP 5_SP.png'; ?>"
                         alt="商品輸入">
                    <div class="overlay-text">
                        <p class="text-header">商品お渡し・納品</p>
                        <p>
                            当社が指定する配送業者による納品、またはご指定いただいた場所への納<br>
                            入を行うか、ご指定の配送業者への引き渡しいたします
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        document.querySelectorAll('.video_section--business__main--video').forEach(element =>
            element.closest('.business__group')?.classList.add('business__group--video-custom')
        );
    });
</script>

<style>

    center h2 {
        font-family: 'FOT-Seurat ProN DB', sans-serif;
        font-size: 24px;
        text-align: center;
        line-height: 48pt;
        letter-spacing: 0pt;
        margin: 0;
        padding: 0;
    }

    .business__group--CATTLE-BLACK {
        padding: 20px 0;
    }

    .main_step {
        background-color: #ffffff;
        padding: 50px 0;
    }

    .business_groups {
        max-width: 1367px;
        width: 100%;
        margin: 0 auto;
    }

    .business_step-group {
        position: relative;
        margin-bottom: 50px;
    }

    .business_step-group-left {
        display: flex;
        justify-content: flex-end;
    }

    .business_step-group-left .overlay-text {
        left: 39%;
    }

    .image-container {
        position: relative;
        display: inline-block;
    }

    .image-container img {
        width: 100%;
        height: auto;
        display: block;
    }

    .overlay-text {
        position: absolute;
        top: 50%;
        left: 75%;
        transform: translate(-50%, -50%);
        color: #212121;
        width: 58%;
    }

    .overlay-text p {
        font-weight: 300
    }

    .overlay-text .text-header {
        font-size: 18px;
        line-height: 2.25rem;
        font-weight: bold;
    }

    .pc_step {
        display: block !important;
    }

    .sp_step {
        display: none !important;;
    }

    .business__group {
        gap: 20px;
        padding: 50px 0;
    }

    .business__content {
        padding-top: 50px;
    }

    .business__main--menu p {
        font-family: FOT-Seurat ProN DB, Regular;
        line-height: 1.5;
    }

    .business__group--menu-main {
        display: none;
    }

    .image_foot {
        display: block;
    }

    .image_foot_mobile {
        display: none;
    }

    .business__main center h2 {
        font-family: 'FOT-Seurat ProN DB', sans-serif;
        font-size: 24px;
        text-align: center;
        line-height: 48pt;
        letter-spacing: 0pt;
        margin: 0;
        padding: 0;
    }

    .business__main .business__group-content p {
        font-family: 'Kozuka Gothic Pr6N', sans-serif;
        line-height: 40px;
        letter-spacing: 0pt;
        margin: 0;
        padding: 0;
        color: #212121;
        margin-top: 20px;
    }

    .description-right b {
        font-family: 'FOT-Seurat ProN DB', sans-serif;
        font-size: 18px;
        line-height: 36pt;
        letter-spacing: 0pt;
        padding: 0;
        color: #212121;
    }

    .description-right p {
        font-size: 16px;
        line-height: 30pt;
        color: #212121;
    }

    .video_section {
        display: none;
    }

    .business__group--CATTLE-BLACK {
        height: auto;
    }

    @media screen and (max-width: 768px) {

        .business__content {
            padding-top: 0;
        }

        .business__group--menu-main {
            display: block;
        }

        .business {
            padding-top: 50px;
        }

        .business__main {
            margin: 0 20px 0 20px;
        }

        .business__main center h2 {
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 18px;
            text-align: center;
            line-height: 36pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
        }

        .business__main p {
            font-family: 'Kozuka Gothic Pr6N', sans-serif;
            font-size: 16px;
            line-height: 30pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;

        }

        .business__group--menu-contents p {
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 13px;
            line-height: 26pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
        }

        .video_section--business__main--video {
            display: block;
            margin: 0 auto;
            color: #FFD700;
        }

        .video_section {
            display: block;
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 18px;
        }

        .description-right b {
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 18px;
            line-height: 36pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
            color: #212121;
        }

        .description-right p {
            font-family: 'Kozuka Gothic Pr6N, R', regular;
            font-size: 16px;
            line-height: 26pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
            color: #212121;
        }

        .image_foot {
            display: none;
        }

        .image_foot_mobile {
            display: block;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .pc_step {
            display: none !important;;
        }

        .sp_step {
            display: block !important;
            width: 100% !important;
            height: 40rem !important;
        }

        .overlay-text {
            position: absolute;
            top: 57%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #212121;
            width: 76%;
        }

        .business_step-group-left .overlay-text {
            left: 50%;
        }

        .main_step {
            padding: 20px;
        }
    }

    .business__main--groups-step .business__group--CATTLE-BLACK {
        padding: 40px;
    }

    .business__main--groups-step h2 {
        font-family: 'FOT-Seurat ProN DB', sans-serif;
        font-size: 30px;
        line-height: 36pt;
        letter-spacing: 0pt;
        color: #212121;
    }
</style>