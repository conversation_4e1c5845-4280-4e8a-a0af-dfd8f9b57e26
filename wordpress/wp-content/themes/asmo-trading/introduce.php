<?php
$bg_image = isset($args['bg_image']) ? $args['bg_image'] : get_template_directory_uri() . '/images/business/BUSINESS CONTENTS BG.png';
?>

<div class="business business__introduce" style="background-image: url('<?php echo esc_url($bg_image); ?>');">
    <div class="business__content">
        <div class="business__main">
            <div class="business__group business__group--menu-main">
                <?php
                $menu_items = [
                    ['title' => 'メキシコ産牛肉って美味しいの？', 'id' => 'si'],
                    ['title' => 'メキシコ産牛肉って安心安全なの？', 'id' => 'mb'],
                    ['title' => 'メキシコ産牛肉の特徴は？', 'id' => 'mv'],
                    ['title' => 'メキシコってどんな国？', 'id' => 'sm'],
                ];

                foreach ($menu_items as $item) : ?>
                    <div class="business__group--menu">
                        <img src="<?php echo get_template_directory_uri() . '/images/business/icon/Icon awesome-chevron-circle-right.png' ?>" alt="商品輸入" />
                        <div class="business__group--menu-contents">
                            <p><a href="#<?php echo esc_attr($item['id']); ?>"><?php echo esc_html($item['title']); ?></a></p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        $sections = [
            [
                'title' => 'メキシコ産牛肉って美味しいの？',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png',
                'content' => '
                    国産牛肉には、和牛や乳牛といった品種の違いに加え、育つ環境や地域の違いによって、品質に大きな差が
                    あります。そのため、こだわりなく商品を選ぶと、思っていたより肉質が硬かったり、サイズが違ったり、
                    うまみが少なかったりすることがあります。
                    メキシコにもさまざまな種類の牛がいて、牛の品種や給餌する飼料、肥育日数、気候 などの要因によって
                    肉質が変わります。<br>日本の消費者に満足していただくためには、メキシコ国内向けの基準ではなく、日本 
                    市場のことも意識して生産している企業と取引をすることが重要です。  
                    現在、弊社が輸入している牛肉は、日本向けにもしっかりと考えられたものです。肉の部位による違いはあ 
                    りますが、実際に召し上がっていただくと、その品質に納得していただけると思います。<br>
                    これまで多くの方にご試食いただき、たくさんの『美味しい』というお声をいただいております。
                ',
                'circle_image' => get_template_directory_uri() . '/images/business/pc/Mexico meet-2.png',
                'mobile_circle_image' => get_template_directory_uri() . '/images/business/mobile/Mexico meet-2.png',
                'id' => sanitize_title('si'),
            ],
            [
                'title' => 'メキシコ産牛肉って安心安全なの？',
                'class' => 'business__main--cattle-black-introduction',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png',
                'content' => '
                    地域によっても違いがありますが、弊社が購買している「BONA PRIME(ボナプライム）」というブランドは、
                    メキシコ北西部に位置しています。この地域は、南北に広がる砂漠、西に太平洋、東にはロッキー山脈から続く
                    シエラマドレ山脈があり、病原菌が侵入しにくい環境にあります。
                    その為、メキシコ国内ではBSE（牛海綿状脳症）の発生がなく、口蹄疫等の伝染病も確認されておりません。
                    また、使用が禁止されている薬物（クレンブテロール）などは政府によって規制されており、安全性が確保され 
                    されています。
                    基本的に成長促進剤は使用されていない為、アメリカなどの他の地域の牛に比べると体格は小さいものの、牛の
                    品種や肉質等は他国と比べても遜色ないと言えます。
                    弊社が輸入している「BONA PRIME（ボナプライム）」の工場においては、厳格な検査を経た上で出荷されて 
                    いるため、安全性の高い商品として安心してお召し上がりいただけます。
                ',
                'circle_image' => get_template_directory_uri() . '/images/business/pc/Picture1.png',
                'mobile_circle_image' => get_template_directory_uri() . '/images/business/mobile/Picture1.png',
                'id' => sanitize_title('mb'),
            ],
            [
                'title' => 'メキシコ産牛肉の特徴は？',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png',
                'content' => '
                    メキシコ産牛肉の特徴として、一つ一つの商品は全体的に小振りな傾向があります。牛の月齢はアメリカ産と
                    大きく変わりませんが、メキシコ産牛肉に対しては成長促進剤を使用していないことが要因と考えられます。
                    また、気温の高い地域が多いため、肉の中に「サシ」と呼ばれる霜降り状の脂が入りにくく、赤身主体の牛肉
                    となる傾向があります。
                    <br>
                    一般的に、サシが多いと旨みが強く、柔らかさも増しますが、育成方法によっては、
                    サシが少なくても十分な柔らかさと旨みを兼ね備えた商品を生産することが可能です。
                    小振りであることを活かせば、牛肉をスライスやカットした際に厚みのある商品を作ることができ、さらに
                    柔らかさを引き出すことも可能です。地域によって気候が変わり、収穫される穀物に違いが生じると、与える
                    牛の飼料も変わってきます。「BONA PRIME（ボナプライム）」は地元産の小麦やトウモロコシなど
                    の穀物を使用した肥育が行われて、ほかのメキシコの地域においても飼料の違いはありますが輸出を視野に
                    入れた穀物肥育が主流となっています。
                    この飼料の種類によって、肉の味・香り・色合いが変化するため、商品の品質を重視するメキシコの企業と
                    連携することで、日本市場向けの商品開発も可能です。
                    メキシコ産牛肉は旨みに優れているだけでなく、臭みがほとんどありません。そのため、調味料で臭みを消し
                    たり、調味液に漬け込んで別の香りでごまかしたりする必要がなく、特に調理店においてはお店独自のソース
                    による繊細な味付けでの調理する事が可能です。
                    ',
                'circle_image' => get_template_directory_uri() . '/images/business/pc/kobe-3136608_1280.png',
                'mobile_circle_image' => get_template_directory_uri() . '/images/business/mobile/kobe-3136608_1280.png',
                'id' => sanitize_title('mv'),
            ],
            [
                'title' => 'メキシコってどんな国？',
                'class' => 'business__main--cattle-black-introduction topics-introduction',
                'image' => get_template_directory_uri() . '/images/business/pc/CATTLE-BLACK.png',
                'content' => '
                    地域によっても異なりますが、多くの方が抱くメキシコのイメージは「ラテンの暑い国」ではない 
                    でしょうか。メキシコ北部では乾燥した地域では雨が少なく、雲もほとんどなく、見渡す限りの青空 
                    が広がっており、気温は時折40℃を超える厳しい暑さとなります。南の方に行くと湿度が高くなり
                    熱帯特有の暑さとなります。
                    どの地域においてもメキシコの人々は仕事に対して情熱的であり、手先が器用で丁寧に仕事をこなし
                    ます。日本企業からの依頼は細かい要望が多いですが、それにも丁寧に対応してくれます。
                    牛肉産業においては家族経営が多く、兄弟で放牧農家、肥育農家、屠畜工場、パッキング工場それ
                    ぞれを運営し、家族内で管理しているケースが一般的です。その為、アメリカやオーストラリアに
                    おいては、放牧、肥育、屠畜およびパッキング工場は全くの別会社が運営していることが多いので
                    価格や品質の安定をコントロールできませんが、メキシコにおいては家族経営の為、価格や品質の
                    安定を図ることが出来ます。
                ',
                'circle_image' => get_template_directory_uri() . '/images/business/pc/pexels-rdne-8639346.png',
                'mobile_circle_image' => get_template_directory_uri() . '/images/business/mobile/pexels-rdne-8639346.png',
                'id' => sanitize_title('sm'),
            ],
        ];

        foreach ($sections as $index => $section) :
            if (in_array($section['class'], ['business__main--cattle-black-introduction'])) echo '<div>';
            $additional_class = isset($section['class']) ? $section['class'] : '';

        ?>
            <div id="<?php echo esc_attr($section['id']); ?>" class="business__main <?php echo $additional_class; ?>">
                <div class="business__group">
                    <?php
                    set_query_var('circle_image', $section['circle_image']);
                    set_query_var('mobile_circle_image', $section['mobile_circle_image']);
                    set_query_var('headerTitle', $section['title']);
                    get_template_part('group');
                    ?>
                    <div class="business__group-content">
                        <center class="business__group--CATTLE-BLACK">
                            <h2><?php echo esc_html($section['title']); ?></h2>
                            <img src="<?php echo esc_url($section['image']); ?>" alt="<?php echo esc_attr($section['title']); ?>" />
                        </center>
                        <p>
                            <?php echo $section['content']; ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<style>
    .business__content {
        padding-top: 50px;
    }

    .business__introduce .business__main--cattle-black-introduction .business__group {
        flex-direction: row-reverse;
    }

    .business__introduce .topics-introduction .business__circle--yellow-double {
        left: 24px;
    }

    .business__introduce .topics-introduction .business__circle--white-double {
        left: 5px;
    }

    .business__introduce .topics-introduction .business__group {
        gap: 7rem;
        height: auto;
    }

    .business__introduce .business__circle--image {
        height: 304px;
        width: 304px;
    }

    .business__introduce .topics-introduction .business__circle--image-double {
        left: -5rem;
        top: 16rem;
        width: 219px;
        height: 219px;
        border-radius: 50%;
        z-index: 3;
    }

    .business__main--menu {
        width: max-content;
        margin: 0 auto;
    }

    .business__main--menu p {
        font-family: FOT-Seurat ProN DB, Regular;
        line-height: 1.5;
    }

    .business__group--menu-main {
        display: none;
    }

    .image_foot {
        display: block;
    }

    .image_foot_mobile {
        display: none;
    }

    .business__main center h2 {
        font-family: 'FOT-Seurat ProN DB', sans-serif;
        font-size: 24px;
        text-align: center;
        line-height: 48pt;
        letter-spacing: 0pt;
        margin: 0;
        padding: 0;
    }

    .business__main .business__group-content p {
        font-family: 'Kozuka Gothic Pr6N', sans-serif;
        /*font-size: 16px;*/
        line-height: 40px;
        margin: 0;
        padding: 0;
        color: #212121;
        margin-top: 20px;
    }

    .description-right b {
        font-family: 'FOT-Seurat ProN DB', sans-serif;
        font-size: 18px;
        line-height: 36pt;
        letter-spacing: 0pt;
        margin: 20px;
        padding: 0;
        color: #212121;
    }

    .description-right p {
        font-family: 'Kozuka Gothic Pr6N, R', regular;
        font-size: 16px;
        line-height: 26pt;
        letter-spacing: 0pt;
        margin: 20px;
        padding: 0;
        color: #212121;
    }

    .video_section {
        display: none;
    }

    .topics-introduction .business__circle--double {
        display: block !important;
    }

    .business__group--CATTLE-BLACK {
        height: auto;
    }

    .business__main--cattle-black-introduction .business__circle--black {
        right: 0;
        left: auto;
    }

    .business__main--cattle-black-introduction .business__circle--yellow {
        right: auto;
        left: 3rem;
    }

    .business__main--cattle-black-introduction .business__circle--white {
        right: auto;
        left: 0.75rem;
    }

    @media screen and (max-width: 768px) {

        .business__content {
            padding-top: 0;
        }

        .business__introduce .business__main--cattle-black-introduction .business__group {
            flex-direction: column;
        }

        .business__introduce .business__circle {
            position: relative;
            left: 0;
            top: 0;
            width: 255px;
            height: 247px;
            border: 1px solid #FFD700;
            opacity: 1;
            border-radius: 50%;
            z-index: 1;
        }

        .business__introduce .business__circle--black {
            left: 0;
            top: 25%;
            height: 36px;
            width: 36px;
            background: #000;
        }

        .business__introduce .business__circle--yellow {
            right: 24px;
            top: 10%;
            height: 24px;
            width: 24px;
            background: #FFD700;
        }

        .business__introduce .business__circle--white {
            right: 6px;
            top: 25%;
            height: 16px;
            width: 15px;
            background: #F5F5DC;
        }

        .business__introduce .business__item {
            border: none;
            margin-bottom: 113px;
        }

        .business__introduce .business__circle--double {
            display: none;
        }


        .business__introduce .topics-introduction .business__circle--image-double {
            top: 9rem;
            left: -4rem;
        }

        .business__introduce .business__circle--image {
            left: 25px;
            bottom: 0;
            height: 209px;
            width: 210px;
        }

        .business__introduce .topics-introduction .business__circle--double {
            display: block;
            left: -4pc;
            top: -153px;
        }

        .business__introduce .business__circle--image-mobile {
            left: 19px;
            bottom: -36px;
            height: 100%;
            width: 100%;
        }


        .business__group--menu-main {
            display: block;
        }

        .business {
            padding-top: 50px;
        }

        .business__main {
            margin: 0 30px 0 30px;
        }

        .business__groups {
            margin: 0 auto;
        }

        .business__main center h2 {
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 18px;
            text-align: center;
            line-height: 36pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
        }

        .business__main p {
            font-family: 'Kozuka Gothic Pr6N', sans-serif;
            font-size: 16px;
            line-height: 30pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
        }

        .business__group--menu-contents p {
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 13px;
            line-height: 26pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
        }

        .video_section--business__main--video {
            display: block;
            margin: 0 auto;
            color: #FFD700;
        }

        .video_section {
            display: block;
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 18px;
        }

        .business__main--icon-description {
            display: block;
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 12px;
            line-height: 24pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
            color: #212121;
        }

        .description-right b {
            font-family: 'FOT-Seurat ProN DB', sans-serif;
            font-size: 18px;
            line-height: 36pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
            color: #212121;
        }

        .description-right p {
            font-family: 'Kozuka Gothic Pr6N, R', regular;
            font-size: 16px;
            line-height: 26pt;
            letter-spacing: 0pt;
            margin: 0;
            padding: 0;
            color: #212121;
        }

        .business__main--dashed,
        .business__main--solid {
            height: 600px;
        }

        .business__main--description {
            height: 575px;
        }

        .business__main--icon {
            bottom: 10%;
        }

        .business__main--icon::after {
            height: 70px;
            width: 0.25rem;
            left: 35px;
        }

        .business__main--icon::before {
            top: 8rem;
            left: 1.65rem;
        }

        .image_foot {
            display: none;
        }

        .image_foot_mobile {
            display: block;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .topics-introduction .business__item {
            left: 6%;
        }

        .topics-introduction .business__circle--double {
            top: -134px;
        }

        .topics-introduction .business__circle--image-double {
            left: -3rem;
            top: 10rem;
        }

        .business__introduce .business__circle--black {
            left: -10px;
        }

        .business__introduce .business__main--cattle-black-introduction .business__group {
            margin: 0 auto;
        }
        .business__main--cattle-black-introduction .business__circle--black {
            right: -10px;
            left: auto;
        }

        .business__main--cattle-black-introduction .business__circle--yellow {
            right: auto;
            left: 1.75rem;
        }

        .business__main--cattle-black-introduction .business__circle--white {
            right: auto;
            left: 0.25rem;
        }
       
    }
</style>