@charset "utf-8";
/*===============================================
●smart.css  画面の横幅が640pxまで
===============================================*/
@media screen and (max-width:640px){
	body{
		font-size:15px;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	img{
		max-width: 100%;
		height: auto;
	}
	sup{
		font-size: 70%;
	}
	/***** index *****/
	#container{
		min-width: 100%;
	}
	.header {
		min-width: 100%;
	}
	.headerin {
		max-width:640px;
		margin:0 auto;
	}
	.headerin h1{
		font-size:12px;
		padding:0 50px 10px 10px
	}
	.headerin div .nav_sp_logo{
		display: block;
		width: 40%;
		margin: 0 auto;
		padding-bottom: 30px;
	}
	.headerin div.inquiry{
		width:70%;
		margin:0 auto 10px auto;
	}
	.headerin div.inquiry a{
		height:40px;
		line-height:40px;
		border-radius:40px;
		display:block;
		text-align:center;
		background:#936310;
		color:#fff;
		font-weight:bold;
		font-size:110%;
		text-decoration:none;
	}
	.headerin div.inquiry a:hover{
		background:#704a09;
	}

	
	
	/* .drawr ul {
	  	display: block;
	  	margin: 0;
	  	padding: 0;
	} */
	/* .drawr ul li {
	  	display: block;
	  	margin: 0;
	  	line-height: 40px;
	} */
	/* .drawr ul li:hover, .drawr ul li.active, .drawr ul li.sidr-class-active {
	  	border-top: 0;
	  	line-height: 41px;
	} */
	/* .drawr ul li:hover > a,
	.drawr ul li:hover > span, .drawr ul li.active > a,
	.drawr ul li.active > span, .drawr ul li.sidr-class-active > a,
	.drawr ul li.sidr-class-active > span {
	  	color: #c42529;
	} */
	/* .drawr ul li a{
	  	display: block;
	  	padding:0 15px;
	  	line-height:25px;
	  	font-size:75%;
	  	text-decoration: none;
	  	color: #333333;
	  	text-align: center;
	} */
	.dropmenu li a.active {
		background: #c42529;
		color: #fff !important;
	}
	.dropmenu li a.inquiry.active{
		background: #c42529 !important;
		color: #fff !important;
	}
	/* .drawr ul li span{
		display:block;
		line-height:20px;
	}
	.drawr ul li ul {
	  	border-bottom: 0;
	  	margin: 0;
	}
	.drawr ul li ul li {
		height:30px;
	  	line-height: 25px;
	  	font-size: 14px;
	  	background:#eee;
	}
	.drawr ul li ul li:last-child {
	  	border-bottom: 0;
	}
	.drawr ul li ul li:hover, .drawr ul li ul li.active, .drawr ul li ul li.sidr-class-active {
	  	border-top: 0;
	  	line-height: 26px;
	}
	.drawr ul li ul li:hover > a,
	.drawr ul li ul li:hover > span, .drawr ul li ul li.active > a,
	.drawr ul li ul li.active > span, .drawr ul li ul li.sidr-class-active > a,
	.drawr ul li ul li.drawr-class-active > span {
	  	box-shadow: 0 0 15px 3px #ccc inset;
	}
	.drawr ul li ul li a,
	.drawr ul li ul li span {
	  	color: rgba(51, 51, 51, 0.8);
	  	padding-left: 20px;
	  	color:#444 !important;
	  	font-size:14px !important;
	  	font-weight:normal;
	}
	.inquiry {
	    background: none !important;
	}
	.nav{
		min-width:100%;
	}
	.nav div.navin{
		max-width:640px;
		margin:0 auto;
		background-color: #ffffff;
	}
	.nav div.navin ul li{
		line-height:25px !important;
	}
	.nav div.navin ul li a{
		padding-top: 10px;
		padding-bottom: 10px;
	}
	.main_img{
		display: none;
	}
	.main_img_sp{
		min-width:100%;
		/*height:540px;
		background:url(../images/img_main_sp.png);
		background-size:cover;*/
	/* } */
	/*.main_img span{
		z-index:-1;
		position:relative;
		overflow:hidden;
	}*/
	.header_logo{
		display: none;
	}
	.logo_sp{
		display: block;
	    position: absolute;
	    top: 8px;
	    width: 60%;
	    left: 20px;
	}
	.bx-wrapper{
		margin-bottom: 0;
	}
	.tb_logo{
		display: none;
	}
	.wrapper{
		min-width:100%;
	}
	.top_content{
		padding:0;
	}
	.top_content1{
		position: relative;
	}
	.top_content02{
		position: absolute;
		top: 0;
	}
	.news_content{
		display: inline-block;
	}
	.top_content:after {clear: both;}
	/* IE6/7 support */
	.top_content {zoom: 1;}
	.top_content02{
		width:100%;
		display:block;
		background:url(../images/img_top01_sp.png);
		background-position:center center;
		background-size:100% 100%;
		position: relative;
	}
	.top_content04{
		float:left;
		width:50%;
	}
	.top_contentin{
		padding:0;
	}
	.top_contentin p{
		color: #000;
	    line-height: 1.8;
	    font-size: 97%;
	    font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.row2 h2{
		text-align: left;
		padding: 0 !important;
		margin-top: 0 !important;
	}
	.pankuzu{
		display:none;
	}
	.content {
		min-width:100%;
		margin-top: 20px;
	}
	.content p {
		line-height: 2;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.business-content,
	.admin .content{
		padding: 0 20px;
	}
	.admin h3{
		text-align: center;
	}
	.admin .right-pc{
		display: none;
	}
	.admin .right-sp{
		display: block;
	}
	.admin .content{
		max-width: 100%;
		display: block;
		box-sizing: border-box;
	}
	.img_pc{
		display: none;
	}
	.img_sp{
		display: block;
	}
	.top_content h2{
		font-size: 100%;
	    color: #c42529;
		padding: 30px 10px 0 10px;
	    font-family: fot-seurat-pron, sans-serif;
	}
	.news_title{
		font-family: fot-seurat-pron, sans-serif;
		text-align: center;
	}
	.row1 span,
	.row1 .month-yr{
		font-family: fot-seurat-pron, sans-serif;
	}
	.row2 a {
		text-decoration: none;
		color: #000;
	}
	.bg_h2{
		min-width:100%;
		height:120px;
		background:url(../images/bg_h2.jpg);
		background-position:center center;
		background-size:cover;
		margin-bottom:30px;
	}
	/* h2{
		text-align:center;
		font-size:130%;
		color:#c42529;
		font-weight: bold;
	} */
	h3 {
		position: relative;
		font-size:110%;
		margin-bottom:15px;
		color: #c42529;
	    font-weight: bold;
		font-family: fot-seurat-pron, sans-serif;
	}
	.business-content{
		margin-bottom: 20px;
	}
	.business-content03{
		margin-bottom: 0;
	}
	.business-content h3{
		margin-bottom: 25px;
		font-family: fot-seurat-pron, sans-serif;
	}
	.business-content a{
		color: #c42529 !important;
		text-decoration-color: #db9091;
		background: none;
	}
	h3:after {
		position: absolute;
		content: " ";
		display: block;
		bottom: -3px;
		width: 20%;
	}
	/* h4 {
		position:relative;
		padding-left:20px;
		font-size:115%;
		margin-bottom:10px;
	}
	h4::after {
		content:'';
		display:block; 
		position:absolute; 
		background:#6d242e;
		width:9px;
		height:9px; 
		top:8px; 
		left:5px; 
		transform:rotate(-45deg);
		-webkit-transform:rotate(-45deg);
		-o-transform:rotate(-45deg);
	} */
	.group_box{
		width:100%;
		padding:0 3% 3% 3%;
		box-sizing: border-box;
	    display: inline-block;
	}	
	.group_slide{
		max-width:80%;
		margin:0 auto;
	}
	.group_slide li img{
		max-width:70%;
		margin: 0 auto;
	}
	div.topics-inc{
		width:90%;
		height:320px;
		padding:5px 10px;
		overflow-y:scroll;
		margin:0 auto 30px auto;
	}
	ul.topics{
		overflow: hidden; /* MacIE */
	}
	/* NN7 */
	ul.topics:after {
		content: "";
		display: block;
		clear: both;
		height: 1px;
		overflow: hidden;
	}

	/* IE6 */
	* html ul.topics{
		height: 1em;
		overflow: visible;
	}
	.row1{
		float: left;
	    width: 16%;
	    font-weight: bold;
	    margin: 10px 0;
	    color: #c42529;
	    background: #f1f1f1;
	    padding: 10px 5px;

	}
	.row1 span{
		font-size: 120%;
	}
	.row1 .month-yr{
		font-size: 80%;
	}
	.row1:before{
		display: none;
	}
	.row2{
		float: right;
	    width: 75%;
	    margin: 10px 0;
		line-height: 1.8;
	}
	.asmomart{
		width:100%;
		display: inline-block;
		box-sizing: border-box;
	    background: #fdf5f5;
	}
	.asmomart p{
		width:230px;
		margin:200px auto 0 auto;
	}
	.asmomart p a{
		display:block;
		text-align:center;
		height:50px;
		line-height:50px;
		background:#ad1e1e;
		color:#fff;
		font-weight:bold;
		font-size:110%;
		text-decoration:none;
	}
	.asmomart p a:hover{
		background:#811717;
	}
	.pagetop{
		position: fixed;
		z-index:100;
		bottom: -50px;
		right: 20px;
	}
	.pagetop a{
		display: block;
		width: 42px;
		height: 42px;
		border:4px solid #c93a4d;
		color: #c93a4d;
		font-size: 16px;
		font-weight:bold;
		text-decoration: none;
		text-align:center;
		line-height: 42px;
	}
	.pagetop a:hover{
		border:4px solid #f8cbd1;
		color:#f8cbd1;
	}
	/***** dl_bna *****/
	.dl_bna{
		max-width:90%;
		padding:10px;
		background:#f4f4f4;
		border:1px solid #c9c9c9;
		margin:0 auto 30px auto;
	}
	.dl_bna dt{
		border-bottom:1px dotted #aaa;
		padding-bottom:10px;
		margin-bottom:5px;
	}
	.dl_bna dd{
		position:relative;
		padding-left:15px;
	}
	.dl_bna dd:before{
		content:''; 
		display:block; 
		position:absolute; 
		box-shadow: 0 0 2px 2px rgba(255,255,255,0.2) inset;
		top:5px; 
		left:2px; 
		height:0; 
		width:0; 
		border-top: 6px solid transparent;
		border-right: 7px solid transparent;
		border-bottom: 6px solid transparent;
		border-left: 9px solid #6d242e;
	}
	/***** graybox *****/
	.graybox{
		width:90%;
		padding:10px;
		background:#f4f4f4;
		border:1px solid #c9c9c9;
		margin:0 auto 30px auto;
	}
	.graybox img{
		margin-bottom:10px;
	}
	/***** list *****/
	.list_style{
		width:100%;
		border-top:1px dotted #aaa;
	}
	.list_style li{
		position:relative;
		padding-left:20px;
		border-bottom:1px dotted #aaa;
	}
	.list_style li:after{
		content:''; 
		display:block; 
		position:absolute; 
		top:10px; 
		left:3px; 
		border-radius:30px;
		height:7px; 
		width:7px; 
		border:3px solid #83d3b6;
	}
	.list_style li span{
		display:block;
		padding:5px 0 1px 0;
	}
	.list_style li em{
		display:block;
		padding:1px 0 5px 0;
	}
	ol.ol_list {
		counter-reset: my-counter;
	}
	ol.ol_list li {
		margin-bottom: 10px;
		padding-left: 30px;
		padding-right:10px;
		position: relative;
	}
	ol.ol_list li:before {
		content: counter(my-counter);
	    counter-increment: my-counter;
	    background-color: #cf5154;
	    color: #fff;
	    font-weight: bold; 
	    display: block;
	    float: left;
	    line-height: 15px;
	    margin-left: -25px;
	    text-align: center;
	    height: 15px;
	    width: 15px;
	    border-radius: 50%;
	    font-size: 11px;
	    margin-top: 3px;
		font-family: 'Gotham';
	}
	ol.ol_list li ul{
		counter-reset:list;
		margin-top:15px;
	}
	ol.ol_list li ul li:before{
		counter-increment: list;
		content: counter(list);
		background-color: #b57d85;
	}
	/***** table01 *****/
	.table01 {
		width: 100%;
		margin:0 auto;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.table01 th{
		padding: 10px;
		text-align: center;
		vertical-align:top;
		font-weight:bold;
		width: 30%;
		border: 1px solid #ccc;
		text-align: left;
		padding-left: 21px;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.table01 td{
		padding: 10px;
		border-top:1px dotted #ccc;
		border: 1px solid #ccc;
		width: 70%;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.table01 td.bb-none{
		border-bottom: none;
	}
	.table01 td.bt-none{
		border: none;
    	border-right: 0px solid #ccc;
	}
	.table01 td table td{
		padding:0;
		border:none;
	}
	.brown_col{
		background-color: #fefbfb;
	}
	/***** table02 *****/
	.table02 {
		width: 100%;
		font-size:80%;
		margin:0 auto 20px auto;
	}
	.table02 th{
		width:12%;
		padding: 5px;
		text-align: center;
		vertical-align:top;
		color:#fff;
		background: #01a369;
		border:1px solid #fff;
	}
	.table02 td{
		padding: 5px;
		text-align: center;
		border:1px solid #01a369;
	}
	/***** table03 *****/
	.table03 {
		width: 100%;
		font-size:80%;
		margin:0 auto 20px auto;
	}
	.table03 th{
		padding: 5px;
		text-align: center;
		font-weight:bold;
		background: #eee;
		border:1px solid #ddd;
	}
	.table03 td{
		padding: 5px;
		text-align: right;
		border:1px solid #ddd;
	}
	.complete-inq{
		padding: 0 20px !important;
	}
	.complete-inq p{
		padding: 0 !important;
	}
	.table_Form {
		width: 100%;
		margin:0 auto 20px auto;
	}
	.table_Form th{
		display:block;
		padding: 15px;
		vertical-align:top;
		font-weight:bold;
		background: #ededed;
	}
	.table_Form th div{position:relative;}
	.table_Form th div img{
		position:absolute;
		top:4px;
		right:0;
	}
	.table_Form td{
		display:block;
		padding: 15px;
		background: #fff4f4;
		padding-left: 50px !important;
	}
	.table_Form td input,
	.table_Form td textarea{
		width:100%;
		padding:10px 15px;
		box-sizing: border-box;
	}
	.table_Form td input.wauto{
		width:auto;
	}
	.table_Form td input.w10{
		width:15%;
	}
	.form_scroll{
		width:100%; 
		overflow:auto; 
		height:120px; 
		font-size:10px;
	}
	:focus::-webkit-input-placeholder { 
		color: white; 
	} 
	/* Chrome・Safari・Opera用(※Edgeにも使える) */
	:focus:-moz-placeholder { 
		color: white; 
	}  
	/* Firefox18以前用 */
	:focus::-moz-placeholder { 
		color: white; 
	} 
	/* Firefox19以上用 */
	:focus:placeholder-shown { 
		color: white; 
	} 
	/* CSS標準(予定)の記述 */
	/***** google map *****/
	.map_wrapper {
		max-width: 640px;
		min-width: 220px;
		margin: 0 auto 20px auto;
		padding: 4px;
		border: 1px solid #CCC;  
	}
	.googlemap {
		position: relative;
		padding-bottom: 80%;
		height: 0;
		overflow: hidden;
	}
	.googlemap iframe {
		position: absolute;
		top: 0;
		left: 0;
		width: 100% !important;
		height: 100% !important;
	}
	.access_route{
		max-width: 640px;
		min-width: 220px;
		margin: 0 auto 20px auto;
	}
	/***custom css***/
	.about,
	.site-policy,
	.privacy{
		width: 100%;
		display: block;
		padding: 0 20px;
		box-sizing: border-box;
	}
	p.btn_link input{
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.admin .left{
		margin-bottom: 10px;
	}
	.left,
	.site-policy h3{
		width: 100%;
		text-align: center;	
	}
	.site-policy h3{
		letter-spacing: 1px;
	}
	.site-policy a{
		color: #333;
	}
	.right img{
		display: block;
	    margin: 0 auto;
	    width: 30%;
	    padding-bottom: 20px;
	}
	.site-policy-cnt{
		margin-bottom: 30px;
	}
	.site-policy-cnt03{
		margin-bottom: 0;
	}
	.left p{
		padding-bottom: 34px;
	    line-height: 2;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.row-desc p,
	.site-policy-cnt p{
		padding-bottom: 34px;
	    line-height: 2;
	    letter-spacing: 1px;
	    text-align: center;
	}
	.site-policy-cnt p{
		letter-spacing: 0;
		padding-bottom: 0;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.row-desc p{
		padding-bottom: 0;
	}
	.row-desc h3{
		text-align: center;
	}
	.business img{
		margin-left: 37px;
		width: 88%;
	}
	.brown_col_sp{
		background-color: #fbf7f7;
		border-top: 2px solid #fbf7f7;
	}
	.white_col_sp{
		background-color: #fff;
	}
	.f115{
		padding:18px 18px 25px 18px!important;
	}
	.row2 strong{
	    color: #c42529
	}
	.top_content h2, .row2 strong {
		font-family: fot-seurat-pron, sans-serif;
	}
	.topics .row2 p{
		padding-left: 0 !important;
	}
	.mart_logo{
		margin: 0 auto; 
	    width: 50%;
	    display: block;
	    margin: 0 auto;
	    padding: 40px 0 20px 0;
	}
	.navin img{
		padding-top: 34px !important;
	}
	.asmomart{
		width:100%;
		display: inline-block;
		background: #fdf5f5;
		margin-bottom: 30px;
	}
	.mart-title{
		color: #c42529;
	    font-weight: bold;
	    text-align: center;
	    padding: 0 20px 20px 20px;
	    font-family: fot-seurat-pron, sans-serif;
	    font-size: 115%;
	}
	.asmomart p{
		width:230px;
		margin:220px auto 0 auto;
	}
	.asmomart p a{
		display:block;
		text-align:center;
		height:50px;
		line-height:50px;
		background:#ad1e1e;
		color:#fff;
		font-weight:bold;
		font-size:110%;
		text-decoration:none;
	}
	.asmomart p a:hover{
		background:#811717;
	}
	.img-list{
		width: 100%;
		display: block;
		margin: 0 auto;
		text-align: center; 
	}
	.mart-img.left,
	.mart-img.right{
		width: 80%;
	    display: block;
	    box-sizing: border-box;
	    margin: 0 auto;
	    padding: 4% 0;
	}
	.mart-img.right{
		padding-bottom: 2%;
	}
	.mart-img img{
		width: 100%;
	}
	.video{
		position: relative;
	    width: 100%;
	    display: block;
	    margin: 0 auto;
	}
	.youtube_link{
		width: 90%; 
		height: auto; 
		display: block; 
		margin: 30px auto 20px auto;
	}
	.gp-logo {
	    position: absolute;
	    top: 2%;
	    right: 7%;
	    width: 25%;
	}
	.youtube_link iframe{
		width: 100%;
		display: inline-block;
		border: none;
	}
	.about .table01 a{
		color: #333;
	}
	.about .table01 a:hover{
		opacity: .8;
	}

	/*footer*/
	.footer{
		display: none;
	}
	.footer_sp{
		min-width: 100%;
	    height: 600px;
	    background:url(../images/footer_sp.png);
	    position: relative;
	    box-sizing: border-box;
	    background-repeat: no-repeat;
	    background-size: 100% 100%;
	}
	.footer-row-sp{
		position: absolute;
	    top: 20%;
	    width: 100%;
	    display: block;
	}
	.footer-row-sp .sp-row1,
	.footer-row-sp .sp-row2{
		width: 100%;
	    display: inline-block;
	    padding: 0 20px;
	    box-sizing: border-box;
	}
	.footer-row-sp h3{
		text-align: left;
		color: #c42529;
		font-weight: bold;
		margin-left: 0;
	}
	.footer-row-sp a{
		text-decoration: none;
		color: #fff;
		font-family: kozuka-gothic-pr6n, sans-serif;
		font-size: 74%;
		background: none;
	}
	.footer-row-sp .footer-left,
	.footer-row-sp .footer-right{
		width: 65%;
		float: left;
		display: block;
		box-sizing: border-box;
	}
	.footer-row-sp .footer-right{
		padding-left: 30px;
	}
	.footer-row-sp .footer-left{
		width: 35%;
	}
	.footer-row-sp .footer-left .logo{
		width: 60%;
	    display: block;
	    margin-bottom: 30px;
	    padding: 12% 3% 3% 3%;
	}
	.footer-row-sp .footer-right .useful-link li,
	.footer-row-sp .footer-right .address li
	{
		width: 100%;
		color: #fff;
	}
	.footer-row-sp .footer-right .useful-link li{
		line-height: 2;
	}
	.footer-row-sp .footer-left .social,
	.footer-row-sp .footer-right .useful-link,
	.footer-row-sp .footer-right .address{
		width: 100%;
		display: block;
	}
	.footer-row-sp .footer-left .social img,
	.footer-row-sp .footer-right .address img{
		width: 10%;
		margin-right: 10px;
		vertical-align: middle;
	}
	.footer-row-sp .footer-right .address img,
	.footer-row-sp .footer-right .address span{
		width: 85%;
		display: block;
	    float: left;
	    font-size: 74%;
	}
	.footer-row-sp .footer-right .address img{
		width: 4%;
		float: left;
		display: block;
		margin-top: 5px;
	}
	.footer-row-sp .footer-right .address span,
	.footer-row-sp .footer-right .address.addr_02{
		width: 100%;
		display: block;
		box-sizing: border-box;
	}
	.footer-row-sp .footer-right .address .gp-name{
		margin-left: 20px;
	}
	.footer-row-sp .footer-right .address .addr_02 div{
		width: 75% !important;
	    float: left;
	    margin-left: 15px;
	    line-height: 2;
	}
	.footer-row-sp .footer-right .address img{
		width: 5%;
	    margin-top: 5px;
	}
	.footer-row-sp .footer-left .social li{
		color: #fff;
		line-height: 3;
	}
	.footer-row-sp .footer-right .useful-link{
		margin-bottom: 30px;
	}
	.footer-row-sp .footer-right .useful-link ul{
		width: 50%;
		display: block;
		float: left;
		font-size: 13px;
	}
	.footer-row-sp .footer-right .useful-link .sec-ul{
		margin-left: 10px;
    	width: 44%;
	}
	.clearfix::after {
	  content: "";
	  clear: both;
	  display: table;
	}
	.footer-row .logo,
	.useful-link{
		width: 50%;
		float: left;
	}
	.footer-row{
		display: inline-block;
		width: 100%;
		color: #fff;
		position: absolute;
		bottom: 15%;
	}
	.copyright_text{
		color: #333;
		background: #fdf5f5;
		font-weight: normal;
		font-family: kozuka-gothic-pr6n, sans-serif;
		width: 100%;
		text-align: center;
		margin: 0 auto;
		font-size: 12px;
	}
	.bxslider_img_w ul li img{
		min-width:100%;
		min-height: 414px;
		max-height: 414px;
	}
	.onlypc{
		display: none;
	}
	.onlysp{
		display: block;
	}
} 
@media print, screen and  (min-width: 280px) and  (max-width: 420px){
	.footer-row-sp .footer-left .social img{
    	width: 8%;
    }
    .footer-row-sp .footer-right .address img {
    	width: 6%;
    }
}
@media print, screen and  (min-width: 280px) and  (max-width: 330px){
	.footer-row-sp .footer-right .useful-link .sec-ul {
		margin-left: 10px;
		width: 43%;
	}
	.footer-row-sp {
		top: 16%;
	}
	.footer-row-sp .footer-right .address .gp-name {
		margin-left: 20px;
		font-size: 70%;
	}
}
@media print, screen and  (min-width: 414px) and  (max-width: 420px){
	.top_content h2 {
    	font-size: 110%;
    }
}      
@media print, screen and  (min-width: 540px) and  (max-width: 640px){
	.footer-row-sp .footer-left .social img{
    	width: 7%;
    }
    .footer-row-sp .footer-right .address img {
    	width: 5%;
    }
	.privacy_text img {
		width: 3%;
	}
}