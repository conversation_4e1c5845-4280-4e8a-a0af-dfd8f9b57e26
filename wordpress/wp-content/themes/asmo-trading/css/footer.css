footer {
  background: url("../images/renew/footer_bg.png") center/cover no-repeat;
  color: white;
  padding: 3rem 15% 1.5rem 15%;
  position: relative;
  width: 100%;
  isolation: isolate;
  display: block;
}

footer::before {
  content: "";
  inset: 0;
  position: absolute;
  background-color: #00000011;
}

.footer__container {
  display: flex;
  justify-content: space-between;
  position: relative;
  border-radius: 5px;
    opacity: 1;
    max-width: 1300px;
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}

.footer__logo {
  flex-shrink: 1;
}

.footer__logo-img {
  height: 6.25rem;
}

.footer__links {
  display: flex;
  flex: 1;
  margin-left: 5rem;
  gap: 2.5rem;
}

.footer__links-list {
  list-style: none;
  margin: 2.5rem 1.25rem 2.5rem;
}

.footer__links-item {
  margin-bottom: 0.625rem;
}

.footer__links-bullet {
  color: var(--primary-color);
  margin-right: 0.625rem;
  line-height: 1.6;
  font-size: 12px;
}

.footer__links-link {
  text-decoration: none;
  color: #f5f5dc;
  font-size: 15px;
}

.footer__links-link:hover {
  color: var(--primary-color);
}

.footer__info {
  display: flex;
  flex: 1;
  justify-content: space-evenly;
  gap: 40px;
}

.footer__info-title {
  color: var(--primary-color);
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.footer__info-list {
  list-style: none;
  color: var(--text-color);
  font-size: 15px;
}

.footer__info-item {
  display: flex;
  margin-bottom: 14px;
}

.footer__info-icon {
  width: 1.5625rem;
  height: 1.5625rem;
  margin-right: 1rem;
  background-color: white;
  padding: 0.1875rem;
  border-radius: 50%;
  border: 1px solid #fff;
  color: #212121;
  text-align: center;
  font-size: 14px;
  padding-top: 4px;
}

.footer__info-item i.fa-phone-alt {
    transform: rotate(90deg);
}

.footer__info-text{
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer__info-social-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  line-height: 21px;
}

.footer__info-social-img {
  width: 25px;
  height: 25px;
  margin-right: 16px;
  background-color: white;
  padding: 3px;
  border-radius: 50%;
  border: 1px solid #fff;
}

.footer__info-social-text{
  display: inline-block;
}
.footer__info-social-link:hover .footer__info-social-text {
  color: var(--primary-color);
}

.footer__info-social-link:hover .footer__info-social-img {
  border-color: var(--primary-color);
}

.footer__bottom {
  text-align: center;
  padding-top: 20px;
  font-size: 12px;
  border-top: 1px solid #f5f5dc25;
  position: relative;
  color: var(--text-color);
}

@media screen and (max-width: 768px) {
  footer {
    display: none;
  }
}

@media screen and (min-width: 768px) and (max-width: 1368px) {
  footer {
    padding: 2rem 8%;
  }
  .footer__logo {
  width: 100%;
  margin-top: 0;
  }
  .footer__logo img {
    width: auto;
    height: 80px;
  }
  .footer__container {
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    gap: 20px;
  }
  .footer__links{
    margin-left: 0;
  }
  .footer__links-list {
    margin: 0px;
    flex: 1;
  }
  .footer__links {
    width: 100%;
    margin: 0;
    padding: 0;
  }
  .footer__info {
    margin-bottom: 2.5rem;
    width: 100%;
  }
  .footer__info-list{
    display: flex;
    flex-direction: column;
  }
  .footer__info-title {
    font-size: 1rem;
  }
  .footer__info-contact, .footer__info-social {
    flex: 1;
  }
}