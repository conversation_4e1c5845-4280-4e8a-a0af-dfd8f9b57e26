@charset "UTF-8";

/***** CSS reset *****/
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td,address,figure {
  margin: 0;
  padding: 0;
}
body {
  color: #333;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
fieldset,img {
  border: 0;
  vertical-align: bottom;
}
address,caption,cite,code,dfn,em,strong,th,var {
  font-style: normal;
  font-weight: normal;
}
ol,ul {
  list-style: none;
}
caption,th {
  text-align: left;
}
h1,h2,h3,h4,h5,h6 {
  font-size: 100%;
  font-weight: normal;
}
q:before,
q:after {
  content: "";
}
abbr,acronym {
  border: 0;
}
hr {
  height: 0;
  border-top: 1px solid #ddd;
}
a {
  cursor: pointer;
}

/* Reset styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  vertical-align: middle;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}
.clear {
  clear: both;
}
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
/* IE6/7 support */
.clearfix {
  *zoom: 1;
}


/***** img *****/
.btn:hover {
  opacity: 0.8;
}
.img_blank {
  vertical-align: middle;
  margin: 0 5px;
}

/***** font *****/
.red {color: #6d242e;}
.green {color: #01a369;}
.orange {color: #ef8d00;}
.white {color: #fff;}
.bold {font-weight: bold;}
.font_normal {font-weight: normal;}
.f115 {font-size: 115%;}
.f130 {font-size: 130%;}

:root {
  --primary-color: #ffd700;
  --secondary-color: #212121;
  --background-color: #fcfcf6;
  --text-color: #f5f5dc;
  --danger-color: #c42529;
}

/* Font face for custom font */
@font-face {
  font-family: "FOT-Seurat Pro DB";
  src: url("../images/fonts/FOT/FOT-Seurat Pro DB.otf") format("opentype");
}

@font-face {
  font-family: "KozGoPro";
  src: url("../images/fonts/KozGoProRegular.otf") format("opentype");
}

@font-face {
  font-family: "Noto Sans JP";
  src: url("../images/fonts/NotoSansJP-Regular.ttf") format("truetype");
}

html,
body,
* {
  font-family: "KozGoPro", sans-serif;
  scroll-behavior: smooth;
}

.pc-only {
  display: block;
}

.sp-only {
  display: none;
}

.text-right {
  text-align: right;
}
