/*new*/
@charset 'UTF-8';
/* Slider */
.slick-loading .slick-list
{
    background: #fff url('../images/ajax-loader.gif') center center no-repeat;
}
/* Icons */
@font-face
{
    font-family: 'slick';
    font-weight: normal;
    font-style: normal;

    src: url('../images/fonts/slick.eot');
    src: url('../images/fonts/slick.eot?#iefix') format('embedded-opentype'), url('../images/fonts/slick.woff') format('woff'), url('../images/fonts/slick.ttf') format('truetype'), url('../images/fonts/slick.svg#slick') format('svg');
}
/* Arrows */
.prev-arrow,
.next-arrow {
  position: absolute;
  top: 50%;
  margin: 0;
  padding: 0;
  line-height: 1;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  cursor: pointer;
  font-weight: bold;
}

.prev-arrow {
  left: -20px;
}

.next-arrow {
  right: -20px;
}
.slick-prev,
.slick-next
{
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: 45%;
    display: block;
    width: 20px;
    height: 20px;
    padding: 0;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: none;
    outline: none;
    background: transparent;
    font-weight: bold;
}
.slick-prev:hover,
.slick-prev:focus,
.slick-next:hover,
.slick-next:focus
{
    /*color: transparent;
    outline: none;
    background: transparent;*/
}
.slick-prev:hover:before,
.slick-prev:focus:before,
.slick-next:hover:before,
.slick-next:focus:before
{
    /*opacity: 1;*/
}
.slick-prev.slick-disabled:before,
.slick-next.slick-disabled:before
{
    opacity: .25;
}
.slick-prev:before,
.slick-next:before
{
    font-family: 'slick';
    font-size: 22px;
    line-height: 1;
    opacity: 1;
    color: #c42529;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.slick-prev
{
    left: -25px;
    background:url(../images/btn_prev.png);
    background-size:cover;
    width: 11px;
    height: 20px;
    background-size: 100% 100%;
}
[dir='rtl'] .slick-prev
{
    right: -25px;
    left: auto;
}
/*.slick-prev:before
{
    content: '<';
}
[dir='rtl'] .slick-prev:before
{
    content: '>';
}*/

.slick-next
{
    right: -25px;
    background:url(../images/btn_next.png);
    background-size:cover;
    width: 11px;
    height: 20px;
    background-size: 100% 100%;
}
[dir='rtl'] .slick-next
{
    right: auto;
    left: -25px;
}
./*slick-next:before
{
    content: '>';
}
[dir='rtl'] .slick-next:before
{
    content: '<';
}*/

/* Dots */
.slick-dotted.slick-slider
{
    margin-bottom: 30px;
}
.slick-dots { 
    position: absolute; 
    bottom: -45px; 
    list-style: none; 
    display: block; 
    text-align: center; 
    padding: 0; 
    width: 100%; 
}
.slick-dots li { 
    position: relative; 
    display: inline-block; 
    height: 20px; 
    width: 20px; 
    margin: 0 5px; 
    padding: 0; 
    cursor: pointer; 
}
.slick-dots li button { 
    border: 0; 
    background: transparent; 
    display: block; 
    height: 16px; 
    width: 16px; 
    outline: none; 
    line-height: 0; 
    font-size: 0; 
    color: #fff; 
    padding: 5px; 
    cursor: pointer; 
}
.slick-dots li button:hover,
.slick-dots li button:focus { 
    outline: none; 
}
.slick-dots li button:hover:before, 
.slick-dots li button:focus:before {
    opacity: 1;
    background: #212121;
    content: '' !important;
}
.slick-dots li button:before { 
    position: absolute; 
    top: 0; left: 0; 
    /* content: "•";  */
    width: 8px; 
    height: 8px; 
    font-family: "slick";
    line-height: 5px; 
    text-align: center;
    -webkit-font-smoothing: antialiased; 
    -moz-osx-font-smoothing: grayscale; 
    background-color: #212121;
    border: 3px solid #fff;
    border-radius: 50%;
    display: inline-block;
    opacity: 1 !important;
    color: transparent !important;
    height: 16px !important; 
    width: 16px !important; 
}
.slick-dots li.slick-active button:before
{
    opacity: 1;
    background: var(--primary-color);
    color: var(--primary-color) !important;
}

.slick-dots{
    bottom: 80px !important;
}