@charset "utf-8";

/*===============================================
●smart.css  画面の横幅が640pxまで
===============================================*/
@media screen and (max-width:640px){

body{
	font-size:15px;
}

img{
	max-width: 100%;
	height: auto;
	
	width /***/:auto;　
}

sup{
	font-size:70%;
}

/***** index *****/
#container{
	min-width:100%;
}

.header {
	min-width:100%;
}
.headerin {
	max-width:640px;
	margin:0 auto;
}
.headerin h1{
	font-size:12px;
	padding:0 50px 10px 10px
}
.headerin div.logo{
	display:block;
	width:75%;
	text-align:center;
	margin:0 auto 10px auto;
	display: none;
}
.logo_sp{
	display: block;
    position: absolute;
    width: 40%;
    margin: 10px;
}
.headerin div .nav_sp_logo{
	display: block;
    width: 40%;
    margin: 20px auto;
}

.headerin div.inquiry{
	width:70%;
	margin:0 auto 10px auto;
}
.headerin div.inquiry a{
	height:40px;
	line-height:40px;
	border-radius:40px;
	display:block;
	text-align:center;
	background:#936310;
	color:#fff;
	font-weight:bold;
	font-size:110%;
	text-decoration:none;
}
.headerin div.inquiry a:hover{
	background:#704a09;
}

.menu-btn {
    background:transparent url(../images/btn.png) no-repeat 0 0;
    display: block;
    width:45px;
    height: 45px;
	position: fixed;
    top:0;
    right:0;
    cursor: pointer;
    z-index: 200;
}
.peke {
    background-position: -45px 0;
    /*display: none;*/
}
.drawr {
    display: none;
	position: fixed;
    top: 0;
    right:0;
	height: 100%;
    width:260px;
    z-index: 100;
	overflow-x: hidden;
	overflow-y: auto;
	font-family: "lucida grande", tahoma, verdana, arial, sans-serif;
	font-size: 15px;
	background: #f8f8f8;
	opacity:0.9;
	color: #333;
	box-shadow: 0 0 5px 5px #ebebeb inset;
}
.drawr ul {
  display: block;
  margin: 0;
  padding: 0;
  background:#fefefe;
  /*border-top: 1px solid #ccc;*/
}
.drawr ul li {
  display: block;
  margin: 0;
  line-height: 40px;
  /*background:#fadcde;*/
  /*border-bottom: 1px solid #ccc;*/
}
.drawr ul li:hover, .drawr ul li.active, .drawr ul li.sidr-class-active {
  border-top: 0;
  line-height: 41px;
}
.drawr ul li:hover > a,
.drawr ul li:hover > span, .drawr ul li.active > a,
.drawr ul li.active > span, .drawr ul li.sidr-class-active > a,
.drawr ul li.sidr-class-active > span {
  /*box-shadow: 0 0 15px 3px #d38389 inset;*/
  color: #c42529;
}
.drawr ul li a{
  display: block;
  padding:0 15px;
  line-height:25px;
  font-size:15px !important;
  text-decoration: none;
  color: #333333;
  text-align: center;
}
.dropmenu li a.active {
	background: #c42529;
	color: #fff !important;
}
.dropmenu li a.inquiry.active{
	background: #c42529 !important;
	color: #fff !important;
}
.drawr ul li span{
	display:block;
	line-height:20px;
}
.drawr ul li ul {
  border-bottom: 0;
  margin: 0;
}
.drawr ul li ul li {
	height:30px;
  line-height: 25px;
  font-size: 14px;
  background:#eee;
}
.drawr ul li ul li:last-child {
  border-bottom: 0;
}
.drawr ul li ul li:hover, .drawr ul li ul li.active, .drawr ul li ul li.sidr-class-active {
  border-top: 0;
  line-height: 26px;
}
.drawr ul li ul li:hover > a,
.drawr ul li ul li:hover > span, .drawr ul li ul li.active > a,
.drawr ul li ul li.active > span, .drawr ul li ul li.sidr-class-active > a,
.drawr ul li ul li.drawr-class-active > span {
  box-shadow: 0 0 15px 3px #ccc inset;
}
.drawr ul li ul li a,
.drawr ul li ul li span {
  color: rgba(51, 51, 51, 0.8);
  padding-left: 20px;
  color:#444 !important;
  font-size:14px !important;
  font-weight:normal;
}

.inquiry {
    background: none !important;
}
.nav{
	min-width:100%;
}
.nav div.navin{
	max-width:640px;
	margin:0 auto;
	background-color: #ffffff;
}
.nav div.navin ul li{
	line-height:25px !important;
}
.nav div.navin ul li a{
	padding-top:5px;
	padding-bottom:5px;
}

.main_img{
	min-width:100%;
	height:300px;
	background:url(../images/img_main_sp.png);
	background-position:center center;
	background-size:cover;
	margin-bottom:30px;
	display: none;
}
.main_img_sp{
	position: relative;
}
.main_img span{
	z-index:-1;
	position:relative;
	overflow:hidden;
}

.wrapper{
	min-width:100%;
}

.top_content{
	padding:0 5px;
	margin-bottom:30px;
}

.pankuzu{
	display:none;
}

.content {
	min-width:100%;
	margin-bottom:50px;
	
}
.content p{
	/*padding:0 5px;*/
	padding: 0 35px 0 35px;
}

.top_content h2{
	font-size:130%;
	color:#6d242e;
	/*border-bottom:6px double #6d242e;*/
	margin-bottom:10px;
}
.bg_h2{
	min-width:100%;
	height:120px;
	background:url(../images/bg_h2.jpg);
	background-position:center center;
	background-size:cover;
	margin-bottom:30px;
}
h2{
	text-align:center;
	 
	font-size:160% !important;
	color:#c42529;
	font-weight: bold;
}

h3 {
	/*border-bottom: solid 3px #ddd;
	position: relative;*/
	letter-spacing: 2px !important;
	font-size:130%;
	margin:0 5px 15px 5px;
	text-align: center;
}
h3:after {
	position: absolute;
	/*content: " ";*/
	display: block;
	border-bottom: solid 3px #6d242e;
	bottom: -3px;
	width: 20%;
}

h4 {
	position:relative;
	padding-left:20px;
	font-size:115%;
	margin:0 5px 10px 5px;
}
h4::after {
	content:'';
	display:block; 
	position:absolute; 
	background:#6d242e;
	width:9px;
	height:9px; 
	top:8px; 
	left:5px; 
	transform:rotate(-45deg);
	-webkit-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
}

.group_box{
	width:100%;
	background:#936310;
	padding:30px 0 15px 0;
}	
.group_slide{
	max-width:80%;
	margin:0 auto;
}
.group_slide li img{
	width:97%;
	padding:0 5px;
}
div.topics-inc{
	width:90%;
	height:320px;
	padding:5px 10px;
	overflow:auto;
	margin:0 auto 30px auto;
}
/*
ul.topics{
	border-bottom:1px dotted #444;
}*/
.row1{
	float: left;
    width: 10%;
    font-weight: bold;
    margin: 10px 0;
    color: #c42529;
    background: #f1f1f1;
    padding: 10px 5px;
}
.row1:before{
	content:''; 
	display:block; 
	position:absolute; 
	box-shadow: 0 0 2px 2px rgba(255,255,255,0.2) inset;
	top:7px; 
	left:2px; 
	height:0; 
	width:0; 
	border-top: 5px solid transparent;
	border-right: 6px solid transparent;
	border-bottom: 5px solid transparent;
	border-left: 8px solid #333;
}
.row2{
	float: right;
    width: 80%;
    margin: 10px 0;
}
.asmomart{
	width:100%;
	/*border-top:30px solid #6d242e;*/
	/*border-bottom:30px solid #6d242e;*/
	height:280px;
	/*background:url(../images/bg_asmomart_sp.jpg);*/
	background-position:center center;
	background-size:cover;
}
.asmomart p{
	width:230px;
	margin:200px auto 0 auto;
}
.asmomart p a{
	display:block;
	text-align:center;
	height:50px;
	line-height:50px;
	background:#ad1e1e;
	color:#fff;
	font-weight:bold;
	font-size:110%;
	text-decoration:none;
}
.asmomart p a:hover{
	background:#811717;
}

.footer {
	min-width:100%;
}
.footer01{
	background:#252525;
}
.footer01 p.icon{
	text-align:center;
	padding-top:20px;
}
.footer01 ul{
	padding:10px 0;
}
.footerin{
	min-width:100%;
}
.footerin ul li{
	font-weight:bold;
	position:relative;
	padding-left:15px;
	padding-bottom:5px;
}
.footerin ul li:before{
	content:''; 
	display:block; 
	position:absolute; 
	box-shadow: 0 0 2px 2px rgba(255,255,255,0.2) inset;
	top:7px; 
	left:2px; 
	height:0; 
	width:0; 
	border-top: 5px solid transparent;
	border-right: 6px solid transparent;
	border-bottom: 5px solid transparent;
	border-left: 8px solid #fff;
}
.footerin ul li a{
	color:#fff;
	text-decoration:none;
}
.footerin ul li a:hover{
	color:#ddd;
}

.footer02{
	background:#6d242e;
	padding:10px 0;
}
address p{
	font-size:11px;
	color:#fff;
	padding:0 10px;
}

.pagetop{
	position: fixed;
	z-index:100;
	bottom: -50px;
	right: 20px;
}
.pagetop a{
	display: block;
	width: 42px;
	height: 42px;
	border:4px solid #c93a4d;
	color: #c93a4d;
	font-size: 16px;
	font-weight:bold;
	text-decoration: none;
	text-align:center;
	line-height: 42px;
}
.pagetop a:hover{
	border:4px solid #f8cbd1;
	color:#f8cbd1;
}

/***** dl_bna *****/
.dl_bna{
	max-width:90%;
	padding:10px;
	background:#f4f4f4;
	border:1px solid #c9c9c9;
	margin:0 auto 30px auto;
}
.dl_bna dt{
	border-bottom:1px dotted #aaa;
	padding-bottom:10px;
	margin-bottom:5px;
}
.dl_bna dd{
	position:relative;
	padding-left:15px;
}
.dl_bna dd:before{
	content:''; 
	display:block; 
	position:absolute; 
	box-shadow: 0 0 2px 2px rgba(255,255,255,0.2) inset;
	top:5px; 
	left:2px; 
	height:0; 
	width:0; 
	border-top: 6px solid transparent;
	border-right: 7px solid transparent;
	border-bottom: 6px solid transparent;
	border-left: 9px solid #6d242e;
}

/***** graybox *****/
.graybox{
	width:90%;
	padding:10px;
	background:#f4f4f4;
	border:1px solid #c9c9c9;
	margin:0 auto 30px auto;
}
.graybox img{
	margin-bottom:10px;
}

/***** list *****/
.list_style{
	width:100%;
	border-top:1px dotted #aaa;
}
.list_style li{
	position:relative;
	padding-left:20px;
	border-bottom:1px dotted #aaa;
}
 
.list_style li:after{
	content:''; 
	display:block; 
	position:absolute; 
	top:10px; 
	left:3px; 
	border-radius:30px;
	height:7px; 
	width:7px; 
	border:3px solid #83d3b6;
}
.list_style li span{
	display:block;
	padding:5px 0 1px 0;
}
.list_style li em{
	display:block;
	padding:1px 0 5px 0;
}

ol.ol_list {
	counter-reset: my-counter;
	padding-left: 40px;
	margin-bottom:30px;
}
 
ol.ol_list li {
	margin-bottom: 10px;
	padding-left: 30px;
	padding-right:10px;
	position: relative;
}
ol.ol_list li:before {
	content: counter(my-counter);
	counter-increment: my-counter;
	background-color: #c11212;;
	color: #fff;
	font-weight:bold;
	display: block;
	float: left;
	line-height: 22px;
	margin-left: -25px;
	text-align: center;
	height: 22px;
	width: 22px;
	border-radius: 50%;
}
ol.ol_list li ul{
	counter-reset:list;
	margin-top:15px;
}
ol.ol_list li ul li:before{
	counter-increment: list;
	content: counter(list);
	background-color: #b57d85;
}

/***** table01 *****/
.table01 {
	width: 95%;
	margin:0 auto 20px auto;
}
.table01 th{
	/*display:block;
	background: #f0f0f0;*/
	padding: 10px;
	text-align: center;
	vertical-align:top;
	font-weight:bold;
	width: 30%;
	border: 1px solid #ccc;
	text-align: left;
	padding-left: 21px;

}
.table01 td{
	/*display:block;
	background:#fff;*/	
	padding: 10px;
	border-top:1px dotted #ccc;
	border: 1px solid #ccc;
	width: 70%;
}
.table01 td table td{
	padding:0;
	border:none;
}
.brown_col{
	background-color: #fbf7f7;
}

/***** table02 *****/
.table02 {
	width: 100%;
	font-size:80%;
	margin:0 auto 20px auto;
}
.table02 th{
	width:12%;
	padding: 5px;
	text-align: center;
	vertical-align:top;
	color:#fff;
	background: #01a369;
	border:1px solid #fff;
}
.table02 td{
	padding: 5px;
	text-align: center;
	border:1px solid #01a369;
}

/***** table03 *****/
.table03 {
	width: 100%;
	font-size:80%;
	margin:0 auto 20px auto;
}
.table03 th{
	padding: 5px;
	text-align: center;
	font-weight:bold;
	background: #eee;
	border:1px solid #ddd;
}
.table03 td{
	padding: 5px;
	text-align: right;
	border:1px solid #ddd;
}

/***** table_Form *****/
.table_Form {
	width: 95%;
	margin:0 auto 20px auto;
}
.table_Form, 
.table_Form th, 
.table_Form td{
	/*border-bottom: 1px solid #fff;*/
}
.table_Form th{
	display:block;
	padding: 15px;
	vertical-align:top;
	font-weight:bold;
	background: #ededed;
}
.table_Form th div{
	position:relative;
}
.table_Form th div img{
	position:absolute;
	top:4px;
	right:0;
}
.table_Form td{
	display:block;
	padding: 15px;
	background: #fff4f4;
	padding-left: 50px !important;

}
.table_Form td input{
	width:90%;
	padding:5px;
}
.table_Form td input.wauto{
	width:auto;
}
.table_Form td input.w10{
	width:15%;
}
.table_Form td textarea{
	width:90%;
	padding:5px;
}
.form_scroll{width:100%; overflow:auto; height:120px; font-size:10px;}
:focus::-webkit-input-placeholder { color: white; } /* Chrome・Safari・Opera用(※Edgeにも使える) */
:focus:-moz-placeholder { color: white; }  /* Firefox18以前用 */
:focus::-moz-placeholder { color: white; } /* Firefox19以上用 */
:focus:placeholder-shown { color: white; } /* CSS標準(予定)の記述 */

.inquiry_box{
	width:90%;
	font-size:80%;
	height:150px;
	padding:10px;
	overflow:auto;
}

/***** google map *****/
.map_wrapper {
	max-width: 640px;
	min-width: 220px;
	margin: 0 auto 20px auto;
	padding: 4px;
	border: 1px solid #CCC;  
}
.googlemap {
	position: relative;
	padding-bottom: 80%;
	height: 0;
	overflow: hidden;
}
.googlemap iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100% !important;
	height: 100% !important;
}
.access_route{
	max-width: 640px;
	min-width: 220px;
	margin: 0 auto 20px auto;
}
/***custom css***/
.left{
	width: 100%;
	text-align: center;	
}
.right img{
	display: block;
    margin: 0 auto;
    width: 20%;
    padding-bottom: 12px;
}
.left p {
	padding-bottom: 34px;
}
.business img{
	margin-left: 37px;
	width: 88% !important;
}
.brown_col_sp{
	background-color: #fbf7f7;
	border-top: 2px solid #fbf7f7;
}
.white_col_sp{
	border-top: 2px solid #fff;
	background-color: #fff;
}
.bottom{
	border-bottom: 2px solid #ccc;

}
.f115{padding:18px !important;}

}
.row2 strong{
	font-size: 160%;
    color: #c42529
}
.topics{
	display: inline-block;
}
.mart_logo{
	margin: 0 auto; 
    width: 36%;
    display: block;
    margin: 0 auto;
    padding: 54px 0 22px 0;
}
.navin img{
	padding-top: 34px !important;
}
}