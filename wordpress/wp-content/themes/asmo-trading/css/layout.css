.sp-only {
  display: none;
}

.backtotop {
  position: fixed;
  bottom: 1.875rem;
  right: 1.875rem;
  width: 3.125rem;
  height: 3.125rem;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
  z-index: 1000;
  overflow: hidden;
}

.backtotop img {
  width: 1.5rem;
  height: 1.5rem;
}

.backtotop:hover img {
  animation: backToTopAnimate 0.3s forwards;
}

@keyframes backToTopAnimate {
  0% {
    transform: translate(0rem, 0rem);
    opacity: 1;
  }
  49% {
    transform: translate(0rem, -2.125rem);
    opacity: 0;
  }
  50% {
    transform: translate(0rem, 2.125rem);
    opacity: 0;
  }
  100% {
    transform: translate(0rem, 0rem);
    opacity: 1;
  }
}

/* Section wrapper styles */
/* .section-wrapper {
  padding: 6.25rem 15%;
  display: flex;
  position: relative;
  width: 100%;
} */

.section-wrapper {
  padding: 100px 20px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
}

/* To-customer section styles */
#introduce {
  background: var(--secondary-color);
  align-items: center;
}

.introduce {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.introduce__avatar {
  width: 28.125rem;
  height: 28.125rem;
  object-fit: cover;
  border-radius: 50%;
  border: 0.625rem solid #383838;
  padding: 0;
  margin: 0;
}

.introduce__content {
  /* width: 100%; */
  /* text-align: center; */
  margin-left: 2.75rem;
}

.section__title {
  color: var(--primary-color);
  text-align: center;
  font-size: 1.875rem;
  font-family: "FOT-Seurat Pro DB", sans-serif;
  margin-bottom: 1.875rem;
  width: fit-content;
  position: relative;
}

.section__title span {
  display: block;
  position: relative;
}

.section__title span::before,
.section__title span::after {
  content: "";
  position: absolute;
  display: block;
  background: var(--primary-color);
  width: 38%;
  height: 0.05rem;
  top: 70%;
  transform: translateX(-50%);
}

.section__title span::before {
  left: 25%;
}

.section__title span::after {
  right: 25%;
  transform: translateX(50%);
}

.section-title__with-symbol {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.symbol__icon {
  height: 1.875rem;
}

.introduce__paragraph {
  color: #fff;
  font-size: 1.125rem;
  line-height: 2;
}

/* News section styles */
#news {
  gap: 3.125rem;
}

.news-block,
.mart-block {
  flex: 1;
  width: 100%;
  background: #fff;
  display: flex;
  position: relative;
  flex-direction: column;
}

.news-block {
  display: flex;
  flex-direction: column;
  background: #fcfcf6;
  padding: 1.875rem;
  border-radius: 0.1875rem;
  /* height: 100%; */
}

.news-block h4 {
  font-family: "FOT-Seurat Pro DB", sans-serif;
  font-size: 1.875rem;
  position: relative;
  width: fit-content;
  margin-bottom: 1.875rem;
}

.news-block img {
  width: 8.8125rem;
  height: 5.625rem;
  object-fit: cover;
  margin-right: 1.25rem;
}

.news-block__list {
  overflow-y: auto;
  padding: 0 4px;
  /* max-height: 727px; */
}

.news-block__item + .news-block__item {
  margin-top: 1.25rem;
  padding-top: 1.25rem;
  border-top: 0.0625rem solid var(--primary-color);
}

.news-block__item__content {
  font-size: 1rem;
  line-height: 1.8;
}

.news-block__item__content--right {
  display: block;
  width: 100%;
  text-align: right;
}

.news-block__header {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
  color: var(--secondary-color);
}

.news-block__title {
  display: block;
  line-height: 1.5;
  font-weight: bold;
  font-size: 1.1rem;
  font-family: "KozGoPro", sans-serif;
}

.news-block__date {
  font-size: 0.9rem;
  font-family: "FOT-Seurat Pro DB", sans-serif;
}

.mart-block img {
  width: 100%;
  flex: 1;
  border: 2px solid var(--primary-color);
}

.mart-block div {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
}

.mart-block div::after {
  content: "";
  position: absolute;
  display: block;
  height: 100%;
  width: 80px;
  background-color: var(--primary-color);
  left: 0;
}

.mart-block div::before {
  content: "";
  position: absolute;
  display: block;
  height: 100%;
  width: 10px;
  background-color: var(--primary-color);
  left: 90px;
}

.mart-block div img {
  width: 75%;
}
.cooperate-group {
  padding: 6.25rem 0;
  display: grid;
  grid-template-columns: repeat(2, 30rem);
  gap: 3.125rem;
  justify-content: center;
  background: var(--background-color);
  position: relative;
}

.cooperate-group__title {
  display: none;
}

.cooperate-group__item {
  background: #fff;
  position: relative;
  overflow: hidden;
  display: block;
}
.cooperate-group__image--main {
  display: block;
  border-radius: 0.375rem;
  position: relative;
  overflow: hidden;
}

.cooperate-group__image--main img {
  width: 30rem;
  height: 21.875rem;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.cooperate-group__image--main::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 92%;
  border: 0.0625rem solid transparent;
  border-radius: 4px;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease-in-out;
  z-index: 2;
}

.cooperate-group__item:hover .cooperate-group__image--main img {
  transform: scale(1.2);
}

.cooperate-group__item:hover .cooperate-group__image--main::before {
  width: 94%;
  border-color: white;
}

.cooperate-group__image--sub {
  background: #fff;
  text-align: center;
  padding: 0.6875rem 0;
  display: block;
  position: relative;
  z-index: 1;
}
.cooperate-group__image--sub img {
  vertical-align: middle;
  height: 3.125rem;
}

@media screen and (max-width: 767px) {
  .section-wrapper {
    padding: 1.75rem;
    flex-direction: column;
  }

  .introduce {
    flex-direction: column;
    text-align: center;
  }

  .section__title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .introduce__avatar {
    width: 18.75rem;
    height: 18.75rem;
    margin: 1rem auto;
  }

  .introduce__content {
    margin-left: 0;
  }

  .introduce__paragraph {
    font-size: 0.9rem;
    text-align: left;
  }
  #news {
    flex-direction: column;
    gap: 2rem;
    padding: 0;
    margin-bottom: 2rem;
  }

  .news-block {
    max-height: none;
    padding: 0.75rem;
  }

  .news-block h4 {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .news-block img {
    /* display: none; */
  }

  .news-block__title {
    font-size: 1rem;
  }
  .news-block__date {
    font-size: 0.8rem;
  }
  .news-block__item__content {
    display: none;
    font-size: 0.9rem;
  }

  .news-block__item__content img.sp-only {
    width: 100%;
    height: 5.625rem;
    margin-bottom: 0.625rem;
  }

  .news-block__item {
    cursor: pointer;
  }

  .news-block__item + .news-block__item {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }

  .news-block__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
  }

  .news-block__header::after {
    content: "\f30b"; /* Down arrow */
    font-size: 0.8rem;
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
    font-family: FontAwesome;
    display: block;
    width: 0.75rem;
    height: 2.5rem;
    line-height: 2.5rem;
  }

  .news-block__item.active .news-block__header::after {
    transform: rotate(90deg); /* Rotate arrow when active */
  }

  .mart-block {
    padding: 0 2rem;
  }

  .mart-block img {
    content: url("../images/renew/mart_banner_sp.jpg");
  }

  .symbol-row {
    margin-bottom: 1rem;
  }

  .cooperate-group {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 3.75rem 1.875rem;
  }

  .cooperate-group__title {
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
    font-family: "FOT-Seurat Pro DB", sans-serif;
  }

  .cooperate-group__image--main img {
    width: 100%;
    height: 14.375rem;
  }

  .backtotop {
    width: 3.2rem;
    height: 3.2rem;
  }

  .slider-container .button-wrapper svg rect {
    width: 6.875rem;
    height: 3.25rem;
  }
}

@media screen and (min-width: 768px) and (max-width: 1368px) {
  .section-wrapper {
    padding: 3rem;
    flex-direction: row;
  }

  .introduce {
    flex-direction: row;
    text-align: left;
  }

  .introduce__avatar {
    width: 40%;
    height: 40%;
    margin: 0 auto;
    margin-bottom: 2rem;
  }

  .introduce__content {
    margin-left: 2rem;
  }

  .symbol-row {
    margin-bottom: 2rem;
  }

  #news {
    flex-direction: column;
    gap: 2rem;
  }

  .news-block {
    padding: 1.5rem;
  }

  .news-block img {
    width: 8rem;
    height: 6rem;
  }

  .cooperate-group {
    grid-template-columns: repeat(2, 45%);
    gap: 2rem;
    padding: 80px 40px;
  }

  .cooperate-group__image--main img {
  }

  .backtotop {
    width: 3rem;
    height: 3rem;
  }
}
