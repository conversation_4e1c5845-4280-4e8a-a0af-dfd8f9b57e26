@charset "utf-8";
/*===============================================
●style.css 画面の横幅が641px以上
===============================================*/
.g-recaptcha{
    margin-bottom: 15px; 
    display: flex; 
    justify-content: center;
  }
@media print, screen and (min-width: 641px){
	body{
		font-size:95%;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	img{
		max-width: 100%;
		height: auto;
		width /***/:auto;　
	}
	sup{
		font-size:70%;
	}
	.zoom{
		overflow: hidden;
	}
	.zoom img{
		transition: .8s;
	}
	.list-mv01{
		opacity: 0;
		transform: translate(0,150px); 
		-webkit-transform: translate(0,150px);
	}
	.mv01{
		opacity: 1.0;
		transform: translate(0,0); 
		-webkit-transform: translate(0,0);
	}
	.list-mv02{
		opacity: 0;
		transform: translate(-250px,0); 
		-webkit-transform: translate(-250px,0);
	}
	.mv02{
		opacity: 1.0;
		transform: translate(0,0); 
		-webkit-transform: translate(0,0);
	}
	/***** index *****/
	#container{
		min-width:100%;
		box-sizing: border-box;
	}
	.header {
		min-width:100%;
	}
	/*.headerin {
		position:relative;
		max-width:1020px;
		min-height:70px;
		margin:0 auto;
	}*/
	.headerin h1{
		position:absolute;
		font-size:70%;
		top:5px;
		right:30px;
	}
	.header_logo{
		position:absolute;
		top:30px;
		left:50px;
		float: left;
		width: auto;
	}
	.logo_sp{
		display: none;
	}
	.headerin div .nav_sp_logo{
		display: none;
	}
	.headerin div.inquiry{
		position:absolute;
		top:25px;
		right:0;
		width:190px;
	}
	.headerin div.inquiry a{
		display:block;
		text-align:center;
		height:40px;
		line-height:40px;
		border-radius:40px;
		background:#936310;
		color:#fff;
		font-weight:bold;
		font-size:110%;
		text-decoration:none;
	}
	.headerin div.inquiry a:hover{
		background:#704a09;
	}
	.nav{
		min-width:100%;
		background:transparent;
		position: absolute;
		padding: 20px 0 10px 0;
	    display: inline-block;
	    box-sizing: border-box;
	}
	.drawr {
		max-height:45px;
	    float: right;
	}
	.navin{
		margin:0 auto;
	}
	.dropmenu{ 
		*zoom: 1; 
		padding: 10px 30px 10px 10px;
	} 
	.dropmenu:before, .dropmenu:after{ 
		content: ""; 
		display: table; 
	} 
	.dropmenu:after{ 
		clear: both; 
	} 
	.dropmenu li{ 
		position: relative;
		line-height:1.3;
		float:left;
		text-align: center;
		display: inline-block;
	}
	.dropmenu li a{ 
		display: block;
		padding:13px;	
		color: #fff;
		text-decoration: none; 
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.dropmenu li ul{ 
		position: absolute; 
		z-index: 9999; 
		top: 100%; 
		left: 0; 
	} 
	.dropmenu li ul li{ 
		width: 200%;
		border-left:0 !important;
		border-right:0 !important;
		
	} 
	.dropmenu li ul li a{
		border-top: 1px solid #f8cbd1; 
		background: #01a369;
		color:#fff;
		padding:5px 15px;
		text-align: left; 
	} 
	.dropmenu li:hover > a{ 
		color:#c42529;
	} 
	.dropmenu li.active >a{
		color:#c42529 !important;
	} 
	#fade_in li ul{ 
		opacity: 0; 
		top: 50%; 
		visibility: hidden; 
		transition: .5s; 
	} 
	#fade_in li:hover ul{ 
		top: 100%; 
		visibility: visible; 
		opacity: 1; 
	}
	.main_img{
		min-width:100%;
	}
	.main_img img{
		min-width: 100%;
		height: auto;
	}
	.main_img_sp{
		display: none;
	}
	.bx-wrapper{
		margin-bottom: 0;
	}
	.wrapper{
		min-width:100%;
	}
	.top_content:before,.top_content:after {content: " ";display: table;}
	.top_content:after {clear: both;}
	/* IE6/7 support */
	.top_content {*zoom: 1;}
	.top_content02{
		float:left;
		width:60%;
		padding-top: 30px;
	}
	.top_content01{
		float:left;
		width:40%;
	}
	.onlypc{
		display: block;
	}
	.onlysp{
		display: none;
	}
	.top_contentin{
		padding:45px;
	}
	.top_contentin p{
		color: #000;
		line-height: 2;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.pankuzu{
		max-width:980px;
		margin:0 auto;
		padding:10px 20px;
		font-size:70%;
	}
	.pankuzu a{
		text-decoration:none;
	}
	.content {
		max-width:1000px;
		margin:30px auto 0 auto;
	}
	.content p{
		margin-bottom: 30px;
		line-height: 2;
	}
	.top_content h2,
	.row2 strong{
		font-size: 20px;
		font-weight: bold;
		color: #c42529;
		margin-bottom:20px;
		font-family: fot-seurat-pron, sans-serif;
	}
	.row2 strong{
		display: block;
	    margin-bottom: 0;
	}
	.bg_h2{
		min-width:100%;
		height:220px;
		background:url(../images/bg_h2.jpg);
		background-position:center center;
		background-size:cover;
		margin-bottom:30px;
	}
	.bg_h2 h2{
		clear:both;
		text-align:center;
		line-height:200px;
		font-size:200%;
		color:#fff;
		text-shadow: 2px 2px 0 #526791;
		letter-spacing: 2px;
	}
	h3 {
		position: relative;
		font-size:130%;
		margin-bottom:15px;
		color: #c11212;
	    font-family: fot-seurat-pron, sans-serif;
		font-weight: bold;
	}
	.business-content{
		margin-bottom: 20px;
	}
	.business-content03,
	.business-content04{
		margin-top: 50px;
	}
	.business-content h3{
		margin-bottom: 25px;
		font-family: fot-seurat-pron, sans-serif;
	}
	.business-content a{
		color: #c42529 !important;
		text-decoration-color: #db9091;
		background: none;
	}
	.img_pc{
		display: block;
	}
	.img_sp{
		display: none;
	}
	h3:after {
		position: absolute;
		content: " ";
		display: block;
		bottom: -3px;
		width: 20%;
	}
	h4 {
		position:relative;
		padding-left:20px;
		font-size:115%;
		margin-bottom:10px;
	}
	h4::after {
		content:'';
		display:block; 
		position:absolute; 
		background:#6d242e;
		width:9px;
		height:9px; 
		top:8px; 
		left:5px; 
		transform:rotate(-45deg);
		-webkit-transform:rotate(-45deg);
		-o-transform:rotate(-45deg);
	}
	.group_box{
		width:100%;
		padding:50px 0 35px 0;
		margin-bottom: 50px;
		display: inline-block;
	}	
	.group_slide{
		max-width:85%;
		margin:0 auto;
	}
	.group_slide li{
		padding:0 5px;
	}
	div.topics-inc{
		height: 320px;
	    padding: 10px;
	    overflow: auto;
	    width: 70%;
	    margin: 0 auto;
	    display: block;
	}
	ul.topics{
		overflow: hidden; /* MacIE */
	}
	.video{
		position: relative;
	    width: 533px;
	    display: block;
	    margin: 0 auto;
	}
	.youtube_link{
		height: 300px; 
		display: block; 
		margin: 0 auto 50px auto;
		-moz-box-shadow: -30px 30px 30px -15px #a4a4a4;
  		-webkit-box-shadow: -30px 30px 30px -15px #a4a4a4;
  		box-shadow: -45px 45px 30px -15px #a4a4a4;
	}
	.gp-logo{
		position: absolute;
		top: 2%;
		right: 2%;
		width: 150px;
	}
	.youtube_link iframe{
		width: 100%;
		display: block;
		border: none;
	}
	/* NN7 */
	ul.topics:after {
		content: "";
		display: block;
		clear: both;
		height: 1px;
		overflow: hidden;
	}

	/* IE6 */
	* html ul.topics{
		height: 1em;
		overflow: visible;
	}
	.row1{
		float: left;
	    width: 10%;
	    font-weight: bold;
	    margin: 10px 0;
	    position: relative;
	    color: #c42529;
	    background: #f1f1f1;
	    padding: 20px 8px;
	}
	.row1 span{
		font-size: 20px;
		font-weight: bold;
		font-family: fot-seurat-pron, sans-serif;
	}
	.row1 .month-yr{
		font-size: 12px;
		font-family: fot-seurat-pron, sans-serif;
	}
	.row2{
		float:right;
		width:82%;
		margin:10px 10px 10px 0;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.row2 a{
		text-decoration: none;
		color: #000;
	}
	.asmomart{
		width:100%;
		display: inline-block;
		margin-top: 50px;
		margin-bottom: 50px;
		padding-top: 30px;
		background:url(../images/mart-bg.png);
		background-size:cover;
	}
	.asmomart .mart_logo{
		text-align: center;
		margin: 0 auto;
		width: 350px;
		display: block;
	}
	.mart-title{
		color: #c42529;
	    font-size: 24px;
	    font-weight: bold;
	    text-align: center;
	    padding: 30px 20px 20px 20px;
	    letter-spacing: 3px;
	    font-family: fot-seurat-pron, sans-serif;
	}
	.asmomart p{
		width:230px;
		margin:220px auto 0 auto;
	}
	.asmomart p a{
		display:block;
		text-align:center;
		height:50px;
		line-height:50px;
		background:#ad1e1e;
		color:#fff;
		font-weight:bold;
		font-size:110%;
		text-decoration:none;
	}
	.asmomart p a:hover{
		background:#811717;
	}
	.img-list{
		width: 100%;
		display: block;
		margin: 0 auto;
		text-align: center; 
		padding-bottom: 30px;
	}
	.mart-img.left,
	.mart-img.right{
		width: 50%;
	    display: block;
	    float: left;
	    padding: 2%;
	    box-sizing: border-box;
	}
	.mart-img img{
		/*width: 100%;*/
	}
	.footer {
		min-width:100%;
		display: block;
		box-sizing: border-box;
	}
	.footer01{
		background:#252525;
		padding:5px 0;
	}
	.footer01 p.icon{
		display:inline-block;
		vertical-align:middle;
	}
	.footer01 ul{
		display:inline-block;
		vertical-align:middle;
	}
	.footerin{
		max-width:1020px;
		text-align:center;
		margin:0 auto;
	}
	.footerin ul li{
		display:inline-block;
		padding-right:14px;
		margin-right:10px;
		margin-bottom:5px;
		border-right:1px solid #fff;
	}
	.footerin ul li:last-child {
		border-right: 0px;
	}
	.footerin ul li a{
		color:#fff;
		font-size:12px;
		text-decoration:none;
	}
	.footerin ul li a:hover{
		text-decoration:underline;
	}
	.footer02{
		background:#6d242e;
		padding:20px 0;
	}
	address{
		max-width:1020px;
	}
	address p{
		text-align:left;
		font-size:11px;
		color:#fff;
	}
	.pagetop{
		position: fixed;
		z-index:100;
		bottom: -50px;
		right: 20px;
	}
	.pagetop a{
		display: block;
		width: 42px;
		height: 42px;
		border:4px solid #c93a4d;
		color: #c93a4d;
		font-size: 16px;
		font-weight:bold;
		text-decoration: none;
		text-align:center;
		line-height: 42px;
	}
	.pagetop a:hover{
		border:4px solid #f8cbd1;
		color:#f8cbd1;
	}
	/***** dl_bna *****/
	.dl_bna{
		max-width:630px;
		padding:10px;
		background:#f4f4f4;
		border:1px solid #c9c9c9;
		margin:0 auto 30px auto;
	}
	.dl_bna dt{
		border-bottom:1px dotted #aaa;
		padding-bottom:10px;
		margin-bottom:5px;
	}
	.dl_bna dd{
		position:relative;
		padding-left:15px;
	}
	.dl_bna dd:before{
		content:''; 
		display:block; 
		position:absolute; 
		box-shadow: 0 0 2px 2px rgba(255,255,255,0.2) inset;
		top:5px; 
		left:2px; 
		height:0; 
		width:0; 
		border-top: 6px solid transparent;
		border-right: 7px solid transparent;
		border-bottom: 6px solid transparent;
		border-left: 9px solid #6d242e;
	}
	/***** graybox *****/
	.graybox{
		padding:20px;
		background:#f4f4f4;
		border:1px solid #c9c9c9;
		margin:0 auto 30px auto;
	}
	.graybox img{
		width:10%;
		display:inline-block;
		vertical-align:middle;
	}
	.graybox p{
		width:85%;
		display:inline-block;
		vertical-align:middle;
	}
	/***** list *****/
	.list_style{
		max-width:760px;
	}
	.list_style li{
		position:relative;
		padding-left:20px;
		border-bottom:1px dotted #aaa;
	}
	.list_style li:after{
		content:''; 
		display:block; 
		position:absolute; 
		top:10px; 
		left:3px; 
		border-radius:30px;
		height:7px; 
		width:7px; 
		border:3px solid #83d3b6;
	}
	.list_style li span{
		display:inline-block;
		vertical-align:top;
		padding:5px 0;
		width:20%;
	}
	.list_style li em{
		display:inline-block;
		vertical-align:top;
		padding:5px 0;
		width:78%;
	}
	ol.ol_list {
		counter-reset: my-counter;
		padding: 0;
		margin-bottom:30px;
	}
	ol.ol_list a:hover{
		color: #c42529;
	}
	ol.ol_list p{
		margin-bottom: 0;
	}
	ol.ol_list li {
		margin-bottom: 20px;
		padding-left: 30px;
		padding-right:10px;
		position: relative;
	}
	ol.ol_list li:before {
		content: counter(my-counter);
		counter-increment: my-counter;
		background-color: #c11212;
		color: #fff;
		display: block;
		float: left;
		line-height: 22px;
		margin-left: -25px;
		text-align: center;
		height: 22px;
		width: 22px;
		border-radius: 50%;
		font-family: 'Gotham';
	}
	ol.ol_list li ul{
		counter-reset:list;
		margin-top:15px;
		margin-left:10px;
	}
	ol.ol_list li ul li:before{
		counter-increment: list;
		content: counter(list);
		background-color: #b57d85;
	}
	/***** table01 *****/
	.table01 {
		width: 100%;
		margin:0 auto 20px auto;
	}
	.about .table01{
		margin-top: 30px;
	}
	.about .table01 a{
		color: #000;
	}
	.about .table01 a:hover{
		opacity: .8;
	}
	.table01 th{
		width:160px;
		padding: 10px;
		text-align: center;
		vertical-align:top;
		font-weight:bold;
		border: 1px solid #ccc;
	}
	.table01 td{
		padding: 10px 10px 10px 20px;
		background:#fff;
		border: 1px solid #ccc;
	}
	.table01 td.bb-none{
		border-bottom: none;
	}
	.table01 td.bt-none{
		border: none;
    	border-right: 1px solid #ccc;
	}
	.table01 td.pb-10{
		padding-bottom: 10px;
	}
	.table01 td table td{
		padding:0;
		border:none;
	}
	.table01 .brown_col{
		background-color: #fdf5f5;
	}
	.table01 .row-sp{
		border-top: none;
	}
	.table01 .brown_col td{
		background-color: #fdf5f5;
	}
	.table01 .bro_col{
		border-bottom:  1px solid #ccc; 
	}
	.table01 .white_col {
		border-top: none;
		border-left: 1px solid #ccc;
		border-right: 1px solid #ccc; 
	}
	.table01 .white_col a,
	.table01 .brown_col a{
		color: #333;
	}
	.white{
		margin-right: 5px;
	}
	.white01{
		color: #333;
	}
	.botm{
		border-bottom: 1px solid #ccc; 
	}
	/***** table02 *****/
	.table02 {
		width: 100%;
		margin:0 auto 20px auto;
	}
	.table02 th{
		width:12%;
		padding: 5px;
		text-align: center;
		vertical-align:top;
		color:#fff;
		background: #01a369;
		border:1px solid #fff;
	}
	.table02 td{
		padding: 5px;
		text-align: center;
		border:1px solid #01a369;
	}
	/***** table03 *****/
	.table03 {
		width: 100%;
		margin:0 auto 20px auto;
	}
	.table03 th{
		padding: 10px;
		text-align: center;
		font-weight:bold;
		background: #eee;
		border:1px solid #ddd;
	}
	.table03 td{
		padding: 10px;
		text-align: right;
		border:1px solid #ddd;
	}
	/***** table_Form *****/
	.table_Form {
		margin:0 auto 20px auto;
	}
	.table_Form, .table_Form th, .table_Form td{
		/*border-bottom: 1px solid #fff;*/
	}
	.table_Form th{
		width:30%;
		padding: 15px;
		vertical-align:top;
		font-weight:bold;
		background: #ededed;
	}
	.table_Form th div{position:relative;}
	.table_Form th div img{
		position:absolute;
		top:4px;
		right:0;
	}
	.table_Form td{
		padding: 15px;
	}
	.table_Form td input{
		width:100%;
		box-sizing: border-box;
		padding:10px;
	}
	.table_Form td input.wauto{
		width:auto;
		margin-right: 10px;
	}
	.table_Form td input.w10{
		width:11%;
		text-align: center;
		margin-bottom: 0;
		box-sizing: border-box;
    	display: inline-block;
	}
	.table_Form td textarea{
		width:100%;
		padding:10px;
		box-sizing: border-box;
	}
	.form_scroll{max-width:520px; overflow:auto; height:150px;}
	:focus::-webkit-input-placeholder { color: white; } /* Chrome・Safari・Opera用(※Edgeにも使える) */
	:focus:-moz-placeholder { color: white; }  /* Firefox18以前用 */
	:focus::-moz-placeholder { color: white; } /* Firefox19以上用 */
	:focus:placeholder-shown { color: white; } /* CSS標準(予定)の記述 */

	.inquiry_box{
		width:100%;
		height:190px;
		padding:10px 0 10px 0;
		overflow:auto;
		box-sizing: border-box;
		margin-top: 10px;
	}
	.inquiry_box p{
		padding-left: 0;
	}
	.inquiry_form td input, .inquiry_form td textarea, .inquiry_box {
	   font-family: kozuka-gothic-pr6n, sans-serif;
	}
	/***** google map *****/
	.map_wrapper {
		max-width: 950px;
		min-width: 640px;
		margin: 0 auto 20px auto;
		padding: 4px;
		border: 1px solid #CCC;  
	}
	.googlemap {
		position: relative;
		padding-bottom: 50%;
		height: 0;
		overflow: hidden;
	}
	.googlemap iframe {
		position: absolute;
		top: 0;
		left: 0;
		width: 100% !important;
		height: 100% !important;
	}
	.access_route{
		max-width: 640px;
		min-width: 280px;
		margin: 0 auto 20px auto;
	}
	.buss_link{
		color: #c42529 !important;
	}
	.inquiry_content .inquiry_confirm_form{
		padding: 50px 20px 50px 20px;
		box-sizing: border-box;
		display: inline-block;
		background:url(../images/inquiry_form_bg.png);
		background-size:cover;
		background-position: center;
	}
	.table_Form{
		width: 65% !important;
		font-weight: bold;
		box-sizing: border-box;
		text-align: center;
	}
	.table_Form td{
		text-align: left;
	}
	.inquiry_form td.last_td{
		padding-bottom: 20px;
	}
	.table_Form td input,textarea{
		margin-top:14px;
		border: 1px solid #c42529;
		background: #f1f1f1;
	}
	.inquiry{
		background-color: #c42529;
		padding: 13px 20px !important;
	}
	.inquiry:hover{
		background-color: #fff;
	}
	.navin{
		margin-right: 0px;
	}
	.news_title{
		font-family: fot-seurat-pron, sans-serif;
		text-align: center;
	}
	.footer{
		min-width: 100%;
	    height: 385px;
	    background:url(../images/footer.png);
	    position: relative;
	    box-sizing: border-box;
	    background-repeat: no-repeat;
	    background-size: 100% 100%;
	}
	.clearfix::after {
	  content: "";
	  clear: both;
	  display: table;
	}
	.copyright_text{
		color: #333;
		background: #fdf5f5;
		font-weight: normal;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	/* Style the active class*/
	.dropmenu li a.active{
	  color: #c42529;
	}
	.dropmenu li a.inquiry.active{
		background: #fff;
	}
	/*style of radio button*/
	.radio-gp{
		margin-bottom: 10px;
	}
	.radio-item {
	  display: inline-block;
	  position: relative;
	  padding: 0 30px 0 0;
	  margin: 10px 25px 0 0;
	}
	.radio-item input[type='radio'] {
	  display: none;
	}
	.radio-item label {
	  font-weight: normal;
	}
	.radio-item label:before {
	  content: " ";
	  display: inline-block;
	  position: relative;
	  top: 5px;
	  right: -1px;
	  margin: 0 20px 0 0;
	  width: 16px;
	  height: 16px;
	  border-radius: 11px;
	  border: 2px solid #eb0f2c;
	  background-color: transparent;
	}
	.radio-item input[type=radio]:checked + label:after {
	  border-radius: 11px;
	  width: 12px;
	  height: 12px;
	  position: absolute;
	  top: 9px;
	  left: 5px;
	  content: " ";
	  display: block;
	  background: #eb0f2c;
	}
	/*footer*/
	.footer_sp{
		display: none;
	}
	.footer-row{
		width: 100%;
		color: #fff;
		position: absolute;
		bottom: 0;
		top: 30%;
		box-sizing: border-box;
		display: block;
	}
	.footer-row li:hover > a,
	.address li:hover {
		opacity: .8;
	}
	.footer-row .logo img{
		max-width: 100px;
		max-height: 100px;
		padding: 55px 0;
	}
	.social{
		width: 16%;
		box-sizing: border-box;
		float: left;
		display: block;
		padding-left: 5%;
	}
	.useful-link{
		width: 35%;
		box-sizing: border-box;
		float: left;
		padding-left: 2%;
	}
	.useful-link ul{
		width: 50%;
		float: left;
	}
	.footer .logo{
		width: 13%;
		float: left;
	}
	.footer li {
		padding-bottom: 10px;
		 
	}
	.right{
		float: right;
	}
	.footer .footer-row a{
		text-decoration: none;
		color: #fff;	font-size: 13px;
		padding: 8px 0;
		display: inline-block;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.footer .footer-row .sec-col a{
		margin-left: 20px;
	}
	.logo{
		text-align: center;
	}
	.footer-row h3{
		color: #c42529;
		font-size: 20px;
		margin-bottom: 20px;
		letter-spacing: 1px;
		font-weight: bold;
		font-family: fot-seurat-pron, sans-serif;
	}
	.social a{
		font-size: 12px !important;
		background: none;
	}
	.social img,
	.address img{
		width: 13%;
		vertical-align: middle;
	}
	.social img{
		margin-right: 20px;
	}
	.address{
		width: 33%;
		box-sizing: border-box;
		float: left;
		padding-left: 10%;
	}
	.address li{
		margin-bottom: 10px;
	}
	.address img{
		width: 4%;
		float: left;
		display: block;
	}
	.address span,
	.addr_02{
		width: 100%;
		display: block;
		box-sizing: border-box;
		font-size: 13px;
	}
	.addr_02 .ml-5{
		margin-left: 15px;
	}
	.gp-name{
		width: 97% !important;
		margin-left: 30px;
	}
	.addr_02 div{
		width: 80% !important;
	    float: left;
	    margin-left: 15px;
	    line-height: 1.2;
	}
	.tel{
		margin-top: 30px;
	}
	.tel a{
		padding-top: 0 !important;
		margin-left: 15px;
	}
	/*privacy*/
	.privacy h2{
	    color: #c11212;
	    font-size: 24px;
	    margin-bottom: 30px;
	    letter-spacing: 4px;
	    font-family: fot-seurat-pron, sans-serif;
		font-weight: bold;
	}
	.privacy h3{
	    letter-spacing: 4px;
	    color: #c11212;
	    margin-bottom: 25px;
	    font-family: fot-seurat-pron, sans-serif;
	}
	.privacy li b,.privacy li p{
	    padding-left: 12px;
	    line-height: 1.8;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.privacy_text span{
		width: 93%;
		float: left;
	}
	.privacy_text img{
		width: 15px;
		vertical-align: middle;
		margin-right: 5px;
		margin-top: 7px;
		float: left;
	}
	.privacy_text{
		margin-left: 20px;
		margin-bottom: 20px !important;
	}
	.inq_text{
		margin-bottom: 20px !important;
	}
	.other li b{
	    padding-left: 12px;
	}
	.privacy a{
		color: #333333;
	}
	.privacy p.text_right{
		margin-bottom: 40px;
	}
	/*admin*/
	.left{
	  width: 78%;
	  float: left;
	}
	.left p{
		padding: 0;
	    font-size: 90%;
	    letter-spacing: 0px;
	    padding-right: 2px;
	    text-align: justify;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.right{
	  width: 20%;
	  float: right;
	}
	.admin .row{
	  padding-bottom: 10px;
	}
	.admin .row img{
	  padding-top: 30px;
	  box-sizing: border-box;
	  padding-left: 20px;
	}
	.admin .row .admin_img01{
		padding-top: 10px;
	}
	.admin .row04{
		padding-bottom: 0;
	}
	.admin .right-pc{
		display: inline-block;
	}
	.admin .right-sp{
		display: none;
	}
	/*recruit*/
	.recruit_title{
		font-size: 24px;
    	margin-bottom: 5px;
	}
	.recruit_table th{
		text-align: left;
		padding-left: 45px;
	}
	.recruit p{
		margin-bottom: 0;
		padding: 0;
		letter-spacing: 0;
	}
	/*inquiry*/
	.inquiry_content{
		margin-top: 20px;
	}
	.btn_require {
		margin-left: 15px;
		width: 7%;
	}
	.inquiry_form td {
		padding-bottom: 0px;
	}
	.inquiry_form td.inquiry_box_td{
		padding-bottom: 20px;
	}
	.inquiry_form td.phone{
		padding-bottom: 20px;
		padding-top: 20px;
	}
	.inquiry_form td.tb_inq{
		padding-top: 20px;
		padding-bottom: 20px; 
	}
	.inquiry_form td input{
		width: 100%;
		display: block;
		box-sizing: border-box;
		padding: 10px 15px;
		margin-bottom: 10px;
		margin-top: 10px;
	}
	.inquiry_form .inquiry_box p{
		padding-right: 15px !important;
		margin-bottom: 0;
	}
	.inquiry_form a{
		color: #333333;
	}
	#js_pref,
	#js_addr{
		margin-bottom: 20px;
	}
	/*sitepolicy*/
	.site-policy{
		margin-top: 30px;
	}
	.site-policy h2{
	    color: #c11212;
	    font-size: 24px;
	    font-weight: bold;
	    text-align: center;
	    letter-spacing: 4px;
	}
	.site-policy h3{
	    letter-spacing: 4px;
	    font-weight: bold;
	    color: #c11212;
	    text-align: center;
	}
	.site-policy p{
	    text-align: center;
	    margin-bottom: 0;
	    line-height: 1.8;
	    letter-spacing: 0.4px;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.site-policy-cnt{
	    margin-bottom: 35px;
	}
	.site-policy-cnt a{
		color: #333333;
	}
	.site-policy-cnt a:hover{
		color: #c42529;
	}
	.site-policy-cnt:last-child{
	    margin-bottom: 0;
	}
	.ms-skin-light-6 .ms-nav-next, .ms-skin-light-6 .ms-nav-prev{
		display: none;
	}
	.ms-skin-light-6 .ms-bullets.ms-dir-h{
		left: 0;
	}
	.ms-bullets.ms-dir-h .ms-bullets-count{
		left: 1%;
	}
	.ms-sbar .ms-bar{
		display: none;
	}
}
/****custom tablet****/
@media print, screen and  (min-width: 641px) and  (max-width: 1080px){
	/***** index *****/
	#container{
		min-width:100%;
	}
	.header {
		min-width:100%;
	}
	.headerin {
		margin:0 auto;
	}
	.headerin h1{
		font-size:12px;
	}
	.header_logo{
		width: 28%;
		left: 25px;
		top: 13px;
	}
	.headerin div .nav_sp_logo{
		display: block;
		width: 50%;
		margin: 0 auto;
		padding-bottom: 30px;
	}
	.headerin div.inquiry{
		margin:0 auto 10px auto;
	}
	.headerin div.inquiry a{
		height:40px;
		line-height:40px;
		border-radius:40px;
		display:block;
		text-align:center;
		background:#936310;
		color:#fff;
		font-weight:bold;
		font-size:110%;
		text-decoration:none;
	}
	.headerin div.inquiry a:hover{
		background:#704a09;
	}
	.menu-btn {
	    background:transparent url(../images/btn.png) no-repeat 0 0;
	    display: block;
	    width:45px;
	    height: 45px;
		position: fixed;
	    top:0;
	    right:0;
	    cursor: pointer;
	    z-index: 200;
	}
	.peke {
	    background-position: -45px 0;
	}
	.drawr {
	    display: none;
		position: fixed;
	    top: 0;
	    right:0;
		min-height: 100%;
	    width:200px;
	    z-index: 100;
		overflow-x: hidden;
		overflow-y: auto;
		font-family: "lucida grande", tahoma, verdana, arial, sans-serif;
		font-size: 15px;
		background: #f8f8f8;
		color: #333;
		box-shadow: 0 0 5px 5px #ebebeb inset;
	}
	.drawr ul {
		display: block;
		margin: 0;
		padding: 0;
		background:#fefefe;
	}
	.drawr ul li {
		display: block;
		margin: 0;
		line-height: 40px;
		float: none;
	}
	.drawr ul li:hover, .drawr ul li.active, .drawr ul li.sidr-class-active {
		border-top: 0;
		line-height: 41px;
	}
	.drawr ul li:hover > a,
	.drawr ul li:hover > span, .drawr ul li.active > a,
	.drawr ul li.active > span, .drawr ul li.sidr-class-active > a,
	.drawr ul li.sidr-class-active > span {
		color: #c42529;
	}
	.drawr ul li a{
		display: block;
		padding:0 15px;
		line-height:25px;
		font-size:75%;
		text-decoration: none;
		color: #333333;
		text-align: center;
	}
	.dropmenu li a.active {
		background: #c42529;
		color: #fff !important;
	}
	.dropmenu li a.inquiry.active{
		background: #c42529 !important;
		color: #fff !important;
	}
	.dropmenu:before, .dropmenu:after{
		display: none;
	}
	.drawr ul li span{
		display:block;
		line-height:20px;
	}
	.drawr ul li ul {
		border-bottom: 0;
		margin: 0;
	}
	.drawr ul li ul li {
		height:30px;
		line-height: 25px;
		font-size: 14px;
		background:#eee;
	}
	.drawr ul li ul li:last-child {
		border-bottom: 0;
	}
	.drawr ul li ul li:hover, .drawr ul li ul li.active, .drawr ul li ul li.sidr-class-active {
		border-top: 0;
		line-height: 26px;
	}
	.drawr ul li ul li:hover > a,
	.drawr ul li ul li:hover > span, .drawr ul li ul li.active > a,
	.drawr ul li ul li.active > span, .drawr ul li ul li.sidr-class-active > a,
	.drawr ul li ul li.drawr-class-active > span {
		box-shadow: 0 0 15px 3px #ccc inset;
	}
	.drawr ul li ul li a,
	.drawr ul li ul li span {
		color: rgba(51, 51, 51, 0.8);
		padding-left: 20px;
		color:#444 !important;
		font-size:14px !important;
		font-weight:normal;
	}
	.inquiry {
	    background: none !important;
	}
	.nav div.navin ul li{
		line-height:25px !important;
	}
	.nav div.navin ul li a{
		padding-top:10px;
		padding-bottom:10px;
	}
	.nav_sp_logo img{
		padding: 34px 0 0 0 !important;
	}
	/*h2{
		color: #e52529;	
	}*/
	h3{
		text-align: center;
		letter-spacing: 1px !important;
		color: #e52529 !important;	
	}
	.admin h3 ,p{
		text-align: left;
		padding-left: 22px !important;
	}
	.admin h3{
		padding-left: 0 !important;
	}
	ol.ol_list li {
	    padding: 0px 0px 0px 47px;
	}
	.privacy p{
		padding-right: 37px;
	}
	ol.ol_list li ul li{
		padding-left: 23px;
	}
	ol.ol_list li:before{
		background-color: #cf5154 !important;
		margin-top: 5px;	
	}
	ol.ol_list li ul li:before {
    
    	background-color: #b57d85 !important;
	}
	.table_Form,.table01 {
	    width: 85%;
	    margin: 0 auto 30px auto;
	}
	.table01 td{
		padding-left: 22px;
	}
	.tb_logo{
		display: block;
	}
	.asmomart{
		margin-top: 30px;
		margin-bottom: 0;
	}
	.admin .row img{
		width: 83%;
		padding-top: 53px !important;
		padding-left: 0 !important;

	}
	.business-content p{
		padding: 0 !important;
		text-align: center;
	}
	.mart_logo{
		padding-right: 0 !important;
	}
	.business-content img{
		/*width: 87% !important;*/
	}
	.business-content a {
		color: #e52529 !important;
		background: none;	
	}
	.top_content1 .top_content02 .top_contentin{
		padding:0 30px 50px 30px;
	}
	.top_content02 h2, .row2 strong{
		font-size: 95%;
	}
	.top_contentin p{
		font-size: 80%;
	}
	.top_content02 p{
		padding:0 !important;
	}
	.top_content2 .top_contentin{
		padding: 20px;
	}
	div.topics-inc {
		width: 90% ;
	}
	.row2 {
		width: 81%;
		font-size: 85%;
	}
	.row2 strong{
		font-size: 130%;
	}
/* 	.topics-inc .row1{
		width: 14%;
		height: 66px;
	} */
	.mart-title{
		font-size: 130%;
	}
	.mart-img {
    	width: 40.5%;
    }
    .slick-slide a{
	    max-width: 80%;
	    margin: 0 auto;
	    padding: 0 2%;
	}	
    .slick-slide img{
		padding-right: 0 !important;
		vertical-align: middle;
		max-width: 80%;
		display: block;
	}
	.slick-prev,
	.slick-next{
		height: 15px !important;
		top: 50% !important;
	}
	.slick-prev {
		left: -25px !important;
	}
	.slick-dots {
		bottom: -30px !important;
	}
	.slick-dots li button:before{
		border: 2px solid #c42529 !important;
	}
	.group_box{
		margin-bottom: 0;
	}
	.topics .row2 p,
	.site-policy-cnt p{
		padding-left: 0 !important;
	}
	.business-content{
		width: 100%;
	    padding: 0 30px;
	    box-sizing: border-box;
	}
	.admin .content,
	.site-policy,
	.privacy{
		width: 100%;
		display: block;
		padding: 0 20px;
		box-sizing: border-box;
	}
	.admin .content .row{
		padding-bottom: 10px;
	}
	.admin .content .row .left{
		width: 75%;
		padding-left: 0;
	}
	.admin .content .row .left h3,
	.admin .content .row .left p,
	.admin .content .row .row-desc h3,
	.admin .content .row .row-desc p{
		padding-left: 0 !important;
	}
	.admin .content .row .row-desc p{
		margin-bottom: 0 !important;
	}
	.recruit p{
		text-align: center;
	}
	.inquiry_content{
		width: 100%;
		box-sizing: border-box;
		margin-top: 10px;
	}
	.confirm-inq,
	.complete-inq{
		padding: 0 20px !important;
	}
	.complete-inq h4{
		text-align: center;
	}
	.complete-inq h4::after{
		left: 36%;
	}
	.confirm-inq p,
	.complete-inq p{
		padding: 0 !important;
	}
	.inquiry_form {
	    width: 90% !important;
	    margin: 0 auto 30px auto;
	    display: block;
	    box-sizing: border-box;
	}
	.inquiry_form tr{
		margin-bottom: 10px;
	}
	.inquiry_form td{
		padding: 15px 15px 5px 15px !important;
	}
	.inquiry_form td input,
	.inquiry_form td textarea{
		border-radius: 3px;
	}
	.inquiry_box{
		padding: 20px 20px 20px 0;
	}
	.inquiry_form .inquiry_box p{
		padding-left: 0 !important;
		padding-right: 0 !important;
	}
	.inquiry_box p{
		text-align: left !important;
	}
	.inquiry_content p{
		text-align: center;
		padding: 0 20px;
		margin-bottom: 0;
	}
	.confirm_form td{
		padding-left: 20px;
	}
	.confirm_form p{
		text-align: left;
	}
	.inquiry_content .btn_link_back{
		margin-bottom: 30px !important;
	}
	.privacy .inq_text{
		padding: 0 0 0 12px !important;
	}
	.privacy p.text_right{
		margin-bottom: 0;
	}
	.privacy .ol_list li p{
		padding-left: 13px !important;
		font-family: kozuka-gothic-pr6n, sans-serif;
	}
	.privacy .ol_list_last{
		margin-bottom: 0;
	}
	.about .table01 a{
		color: #000;
	}
	.about .table01 a:hover{
		opacity: .8;
	}
	.footer{
		height: 400px;
	}
	.footer-row{
    	bottom: 0;
    	top: 27%;
    	width: 100%;
    	box-sizing: border-box;
    }
    .footer-row .logo img{
    	padding-left: 10px;
    }
    .copyright_text{
    	width: 100%;
    	display: block;
    	margin: 0 auto;
    	box-sizing: border-box;
    }
    .copyright_text p{
    	text-align: center;
    	padding: 0;
    }
    .footer .footer-row a{
    	font-size: 80%;
    }
    .useful-link{
    	padding-left: 30px;
    }
    .social {
	    width: 20%;
	}
    .social img, .address img {
    	width: 10%;
    	padding: 0 !important;
	}
	.footer-row h3{
		text-align: left;
		margin-bottom: 0;
		font-size: 110%;
	}
	.footer ul{
		margin-top: 20px;
	}
	.footer .useful-link .sec-col{
		width: 40%;
	}
	.address img{
		width: 7%;
		margin-right: 10px;
	}
	.address span {
		width: 80%;
		font-size: 80%;
	}
	.addr_02 div span {
		font-size: 86%;
	}
	.addr_02 .ml-5{
		margin-left: 0;
	}
	.bxslider_img_w ul li img{
		min-width:100%;
		min-height: 400px !important;
		max-height: 400px !important;
	}
	.address {
		width: 30%;
		padding-left: 2%;
	}
	.gp-name {
		width: 97% !important;
		margin-left: 15px;
	}
	.addr_02 div {
		width: 78% !important;
		margin-left: 5px;
	}
	.tel img {
		width: 7%;
		margin-top: 7px;
	}
	.tel a {
		margin-left: 0px;
	}
}
@media print, screen and  (min-width: 641px) and  (max-width: 767px){
	.addr_02 .ml-5 {
		margin-left: 0 !important; 
		width: 100% !important;
	}
	.footer .footer-row a,
	.social a{
		font-size: 58% !important;
	}
	.address span {
		font-size: 75%;
	}
	.addr_02 div span {
		font-size: 85%;
	}
}
@media print, screen and  (min-width: 768px) and  (max-width: 1080px){
	.footer {
	    height: 340px;
	}
    .footer-row{
    	bottom: 0;
    	width: 100%;
    	box-sizing: border-box;
    }
    .footer-row .logo img{
    	padding-left: 10px;
    }
    .copyright_text{
    	width: 100%;
    	display: block;
    	margin: 0 auto;
    	box-sizing: border-box;
    }
    .copyright_text p{
    	text-align: center;
    	padding: 0;
    }
    .footer .footer-row a{
    	font-size: 70%;
    }
    .useful-link{
    	padding-left: 30px;
    }
    /*.social{
    	margin-right: 30px;
    }*/
    .social img, .address img {
    	width: 10%;
    	padding: 0 !important;
	}
	.footer-row h3{
		text-align: left;
		margin-bottom: 0;
	}
	.footer ul{
		margin-top: 20px;
	}
	.footer .useful-link .sec-col{
		width: 45%;
	}
	.address img{
		width: 5%;
	}
	/*.address span {
		width: 86% !important;
		font-size: 78%;
	}*/
	.addr_02 .ml-5 {
	   	width: 100% !important;
	}
	.privacy{
		margin-top: 40px;
	}
	.inquiry_form td input,
	.inquiry_form td textarea,
	.inquiry_box{
		/*width: 90%;*/
	}
	.inquiry_box{
		padding-right: 10px;
	}
	.btn_require {
	    width: 5%;
	}
	.recruit_title {
	    font-size: 130%;
	}
	.complete-inq h4::after {
	    left: 38%;
	}
	.addr_02 div{
		margin-left: 0 !important;
	}
	.top_contentin p{
		font-size: 80%;
	}
}
@media print, screen and (min-width: 900px) and (max-width: 1080px){
	.inquiry_form{
		width: 74% !important;
	}
	.privacy .ol_list li p{
		padding-left: 13px !important;
		font-family: kozuka-gothic-pr6n, sans-serif;
		font-size: 75%;
	}
	.privacy p.text_right{
		font-size: 75%;
	}
	.complete-inq h4::after {
	    left: 40%;
	}
}
@media print, screen and (min-width: 1150px) and (max-width: 1800px){
	.footer {
    	height: 350px;
    }
}