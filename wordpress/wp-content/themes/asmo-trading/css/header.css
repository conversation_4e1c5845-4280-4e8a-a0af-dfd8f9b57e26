/* Header styles */
header {
  display: flex;
  align-items: center;
  padding: 0 3%;
  background: rgb(255, 255, 255);
  background: linear-gradient(
    110deg,
    rgba(255, 255, 255, 0.75) 12%,
    rgba(255, 255, 255, 0.75) 30%,
    rgba(0, 0, 0, 0.75) 50%,
    rgba(0, 0, 0, 0.75) 100%
  );
  width: 100%;
  position: relative;
  height: 7.875rem;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}

header:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 25%;
  max-width: 30rem;
  height: 100%;
  border-bottom-right-radius: 5rem;
  background: #fff;
  z-index: -1;
  opacity: 0.8;
}

header #logo {
  flex-shrink: 1;
  position: relative;
}

header #logo img {
  height: auto;
  max-height: 3.75rem;
}

/* Navigation styles */
nav {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
  height: 100%;
}

nav ul {
  list-style: none;
  display: flex;
  gap: 5%;
  margin-right: 3%;
  padding: 0;
  position: relative;
  justify-content: flex-end;
  flex-grow: 1;
}

header li {
  width: fit-content;
  position: relative;
  text-align: center;
}

header li {
  color: #fff;
  text-align: left;
  position: relative;
  font-size: 1rem;
  text-align: center;
}
/* Hover effect for navigation links */
header nav li a:hover,
header nav li a.active {
  font-weight: bold;
  color: var(--danger-color);
}

header nav li a::before,
header li a.active::before {
  text-align: center;
  width: 0;
  height: 0;
  content: "";
  position: absolute;
  bottom: -0.625rem;
  left: 0%;
  border-width: 0rem;
  border-style: solid;
  border-color: transparent transparent var(--danger-color) transparent;
  transform: translate(-50%, 0);
  transition: left 0.3s ease-in-out;
  width: 0%;
}

header nav li a:hover::before,
header nav li a.active::before {
  left: 50%;
  display: block;
  border-width: 0.625rem;
}

header nav li a::after,
header nav li a.active::after {
  content: "";
  position: absolute;
  display: block;
  height: 0.125rem;
  width: 0%;
  bottom: -0.625rem;
  background: var(--danger-color);
  transition: width 0.3s ease-in-out;
}

header nav li a:hover::after,
header nav li a.active::after {
  width: 100%;
}

header .contact-btn {
  background-color: var(--danger-color);
  color: #fff;
  font-size: 0.9rem;
  font-weight: bold;
  margin-right: 3%;
  text-wrap-mode: nowrap;
  padding: 0 0.75rem;
  border-radius: 0.125rem;
  display: flex;
  height: 3rem;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;
  flex-shrink: 1;
}

header .contact-btn:hover {
  background-color: #fff;
  color: #000;
}

header .social-icons {
  display: flex;
  gap: 1.875rem;
  border-left: 0.18rem solid #ccc;
  align-items: center;
  padding-left: 3%;
  flex-shrink: 1;
}

header .social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.6rem;
  height: 3rem;
}

header .social-icons img:hover {
  filter: drop-shadow(0 0 1px #ffffff50);
}

/* Banner styles */
.banner {
  position: relative;
  top: 0;
  left: 0;
}

.banner-title {
  position: absolute;
  top: 42%;
  left: 0%;
  text-align: center;
  color: var(--secondary-color);
  font-size: 1.5rem;
  z-index: 10;
}

/* .banner-title h2 {
  margin: 0.5rem 3.4375rem;
  font-size: 2.5rem;
  color: #303017;
  background: #fffe87;;
  padding: 0.25rem 12px;
  text-align: left;
  width: fit-content;
  font-size: 2.5rem;
  position: relative;
  border-radius: 0.25rem;
  line-height: 3.75rem;
  font-family: "Noto Sans JP", sans-serif !important;
  font-weight: bold;
} */
 
.banner-title h2 { 
   text-align: left;
}
.banner-title h2 span {
  color: var(--danger-color);
  font-family: "Noto Sans JP", sans-serif !important;
}

.banner-title h2:last-child {
  /* background: var(--danger-color); */
  /* background: #fffe87; */
  font-size: 2rem;
  padding-left: 1rem;
  color: #000;
  font-weight: bold;
}

/* Slider container styles */
.slider-container {
  width: 100%;
  /* height: 100vh; */
  margin: 0 0 0 auto;
  margin-top: 7.875rem;
  position: relative;
  max-height: 38vw;
}

.header-slider .slide img {
  width: 100%;
  transition: transform 1s ease-in-out;
  height: 100vh;
  max-height: 38vw;
  object-fit: cover;
}
.custom_banner,
.header-slider .inquiry-slide img,
.header-slider .other-slide img {
  height: 60vh !important;
}

@media screen and (max-width: 1120px) {
  .header-slider .slide img {
    height: 44rem;
  }
}

@media screen and (max-width: 767px) {
  .header-slider .slide img {
    height: 22rem;
  }
}

.header-slider .slick-current img {
  transition: transform 2s ease-in-out;
}

@media screen and (max-width: 1120px) {
  .pc-only {
    display: none;
  }

  .sp-only {
    display: block !important;
  }

  .drawr {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 90%;
    z-index: 1000;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: 0.9375rem;
    background: #212121cf !important;
    padding-top: 2rem;
    border-right: 1px solid #404040;
  }

  .menu-btn,
  .menu-btn {
    background: transparent url(../images//renew/btn.png) no-repeat 0 0;
    display: block;
    width: 2.8125rem;
    height: 2.8125rem;
    position: fixed;
    top: 1rem;
    right: 1.5rem;
    cursor: pointer;
    z-index: 200;
    background-position: 0 50%;
  }

  .menu-btn.active {
    background-position: -2.8125rem 50%;
  }

  #bg_blur.active {
    width: 100%;
    height: calc(100% - 5rem);
    background: rgb(0 0 0 / 52%);
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 999;
  }

  header .copyright {
    border-top: 1px solid #ccc;
    padding-top: 1.5rem;
    margin: 1.5rem 0;
    color: #f5f5dc;
    font-size: 0.7rem;
    text-align: center;
    width: 100%;
    position: relative;
  }
  header li.copyright:hover {
    color: var(--text-color);
  }

  header nav li:hover,
  header nav li:hover a,
  header nav li a:hover,
  header nav li a.active {
    color: var(--primary-color) !important;
    font-weight: unset;
  }

  header nav li a.active::before {
    left: -1rem;
    top: 20%;
    border-color: transparent transparent transparent #f5f5dc;
    transform: scale(0.7);
    z-index: 10;
  }

  header nav li a:hover::before {
    display: none;
  }

  header nav li a::after,
  header nav li a.active::after {
    background: var(--primary-color);
    /* bottom: -0.3rem; */
  }

  header {
    height: 5rem;
    justify-content: flex-start;
    background: #fff;
    padding: 0 2rem;
  }

  header #logo img {
    margin-bottom: 0;
    width: 15rem;
    height: auto;
  }

  header nav {
    flex-direction: column;
    margin: 0 0 2rem;
    width: 100%;
    gap: 0.625rem;
    margin-top: 5rem;
    padding-top: 5rem;
  }
  header nav ul {
    flex-direction: column;
    gap: 0;
    margin-right: 0;
    padding: 1rem 2.5rem;
    position: relative;
    justify-content: space-around;
    width: 100%;
  }
  header nav li a {
    color: var(--text-color);
  }

  header .contact-btn {
    display: none;
  }

  header .social-icons {
    display: none;
  }

  .slider-container {
    height: 44rem;
    width: 100%;
    /* max-height: 44rem !important; */
    overflow: hidden;
  }

  .header-slider {
    height: 44rem;
  }
  .header-slider .slide:nth-child(2) img {
    object-position: 80%;
  }
  .header-slider .slide:nth-child(4) imPg {
    object-position: 63%;
  }
  .slick-dots {
    bottom: 2.5rem !important;
  }
  .slick-dots li {
    margin: 0 0.125rem !important;
  }
  .slick-dots li button:before {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
  .slick-slider .slick-track,
  .slick-slider .slick-list {
    margin-bottom: 0.625rem;
  }

  .banner-title h2,
  .banner-title h2 span {
    font-size: 1.6rem;

    padding: 0;
  }
  .banner-title h2:nth-child(1) {
    margin-bottom: 0.5rem;
  }

  .banner-title h2:nth-child(2) {
    font-size: 1.3rem;
  }
}

@media screen and (max-width: 767px) {
  .banner-title {
    width: 100%;
    top: unset;
    bottom: 30%;
  }
  .banner-title h2,
  .banner-title h2 span {
    font-size: 1.15rem;
    margin: 0;
    /* padding: 0.25rem; */
    font-weight: bold;
    line-height: 2.5rem;
  }
  .banner-title h2:nth-child(1) {
    margin-bottom: 0.5rem;
  }

  .banner-title h2:nth-child(2) {
    width: fit-content;
    text-align: right;
    position: absolute;
    right: 0;
    padding-left: 0.5rem;
    font-size: 0.8rem;
  }

  .header-slider .slide:first-child {
    /* content: url("../images/renew/slider1_sp.jpg"); */
    /* object-position: 0 -4.5rem; */
    scale: 1.125;
  }
  .slider-container {
    margin-top: 5.875rem;
  }

}
