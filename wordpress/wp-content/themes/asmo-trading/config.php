<?php
/*todo: config information about $email of this system. $email: Send mail to user.*/
// 本番環境
return array(
    "SMTP_CHARSET" => 'UTF-8',
    "SMTP_HOST" => 'email-smtp.us-west-2.amazonaws.com',
    "SMTP_USERNAME" => 'AKIAUUXKJFJ3YOIJDHGJ',
    "SMTP_PASSWORD" => 'BHGwt0XG/6K7lhZwKkeWNoxnAlots5jMWo4DxbMrb1oB',
    "SMTP_SERCURE" => 'tls',
    "SMTP_PORT" => '587',
    "EMAIL_FROM" => '<EMAIL>',
    "SMTP_EMAIL_ADMIN" => '<EMAIL>',
    "BCC_USERNAME" => '<EMAIL>',
);	
// return array(
//     "SMTP_CHARSET" => 'UTF-8',
//     "SMTP_HOST" => 'asmo-trd.sakura.ne.jp',
//     "SMTP_USERNAME" => '<EMAIL>',
//     "SMTP_PASSWORD" => 'sa2JBuGA',
//     "SMTP_SERCURE" => 'tls',
//     "SMTP_PORT" => '587',
//     "EMAIL_FROM" => '<EMAIL>',
//     "SMTP_EMAIL_ADMIN" => '<EMAIL>',
//     "BCC_USERNAME" => '<EMAIL>',
// );	
// テスト環境
// return array(
//      "SMTP_CHARSET" => 'UTF-8',
//     "SMTP_HOST" => 'mp-co.sakura.ne.jp',
//     "SMTP_USERNAME" => '<EMAIL>',
//     "SMTP_PASSWORD" => 'LHhEMR1K',
//     "SMTP_SERCURE" => 'tls',
//     "SMTP_PORT" => '587',
//     "SMTP_EMAIL_ADMIN" => '<EMAIL>',
// 	"BCC_USERNAME" => '<EMAIL>',
// );
