<?php 
/*
 * Template Name: Jobs Template
 */
get_header(); ?>
<div class="wrapper">
    <div class="content jobs">
        <div class="title">
            <h3>応募フォーム</h3>
            <div class="img">
                <img src="<?php echo get_template_directory_uri().'/images/CATTLE-CHARCOAL.png'?>" alt="株式会社アスモトレーディング">
            </div>
        </div>
        <div class="description">
            下記フォームに必要事項をご記入の上、「確認画面へ」ボタンを押してください。 
        </div>
        <br>
        <form method="post" id="confirm_form" class= "jobs-form" action="/jobs/confirm">
            <div class="row-group">
                <div class="label">
                    お名前
                    <div class="required">必須</div>
                </div>
                <div class="input-control">
                    <input type="text" placeholder="山田　太郎" name="name">
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    フリガナ
                </div>
                <div class="input-control">
                    <input type="text" name="name_furigana">
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    性別
                </div>
                <div class="check-group">
                    <label class="input-checkbox">
                        <input type="radio" name="gender" value="男性">
                        男性
                    </label>
                    <label class="input-checkbox">
                        <input type="radio" name="gender" value="女性">
                        女性
                    </label>
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    メールアドレス
                    <div class="required">必須</div>
                </div>
                <div class="input-control">
                    <input type="text" placeholder="<EMAIL>" name="email">
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    電話番号  
                    <div class="required">必須</div>
                </div>
                <div class="input-control">
                    <input type="text" placeholder="0663650029" name="phone">
                </div>
            </div>
            <p>※ハイフンは入れずに入力して下さい。</p>
            <div class="row-group">
                <div class="label">
                    住所
                </div>
                <div class="input-post">
                    <div class="lb-t">郵便番号：〒</div>
                    <div class="input-control">
                        <input type="text" placeholder="530" name="zip1" id="js_zip1" maxlength="3">
                    </div>
                    <div class="lb">ー</div>
                    <div class="input-control">
                        <input type="text" placeholder="0057" name="zip2" id="js_zip2" maxlength="4">
                    </div>
                    <div class="lb-t">（半角数字）</div>
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    都道府県：
                </div>
                <div class="input-control">
                    <input type="text" placeholder="大阪府" name="pref" id="js_pref">
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    市区町村・番地：
                </div>
                <div class="input-control">
                    <input type="text" placeholder="大阪市北区曽根崎1-2-9" name="city" id="js_city">
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    建物名称他：
                </div>
                <div class="input-control">
                    <input type="text" name="address">
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    希望職種
                </div>
                <div class="input-control">
                    <input type="text" name="job_content">
                </div>
            </div>
            <div class="row-group">
                <div class="label">
                    個人情報保護
                    <div class="required">必須</div>
                </div>
                <div class="jobs-privacy">
                    ご登録いただく方は、<a href="/privacy">個人情報保護方針に同意</a> したものとします。 <br>
                    よくお読みの上、同意いただける場合は[同意する]にチェックをお願い致します。
                </div>
                <div class="check-group">
                    <label class="input-checkbox">
                        <input type="radio" name="agree" value="0">
                        同意しない
                    </label>
                    <label class="input-checkbox">
                        <input type="radio" name="agree" value="1">
                        同意する
                    </label>
                </div>
            </div>
            <div class="g-recaptcha" data-sitekey="6LeyulUnAAAAAM_1YKUmy06yU-_KDtVeuhpUOXQN" data-callback="onSubmit"></div>
            <button class="btn-submit" type="submit">
                <div class="img">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"/></svg>
                </div>
                <div class="text">
                    内容確認画面へ
                </div>
            </button>
            <input type="hidden" id="recaptcha_token" name="token">
        </form>
        <br>
    </div>
</div>
<script src="https://www.google.com/recaptcha/api.js?hl=ja" async defer></script>
<script type="text/javascript">
    $(window).ready(function() {
        $('#js_zip1').jpostal({
            postcode: [
                '#js_zip1',
                '#js_zip2'
            ],
            address: {
                '#js_pref': '%3',
                '#js_city': '%4%5'
            }
        });
        $('#confirm_form').submit(function(){
            if ($("#recaptcha_token").val()=='') {
                return false
            }
        })
    });
    function onSubmit(token){
        $("#recaptcha_token").val(token)
    }
</script>
<?php get_footer(); ?>