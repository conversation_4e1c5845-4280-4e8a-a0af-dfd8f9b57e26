<!doctype html>
<html>

<head></head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,user-scalable=no,maximum-scale=1"/>
<link rel="shortcut icon" href="/favicon/favicon.ico"/>
<?php
wp_head();
global $title, $meta_keywords, $meta_description, $h1, $wp;

// Page meta data
$page_meta = [
    'home' => [
        'title' => "株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'about' => [
        'title' => "会社概要｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "会社概要｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'introduce' => [
        'title' => "メキシコ産牛肉って美味しいの？｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "管理体制｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'business' => [
        'title' => "事業内容｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "事業内容｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'privacy' => [
        'title' => "個人情報保護方針｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "個人情報保護方針｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'sitepolicy' => [
        'title' => "サイトポリシー｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "サイトポリシー｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'recruit' => [
        'title' => "採用情報｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "採用情報｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'inquiry' => [
        'title' => "お問い合わせ｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "お問い合わせ｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/input_validation.js']
    ],
    'inquiry/confirm' => [
        'title' => "お問い合わせ｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "お問い合わせ｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/input_validation.js']
    ],
    'inquiry/complete' => [
        'title' => "お問い合わせ｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "お問い合わせ｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/input_validation.js']
    ],
    'jobs' => [
        'title' => "応募フォーム｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "応募フォーム｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/job_validation.js']
    ],
    'jobs/confirm' => [
        'title' => "応募フォーム｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "応募フォーム｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/job_validation.js']
    ],
    'jobs/complete' => [
        'title' => "応募フォーム｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "応募フォーム｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/job_validation.js']
    ],
    'default' => [
        'title' => "404 Not Found｜株式会社アスモトレーディング",
        'description' => "株式会社アスモトレーディングは、食肉卸売事業、給食事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,給食事業"
    ]
];

$current_page = is_home() || is_front_page() ? 'home' : ($wp->request ?? 'default');
$meta = $page_meta[$current_page] ?? $page_meta['default'];

$title = $meta['title'];
$meta_description = $meta['description'];
$meta_keywords = $meta['keywords'];

// Output page-specific scripts
if (!empty($meta['scripts'])) {
    foreach ($meta['scripts'] as $script) {
        echo '<script src="' . esc_url($script) . '" type="text/javascript"></script>';
    }
}
?>
<title><?php echo esc_html($title); ?></title>
<meta name="Keywords" content="<?php echo esc_attr($meta_keywords); ?>"/>
<meta name="Description" content="<?php echo esc_attr($meta_description); ?>"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
</head>

<body class="<?php echo (is_home() || is_front_page()) ? 'home' : ''; ?>">
<div>
    <header>
        <a href="/" id="logo">
            <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/asmo_trading_logo.png'); ?>"
                 alt="Image 1">
        </a>
        <nav class="drawr">
            <ul>
                <li><a href="/">ホーム</a></li>
                <li><a href="/business">事業内容</a></li>
                <li><a href="/introduce">メキシコ産牛肉って美味しいの？</a></li>
                <li><a href="/about">会社概要</a></li>
                <li><a href="/recruit">採用情報</a></li>
                <li class="sp-only"><a href="/inquiry">お問い合わせ</a></li>
                <li class="sp-only"><a href="/sitepolicy">サイトポリシー</a></li>
                <li class="sp-only"><a href="/privacy">個人情報保護方針</a></li>
                <li class="copyright sp-only">Copyright© ASMOTRADING CORPORATION.<br/>All Rights Reserved.
                </li>
            </ul>
            <a class="contact-btn" href="/inquiry/">お問い合わせ</a>
            <div class="social-icons">
                <a href="https://www.instagram.com/asmo_trading/">
                    <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/instagram_white.png'); ?>"
                         alt="Instagram">
                </a>
                <a href="#">
                    <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/contact_white.png'); ?>"
                         alt="Contact">
                </a>
            </div>
        </nav>
        <a class="menu-btn"></a>
        <div id="bg_blur" class="sp-only"></div>
    </header>
    <div class="banner">
        <?php
        $bannerClassMap = [
            '/introduce' => 'custom-slider-banner',
            '/about' => 'custom-slider-banner-hidden',
            '/recruit' => 'banner-title-bg',
            '/business' => 'custom-slider-banner-hidden',
            '/inquiry' => 'custom-slider-banner-hidden',
            '/privacy' => 'custom-slider-banner-hidden',
            '/sitepolicy' => 'custom-slider-banner-hidden',
        ];
        $currentUrl = rtrim(strtok($_SERVER['REQUEST_URI'], '?'), '/');
        $bannerClass = $bannerClassMap[$currentUrl] ?? '';

        $classBannerUrl = [
            '/business'=> 'custom_banner',
            '/introduce' => 'custom_banner',
            '/about' => 'custom_banner',
            '/recruit' => 'custom_banner',
            '/inquiry' => 'custom_banner',
        ];
        ?>
        <?php if ($currentUrl !== '/recruit' && $currentUrl !== '/introduce'): ?>
         <div class="banner-title <?php echo esc_attr($bannerClass); ?>">
            <?php if ($currentUrl === '' || $currentUrl === '/'): ?>
                <h2>
                    <img class="banner-title-img" src="<?php echo esc_url(get_template_directory_uri() . '/images/企業理念②-1 2.png'); ?>"
                         alt="企業理念②-1">
                </h2>
                
            <?php else: ?>
                <h2><span>食肉卸売事業、</span>通信販売事業を通じて、</h2>
                <h2>安全で安心な「食」をお客様に提供してまいります。</h2>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        <div class="slider-container fade-in-container <?php echo isset($classBannerUrl[$currentUrl]) ? esc_attr($classBannerUrl[$currentUrl]) : ''; ?>">
            <div class="header-slider fade-in-slider">
                <!-- Trình chiếu slider page đầu -->
                <div class="slide home-slide fade-in-slide"
                style=" object-position: center 0; "><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/slide pictureü@ç@.png"
                            alt="Slide 1" class="slide-image fade-in-image"/></div>
                <div class="slide home-slide fade-in-slide delay-1"><img
                style=" object-position: center 0; "
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/slide pictureü@çA.png"
                            alt="Slide 2" class="slide-image fade-in-image"/></div>
                <div class="slide home-slide fade-in-slide delay-2"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/slide pictureü@çB.png"
                            alt="Slide 3" class="slide-image fade-in-image"/></div>
                <div class="slide home-slide fade-in-slide delay-3"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/renew/slide1.png"
                            alt="Slide 4" class="slide-image fade-in-image"/></div>

                <!-- Hiển thị hình ảnh trình chiếu slider recruit -->
                <div class="slide inquiry-slide fade-in-slide"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/recruit image.png"
                            alt="Slide 5" class="slide-image fade-in-image"/></div>
                <div class="slide inquiry-slide fade-in-slide delay-1"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/recruitç@.jpg"
                            alt="Slide 6" class="slide-image fade-in-image"/></div>
                <div class="slide inquiry-slide fade-in-slide delay-2"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/recruitçA.jpg"
                            alt="Slide 7" class="slide-image fade-in-image"/></div>

                <!-- Hiển thị hình ảnh cố định theo url -->
                <div class="slide other-slide fade-in-slide" data-url="/business"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/Business details.png"
                            alt="Slide 8" class="slide-image fade-in-image"/></div>
                <div class="slide other-slide fade-in-slide" data-url="/introduce"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/Mexico Meet.jpg"
                            alt="Slide 9" class="slide-image fade-in-image"/></div>
                <div class="slide other-slide fade-in-slide" data-url="/privacy"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/privacyüòcopyright.jpg"
                            alt="Slide 10" class="slide-image fade-in-image"/></div>
                <div class="slide other-slide fade-in-slide" data-url="/inquiry"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/contact.jpg"
                            alt="Slide 11" class="slide-image fade-in-image"/></div>
                <div class="slide other-slide fade-in-slide" data-url="/about"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/company profile.jpg"
                            alt="Slide 12" class="slide-image fade-in-image"/></div>
            </div>
        </div>
    </div>
    <div>
        <script>
            jQuery(function ($) {
                var currentUrl = window.location.pathname.replace(/\/$/, '');

                // Highlight active menu link
                $('nav ul li a').each(function () {
                    var href = $(this).attr('href').replace(/\/$/, '');
                    if (href === currentUrl) {
                        $(this).addClass('active');
                    }
                });

                var slideMap = {
                    '/': [0, 1, 2, 3],
                    '': [0, 1, 2, 3],
                    // '/recruit': [4, 5, 6],
                    '/recruit': [4],
                    '/business': [7],
                    '/introduce': [8],
                    '/sitepolicy': [9],
                    '/inquiry': [10],
                    '/about': [11],
                    '/privacy': [9]
                };

                var indices = slideMap.hasOwnProperty(currentUrl) ? slideMap[currentUrl] : [0];

                // Keep only relevant slides
                $('.header-slider .slide').each(function (index) {
                    if (!indices.includes(index)) {
                        $(this).remove();
                    }
                });

                // Now only relevant slides are left, show the slider
                $('.header-slider').css('visibility', 'visible');

                // Initialize Slick after DOM is ready with correct slides
                $('.header-slider').slick({
                    autoplay: indices.length > 1,
                    autoplaySpeed: 3000,
                    speed: 700,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                    dots: true,
                    pauseOnHover: false,
                    pauseOnFocus: false,
                    fade: true,
                    cssEase: 'cubic-bezier(0.33, 1, 0.68, 1)',
                    useTransform: true,
                    useCSS: true
                });

                // Banner class map for styling
                var bannerClassMap = {
                    1: 'custom-slider-banner',
                    2: 'custom-slider-banner-hidden',
                    3: 'banner-title-bg'
                };

                $('.header-slider').on('beforeChange', function (event, slick, currentSlide, nextSlide) {
                    var $bannerTitle = $('.banner-title');
                    $bannerTitle.removeClass('custom-slider-banner custom-slider-banner-hidden banner-title-bg');
                    if (bannerClassMap[nextSlide]) {
                        $bannerTitle.addClass(bannerClassMap[nextSlide]);
                    }
                });

                // Smooth FadeIn Animation System
                function initFadeInAnimations() {
                    // Intersection Observer for scroll animations
                    const observerOptions = {
                        threshold: 0.1,
                        rootMargin: '0px 0px -50px 0px'
                    };

                    const observer = new IntersectionObserver(function(entries) {
                        entries.forEach(function(entry) {
                            if (entry.isIntersecting) {
                                entry.target.classList.add('is-visible');
                                observer.unobserve(entry.target);
                            }
                        });
                    }, observerOptions);

                    // Observe all fade-in elements
                    $('.fade-in, .fade-in-slide').each(function() {
                        observer.observe(this);
                    });
                }

                // Slider Image Load Animation
                function initSliderImageAnimations() {
                    $('.slide-image.fade-in-image').each(function() {
                        const img = this;
                        if (img.complete) {
                            $(img).addClass('is-loaded');
                        } else {
                            $(img).on('load', function() {
                                $(this).addClass('is-loaded');
                            });
                        }
                    });
                }

                // Initialize animations when page is ready
                $(document).ready(function() {
                    initFadeInAnimations();
                    initSliderImageAnimations();

                    // Add fade-in class to elements that should animate on scroll
                    $('main section, .content-block, .card, .feature').addClass('fade-in');
                });

                // Page load animations
                $(window).on('load', function() {
                    // Remove initial visibility hidden and trigger animations
                    $('.header-slider').css('visibility', 'visible');

                    // Stagger animation for multiple elements
                    $('.banner-title h2').each(function(index) {
                        $(this).css('animation-delay', (index * 0.2) + 's');
                    });

                    // Trigger slide animations after slider is initialized
                    setTimeout(function() {
                        $('.slick-slide.slick-active .fade-in-slide').addClass('is-visible');
                        $('.slick-slide.slick-active .slide-image').addClass('is-loaded');
                    }, 500);
                });

                // Handle slide change animations
                $('.header-slider').on('afterChange', function(event, slick, currentSlide) {
                    // Reset all slides
                    $('.fade-in-slide').removeClass('is-visible');
                    $('.slide-image').removeClass('is-loaded');

                    // Animate current slide
                    setTimeout(function() {
                        $('.slick-slide.slick-active .fade-in-slide').addClass('is-visible');
                        $('.slick-slide.slick-active .slide-image').addClass('is-loaded');
                    }, 100);
                });
            });
        </script>

        <style>
            .custom-slider-banner {
                width: 100%;
                display: grid;
                justify-content: end;
            }

            .custom-slider-banner h2 {
                text-align: right !important;
            }

            .custom-slider-banner-hidden {
                display: none;
            }

            .banner-title-bg {
                /* background-color: #9aa7b447; */
            }

            .header-slider {
                visibility: hidden;
            }
            .banner-title-img {
                width: 50%;
            }

            /* Smooth FadeIn Animation System */
            .fade-in {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.7s cubic-bezier(0.33, 1, 0.68, 1);
                will-change: opacity, transform;
            }

            .fade-in.is-visible {
                opacity: 1;
                transform: translateY(0);
            }

            /* Delay variations for staggered animations */
            .fade-in.delay-1 { transition-delay: 0.1s; }
            .fade-in.delay-2 { transition-delay: 0.2s; }
            .fade-in.delay-3 { transition-delay: 0.3s; }
            .fade-in.delay-4 { transition-delay: 0.4s; }
            .fade-in.delay-5 { transition-delay: 0.5s; }

            /* Custom fadeIn animation for slides */
            .header-slider .slick-slide {
                opacity: 0;
                transition: opacity 0.7s cubic-bezier(0.33, 1, 0.68, 1);
                will-change: opacity;
            }

            .header-slider .slick-slide.slick-active {
                opacity: 1;
            }

            .header-slider .slick-slide.slick-current {
                opacity: 1;
            }

            /* Page load animations */
            .banner-title {
                opacity: 0;
                transform: translateY(20px);
                animation: fadeInUp 1s cubic-bezier(0.33, 1, 0.68, 1) 0.3s forwards;
            }

            /* Slider Container FadeIn */
            .fade-in-container {
                opacity: 0;
                transform: scale(0.95);
                animation: fadeInScale 1.2s cubic-bezier(0.33, 1, 0.68, 1) 0.2s forwards;
            }

            .fade-in-slider {
                opacity: 0;
                animation: fadeIn 1s cubic-bezier(0.33, 1, 0.68, 1) 0.4s forwards;
            }

            /* Individual Slide FadeIn */
            .fade-in-slide {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.8s cubic-bezier(0.33, 1, 0.68, 1);
            }

            .fade-in-slide.is-visible {
                opacity: 1;
                transform: translateY(0);
            }

               .slick-dotted.slick-slider {
                    margin-bottom: 0 !important;
                }

            /* Slide Images FadeIn */
            .slide-image.fade-in-image {
                opacity: 0;
                transform: scale(1.05);
                transition: all 1s cubic-bezier(0.33, 1, 0.68, 1);
            }

            .slide-image.fade-in-image.is-loaded {
                opacity: 1;
                transform: scale(1);
            }

            /* Active slide animation */
            .slick-slide.slick-active .fade-in-slide {
                opacity: 1;
                transform: translateY(0);
            }

            .slick-slide.slick-active .slide-image {
                opacity: 1;
                transform: scale(1);
            }

            @keyframes fadeIn {
                0% {
                    opacity: 0;
                }
                100% {
                    opacity: 1;
                }
            }

            @keyframes fadeInUp {
                0% {
                    opacity: 0;
                    transform: translateY(20px);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes fadeInScale {
                0% {
                    opacity: 0;
                    transform: scale(0.95);
                }
                100% {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            /* .header-slider .slide img {
                 width: 100%;
                 height: auto;
                 display: block;
                 max-width: 100%;
                 object-fit: cover;
            } */

            @media screen and (max-width: 640px) {
                .header-slider .slide img {
                    width: 100%;
                    height: auto;
                    display: block;
                    max-width: 100%;
                    object-fit: cover;
                }

                .slick-dots {
                    bottom: 26rem !important;
                }

                .slider-container {
                    height: 18rem;
                }

                
                .slick-dotted.slick-slider {
                    margin-bottom: 0 !important;
                }

                .slick-slider .slick-track,
                .slick-slider .slick-list {
                    position: relative;
                    /*top: 40px;*/
                    margin-bottom: 0;
                }

                .custom-slider-banner h2{
                    width: 100%;
                    display: table-caption;
                    justify-content: end;

                }
                .site-policy .title h3{
                        color: #212121;
                }
               /* Apply max-height only on homepage */
               body.home .slider-container {
                max-height: 44rem;
               }

                  .banner-title-img {
                    width: 100%;
                }

                .banner-title h2:nth-child(1) {
                    margin-bottom: -2.5rem;
                }

                /* Mobile optimizations for fade animations */
                .fade-in {
                    transform: translateY(20px);
                }

                .banner-title {
                    animation-duration: 0.8s;
                }

                .header-slider {
                    animation-duration: 1s;
                }

            }
        </style>
    </div>
</div>
</body>

</html>