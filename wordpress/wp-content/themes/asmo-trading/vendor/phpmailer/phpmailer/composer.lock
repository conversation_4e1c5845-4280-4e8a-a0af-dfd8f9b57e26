{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "ca5abc72444d9608a35c39f9064c139b", "content-hash": "8b66ed71ae9ca8cd0258c814615d624f", "packages": [], "packages-dev": [{"name": "cilex/cilex", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Cilex/Cilex.git", "reference": "7acd965a609a56d0345e8b6071c261fbdb926cb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Cilex/Cilex/zipball/7acd965a609a56d0345e8b6071c261fbdb926cb5", "reference": "7acd965a609a56d0345e8b6071c261fbdb926cb5", "shasum": ""}, "require": {"cilex/console-service-provider": "1.*", "php": ">=5.3.3", "pimple/pimple": "~1.0", "symfony/finder": "~2.1", "symfony/process": "~2.1"}, "require-dev": {"phpunit/phpunit": "3.7.*", "symfony/validator": "~2.1"}, "suggest": {"monolog/monolog": ">=1.0.0", "symfony/validator": ">=1.0.0", "symfony/yaml": ">=1.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Cilex": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PHP micro-framework for Command line tools based on the Symfony2 Components", "homepage": "http://cilex.github.com", "keywords": ["cli", "microframework"], "time": "2014-03-29 14:03:13"}, {"name": "cilex/console-service-provider", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/Cilex/console-service-provider.git", "reference": "25ee3d1875243d38e1a3448ff94bdf944f70d24e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Cilex/console-service-provider/zipball/25ee3d1875243d38e1a3448ff94bdf944f70d24e", "reference": "25ee3d1875243d38e1a3448ff94bdf944f70d24e", "shasum": ""}, "require": {"php": ">=5.3.3", "pimple/pimple": "1.*@dev", "symfony/console": "~2.1"}, "require-dev": {"cilex/cilex": "1.*@dev", "silex/silex": "1.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Cilex\\Provider\\Console": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Console Service Provider", "keywords": ["cilex", "console", "pimple", "service-provider", "silex"], "time": "2012-12-19 10:50:58"}, {"name": "container-interop/container-interop", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/container-interop/container-interop.git", "reference": "fc08354828f8fd3245f77a66b9e23a6bca48297e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/container-interop/container-interop/zipball/fc08354828f8fd3245f77a66b9e23a6bca48297e", "reference": "fc08354828f8fd3245f77a66b9e23a6bca48297e", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"Interop\\Container\\": "src/Interop/Container/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Promoting the interoperability of container objects (DIC, SL, etc.)", "time": "2014-12-30 15:22:37"}, {"name": "doctrine/annotations", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/f25c8aab83e0c3e976fd7d19875f198ccf2f7535", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "php": ">=5.3.2"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Annotations\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2015-08-31 12:32:49"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14 21:17:01"}, {"name": "doctrine/lexer", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/83893c552fd2045dd78aef794c31e694c37c0b8c", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Lexer\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Base library for a lexer that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "http://www.doctrine-project.org", "keywords": ["lexer", "parser"], "time": "2014-09-09 13:34:57"}, {"name": "erusev/parsedown", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/erusev/parsedown.git", "reference": "3ebbd730b5c2cf5ce78bc1bf64071407fc6674b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/3ebbd730b5c2cf5ce78bc1bf64071407fc6674b7", "reference": "3ebbd730b5c2cf5ce78bc1bf64071407fc6674b7", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "time": "2015-10-04 16:44:32"}, {"name": "herrera-io/json", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/php-json.git", "reference": "60c696c9370a1e5136816ca557c17f82a6fa83f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/php-json/zipball/60c696c9370a1e5136816ca557c17f82a6fa83f1", "reference": "60c696c9370a1e5136816ca557c17f82a6fa83f1", "shasum": ""}, "require": {"ext-json": "*", "justinrainbow/json-schema": ">=1.0,<2.0-dev", "php": ">=5.3.3", "seld/jsonlint": ">=1.0,<2.0-dev"}, "require-dev": {"herrera-io/phpunit-test-case": "1.*", "mikey179/vfsstream": "1.1.0", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/lib/json_version.php"], "psr-0": {"Herrera\\Json": "src/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io/", "role": "Developer"}], "description": "A library for simplifying JSON linting and validation.", "homepage": "http://herrera-io.github.com/php-json", "keywords": ["json", "lint", "schema", "validate"], "time": "2013-10-30 16:51:34"}, {"name": "herrera-io/phar-update", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/php-phar-update.git", "reference": "00a79e1d5b8cf3c080a2e3becf1ddf7a7fea025b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/php-phar-update/zipball/00a79e1d5b8cf3c080a2e3becf1ddf7a7fea025b", "reference": "00a79e1d5b8cf3c080a2e3becf1ddf7a7fea025b", "shasum": ""}, "require": {"herrera-io/json": "1.*", "kherge/version": "1.*", "php": ">=5.3.3"}, "require-dev": {"herrera-io/phpunit-test-case": "1.*", "mikey179/vfsstream": "1.1.0", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/lib/constants.php"], "psr-0": {"Herrera\\Phar\\Update": "src/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io/", "role": "Developer"}], "description": "A library for self-updating <PERSON><PERSON>.", "homepage": "http://herrera-io.github.com/php-phar-update", "keywords": ["phar", "update"], "time": "2013-10-30 17:23:01"}, {"name": "jms/metadata", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/metadata.git", "reference": "22b72455559a25777cfd28c4ffda81ff7639f353"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/metadata/zipball/22b72455559a25777cfd28c4ffda81ff7639f353", "reference": "22b72455559a25777cfd28c4ffda81ff7639f353", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"doctrine/cache": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-0": {"Metadata\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "description": "Class/method/property metadata management in PHP", "keywords": ["annotations", "metadata", "xml", "yaml"], "time": "2014-07-12 07:13:19"}, {"name": "jms/parser-lib", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/parser-lib.git", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/parser-lib/zipball/c509473bc1b4866415627af0e1c6cc8ac97fa51d", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d", "shasum": ""}, "require": {"phpoption/phpoption": ">=0.9,<2.0-dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"JMS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "description": "A library for easily creating recursive-descent parsers.", "time": "2012-11-18 18:08:43"}, {"name": "jms/serializer", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/serializer.git", "reference": "fe13a1f993ea3456e195b7820692f2eb2b6bbb48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/serializer/zipball/fe13a1f993ea3456e195b7820692f2eb2b6bbb48", "reference": "fe13a1f993ea3456e195b7820692f2eb2b6bbb48", "shasum": ""}, "require": {"doctrine/annotations": "1.*", "doctrine/instantiator": "~1.0.3", "jms/metadata": "~1.1", "jms/parser-lib": "1.*", "php": ">=5.4.0", "phpcollection/phpcollection": "~0.1"}, "conflict": {"twig/twig": "<1.12"}, "require-dev": {"doctrine/orm": "~2.1", "doctrine/phpcr-odm": "~1.0.1", "jackalope/jackalope-doctrine-dbal": "1.0.*", "phpunit/phpunit": "~4.0", "propel/propel1": "~1.7", "symfony/filesystem": "2.*", "symfony/form": "~2.1", "symfony/translation": "~2.0", "symfony/validator": "~2.0", "symfony/yaml": "2.*", "twig/twig": "~1.12|~2.0"}, "suggest": {"symfony/yaml": "Required if you'd like to serialize data to YAML format."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-0": {"JMS\\Serializer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Library for (de-)serializing data of any complexity; supports XML, JSON, and YAML.", "homepage": "http://jmsyst.com/libs/serializer", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "time": "2015-10-27 09:24:41"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "cc84765fb7317f6b07bd8ac78364747f95b86341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/cc84765fb7317f6b07bd8ac78364747f95b86341", "reference": "cc84765fb7317f6b07bd8ac78364747f95b86341", "shasum": ""}, "require": {"php": ">=5.3.29"}, "require-dev": {"json-schema/json-schema-test-suite": "1.1.0", "phpdocumentor/phpdocumentor": "~2", "phpunit/phpunit": "~3.7"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2016-01-25 15:43:01"}, {"name": "kherge/version", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/Version.git", "reference": "f07cf83f8ce533be8f93d2893d96d674bbeb7e30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/Version/zipball/f07cf83f8ce533be8f93d2893d96d674bbeb7e30", "reference": "f07cf83f8ce533be8f93d2893d96d674bbeb7e30", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"KevinGH\\Version": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.kevingh.com/"}], "description": "A parsing and comparison library for semantic versioning.", "homepage": "http://github.com/kherge/Version", "time": "2012-08-16 17:13:03"}, {"name": "monolog/monolog", "version": "1.19.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "5f56ed5212dc509c8dc8caeba2715732abb32dbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/5f56ed5212dc509c8dc8caeba2715732abb32dbf", "reference": "5f56ed5212dc509c8dc8caeba2715732abb32dbf", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "raven/raven": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "~5.3"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "raven/raven": "Allow sending log messages to a Sentry server", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2016-04-12 18:29:35"}, {"name": "nikic/php-parser", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "f78af2c9c86107aa1a34cd1dbb5bbe9eeb0d9f51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/f78af2c9c86107aa1a34cd1dbb5bbe9eeb0d9f51", "reference": "f78af2c9c86107aa1a34cd1dbb5bbe9eeb0d9f51", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"files": ["lib/bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2015-09-19 14:15:08"}, {"name": "phpcollection/phpcollection", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-collection.git", "reference": "b8bf55a0a929ca43b01232b36719f176f86c7e83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-collection/zipball/b8bf55a0a929ca43b01232b36719f176f86c7e83", "reference": "b8bf55a0a929ca43b01232b36719f176f86c7e83", "shasum": ""}, "require": {"phpoption/phpoption": "1.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "autoload": {"psr-0": {"PhpCollection": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "http://jmsyst.com", "role": "Developer of wrapped JMSSerializerBundle"}], "description": "General-Purpose Collection Library for PHP", "keywords": ["collection", "list", "map", "sequence", "set"], "time": "2014-03-11 13:46:42"}, {"name": "phpdocumentor/fileset", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/Fileset.git", "reference": "bfa78d8fa9763dfce6d0e5d3730c1d8ab25d34b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/Fileset/zipball/bfa78d8fa9763dfce6d0e5d3730c1d8ab25d34b0", "reference": "bfa78d8fa9763dfce6d0e5d3730c1d8ab25d34b0", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/finder": "~2.1"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Fileset component for collecting a set of files given directories and file paths", "homepage": "http://www.phpdoc.org", "keywords": ["files", "fileset", "phpdoc"], "time": "2013-08-06 21:07:42"}, {"name": "phpdocumentor/graphviz", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/GraphViz.git", "reference": "a906a90a9f230535f25ea31caf81b2323956283f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/GraphViz/zipball/a906a90a9f230535f25ea31caf81b2323956283f", "reference": "a906a90a9f230535f25ea31caf81b2323956283f", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2016-02-02 13:00:08"}, {"name": "phpdocumentor/phpdocumentor", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/phpDocumentor2.git", "reference": "be607da0eef9b9249c43c5b4820d25d631c73667"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/phpDocumentor2/zipball/be607da0eef9b9249c43c5b4820d25d631c73667", "reference": "be607da0eef9b9249c43c5b4820d25d631c73667", "shasum": ""}, "require": {"cilex/cilex": "~1.0", "erusev/parsedown": "~1.0", "herrera-io/phar-update": "1.0.3", "jms/serializer": ">=0.12", "monolog/monolog": "~1.6", "php": ">=5.3.3", "phpdocumentor/fileset": "~1.0", "phpdocumentor/graphviz": "~1.0", "phpdocumentor/reflection": "^3.0", "phpdocumentor/reflection-docblock": "~2.0", "symfony/config": "~2.3", "symfony/console": "~2.3", "symfony/event-dispatcher": "~2.1", "symfony/process": "~2.0", "symfony/stopwatch": "~2.3", "symfony/validator": "~2.2", "twig/twig": "~1.3", "zendframework/zend-cache": "~2.1", "zendframework/zend-config": "~2.1", "zendframework/zend-filter": "~2.1", "zendframework/zend-i18n": "~2.1", "zendframework/zend-serializer": "~2.1", "zendframework/zend-servicemanager": "~2.1", "zendframework/zend-stdlib": "~2.1", "zetacomponents/document": ">=1.3.1"}, "require-dev": {"behat/behat": "~3.0", "mikey179/vfsstream": "~1.2", "mockery/mockery": "~0.9@dev", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.4", "symfony/expression-language": "~2.4"}, "suggest": {"ext-twig": "Enabling the twig extension improves the generation of twig based templates.", "ext-xslcache": "Enabling the XSLCache extension improves the generation of xml based templates."}, "bin": ["bin/phpdoc.php", "bin/phpdoc"], "type": "library", "extra": {"branch-alias": {"dev-develop": "2.9-dev"}}, "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit/"], "Cilex\\Provider": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Documentation Generator for PHP", "homepage": "http://www.phpdoc.org", "keywords": ["api", "application", "dga", "documentation", "phpdoc"], "time": "2016-05-22 09:50:56"}, {"name": "phpdocumentor/reflection", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/Reflection.git", "reference": "793bfd92d9a0fc96ae9608fb3e947c3f59fb3a0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/Reflection/zipball/793bfd92d9a0fc96ae9608fb3e947c3f59fb3a0d", "reference": "793bfd92d9a0fc96ae9608fb3e947c3f59fb3a0d", "shasum": ""}, "require": {"nikic/php-parser": "^1.0", "php": ">=5.3.3", "phpdocumentor/reflection-docblock": "~2.0", "psr/log": "~1.0"}, "require-dev": {"behat/behat": "~2.4", "mockery/mockery": "~0.8", "phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit/", "tests/mocks/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Reflection library to do Static Analysis for PHP Projects", "homepage": "http://www.phpdoc.org", "keywords": ["phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2016-05-21 08:42:32"}, {"name": "phpdocumentor/reflection-docblock", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "d68dbdc53dc358a816f00b300704702b2eaff7b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/d68dbdc53dc358a816f00b300704702b2eaff7b8", "reference": "d68dbdc53dc358a816f00b300704702b2eaff7b8", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"phpDocumentor": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2015-02-03 12:10:50"}, {"name": "phpoption/phpoption", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/94e644f7d2051a5f0fcf77d81605f152eecff0ed", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-0": {"PhpOption\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2015-07-25 16:39:46"}, {"name": "phpspec/prophecy", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "58a8137754bc24b25740d4281399a4a3596058e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/58a8137754bc24b25740d4281399a4a3596058e0", "reference": "58a8137754bc24b25740d4281399a4a3596058e0", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2", "sebastian/comparator": "^1.1", "sebastian/recursion-context": "^1.0"}, "require-dev": {"phpspec/phpspec": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2016-06-07 08:13:47"}, {"name": "phpunit/php-code-coverage", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/eabf68b476ac7d0f73793aada060f1c1a9bf8979", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-text-template": "~1.2", "phpunit/php-token-stream": "~1.3", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0"}, "require-dev": {"ext-xdebug": ">=2.1.4", "phpunit/phpunit": "~4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1", "ext-xmlwriter": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2015-10-06 15:47:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2015-06-21 13:08:43"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21 13:50:34"}, {"name": "phpunit/php-timer", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "38e9124049cf1a164f1e4537caf19c99bf1eb260"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/38e9124049cf1a164f1e4537caf19c99bf1eb260", "reference": "38e9124049cf1a164f1e4537caf19c99bf1eb260", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4|~5"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2016-05-12 18:03:57"}, {"name": "phpunit/php-token-stream", "version": "1.4.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da", "reference": "3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2015-09-15 10:49:45"}, {"name": "phpunit/phpunit", "version": "4.7.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "9b97f9d807b862c2de2a36e86690000801c85724"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/9b97f9d807b862c2de2a36e86690000801c85724", "reference": "9b97f9d807b862c2de2a36e86690000801c85724", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpspec/prophecy": "~1.3,>=1.3.1", "phpunit/php-code-coverage": "~2.1", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": ">=1.0.6", "phpunit/phpunit-mock-objects": "~2.3", "sebastian/comparator": "~1.1", "sebastian/diff": "~1.2", "sebastian/environment": "~1.2", "sebastian/exporter": "~1.2", "sebastian/global-state": "~1.0", "sebastian/version": "~1.0", "symfony/yaml": "~2.1|~3.0"}, "suggest": {"phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.7.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2015-07-13 11:28:34"}, {"name": "phpunit/phpunit-mock-objects", "version": "2.3.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/ac8e7a3db35738d56ee9a76e78a4e03d97628983", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": ">=5.3.3", "phpunit/php-text-template": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2015-10-02 06:51:40"}, {"name": "pimple/pimple", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "2019c145fe393923f3441b23f29bbdfaa5c58c4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/2019c145fe393923f3441b23f29bbdfaa5c58c4d", "reference": "2019c145fe393923f3441b23f29bbdfaa5c58c4d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Pimple": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "Pimple is a simple Dependency Injection Container for PHP 5.3", "homepage": "http://pimple.sensiolabs.org", "keywords": ["container", "dependency injection"], "time": "2013-11-22 08:30:29"}, {"name": "psr/log", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe0936ee26643249e916849d48e3a51d5f5e278b", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Psr\\Log\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "keywords": ["log", "psr", "psr-3"], "time": "2012-12-21 11:40:51"}, {"name": "sebastian/comparator", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/937efb279bd37a375bcadf584dec0726f84dbf22", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2015-07-26 15:48:44"}, {"name": "sebastian/diff", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/13edfd8706462032c2f52b4b862974dd46b71c9e", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2015-12-08 07:14:41"}, {"name": "sebastian/environment", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "4e8f0da10ac5802913afc151413bc8c53b6c2716"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/environment/zipball/4e8f0da10ac5802913afc151413bc8c53b6c2716", "reference": "4e8f0da10ac5802913afc151413bc8c53b6c2716", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-05-17 03:18:57"}, {"name": "sebastian/exporter", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/42c4c2eec485ee3e159ec9884f95b431287edde4", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~1.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-06-17 09:04:28"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12 03:26:01"}, {"name": "sebastian/recursion-context", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "913401df809e99e4f47b27cdd781f4a258d58791"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/913401df809e99e4f47b27cdd781f4a258d58791", "reference": "913401df809e99e4f47b27cdd781f4a258d58791", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2015-11-11 19:50:13"}, {"name": "sebastian/version", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "shasum": ""}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2015-06-21 13:59:46"}, {"name": "seld/jsonlint", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "66834d3e3566bb5798db7294619388786ae99394"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/66834d3e3566bb5798db7294619388786ae99394", "reference": "66834d3e3566bb5798db7294619388786ae99394", "shasum": ""}, "require": {"php": "^5.3 || ^7.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2015-11-21 02:21:41"}, {"name": "symfony/config", "version": "v2.8.7", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "a2edd59c2163c65747fc3f35d132b5a39266bd05"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/a2edd59c2163c65747fc3f35d132b5a39266bd05", "reference": "a2edd59c2163c65747fc3f35d132b5a39266bd05", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/filesystem": "~2.3|~3.0.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2016-06-06 11:11:27"}, {"name": "symfony/console", "version": "v2.8.7", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "5ac8bc9aa77bb2edf06af3a1bb6bc1020d23acd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/5ac8bc9aa77bb2edf06af3a1bb6bc1020d23acd3", "reference": "5ac8bc9aa77bb2edf06af3a1bb6bc1020d23acd3", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/event-dispatcher": "~2.1|~3.0.0", "symfony/process": "~2.1|~3.0.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2016-06-06 15:06:25"}, {"name": "symfony/event-dispatcher", "version": "v2.8.7", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "2a6b8713f8bdb582058cfda463527f195b066110"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2a6b8713f8bdb582058cfda463527f195b066110", "reference": "2a6b8713f8bdb582058cfda463527f195b066110", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.0,>=2.0.5|~3.0.0", "symfony/dependency-injection": "~2.6|~3.0.0", "symfony/expression-language": "~2.6|~3.0.0", "symfony/stopwatch": "~2.3|~3.0.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2016-06-06 11:11:27"}, {"name": "symfony/filesystem", "version": "v3.0.7", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "74fec3511b62cb934b64bce1d96f06fffa4beafd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/74fec3511b62cb934b64bce1d96f06fffa4beafd", "reference": "74fec3511b62cb934b64bce1d96f06fffa4beafd", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2016-04-12 18:09:53"}, {"name": "symfony/finder", "version": "v2.8.7", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "3ec095fab1800222732ca522a95dce8fa124007b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/3ec095fab1800222732ca522a95dce8fa124007b", "reference": "3ec095fab1800222732ca522a95dce8fa124007b", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2016-06-06 11:11:27"}, {"name": "symfony/polyfill-mbstring", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "dff51f72b0706335131b00a7f49606168c582594"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/dff51f72b0706335131b00a7f49606168c582594", "reference": "dff51f72b0706335131b00a7f49606168c582594", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2016-05-18 14:26:46"}, {"name": "symfony/process", "version": "v2.8.7", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "115347d00c342198cdc52a7bd8bc15b5ab43500c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/115347d00c342198cdc52a7bd8bc15b5ab43500c", "reference": "115347d00c342198cdc52a7bd8bc15b5ab43500c", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2016-06-06 11:11:27"}, {"name": "symfony/stopwatch", "version": "v2.8.7", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "5e628055488bcc42dbace3af65be435d094e37e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/5e628055488bcc42dbace3af65be435d094e37e4", "reference": "5e628055488bcc42dbace3af65be435d094e37e4", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2016-06-06 11:11:27"}, {"name": "symfony/translation", "version": "v3.0.7", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "2b0aacaa613c0ec1ad8046f972d8abdcb19c1db7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/2b0aacaa613c0ec1ad8046f972d8abdcb19c1db7", "reference": "2b0aacaa613c0ec1ad8046f972d8abdcb19c1db7", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.8"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0", "symfony/intl": "~2.8|~3.0", "symfony/yaml": "~2.8|~3.0"}, "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2016-06-06 11:33:26"}, {"name": "symfony/validator", "version": "v2.8.7", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "4c8f9fd8e2150dbc4745ef13378e690588365df0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/4c8f9fd8e2150dbc4745ef13378e690588365df0", "reference": "4c8f9fd8e2150dbc4745ef13378e690588365df0", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0", "symfony/translation": "~2.4|~3.0.0"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "egulias/email-validator": "~1.2,>=1.2.1", "symfony/config": "~2.2|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "symfony/http-foundation": "~2.1|~3.0.0", "symfony/intl": "~2.7.4|~2.8|~3.0.0", "symfony/property-access": "~2.3|~3.0.0", "symfony/yaml": "~2.0,>=2.0.5|~3.0.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "egulias/email-validator": "Strict (RFC compliant) email validation", "symfony/config": "", "symfony/expression-language": "For using the 2.4 Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For using the 2.4 Validator API", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "time": "2016-04-14 08:48:44"}, {"name": "symfony/yaml", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "c5a7e7fc273c758b92b85dcb9c46149ccda89623"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/c5a7e7fc273c758b92b85dcb9c46149ccda89623", "reference": "c5a7e7fc273c758b92b85dcb9c46149ccda89623", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2016-06-14 11:18:07"}, {"name": "twig/twig", "version": "v1.24.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "3566d311a92aae4deec6e48682dc5a4528c4a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/3566d311a92aae4deec6e48682dc5a4528c4a512", "reference": "3566d311a92aae4deec6e48682dc5a4528c4a512", "shasum": ""}, "require": {"php": ">=5.2.7"}, "require-dev": {"symfony/debug": "~2.7", "symfony/phpunit-bridge": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.24-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}, {"name": "Twig Team", "homepage": "http://twig.sensiolabs.org/contributors", "role": "Contributors"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "http://twig.sensiolabs.org", "keywords": ["templating"], "time": "2016-05-30 09:11:59"}, {"name": "zendframework/zend-cache", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-cache.git", "reference": "2c68def8f96ce842d2f2a9a69e2f3508c2f5312d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-cache/zipball/2c68def8f96ce842d2f2a9a69e2f3508c2f5312d", "reference": "2c68def8f96ce842d2f2a9a69e2f3508c2f5312d", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-eventmanager": "^2.6.2 || ^3.0", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpbench/phpbench": "^0.10.0", "phpunit/phpunit": "^4.5", "zendframework/zend-serializer": "^2.6", "zendframework/zend-session": "^2.6.2"}, "suggest": {"ext-apc": "APC or compatible extension, to use the APC storage adapter", "ext-apcu": "APCU >= 5.1.0, to use the APCu storage adapter", "ext-dba": "DBA, to use the DBA storage adapter", "ext-memcache": "Memcache >= 2.0.0 to use the Memcache storage adapter", "ext-memcached": "Memcached >= 1.0.0 to use the Memcached storage adapter", "ext-mongo": "Mongo, to use MongoDb storage adapter", "ext-redis": "Redis, to use Redis storage adapter", "ext-wincache": "WinCache, to use the WinCache storage adapter", "ext-xcache": "XCache, to use the XCache storage adapter", "mongofill/mongofill": "Alternative to ext-mongo - a pure PHP implementation designed as a drop in replacement", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-session": "Zend\\Session component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev", "dev-develop": "2.8-dev"}, "zf": {"component": "Zend\\Cache", "config-provider": "Zend\\Cache\\ConfigProvider"}}, "autoload": {"psr-4": {"Zend\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides a generic way to cache any data", "homepage": "https://github.com/zendframework/zend-cache", "keywords": ["cache", "zf2"], "time": "2016-05-12 21:47:55"}, {"name": "zendframework/zend-config", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/zendframework/zend-config.git", "reference": "2920e877a9f6dca9fa8f6bd3b1ffc2e19bb1e30d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-config/zipball/2920e877a9f6dca9fa8f6bd3b1ffc2e19bb1e30d", "reference": "2920e877a9f6dca9fa8f6bd3b1ffc2e19bb1e30d", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-filter": "^2.6", "zendframework/zend-i18n": "^2.5", "zendframework/zend-json": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3"}, "suggest": {"zendframework/zend-filter": "Zend\\Filter component", "zendframework/zend-i18n": "Zend\\I18n component", "zendframework/zend-json": "Zend\\Json to use the Json reader or writer classes", "zendframework/zend-servicemanager": "Zend\\ServiceManager for use with the Config Factory to retrieve reader and writer instances"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev", "dev-develop": "2.7-dev"}}, "autoload": {"psr-4": {"Zend\\Config\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides a nested object property based user interface for accessing this configuration data within application code", "homepage": "https://github.com/zendframework/zend-config", "keywords": ["config", "zf2"], "time": "2016-02-04 23:01:10"}, {"name": "zendframework/zend-eventmanager", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-eventmanager.git", "reference": "5c80bdee0e952be112dcec0968bad770082c3a6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-eventmanager/zipball/5c80bdee0e952be112dcec0968bad770082c3a6e", "reference": "5c80bdee0e952be112dcec0968bad770082c3a6e", "shasum": ""}, "require": {"php": "^5.5 || ^7.0"}, "require-dev": {"athletic/athletic": "^0.1", "container-interop/container-interop": "^1.1.0", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.0", "zendframework/zend-stdlib": "^2.7.3 || ^3.0"}, "suggest": {"container-interop/container-interop": "^1.1.0, to use the lazy listeners feature", "zendframework/zend-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev", "dev-develop": "3.1-dev"}}, "autoload": {"psr-4": {"Zend\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://github.com/zendframework/zend-eventmanager", "keywords": ["event", "eventmanager", "events", "zf2"], "time": "2016-02-18 20:53:00"}, {"name": "zendframework/zend-filter", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-filter.git", "reference": "84c50246428efb0a1e52868e162dab3e149d5b80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-filter/zipball/84c50246428efb0a1e52868e162dab3e149d5b80", "reference": "84c50246428efb0a1e52868e162dab3e149d5b80", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "pear/archive_tar": "^1.4", "phpunit/phpunit": "~4.0", "zendframework/zend-crypt": "^2.6", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-uri": "^2.5"}, "suggest": {"zendframework/zend-crypt": "Zend\\Crypt component, for encryption filters", "zendframework/zend-i18n": "Zend\\I18n component for filters depending on i18n functionality", "zendframework/zend-servicemanager": "Zend\\ServiceManager component, for using the filter chain functionality", "zendframework/zend-uri": "Zend\\Uri component, for the UriNormalize filter"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev", "dev-develop": "2.8-dev"}, "zf": {"component": "Zend\\Filter", "config-provider": "Zend\\Filter\\ConfigProvider"}}, "autoload": {"psr-4": {"Zend\\Filter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides a set of commonly needed data filters", "homepage": "https://github.com/zendframework/zend-filter", "keywords": ["filter", "zf2"], "time": "2016-04-18 18:32:43"}, {"name": "zendframework/zend-hydrator", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/zendframework/zend-hydrator.git", "reference": "22652e1661a5a10b3f564cf7824a2206cf5a4a65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-hydrator/zipball/22652e1661a5a10b3f564cf7824a2206cf5a4a65", "reference": "22652e1661a5a10b3f564cf7824a2206cf5a4a65", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.0@dev", "zendframework/zend-eventmanager": "^2.6.2 || ^3.0", "zendframework/zend-filter": "^2.6", "zendframework/zend-inputfilter": "^2.6", "zendframework/zend-serializer": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3"}, "suggest": {"zendframework/zend-eventmanager": "^2.6.2 || ^3.0, to support aggregate hydrator usage", "zendframework/zend-filter": "^2.6, to support naming strategy hydrator usage", "zendframework/zend-serializer": "^2.6.1, to use the SerializableStrategy", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3, to support hydrator plugin manager usage"}, "type": "library", "extra": {"branch-alias": {"dev-release-1.0": "1.0-dev", "dev-release-1.1": "1.1-dev", "dev-master": "2.0-dev", "dev-develop": "2.1-dev"}}, "autoload": {"psr-4": {"Zend\\Hydrator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-hydrator", "keywords": ["hydrator", "zf2"], "time": "2016-02-18 22:38:26"}, {"name": "zendframework/zend-i18n", "version": "2.7.3", "source": {"type": "git", "url": "https://github.com/zendframework/zend-i18n.git", "reference": "b2db0d8246a865c659f93199f90f5fc2cd2f3cd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-i18n/zipball/b2db0d8246a865c659f93199f90f5fc2cd2f3cd8", "reference": "b2db0d8246a865c659f93199f90f5fc2cd2f3cd8", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-cache": "^2.6.1", "zendframework/zend-config": "^2.6", "zendframework/zend-eventmanager": "^2.6.2 || ^3.0", "zendframework/zend-filter": "^2.6.1", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3", "zendframework/zend-validator": "^2.6", "zendframework/zend-view": "^2.6.3"}, "suggest": {"ext-intl": "Required for most features of Zend\\I18n; included in default builds of PHP", "zendframework/zend-cache": "Zend\\Cache component", "zendframework/zend-config": "Zend\\Config component", "zendframework/zend-eventmanager": "You should install this package to use the events in the translator", "zendframework/zend-filter": "You should install this package to use the provided filters", "zendframework/zend-i18n-resources": "Translation resources", "zendframework/zend-servicemanager": "Zend\\ServiceManager component", "zendframework/zend-validator": "You should install this package to use the provided validators", "zendframework/zend-view": "You should install this package to use the provided view helpers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev", "dev-develop": "2.8-dev"}, "zf": {"component": "Zend\\I18n", "config-provider": "Zend\\I18n\\ConfigProvider"}}, "autoload": {"psr-4": {"Zend\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-i18n", "keywords": ["i18n", "zf2"], "time": "2016-06-07 21:08:30"}, {"name": "zendframework/zend-json", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/zendframework/zend-json.git", "reference": "f42a1588e75c2a3e338cd94c37906231e616daab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-json/zipball/f42a1588e75c2a3e338cd94c37906231e616daab", "reference": "f42a1588e75c2a3e338cd94c37906231e616daab", "shasum": ""}, "require": {"php": "^5.5 || ^7.0"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "^2.3", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "suggest": {"zendframework/zend-json-server": "For implementing JSON-RPC servers", "zendframework/zend-xml2json": "For converting XML documents to JSON"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev", "dev-develop": "3.1-dev"}}, "autoload": {"psr-4": {"Zend\\Json\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides convenience methods for serializing native PHP to JSON and decoding JSON to native PHP", "homepage": "https://github.com/zendframework/zend-json", "keywords": ["json", "zf2"], "time": "2016-04-01 02:34:00"}, {"name": "zendframework/zend-serializer", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/zendframework/zend-serializer.git", "reference": "ff74ea020f5f90866eb28365327e9bc765a61a6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-serializer/zipball/ff74ea020f5f90866eb28365327e9bc765a61a6e", "reference": "ff74ea020f5f90866eb28365327e9bc765a61a6e", "shasum": ""}, "require": {"php": "^5.6 || ^7.0", "zendframework/zend-json": "^2.5 || ^3.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^4.5", "squizlabs/php_codesniffer": "^2.3.1", "zendframework/zend-math": "^2.6", "zendframework/zend-servicemanager": "^2.7.5 || ^3.0.3"}, "suggest": {"zendframework/zend-math": "(^2.6 || ^3.0) To support Python Pickle serialization", "zendframework/zend-servicemanager": "(^2.7.5 || ^3.0.3) To support plugin manager support"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev", "dev-develop": "2.9-dev"}, "zf": {"component": "Zend\\Serializer", "config-provider": "Zend\\Serializer\\ConfigProvider"}}, "autoload": {"psr-4": {"Zend\\Serializer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides an adapter based interface to simply generate storable representation of PHP types by different facilities, and recover", "homepage": "https://github.com/zendframework/zend-serializer", "keywords": ["serializer", "zf2"], "time": "2016-06-21 17:01:55"}, {"name": "zendframework/zend-servicemanager", "version": "2.7.6", "source": {"type": "git", "url": "https://github.com/zendframework/zend-servicemanager.git", "reference": "a6db4d13b9141fccce5dcb553df0295d6ad7d477"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-servicemanager/zipball/a6db4d13b9141fccce5dcb553df0295d6ad7d477", "reference": "a6db4d13b9141fccce5dcb553df0295d6ad7d477", "shasum": ""}, "require": {"container-interop/container-interop": "~1.0", "php": "^5.5 || ^7.0"}, "require-dev": {"athletic/athletic": "dev-master", "fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-di": "~2.5", "zendframework/zend-mvc": "~2.5"}, "suggest": {"ocramius/proxy-manager": "ProxyManager 0.5.* to handle lazy initialization of services", "zendframework/zend-di": "Zend\\Di component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev", "dev-develop": "3.0-dev"}}, "autoload": {"psr-4": {"Zend\\ServiceManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-servicemanager", "keywords": ["servicemanager", "zf2"], "time": "2016-04-27 19:07:40"}, {"name": "zendframework/zend-stdlib", "version": "2.7.7", "source": {"type": "git", "url": "https://github.com/zendframework/zend-stdlib.git", "reference": "0e44eb46788f65e09e077eb7f44d2659143bcc1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/0e44eb46788f65e09e077eb7f44d2659143bcc1f", "reference": "0e44eb46788f65e09e077eb7f44d2659143bcc1f", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "zendframework/zend-hydrator": "~1.1"}, "require-dev": {"athletic/athletic": "~0.1", "fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-config": "~2.5", "zendframework/zend-eventmanager": "~2.5", "zendframework/zend-filter": "~2.5", "zendframework/zend-inputfilter": "~2.5", "zendframework/zend-serializer": "~2.5", "zendframework/zend-servicemanager": "~2.5"}, "suggest": {"zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-filter": "To support naming strategy hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-servicemanager": "To support hydrator plugin manager usage"}, "type": "library", "extra": {"branch-alias": {"dev-release-2.7": "2.7-dev", "dev-master": "3.0-dev", "dev-develop": "3.1-dev"}}, "autoload": {"psr-4": {"Zend\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-stdlib", "keywords": ["stdlib", "zf2"], "time": "2016-04-12 21:17:31"}, {"name": "zetacomponents/base", "version": "1.9", "source": {"type": "git", "url": "https://github.com/zetacomponents/Base.git", "reference": "f20df24e8de3e48b6b69b2503f917e457281e687"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zetacomponents/Base/zipball/f20df24e8de3e48b6b69b2503f917e457281e687", "reference": "f20df24e8de3e48b6b69b2503f917e457281e687", "shasum": ""}, "require-dev": {"zetacomponents/unit-test": "*"}, "type": "library", "autoload": {"classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}], "description": "The Base package provides the basic infrastructure that all packages rely on. Therefore every component relies on this package.", "homepage": "https://github.com/zetacomponents", "time": "2014-09-19 03:28:34"}, {"name": "zetacomponents/document", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/zetacomponents/Document.git", "reference": "688abfde573cf3fe0730f82538fbd7aa9fc95bc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zetacomponents/Document/zipball/688abfde573cf3fe0730f82538fbd7aa9fc95bc8", "reference": "688abfde573cf3fe0730f82538fbd7aa9fc95bc8", "shasum": ""}, "require": {"zetacomponents/base": "*"}, "require-dev": {"zetacomponents/unit-test": "dev-master"}, "type": "library", "autoload": {"classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}], "description": "The Document components provides a general conversion framework for different semantic document markup languages like XHTML, Docbook, RST and similar.", "homepage": "https://github.com/zetacomponents", "time": "2013-12-19 11:40:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.0.0"}, "platform-dev": []}