[{"name": "phpmailer/phpmailer", "version": "v5.2.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "b18cb98131bd83103ccb26a888fdfe3177b8a663"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/b18cb98131bd83103ccb26a888fdfe3177b8a663", "reference": "b18cb98131bd83103ccb26a888fdfe3177b8a663", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpdocumentor/phpdocumentor": "*", "phpunit/phpunit": "4.7.*"}, "suggest": {"league/oauth2-google": "Needed for Google XOAUTH2 authentication"}, "time": "2017-01-09 09:33:47", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["class.phpmailer.php", "class.phpmaileroauth.php", "class.phpmaileroauthgoogle.php", "class.smtp.php", "class.pop3.php", "extras/EasyPeasyICS.php", "extras/ntlm_sasl_client.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP"}]