
<ul class="group_slide bxslider">
	<?php function isMobileDev(){
	    if(isset($_SERVER['HTTP_USER_AGENT']) and !empty($_SERVER['HTTP_USER_AGENT'])){
	       $user_ag = $_SERVER['HTTP_USER_AGENT'];
	       if(preg_match('/(Mobile|Android|Tablet|GoBrowser|[0-9]x[0-9]*|uZardWeb\/|Mini|Doris\/|Skyfire\/|iPhone|Fennec\/|Maemo|Iris\/|CLDC\-|Mobi\/)/uis',$user_ag)){
	          return true;
	       }else{
	          return false;
	       };
	    }else{
	       return false;
	    };
	} ?>
	<?php
	define ('UPLOADS_PATH', 'http://renewtest.asmo-trading.co.jp/wp-content/uploads/sites/27/');
	if($sliders){
		foreach($sliders as $key=>$value):
		   /**
		    * Begin: slides
		    * */
		   ?>
		   <?php if(!isMobileDev() && $key%2 !=0 ){ ?>
		   <li>
			  <?php
			   	 
			  	 $img_src = UPLOADS_PATH.$value['src_full'];
			  	 $img_alt = $value['alt'];
				 $link_id = $value['link_id'];
				 $link_class = $value['link_class'];
				 $link = $value['link'];
				 $link_title = $value['link_title'];
				 $link_rel = $value['link_rel'];
				 $link_target = $value['target'];
				 /**
				  * Begin: include link
				  * */
				 ?>
				 	<img src="<?= $img_src ?>" class="<?= $link_class ?>" alt="<?= $img_alt ?>">
			 <?php }else if(isMobileDev() && $key%2 ==0){?>
				 <li>
	  			  <?php
	  			  	 $img_src = UPLOADS_PATH.$value['src_full'];
	  			  	 $img_alt = $value['alt'];
	  				 $link_id = $value['link_id'];
	  				 $link_class = $value['link_class'];
	  				 $link = $value['link'];
	  				 $link_title = $value['link_title'];
	  				 $link_rel = $value['link_rel'];
	  				 $link_target = $value['target'];
	  				 /**
	  				  * Begin: include link
	  				  * */
	  				 ?>
					 <img src="<?= $img_src ?>" class="<?= $link_class ?>" alt="<?= $img_alt ?>">
			 <?php } ?>
		   </li>
	    <?php endforeach;
	}; ?>
</ul>
