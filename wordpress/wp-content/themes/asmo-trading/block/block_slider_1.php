<ul class="group_slide multiple-item">
	<?php
	if($sliders){
	    foreach($sliders as $key=>$value):
		   /**
		    * Begin: slides
		    * */
		   ?>
		   <li>
			  <?php
			  
			  $img_src = UPLOADS_PATH.$value['src_full'];
			  $img_alt = $value['alt'];
			  if($value['link']){
				 $link_id = $value['link_id'];
				 $link_class = $value['link_class'];
				 $link = $value['link'];
				 $link_title = $value['link_title'];
				 $link_rel = $value['link_rel'];
				 $link_target = $value['target'];
				 /**
				  * Begin: include link
				  * */
				 ?>
				 <a id="<?= $link_id ?>" class="<?= $link_class ?>" href="<?= $link ?>" title="<?= $link_title ?>" rel="<?= $link_rel ?>" target="<?= $link_target ?>">
					<img src="<?= $img_src ?>" alt="<?= $img_alt ?>">
				 </a>
			  <?php }else{
				 /**
				  * Begin: img only
				  * */
				 ?>
				 <img src="<?= $img_src ?>" alt="<?= $img_alt ?>">
			  <?php }?>
		   </li>
	    <?php endforeach;
	}else{
	    /**
	     * begin empty
	     * */
	    ?>

	<?php } ?>
</ul>
