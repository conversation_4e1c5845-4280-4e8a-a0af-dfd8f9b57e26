<div class="business__step--main">
    <div class="business__main">
        <div class="business__main--contents">
            <div class="business__main--dashed"></div>
            <div class="business__main--solid"></div>
            <div class="business__main--description">
                <div class="group-description">
                    <div class="description-left">
                        <img src="<?php echo esc_url($circle_image); ?>" alt="Step 1">
                    </div>
                    <div class="description-right">
                        <b><?php echo esc_html($headerTitle); ?></b><br>
                        <p><?php echo wp_kses_post(get_query_var('description')); ?>
</p>
                    </div>
                </div>
            </div>
            
            <div class="business__main--icon">
                <div class="business__main--icon-center">
                    <div class="business__main--icon-description">
                        <p class="text-stemp">STEP</p>
                        <p><?php echo esc_html($stepNumber); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .business__step--main {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #FFFFFF;
        padding: 20px;
    }

    .business__step--main>.business__main {
        position: relative;
        height: 271px;
        width: 100%;
        margin: 0 442px;
        z-index: 0;
    }

    .business__main--dashed,
    .business__main--solid,
    .business__main--description,
    .business__main--icon {
        position: absolute;
        z-index: -1;
    }

    .business__main--dashed {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 175px 10px 175px 175px;
        border: 4px dashed #FFD700;
    }

    .business__main--dashed::after {
        content: '';
        position: absolute;
        right: 8rem;
        bottom: -4px;
        width: 48%;
        background-color: #FFF8E1;
        border-radius: 175px 10px 175px 175px;
        border: 2px solid #FFD700;
        z-index: 0;
    }

    .business__main--solid {
        top: 0;
        right: 0;
        width: 280px;
        bottom: 0;
        border-radius: 0 10px 135px 0;
        border: 4px solid #FFD700;
        border-left: none;
    }

    .business__main--description {
        top: 13px;
        left: 13px;
        right: 13px;
        bottom: 13px;
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        color: #000;
        background-color: #FFF8E1;
        border-radius: 175px 10px 175px 175px;
        z-index: 1;
    }

    .group-description {
        display: flex;
        height: 100%;
        padding: 0 20px;
        gap: 29.66px;
        align-items: center;
    }

    .description-left {
        width: 213px;
    }

    .description-left img {
        width: 100%;
        height: auto;
    }

    .description-right {
        text-align: left;
        font: normal normal normal 16px / 30px Kozuka Gothic Pr6N;
        color: #212121;
        /*width: 42pc;*/
        width: auto;
        padding: 0 21px;
        font-size: 16px;
        opacity: 1;
    }

    .business__main--icon {
        right: 50%;
        left: -51px;
        bottom: 35%;
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: #fff;
        border: 2px dashed #FFD700;
        z-index: 2;
    }

    .business__main--icon::after {
        content: '';
        position: absolute;
        top: 70px;
        left: 41px;
        width: 5px;
        height: 106px;
        background-color: #FFD700;
    }

    .business__main--icon::before {
        content: '';
        position: absolute;
        top: 11rem;
        left: 2rem;
        width: 50px;
        height: 106px;
        background: url('<?php echo get_template_directory_uri() . '/images/business/icon/Polygon 8.png' ?>') no-repeat;
    }

    .business__main--icon-center {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 75px;
        height: 75px;
        background-color: #FFD700;
        border-radius: 50%;
        transform: translate(-50%, -50%);
    }

    .text-stemp {
        height: 30px;
    }

    .stemp-end .business__main--icon::after,
    .stemp-end .business__main--icon::before {
        display: none;
    }

    .stemp-col .group-description {
        flex-direction: row-reverse;
        padding: 0 22px;
        gap: 0;
        justify-content: space-evenly;
    }

    .stemp-col .business__main--dashed::after {
        left: 8rem;
    }

    .stemp-col .business__main--solid {
        border-radius: 10px 0 0 135px;
        left: 0;
        border-left: 4px solid #FFD700;
        border-right: none;
        border-top: 4px solid #FFD700;
        border-bottom: 4px solid #FFD700;
    }

    .stemp-col .business__main--dashed {
        border-radius: 10px 175px 175px 175px;
    }

    .stemp-col .business__main--description {
        border-radius: 10px 201px 175px 175px;
    }

    @media screen and (min-width: 760px) and (max-width: 1366px) {
        .business__step--main {
            padding: 70px;
        }

        .business__step--main>.business__main {
            margin: 0;
        }
    }

    @media screen and (max-width: 768px) {
        .business__step--main {
            height: 100%;
        }

        .business__step--main>.business__main {
            margin: 0;
            height: 45rem;
        }

        .business__main--dashed {
            border-radius: 100px 10px 100px 10px;
            height: 568px;
        }

        .business__main--solid {
            width: 131px;
            height: 568px;
            border-radius: 0 10px 100px 0;
        }

        .business__main--dashed::after {
            width: 15%;
        }

        .business__main--icon {
            left: 35%;
            bottom: 15%;
        }

        .business__main--description {
            height: 543px;
            border-radius: 100px 10px 95px 10px;
        }

        .group-description {
            flex-direction: column;
            padding: 20px 20px 0;
        }

        .description-right {
            width: 100%;
        }

        .stemp-end-business .description-right {
            position: absolute;
            top: 50%;
            bottom: 50%;
            left: 0;
            padding: 0 20px;
            width: 100%;
        }

        .stemp-col .business__main--dashed {
            border-radius: 10px 110px 10px 130px;
        }

        .stemp-col .business__main--description {
            border-radius: 10px 100px 10px 120px;
        }

        .stemp-col .group-description {
            flex-direction: column;
        }

        
    }
</style>