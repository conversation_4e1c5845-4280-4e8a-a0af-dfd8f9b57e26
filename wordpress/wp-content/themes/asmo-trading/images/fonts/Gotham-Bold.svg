<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170812 at Fri Nov  3 21:49:08 2006
 By www-data
HTF Gotham Copr. 2000 The Hoefler Type Foundry, Inc. Info: www.typography.com
</metadata>
<defs>
<font id="Gotham-Bold" horiz-adv-x="500" >
  <font-face 
    font-family="Gotham"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 0 0 0 0 0 0 0 0"
    ascent="800"
    descent="-200"
    x-height="536"
    cap-height="700"
    bbox="-71 -173 1195 932"
    underline-thickness="20"
    underline-position="-153"
    unicode-range="U+0020-FB02"
  />
<missing-glyph 
 />
    <glyph glyph-name=".notdef" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="300" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="324" 
d="M73 700h178v-20l-47 -442h-84l-47 442v20zM243 0h-162v160h162v-160z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="520" 
d="M293 381l18 319h168v-5l-109 -314h-77zM57 381l18 319h168v-5l-109 -314h-77z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="700" 
d="M665 426h-98l-25 -148h88v-128h-110l-26 -150h-132l26 150h-145l-26 -150h-132l26 150h-76v128h98l25 148h-88v128h110l25 146h132l-25 -146h145l25 146h132l-25 -146h76v-128zM435 426h-145l-25 -148h145z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="646" 
d="M596 199q0 -82 -57.5 -134.5t-154.5 -63.5v-99h-104v101q-133 17 -236 95l78 114q86 -65 163 -81v159q-112 30 -163.5 78.5t-51.5 134.5q0 82 57 134.5t153 62.5v58h104v-60q101 -14 186 -74l-67 -117q-64 43 -124 59v-151q114 -31 165.5 -81.5t51.5 -134.5zM285 440v135
q-69 -10 -69 -63q0 -25 15 -41.5t54 -30.5zM450 191q0 26 -15.5 43t-55.5 32v-140q71 11 71 65z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="860" 
d="M385 530q0 -76 -48 -128t-123 -52t-122 51t-47 127t48 128t123 52t122 -51t47 -127zM745 700l-511 -700h-119l511 700h119zM815 172q0 -76 -48 -128t-123 -52t-122 51t-47 127t48 128t123 52t122 -51t47 -127zM280 528q0 37 -18.5 63t-47.5 26q-30 0 -47 -25t-17 -62
t18.5 -63t47.5 -26q30 0 47 25t17 62zM710 170q0 37 -18.5 63t-47.5 26q-30 0 -47 -25t-17 -62t18.5 -63t47.5 -26q30 0 47 25t17 62z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="696" 
d="M668 65l-113 -79l-81 82q-95 -78 -205 -78q-102 0 -168.5 55.5t-66.5 147.5q0 131 136 193q-49 71 -49 141q0 77 57.5 131t154.5 54q88 0 142.5 -50.5t54.5 -126.5q0 -127 -147 -184l93 -93q33 52 68 120l113 -62q-52 -94 -92 -148zM396 526q0 31 -18 48.5t-48 17.5
q-31 0 -49.5 -20t-18.5 -52q0 -41 41 -88q48 18 70.5 39.5t22.5 54.5zM388 156l-134 136q-65 -34 -65 -93q0 -37 27.5 -60t70.5 -23q50 0 101 40z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="286" 
d="M57 381l18 319h170v-5l-109 -314h-79z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="445" 
d="M403 614q-102 -71 -147.5 -146.5t-45.5 -180.5t45.5 -180.5t147.5 -146.5l-67 -101q-139 76 -208 180.5t-69 247.5t69 247.5t208 180.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="445" 
d="M386 287q0 -143 -69 -247.5t-208 -180.5l-67 101q102 71 147.5 146.5t45.5 180.5t-45.5 180.5t-147.5 146.5l67 101q139 -76 208 -180.5t69 -247.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="430" 
d="M379 490l-42 -72l-97 74l16 -120h-82l16 120l-97 -74l-42 72l112 47l-112 47l42 72l97 -74l-16 120h82l-16 -120l97 74l42 -72l-112 -47z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="640" 
d="M249 421v180h142v-180h181v-138h-181v-180h-142v180h-181v138h181z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="286" 
d="M26 -99q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="408" 
d="M358 237h-308v146h308v-146z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="286" 
d="M224 0h-162v160h162v-160z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="530" 
d="M415 798h129l-443 -926h-129z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="730" 
d="M676 351q0 -155 -87 -259t-225 -104t-224 103t-86 258t87 259t225 104t224 -103t86 -258zM518 349q0 97 -42.5 160.5t-111.5 63.5t-110.5 -62t-41.5 -160t42.5 -161t111.5 -63t110.5 62.5t41.5 159.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="422" 
d="M24 645l201 60h105v-705h-152v549l-122 -30z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="623" 
d="M309 572q-39 0 -70.5 -21.5t-72.5 -72.5l-108 87q55 75 113.5 110t147.5 35q111 0 176 -59t65 -158q0 -82 -40.5 -137.5t-143.5 -134.5l-114 -88h305v-133h-518v122l233 191q69 57 95 91.5t26 76.5q0 43 -26 67t-68 24z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="621" 
d="M354 568h-275v132h473v-116l-177 -169q188 -31 188 -197q0 -101 -68.5 -165.5t-183.5 -64.5q-171 0 -273 121l106 101q75 -84 169 -84q44 0 71.5 23.5t27.5 62.5q0 43 -35.5 66.5t-101.5 23.5h-64l-24 98z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="689" 
d="M415 705h130v-429h94v-125h-94v-151h-147v151h-342l-25 109zM398 276v218l-186 -218h186z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="630" 
d="M90 355l20 345h430v-135h-298l-8 -121q48 12 92 12q110 0 177.5 -55.5t67.5 -169.5q0 -111 -72.5 -177t-193.5 -66q-152 0 -262 105l93 111q84 -76 167 -76q55 0 86 25.5t31 72.5q0 45 -33.5 70.5t-90.5 25.5q-56 0 -117 -26z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="662" 
d="M580 635l-81 -119q-38 29 -69.5 42.5t-73.5 13.5q-62 0 -97.5 -44.5t-43.5 -117.5q68 45 145 45q108 0 177 -58.5t69 -165.5t-76 -175t-191 -68q-127 0 -202 75q-83 83 -83 266q0 171 78 277t229 106q122 0 219 -77zM453 224q0 46 -33 74.5t-89 28.5t-88 -28t-32 -74
q0 -47 33 -76t89 -29t88 28.5t32 75.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="619" 
d="M68 567v133h504v-116l-314 -584h-174l313 567h-329z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="630" 
d="M585 197q0 -96 -75.5 -151.5t-194.5 -55.5q-118 0 -194 56t-76 147q0 63 28 103.5t85 66.5q-89 54 -89 154q0 82 68.5 137.5t177.5 55.5t177.5 -55.5t68.5 -137.5q0 -100 -89 -154q57 -30 85 -68t28 -98zM415 500q0 35 -27 59t-73 24t-73 -24t-27 -58q0 -38 27.5 -62.5
t72.5 -24.5t72.5 24t27.5 62zM436 205q0 40 -33.5 63.5t-87.5 23.5t-87.5 -23.5t-33.5 -63.5q0 -38 32.5 -63t88.5 -25t88.5 25t32.5 63z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="662" 
d="M608 371q0 -175 -80 -279t-226 -104q-130 0 -230 83l81 117q75 -60 153 -60q62 0 98 43t44 116q-56 -48 -142 -48q-115 0 -182.5 60.5t-67.5 166.5q0 107 74.5 176.5t192.5 69.5q69 0 115.5 -18t86.5 -58q83 -83 83 -265zM451 472q0 48 -33 78t-90 30q-56 0 -87.5 -29.5
t-31.5 -77.5t33 -77t89 -29t88 29t32 76z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="296" 
d="M229 376h-162v160h162v-160zM229 0h-162v160h162v-160z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="296" 
d="M229 376h-162v160h162v-160zM31 -99q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="640" 
d="M68 421l484 212v-141l-338 -139l338 -139v-143l-484 212v138z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="640" 
d="M83 551h474v-138h-474v138zM83 291h474v-138h-474v138z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="640" 
d="M572 283l-484 -212v141l338 139l-338 139v143l484 -212v-138z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="544" 
d="M179 405q175 7 175 92q0 34 -24 53t-68 19q-81 0 -150 -69l-92 101q98 108 245 108q110 0 176 -54.5t66 -153.5q0 -162 -197 -202l-11 -61h-102l-23 162zM326 0h-162v160h162v-160z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="980" 
d="M927 316q0 -120 -56 -185.5t-143 -65.5q-101 0 -153 60q-66 -60 -148 -60q-78 0 -130.5 53t-52.5 140q0 104 67.5 177.5t156.5 73.5q88 0 138 -68l11 53l118 -19l-47 -269q-3 -15 -3 -31q0 -28 19.5 -45.5t53.5 -17.5q55 0 91 54t36 150q0 142 -114.5 249t-279.5 107
q-168 0 -282 -116t-114 -282q0 -167 114.5 -281.5t289.5 -114.5q131 0 245 67l20 -32q-123 -75 -265 -75q-190 0 -318 127.5t-128 308.5q0 180 127.5 309t310.5 129q180 0 308 -119.5t128 -276.5zM581 312q0 42 -26 68t-67 26q-49 0 -84.5 -41t-35.5 -100q0 -46 26 -71.5
t68 -25.5q48 0 83.5 41.5t35.5 102.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="790" 
d="M766 0h-161l-64 157h-296l-64 -157h-157l300 705h142zM486 293l-93 227l-93 -227h186z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="722" 
d="M679 192q0 -94 -70 -143t-192 -49h-333v700h325q107 0 169 -49t62 -132v-2q0 -99 -92 -151q65 -25 98 -65t33 -107v-2zM487 491v2q0 35 -26 53.5t-75 18.5h-152v-148h142q111 0 111 74zM526 211v2q0 76 -114 76h-178v-154h183q109 0 109 76z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="738" 
d="M699 113q-58 -62 -124.5 -93.5t-162.5 -31.5q-154 0 -256.5 104t-102.5 256v2q0 152 103 257t262 105q93 0 158 -27.5t118 -80.5l-98 -113q-87 79 -179 79q-87 0 -145 -63.5t-58 -154.5v-2q0 -92 57.5 -156t145.5 -64q56 0 97 20t87 62z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="782" 
d="M729 350q0 -150 -104.5 -250t-267.5 -100h-273v700h273q163 0 267.5 -99.5t104.5 -248.5v-2zM568 348v2q0 93 -58.5 152t-152.5 59h-119v-422h119q94 0 152.5 58t58.5 151z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="670" 
d="M617 0h-533v700h528v-137h-375v-142h330v-137h-330v-147h380v-137z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="656" 
d="M617 560h-379v-149h334v-140h-334v-271h-154v700h533v-140z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="784" 
d="M720 99q-131 -111 -299 -111q-162 0 -265 102t-103 258v2q0 151 105 256.5t262 105.5q91 0 154 -23.5t121 -72.5l-97 -117q-44 37 -85 54t-98 17q-84 0 -142.5 -64t-58.5 -154v-2q0 -96 59.5 -159t152.5 -63q86 0 145 42v100h-155v133h304v-304z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="760" 
d="M676 0h-154v281h-284v-281h-154v700h154v-277h284v277h154v-700z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="336" 
d="M245 0h-154v700h154v-700z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="564" 
d="M491 242q0 -124 -66.5 -188t-181.5 -64q-141 0 -229 105l97 108q64 -70 128 -70q94 0 94 116v451h158v-458z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="730" 
d="M721 0h-185l-217 298l-81 -84v-214h-154v700h154v-306l284 306h186l-285 -297z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="619" 
d="M587 0h-503v700h154v-560h349v-140z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="868" 
d="M784 0h-153v457l-197 -299h-4l-195 296v-454h-151v700h166l184 -296l184 296h166v-700z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="790" 
d="M706 0h-131l-339 445v-445h-152v700h142l328 -431v431h152v-700z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="850" 
d="M797 350q0 -151 -106.5 -256.5t-266.5 -105.5t-265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2zM636 348v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64t151 63t59 155z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="668" 
d="M638 456q0 -117 -80 -181.5t-203 -64.5h-117v-210h-154v700h286q124 0 196 -66.5t72 -175.5v-2zM482 453v2q0 51 -33 78.5t-92 27.5h-119v-214h122q56 0 89 30t33 76z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="850" 
d="M807 88l-98 -109l-77 69q-93 -60 -208 -60q-160 0 -265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2q0 -111 -62 -201zM636 348v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64q49 0 92 19l-114 97l98 110
l115 -104q19 42 19 96z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="723" 
d="M689 0h-180l-150 224h-121v-224h-154v700h320q124 0 194 -61.5t70 -169.5v-2q0 -161 -150 -217zM512 459v2q0 49 -32 74.5t-89 25.5h-153v-201h156q55 0 86.5 27t31.5 72z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="640" 
d="M590 207q0 -102 -70.5 -159.5t-186.5 -57.5q-172 0 -297 112l91 109q103 -85 209 -85q47 0 74 17.5t27 48.5v2q0 30 -28 48.5t-113 40.5q-60 15 -97 29.5t-70.5 38.5t-49 59.5t-15.5 86.5v2q0 95 68.5 153t176.5 58q149 0 260 -89l-80 -116q-99 69 -182 69
q-43 0 -66.5 -17t-23.5 -44v-2q0 -34 29 -51t122 -41q114 -30 168 -76.5t54 -133.5v-2z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="648" 
d="M614 558h-213v-558h-154v558h-213v142h580v-142z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="756" 
d="M683 305q0 -156 -81.5 -236t-225.5 -80q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="750" 
d="M726 700l-283 -705h-136l-283 705h170l183 -493l183 493h166z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1122" 
d="M1092 700l-239 -705h-134l-158 458l-158 -458h-134l-239 705h165l145 -474l157 476h132l157 -476l145 474h161z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="732" 
d="M701 0h-180l-157 240l-158 -240h-175l244 356l-234 344h180l147 -227l148 227h175l-234 -342z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="716" 
d="M704 700l-269 -421v-279h-154v276l-269 424h180l167 -281l170 281h175z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="708" 
d="M646 0h-583v117l385 448h-373v135h571v-117l-385 -448h385v-135z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="465" 
d="M406 700v-119h-177v-592h177v-119h-323v830h323z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="530" 
d="M115 798l443 -926h-129l-443 926h129z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="465" 
d="M59 700h323v-830h-323v119h177v592h-177v119z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M249 610l-91 -117h-108l148 207h104l148 -207h-110z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="600" 
d="M-2 -44h604v-116h-604v116z" />
    <glyph glyph-name="grave" unicode="`" 
d="M354 595h-116l-128 120l131 57z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="592" 
d="M529 0h-147v58q-61 -68 -162 -68q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q239 0 239 -231v-311zM385 189v27q-43 20 -97 20q-49 0 -77 -19t-28 -55v-2q0 -30 23 -47.5t60 -17.5
q53 0 86 26.5t33 67.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="675" 
d="M631 267q0 -128 -71.5 -202.5t-172.5 -74.5q-100 0 -167 80v-70h-152v730h152v-271q64 87 167 87q100 0 172 -74.5t72 -202.5v-2zM479 267v2q0 65 -38 106.5t-93 41.5t-92.5 -41.5t-37.5 -106.5v-2q0 -65 37.5 -106.5t92.5 -41.5t93 41t38 107z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="571" 
d="M540 86q-44 -48 -94 -73t-125 -25q-120 0 -199.5 80.5t-79.5 196.5v2q0 116 80 197.5t201 81.5q134 0 214 -90l-93 -100q-56 59 -122 59q-56 0 -93 -43t-37 -103v-2q0 -63 37.5 -105.5t98.5 -42.5q63 0 123 57z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="675" 
d="M607 0h-152v77q-64 -87 -167 -87q-100 0 -172 74.5t-72 202.5v2q0 128 71.5 202.5t172.5 74.5q100 0 167 -80v264h152v-730zM457 267v2q0 65 -37.5 106.5t-92.5 41.5t-93 -41t-38 -107v-2q0 -65 38 -106.5t93 -41.5t92.5 41.5t37.5 106.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="613" 
d="M568 253q0 -10 -2 -38h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207v-2zM420 310q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5h229z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="382" 
d="M368 596q-36 13 -69 13q-55 0 -55 -58v-21h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="675" 
d="M607 121q0 -149 -73.5 -216t-225.5 -67q-131 0 -237 56l52 114q87 -49 181 -49q153 0 153 146v26q-36 -44 -76.5 -65.5t-98.5 -21.5q-99 0 -168.5 67.5t-69.5 182.5v2q0 115 70 182.5t168 67.5q100 0 173 -80v70h152v-415zM457 294v2q0 54 -37.5 89t-93.5 35t-93 -35
t-37 -89v-2q0 -55 37 -89.5t93 -34.5t93.5 35t37.5 89z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="623" 
d="M559 0h-152v299q0 53 -24 81t-68 28t-69.5 -28t-25.5 -81v-299h-152v730h152v-270q67 86 157 86q86 0 134 -53t48 -146v-347z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="302" 
d="M231 595h-160v135h160v-135zM227 0h-152v536h152v-536z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="302" 
d="M231 595h-160v135h160v-135zM227 9q0 -172 -172 -172q-34 0 -66 6v119q18 -3 32 -3q54 0 54 62v515h152v-527z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="592" 
d="M587 0h-174l-140 219l-53 -56v-163h-152v730h152v-389l178 195h182l-204 -211z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="302" 
d="M227 0h-152v730h152v-730z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="950" 
d="M886 0h-152v299q0 109 -89 109q-43 0 -67.5 -28t-24.5 -81v-299h-152v299q0 109 -89 109q-43 0 -67.5 -28t-24.5 -81v-299h-152v536h152v-76q67 86 158 86q107 0 151 -85q73 85 176 85q86 0 133.5 -50.5t47.5 -146.5v-349z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="623" 
d="M559 0h-152v299q0 53 -24 81t-68 28t-69.5 -28t-25.5 -81v-299h-152v536h152v-76q67 86 157 86q86 0 134 -53t48 -146v-347z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="664" 
d="M622 267q0 -117 -83.5 -198t-207.5 -81t-206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2zM472 265v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5q63 0 101 42t38 104z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="675" 
d="M631 267q0 -128 -71.5 -202.5t-172.5 -74.5q-100 0 -167 80v-230h-152v696h152v-77q64 87 167 87q100 0 172 -74.5t72 -202.5v-2zM479 267v2q0 65 -38 106.5t-93 41.5t-92.5 -41.5t-37.5 -106.5v-2q0 -65 37.5 -106.5t92.5 -41.5t93 41t38 107z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="675" 
d="M607 -160h-152v237q-64 -87 -167 -87q-100 0 -172 74.5t-72 202.5v2q0 128 71.5 202.5t172.5 74.5q100 0 167 -80v70h152v-696zM457 267v2q0 65 -37.5 106.5t-92.5 41.5t-93 -41t-38 -107v-2q0 -65 38 -106.5t93 -41.5t92.5 41.5t37.5 106.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="418" 
d="M391 387h-8q-78 0 -120.5 -47.5t-42.5 -141.5v-198h-152v536h152v-108q52 123 171 118v-159z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="502" 
d="M461 163q0 -86 -56.5 -129.5t-148.5 -43.5q-127 0 -230 81l65 100q88 -64 169 -64q64 0 64 40v2q0 5 -1.5 10t-5.5 9t-8.5 7.5t-12 7t-13 6t-15.5 6t-17 5.5t-19 5.5t-19 5.5q-34 10 -58 20.5t-49.5 28.5t-39 46.5t-13.5 65.5v2q0 79 56 124.5t140 45.5q108 0 200 -62
l-58 -105q-85 50 -145 50q-27 0 -41.5 -10.5t-14.5 -26.5v-2q0 -27 92 -58q11 -4 17 -6q35 -12 58.5 -22.5t50 -29t40 -45.5t13.5 -62v-2z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="415" 
d="M369 22q-52 -31 -120 -31q-75 0 -115.5 36.5t-40.5 124.5v254h-64v130h64v137h152v-137h126v-130h-126v-229q0 -52 49 -52q40 0 75 19v-122z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="623" 
d="M555 0h-152v76q-67 -86 -157 -86q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="600" 
d="M580 536l-211 -540h-138l-211 540h161l120 -359l121 359h158z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="861" 
d="M837 536l-167 -540h-137l-103 329l-105 -329h-136l-165 540h154l86 -325l101 327h131l102 -328l88 326h151z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="584" 
d="M561 0h-163l-108 167l-108 -167h-159l188 272l-181 264h163l100 -156l101 156h159l-181 -261z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="600" 
d="M578 536l-206 -549q-31 -83 -70 -116.5t-106 -33.5q-72 0 -135 37l51 110q35 -21 65 -21q36 0 53 35l-210 538h161l122 -365l117 365h158z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="563" 
d="M516 0h-466v107l277 306h-268v123h457v-107l-278 -306h278v-123z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="490" 
d="M443 614q-76 -22 -99 -46.5t-23 -72.5q0 -10 1.5 -36.5t1.5 -37.5q0 -100 -104 -134q104 -34 104 -134q0 -11 -1.5 -37.5t-1.5 -36.5q0 -48 23 -72.5t99 -46.5l-27 -101q-132 23 -183 70t-51 128q0 7 1.5 33.5t1.5 35.5q0 58 -25 80.5t-85 22.5h-26v116h26q60 0 85 22.5
t25 80.5q0 9 -1.5 35.5t-1.5 33.5q0 81 51 128t183 70z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="350" 
d="M235 -128h-120v926h120v-926z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="490" 
d="M441 229h-26q-60 0 -85 -22.5t-25 -80.5q0 -9 1.5 -35.5t1.5 -33.5q0 -81 -51 -128t-183 -70l-27 101q76 22 99 46.5t23 72.5q0 10 -1.5 36.5t-1.5 37.5q0 100 104 134q-104 34 -104 134q0 11 1.5 37.5t1.5 36.5q0 48 -23 72.5t-99 46.5l27 101q132 -23 183 -70t51 -128
q0 -7 -1.5 -33.5t-1.5 -35.5q0 -58 25 -80.5t85 -22.5h26v-116z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="510" 
d="M461 372q-19 -69 -47 -103.5t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5t23.5 37.5z" />
    <glyph glyph-name="nbspace" unicode="&#xa0;" horiz-adv-x="300" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="324" 
d="M81 700h162v-160h-162v160zM251 0h-178v20l47 442h84l47 -442v-20z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="581" 
d="M348 208q55 7 108 56l86 -87q-88 -98 -214 -101l-16 -82h-104l19 98q-80 30 -130 101t-50 161q0 117 79.5 198.5t200.5 81.5h6l14 72h104l-17 -88q59 -20 108 -74l-90 -97q-25 25 -49 37zM312 503q-52 -4 -85 -46.5t-33 -100.5q0 -89 64 -130z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="644" 
d="M136 394v81q0 111 62 173t175 62q140 0 225 -115l-117 -94q-27 35 -51 50.5t-55 15.5q-38 0 -60.5 -25.5t-22.5 -70.5v-77h228v-127h-228v-134h304v-133h-536v101l76 25v141h-76v127h76z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="720" 
d="M124 386h-75v101h94q32 102 107.5 163.5t183.5 61.5q87 0 144.5 -33t101.5 -95l-116 -88q-30 40 -60.5 59.5t-72.5 19.5q-88 0 -127 -88h184v-101h-208q-1 -11 -1 -35q0 -22 1 -33h208v-101h-186q39 -92 135 -92q41 0 70 19t65 63l110 -81q-48 -67 -106 -102.5
t-139 -35.5q-112 0 -189 61.5t-107 167.5h-92v101h75q-1 10 -1 31q0 25 1 37z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="686" 
d="M417 262v-62h184v-108h-184v-92h-148v92h-184v108h184v62h-184v108h143l-214 330h177l154 -259l155 259h172l-216 -330h145v-108h-184z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="632" 
d="M577 356q0 -85 -89 -118q42 -33 42 -92q0 -72 -54 -114t-151 -42q-146 0 -233 95l82 82q72 -66 147 -66q66 0 66 37q0 18 -21 29.5t-89 26.5q-121 27 -171.5 60t-50.5 90q0 85 89 118q-42 33 -42 92q0 72 54 114t151 42q146 0 233 -95l-82 -82q-72 66 -147 66
q-66 0 -66 -37q0 -18 21 -29.5t89 -26.5q121 -27 171.5 -60t50.5 -90zM444 334q0 20 -23.5 33t-77.5 26q-72 18 -100 18q-26 0 -40.5 -12t-14.5 -33q0 -20 23.5 -33t77.5 -26q72 -18 100 -18q26 0 40.5 12t14.5 33z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M430 595h-146v135h146v-135zM216 595h-146v135h146v-135z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="830" 
d="M777 351q0 -149 -106 -256t-257 -107t-256 106t-105 255t106 256t257 107t256 -106t105 -255zM735 351q0 134 -91.5 227.5t-227.5 93.5t-228.5 -94t-92.5 -229q0 -134 91.5 -227.5t227.5 -93.5t228.5 94t92.5 229zM577 225q-64 -69 -158 -69q-84 0 -140.5 57t-56.5 141
t56.5 142t143.5 58q91 0 152 -59l-55 -64q-48 43 -97 43q-47 0 -78.5 -34.5t-31.5 -84.5t31 -84.5t79 -34.5q49 0 99 45z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="390" 
d="M323 405h-81v32q-33 -38 -89 -38q-44 0 -72.5 24t-28.5 67q0 47 32 71t87 24q36 0 72 -12v5q0 54 -64 54q-38 0 -81 -16l-21 63q53 24 115 24q131 0 131 -127v-171zM327 277h-278v66h278v-66zM244 509v15q-27 11 -54 11q-57 0 -57 -41q0 -37 45 -37q29 0 47.5 14.5
t18.5 37.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="645" 
d="M481 496l116 -56l-130 -171l130 -171l-116 -58l-168 224v8zM209 496l116 -56l-130 -171l130 -171l-116 -58l-168 224v8z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="830" 
d="M777 351q0 -149 -106 -256t-257 -107t-256 106t-105 255t106 256t257 107t256 -106t105 -255zM735 351q0 134 -91.5 227.5t-227.5 93.5t-228.5 -94t-92.5 -229q0 -134 91.5 -227.5t227.5 -93.5t228.5 94t92.5 229zM588 426q0 -90 -83 -120l94 -138h-99l-82 124h-67v-124
h-85v385h171q70 0 110.5 -33.5t40.5 -93.5zM502 421q0 57 -68 57h-83v-113h85q32 0 49 15.5t17 40.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M430 600h-360v105h360v-105z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="480" 
d="M413 537q0 -68 -52 -118.5t-121 -50.5t-121 50.5t-52 118.5t52 118.5t121 50.5t121 -50.5t52 -118.5zM332 537q0 38 -26.5 66t-65.5 28t-65.5 -28t-26.5 -66t26.5 -66t65.5 -28t65.5 28t26.5 66z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="614" 
d="M570 207q0 -97 -68 -158t-188 -61q-168 0 -277 119l103 100q78 -81 176 -81q49 0 76 22t27 61q0 80 -132 80h-77v128h81q52 0 80.5 22.5t28.5 59.5q0 34 -27 54.5t-74 20.5q-41 0 -74 -20.5t-69 -60.5l-106 90q52 65 109 97t142 32q113 0 181.5 -52.5t68.5 -136.5
q0 -112 -92 -163q53 -23 82 -59t29 -94z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M262 595h-116l113 177l131 -57z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="850" 
d="M797 351q0 -156 -114 -262l178 -180v-10h-226l-105 103q-51 -14 -106 -14q-160 0 -265.5 104.5t-105.5 256.5t106.5 257.5t266.5 105.5t265.5 -104.5t105.5 -256.5zM636 349q0 92 -60 156.5t-152 64.5t-151 -63.5t-59 -155.5t60 -156.5t152 -64.5t151 63.5t59 155.5z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="590" 
d="M311 700h195v-700h-154v230h-24q-131 0 -212 60.5t-81 173.5q0 110 75.5 173t200.5 63z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="286" 
d="M224 230h-162v160h162v-160z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M207 10h112l-74 -183l-127 53z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="390" 
d="M349 552q0 -65 -44.5 -109.5t-110.5 -44.5t-109.5 44t-43.5 109t44.5 109.5t110.5 44.5t109.5 -44t43.5 -109zM339 277h-288v66h288v-66zM267 551q0 34 -20.5 58t-52.5 24t-51.5 -23.5t-19.5 -57.5t20.5 -58t52.5 -24t51.5 23.5t19.5 57.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="645" 
d="M436 40l-116 56l130 171l-130 171l116 58l168 -224v-8zM164 40l-116 56l130 171l-130 171l116 58l168 -224v-8z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="544" 
d="M218 700h162v-160h-162v160zM365 295q-175 -7 -175 -92q0 -34 24 -53t68 -19q81 0 150 69l92 -101q-98 -108 -245 -108q-110 0 -176 54.5t-66 153.5q0 162 197 202l11 61h102l23 -162z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="790" 
d="M766 0h-161l-64 157h-296l-64 -157h-157l300 705h142zM486 293l-93 227l-93 -227h186zM459 755h-116l-128 120l131 57z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="790" 
d="M766 0h-161l-64 157h-296l-64 -157h-157l300 705h142zM486 293l-93 227l-93 -227h186zM447 755h-116l113 177l131 -57z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="790" 
d="M766 0h-161l-64 157h-296l-64 -157h-157l300 705h142zM486 293l-93 227l-93 -227h186zM394 821l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="790" 
d="M766 0h-161l-64 157h-296l-64 -157h-157l300 705h142zM486 293l-93 227l-93 -227h186zM601 893q-19 -69 -47 -103.5t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5
t23.5 37.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="790" 
d="M766 0h-161l-64 157h-296l-64 -157h-157l300 705h142zM486 293l-93 227l-93 -227h186zM575 755h-146v135h146v-135zM361 755h-146v135h146v-135z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="790" 
d="M766 0h-161l-64 157h-296l-64 -157h-157l292 687q-42 37 -42 88q0 48 36.5 82.5t84.5 34.5t84.5 -34.5t36.5 -82.5q0 -51 -42 -88zM457 775q0 25 -18.5 43.5t-43.5 18.5t-43.5 -18.5t-18.5 -43.5t18.5 -43.5t43.5 -18.5t43.5 18.5t18.5 43.5zM486 293l-93 227l-93 -227
h186z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1045" 
d="M992 0h-508v157h-235l-83 -157h-157l377 700h601v-137h-350v-142h305v-137h-305v-147h355v-137zM484 293v274h-18l-145 -274h163z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="738" 
d="M326 -2q-121 28 -197 125t-76 226q0 152 103 257.5t262 105.5q93 0 158 -27.5t118 -80.5l-98 -113q-87 79 -179 79q-87 0 -145 -63.5t-58 -155.5t57.5 -156.5t145.5 -64.5q56 0 97 20t87 62l98 -99q-54 -58 -115 -89t-146 -35l-65 -162l-127 53z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="670" 
d="M617 0h-533v700h528v-137h-375v-142h330v-137h-330v-147h380v-137zM433 755h-116l-128 120l131 57z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="670" 
d="M617 0h-533v700h528v-137h-375v-142h330v-137h-330v-147h380v-137zM380 755h-116l113 177l131 -57z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="670" 
d="M617 0h-533v700h528v-137h-375v-142h330v-137h-330v-147h380v-137zM345 821l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="670" 
d="M617 0h-533v700h528v-137h-375v-142h330v-137h-330v-147h380v-137zM526 755h-146v135h146v-135zM312 755h-146v135h146v-135z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="336" 
d="M245 0h-154v700h154v-700zM232 755h-116l-128 120l131 57z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="336" 
d="M245 0h-154v700h154v-700zM220 755h-116l113 177l131 -57z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="336" 
d="M245 0h-154v700h154v-700zM167 821l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="336" 
d="M245 0h-154v700h154v-700zM348 755h-146v135h146v-135zM134 755h-146v135h146v-135z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="812" 
d="M114 284h-75v136h75v280h273q163 0 267.5 -99.5t104.5 -249.5t-104.5 -250.5t-267.5 -100.5h-273v284zM266 420h152v-136h-152v-145h121q94 0 152.5 58t58.5 152t-58.5 153t-152.5 59h-121v-141z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="790" 
d="M706 0h-131l-339 445v-445h-152v700h142l328 -431v431h152v-700zM601 893q-19 -69 -47 -103.5t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5t23.5 37.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="850" 
d="M797 350q0 -151 -106.5 -256.5t-266.5 -105.5t-265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2zM636 348v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64t151 63t59 155zM479 755h-116l-128 120l131 57z
" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="850" 
d="M797 350q0 -151 -106.5 -256.5t-266.5 -105.5t-265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2zM636 348v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64t151 63t59 155zM487 755h-116l113 177l131 -57z
" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="850" 
d="M797 350q0 -151 -106.5 -256.5t-266.5 -105.5t-265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2zM636 348v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64t151 63t59 155zM424 821l-79 -66h-115l123 157
h144l123 -157h-117z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="850" 
d="M797 350q0 -151 -106.5 -256.5t-266.5 -105.5t-265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2zM636 348v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64t151 63t59 155zM631 893q-19 -69 -47 -103.5
t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5t23.5 37.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="850" 
d="M797 350q0 -151 -106.5 -256.5t-266.5 -105.5t-265.5 104.5t-105.5 255.5v2q0 151 106.5 256.5t266.5 105.5t265.5 -104.5t105.5 -255.5v-2zM636 348v2q0 92 -60 156t-152 64t-151 -63t-59 -155v-2q0 -92 60 -156t152 -64t151 63t59 155zM605 755h-146v135h146v-135z
M391 755h-146v135h146v-135z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="850" 
d="M797 351q0 -152 -106.5 -257.5t-266.5 -105.5q-107 0 -193 50l-39 -43h-147l98 114q-90 99 -90 240q0 152 106.5 257.5t266.5 105.5q107 0 193 -50l39 43h147l-98 -114q90 -99 90 -240zM523 549q-46 24 -99 24q-92 0 -152.5 -65.5t-60.5 -156.5q0 -68 35 -123zM639 349
q0 68 -35 123l-277 -321q46 -24 99 -24q92 0 152.5 65.5t60.5 156.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="756" 
d="M683 305q0 -156 -81.5 -236t-225.5 -80q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395zM447 750h-116l-128 120l131 57z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="756" 
d="M683 305q0 -156 -81.5 -236t-225.5 -80q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395zM425 750h-116l113 177l131 -57z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="756" 
d="M683 305q0 -156 -81.5 -236t-225.5 -80q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395zM377 821l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="756" 
d="M683 305q0 -156 -81.5 -236t-225.5 -80q-143 0 -223 79.5t-80 231.5v400h154v-396q0 -84 39.5 -128.5t111.5 -44.5t111.5 43t39.5 125v401h154v-395zM558 755h-146v135h146v-135zM344 755h-146v135h146v-135z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="716" 
d="M704 700l-269 -421v-279h-154v276l-269 424h180l167 -281l170 281h175zM415 750h-116l113 177l131 -57z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="673" 
d="M638 355q0 -118 -80 -182.5t-203 -64.5h-117v-108h-154v700h154v-102h132q124 0 196 -66.5t72 -176.5zM482 352q0 52 -33 79.5t-92 27.5h-119v-214h122q57 0 89.5 29.5t32.5 77.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="620" 
d="M537 542q0 -60 -24 -97t-67 -67q131 -57 131 -178q0 -100 -75 -153.5t-203 -48.5v114q128 6 128 93q0 40 -35 65t-93 33v100q88 56 88 125q0 38 -21.5 59.5t-58.5 21.5q-39 0 -63.5 -27.5t-24.5 -72.5v-509h-151v503q0 110 66 172t180 62q104 0 163.5 -55t59.5 -140z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="592" 
d="M529 0h-147v58q-61 -68 -162 -68q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q239 0 239 -231v-311zM385 189v27q-43 20 -97 20q-49 0 -77 -19t-28 -55v-2q0 -30 23 -47.5t60 -17.5
q53 0 86 26.5t33 67.5zM370 595h-116l-128 120l131 57z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="592" 
d="M529 0h-147v58q-61 -68 -162 -68q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q239 0 239 -231v-311zM385 189v27q-43 20 -97 20q-49 0 -77 -19t-28 -55v-2q0 -30 23 -47.5t60 -17.5
q53 0 86 26.5t33 67.5zM343 595h-116l113 177l131 -57z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="592" 
d="M529 0h-147v58q-61 -68 -162 -68q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q239 0 239 -231v-311zM385 189v27q-43 20 -97 20q-49 0 -77 -19t-28 -55v-2q0 -30 23 -47.5t60 -17.5
q53 0 86 26.5t33 67.5zM293 661l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="592" 
d="M529 0h-147v58q-61 -68 -162 -68q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q239 0 239 -231v-311zM385 189v27q-43 20 -97 20q-49 0 -77 -19t-28 -55v-2q0 -30 23 -47.5t60 -17.5
q53 0 86 26.5t33 67.5zM502 733q-19 -69 -47 -103.5t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5t23.5 37.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="592" 
d="M529 0h-147v58q-61 -68 -162 -68q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q239 0 239 -231v-311zM385 189v27q-43 20 -97 20q-49 0 -77 -19t-28 -55v-2q0 -30 23 -47.5t60 -17.5
q53 0 86 26.5t33 67.5zM474 595h-146v135h146v-135zM260 595h-146v135h146v-135z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="592" 
d="M529 0h-147v58q-61 -68 -162 -68q-79 0 -131.5 43.5t-52.5 120.5v2q0 85 58.5 128t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q239 0 239 -231v-311zM385 189v27q-43 20 -97 20q-49 0 -77 -19t-28 -55v-2q0 -30 23 -47.5t60 -17.5
q53 0 86 26.5t33 67.5zM415 710q0 -48 -36.5 -82.5t-84.5 -34.5t-84.5 34.5t-36.5 82.5t36.5 82.5t84.5 34.5t84.5 -34.5t36.5 -82.5zM356 710q0 26 -18 44t-44 18t-44 -18t-18 -44t18 -44t44 -18t44 18t18 44z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="951" 
d="M456 69q-107 -79 -218 -79q-91 0 -146.5 44.5t-55.5 122.5q0 84 58.5 127t157.5 43q67 0 131 -22v9q0 98 -115 98q-69 0 -148 -29l-38 116q98 43 208 43q126 0 182 -68q70 72 177 72q124 0 190.5 -84.5t66.5 -207.5q0 -11 -2 -39h-373q24 -105 131 -105q71 0 130 55
l87 -77q-81 -100 -219 -100q-125 0 -204 81zM529 310h229q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5zM384 217q-45 19 -96 19q-105 0 -105 -73q0 -31 23 -48.5t60 -17.5q71 0 137 51q-15 35 -19 69z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="571" 
d="M236 0q-87 26 -140.5 99t-53.5 167q0 117 80 198.5t201 81.5q134 0 214 -90l-93 -100q-56 59 -122 59q-56 0 -93 -43t-37 -104q0 -64 37.5 -106.5t98.5 -42.5q63 0 123 57l89 -90q-83 -90 -194 -97l-65 -162l-127 53z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="613" 
d="M568 253q0 -10 -2 -38h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207v-2zM420 310q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5h229zM366 595h-116l-128 120
l131 57z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="613" 
d="M568 253q0 -10 -2 -38h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207v-2zM420 310q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5h229zM362 595h-116l113 177
l131 -57z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="613" 
d="M568 253q0 -10 -2 -38h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207v-2zM420 310q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5h229zM305 661l-79 -66h-115
l123 157h144l123 -157h-117z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="613" 
d="M568 253q0 -10 -2 -38h-373q11 -51 45 -78t86 -27q71 0 130 55l87 -77q-81 -100 -219 -100q-122 0 -201 77.5t-79 199.5v2q0 117 75 198t190 81q127 0 194 -84t67 -207v-2zM420 310q-7 52 -36.5 83t-76.5 31q-46 0 -76.5 -30.5t-39.5 -83.5h229zM486 595h-146v135h146
v-135zM272 595h-146v135h146v-135z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="302" 
d="M227 0h-152v536h152v-536zM217 595h-116l-128 120l131 57z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="302" 
d="M227 0h-152v536h152v-536zM201 595h-116l113 177l131 -57z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="302" 
d="M227 0h-152v536h152v-536zM150 661l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="302" 
d="M227 0h-152v536h152v-536zM331 595h-146v135h146v-135zM117 595h-146v135h146v-135z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="648" 
d="M307 570l-114 -47l-56 75l115 46l-64 86h171l23 -33l100 41l55 -75l-102 -41l81 -115q47 -67 67.5 -123.5t20.5 -120.5q0 -116 -77 -195.5t-208 -79.5q-119 0 -198 76t-79 187q0 104 64 169.5t165 65.5q63 0 123 -32zM456 248q0 55 -37 92.5t-96 37.5q-60 0 -96.5 -37
t-36.5 -93t37.5 -94t96.5 -38q60 0 96 37.5t36 94.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="623" 
d="M559 0h-152v299q0 53 -24 81t-68 28t-69.5 -28t-25.5 -81v-299h-152v536h152v-76q67 86 157 86q86 0 134 -53t48 -146v-347zM522 733q-19 -69 -47 -103.5t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5
q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5t23.5 37.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="664" 
d="M622 267q0 -117 -83.5 -198t-207.5 -81t-206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2zM472 265v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5q63 0 101 42t38 104zM390 595h-116
l-128 120l131 57z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="664" 
d="M622 267q0 -117 -83.5 -198t-207.5 -81t-206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2zM472 265v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5q63 0 101 42t38 104zM390 595h-116
l113 177l131 -57z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="664" 
d="M622 267q0 -117 -83.5 -198t-207.5 -81t-206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2zM472 265v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5q63 0 101 42t38 104zM331 661l-79 -66
h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="664" 
d="M622 267q0 -117 -83.5 -198t-207.5 -81t-206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2zM472 265v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5q63 0 101 42t38 104zM538 733
q-19 -69 -47 -103.5t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5t23.5 37.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="664" 
d="M622 267q0 -117 -83.5 -198t-207.5 -81t-206.5 80.5t-82.5 196.5v2q0 117 83.5 198t207.5 81t206.5 -80.5t82.5 -196.5v-2zM472 265v2q0 61 -39.5 104.5t-101.5 43.5q-63 0 -101 -42t-38 -104v-2q0 -61 39.5 -104.5t101.5 -43.5q63 0 101 42t38 104zM512 595h-146v135
h146v-135zM298 595h-146v135h146v-135z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="664" 
d="M622 268q0 -117 -83.5 -198.5t-207.5 -81.5q-77 0 -144 35l-27 -28h-133l80 92q-65 76 -65 179q0 117 83.5 198.5t207.5 81.5q78 0 143 -35l28 30h133l-81 -93q66 -77 66 -180zM388 407q-25 12 -57 12q-64 0 -103.5 -43.5t-39.5 -107.5q0 -37 16 -71zM476 266
q0 37 -16 71l-183 -211q27 -11 56 -11q64 0 103.5 43.5t39.5 107.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="623" 
d="M555 0h-152v76q-67 -86 -157 -86q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536zM385 595h-116l-128 120l131 57z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="623" 
d="M555 0h-152v76q-67 -86 -157 -86q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536zM353 595h-116l113 177l131 -57z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="623" 
d="M555 0h-152v76q-67 -86 -157 -86q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536zM310 661l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="623" 
d="M555 0h-152v76q-67 -86 -157 -86q-86 0 -134 53t-48 146v347h152v-299q0 -53 24 -81t68 -28t69.5 28t25.5 81v299h152v-536zM491 595h-146v135h146v-135zM277 595h-146v135h146v-135z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="600" 
d="M578 536l-206 -549q-31 -83 -70 -116.5t-106 -33.5q-72 0 -135 37l51 110q35 -21 65 -21q36 0 53 35l-210 538h161l122 -365l117 365h158zM345 595h-116l113 177l131 -57z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="675" 
d="M631 268q0 -129 -71.5 -203.5t-172.5 -74.5q-100 0 -167 80v-230h-152v890h152v-271q64 87 167 87q100 0 172 -74.5t72 -203.5zM479 268q0 66 -38 107.5t-93 41.5t-92.5 -41t-37.5 -108t37.5 -108t92.5 -41t93 41.5t38 107.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="600" 
d="M578 536l-206 -549q-31 -83 -70 -116.5t-106 -33.5q-72 0 -135 37l51 110q35 -21 65 -21q36 0 53 35l-210 538h161l122 -365l117 365h158zM480 595h-146v135h146v-135zM266 595h-146v135h146v-135z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="302" 
d="M227 0h-152v536h152v-536z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="649" 
d="M617 0h-503v262l-75 -22v143l75 22v295h152v-249l192 57v-143l-192 -57v-168h351v-140z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="362" 
d="M256 324v-324h-150v280l-76 -23v143l76 23v307h150v-263l76 23v-143z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1075" 
d="M1022 0h-597q-163 0 -267.5 99.5t-104.5 249.5t104.5 250.5t267.5 100.5h592v-137h-350v-142h305v-137h-305v-147h355v-137zM514 139v422h-89q-94 0 -152.5 -58t-58.5 -152t58.5 -153t152.5 -59h89z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1033" 
d="M532 83q-80 -95 -210 -95q-115 0 -197.5 81t-82.5 197t83.5 198t202.5 82q128 0 207 -94q73 94 196 94q124 0 190.5 -84.5t66.5 -207.5q0 -11 -2 -39h-373q24 -105 131 -105q71 0 130 55l87 -77q-81 -100 -219 -100q-136 0 -210 95zM611 310h229q-7 52 -36.5 83t-76.5 31
q-46 0 -76.5 -30.5t-39.5 -83.5zM462 266q0 62 -38 105.5t-98 43.5q-61 0 -97.5 -42.5t-36.5 -104.5t38 -105.5t98 -43.5q61 0 97.5 42.5t36.5 104.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="640" 
d="M590 207q0 -102 -70.5 -159.5t-186.5 -57.5q-172 0 -297 112l91 109q103 -85 209 -85q47 0 74 17.5t27 48.5v2q0 30 -28 48.5t-113 40.5q-60 15 -97 29.5t-70.5 38.5t-49 59.5t-15.5 86.5v2q0 95 68.5 153t176.5 58q149 0 260 -89l-80 -116q-99 69 -182 69
q-43 0 -66.5 -17t-23.5 -44v-2q0 -34 29 -51t122 -41q114 -30 168 -76.5t54 -133.5v-2zM318 846l79 66h115l-123 -157h-144l-123 157h117z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="502" 
d="M461 163q0 -86 -56.5 -129.5t-148.5 -43.5q-127 0 -230 81l65 100q88 -64 169 -64q64 0 64 40v2q0 5 -1.5 10t-5.5 9t-8.5 7.5t-12 7t-13 6t-15.5 6t-17 5.5t-19 5.5t-19 5.5q-34 10 -58 20.5t-49.5 28.5t-39 46.5t-13.5 65.5v2q0 79 56 124.5t140 45.5q108 0 200 -62
l-58 -105q-85 50 -145 50q-27 0 -41.5 -10.5t-14.5 -26.5v-2q0 -27 92 -58q11 -4 17 -6q35 -12 58.5 -22.5t50 -29t40 -45.5t13.5 -62v-2zM256 686l79 66h115l-123 -157h-144l-123 157h117z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="716" 
d="M704 700l-269 -421v-279h-154v276l-269 424h180l167 -281l170 281h175zM538 755h-146v135h146v-135zM324 755h-146v135h146v-135z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="708" 
d="M646 0h-583v117l385 448h-373v135h571v-117l-385 -448h385v-135zM358 846l79 66h115l-123 -157h-144l-123 157h117z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="563" 
d="M516 0h-466v107l277 306h-268v123h457v-107l-278 -306h278v-123zM290 686l79 66h115l-123 -157h-144l-123 157h117z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="536" 
d="M500 567q-40 10 -68 10q-63 0 -75 -68l-7 -40h115v-125h-137l-33 -187q-16 -87 -62 -124.5t-133 -37.5q-36 0 -59 5v123q20 -3 37 -3q32 0 48 14t22 47l28 163h-67v125h89l14 74q15 81 59 121.5t130 40.5q61 0 99 -12v-126z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M249 661l-79 -66h-115l123 157h144l123 -157h-117z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M251 686l79 66h115l-123 -157h-144l-123 157h117z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M438 752q-7 -75 -57.5 -117t-130.5 -42t-130.5 42t-57.5 117h102q20 -60 86 -60t86 60h102z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M325 595h-150v135h150v-135z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M371 710q0 -48 -36.5 -82.5t-84.5 -34.5t-84.5 34.5t-36.5 82.5t36.5 82.5t84.5 34.5t84.5 -34.5t36.5 -82.5zM312 710q0 26 -18 44t-44 18t-44 -18t-18 -44t18 -44t44 -18t44 18t18 44z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M291 10q-6 -21 -6 -39q0 -63 86 -68v-67q-95 -8 -147.5 20.5t-52.5 88.5q0 31 16 65h104z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M456 733q-19 -69 -47 -103.5t-77 -34.5q-34 0 -84.5 19.5t-65.5 19.5q-22 0 -33.5 -11.5t-23.5 -37.5l-81 25q19 69 47 103.5t77 34.5q34 0 84.5 -19.5t65.5 -19.5q22 0 33.5 11.5t23.5 37.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M342 595h-111l113 177l128 -57zM147 595h-111l111 177l128 -57z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="764" 
d="M626 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21zM244 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="830" 
d="M777 351q0 -149 -106 -256t-257 -107t-256 106t-105 255t106 256t257 107t256 -106t105 -255zM735 351q0 134 -91.5 227.5t-227.5 93.5t-228.5 -94t-92.5 -229q0 -134 91.5 -227.5t227.5 -93.5t228.5 94t92.5 229zM588 415q0 -64 -44 -99.5t-112 -35.5h-64v-116h-86v385
h158q68 0 108 -36.5t40 -97.5zM501 414q0 28 -18.5 43.5t-50.5 15.5h-64v-118h66q31 0 49 16.5t18 42.5z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="528" 
d="M478 239h-428v142h428v-142z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="898" 
d="M848 239h-798v142h798v-142z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="286" 
d="M255 642q-106 -11 -98 -99h62v-160h-162v135q0 96 46.5 140.5t138.5 46.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="286" 
d="M31 441q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="286" 
d="M26 -99q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="540" 
d="M509 642q-106 -11 -98 -99h62v-160h-162v135q0 96 46.5 140.5t138.5 46.5zM255 642q-106 -11 -98 -99h62v-160h-162v135q0 96 46.5 140.5t138.5 46.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="540" 
d="M285 441q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5zM31 441q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="540" 
d="M280 -99q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5zM26 -99q106 11 98 99h-62v160h162v-135q0 -96 -46.5 -140.5t-138.5 -46.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="410" 
d="M237 508l10 -270h-84l10 270l-120 -10v83l119 -10l-13 129h92l-13 -129l119 10v-83z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="430" 
d="M367 119l-119 10l13 -129h-92l13 129l-119 -10v83l119 -10l-9 158l9 158l-119 -10v83l119 -10l-13 129h92l-13 -129l119 10v-83l-119 10l9 -158l-9 -158l119 10v-83z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M399 352q0 -62 -43.5 -105.5t-105.5 -43.5t-105.5 43.5t-43.5 105.5t43.5 105.5t105.5 43.5t105.5 -43.5t43.5 -105.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="840" 
d="M778 0h-158v156h158v-156zM499 0h-158v156h158v-156zM220 0h-158v156h158v-156z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1240" 
d="M385 530q0 -76 -48 -128t-123 -52t-122 51t-47 127t48 128t123 52t122 -51t47 -127zM626 700h119l-511 -700h-119zM1195 172q0 -76 -48 -128t-123 -52t-122 51t-47 127t48 128t123 52t122 -51t47 -127zM815 172q0 -76 -48 -128t-123 -52t-122 51t-47 127t48 128t123 52
t122 -51t47 -127zM280 528q0 37 -18.5 63t-47.5 26q-30 0 -47 -25t-17 -62t18.5 -63t47.5 -26q30 0 47 25t17 62zM1090 170q0 37 -18.5 63t-47.5 26q-30 0 -47 -25t-17 -62t18.5 -63t47.5 -26q30 0 47 25t17 62zM710 170q0 37 -18.5 63t-47.5 26q-30 0 -47 -25t-17 -62
t18.5 -63t47.5 -26q30 0 47 25t17 62z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="373" 
d="M209 496l116 -56l-130 -171l130 -171l-116 -58l-168 224v8z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="373" 
d="M164 40l-116 56l130 171l-130 171l116 58l168 -224v-8z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="480" 
d="M395 758h134l-466 -856h-134z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="690" 
d="M587 600l-85 -135h-6l-85 135v-208h-66v308h74l81 -130l81 130h72v-308h-66v208zM188 638v-246h-70v246h-92v62h254v-62h-92z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1066" 
d="M626 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21zM244 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21zM991 0h-152v730h152
v-730z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="1066" 
d="M995 595h-160v135h160v-135zM626 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21zM244 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13
q-55 0 -55 -58v-21zM991 0h-152v536h152v-536z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="967" 
d="M868 0h-767v750h767v-750zM859 -104h-6l-3 20l-10 -21h-2l-10 21l-3 -20h-6l6 34h1l13 -26l13 26h1zM806 -87q0 -18 -17 -18q-7 0 -12 5t-5 12q0 18 18 18q7 0 11.5 -5t4.5 -12zM758 -96l-1 -6q-3 -3 -10 -3q-18 0 -18 17q0 8 5.5 13t12.5 5t11 -3l-2 -5q-3 3 -9 3
q-12 0 -12 -12q0 -13 13 -13q4 0 10 4zM702 -71l-13 -19v-14h-6v14l-14 19l6 1l11 -15l9 14h7zM656 -104h-6v15h-16v-15h-6v33h6v-13h16v13h6v-33zM612 -82q0 -12 -17 -12v-10h-6v33q23 3 23 -11zM576 -104h-6l-4 8h-14l-3 -8h-6l15 34h2zM534 -104h-7l-8 11h-5v-11h-6v33
q23 3 23 -10q0 -7 -7 -10zM491 -101q-6 -4 -13 -4q-18 0 -18 17q0 8 5.5 13t13.5 5q6 0 12 -4l-2 -5q-4 4 -11 4q-12 0 -12 -13q0 -12 13 -12q2 0 6 2v7h-9v5h15v-15zM447 -87q0 -18 -17 -18q-7 0 -12 5t-5 12q0 18 18 18q7 0 11.5 -5t4.5 -12zM400 -82q0 -12 -17 -12v-10
h-6v33q23 3 23 -11zM364 -71l-13 -19v-14h-6v14l-14 19l6 1l11 -15l9 14h7zM323 -71v-5h-12v-28h-6v28h-12v5h30zM267 -71l-12 -34h-1l-11 23l-10 -23h-1l-12 34l6 1l7 -23l10 23h1l10 -23l7 22h6zM211 -71l-12 -34h-1l-11 23l-10 -23h-1l-12 34l6 1l7 -23l10 23h1l10 -23
l7 22h6zM155 -71l-12 -34h-1l-11 23l-10 -23h-1l-12 34l6 1l7 -23l10 23h1l10 -23l7 22h6zM716 -101q0 -4 -4 -4t-4 4t4 4t4 -4zM284 -101q0 -4 -4 -4t-4 4t4 4t4 -4zM559 686q0 8 -1 12q-2 -1 -17 -1t-18 1l-1 -12h2q4 9 10 9q3 0 3 -3v-23q-1 -5 -5 -5v-2h17v2l-4 2
q-1 1 -1 3v23q0 3 3 3t5.5 -2.5t3.5 -4.5l1 -2h2zM504 662v2q-6 -1 -6 5v21l6 5v2h-18v-1v-1l4 -2q1 -1 1 -3v-9h-17v9q1 5 5 5v1v1h-17v-2l4 -2q1 -1 1 -3v-21l-5 -5v-2h17v2q-5 0 -5 5v10h17v-10q-1 -5 -5 -5v-2h18zM439 664l-4 2q-1 1 -1 3v21q1 5 5 5v2h-29l-2 -10l2 -1
q3 8 9 8h4q4 0 4 -3v-10h-4l-6 5h-2v-13h2q0 6 6 6h4v-11q0 -3 -4 -3h-4q-3 0 -5.5 2.5t-3.5 4.5l-1 2l-2 -1l2 -11h30v2zM827 618v6h-685v-6h685zM696 540q0 15 -11.5 26.5t-25.5 11.5q-8 0 -15.5 -5.5t-7.5 -16.5q0 -16 11 -27t26 -11q8 0 15.5 5.5t7.5 16.5zM589 537
q0 12 -14 17q5 3 5 9q0 15 -21 15q-13 0 -13 -11q0 -6 5 -6q4 0 4 3q0 2 -1.5 3.5t-1.5 2.5q0 5 7 5q10 0 10 -12q0 -4 -2 -6q-16 2 -16 -6q0 -6 8 -6q6 0 11 5q6 -4 6 -15q0 -14 -12 -14t-12 9q0 3 3 3l1 -1q2 -1 4 -1q4 0 4 5t-7 5q-9 0 -9 -10q0 -13 16 -13q25 0 25 19z
M295 537q0 12 -14 17q5 3 5 9q0 15 -21 15q-13 0 -13 -11q0 -6 5 -6q4 0 4 3q0 2 -1.5 3.5t-1.5 2.5q0 5 7 5q10 0 10 -12q0 -4 -2 -6q-16 2 -16 -6q0 -6 8 -6q6 0 11 5q6 -4 6 -15q0 -14 -12 -14t-12 9q0 3 3 3l1 -1q2 -1 4 -1q4 0 4 5t-7 5q-9 0 -9 -10q0 -13 16 -13
q25 0 25 19zM820 522q-8 -1 -10 8l-7 35q-1 4 1 6t4 2h2l-1 3h-28l1 -3q7 1 9 -8l3 -15h-24l-3 15q-1 4 1 6t4 2h2l-1 3h-29l1 -3q8 1 10 -8l7 -35q1 -4 -1 -6t-4 -2h-2l1 -3h29l-1 3q-8 -1 -10 8l-3 16h24l3 -16q1 -4 -1 -6t-4 -2h-2l1 -3h29zM498 519l-1 3q-8 -1 -10 8
l-7 35q-1 4 1 6t4 2h2l-1 3h-45v-17h4q-1 13 13 13h4q5 0 6 -6l3 -16h-4q-4 0 -7 2t-4 4l-1 3h-3l5 -23h3q-1 11 8 11h4l3 -17q1 -4 -1 -6t-4 -2h-2l1 -3h29zM202 522q-8 -1 -10 8l-7 35q-1 4 1 6t4 2h2l-1 3h-27q-20 0 -20 -15q0 -13 19 -15l-9 -16q-2 -4 -5.5 -6t-6.5 -2
h-2l1 -3h19l12 23q2 3 5 3l3 -15q1 -4 -1 -6t-4 -2h-2l1 -3h29zM400 521q0 8 -13 9q-4 2 -5 14.5t-5.5 23t-18.5 10.5q-5 0 -9 -3.5t-4 -6.5q0 -5 5 -5q4 0 4 6t5 6t7 -8t4.5 -19.5t9.5 -17.5q-8 0 -14 -6.5t-9 -6.5q-5 0 -5 4q0 2 1 3t1 2q0 5 -5 5t-5 -7q0 -4 5 -8t10 -4
q10 0 15 6.5t10 6.5q4 -7 12 -7q4 0 4 3zM827 475v6h-685v-6h685zM827 378v62h-147v-62h36v28h11v-65h-14v-33h81v33h-14v65h11v-28h36zM648 406v34h-81v-34h11l-15 -23l-15 23h10v34h-62v-34h12l37 -46v-19h-14v-33h81v33h-15v20l37 45h14zM458 308v33h-14v65h14v34h-96
q-21 0 -34 -11.5t-13 -28.5q0 -18 13 -29t34 -11h30v-19h-15v-33h81zM283 308v33h-14v65h14v34h-141v-62h41v28h33v-20h-24v-24h24v-21h-33v28h-41v-61h141zM827 271v6h-685v-6h685zM728 192q0 15 -11.5 26.5t-25.5 11.5q-8 0 -15.5 -5.5t-7.5 -16.5q0 -16 11 -27t26 -11
q8 0 15.5 5.5t7.5 16.5zM203 217q-7 13 -18 13q-15 0 -14 -23q-7 23 -21 23q-12 0 -12 -12l3 -1q0 6 8 6q16 0 24 -33q2 -8 0.5 -11.5t-3.5 -3.5h-3l1 -4h28l-1 4q-7 -2 -10 9q-5 17 -5 25q0 13 9 13q5 0 12 -7zM823 171l-1 3q-8 -1 -10 8l-7 35q-1 4 1 6t4 2h2l-1 3h-45
v-17h4q-1 13 13 13h4q5 0 6 -6l3 -16h-4q-4 0 -7 2t-4 4l-1 3h-3l5 -23h3q-1 11 8 11h4l3 -17q1 -4 -1 -6t-4 -2h-2l1 -3h29zM621 225l-1 3h-29l1 -3q8 1 10 -9l5 -21q5 -20 -11 -20q-14 0 -20 22l-4 19q-1 5 1 7t4 2h2v3h-24l1 -3q9 1 11 -9l4 -19q5 -27 28 -27q24 0 19 24
l-5 22q-1 5 1 7t4 2h3zM522 174q-6 0 -9 10l-7 32q-1 4 1 6.5t5 2.5h2l-1 3h-14l-29 -42l-5 30q-1 4 1 6t4 2l2 1l-1 3h-21l1 -3q7 0 9 -9l8 -45l3 -1l30 44l6 -30q1 -5 -0.5 -7.5t-3.5 -2.5h-2l1 -3h21zM414 171l-1 4q-7 -2 -10 9l-8 31q-3 9 8 10l-1 3h-30q-25 0 -25 -22
q0 -15 11 -25t24 -10h32zM306 174q-8 -1 -10 8l-7 35q-1 4 1 6t4 2h2l-1 3h-27q-20 0 -20 -15q0 -13 19 -15l-9 -16q-2 -4 -5.5 -6t-6.5 -2h-2l1 -3h19l12 23q2 3 5 3l3 -15q1 -4 -1 -6t-4 -2h-2l1 -3h29zM827 127v6h-685v-6h685zM694 46v2q-6 1 -6 5v20l6 6v2h-10l-24 -26
v18q0 2 1.5 3.5t2.5 1.5l2 1v2h-15v-2q6 -1 6 -6v-28h3l25 28v-20l-5 -5v-2h14zM633 48l-4 2q-1 1 -1 3v21q1 5 5 5v2h-29l-2 -10l2 -1q3 8 9 8h4q4 0 4 -3v-10h-4l-6 5h-2v-13h2q0 6 6 6h4v-11q0 -3 -4 -3h-4q-3 0 -5.5 2.5t-3.5 4.5l-1 2l-2 -1l2 -11h30v2zM583 81h-15v-2
l2 -5l-9 -19l-5 10l4 8l6 6v2h-26l1 -2q3 0 7 -6l3 -5l-7 -13l-8 18l2 6v2h-13v-2q4 -1 6 -6l13 -27h1q1 -1 1 0l8 16l9 -16h1l12 27q1 3 3 4.5t3 1.5h1v2zM480 79v2h-15v-2l1 -5l-6 -9l-6 9l3 5v2h-15v-2q5 0 8 -5l8 -12v-8l-5 -6v-2h17v2q-5 0 -5 6v8l8 12zM425 64
q0 18 -20 18t-20 -18q0 -19 20 -19t20 19zM364 46v2q-5 0 -5 5v20l5 6v2h-18q-13 0 -13 -9q0 -5 9 -9l-7 -10l-8 -5v-2h11l7 12q3 4 7 4v-9l-5 -5v-2h17zM309 46v2q-6 1 -6 6v20l6 5v2h-18v-2q6 0 6 -5v-9q-2 0 -4 2l-8 8l1 4v2h-14v-2q5 -1 8 -4l10 -9l-13 -13l-7 -5v-2h17
v2l-2 5l8 8q2 2 4 2v-9q0 -3 -1.5 -4.5t-3.5 -1.5h-1v-2h18zM606 -83q0 8 -11 7v-13q11 0 11 6zM525 -82q0 8 -11 6v-12q11 -1 11 6zM394 -83q0 8 -11 7v-13q11 0 11 6zM564 -92l-5 12l-5 -12h10zM800 -88q0 13 -11 13t-11 -12q0 -13 11 -13t11 12zM441 -88q0 13 -11 13
t-11 -12q0 -13 11 -13t11 12zM176 548h-5q-15 0 -15 15q0 9 8 9t9 -5zM566 553q-3 -4 -7 -4t-4 2q0 3 4 3q5 0 7 -1zM272 553q-3 -4 -7 -4t-4 2q0 3 4 3q5 0 7 -1zM684 535q0 -13 -11 -13q-9 0 -17 11.5t-8 26.5q0 14 11 14q9 0 17 -12t8 -27zM392 384h-11q-13 0 -13 11
t13 11h11v-22zM280 200h-5q-15 0 -15 15q0 9 8 9t9 -5zM716 187q0 -13 -11 -13q-9 0 -17 11.5t-8 26.5q0 14 11 14q9 0 17 -12t8 -27zM393 182q0 -7 -4 -7h-5q-11 0 -18.5 10t-7.5 22q0 17 14 17h6q4 -1 6 -7zM553 79q5 0 0 -9q-5 9 0 9zM352 64h-3q-8 0 -8 8q0 6 7 6
q4 0 4 -4v-10zM417 64q0 -17 -12 -17t-12 17q0 16 12 16t12 -16z" />
    <glyph glyph-name="fi" unicode="&#xfb01;" horiz-adv-x="684" 
d="M613 595h-160v135h160v-135zM244 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21zM609 0h-152v536h152v-536z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="684" 
d="M244 530h123v-124h-121v-406h-152v406h-63v125h63v34q0 172 166 172q61 0 108 -15v-126q-36 13 -69 13q-55 0 -55 -58v-21zM609 0h-152v730h152v-730z" />
    <glyph glyph-name="NUL" horiz-adv-x="0" 
 />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x26;" u2="Y" k="60" />
    <hkern u1="&#x26;" u2="W" k="45" />
    <hkern u1="&#x26;" u2="V" k="55" />
    <hkern u1="&#x26;" u2="T" k="50" />
    <hkern u1="&#x28;" u2="&#x153;" k="30" />
    <hkern u1="&#x28;" u2="&#x152;" k="30" />
    <hkern u1="&#x28;" u2="&#xb5;" k="30" />
    <hkern u1="&#x28;" u2="s" k="15" />
    <hkern u1="&#x28;" u2="q" k="30" />
    <hkern u1="&#x28;" u2="o" k="30" />
    <hkern u1="&#x28;" u2="j" k="-30" />
    <hkern u1="&#x28;" u2="g" k="20" />
    <hkern u1="&#x28;" u2="e" k="30" />
    <hkern u1="&#x28;" u2="d" k="30" />
    <hkern u1="&#x28;" u2="c" k="30" />
    <hkern u1="&#x28;" u2="Q" k="30" />
    <hkern u1="&#x28;" u2="O" k="30" />
    <hkern u1="&#x28;" u2="J" k="15" />
    <hkern u1="&#x28;" u2="G" k="30" />
    <hkern u1="&#x28;" u2="C" k="30" />
    <hkern u1="&#x2a;" u2="&#x153;" k="20" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="100" />
    <hkern u1="&#x2a;" u2="t" k="-10" />
    <hkern u1="&#x2a;" u2="s" k="10" />
    <hkern u1="&#x2a;" u2="q" k="15" />
    <hkern u1="&#x2a;" u2="o" k="20" />
    <hkern u1="&#x2a;" u2="g" k="15" />
    <hkern u1="&#x2a;" u2="e" k="20" />
    <hkern u1="&#x2a;" u2="d" k="15" />
    <hkern u1="&#x2a;" u2="c" k="20" />
    <hkern u1="&#x2a;" u2="a" k="10" />
    <hkern u1="&#x2a;" u2="J" k="80" />
    <hkern u1="&#x2a;" u2="A" k="100" />
    <hkern u1="&#x2c;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2c;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2c;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2c;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2c;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2c;" u2="&#x153;" k="20" />
    <hkern u1="&#x2c;" u2="&#x152;" k="40" />
    <hkern u1="&#x2c;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2c;" u2="y" k="45" />
    <hkern u1="&#x2c;" u2="w" k="70" />
    <hkern u1="&#x2c;" u2="v" k="85" />
    <hkern u1="&#x2c;" u2="t" k="25" />
    <hkern u1="&#x2c;" u2="q" k="10" />
    <hkern u1="&#x2c;" u2="o" k="20" />
    <hkern u1="&#x2c;" u2="j" k="-15" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2c;" u2="f" k="15" />
    <hkern u1="&#x2c;" u2="e" k="20" />
    <hkern u1="&#x2c;" u2="d" k="10" />
    <hkern u1="&#x2c;" u2="c" k="20" />
    <hkern u1="&#x2c;" u2="Y" k="130" />
    <hkern u1="&#x2c;" u2="W" k="100" />
    <hkern u1="&#x2c;" u2="V" k="120" />
    <hkern u1="&#x2c;" u2="U" k="15" />
    <hkern u1="&#x2c;" u2="T" k="100" />
    <hkern u1="&#x2c;" u2="Q" k="40" />
    <hkern u1="&#x2c;" u2="O" k="40" />
    <hkern u1="&#x2c;" u2="G" k="40" />
    <hkern u1="&#x2c;" u2="C" k="40" />
    <hkern u1="&#x2c;" u2="&#x37;" k="20" />
    <hkern u1="&#x2c;" u2="&#x31;" k="50" />
    <hkern u1="&#x2c;" u2="&#x30;" k="20" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2d;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2d;" u2="z" k="10" />
    <hkern u1="&#x2d;" u2="y" k="15" />
    <hkern u1="&#x2d;" u2="x" k="30" />
    <hkern u1="&#x2d;" u2="w" k="10" />
    <hkern u1="&#x2d;" u2="v" k="15" />
    <hkern u1="&#x2d;" u2="Z" k="30" />
    <hkern u1="&#x2d;" u2="Y" k="80" />
    <hkern u1="&#x2d;" u2="X" k="50" />
    <hkern u1="&#x2d;" u2="W" k="35" />
    <hkern u1="&#x2d;" u2="V" k="40" />
    <hkern u1="&#x2d;" u2="T" k="90" />
    <hkern u1="&#x2d;" u2="A" k="40" />
    <hkern u1="&#x2d;" u2="&#x37;" k="40" />
    <hkern u1="&#x2d;" u2="&#x33;" k="10" />
    <hkern u1="&#x2d;" u2="&#x31;" k="30" />
    <hkern u1="&#x2e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="40" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="40" />
    <hkern u1="&#x2e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2e;" u2="&#x153;" k="20" />
    <hkern u1="&#x2e;" u2="&#x152;" k="40" />
    <hkern u1="&#x2e;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2e;" u2="y" k="60" />
    <hkern u1="&#x2e;" u2="w" k="70" />
    <hkern u1="&#x2e;" u2="v" k="85" />
    <hkern u1="&#x2e;" u2="t" k="25" />
    <hkern u1="&#x2e;" u2="q" k="10" />
    <hkern u1="&#x2e;" u2="o" k="20" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="f" k="15" />
    <hkern u1="&#x2e;" u2="e" k="20" />
    <hkern u1="&#x2e;" u2="d" k="10" />
    <hkern u1="&#x2e;" u2="c" k="20" />
    <hkern u1="&#x2e;" u2="Y" k="130" />
    <hkern u1="&#x2e;" u2="W" k="100" />
    <hkern u1="&#x2e;" u2="V" k="120" />
    <hkern u1="&#x2e;" u2="U" k="15" />
    <hkern u1="&#x2e;" u2="T" k="100" />
    <hkern u1="&#x2e;" u2="Q" k="40" />
    <hkern u1="&#x2e;" u2="O" k="40" />
    <hkern u1="&#x2e;" u2="G" k="40" />
    <hkern u1="&#x2e;" u2="C" k="40" />
    <hkern u1="&#x2e;" u2="&#x37;" k="20" />
    <hkern u1="&#x2e;" u2="&#x31;" k="50" />
    <hkern u1="&#x2e;" u2="&#x30;" k="20" />
    <hkern u1="&#x2f;" u2="&#xfb02;" k="25" />
    <hkern u1="&#x2f;" u2="&#xfb01;" k="25" />
    <hkern u1="&#x2f;" u2="&#x2248;" k="25" />
    <hkern u1="&#x2f;" u2="&#x221a;" k="25" />
    <hkern u1="&#x2f;" u2="&#x3a9;" k="25" />
    <hkern u1="&#x2f;" u2="&#x153;" k="80" />
    <hkern u1="&#x2f;" u2="&#x152;" k="40" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="65" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="120" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2f;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2f;" u2="z" k="60" />
    <hkern u1="&#x2f;" u2="y" k="50" />
    <hkern u1="&#x2f;" u2="x" k="50" />
    <hkern u1="&#x2f;" u2="w" k="50" />
    <hkern u1="&#x2f;" u2="v" k="50" />
    <hkern u1="&#x2f;" u2="u" k="50" />
    <hkern u1="&#x2f;" u2="t" k="20" />
    <hkern u1="&#x2f;" u2="s" k="85" />
    <hkern u1="&#x2f;" u2="r" k="50" />
    <hkern u1="&#x2f;" u2="q" k="70" />
    <hkern u1="&#x2f;" u2="p" k="50" />
    <hkern u1="&#x2f;" u2="o" k="80" />
    <hkern u1="&#x2f;" u2="n" k="50" />
    <hkern u1="&#x2f;" u2="m" k="50" />
    <hkern u1="&#x2f;" u2="g" k="70" />
    <hkern u1="&#x2f;" u2="f" k="25" />
    <hkern u1="&#x2f;" u2="e" k="80" />
    <hkern u1="&#x2f;" u2="d" k="70" />
    <hkern u1="&#x2f;" u2="c" k="80" />
    <hkern u1="&#x2f;" u2="a" k="65" />
    <hkern u1="&#x2f;" u2="Z" k="20" />
    <hkern u1="&#x2f;" u2="S" k="30" />
    <hkern u1="&#x2f;" u2="Q" k="40" />
    <hkern u1="&#x2f;" u2="O" k="40" />
    <hkern u1="&#x2f;" u2="J" k="130" />
    <hkern u1="&#x2f;" u2="G" k="40" />
    <hkern u1="&#x2f;" u2="C" k="40" />
    <hkern u1="&#x2f;" u2="A" k="120" />
    <hkern u1="&#x2f;" u2="&#x39;" k="20" />
    <hkern u1="&#x2f;" u2="&#x38;" k="15" />
    <hkern u1="&#x2f;" u2="&#x37;" k="10" />
    <hkern u1="&#x2f;" u2="&#x36;" k="35" />
    <hkern u1="&#x2f;" u2="&#x35;" k="20" />
    <hkern u1="&#x2f;" u2="&#x34;" k="95" />
    <hkern u1="&#x2f;" u2="&#x33;" k="10" />
    <hkern u1="&#x2f;" u2="&#x32;" k="20" />
    <hkern u1="&#x2f;" u2="&#x31;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x30;" k="35" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="160" />
    <hkern u1="&#x30;" u2="&#xb1;" k="15" />
    <hkern u1="&#x30;" u2="&#x37;" k="25" />
    <hkern u1="&#x30;" u2="&#x33;" k="10" />
    <hkern u1="&#x30;" u2="&#x32;" k="10" />
    <hkern u1="&#x30;" u2="&#x31;" k="5" />
    <hkern u1="&#x30;" u2="&#x2f;" k="35" />
    <hkern u1="&#x30;" u2="&#x2e;" k="20" />
    <hkern u1="&#x30;" u2="&#x2c;" k="20" />
    <hkern u1="&#x31;" u2="&#x2044;" k="-55" />
    <hkern u1="&#x32;" u2="&#x2044;" k="-60" />
    <hkern u1="&#x32;" u2="&#x37;" k="5" />
    <hkern u1="&#x32;" u2="&#x34;" k="15" />
    <hkern u1="&#x33;" u2="&#x2044;" k="-30" />
    <hkern u1="&#x33;" u2="&#x39;" k="5" />
    <hkern u1="&#x33;" u2="&#x37;" k="20" />
    <hkern u1="&#x33;" u2="&#x35;" k="5" />
    <hkern u1="&#x33;" u2="&#x2f;" k="10" />
    <hkern u1="&#x34;" u2="&#x2044;" k="-30" />
    <hkern u1="&#x34;" u2="&#x39;" k="10" />
    <hkern u1="&#x34;" u2="&#x37;" k="30" />
    <hkern u1="&#x34;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x2f;" k="20" />
    <hkern u1="&#x35;" u2="&#x2044;" k="-20" />
    <hkern u1="&#x35;" u2="&#xb1;" k="5" />
    <hkern u1="&#x35;" u2="&#x39;" k="5" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x33;" k="5" />
    <hkern u1="&#x35;" u2="&#x32;" k="10" />
    <hkern u1="&#x35;" u2="&#x2f;" k="20" />
    <hkern u1="&#x36;" u2="&#x2044;" k="-30" />
    <hkern u1="&#x36;" u2="&#xb1;" k="5" />
    <hkern u1="&#x36;" u2="&#x39;" k="10" />
    <hkern u1="&#x36;" u2="&#x37;" k="15" />
    <hkern u1="&#x36;" u2="&#x33;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="10" />
    <hkern u1="&#x36;" u2="&#x2f;" k="10" />
    <hkern u1="&#x37;" u2="&#x2044;" k="110" />
    <hkern u1="&#x37;" u2="&#x2014;" k="30" />
    <hkern u1="&#x37;" u2="&#x2013;" k="30" />
    <hkern u1="&#x37;" u2="&#xb1;" k="10" />
    <hkern u1="&#x37;" u2="&#x39;" k="15" />
    <hkern u1="&#x37;" u2="&#x38;" k="10" />
    <hkern u1="&#x37;" u2="&#x36;" k="20" />
    <hkern u1="&#x37;" u2="&#x35;" k="25" />
    <hkern u1="&#x37;" u2="&#x34;" k="85" />
    <hkern u1="&#x37;" u2="&#x33;" k="20" />
    <hkern u1="&#x37;" u2="&#x32;" k="15" />
    <hkern u1="&#x37;" u2="&#x31;" k="-10" />
    <hkern u1="&#x37;" u2="&#x30;" k="20" />
    <hkern u1="&#x37;" u2="&#x2f;" k="140" />
    <hkern u1="&#x37;" u2="&#x2e;" k="100" />
    <hkern u1="&#x37;" u2="&#x2d;" k="30" />
    <hkern u1="&#x37;" u2="&#x2c;" k="100" />
    <hkern u1="&#x38;" u2="&#x2044;" k="-40" />
    <hkern u1="&#x38;" u2="&#xb1;" k="5" />
    <hkern u1="&#x38;" u2="&#x39;" k="5" />
    <hkern u1="&#x38;" u2="&#x37;" k="10" />
    <hkern u1="&#x39;" u2="&#x2044;" k="-15" />
    <hkern u1="&#x39;" u2="&#xb1;" k="10" />
    <hkern u1="&#x39;" u2="&#x37;" k="10" />
    <hkern u1="&#x39;" u2="&#x35;" k="5" />
    <hkern u1="&#x39;" u2="&#x33;" k="10" />
    <hkern u1="&#x39;" u2="&#x32;" k="10" />
    <hkern u1="&#x39;" u2="&#x2f;" k="25" />
    <hkern u1="&#x39;" u2="&#x2e;" k="10" />
    <hkern u1="&#x39;" u2="&#x2c;" k="10" />
    <hkern u1="&#x3a;" u2="Y" k="40" />
    <hkern u1="&#x3a;" u2="W" k="15" />
    <hkern u1="&#x3a;" u2="V" k="20" />
    <hkern u1="&#x3a;" u2="T" k="10" />
    <hkern u1="&#x3b;" u2="Y" k="40" />
    <hkern u1="&#x3b;" u2="W" k="15" />
    <hkern u1="&#x3b;" u2="V" k="20" />
    <hkern u1="&#x3b;" u2="T" k="10" />
    <hkern u1="A" u2="&#xfb02;" k="20" />
    <hkern u1="A" u2="&#xfb01;" k="20" />
    <hkern u1="A" u2="&#x2248;" k="20" />
    <hkern u1="A" u2="&#x221a;" k="20" />
    <hkern u1="A" u2="&#x2122;" k="100" />
    <hkern u1="A" u2="&#x201d;" k="80" />
    <hkern u1="A" u2="&#x201c;" k="80" />
    <hkern u1="A" u2="&#x2019;" k="80" />
    <hkern u1="A" u2="&#x2018;" k="80" />
    <hkern u1="A" u2="&#x2014;" k="40" />
    <hkern u1="A" u2="&#x2013;" k="40" />
    <hkern u1="A" u2="&#x3a9;" k="20" />
    <hkern u1="A" u2="&#x153;" k="25" />
    <hkern u1="A" u2="&#x152;" k="45" />
    <hkern u1="A" u2="&#xc6;" k="20" />
    <hkern u1="A" u2="&#xb5;" k="45" />
    <hkern u1="A" u2="&#xab;" k="20" />
    <hkern u1="A" u2="y" k="50" />
    <hkern u1="A" u2="w" k="55" />
    <hkern u1="A" u2="v" k="70" />
    <hkern u1="A" u2="u" k="10" />
    <hkern u1="A" u2="t" k="30" />
    <hkern u1="A" u2="q" k="25" />
    <hkern u1="A" u2="o" k="25" />
    <hkern u1="A" u2="g" k="25" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="A" u2="e" k="25" />
    <hkern u1="A" u2="d" k="25" />
    <hkern u1="A" u2="c" k="25" />
    <hkern u1="A" u2="\" k="120" />
    <hkern u1="A" u2="Y" k="110" />
    <hkern u1="A" u2="X" k="20" />
    <hkern u1="A" u2="W" k="90" />
    <hkern u1="A" u2="V" k="100" />
    <hkern u1="A" u2="U" k="25" />
    <hkern u1="A" u2="T" k="90" />
    <hkern u1="A" u2="S" k="10" />
    <hkern u1="A" u2="Q" k="45" />
    <hkern u1="A" u2="O" k="45" />
    <hkern u1="A" u2="G" k="45" />
    <hkern u1="A" u2="C" k="45" />
    <hkern u1="A" u2="A" k="20" />
    <hkern u1="A" u2="&#x3f;" k="60" />
    <hkern u1="A" u2="&#x2d;" k="40" />
    <hkern u1="A" u2="&#x2a;" k="100" />
    <hkern u1="B" u2="y" k="10" />
    <hkern u1="B" u2="w" k="10" />
    <hkern u1="B" u2="v" k="10" />
    <hkern u1="B" u2="Y" k="30" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="W" k="15" />
    <hkern u1="B" u2="V" k="20" />
    <hkern u1="B" u2="T" k="10" />
    <hkern u1="B" u2="&#x3f;" k="5" />
    <hkern u1="B" u2="&#x26;" k="-15" />
    <hkern u1="C" u2="&#x2014;" k="10" />
    <hkern u1="C" u2="&#x2013;" k="10" />
    <hkern u1="C" u2="&#x153;" k="10" />
    <hkern u1="C" u2="&#x152;" k="20" />
    <hkern u1="C" u2="&#xb5;" k="20" />
    <hkern u1="C" u2="y" k="10" />
    <hkern u1="C" u2="x" k="10" />
    <hkern u1="C" u2="w" k="10" />
    <hkern u1="C" u2="v" k="10" />
    <hkern u1="C" u2="q" k="10" />
    <hkern u1="C" u2="o" k="10" />
    <hkern u1="C" u2="g" k="10" />
    <hkern u1="C" u2="e" k="10" />
    <hkern u1="C" u2="d" k="10" />
    <hkern u1="C" u2="c" k="10" />
    <hkern u1="C" u2="Y" k="10" />
    <hkern u1="C" u2="X" k="10" />
    <hkern u1="C" u2="W" k="5" />
    <hkern u1="C" u2="V" k="5" />
    <hkern u1="C" u2="Q" k="20" />
    <hkern u1="C" u2="O" k="20" />
    <hkern u1="C" u2="G" k="20" />
    <hkern u1="C" u2="C" k="20" />
    <hkern u1="C" u2="&#x2d;" k="10" />
    <hkern u1="D" u2="&#x2026;" k="40" />
    <hkern u1="D" u2="&#xc6;" k="50" />
    <hkern u1="D" u2="&#x7d;" k="20" />
    <hkern u1="D" u2="x" k="10" />
    <hkern u1="D" u2="]" k="20" />
    <hkern u1="D" u2="\" k="40" />
    <hkern u1="D" u2="Z" k="40" />
    <hkern u1="D" u2="Y" k="65" />
    <hkern u1="D" u2="X" k="55" />
    <hkern u1="D" u2="W" k="40" />
    <hkern u1="D" u2="V" k="45" />
    <hkern u1="D" u2="T" k="40" />
    <hkern u1="D" u2="S" k="10" />
    <hkern u1="D" u2="J" k="40" />
    <hkern u1="D" u2="A" k="50" />
    <hkern u1="D" u2="&#x3f;" k="20" />
    <hkern u1="D" u2="&#x2f;" k="40" />
    <hkern u1="D" u2="&#x2e;" k="40" />
    <hkern u1="D" u2="&#x2c;" k="40" />
    <hkern u1="D" u2="&#x29;" k="30" />
    <hkern u1="E" u2="&#x153;" k="10" />
    <hkern u1="E" u2="y" k="10" />
    <hkern u1="E" u2="w" k="10" />
    <hkern u1="E" u2="v" k="10" />
    <hkern u1="E" u2="o" k="10" />
    <hkern u1="E" u2="e" k="10" />
    <hkern u1="E" u2="d" k="10" />
    <hkern u1="E" u2="c" k="10" />
    <hkern u1="F" u2="&#x203a;" k="15" />
    <hkern u1="F" u2="&#x2026;" k="100" />
    <hkern u1="F" u2="&#x201d;" k="-20" />
    <hkern u1="F" u2="&#x2019;" k="-20" />
    <hkern u1="F" u2="&#x153;" k="15" />
    <hkern u1="F" u2="&#xe6;" k="25" />
    <hkern u1="F" u2="&#xc6;" k="80" />
    <hkern u1="F" u2="&#xbb;" k="15" />
    <hkern u1="F" u2="z" k="15" />
    <hkern u1="F" u2="y" k="15" />
    <hkern u1="F" u2="w" k="10" />
    <hkern u1="F" u2="v" k="15" />
    <hkern u1="F" u2="s" k="10" />
    <hkern u1="F" u2="q" k="10" />
    <hkern u1="F" u2="o" k="15" />
    <hkern u1="F" u2="g" k="10" />
    <hkern u1="F" u2="e" k="15" />
    <hkern u1="F" u2="d" k="10" />
    <hkern u1="F" u2="c" k="15" />
    <hkern u1="F" u2="a" k="25" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="J" k="110" />
    <hkern u1="F" u2="A" k="80" />
    <hkern u1="F" u2="&#x3f;" k="-10" />
    <hkern u1="F" u2="&#x2f;" k="70" />
    <hkern u1="F" u2="&#x2e;" k="100" />
    <hkern u1="F" u2="&#x2c;" k="100" />
    <hkern u1="F" u2="&#x26;" k="15" />
    <hkern u1="G" u2="&#xe6;" k="-10" />
    <hkern u1="G" u2="y" k="5" />
    <hkern u1="G" u2="v" k="5" />
    <hkern u1="G" u2="a" k="-10" />
    <hkern u1="G" u2="\" k="15" />
    <hkern u1="G" u2="Y" k="30" />
    <hkern u1="G" u2="X" k="10" />
    <hkern u1="G" u2="W" k="20" />
    <hkern u1="G" u2="V" k="25" />
    <hkern u1="G" u2="T" k="10" />
    <hkern u1="G" u2="&#x3f;" k="10" />
    <hkern u1="J" u2="&#x2026;" k="15" />
    <hkern u1="J" u2="&#xc6;" k="25" />
    <hkern u1="J" u2="J" k="20" />
    <hkern u1="J" u2="A" k="25" />
    <hkern u1="J" u2="&#x2e;" k="15" />
    <hkern u1="J" u2="&#x2c;" k="15" />
    <hkern u1="K" u2="&#xfb02;" k="20" />
    <hkern u1="K" u2="&#xfb01;" k="20" />
    <hkern u1="K" u2="&#x2248;" k="20" />
    <hkern u1="K" u2="&#x221a;" k="20" />
    <hkern u1="K" u2="&#x2014;" k="50" />
    <hkern u1="K" u2="&#x2013;" k="50" />
    <hkern u1="K" u2="&#x3a9;" k="20" />
    <hkern u1="K" u2="&#x153;" k="30" />
    <hkern u1="K" u2="&#x152;" k="55" />
    <hkern u1="K" u2="&#xe6;" k="10" />
    <hkern u1="K" u2="&#xc6;" k="20" />
    <hkern u1="K" u2="&#xb5;" k="55" />
    <hkern u1="K" u2="&#xab;" k="20" />
    <hkern u1="K" u2="y" k="50" />
    <hkern u1="K" u2="w" k="50" />
    <hkern u1="K" u2="v" k="60" />
    <hkern u1="K" u2="u" k="20" />
    <hkern u1="K" u2="t" k="25" />
    <hkern u1="K" u2="q" k="25" />
    <hkern u1="K" u2="o" k="30" />
    <hkern u1="K" u2="g" k="25" />
    <hkern u1="K" u2="f" k="20" />
    <hkern u1="K" u2="e" k="30" />
    <hkern u1="K" u2="d" k="25" />
    <hkern u1="K" u2="c" k="30" />
    <hkern u1="K" u2="a" k="10" />
    <hkern u1="K" u2="Y" k="30" />
    <hkern u1="K" u2="W" k="30" />
    <hkern u1="K" u2="V" k="30" />
    <hkern u1="K" u2="U" k="15" />
    <hkern u1="K" u2="T" k="10" />
    <hkern u1="K" u2="S" k="10" />
    <hkern u1="K" u2="Q" k="55" />
    <hkern u1="K" u2="O" k="55" />
    <hkern u1="K" u2="G" k="55" />
    <hkern u1="K" u2="C" k="55" />
    <hkern u1="K" u2="A" k="20" />
    <hkern u1="K" u2="&#x2d;" k="50" />
    <hkern u1="L" u2="&#xfb02;" k="20" />
    <hkern u1="L" u2="&#xfb01;" k="20" />
    <hkern u1="L" u2="&#x2248;" k="20" />
    <hkern u1="L" u2="&#x221a;" k="20" />
    <hkern u1="L" u2="&#x2122;" k="90" />
    <hkern u1="L" u2="&#x201d;" k="40" />
    <hkern u1="L" u2="&#x201c;" k="40" />
    <hkern u1="L" u2="&#x2019;" k="40" />
    <hkern u1="L" u2="&#x2018;" k="40" />
    <hkern u1="L" u2="&#x2014;" k="40" />
    <hkern u1="L" u2="&#x2013;" k="40" />
    <hkern u1="L" u2="&#x3a9;" k="20" />
    <hkern u1="L" u2="&#x153;" k="10" />
    <hkern u1="L" u2="&#x152;" k="40" />
    <hkern u1="L" u2="&#xb5;" k="40" />
    <hkern u1="L" u2="y" k="60" />
    <hkern u1="L" u2="w" k="50" />
    <hkern u1="L" u2="v" k="60" />
    <hkern u1="L" u2="t" k="20" />
    <hkern u1="L" u2="q" k="5" />
    <hkern u1="L" u2="o" k="10" />
    <hkern u1="L" u2="g" k="5" />
    <hkern u1="L" u2="f" k="20" />
    <hkern u1="L" u2="e" k="10" />
    <hkern u1="L" u2="d" k="5" />
    <hkern u1="L" u2="c" k="10" />
    <hkern u1="L" u2="\" k="120" />
    <hkern u1="L" u2="Y" k="130" />
    <hkern u1="L" u2="W" k="100" />
    <hkern u1="L" u2="V" k="115" />
    <hkern u1="L" u2="U" k="20" />
    <hkern u1="L" u2="T" k="100" />
    <hkern u1="L" u2="Q" k="40" />
    <hkern u1="L" u2="O" k="40" />
    <hkern u1="L" u2="G" k="40" />
    <hkern u1="L" u2="C" k="40" />
    <hkern u1="L" u2="&#x3f;" k="60" />
    <hkern u1="L" u2="&#x2d;" k="40" />
    <hkern u1="L" u2="&#x2a;" k="80" />
    <hkern u1="O" u2="&#x2026;" k="40" />
    <hkern u1="O" u2="&#xc6;" k="45" />
    <hkern u1="O" u2="&#x7d;" k="20" />
    <hkern u1="O" u2="x" k="5" />
    <hkern u1="O" u2="]" k="20" />
    <hkern u1="O" u2="\" k="40" />
    <hkern u1="O" u2="Z" k="35" />
    <hkern u1="O" u2="Y" k="60" />
    <hkern u1="O" u2="X" k="50" />
    <hkern u1="O" u2="W" k="40" />
    <hkern u1="O" u2="V" k="45" />
    <hkern u1="O" u2="T" k="30" />
    <hkern u1="O" u2="S" k="5" />
    <hkern u1="O" u2="J" k="30" />
    <hkern u1="O" u2="A" k="45" />
    <hkern u1="O" u2="&#x3f;" k="20" />
    <hkern u1="O" u2="&#x2f;" k="40" />
    <hkern u1="O" u2="&#x2e;" k="40" />
    <hkern u1="O" u2="&#x2c;" k="40" />
    <hkern u1="O" u2="&#x29;" k="30" />
    <hkern u1="P" u2="&#xfb02;" k="-15" />
    <hkern u1="P" u2="&#xfb01;" k="-15" />
    <hkern u1="P" u2="&#x2248;" k="-15" />
    <hkern u1="P" u2="&#x221a;" k="-15" />
    <hkern u1="P" u2="&#x2026;" k="100" />
    <hkern u1="P" u2="&#x201d;" k="-20" />
    <hkern u1="P" u2="&#x2019;" k="-20" />
    <hkern u1="P" u2="&#x3a9;" k="-15" />
    <hkern u1="P" u2="&#x153;" k="5" />
    <hkern u1="P" u2="&#xe6;" k="10" />
    <hkern u1="P" u2="&#xc6;" k="70" />
    <hkern u1="P" u2="&#xab;" k="-10" />
    <hkern u1="P" u2="y" k="-10" />
    <hkern u1="P" u2="w" k="-10" />
    <hkern u1="P" u2="v" k="-10" />
    <hkern u1="P" u2="u" k="-5" />
    <hkern u1="P" u2="t" k="-15" />
    <hkern u1="P" u2="o" k="5" />
    <hkern u1="P" u2="f" k="-15" />
    <hkern u1="P" u2="e" k="5" />
    <hkern u1="P" u2="c" k="5" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="Z" k="15" />
    <hkern u1="P" u2="Y" k="10" />
    <hkern u1="P" u2="X" k="30" />
    <hkern u1="P" u2="W" k="5" />
    <hkern u1="P" u2="V" k="10" />
    <hkern u1="P" u2="J" k="100" />
    <hkern u1="P" u2="A" k="70" />
    <hkern u1="P" u2="&#x2f;" k="60" />
    <hkern u1="P" u2="&#x2e;" k="100" />
    <hkern u1="P" u2="&#x2c;" k="100" />
    <hkern u1="Q" u2="Y" k="65" />
    <hkern u1="Q" u2="W" k="40" />
    <hkern u1="Q" u2="V" k="45" />
    <hkern u1="Q" u2="T" k="30" />
    <hkern u1="Q" u2="&#x3f;" k="20" />
    <hkern u1="Q" u2="&#x29;" k="10" />
    <hkern u1="R" u2="&#xfb02;" k="-10" />
    <hkern u1="R" u2="&#xfb01;" k="-10" />
    <hkern u1="R" u2="&#x2248;" k="-10" />
    <hkern u1="R" u2="&#x221a;" k="-10" />
    <hkern u1="R" u2="&#x3a9;" k="-10" />
    <hkern u1="R" u2="&#x153;" k="10" />
    <hkern u1="R" u2="t" k="-10" />
    <hkern u1="R" u2="q" k="5" />
    <hkern u1="R" u2="o" k="10" />
    <hkern u1="R" u2="g" k="5" />
    <hkern u1="R" u2="f" k="-10" />
    <hkern u1="R" u2="e" k="10" />
    <hkern u1="R" u2="d" k="5" />
    <hkern u1="R" u2="c" k="10" />
    <hkern u1="R" u2="Y" k="25" />
    <hkern u1="R" u2="W" k="15" />
    <hkern u1="R" u2="V" k="20" />
    <hkern u1="R" u2="J" k="5" />
    <hkern u1="S" u2="&#xfb02;" k="5" />
    <hkern u1="S" u2="&#xfb01;" k="5" />
    <hkern u1="S" u2="&#x2248;" k="5" />
    <hkern u1="S" u2="&#x221a;" k="5" />
    <hkern u1="S" u2="&#x3a9;" k="5" />
    <hkern u1="S" u2="&#xc6;" k="15" />
    <hkern u1="S" u2="z" k="5" />
    <hkern u1="S" u2="y" k="15" />
    <hkern u1="S" u2="x" k="15" />
    <hkern u1="S" u2="w" k="10" />
    <hkern u1="S" u2="v" k="15" />
    <hkern u1="S" u2="t" k="5" />
    <hkern u1="S" u2="f" k="5" />
    <hkern u1="S" u2="\" k="20" />
    <hkern u1="S" u2="Z" k="10" />
    <hkern u1="S" u2="Y" k="30" />
    <hkern u1="S" u2="X" k="25" />
    <hkern u1="S" u2="W" k="25" />
    <hkern u1="S" u2="V" k="30" />
    <hkern u1="S" u2="T" k="15" />
    <hkern u1="S" u2="S" k="10" />
    <hkern u1="S" u2="A" k="15" />
    <hkern u1="S" u2="&#x3f;" k="10" />
    <hkern u1="T" u2="&#xfb02;" k="30" />
    <hkern u1="T" u2="&#xfb01;" k="30" />
    <hkern u1="T" u2="&#x2248;" k="30" />
    <hkern u1="T" u2="&#x221a;" k="30" />
    <hkern u1="T" u2="&#x203a;" k="70" />
    <hkern u1="T" u2="&#x2026;" k="100" />
    <hkern u1="T" u2="&#x2014;" k="90" />
    <hkern u1="T" u2="&#x2013;" k="90" />
    <hkern u1="T" u2="&#x3a9;" k="30" />
    <hkern u1="T" u2="&#x153;" k="105" />
    <hkern u1="T" u2="&#x152;" k="30" />
    <hkern u1="T" u2="&#xe6;" k="105" />
    <hkern u1="T" u2="&#xc6;" k="90" />
    <hkern u1="T" u2="&#xbb;" k="70" />
    <hkern u1="T" u2="&#xb5;" k="30" />
    <hkern u1="T" u2="&#xab;" k="90" />
    <hkern u1="T" u2="z" k="80" />
    <hkern u1="T" u2="y" k="50" />
    <hkern u1="T" u2="x" k="50" />
    <hkern u1="T" u2="w" k="40" />
    <hkern u1="T" u2="v" k="50" />
    <hkern u1="T" u2="u" k="50" />
    <hkern u1="T" u2="t" k="20" />
    <hkern u1="T" u2="s" k="90" />
    <hkern u1="T" u2="r" k="60" />
    <hkern u1="T" u2="q" k="95" />
    <hkern u1="T" u2="p" k="60" />
    <hkern u1="T" u2="o" k="105" />
    <hkern u1="T" u2="n" k="60" />
    <hkern u1="T" u2="m" k="60" />
    <hkern u1="T" u2="l" k="5" />
    <hkern u1="T" u2="j" k="10" />
    <hkern u1="T" u2="i" k="10" />
    <hkern u1="T" u2="g" k="95" />
    <hkern u1="T" u2="f" k="30" />
    <hkern u1="T" u2="e" k="105" />
    <hkern u1="T" u2="d" k="95" />
    <hkern u1="T" u2="c" k="105" />
    <hkern u1="T" u2="a" k="105" />
    <hkern u1="T" u2="Z" k="10" />
    <hkern u1="T" u2="S" k="15" />
    <hkern u1="T" u2="Q" k="30" />
    <hkern u1="T" u2="O" k="30" />
    <hkern u1="T" u2="J" k="110" />
    <hkern u1="T" u2="G" k="30" />
    <hkern u1="T" u2="C" k="30" />
    <hkern u1="T" u2="A" k="90" />
    <hkern u1="T" u2="&#x3b;" k="10" />
    <hkern u1="T" u2="&#x3a;" k="10" />
    <hkern u1="T" u2="&#x2f;" k="90" />
    <hkern u1="T" u2="&#x2e;" k="100" />
    <hkern u1="T" u2="&#x2d;" k="90" />
    <hkern u1="T" u2="&#x2c;" k="100" />
    <hkern u1="T" u2="&#x26;" k="45" />
    <hkern u1="U" u2="&#x2026;" k="15" />
    <hkern u1="U" u2="&#xc6;" k="25" />
    <hkern u1="U" u2="x" k="5" />
    <hkern u1="U" u2="X" k="10" />
    <hkern u1="U" u2="J" k="20" />
    <hkern u1="U" u2="A" k="25" />
    <hkern u1="U" u2="&#x2f;" k="15" />
    <hkern u1="U" u2="&#x2e;" k="15" />
    <hkern u1="U" u2="&#x2c;" k="15" />
    <hkern u1="V" u2="&#xfb02;" k="25" />
    <hkern u1="V" u2="&#xfb01;" k="25" />
    <hkern u1="V" u2="&#x2248;" k="25" />
    <hkern u1="V" u2="&#x221a;" k="25" />
    <hkern u1="V" u2="&#x203a;" k="40" />
    <hkern u1="V" u2="&#x2026;" k="120" />
    <hkern u1="V" u2="&#x2014;" k="40" />
    <hkern u1="V" u2="&#x2013;" k="40" />
    <hkern u1="V" u2="&#x3a9;" k="25" />
    <hkern u1="V" u2="&#x153;" k="70" />
    <hkern u1="V" u2="&#x152;" k="45" />
    <hkern u1="V" u2="&#xe6;" k="70" />
    <hkern u1="V" u2="&#xc6;" k="100" />
    <hkern u1="V" u2="&#xbb;" k="40" />
    <hkern u1="V" u2="&#xb5;" k="45" />
    <hkern u1="V" u2="&#xab;" k="60" />
    <hkern u1="V" u2="z" k="55" />
    <hkern u1="V" u2="y" k="40" />
    <hkern u1="V" u2="x" k="50" />
    <hkern u1="V" u2="w" k="35" />
    <hkern u1="V" u2="v" k="40" />
    <hkern u1="V" u2="u" k="40" />
    <hkern u1="V" u2="t" k="20" />
    <hkern u1="V" u2="s" k="60" />
    <hkern u1="V" u2="r" k="40" />
    <hkern u1="V" u2="q" k="65" />
    <hkern u1="V" u2="p" k="40" />
    <hkern u1="V" u2="o" k="70" />
    <hkern u1="V" u2="n" k="40" />
    <hkern u1="V" u2="m" k="40" />
    <hkern u1="V" u2="l" k="10" />
    <hkern u1="V" u2="j" k="20" />
    <hkern u1="V" u2="i" k="20" />
    <hkern u1="V" u2="g" k="65" />
    <hkern u1="V" u2="f" k="25" />
    <hkern u1="V" u2="e" k="70" />
    <hkern u1="V" u2="d" k="65" />
    <hkern u1="V" u2="c" k="70" />
    <hkern u1="V" u2="a" k="70" />
    <hkern u1="V" u2="Z" k="10" />
    <hkern u1="V" u2="Y" k="20" />
    <hkern u1="V" u2="X" k="20" />
    <hkern u1="V" u2="W" k="10" />
    <hkern u1="V" u2="V" k="10" />
    <hkern u1="V" u2="S" k="25" />
    <hkern u1="V" u2="Q" k="45" />
    <hkern u1="V" u2="O" k="45" />
    <hkern u1="V" u2="J" k="120" />
    <hkern u1="V" u2="G" k="45" />
    <hkern u1="V" u2="C" k="45" />
    <hkern u1="V" u2="A" k="100" />
    <hkern u1="V" u2="&#x3b;" k="20" />
    <hkern u1="V" u2="&#x3a;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="120" />
    <hkern u1="V" u2="&#x2e;" k="120" />
    <hkern u1="V" u2="&#x2d;" k="40" />
    <hkern u1="V" u2="&#x2c;" k="120" />
    <hkern u1="V" u2="&#x26;" k="40" />
    <hkern u1="W" u2="&#xfb02;" k="30" />
    <hkern u1="W" u2="&#xfb01;" k="30" />
    <hkern u1="W" u2="&#x2248;" k="30" />
    <hkern u1="W" u2="&#x221a;" k="30" />
    <hkern u1="W" u2="&#x203a;" k="35" />
    <hkern u1="W" u2="&#x2026;" k="100" />
    <hkern u1="W" u2="&#x2014;" k="35" />
    <hkern u1="W" u2="&#x2013;" k="35" />
    <hkern u1="W" u2="&#x3a9;" k="30" />
    <hkern u1="W" u2="&#x153;" k="65" />
    <hkern u1="W" u2="&#x152;" k="40" />
    <hkern u1="W" u2="&#xe6;" k="70" />
    <hkern u1="W" u2="&#xc6;" k="90" />
    <hkern u1="W" u2="&#xbb;" k="35" />
    <hkern u1="W" u2="&#xb5;" k="40" />
    <hkern u1="W" u2="&#xab;" k="50" />
    <hkern u1="W" u2="z" k="55" />
    <hkern u1="W" u2="y" k="35" />
    <hkern u1="W" u2="x" k="40" />
    <hkern u1="W" u2="w" k="35" />
    <hkern u1="W" u2="v" k="35" />
    <hkern u1="W" u2="u" k="35" />
    <hkern u1="W" u2="t" k="25" />
    <hkern u1="W" u2="s" k="60" />
    <hkern u1="W" u2="r" k="35" />
    <hkern u1="W" u2="q" k="60" />
    <hkern u1="W" u2="p" k="35" />
    <hkern u1="W" u2="o" k="65" />
    <hkern u1="W" u2="n" k="35" />
    <hkern u1="W" u2="m" k="35" />
    <hkern u1="W" u2="l" k="10" />
    <hkern u1="W" u2="j" k="15" />
    <hkern u1="W" u2="i" k="15" />
    <hkern u1="W" u2="g" k="60" />
    <hkern u1="W" u2="f" k="30" />
    <hkern u1="W" u2="e" k="65" />
    <hkern u1="W" u2="d" k="60" />
    <hkern u1="W" u2="c" k="65" />
    <hkern u1="W" u2="a" k="70" />
    <hkern u1="W" u2="Z" k="10" />
    <hkern u1="W" u2="Y" k="20" />
    <hkern u1="W" u2="X" k="15" />
    <hkern u1="W" u2="W" k="10" />
    <hkern u1="W" u2="V" k="10" />
    <hkern u1="W" u2="S" k="20" />
    <hkern u1="W" u2="Q" k="40" />
    <hkern u1="W" u2="O" k="40" />
    <hkern u1="W" u2="J" k="105" />
    <hkern u1="W" u2="G" k="40" />
    <hkern u1="W" u2="C" k="40" />
    <hkern u1="W" u2="A" k="90" />
    <hkern u1="W" u2="&#x3b;" k="15" />
    <hkern u1="W" u2="&#x3a;" k="15" />
    <hkern u1="W" u2="&#x2f;" k="100" />
    <hkern u1="W" u2="&#x2e;" k="100" />
    <hkern u1="W" u2="&#x2d;" k="35" />
    <hkern u1="W" u2="&#x2c;" k="100" />
    <hkern u1="W" u2="&#x26;" k="30" />
    <hkern u1="X" u2="&#xfb02;" k="20" />
    <hkern u1="X" u2="&#xfb01;" k="20" />
    <hkern u1="X" u2="&#x2248;" k="20" />
    <hkern u1="X" u2="&#x221a;" k="20" />
    <hkern u1="X" u2="&#x203a;" k="20" />
    <hkern u1="X" u2="&#x2014;" k="50" />
    <hkern u1="X" u2="&#x2013;" k="50" />
    <hkern u1="X" u2="&#x3a9;" k="20" />
    <hkern u1="X" u2="&#x153;" k="45" />
    <hkern u1="X" u2="&#x152;" k="50" />
    <hkern u1="X" u2="&#xe6;" k="10" />
    <hkern u1="X" u2="&#xc6;" k="20" />
    <hkern u1="X" u2="&#xbb;" k="20" />
    <hkern u1="X" u2="&#xb5;" k="50" />
    <hkern u1="X" u2="&#xab;" k="50" />
    <hkern u1="X" u2="y" k="40" />
    <hkern u1="X" u2="w" k="40" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="u" k="20" />
    <hkern u1="X" u2="t" k="20" />
    <hkern u1="X" u2="q" k="40" />
    <hkern u1="X" u2="o" k="45" />
    <hkern u1="X" u2="l" k="10" />
    <hkern u1="X" u2="j" k="10" />
    <hkern u1="X" u2="i" k="10" />
    <hkern u1="X" u2="g" k="40" />
    <hkern u1="X" u2="f" k="20" />
    <hkern u1="X" u2="e" k="45" />
    <hkern u1="X" u2="d" k="40" />
    <hkern u1="X" u2="c" k="45" />
    <hkern u1="X" u2="a" k="10" />
    <hkern u1="X" u2="Y" k="30" />
    <hkern u1="X" u2="W" k="15" />
    <hkern u1="X" u2="V" k="20" />
    <hkern u1="X" u2="U" k="10" />
    <hkern u1="X" u2="S" k="30" />
    <hkern u1="X" u2="Q" k="50" />
    <hkern u1="X" u2="O" k="50" />
    <hkern u1="X" u2="J" k="10" />
    <hkern u1="X" u2="G" k="50" />
    <hkern u1="X" u2="C" k="50" />
    <hkern u1="X" u2="A" k="20" />
    <hkern u1="X" u2="&#x3f;" k="15" />
    <hkern u1="X" u2="&#x2d;" k="50" />
    <hkern u1="X" u2="&#x26;" k="10" />
    <hkern u1="Y" u2="&#xfb02;" k="40" />
    <hkern u1="Y" u2="&#xfb01;" k="40" />
    <hkern u1="Y" u2="&#x2248;" k="40" />
    <hkern u1="Y" u2="&#x221a;" k="40" />
    <hkern u1="Y" u2="&#x203a;" k="75" />
    <hkern u1="Y" u2="&#x2026;" k="130" />
    <hkern u1="Y" u2="&#x2014;" k="80" />
    <hkern u1="Y" u2="&#x2013;" k="80" />
    <hkern u1="Y" u2="&#x3a9;" k="40" />
    <hkern u1="Y" u2="&#x153;" k="110" />
    <hkern u1="Y" u2="&#x152;" k="60" />
    <hkern u1="Y" u2="&#xe6;" k="100" />
    <hkern u1="Y" u2="&#xc6;" k="110" />
    <hkern u1="Y" u2="&#xbb;" k="75" />
    <hkern u1="Y" u2="&#xb5;" k="60" />
    <hkern u1="Y" u2="&#xab;" k="100" />
    <hkern u1="Y" u2="z" k="80" />
    <hkern u1="Y" u2="y" k="60" />
    <hkern u1="Y" u2="x" k="70" />
    <hkern u1="Y" u2="w" k="55" />
    <hkern u1="Y" u2="v" k="60" />
    <hkern u1="Y" u2="u" k="75" />
    <hkern u1="Y" u2="t" k="30" />
    <hkern u1="Y" u2="s" k="100" />
    <hkern u1="Y" u2="r" k="75" />
    <hkern u1="Y" u2="q" k="105" />
    <hkern u1="Y" u2="p" k="75" />
    <hkern u1="Y" u2="o" k="110" />
    <hkern u1="Y" u2="n" k="75" />
    <hkern u1="Y" u2="m" k="75" />
    <hkern u1="Y" u2="l" k="10" />
    <hkern u1="Y" u2="j" k="20" />
    <hkern u1="Y" u2="i" k="20" />
    <hkern u1="Y" u2="g" k="105" />
    <hkern u1="Y" u2="f" k="40" />
    <hkern u1="Y" u2="e" k="110" />
    <hkern u1="Y" u2="d" k="105" />
    <hkern u1="Y" u2="c" k="110" />
    <hkern u1="Y" u2="a" k="100" />
    <hkern u1="Y" u2="Z" k="10" />
    <hkern u1="Y" u2="Y" k="20" />
    <hkern u1="Y" u2="X" k="30" />
    <hkern u1="Y" u2="W" k="20" />
    <hkern u1="Y" u2="V" k="20" />
    <hkern u1="Y" u2="S" k="35" />
    <hkern u1="Y" u2="Q" k="60" />
    <hkern u1="Y" u2="O" k="60" />
    <hkern u1="Y" u2="J" k="130" />
    <hkern u1="Y" u2="G" k="60" />
    <hkern u1="Y" u2="C" k="60" />
    <hkern u1="Y" u2="A" k="110" />
    <hkern u1="Y" u2="&#x3b;" k="40" />
    <hkern u1="Y" u2="&#x3a;" k="40" />
    <hkern u1="Y" u2="&#x2f;" k="110" />
    <hkern u1="Y" u2="&#x2e;" k="130" />
    <hkern u1="Y" u2="&#x2d;" k="80" />
    <hkern u1="Y" u2="&#x2c;" k="130" />
    <hkern u1="Y" u2="&#x26;" k="55" />
    <hkern u1="Z" u2="&#xfb02;" k="10" />
    <hkern u1="Z" u2="&#xfb01;" k="10" />
    <hkern u1="Z" u2="&#x2248;" k="10" />
    <hkern u1="Z" u2="&#x221a;" k="10" />
    <hkern u1="Z" u2="&#x2014;" k="30" />
    <hkern u1="Z" u2="&#x2013;" k="30" />
    <hkern u1="Z" u2="&#x3a9;" k="10" />
    <hkern u1="Z" u2="&#x153;" k="25" />
    <hkern u1="Z" u2="&#x152;" k="35" />
    <hkern u1="Z" u2="&#xb5;" k="35" />
    <hkern u1="Z" u2="&#xab;" k="20" />
    <hkern u1="Z" u2="y" k="15" />
    <hkern u1="Z" u2="w" k="15" />
    <hkern u1="Z" u2="v" k="20" />
    <hkern u1="Z" u2="q" k="20" />
    <hkern u1="Z" u2="o" k="25" />
    <hkern u1="Z" u2="g" k="20" />
    <hkern u1="Z" u2="f" k="10" />
    <hkern u1="Z" u2="e" k="25" />
    <hkern u1="Z" u2="d" k="20" />
    <hkern u1="Z" u2="c" k="25" />
    <hkern u1="Z" u2="Z" k="10" />
    <hkern u1="Z" u2="S" k="10" />
    <hkern u1="Z" u2="Q" k="35" />
    <hkern u1="Z" u2="O" k="35" />
    <hkern u1="Z" u2="G" k="35" />
    <hkern u1="Z" u2="C" k="35" />
    <hkern u1="Z" u2="&#x2d;" k="30" />
    <hkern u1="[" u2="&#x153;" k="20" />
    <hkern u1="[" u2="&#x152;" k="20" />
    <hkern u1="[" u2="&#xe6;" k="10" />
    <hkern u1="[" u2="&#xb5;" k="20" />
    <hkern u1="[" u2="y" k="10" />
    <hkern u1="[" u2="x" k="10" />
    <hkern u1="[" u2="w" k="20" />
    <hkern u1="[" u2="v" k="20" />
    <hkern u1="[" u2="s" k="15" />
    <hkern u1="[" u2="q" k="20" />
    <hkern u1="[" u2="o" k="20" />
    <hkern u1="[" u2="j" k="-30" />
    <hkern u1="[" u2="e" k="20" />
    <hkern u1="[" u2="d" k="20" />
    <hkern u1="[" u2="c" k="20" />
    <hkern u1="[" u2="a" k="10" />
    <hkern u1="[" u2="Q" k="20" />
    <hkern u1="[" u2="O" k="20" />
    <hkern u1="[" u2="J" k="10" />
    <hkern u1="[" u2="G" k="20" />
    <hkern u1="[" u2="C" k="20" />
    <hkern u1="\" u2="&#xfb02;" k="10" />
    <hkern u1="\" u2="&#xfb01;" k="10" />
    <hkern u1="\" u2="&#x2248;" k="10" />
    <hkern u1="\" u2="&#x221a;" k="10" />
    <hkern u1="\" u2="&#x3a9;" k="10" />
    <hkern u1="\" u2="&#x152;" k="40" />
    <hkern u1="\" u2="&#xb5;" k="40" />
    <hkern u1="\" u2="y" k="60" />
    <hkern u1="\" u2="w" k="60" />
    <hkern u1="\" u2="v" k="70" />
    <hkern u1="\" u2="t" k="30" />
    <hkern u1="\" u2="j" k="-30" />
    <hkern u1="\" u2="f" k="10" />
    <hkern u1="\" u2="Y" k="110" />
    <hkern u1="\" u2="W" k="100" />
    <hkern u1="\" u2="V" k="120" />
    <hkern u1="\" u2="U" k="15" />
    <hkern u1="\" u2="T" k="90" />
    <hkern u1="\" u2="Q" k="40" />
    <hkern u1="\" u2="O" k="40" />
    <hkern u1="\" u2="G" k="40" />
    <hkern u1="\" u2="C" k="40" />
    <hkern u1="a" u2="y" k="20" />
    <hkern u1="a" u2="w" k="20" />
    <hkern u1="a" u2="v" k="20" />
    <hkern u1="a" u2="t" k="5" />
    <hkern u1="a" u2="\" k="75" />
    <hkern u1="a" u2="&#x3f;" k="35" />
    <hkern u1="a" u2="&#x2a;" k="15" />
    <hkern u1="b" u2="&#x203a;" k="5" />
    <hkern u1="b" u2="&#x2026;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="&#xbb;" k="5" />
    <hkern u1="b" u2="&#x7d;" k="15" />
    <hkern u1="b" u2="z" k="15" />
    <hkern u1="b" u2="y" k="25" />
    <hkern u1="b" u2="x" k="30" />
    <hkern u1="b" u2="w" k="20" />
    <hkern u1="b" u2="v" k="25" />
    <hkern u1="b" u2="]" k="20" />
    <hkern u1="b" u2="\" k="70" />
    <hkern u1="b" u2="&#x3f;" k="35" />
    <hkern u1="b" u2="&#x2e;" k="10" />
    <hkern u1="b" u2="&#x2c;" k="10" />
    <hkern u1="b" u2="&#x2a;" k="15" />
    <hkern u1="b" u2="&#x29;" k="30" />
    <hkern u1="c" u2="&#x203a;" k="-10" />
    <hkern u1="c" u2="&#x2039;" k="10" />
    <hkern u1="c" u2="&#x201d;" k="-15" />
    <hkern u1="c" u2="&#x201c;" k="-10" />
    <hkern u1="c" u2="&#x2019;" k="-15" />
    <hkern u1="c" u2="&#x2018;" k="-10" />
    <hkern u1="c" u2="&#x153;" k="15" />
    <hkern u1="c" u2="&#xbb;" k="-10" />
    <hkern u1="c" u2="&#xab;" k="10" />
    <hkern u1="c" u2="y" k="5" />
    <hkern u1="c" u2="x" k="10" />
    <hkern u1="c" u2="w" k="5" />
    <hkern u1="c" u2="v" k="5" />
    <hkern u1="c" u2="q" k="10" />
    <hkern u1="c" u2="o" k="15" />
    <hkern u1="c" u2="g" k="10" />
    <hkern u1="c" u2="e" k="15" />
    <hkern u1="c" u2="d" k="10" />
    <hkern u1="c" u2="c" k="15" />
    <hkern u1="c" u2="\" k="40" />
    <hkern u1="c" u2="&#x3f;" k="15" />
    <hkern u1="c" u2="&#x29;" k="15" />
    <hkern u1="e" u2="&#x2026;" k="10" />
    <hkern u1="e" u2="&#x7d;" k="10" />
    <hkern u1="e" u2="z" k="15" />
    <hkern u1="e" u2="y" k="25" />
    <hkern u1="e" u2="x" k="30" />
    <hkern u1="e" u2="w" k="25" />
    <hkern u1="e" u2="v" k="25" />
    <hkern u1="e" u2="]" k="20" />
    <hkern u1="e" u2="\" k="80" />
    <hkern u1="e" u2="&#x3f;" k="40" />
    <hkern u1="e" u2="&#x2e;" k="10" />
    <hkern u1="e" u2="&#x2c;" k="10" />
    <hkern u1="e" u2="&#x2a;" k="20" />
    <hkern u1="e" u2="&#x29;" k="30" />
    <hkern u1="f" u2="&#x2122;" k="-40" />
    <hkern u1="f" u2="&#x2039;" k="15" />
    <hkern u1="f" u2="&#x2026;" k="45" />
    <hkern u1="f" u2="&#x201d;" k="-35" />
    <hkern u1="f" u2="&#x201c;" k="-30" />
    <hkern u1="f" u2="&#x2019;" k="-35" />
    <hkern u1="f" u2="&#x2018;" k="-30" />
    <hkern u1="f" u2="&#x153;" k="10" />
    <hkern u1="f" u2="&#xe6;" k="15" />
    <hkern u1="f" u2="&#xab;" k="15" />
    <hkern u1="f" u2="&#x7d;" k="-30" />
    <hkern u1="f" u2="z" k="10" />
    <hkern u1="f" u2="q" k="10" />
    <hkern u1="f" u2="o" k="10" />
    <hkern u1="f" u2="g" k="10" />
    <hkern u1="f" u2="e" k="10" />
    <hkern u1="f" u2="d" k="10" />
    <hkern u1="f" u2="c" k="10" />
    <hkern u1="f" u2="a" k="15" />
    <hkern u1="f" u2="]" k="-20" />
    <hkern u1="f" u2="\" k="-30" />
    <hkern u1="f" u2="&#x3f;" k="-35" />
    <hkern u1="f" u2="&#x2f;" k="45" />
    <hkern u1="f" u2="&#x2e;" k="45" />
    <hkern u1="f" u2="&#x2c;" k="45" />
    <hkern u1="f" u2="&#x2a;" k="-30" />
    <hkern u1="f" u2="&#x29;" k="-30" />
    <hkern u1="g" u2="\" k="50" />
    <hkern u1="h" u2="y" k="15" />
    <hkern u1="h" u2="w" k="15" />
    <hkern u1="h" u2="v" k="20" />
    <hkern u1="h" u2="\" k="75" />
    <hkern u1="h" u2="&#x3f;" k="30" />
    <hkern u1="h" u2="&#x2a;" k="15" />
    <hkern u1="k" u2="&#x203a;" k="10" />
    <hkern u1="k" u2="&#x2039;" k="20" />
    <hkern u1="k" u2="&#x2014;" k="20" />
    <hkern u1="k" u2="&#x2013;" k="20" />
    <hkern u1="k" u2="&#x153;" k="25" />
    <hkern u1="k" u2="&#xe6;" k="10" />
    <hkern u1="k" u2="&#xbb;" k="10" />
    <hkern u1="k" u2="&#xab;" k="20" />
    <hkern u1="k" u2="y" k="15" />
    <hkern u1="k" u2="w" k="20" />
    <hkern u1="k" u2="v" k="20" />
    <hkern u1="k" u2="u" k="10" />
    <hkern u1="k" u2="t" k="10" />
    <hkern u1="k" u2="q" k="25" />
    <hkern u1="k" u2="o" k="25" />
    <hkern u1="k" u2="g" k="25" />
    <hkern u1="k" u2="e" k="25" />
    <hkern u1="k" u2="d" k="25" />
    <hkern u1="k" u2="c" k="25" />
    <hkern u1="k" u2="a" k="10" />
    <hkern u1="k" u2="\" k="40" />
    <hkern u1="k" u2="&#x2d;" k="20" />
    <hkern u1="m" u2="y" k="15" />
    <hkern u1="m" u2="w" k="15" />
    <hkern u1="m" u2="v" k="20" />
    <hkern u1="m" u2="\" k="75" />
    <hkern u1="m" u2="&#x3f;" k="30" />
    <hkern u1="m" u2="&#x2a;" k="15" />
    <hkern u1="n" u2="y" k="15" />
    <hkern u1="n" u2="w" k="15" />
    <hkern u1="n" u2="v" k="20" />
    <hkern u1="n" u2="\" k="75" />
    <hkern u1="n" u2="&#x3f;" k="30" />
    <hkern u1="n" u2="&#x2a;" k="15" />
    <hkern u1="o" u2="&#x203a;" k="10" />
    <hkern u1="o" u2="&#x2026;" k="20" />
    <hkern u1="o" u2="&#x201c;" k="20" />
    <hkern u1="o" u2="&#x2018;" k="20" />
    <hkern u1="o" u2="&#xbb;" k="10" />
    <hkern u1="o" u2="&#x7d;" k="15" />
    <hkern u1="o" u2="z" k="20" />
    <hkern u1="o" u2="y" k="30" />
    <hkern u1="o" u2="x" k="35" />
    <hkern u1="o" u2="w" k="25" />
    <hkern u1="o" u2="v" k="30" />
    <hkern u1="o" u2="]" k="20" />
    <hkern u1="o" u2="\" k="80" />
    <hkern u1="o" u2="&#x3f;" k="50" />
    <hkern u1="o" u2="&#x2e;" k="20" />
    <hkern u1="o" u2="&#x2c;" k="20" />
    <hkern u1="o" u2="&#x2a;" k="20" />
    <hkern u1="o" u2="&#x29;" k="30" />
    <hkern u1="p" u2="&#x203a;" k="5" />
    <hkern u1="p" u2="&#x2026;" k="10" />
    <hkern u1="p" u2="&#x201c;" k="10" />
    <hkern u1="p" u2="&#x2018;" k="10" />
    <hkern u1="p" u2="&#xbb;" k="5" />
    <hkern u1="p" u2="&#x7d;" k="15" />
    <hkern u1="p" u2="z" k="15" />
    <hkern u1="p" u2="y" k="25" />
    <hkern u1="p" u2="x" k="30" />
    <hkern u1="p" u2="w" k="20" />
    <hkern u1="p" u2="v" k="25" />
    <hkern u1="p" u2="]" k="20" />
    <hkern u1="p" u2="\" k="70" />
    <hkern u1="p" u2="&#x3f;" k="35" />
    <hkern u1="p" u2="&#x2e;" k="10" />
    <hkern u1="p" u2="&#x2c;" k="10" />
    <hkern u1="p" u2="&#x2a;" k="15" />
    <hkern u1="p" u2="&#x29;" k="30" />
    <hkern u1="q" u2="\" k="50" />
    <hkern u1="r" u2="&#x2039;" k="10" />
    <hkern u1="r" u2="&#x2026;" k="90" />
    <hkern u1="r" u2="&#x201d;" k="-35" />
    <hkern u1="r" u2="&#x201c;" k="-20" />
    <hkern u1="r" u2="&#x2019;" k="-35" />
    <hkern u1="r" u2="&#x2018;" k="-20" />
    <hkern u1="r" u2="&#x153;" k="15" />
    <hkern u1="r" u2="&#xe6;" k="25" />
    <hkern u1="r" u2="&#xab;" k="10" />
    <hkern u1="r" u2="z" k="10" />
    <hkern u1="r" u2="q" k="10" />
    <hkern u1="r" u2="o" k="15" />
    <hkern u1="r" u2="g" k="10" />
    <hkern u1="r" u2="e" k="15" />
    <hkern u1="r" u2="d" k="10" />
    <hkern u1="r" u2="c" k="15" />
    <hkern u1="r" u2="a" k="25" />
    <hkern u1="r" u2="\" k="30" />
    <hkern u1="r" u2="&#x2f;" k="75" />
    <hkern u1="r" u2="&#x2e;" k="90" />
    <hkern u1="r" u2="&#x2c;" k="90" />
    <hkern u1="r" u2="&#x2a;" k="-20" />
    <hkern u1="s" u2="&#x2039;" k="10" />
    <hkern u1="s" u2="&#x201c;" k="10" />
    <hkern u1="s" u2="&#x2018;" k="10" />
    <hkern u1="s" u2="&#xab;" k="10" />
    <hkern u1="s" u2="&#x7d;" k="10" />
    <hkern u1="s" u2="z" k="10" />
    <hkern u1="s" u2="y" k="15" />
    <hkern u1="s" u2="x" k="25" />
    <hkern u1="s" u2="w" k="15" />
    <hkern u1="s" u2="v" k="20" />
    <hkern u1="s" u2="t" k="10" />
    <hkern u1="s" u2="s" k="10" />
    <hkern u1="s" u2="]" k="15" />
    <hkern u1="s" u2="\" k="75" />
    <hkern u1="s" u2="&#x3f;" k="35" />
    <hkern u1="s" u2="&#x29;" k="20" />
    <hkern u1="t" u2="&#x2039;" k="10" />
    <hkern u1="t" u2="&#x201d;" k="-10" />
    <hkern u1="t" u2="&#x2019;" k="-10" />
    <hkern u1="t" u2="&#x153;" k="15" />
    <hkern u1="t" u2="&#xab;" k="10" />
    <hkern u1="t" u2="q" k="15" />
    <hkern u1="t" u2="o" k="15" />
    <hkern u1="t" u2="g" k="15" />
    <hkern u1="t" u2="e" k="15" />
    <hkern u1="t" u2="d" k="15" />
    <hkern u1="t" u2="c" k="15" />
    <hkern u1="t" u2="\" k="40" />
    <hkern u1="u" u2="\" k="50" />
    <hkern u1="v" u2="&#x203a;" k="15" />
    <hkern u1="v" u2="&#x2039;" k="30" />
    <hkern u1="v" u2="&#x2026;" k="85" />
    <hkern u1="v" u2="&#x2014;" k="15" />
    <hkern u1="v" u2="&#x2013;" k="15" />
    <hkern u1="v" u2="&#x153;" k="30" />
    <hkern u1="v" u2="&#xe6;" k="25" />
    <hkern u1="v" u2="&#xbb;" k="15" />
    <hkern u1="v" u2="&#xab;" k="30" />
    <hkern u1="v" u2="&#x7d;" k="10" />
    <hkern u1="v" u2="z" k="5" />
    <hkern u1="v" u2="y" k="15" />
    <hkern u1="v" u2="x" k="10" />
    <hkern u1="v" u2="w" k="15" />
    <hkern u1="v" u2="v" k="15" />
    <hkern u1="v" u2="s" k="20" />
    <hkern u1="v" u2="q" k="25" />
    <hkern u1="v" u2="o" k="30" />
    <hkern u1="v" u2="g" k="25" />
    <hkern u1="v" u2="e" k="30" />
    <hkern u1="v" u2="d" k="25" />
    <hkern u1="v" u2="c" k="30" />
    <hkern u1="v" u2="a" k="25" />
    <hkern u1="v" u2="]" k="20" />
    <hkern u1="v" u2="\" k="50" />
    <hkern u1="v" u2="&#x3f;" k="10" />
    <hkern u1="v" u2="&#x2f;" k="70" />
    <hkern u1="v" u2="&#x2e;" k="85" />
    <hkern u1="v" u2="&#x2d;" k="15" />
    <hkern u1="v" u2="&#x2c;" k="85" />
    <hkern u1="w" u2="&#x203a;" k="10" />
    <hkern u1="w" u2="&#x2039;" k="20" />
    <hkern u1="w" u2="&#x2026;" k="70" />
    <hkern u1="w" u2="&#x2014;" k="10" />
    <hkern u1="w" u2="&#x2013;" k="10" />
    <hkern u1="w" u2="&#x153;" k="25" />
    <hkern u1="w" u2="&#xe6;" k="20" />
    <hkern u1="w" u2="&#xbb;" k="10" />
    <hkern u1="w" u2="&#xab;" k="20" />
    <hkern u1="w" u2="&#x7d;" k="10" />
    <hkern u1="w" u2="z" k="5" />
    <hkern u1="w" u2="y" k="10" />
    <hkern u1="w" u2="x" k="10" />
    <hkern u1="w" u2="w" k="10" />
    <hkern u1="w" u2="v" k="15" />
    <hkern u1="w" u2="s" k="15" />
    <hkern u1="w" u2="q" k="20" />
    <hkern u1="w" u2="o" k="25" />
    <hkern u1="w" u2="g" k="20" />
    <hkern u1="w" u2="e" k="25" />
    <hkern u1="w" u2="d" k="20" />
    <hkern u1="w" u2="c" k="25" />
    <hkern u1="w" u2="a" k="20" />
    <hkern u1="w" u2="]" k="20" />
    <hkern u1="w" u2="\" k="50" />
    <hkern u1="w" u2="&#x3f;" k="10" />
    <hkern u1="w" u2="&#x2f;" k="60" />
    <hkern u1="w" u2="&#x2e;" k="70" />
    <hkern u1="w" u2="&#x2d;" k="10" />
    <hkern u1="w" u2="&#x2c;" k="70" />
    <hkern u1="x" u2="&#x203a;" k="15" />
    <hkern u1="x" u2="&#x2039;" k="45" />
    <hkern u1="x" u2="&#x2014;" k="30" />
    <hkern u1="x" u2="&#x2013;" k="30" />
    <hkern u1="x" u2="&#x153;" k="35" />
    <hkern u1="x" u2="&#xe6;" k="15" />
    <hkern u1="x" u2="&#xbb;" k="15" />
    <hkern u1="x" u2="&#xab;" k="45" />
    <hkern u1="x" u2="&#x7d;" k="10" />
    <hkern u1="x" u2="y" k="10" />
    <hkern u1="x" u2="w" k="10" />
    <hkern u1="x" u2="v" k="10" />
    <hkern u1="x" u2="s" k="20" />
    <hkern u1="x" u2="q" k="30" />
    <hkern u1="x" u2="o" k="35" />
    <hkern u1="x" u2="g" k="30" />
    <hkern u1="x" u2="e" k="35" />
    <hkern u1="x" u2="d" k="30" />
    <hkern u1="x" u2="c" k="35" />
    <hkern u1="x" u2="a" k="15" />
    <hkern u1="x" u2="]" k="10" />
    <hkern u1="x" u2="\" k="50" />
    <hkern u1="x" u2="&#x3f;" k="15" />
    <hkern u1="x" u2="&#x2d;" k="30" />
    <hkern u1="y" u2="&#x203a;" k="15" />
    <hkern u1="y" u2="&#x2039;" k="30" />
    <hkern u1="y" u2="&#x2026;" k="85" />
    <hkern u1="y" u2="&#x2014;" k="15" />
    <hkern u1="y" u2="&#x2013;" k="15" />
    <hkern u1="y" u2="&#x153;" k="30" />
    <hkern u1="y" u2="&#xe6;" k="25" />
    <hkern u1="y" u2="&#xbb;" k="15" />
    <hkern u1="y" u2="&#xab;" k="30" />
    <hkern u1="y" u2="&#x7d;" k="10" />
    <hkern u1="y" u2="z" k="5" />
    <hkern u1="y" u2="y" k="10" />
    <hkern u1="y" u2="x" k="10" />
    <hkern u1="y" u2="w" k="10" />
    <hkern u1="y" u2="v" k="15" />
    <hkern u1="y" u2="s" k="20" />
    <hkern u1="y" u2="q" k="25" />
    <hkern u1="y" u2="o" k="30" />
    <hkern u1="y" u2="g" k="25" />
    <hkern u1="y" u2="e" k="30" />
    <hkern u1="y" u2="d" k="25" />
    <hkern u1="y" u2="c" k="30" />
    <hkern u1="y" u2="a" k="25" />
    <hkern u1="y" u2="]" k="20" />
    <hkern u1="y" u2="\" k="50" />
    <hkern u1="y" u2="&#x3f;" k="10" />
    <hkern u1="y" u2="&#x2f;" k="70" />
    <hkern u1="y" u2="&#x2e;" k="85" />
    <hkern u1="y" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2c;" k="85" />
    <hkern u1="z" u2="&#x2039;" k="15" />
    <hkern u1="z" u2="&#x153;" k="15" />
    <hkern u1="z" u2="&#xab;" k="15" />
    <hkern u1="z" u2="q" k="15" />
    <hkern u1="z" u2="o" k="15" />
    <hkern u1="z" u2="g" k="15" />
    <hkern u1="z" u2="e" k="15" />
    <hkern u1="z" u2="d" k="15" />
    <hkern u1="z" u2="c" k="15" />
    <hkern u1="z" u2="\" k="45" />
    <hkern u1="&#x7b;" u2="&#x153;" k="15" />
    <hkern u1="&#x7b;" u2="&#x152;" k="20" />
    <hkern u1="&#x7b;" u2="&#xb5;" k="20" />
    <hkern u1="&#x7b;" u2="z" k="10" />
    <hkern u1="&#x7b;" u2="y" k="10" />
    <hkern u1="&#x7b;" u2="x" k="10" />
    <hkern u1="&#x7b;" u2="w" k="10" />
    <hkern u1="&#x7b;" u2="v" k="10" />
    <hkern u1="&#x7b;" u2="s" k="10" />
    <hkern u1="&#x7b;" u2="q" k="15" />
    <hkern u1="&#x7b;" u2="o" k="15" />
    <hkern u1="&#x7b;" u2="j" k="-35" />
    <hkern u1="&#x7b;" u2="g" k="10" />
    <hkern u1="&#x7b;" u2="e" k="15" />
    <hkern u1="&#x7b;" u2="d" k="15" />
    <hkern u1="&#x7b;" u2="c" k="15" />
    <hkern u1="&#x7b;" u2="Q" k="20" />
    <hkern u1="&#x7b;" u2="O" k="20" />
    <hkern u1="&#x7b;" u2="J" k="10" />
    <hkern u1="&#x7b;" u2="G" k="20" />
    <hkern u1="&#x7b;" u2="C" k="20" />
    <hkern u1="&#xa3;" u2="&#x34;" k="15" />
    <hkern u1="&#xa4;" u2="&#x31;" k="-20" />
    <hkern u1="&#xa5;" u2="&#x34;" k="20" />
    <hkern u1="&#xa7;" u2="&#x37;" k="15" />
    <hkern u1="&#xab;" u2="&#x153;" k="10" />
    <hkern u1="&#xab;" u2="y" k="15" />
    <hkern u1="&#xab;" u2="x" k="15" />
    <hkern u1="&#xab;" u2="w" k="10" />
    <hkern u1="&#xab;" u2="v" k="15" />
    <hkern u1="&#xab;" u2="q" k="5" />
    <hkern u1="&#xab;" u2="o" k="10" />
    <hkern u1="&#xab;" u2="g" k="5" />
    <hkern u1="&#xab;" u2="e" k="10" />
    <hkern u1="&#xab;" u2="d" k="5" />
    <hkern u1="&#xab;" u2="c" k="10" />
    <hkern u1="&#xab;" u2="Y" k="75" />
    <hkern u1="&#xab;" u2="X" k="20" />
    <hkern u1="&#xab;" u2="W" k="35" />
    <hkern u1="&#xab;" u2="V" k="40" />
    <hkern u1="&#xab;" u2="T" k="70" />
    <hkern u1="&#xb1;" u2="&#x2044;" k="-30" />
    <hkern u1="&#xb1;" u2="&#x37;" k="5" />
    <hkern u1="&#xb5;" u2="&#x2026;" k="40" />
    <hkern u1="&#xb5;" u2="&#xc6;" k="45" />
    <hkern u1="&#xb5;" u2="&#x7d;" k="20" />
    <hkern u1="&#xb5;" u2="x" k="5" />
    <hkern u1="&#xb5;" u2="]" k="20" />
    <hkern u1="&#xb5;" u2="\" k="40" />
    <hkern u1="&#xb5;" u2="Z" k="35" />
    <hkern u1="&#xb5;" u2="Y" k="60" />
    <hkern u1="&#xb5;" u2="X" k="50" />
    <hkern u1="&#xb5;" u2="W" k="40" />
    <hkern u1="&#xb5;" u2="V" k="45" />
    <hkern u1="&#xb5;" u2="T" k="30" />
    <hkern u1="&#xb5;" u2="S" k="5" />
    <hkern u1="&#xb5;" u2="J" k="30" />
    <hkern u1="&#xb5;" u2="A" k="45" />
    <hkern u1="&#xb5;" u2="&#x3f;" k="20" />
    <hkern u1="&#xb5;" u2="&#x2f;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2e;" k="40" />
    <hkern u1="&#xb5;" u2="&#x2c;" k="40" />
    <hkern u1="&#xb5;" u2="&#x29;" k="30" />
    <hkern u1="&#xbb;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbb;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbb;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbb;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbb;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="20" />
    <hkern u1="&#xbb;" u2="z" k="20" />
    <hkern u1="&#xbb;" u2="y" k="30" />
    <hkern u1="&#xbb;" u2="x" k="45" />
    <hkern u1="&#xbb;" u2="w" k="20" />
    <hkern u1="&#xbb;" u2="v" k="30" />
    <hkern u1="&#xbb;" u2="t" k="10" />
    <hkern u1="&#xbb;" u2="s" k="10" />
    <hkern u1="&#xbb;" u2="f" k="10" />
    <hkern u1="&#xbb;" u2="Z" k="15" />
    <hkern u1="&#xbb;" u2="Y" k="100" />
    <hkern u1="&#xbb;" u2="X" k="50" />
    <hkern u1="&#xbb;" u2="W" k="50" />
    <hkern u1="&#xbb;" u2="V" k="60" />
    <hkern u1="&#xbb;" u2="T" k="90" />
    <hkern u1="&#xbb;" u2="S" k="10" />
    <hkern u1="&#xbb;" u2="A" k="20" />
    <hkern u1="&#xbf;" u2="&#xfb02;" k="10" />
    <hkern u1="&#xbf;" u2="&#xfb01;" k="10" />
    <hkern u1="&#xbf;" u2="&#x2248;" k="10" />
    <hkern u1="&#xbf;" u2="&#x221a;" k="10" />
    <hkern u1="&#xbf;" u2="&#x3a9;" k="10" />
    <hkern u1="&#xbf;" u2="&#x152;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="-10" />
    <hkern u1="&#xbf;" u2="&#xb5;" k="20" />
    <hkern u1="&#xbf;" u2="y" k="35" />
    <hkern u1="&#xbf;" u2="w" k="35" />
    <hkern u1="&#xbf;" u2="v" k="45" />
    <hkern u1="&#xbf;" u2="t" k="15" />
    <hkern u1="&#xbf;" u2="f" k="10" />
    <hkern u1="&#xbf;" u2="a" k="-10" />
    <hkern u1="&#xbf;" u2="Y" k="70" />
    <hkern u1="&#xbf;" u2="X" k="10" />
    <hkern u1="&#xbf;" u2="W" k="50" />
    <hkern u1="&#xbf;" u2="V" k="60" />
    <hkern u1="&#xbf;" u2="U" k="15" />
    <hkern u1="&#xbf;" u2="T" k="60" />
    <hkern u1="&#xbf;" u2="Q" k="20" />
    <hkern u1="&#xbf;" u2="O" k="20" />
    <hkern u1="&#xbf;" u2="G" k="20" />
    <hkern u1="&#xbf;" u2="C" k="20" />
    <hkern u1="&#xc6;" u2="&#x153;" k="10" />
    <hkern u1="&#xc6;" u2="y" k="10" />
    <hkern u1="&#xc6;" u2="w" k="10" />
    <hkern u1="&#xc6;" u2="v" k="10" />
    <hkern u1="&#xc6;" u2="o" k="10" />
    <hkern u1="&#xc6;" u2="e" k="10" />
    <hkern u1="&#xc6;" u2="d" k="10" />
    <hkern u1="&#xc6;" u2="c" k="10" />
    <hkern u1="&#xdf;" u2="y" k="10" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="w" k="5" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xe6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="10" />
    <hkern u1="&#xe6;" u2="z" k="15" />
    <hkern u1="&#xe6;" u2="y" k="25" />
    <hkern u1="&#xe6;" u2="x" k="30" />
    <hkern u1="&#xe6;" u2="w" k="25" />
    <hkern u1="&#xe6;" u2="v" k="25" />
    <hkern u1="&#xe6;" u2="]" k="20" />
    <hkern u1="&#xe6;" u2="\" k="80" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="40" />
    <hkern u1="&#xe6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="20" />
    <hkern u1="&#xe6;" u2="&#x29;" k="30" />
    <hkern u1="&#x152;" u2="&#x153;" k="10" />
    <hkern u1="&#x152;" u2="y" k="10" />
    <hkern u1="&#x152;" u2="w" k="10" />
    <hkern u1="&#x152;" u2="v" k="10" />
    <hkern u1="&#x152;" u2="o" k="10" />
    <hkern u1="&#x152;" u2="e" k="10" />
    <hkern u1="&#x152;" u2="d" k="10" />
    <hkern u1="&#x152;" u2="c" k="10" />
    <hkern u1="&#x153;" u2="&#x2026;" k="10" />
    <hkern u1="&#x153;" u2="&#x7d;" k="10" />
    <hkern u1="&#x153;" u2="z" k="15" />
    <hkern u1="&#x153;" u2="y" k="25" />
    <hkern u1="&#x153;" u2="x" k="30" />
    <hkern u1="&#x153;" u2="w" k="25" />
    <hkern u1="&#x153;" u2="v" k="25" />
    <hkern u1="&#x153;" u2="]" k="20" />
    <hkern u1="&#x153;" u2="\" k="80" />
    <hkern u1="&#x153;" u2="&#x3f;" k="40" />
    <hkern u1="&#x153;" u2="&#x2e;" k="10" />
    <hkern u1="&#x153;" u2="&#x2c;" k="10" />
    <hkern u1="&#x153;" u2="&#x2a;" k="20" />
    <hkern u1="&#x153;" u2="&#x29;" k="30" />
    <hkern u1="&#x192;" u2="&#x35;" k="10" />
    <hkern u1="&#x192;" u2="&#x34;" k="30" />
    <hkern u1="&#x192;" u2="&#x31;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2122;" k="-40" />
    <hkern u1="&#x3a9;" u2="&#x2039;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x2026;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x201d;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x201c;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x2019;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x153;" k="10" />
    <hkern u1="&#x3a9;" u2="&#xe6;" k="15" />
    <hkern u1="&#x3a9;" u2="&#xab;" k="15" />
    <hkern u1="&#x3a9;" u2="&#x7d;" k="-30" />
    <hkern u1="&#x3a9;" u2="z" k="10" />
    <hkern u1="&#x3a9;" u2="q" k="10" />
    <hkern u1="&#x3a9;" u2="o" k="10" />
    <hkern u1="&#x3a9;" u2="g" k="10" />
    <hkern u1="&#x3a9;" u2="e" k="10" />
    <hkern u1="&#x3a9;" u2="d" k="10" />
    <hkern u1="&#x3a9;" u2="c" k="10" />
    <hkern u1="&#x3a9;" u2="a" k="15" />
    <hkern u1="&#x3a9;" u2="]" k="-20" />
    <hkern u1="&#x3a9;" u2="\" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x3f;" k="-35" />
    <hkern u1="&#x3a9;" u2="&#x2f;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2e;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2c;" k="45" />
    <hkern u1="&#x3a9;" u2="&#x2a;" k="-30" />
    <hkern u1="&#x3a9;" u2="&#x29;" k="-30" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2013;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2013;" u2="z" k="10" />
    <hkern u1="&#x2013;" u2="y" k="15" />
    <hkern u1="&#x2013;" u2="x" k="30" />
    <hkern u1="&#x2013;" u2="w" k="10" />
    <hkern u1="&#x2013;" u2="v" k="15" />
    <hkern u1="&#x2013;" u2="Z" k="30" />
    <hkern u1="&#x2013;" u2="Y" k="80" />
    <hkern u1="&#x2013;" u2="X" k="50" />
    <hkern u1="&#x2013;" u2="W" k="35" />
    <hkern u1="&#x2013;" u2="V" k="40" />
    <hkern u1="&#x2013;" u2="T" k="90" />
    <hkern u1="&#x2013;" u2="A" k="40" />
    <hkern u1="&#x2013;" u2="&#x37;" k="40" />
    <hkern u1="&#x2013;" u2="&#x33;" k="10" />
    <hkern u1="&#x2013;" u2="&#x31;" k="30" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2014;" u2="&#xb1;" k="10" />
    <hkern u1="&#x2014;" u2="z" k="10" />
    <hkern u1="&#x2014;" u2="y" k="15" />
    <hkern u1="&#x2014;" u2="x" k="30" />
    <hkern u1="&#x2014;" u2="w" k="10" />
    <hkern u1="&#x2014;" u2="v" k="15" />
    <hkern u1="&#x2014;" u2="Z" k="30" />
    <hkern u1="&#x2014;" u2="Y" k="80" />
    <hkern u1="&#x2014;" u2="X" k="50" />
    <hkern u1="&#x2014;" u2="W" k="35" />
    <hkern u1="&#x2014;" u2="V" k="40" />
    <hkern u1="&#x2014;" u2="T" k="90" />
    <hkern u1="&#x2014;" u2="A" k="40" />
    <hkern u1="&#x2014;" u2="&#x37;" k="40" />
    <hkern u1="&#x2014;" u2="&#x33;" k="10" />
    <hkern u1="&#x2014;" u2="&#x31;" k="30" />
    <hkern u1="&#x2018;" u2="&#x153;" k="15" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="90" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="35" />
    <hkern u1="&#x2018;" u2="z" k="10" />
    <hkern u1="&#x2018;" u2="t" k="-15" />
    <hkern u1="&#x2018;" u2="s" k="10" />
    <hkern u1="&#x2018;" u2="q" k="15" />
    <hkern u1="&#x2018;" u2="o" k="15" />
    <hkern u1="&#x2018;" u2="g" k="15" />
    <hkern u1="&#x2018;" u2="e" k="15" />
    <hkern u1="&#x2018;" u2="d" k="15" />
    <hkern u1="&#x2018;" u2="c" k="15" />
    <hkern u1="&#x2018;" u2="a" k="10" />
    <hkern u1="&#x2018;" u2="J" k="80" />
    <hkern u1="&#x2018;" u2="A" k="90" />
    <hkern u1="&#x2019;" u2="&#x153;" k="35" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="20" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="100" />
    <hkern u1="&#x2019;" u2="s" k="15" />
    <hkern u1="&#x2019;" u2="q" k="30" />
    <hkern u1="&#x2019;" u2="o" k="35" />
    <hkern u1="&#x2019;" u2="g" k="30" />
    <hkern u1="&#x2019;" u2="e" k="35" />
    <hkern u1="&#x2019;" u2="d" k="30" />
    <hkern u1="&#x2019;" u2="c" k="35" />
    <hkern u1="&#x2019;" u2="a" k="20" />
    <hkern u1="&#x2019;" u2="J" k="100" />
    <hkern u1="&#x2019;" u2="A" k="100" />
    <hkern u1="&#x201a;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201a;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201a;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201a;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201a;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201a;" u2="&#x153;" k="20" />
    <hkern u1="&#x201a;" u2="&#x152;" k="40" />
    <hkern u1="&#x201a;" u2="&#xb5;" k="40" />
    <hkern u1="&#x201a;" u2="y" k="45" />
    <hkern u1="&#x201a;" u2="w" k="70" />
    <hkern u1="&#x201a;" u2="v" k="85" />
    <hkern u1="&#x201a;" u2="t" k="25" />
    <hkern u1="&#x201a;" u2="q" k="10" />
    <hkern u1="&#x201a;" u2="o" k="20" />
    <hkern u1="&#x201a;" u2="j" k="-15" />
    <hkern u1="&#x201a;" u2="g" k="10" />
    <hkern u1="&#x201a;" u2="f" k="15" />
    <hkern u1="&#x201a;" u2="e" k="20" />
    <hkern u1="&#x201a;" u2="d" k="10" />
    <hkern u1="&#x201a;" u2="c" k="20" />
    <hkern u1="&#x201a;" u2="Y" k="130" />
    <hkern u1="&#x201a;" u2="W" k="100" />
    <hkern u1="&#x201a;" u2="V" k="120" />
    <hkern u1="&#x201a;" u2="U" k="15" />
    <hkern u1="&#x201a;" u2="T" k="100" />
    <hkern u1="&#x201a;" u2="Q" k="40" />
    <hkern u1="&#x201a;" u2="O" k="40" />
    <hkern u1="&#x201a;" u2="G" k="40" />
    <hkern u1="&#x201a;" u2="C" k="40" />
    <hkern u1="&#x201c;" u2="&#x153;" k="15" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="90" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="35" />
    <hkern u1="&#x201c;" u2="z" k="10" />
    <hkern u1="&#x201c;" u2="t" k="-15" />
    <hkern u1="&#x201c;" u2="s" k="10" />
    <hkern u1="&#x201c;" u2="q" k="15" />
    <hkern u1="&#x201c;" u2="o" k="15" />
    <hkern u1="&#x201c;" u2="g" k="15" />
    <hkern u1="&#x201c;" u2="e" k="15" />
    <hkern u1="&#x201c;" u2="d" k="15" />
    <hkern u1="&#x201c;" u2="c" k="15" />
    <hkern u1="&#x201c;" u2="a" k="10" />
    <hkern u1="&#x201c;" u2="J" k="80" />
    <hkern u1="&#x201c;" u2="A" k="90" />
    <hkern u1="&#x201e;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x201e;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x201e;" u2="&#x2248;" k="15" />
    <hkern u1="&#x201e;" u2="&#x221a;" k="15" />
    <hkern u1="&#x201e;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x201e;" u2="&#x153;" k="20" />
    <hkern u1="&#x201e;" u2="&#x152;" k="40" />
    <hkern u1="&#x201e;" u2="&#xb5;" k="40" />
    <hkern u1="&#x201e;" u2="y" k="45" />
    <hkern u1="&#x201e;" u2="w" k="70" />
    <hkern u1="&#x201e;" u2="v" k="85" />
    <hkern u1="&#x201e;" u2="t" k="25" />
    <hkern u1="&#x201e;" u2="q" k="10" />
    <hkern u1="&#x201e;" u2="o" k="20" />
    <hkern u1="&#x201e;" u2="j" k="-15" />
    <hkern u1="&#x201e;" u2="g" k="10" />
    <hkern u1="&#x201e;" u2="f" k="15" />
    <hkern u1="&#x201e;" u2="e" k="20" />
    <hkern u1="&#x201e;" u2="d" k="10" />
    <hkern u1="&#x201e;" u2="c" k="20" />
    <hkern u1="&#x201e;" u2="Y" k="130" />
    <hkern u1="&#x201e;" u2="W" k="100" />
    <hkern u1="&#x201e;" u2="V" k="120" />
    <hkern u1="&#x201e;" u2="U" k="15" />
    <hkern u1="&#x201e;" u2="T" k="100" />
    <hkern u1="&#x201e;" u2="Q" k="40" />
    <hkern u1="&#x201e;" u2="O" k="40" />
    <hkern u1="&#x201e;" u2="G" k="40" />
    <hkern u1="&#x201e;" u2="C" k="40" />
    <hkern u1="&#x2026;" u2="&#xfb02;" k="15" />
    <hkern u1="&#x2026;" u2="&#xfb01;" k="15" />
    <hkern u1="&#x2026;" u2="&#x2248;" k="15" />
    <hkern u1="&#x2026;" u2="&#x221a;" k="15" />
    <hkern u1="&#x2026;" u2="&#x3a9;" k="15" />
    <hkern u1="&#x2026;" u2="&#x153;" k="20" />
    <hkern u1="&#x2026;" u2="&#x152;" k="40" />
    <hkern u1="&#x2026;" u2="&#xb5;" k="40" />
    <hkern u1="&#x2026;" u2="y" k="60" />
    <hkern u1="&#x2026;" u2="w" k="70" />
    <hkern u1="&#x2026;" u2="v" k="85" />
    <hkern u1="&#x2026;" u2="t" k="25" />
    <hkern u1="&#x2026;" u2="q" k="10" />
    <hkern u1="&#x2026;" u2="o" k="20" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2026;" u2="f" k="15" />
    <hkern u1="&#x2026;" u2="e" k="20" />
    <hkern u1="&#x2026;" u2="d" k="10" />
    <hkern u1="&#x2026;" u2="c" k="20" />
    <hkern u1="&#x2026;" u2="Y" k="130" />
    <hkern u1="&#x2026;" u2="W" k="100" />
    <hkern u1="&#x2026;" u2="V" k="120" />
    <hkern u1="&#x2026;" u2="U" k="15" />
    <hkern u1="&#x2026;" u2="T" k="100" />
    <hkern u1="&#x2026;" u2="Q" k="40" />
    <hkern u1="&#x2026;" u2="O" k="40" />
    <hkern u1="&#x2026;" u2="G" k="40" />
    <hkern u1="&#x2026;" u2="C" k="40" />
    <hkern u1="&#x2039;" u2="&#x153;" k="10" />
    <hkern u1="&#x2039;" u2="y" k="15" />
    <hkern u1="&#x2039;" u2="x" k="15" />
    <hkern u1="&#x2039;" u2="w" k="10" />
    <hkern u1="&#x2039;" u2="v" k="15" />
    <hkern u1="&#x2039;" u2="q" k="5" />
    <hkern u1="&#x2039;" u2="o" k="10" />
    <hkern u1="&#x2039;" u2="g" k="5" />
    <hkern u1="&#x2039;" u2="e" k="10" />
    <hkern u1="&#x2039;" u2="d" k="5" />
    <hkern u1="&#x2039;" u2="c" k="10" />
    <hkern u1="&#x2039;" u2="Y" k="75" />
    <hkern u1="&#x2039;" u2="X" k="20" />
    <hkern u1="&#x2039;" u2="W" k="35" />
    <hkern u1="&#x2039;" u2="V" k="40" />
    <hkern u1="&#x2039;" u2="T" k="70" />
    <hkern u1="&#x203a;" u2="&#xfb02;" k="10" />
    <hkern u1="&#x203a;" u2="&#xfb01;" k="10" />
    <hkern u1="&#x203a;" u2="&#x2248;" k="10" />
    <hkern u1="&#x203a;" u2="&#x221a;" k="10" />
    <hkern u1="&#x203a;" u2="&#x3a9;" k="10" />
    <hkern u1="&#x203a;" u2="z" k="20" />
    <hkern u1="&#x203a;" u2="y" k="30" />
    <hkern u1="&#x203a;" u2="x" k="45" />
    <hkern u1="&#x203a;" u2="w" k="20" />
    <hkern u1="&#x203a;" u2="v" k="30" />
    <hkern u1="&#x203a;" u2="t" k="10" />
    <hkern u1="&#x203a;" u2="s" k="10" />
    <hkern u1="&#x203a;" u2="f" k="10" />
    <hkern u1="&#x2044;" u2="&#xb1;" k="-15" />
    <hkern u1="&#x2044;" u2="&#x39;" k="-20" />
    <hkern u1="&#x2044;" u2="&#x38;" k="-20" />
    <hkern u1="&#x2044;" u2="&#x37;" k="-30" />
    <hkern u1="&#x2044;" u2="&#x35;" k="-20" />
    <hkern u1="&#x2044;" u2="&#x34;" k="75" />
    <hkern u1="&#x2044;" u2="&#x33;" k="-40" />
    <hkern u1="&#x2044;" u2="&#x32;" k="-10" />
    <hkern u1="&#x2044;" u2="&#x31;" k="-60" />
  </font>
</defs></svg>
