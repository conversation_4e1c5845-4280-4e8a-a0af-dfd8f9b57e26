<?php get_header(); ?>
<?php if (is_home() || is_front_page()): ?>
  <div class="wrapper">

    <section id="introduce" class="section-wrapper-main">
      <div class="section-wrapper">
        <img class="introduce__avatar pc-only" src="<?php echo get_template_directory_uri(); ?>/images/renew/introduce.jpg"
          alt="Slide 1">
        <div class="introduce__content">
          <div class="section-title__with-symbol">
            <p class="section__title">
              全てのお客様にご満足いただくために
              <span><img class="symbol__icon"
                  src="<?php echo get_template_directory_uri(); ?>/images/renew/cow-gold.png" /></span>
            </p>
          </div>
          <img class="introduce__avatar sp-only"
            src="<?php echo get_template_directory_uri(); ?>/images/renew/introduce.jpg" alt="Slide 1">
          <p class="introduce__paragraph">
            私たちは、単なる価格訴求ではなく、「良質な商品を適正価格で提供すること」 を通じて、すべての <br>
            お客様にご満足いただけることを目指しています。価格の安さだけでなく、品質や価値にもこだわり、 <br>
            お客様一人ひとりのニーズに寄り添いながら、最適な提案ができる企業を目指します。
            <br>
            「美味しさ」はもちろんのこと、「安全・安心」 を最優先に考え、産地・生産・物流・販売のすべて <br>
            の工程で徹底した品質管理を行い、お客様に信頼される商品をお届けするとともに、商品力の強化に加 <br>
            え、営業力やサービスの質の向上にも力を入れ、最良の選択肢を提供できるよう努めます。 <br>

            また、市場の変化に柔軟に対応できる企業であり続けるために、社員の成長を重視し、常に最新の情報 <br>
            を学びながら、商品力・提案力・人間力を高め、より良い未来を創造してまいります。
          </p>
        </div>
      </div>
    </section>

    <section id="" class="section-wrapper-main-news">
      <div class="section-wrapper">
        <div class="news-block">
          <h4>お知らせ ニュース</h4>
          <ul class="news-block__list">
            <li class="news-block__item">
              <div class="news-block__header">
                <img src="<?php echo get_template_directory_uri(); ?>/images/renew/beach-6517214_1280.png" alt="Slide 1" class="pc-only">
                <div>
                  <p class="news-block__title">
                    夏季休業日のお知らせ
                  </p>
                  <p class="news-block__date">
                    2024年7月24日
                  </p>
                </div>
              </div>
              <p class="news-block__item__content">
                <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/beach-6517214_1280.png'); ?>" alt="Slide 1" class="sp-only">
                <span class="news-block__item__content--title">
                  誠に勝手ながら当社では、以下の期間を夏季休業とさせていただきます。
                  <br>
                  2024年8月10日(土)～2024年8月15日(木)
                  <br>
                  上記以外の日程におきましても、各お取引先様の休業で出荷等対応が出来ない可能性もございます。ご理解賜りますようお願い申し上げます。
                </span>

              </p>
            </li>

            <li class="news-block__item">
              <div class="news-block__header">
                <img src="<?php echo get_template_directory_uri(); ?>/images/renew/2024-NEWS.png" alt="Slide 1" class="pc-only">
                <div>
                  <p class="news-block__title">
                    年末年始休業日のお知らせ
                  </p>
                  <p class="news-block__date">
                    2023年12月25日
                  </p>
                </div>
              </div>
              <p class="news-block__item__content">
                <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/2024-NEWS.png'); ?>" alt="Slide 1" class="sp-only">
                <span class="news-block__item__content--title">
                  拝啓　時下ますますご清栄のこととお喜び申し上げます。平素は格別のご高配を賜 <br>
                  り、厚く御礼申し上げます。さて、弊社では誠に勝手ながら、年末年始の休業日を<br>
                  下記の通りとさせて頂きます。何かとご不便をお掛け致します<br>
                  が、御理解賜ります様、宜しく御願い致します。
                </span>
                <br>
                <span class="news-block__item__content--sincerely">
                  敬具
                </span>
                <br>
               
              <span class="d-flex-block">
                <span class="block-date">
                  12月29日（金） <br>
                  12月30日（土）～ 1月3日（水） <br>
                  1月4日　（木）
                </span>
                <span class="block-content">
                  年内最終営業日 <br>
                  休業日 <br>
                  年始仕事始め
                </span>
              </span>
              </p>
            </li>

            <li class="news-block__item">
              <div class="news-block__header">
                <img src="<?php echo get_template_directory_uri(); ?>/images/renew/ai-generated-7958926_1280.png" alt="Slide 1" class="pc-only">
                <div>
                  <p class="news-block__title">
                    弊社使用システムへの不正アクセス事案のお知らせ
                  </p>
                  <p class="news-block__date">
                    2023年8月4日
                  </p>
                </div>
              </div>
              <p class="news-block__item__content">
                <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/ai-generated-7958926_1280.png'); ?>" alt="Slide 1" class="sp-only">
                <span class="news-block__item__content--title">
                  過去に弊社と雇用関係のあった皆様へ
                </span>

              </p>
            </li>
          </ul>
        </div>
        <img class=" pc-only" src="<?php echo get_template_directory_uri(); ?>/images/renew/ASMO MART BANNER.png"
          alt="Slide 1">
      </div>
    </section>

    <section class="cooperate-group">
      <p class="cooperate-group__title">グループ会社一覧</p>
      <?php
      $companies = [
        ['url' => 'https://asmo1.co.jp', 'main' => 'cooperate-group_1.jpg', 'sub' => 'logo.png'],
        ['url' => 'https://www.asmo-foodservice.co.jp/', 'main' => 'cooperate-group_2.jpg', 'sub' => 'logo_food.png'],
        ['url' => 'https://www.asmokaigo.co.jp/', 'main' => 'cooperate-group_3.jpg', 'sub' => 'logo_kaigo.png'],
        ['url' => 'https://www.asmo-catering.com/ja/', 'main' => 'cooperate-group_4.jpg', 'sub' => 'logo_catering.png'],
      ];
      foreach ($companies as $company): ?>
        <a href="<?php echo $company['url']; ?>" class="cooperate-group__item">
          <div class="cooperate-group__image--main">
            <img src="<?php echo get_template_directory_uri(); ?>/images/renew/<?php echo $company['main']; ?>"
              alt="Company">
          </div>
          <div class="cooperate-group__image--sub">
            <img src="<?php echo get_template_directory_uri(); ?>/images/renew/<?php echo $company['sub']; ?>" alt="Logo">
          </div>
        </a>
      <?php endforeach; ?>
    </section>
  </div>
<?php else: ?>
  <?php
  $pages = [
    'about' => 'about.php',
    'introduce' => 'introduce.php',
    'business' => 'business.php',
    'privacy' => 'privacy.php',
    'sitepolicy' => 'sitepolicy.php',
    'recruit' => 'recruit.php',
    'inquiry' => 'inquiry/index.php',
    'inquiry/confirm' => 'inquiry/confirm.php',
    'inquiry/complete' => 'inquiry/complete.php',
    'jobs' => 'jobs/index.php',
    'jobs/confirm' => 'jobs/confirm.php',
    'jobs/complete' => 'jobs/complete.php',
  ];
  $page = $pages[$wp->request] ?? 'error/404-file-not-found.php';
  include($page);
  ?>
<?php endif; ?>

<?php get_footer(); ?>


<script>
  $(document).ready(function() {
    // Toggle news block content for mobile
    const toggleNewsBlock = () => {
      const isMobile = window.matchMedia("(max-width: 767px)").matches;
      $('.news-block__item').off('click'); // Remove previous click handlers
      if (isMobile) {
        $('.news-block__item').on('click', function() {
          $(this).find('.news-block__item__content').slideToggle();
          $(this).toggleClass('active');
        });
      } else {
        $('.news-block__item__content').removeAttr('style'); // Reset styles for desktop
        $('.news-block__item').removeClass('active');
      }
    };

    // Initial call and bind resize event
    toggleNewsBlock();
    $(window).on('resize', toggleNewsBlock);
  });
</script>

<style>
  .news-block__item__content--title {
    font-family: 'Kozuka Gothic Pr6N', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #000;
    letter-spacing: 0.5px;
  }

  .mart-block img {
    width: fit-content !important;
    height: auto;
  }

  .news-block__item__content--sincerely {
    float: right;
    font-family: 'Kozuka Gothic Pr6N', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #000;
  }

  .d-flex-block {
    display: flex;
    justify-content: flex-start;
    margin-top: 10px;
  }

  .block-content {
    margin: 0px 50px 0 50px;
  }

  .section-wrapper-main-news {
    max-width: 1400px;
    margin: 0 auto;
  }
</style>