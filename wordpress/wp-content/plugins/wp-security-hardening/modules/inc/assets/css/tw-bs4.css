.tw-bs4 {
  /*# sourceMappingURL=bootstrap.css.map */
}
.tw-bs4 :root {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #007bff;
  --secondary: #6c757d;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.tw-bs4 *, .tw-bs4 *::before, .tw-bs4 *::after {
  box-sizing: border-box;
}
.tw-bs4 html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.tw-bs4 article, .tw-bs4 aside, .tw-bs4 figcaption, .tw-bs4 figure, .tw-bs4 footer, .tw-bs4 header, .tw-bs4 hgroup, .tw-bs4 main, .tw-bs4 nav, .tw-bs4 section {
  display: block;
}
.tw-bs4 body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}
.tw-bs4 [tabindex="-1"]:focus {
  outline: 0 !important;
}
.tw-bs4 hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
.tw-bs4 h1, .tw-bs4 h2, .tw-bs4 h3, .tw-bs4 h4, .tw-bs4 h5, .tw-bs4 h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
.tw-bs4 p {
  margin-top: 0;
  margin-bottom: 1rem;
}
.tw-bs4 abbr[title], .tw-bs4 abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}
.tw-bs4 address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
.tw-bs4 ol, .tw-bs4 ul, .tw-bs4 dl {
  margin-top: 0;
  margin-bottom: 1rem;
}
.tw-bs4 ol ol, .tw-bs4 ul ul, .tw-bs4 ol ul, .tw-bs4 ul ol {
  margin-bottom: 0;
}
.tw-bs4 dt {
  font-weight: 700;
}
.tw-bs4 dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
.tw-bs4 blockquote {
  margin: 0 0 1rem;
}
.tw-bs4 b, .tw-bs4 strong {
  font-weight: bolder;
}
.tw-bs4 small {
  font-size: 80%;
}
.tw-bs4 sub, .tw-bs4 sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
.tw-bs4 sub {
  bottom: -0.25em;
}
.tw-bs4 sup {
  top: -0.5em;
}
.tw-bs4 a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
}
.tw-bs4 a:hover {
  color: #0056b3;
  text-decoration: underline;
}
.tw-bs4 a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}
.tw-bs4 a:not([href]):not([tabindex]):hover, .tw-bs4 a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}
.tw-bs4 a:not([href]):not([tabindex]):focus {
  outline: 0;
}
.tw-bs4 pre, .tw-bs4 code, .tw-bs4 kbd, .tw-bs4 samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}
.tw-bs4 pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
}
.tw-bs4 figure {
  margin: 0 0 1rem;
}
.tw-bs4 img {
  vertical-align: middle;
  border-style: none;
}
.tw-bs4 svg {
  overflow: hidden;
  vertical-align: middle;
}
.tw-bs4 table {
  border-collapse: collapse;
}
.tw-bs4 caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}
.tw-bs4 th {
  text-align: inherit;
}
.tw-bs4 label {
  display: inline-block;
  margin-bottom: 0.5rem;
}
.tw-bs4 button {
  border-radius: 0;
}
.tw-bs4 button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}
.tw-bs4 input, .tw-bs4 button, .tw-bs4 select, .tw-bs4 optgroup, .tw-bs4 textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.tw-bs4 button, .tw-bs4 input {
  overflow: visible;
}
.tw-bs4 button, .tw-bs4 select {
  text-transform: none;
}
.tw-bs4 select {
  word-wrap: normal;
}
.tw-bs4 button, .tw-bs4 [type="button"], .tw-bs4 [type="reset"], .tw-bs4 [type="submit"] {
  -webkit-appearance: button;
}
.tw-bs4 button:not(:disabled), .tw-bs4 [type="button"]:not(:disabled), .tw-bs4 [type="reset"]:not(:disabled), .tw-bs4 [type="submit"]:not(:disabled) {
  cursor: pointer;
}
.tw-bs4 button::-moz-focus-inner, .tw-bs4 [type="button"]::-moz-focus-inner, .tw-bs4 [type="reset"]::-moz-focus-inner, .tw-bs4 [type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
.tw-bs4 input[type="radio"], .tw-bs4 input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}
.tw-bs4 input[type="date"], .tw-bs4 input[type="time"], .tw-bs4 input[type="datetime-local"], .tw-bs4 input[type="month"] {
  -webkit-appearance: listbox;
}
.tw-bs4 textarea {
  overflow: auto;
  resize: vertical;
}
.tw-bs4 fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
.tw-bs4 legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}
.tw-bs4 progress {
  vertical-align: baseline;
}
.tw-bs4 [type="number"]::-webkit-inner-spin-button, .tw-bs4 [type="number"]::-webkit-outer-spin-button {
  height: auto;
}
.tw-bs4 [type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
.tw-bs4 [type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
.tw-bs4 ::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
.tw-bs4 output {
  display: inline-block;
}
.tw-bs4 summary {
  display: list-item;
  cursor: pointer;
}
.tw-bs4 template {
  display: none;
}
.tw-bs4 [hidden] {
  display: none !important;
}
.tw-bs4 h1, .tw-bs4 h2, .tw-bs4 h3, .tw-bs4 h4, .tw-bs4 h5, .tw-bs4 h6, .tw-bs4 .h1, .tw-bs4 .h2, .tw-bs4 .h3, .tw-bs4 .h4, .tw-bs4 .h5, .tw-bs4 .h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}
.tw-bs4 h1, .tw-bs4 .h1 {
  font-size: 2.5rem;
}
.tw-bs4 h2, .tw-bs4 .h2 {
  font-size: 2rem;
}
.tw-bs4 h3, .tw-bs4 .h3 {
  font-size: 1.75rem;
}
.tw-bs4 h4, .tw-bs4 .h4 {
  font-size: 1.5rem;
}
.tw-bs4 h5, .tw-bs4 .h5 {
  font-size: 1.25rem;
}
.tw-bs4 h6, .tw-bs4 .h6 {
  font-size: 1rem;
}
.tw-bs4 .lead {
  font-size: 1.25rem;
  font-weight: 300;
}
.tw-bs4 .display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}
.tw-bs4 .display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}
.tw-bs4 .display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2;
}
.tw-bs4 .display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2;
}
.tw-bs4 hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.tw-bs4 small, .tw-bs4 .small {
  font-size: 80%;
  font-weight: 400;
}
.tw-bs4 mark, .tw-bs4 .mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}
.tw-bs4 .list-unstyled {
  padding-left: 0;
  list-style: none;
}
.tw-bs4 .list-inline {
  padding-left: 0;
  list-style: none;
}
.tw-bs4 .list-inline-item {
  display: inline-block;
}
.tw-bs4 .list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}
.tw-bs4 .initialism {
  font-size: 90%;
  text-transform: uppercase;
}
.tw-bs4 .blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.tw-bs4 .blockquote-footer {
  display: block;
  font-size: 80%;
  color: #6c757d;
}
.tw-bs4 .blockquote-footer::before {
  content: "\2014\00A0";
}
.tw-bs4 .img-fluid {
  max-width: 100%;
  height: auto;
}
.tw-bs4 .img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}
.tw-bs4 .figure {
  display: inline-block;
}
.tw-bs4 .figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}
.tw-bs4 .figure-caption {
  font-size: 90%;
  color: #6c757d;
}
.tw-bs4 code {
  font-size: 87.5%;
  color: #e83e8c;
  word-break: break-word;
}
.tw-bs4 a > code {
  color: inherit;
}
.tw-bs4 kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}
.tw-bs4 kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}
.tw-bs4 pre {
  display: block;
  font-size: 87.5%;
  color: #212529;
}
.tw-bs4 pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}
.tw-bs4 .pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.tw-bs4 .container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .tw-bs4 .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .container {
    max-width: 1140px;
  }
}
.tw-bs4 .container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
.tw-bs4 .row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}
.tw-bs4 .no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.tw-bs4 .no-gutters > .col, .tw-bs4 .no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}
.tw-bs4 .col-1, .tw-bs4 .col-2, .tw-bs4 .col-3, .tw-bs4 .col-4, .tw-bs4 .col-5, .tw-bs4 .col-6, .tw-bs4 .col-7, .tw-bs4 .col-8, .tw-bs4 .col-9, .tw-bs4 .col-10, .tw-bs4 .col-11, .tw-bs4 .col-12, .tw-bs4 .col, .tw-bs4 .col-auto, .tw-bs4 .col-sm-1, .tw-bs4 .col-sm-2, .tw-bs4 .col-sm-3, .tw-bs4 .col-sm-4, .tw-bs4 .col-sm-5, .tw-bs4 .col-sm-6, .tw-bs4 .col-sm-7, .tw-bs4 .col-sm-8, .tw-bs4 .col-sm-9, .tw-bs4 .col-sm-10, .tw-bs4 .col-sm-11, .tw-bs4 .col-sm-12, .tw-bs4 .col-sm, .tw-bs4 .col-sm-auto, .tw-bs4 .col-md-1, .tw-bs4 .col-md-2, .tw-bs4 .col-md-3, .tw-bs4 .col-md-4, .tw-bs4 .col-md-5, .tw-bs4 .col-md-6, .tw-bs4 .col-md-7, .tw-bs4 .col-md-8, .tw-bs4 .col-md-9, .tw-bs4 .col-md-10, .tw-bs4 .col-md-11, .tw-bs4 .col-md-12, .tw-bs4 .col-md, .tw-bs4 .col-md-auto, .tw-bs4 .col-lg-1, .tw-bs4 .col-lg-2, .tw-bs4 .col-lg-3, .tw-bs4 .col-lg-4, .tw-bs4 .col-lg-5, .tw-bs4 .col-lg-6, .tw-bs4 .col-lg-7, .tw-bs4 .col-lg-8, .tw-bs4 .col-lg-9, .tw-bs4 .col-lg-10, .tw-bs4 .col-lg-11, .tw-bs4 .col-lg-12, .tw-bs4 .col-lg, .tw-bs4 .col-lg-auto, .tw-bs4 .col-xl-1, .tw-bs4 .col-xl-2, .tw-bs4 .col-xl-3, .tw-bs4 .col-xl-4, .tw-bs4 .col-xl-5, .tw-bs4 .col-xl-6, .tw-bs4 .col-xl-7, .tw-bs4 .col-xl-8, .tw-bs4 .col-xl-9, .tw-bs4 .col-xl-10, .tw-bs4 .col-xl-11, .tw-bs4 .col-xl-12, .tw-bs4 .col-xl, .tw-bs4 .col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}
.tw-bs4 .col {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 100%;
}
.tw-bs4 .col-auto {
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}
.tw-bs4 .col-1 {
  -ms-flex: 0 0 8.33333%;
  flex: 0 0 8.33333%;
  max-width: 8.33333%;
}
.tw-bs4 .col-2 {
  -ms-flex: 0 0 16.66667%;
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}
.tw-bs4 .col-3 {
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
}
.tw-bs4 .col-4 {
  -ms-flex: 0 0 33.33333%;
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}
.tw-bs4 .col-5 {
  -ms-flex: 0 0 41.66667%;
  flex: 0 0 41.66667%;
  max-width: 41.66667%;
}
.tw-bs4 .col-6 {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
}
.tw-bs4 .col-7 {
  -ms-flex: 0 0 58.33333%;
  flex: 0 0 58.33333%;
  max-width: 58.33333%;
}
.tw-bs4 .col-8 {
  -ms-flex: 0 0 66.66667%;
  flex: 0 0 66.66667%;
  max-width: 66.66667%;
}
.tw-bs4 .col-9 {
  -ms-flex: 0 0 75%;
  flex: 0 0 75%;
  max-width: 75%;
}
.tw-bs4 .col-10 {
  -ms-flex: 0 0 83.33333%;
  flex: 0 0 83.33333%;
  max-width: 83.33333%;
}
.tw-bs4 .col-11 {
  -ms-flex: 0 0 91.66667%;
  flex: 0 0 91.66667%;
  max-width: 91.66667%;
}
.tw-bs4 .col-12 {
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}
.tw-bs4 .order-first {
  -ms-flex-order: -1;
  order: -1;
}
.tw-bs4 .order-last {
  -ms-flex-order: 13;
  order: 13;
}
.tw-bs4 .order-0 {
  -ms-flex-order: 0;
  order: 0;
}
.tw-bs4 .order-1 {
  -ms-flex-order: 1;
  order: 1;
}
.tw-bs4 .order-2 {
  -ms-flex-order: 2;
  order: 2;
}
.tw-bs4 .order-3 {
  -ms-flex-order: 3;
  order: 3;
}
.tw-bs4 .order-4 {
  -ms-flex-order: 4;
  order: 4;
}
.tw-bs4 .order-5 {
  -ms-flex-order: 5;
  order: 5;
}
.tw-bs4 .order-6 {
  -ms-flex-order: 6;
  order: 6;
}
.tw-bs4 .order-7 {
  -ms-flex-order: 7;
  order: 7;
}
.tw-bs4 .order-8 {
  -ms-flex-order: 8;
  order: 8;
}
.tw-bs4 .order-9 {
  -ms-flex-order: 9;
  order: 9;
}
.tw-bs4 .order-10 {
  -ms-flex-order: 10;
  order: 10;
}
.tw-bs4 .order-11 {
  -ms-flex-order: 11;
  order: 11;
}
.tw-bs4 .order-12 {
  -ms-flex-order: 12;
  order: 12;
}
.tw-bs4 .offset-1 {
  margin-left: 8.33333%;
}
.tw-bs4 .offset-2 {
  margin-left: 16.66667%;
}
.tw-bs4 .offset-3 {
  margin-left: 25%;
}
.tw-bs4 .offset-4 {
  margin-left: 33.33333%;
}
.tw-bs4 .offset-5 {
  margin-left: 41.66667%;
}
.tw-bs4 .offset-6 {
  margin-left: 50%;
}
.tw-bs4 .offset-7 {
  margin-left: 58.33333%;
}
.tw-bs4 .offset-8 {
  margin-left: 66.66667%;
}
.tw-bs4 .offset-9 {
  margin-left: 75%;
}
.tw-bs4 .offset-10 {
  margin-left: 83.33333%;
}
.tw-bs4 .offset-11 {
  margin-left: 91.66667%;
}
@media (min-width: 576px) {
  .tw-bs4 .col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .tw-bs4 .col-sm-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .tw-bs4 .col-sm-1 {
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .tw-bs4 .col-sm-2 {
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .tw-bs4 .col-sm-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .tw-bs4 .col-sm-4 {
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .tw-bs4 .col-sm-5 {
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .tw-bs4 .col-sm-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .tw-bs4 .col-sm-7 {
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .tw-bs4 .col-sm-8 {
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .tw-bs4 .col-sm-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .tw-bs4 .col-sm-10 {
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .tw-bs4 .col-sm-11 {
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .tw-bs4 .col-sm-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .tw-bs4 .order-sm-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .tw-bs4 .order-sm-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .tw-bs4 .order-sm-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .tw-bs4 .order-sm-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .tw-bs4 .order-sm-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .tw-bs4 .order-sm-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .tw-bs4 .order-sm-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .tw-bs4 .order-sm-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .tw-bs4 .order-sm-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .tw-bs4 .order-sm-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .tw-bs4 .order-sm-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .tw-bs4 .order-sm-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .tw-bs4 .order-sm-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .tw-bs4 .order-sm-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .tw-bs4 .order-sm-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .tw-bs4 .offset-sm-0 {
    margin-left: 0;
  }
  .tw-bs4 .offset-sm-1 {
    margin-left: 8.33333%;
  }
  .tw-bs4 .offset-sm-2 {
    margin-left: 16.66667%;
  }
  .tw-bs4 .offset-sm-3 {
    margin-left: 25%;
  }
  .tw-bs4 .offset-sm-4 {
    margin-left: 33.33333%;
  }
  .tw-bs4 .offset-sm-5 {
    margin-left: 41.66667%;
  }
  .tw-bs4 .offset-sm-6 {
    margin-left: 50%;
  }
  .tw-bs4 .offset-sm-7 {
    margin-left: 58.33333%;
  }
  .tw-bs4 .offset-sm-8 {
    margin-left: 66.66667%;
  }
  .tw-bs4 .offset-sm-9 {
    margin-left: 75%;
  }
  .tw-bs4 .offset-sm-10 {
    margin-left: 83.33333%;
  }
  .tw-bs4 .offset-sm-11 {
    margin-left: 91.66667%;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .col-md {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .tw-bs4 .col-md-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .tw-bs4 .col-md-1 {
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .tw-bs4 .col-md-2 {
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .tw-bs4 .col-md-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .tw-bs4 .col-md-4 {
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .tw-bs4 .col-md-5 {
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .tw-bs4 .col-md-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .tw-bs4 .col-md-7 {
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .tw-bs4 .col-md-8 {
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .tw-bs4 .col-md-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .tw-bs4 .col-md-10 {
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .tw-bs4 .col-md-11 {
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .tw-bs4 .col-md-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .tw-bs4 .order-md-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .tw-bs4 .order-md-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .tw-bs4 .order-md-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .tw-bs4 .order-md-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .tw-bs4 .order-md-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .tw-bs4 .order-md-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .tw-bs4 .order-md-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .tw-bs4 .order-md-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .tw-bs4 .order-md-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .tw-bs4 .order-md-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .tw-bs4 .order-md-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .tw-bs4 .order-md-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .tw-bs4 .order-md-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .tw-bs4 .order-md-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .tw-bs4 .order-md-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .tw-bs4 .offset-md-0 {
    margin-left: 0;
  }
  .tw-bs4 .offset-md-1 {
    margin-left: 8.33333%;
  }
  .tw-bs4 .offset-md-2 {
    margin-left: 16.66667%;
  }
  .tw-bs4 .offset-md-3 {
    margin-left: 25%;
  }
  .tw-bs4 .offset-md-4 {
    margin-left: 33.33333%;
  }
  .tw-bs4 .offset-md-5 {
    margin-left: 41.66667%;
  }
  .tw-bs4 .offset-md-6 {
    margin-left: 50%;
  }
  .tw-bs4 .offset-md-7 {
    margin-left: 58.33333%;
  }
  .tw-bs4 .offset-md-8 {
    margin-left: 66.66667%;
  }
  .tw-bs4 .offset-md-9 {
    margin-left: 75%;
  }
  .tw-bs4 .offset-md-10 {
    margin-left: 83.33333%;
  }
  .tw-bs4 .offset-md-11 {
    margin-left: 91.66667%;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .col-lg {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .tw-bs4 .col-lg-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .tw-bs4 .col-lg-1 {
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .tw-bs4 .col-lg-2 {
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .tw-bs4 .col-lg-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .tw-bs4 .col-lg-4 {
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .tw-bs4 .col-lg-5 {
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .tw-bs4 .col-lg-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .tw-bs4 .col-lg-7 {
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .tw-bs4 .col-lg-8 {
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .tw-bs4 .col-lg-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .tw-bs4 .col-lg-10 {
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .tw-bs4 .col-lg-11 {
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .tw-bs4 .col-lg-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .tw-bs4 .order-lg-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .tw-bs4 .order-lg-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .tw-bs4 .order-lg-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .tw-bs4 .order-lg-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .tw-bs4 .order-lg-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .tw-bs4 .order-lg-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .tw-bs4 .order-lg-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .tw-bs4 .order-lg-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .tw-bs4 .order-lg-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .tw-bs4 .order-lg-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .tw-bs4 .order-lg-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .tw-bs4 .order-lg-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .tw-bs4 .order-lg-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .tw-bs4 .order-lg-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .tw-bs4 .order-lg-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .tw-bs4 .offset-lg-0 {
    margin-left: 0;
  }
  .tw-bs4 .offset-lg-1 {
    margin-left: 8.33333%;
  }
  .tw-bs4 .offset-lg-2 {
    margin-left: 16.66667%;
  }
  .tw-bs4 .offset-lg-3 {
    margin-left: 25%;
  }
  .tw-bs4 .offset-lg-4 {
    margin-left: 33.33333%;
  }
  .tw-bs4 .offset-lg-5 {
    margin-left: 41.66667%;
  }
  .tw-bs4 .offset-lg-6 {
    margin-left: 50%;
  }
  .tw-bs4 .offset-lg-7 {
    margin-left: 58.33333%;
  }
  .tw-bs4 .offset-lg-8 {
    margin-left: 66.66667%;
  }
  .tw-bs4 .offset-lg-9 {
    margin-left: 75%;
  }
  .tw-bs4 .offset-lg-10 {
    margin-left: 83.33333%;
  }
  .tw-bs4 .offset-lg-11 {
    margin-left: 91.66667%;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .col-xl {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .tw-bs4 .col-xl-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .tw-bs4 .col-xl-1 {
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .tw-bs4 .col-xl-2 {
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .tw-bs4 .col-xl-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .tw-bs4 .col-xl-4 {
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .tw-bs4 .col-xl-5 {
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .tw-bs4 .col-xl-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .tw-bs4 .col-xl-7 {
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .tw-bs4 .col-xl-8 {
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .tw-bs4 .col-xl-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .tw-bs4 .col-xl-10 {
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .tw-bs4 .col-xl-11 {
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .tw-bs4 .col-xl-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .tw-bs4 .order-xl-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .tw-bs4 .order-xl-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .tw-bs4 .order-xl-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .tw-bs4 .order-xl-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .tw-bs4 .order-xl-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .tw-bs4 .order-xl-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .tw-bs4 .order-xl-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .tw-bs4 .order-xl-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .tw-bs4 .order-xl-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .tw-bs4 .order-xl-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .tw-bs4 .order-xl-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .tw-bs4 .order-xl-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .tw-bs4 .order-xl-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .tw-bs4 .order-xl-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .tw-bs4 .order-xl-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .tw-bs4 .offset-xl-0 {
    margin-left: 0;
  }
  .tw-bs4 .offset-xl-1 {
    margin-left: 8.33333%;
  }
  .tw-bs4 .offset-xl-2 {
    margin-left: 16.66667%;
  }
  .tw-bs4 .offset-xl-3 {
    margin-left: 25%;
  }
  .tw-bs4 .offset-xl-4 {
    margin-left: 33.33333%;
  }
  .tw-bs4 .offset-xl-5 {
    margin-left: 41.66667%;
  }
  .tw-bs4 .offset-xl-6 {
    margin-left: 50%;
  }
  .tw-bs4 .offset-xl-7 {
    margin-left: 58.33333%;
  }
  .tw-bs4 .offset-xl-8 {
    margin-left: 66.66667%;
  }
  .tw-bs4 .offset-xl-9 {
    margin-left: 75%;
  }
  .tw-bs4 .offset-xl-10 {
    margin-left: 83.33333%;
  }
  .tw-bs4 .offset-xl-11 {
    margin-left: 91.66667%;
  }
}
.tw-bs4 .table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
}
.tw-bs4 .table th, .tw-bs4 .table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
}
.tw-bs4 .table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
}
.tw-bs4 .table tbody + tbody {
  border-top: 2px solid #dee2e6;
}
.tw-bs4 .table-sm th, .tw-bs4 .table-sm td {
  padding: 0.3rem;
}
.tw-bs4 .table-bordered {
  border: 1px solid #dee2e6;
}
.tw-bs4 .table-bordered th, .tw-bs4 .table-bordered td {
  border: 1px solid #dee2e6;
}
.tw-bs4 .table-bordered thead th, .tw-bs4 .table-bordered thead td {
  border-bottom-width: 2px;
}
.tw-bs4 .table-borderless th, .tw-bs4 .table-borderless td, .tw-bs4 .table-borderless thead th, .tw-bs4 .table-borderless tbody + tbody {
  border: 0;
}
.tw-bs4 .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}
.tw-bs4 .table-hover tbody tr:hover {
  color: #212529;
  background-color: rgba(0, 0, 0, 0.075);
}
.tw-bs4 .table-primary, .tw-bs4 .table-primary > th, .tw-bs4 .table-primary > td {
  background-color: #b8daff;
}
.tw-bs4 .table-primary th, .tw-bs4 .table-primary td, .tw-bs4 .table-primary thead th, .tw-bs4 .table-primary tbody + tbody {
  border-color: #7abaff;
}
.tw-bs4 .table-hover .table-primary:hover {
  background-color: #9fcdff;
}
.tw-bs4 .table-hover .table-primary:hover > td, .tw-bs4 .table-hover .table-primary:hover > th {
  background-color: #9fcdff;
}
.tw-bs4 .table-secondary, .tw-bs4 .table-secondary > th, .tw-bs4 .table-secondary > td {
  background-color: #d6d8db;
}
.tw-bs4 .table-secondary th, .tw-bs4 .table-secondary td, .tw-bs4 .table-secondary thead th, .tw-bs4 .table-secondary tbody + tbody {
  border-color: #b3b7bb;
}
.tw-bs4 .table-hover .table-secondary:hover {
  background-color: #c8cbcf;
}
.tw-bs4 .table-hover .table-secondary:hover > td, .tw-bs4 .table-hover .table-secondary:hover > th {
  background-color: #c8cbcf;
}
.tw-bs4 .table-success, .tw-bs4 .table-success > th, .tw-bs4 .table-success > td {
  background-color: #c3e6cb;
}
.tw-bs4 .table-success th, .tw-bs4 .table-success td, .tw-bs4 .table-success thead th, .tw-bs4 .table-success tbody + tbody {
  border-color: #8fd19e;
}
.tw-bs4 .table-hover .table-success:hover {
  background-color: #b1dfbb;
}
.tw-bs4 .table-hover .table-success:hover > td, .tw-bs4 .table-hover .table-success:hover > th {
  background-color: #b1dfbb;
}
.tw-bs4 .table-info, .tw-bs4 .table-info > th, .tw-bs4 .table-info > td {
  background-color: #bee5eb;
}
.tw-bs4 .table-info th, .tw-bs4 .table-info td, .tw-bs4 .table-info thead th, .tw-bs4 .table-info tbody + tbody {
  border-color: #86cfda;
}
.tw-bs4 .table-hover .table-info:hover {
  background-color: #abdde5;
}
.tw-bs4 .table-hover .table-info:hover > td, .tw-bs4 .table-hover .table-info:hover > th {
  background-color: #abdde5;
}
.tw-bs4 .table-warning, .tw-bs4 .table-warning > th, .tw-bs4 .table-warning > td {
  background-color: #ffeeba;
}
.tw-bs4 .table-warning th, .tw-bs4 .table-warning td, .tw-bs4 .table-warning thead th, .tw-bs4 .table-warning tbody + tbody {
  border-color: #ffdf7e;
}
.tw-bs4 .table-hover .table-warning:hover {
  background-color: #ffe8a1;
}
.tw-bs4 .table-hover .table-warning:hover > td, .tw-bs4 .table-hover .table-warning:hover > th {
  background-color: #ffe8a1;
}
.tw-bs4 .table-danger, .tw-bs4 .table-danger > th, .tw-bs4 .table-danger > td {
  background-color: #f5c6cb;
}
.tw-bs4 .table-danger th, .tw-bs4 .table-danger td, .tw-bs4 .table-danger thead th, .tw-bs4 .table-danger tbody + tbody {
  border-color: #ed969e;
}
.tw-bs4 .table-hover .table-danger:hover {
  background-color: #f1b0b7;
}
.tw-bs4 .table-hover .table-danger:hover > td, .tw-bs4 .table-hover .table-danger:hover > th {
  background-color: #f1b0b7;
}
.tw-bs4 .table-light, .tw-bs4 .table-light > th, .tw-bs4 .table-light > td {
  background-color: #fdfdfe;
}
.tw-bs4 .table-light th, .tw-bs4 .table-light td, .tw-bs4 .table-light thead th, .tw-bs4 .table-light tbody + tbody {
  border-color: #fbfcfc;
}
.tw-bs4 .table-hover .table-light:hover {
  background-color: #ececf6;
}
.tw-bs4 .table-hover .table-light:hover > td, .tw-bs4 .table-hover .table-light:hover > th {
  background-color: #ececf6;
}
.tw-bs4 .table-dark, .tw-bs4 .table-dark > th, .tw-bs4 .table-dark > td {
  background-color: #c6c8ca;
}
.tw-bs4 .table-dark th, .tw-bs4 .table-dark td, .tw-bs4 .table-dark thead th, .tw-bs4 .table-dark tbody + tbody {
  border-color: #95999c;
}
.tw-bs4 .table-hover .table-dark:hover {
  background-color: #b9bbbe;
}
.tw-bs4 .table-hover .table-dark:hover > td, .tw-bs4 .table-hover .table-dark:hover > th {
  background-color: #b9bbbe;
}
.tw-bs4 .table-active, .tw-bs4 .table-active > th, .tw-bs4 .table-active > td {
  background-color: rgba(0, 0, 0, 0.075);
}
.tw-bs4 .table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}
.tw-bs4 .table-hover .table-active:hover > td, .tw-bs4 .table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}
.tw-bs4 .table .thead-dark th {
  color: #fff;
  background-color: #343a40;
  border-color: #454d55;
}
.tw-bs4 .table .thead-light th {
  color: #495057;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
.tw-bs4 .table-dark {
  color: #fff;
  background-color: #343a40;
}
.tw-bs4 .table-dark th, .tw-bs4 .table-dark td, .tw-bs4 .table-dark thead th {
  border-color: #454d55;
}
.tw-bs4 .table-dark.table-bordered {
  border: 0;
}
.tw-bs4 .table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}
.tw-bs4 .table-dark.table-hover tbody tr:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.075);
}
@media (max-width: 575.98px) {
  .tw-bs4 .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .tw-bs4 .table-responsive-sm > .table-bordered {
    border: 0;
  }
}
@media (max-width: 767.98px) {
  .tw-bs4 .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .tw-bs4 .table-responsive-md > .table-bordered {
    border: 0;
  }
}
@media (max-width: 991.98px) {
  .tw-bs4 .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .tw-bs4 .table-responsive-lg > .table-bordered {
    border: 0;
  }
}
@media (max-width: 1199.98px) {
  .tw-bs4 .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .tw-bs4 .table-responsive-xl > .table-bordered {
    border: 0;
  }
}
.tw-bs4 .table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.tw-bs4 .table-responsive > .table-bordered {
  border: 0;
}
.tw-bs4 .form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .form-control {
    transition: none;
  }
}
.tw-bs4 .form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}
.tw-bs4 .form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .form-control::-webkit-input-placeholder {
  color: #6c757d;
  opacity: 1;
}
.tw-bs4 .form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}
.tw-bs4 .form-control:-ms-input-placeholder {
  color: #6c757d;
  opacity: 1;
}
.tw-bs4 .form-control::-ms-input-placeholder {
  color: #6c757d;
  opacity: 1;
}
.tw-bs4 .form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}
.tw-bs4 .form-control:disabled, .tw-bs4 .form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}
.tw-bs4 select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}
.tw-bs4 .form-control-file, .tw-bs4 .form-control-range {
  display: block;
  width: 100%;
}
.tw-bs4 .col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}
.tw-bs4 .col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5;
}
.tw-bs4 .col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.5;
}
.tw-bs4 .form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.tw-bs4 .form-control-plaintext.form-control-sm, .tw-bs4 .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}
.tw-bs4 .form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.tw-bs4 .form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.tw-bs4 select.form-control[size], .tw-bs4 select.form-control[multiple] {
  height: auto;
}
.tw-bs4 textarea.form-control {
  height: auto;
}
.tw-bs4 .form-group {
  margin-bottom: 1rem;
}
.tw-bs4 .form-text {
  display: block;
  margin-top: 0.25rem;
}
.tw-bs4 .form-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}
.tw-bs4 .form-row > .col, .tw-bs4 .form-row > [class*="col-"] {
  padding-right: 5px;
  padding-left: 5px;
}
.tw-bs4 .form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}
.tw-bs4 .form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}
.tw-bs4 .form-check-input:disabled ~ .form-check-label {
  color: #6c757d;
}
.tw-bs4 .form-check-label {
  margin-bottom: 0;
}
.tw-bs4 .form-check-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}
.tw-bs4 .form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}
.tw-bs4 .valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #28a745;
}
.tw-bs4 .valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(40, 167, 69, 0.9);
  border-radius: 0.25rem;
}
.tw-bs4 .was-validated .form-control:valid, .tw-bs4 .form-control.is-valid {
  border-color: #28a745;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.tw-bs4 .was-validated .form-control:valid:focus, .tw-bs4 .form-control.is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
.tw-bs4 .was-validated .form-control:valid ~ .valid-feedback, .tw-bs4 .was-validated .form-control:valid ~ .valid-tooltip, .tw-bs4 .form-control.is-valid ~ .valid-feedback, .tw-bs4 .form-control.is-valid ~ .valid-tooltip {
  display: block;
}
.tw-bs4 .was-validated textarea.form-control:valid, .tw-bs4 textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.tw-bs4 .was-validated .custom-select:valid, .tw-bs4 .custom-select.is-valid {
  border-color: #28a745;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.tw-bs4 .was-validated .custom-select:valid:focus, .tw-bs4 .custom-select.is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
.tw-bs4 .was-validated .custom-select:valid ~ .valid-feedback, .tw-bs4 .was-validated .custom-select:valid ~ .valid-tooltip, .tw-bs4 .custom-select.is-valid ~ .valid-feedback, .tw-bs4 .custom-select.is-valid ~ .valid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .form-control-file:valid ~ .valid-feedback, .tw-bs4 .was-validated .form-control-file:valid ~ .valid-tooltip, .tw-bs4 .form-control-file.is-valid ~ .valid-feedback, .tw-bs4 .form-control-file.is-valid ~ .valid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .form-check-input:valid ~ .form-check-label, .tw-bs4 .form-check-input.is-valid ~ .form-check-label {
  color: #28a745;
}
.tw-bs4 .was-validated .form-check-input:valid ~ .valid-feedback, .tw-bs4 .was-validated .form-check-input:valid ~ .valid-tooltip, .tw-bs4 .form-check-input.is-valid ~ .valid-feedback, .tw-bs4 .form-check-input.is-valid ~ .valid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .custom-control-input:valid ~ .custom-control-label, .tw-bs4 .custom-control-input.is-valid ~ .custom-control-label {
  color: #28a745;
}
.tw-bs4 .was-validated .custom-control-input:valid ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-valid ~ .custom-control-label::before {
  border-color: #28a745;
}
.tw-bs4 .was-validated .custom-control-input:valid ~ .valid-feedback, .tw-bs4 .was-validated .custom-control-input:valid ~ .valid-tooltip, .tw-bs4 .custom-control-input.is-valid ~ .valid-feedback, .tw-bs4 .custom-control-input.is-valid ~ .valid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-valid:checked ~ .custom-control-label::before {
  border-color: #34ce57;
  background-color: #34ce57;
}
.tw-bs4 .was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-valid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
.tw-bs4 .was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #28a745;
}
.tw-bs4 .was-validated .custom-file-input:valid ~ .custom-file-label, .tw-bs4 .custom-file-input.is-valid ~ .custom-file-label {
  border-color: #28a745;
}
.tw-bs4 .was-validated .custom-file-input:valid ~ .valid-feedback, .tw-bs4 .was-validated .custom-file-input:valid ~ .valid-tooltip, .tw-bs4 .custom-file-input.is-valid ~ .valid-feedback, .tw-bs4 .custom-file-input.is-valid ~ .valid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .custom-file-input:valid:focus ~ .custom-file-label, .tw-bs4 .custom-file-input.is-valid:focus ~ .custom-file-label {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
.tw-bs4 .invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}
.tw-bs4 .invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(220, 53, 69, 0.9);
  border-radius: 0.25rem;
}
.tw-bs4 .was-validated .form-control:invalid, .tw-bs4 .form-control.is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.tw-bs4 .was-validated .form-control:invalid:focus, .tw-bs4 .form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
.tw-bs4 .was-validated .form-control:invalid ~ .invalid-feedback, .tw-bs4 .was-validated .form-control:invalid ~ .invalid-tooltip, .tw-bs4 .form-control.is-invalid ~ .invalid-feedback, .tw-bs4 .form-control.is-invalid ~ .invalid-tooltip {
  display: block;
}
.tw-bs4 .was-validated textarea.form-control:invalid, .tw-bs4 textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.tw-bs4 .was-validated .custom-select:invalid, .tw-bs4 .custom-select.is-invalid {
  border-color: #dc3545;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.tw-bs4 .was-validated .custom-select:invalid:focus, .tw-bs4 .custom-select.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
.tw-bs4 .was-validated .custom-select:invalid ~ .invalid-feedback, .tw-bs4 .was-validated .custom-select:invalid ~ .invalid-tooltip, .tw-bs4 .custom-select.is-invalid ~ .invalid-feedback, .tw-bs4 .custom-select.is-invalid ~ .invalid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .form-control-file:invalid ~ .invalid-feedback, .tw-bs4 .was-validated .form-control-file:invalid ~ .invalid-tooltip, .tw-bs4 .form-control-file.is-invalid ~ .invalid-feedback, .tw-bs4 .form-control-file.is-invalid ~ .invalid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .form-check-input:invalid ~ .form-check-label, .tw-bs4 .form-check-input.is-invalid ~ .form-check-label {
  color: #dc3545;
}
.tw-bs4 .was-validated .form-check-input:invalid ~ .invalid-feedback, .tw-bs4 .was-validated .form-check-input:invalid ~ .invalid-tooltip, .tw-bs4 .form-check-input.is-invalid ~ .invalid-feedback, .tw-bs4 .form-check-input.is-invalid ~ .invalid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .custom-control-input:invalid ~ .custom-control-label, .tw-bs4 .custom-control-input.is-invalid ~ .custom-control-label {
  color: #dc3545;
}
.tw-bs4 .was-validated .custom-control-input:invalid ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-invalid ~ .custom-control-label::before {
  border-color: #dc3545;
}
.tw-bs4 .was-validated .custom-control-input:invalid ~ .invalid-feedback, .tw-bs4 .was-validated .custom-control-input:invalid ~ .invalid-tooltip, .tw-bs4 .custom-control-input.is-invalid ~ .invalid-feedback, .tw-bs4 .custom-control-input.is-invalid ~ .invalid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-invalid:checked ~ .custom-control-label::before {
  border-color: #e4606d;
  background-color: #e4606d;
}
.tw-bs4 .was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-invalid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
.tw-bs4 .was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .tw-bs4 .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #dc3545;
}
.tw-bs4 .was-validated .custom-file-input:invalid ~ .custom-file-label, .tw-bs4 .custom-file-input.is-invalid ~ .custom-file-label {
  border-color: #dc3545;
}
.tw-bs4 .was-validated .custom-file-input:invalid ~ .invalid-feedback, .tw-bs4 .was-validated .custom-file-input:invalid ~ .invalid-tooltip, .tw-bs4 .custom-file-input.is-invalid ~ .invalid-feedback, .tw-bs4 .custom-file-input.is-invalid ~ .invalid-tooltip {
  display: block;
}
.tw-bs4 .was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .tw-bs4 .custom-file-input.is-invalid:focus ~ .custom-file-label {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
.tw-bs4 .form-inline {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -ms-flex-align: center;
  align-items: center;
}
.tw-bs4 .form-inline .form-check {
  width: 100%;
}
@media (min-width: 576px) {
  .tw-bs4 .form-inline label {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-bottom: 0;
  }
  .tw-bs4 .form-inline .form-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 0;
  }
  .tw-bs4 .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .tw-bs4 .form-inline .form-control-plaintext {
    display: inline-block;
  }
  .tw-bs4 .form-inline .input-group, .tw-bs4 .form-inline .custom-select {
    width: auto;
  }
  .tw-bs4 .form-inline .form-check {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }
  .tw-bs4 .form-inline .form-check-input {
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .tw-bs4 .form-inline .custom-control {
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .tw-bs4 .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}
.tw-bs4 .btn {
  display: inline-block;
  font-weight: 400;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .btn {
    transition: none;
  }
}
.tw-bs4 .btn:hover {
  color: #212529;
  text-decoration: none;
}
.tw-bs4 .btn:focus, .tw-bs4 .btn.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .btn.disabled, .tw-bs4 .btn:disabled {
  opacity: 0.65;
}
.tw-bs4 a.btn.disabled, .tw-bs4 fieldset:disabled a.btn {
  pointer-events: none;
}
.tw-bs4 .btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}
.tw-bs4 .btn-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}
.tw-bs4 .btn-primary:focus, .tw-bs4 .btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}
.tw-bs4 .btn-primary.disabled, .tw-bs4 .btn-primary:disabled {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}
.tw-bs4 .btn-primary:not(:disabled):not(.disabled):active, .tw-bs4 .btn-primary:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #0062cc;
  border-color: #005cbf;
}
.tw-bs4 .btn-primary:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-primary:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}
.tw-bs4 .btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.tw-bs4 .btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}
.tw-bs4 .btn-secondary:focus, .tw-bs4 .btn-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}
.tw-bs4 .btn-secondary.disabled, .tw-bs4 .btn-secondary:disabled {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.tw-bs4 .btn-secondary:not(:disabled):not(.disabled):active, .tw-bs4 .btn-secondary:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #545b62;
  border-color: #4e555b;
}
.tw-bs4 .btn-secondary:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-secondary:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}
.tw-bs4 .btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.tw-bs4 .btn-success:hover {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
}
.tw-bs4 .btn-success:focus, .tw-bs4 .btn-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}
.tw-bs4 .btn-success.disabled, .tw-bs4 .btn-success:disabled {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.tw-bs4 .btn-success:not(:disabled):not(.disabled):active, .tw-bs4 .btn-success:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #1e7e34;
  border-color: #1c7430;
}
.tw-bs4 .btn-success:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-success:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}
.tw-bs4 .btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.tw-bs4 .btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}
.tw-bs4 .btn-info:focus, .tw-bs4 .btn-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}
.tw-bs4 .btn-info.disabled, .tw-bs4 .btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.tw-bs4 .btn-info:not(:disabled):not(.disabled):active, .tw-bs4 .btn-info:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}
.tw-bs4 .btn-info:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-info:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}
.tw-bs4 .btn-warning {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}
.tw-bs4 .btn-warning:hover {
  color: #212529;
  background-color: #e0a800;
  border-color: #d39e00;
}
.tw-bs4 .btn-warning:focus, .tw-bs4 .btn-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);
}
.tw-bs4 .btn-warning.disabled, .tw-bs4 .btn-warning:disabled {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}
.tw-bs4 .btn-warning:not(:disabled):not(.disabled):active, .tw-bs4 .btn-warning:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-warning.dropdown-toggle {
  color: #212529;
  background-color: #d39e00;
  border-color: #c69500;
}
.tw-bs4 .btn-warning:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-warning:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);
}
.tw-bs4 .btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.tw-bs4 .btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}
.tw-bs4 .btn-danger:focus, .tw-bs4 .btn-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
}
.tw-bs4 .btn-danger.disabled, .tw-bs4 .btn-danger:disabled {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.tw-bs4 .btn-danger:not(:disabled):not(.disabled):active, .tw-bs4 .btn-danger:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #bd2130;
  border-color: #b21f2d;
}
.tw-bs4 .btn-danger:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-danger:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
}
.tw-bs4 .btn-light {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.tw-bs4 .btn-light:hover {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}
.tw-bs4 .btn-light:focus, .tw-bs4 .btn-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}
.tw-bs4 .btn-light.disabled, .tw-bs4 .btn-light:disabled {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.tw-bs4 .btn-light:not(:disabled):not(.disabled):active, .tw-bs4 .btn-light:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}
.tw-bs4 .btn-light:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-light:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}
.tw-bs4 .btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.tw-bs4 .btn-dark:hover {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
}
.tw-bs4 .btn-dark:focus, .tw-bs4 .btn-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}
.tw-bs4 .btn-dark.disabled, .tw-bs4 .btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.tw-bs4 .btn-dark:not(:disabled):not(.disabled):active, .tw-bs4 .btn-dark:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}
.tw-bs4 .btn-dark:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-dark:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}
.tw-bs4 .btn-outline-primary {
  color: #007bff;
  border-color: #007bff;
}
.tw-bs4 .btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}
.tw-bs4 .btn-outline-primary:focus, .tw-bs4 .btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}
.tw-bs4 .btn-outline-primary.disabled, .tw-bs4 .btn-outline-primary:disabled {
  color: #007bff;
  background-color: transparent;
}
.tw-bs4 .btn-outline-primary:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-primary:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}
.tw-bs4 .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}
.tw-bs4 .btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}
.tw-bs4 .btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.tw-bs4 .btn-outline-secondary:focus, .tw-bs4 .btn-outline-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}
.tw-bs4 .btn-outline-secondary.disabled, .tw-bs4 .btn-outline-secondary:disabled {
  color: #6c757d;
  background-color: transparent;
}
.tw-bs4 .btn-outline-secondary:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-secondary:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.tw-bs4 .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}
.tw-bs4 .btn-outline-success {
  color: #28a745;
  border-color: #28a745;
}
.tw-bs4 .btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.tw-bs4 .btn-outline-success:focus, .tw-bs4 .btn-outline-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}
.tw-bs4 .btn-outline-success.disabled, .tw-bs4 .btn-outline-success:disabled {
  color: #28a745;
  background-color: transparent;
}
.tw-bs4 .btn-outline-success:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-success:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.tw-bs4 .btn-outline-success:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-success:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}
.tw-bs4 .btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}
.tw-bs4 .btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.tw-bs4 .btn-outline-info:focus, .tw-bs4 .btn-outline-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}
.tw-bs4 .btn-outline-info.disabled, .tw-bs4 .btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}
.tw-bs4 .btn-outline-info:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-info:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.tw-bs4 .btn-outline-info:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-info:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}
.tw-bs4 .btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}
.tw-bs4 .btn-outline-warning:hover {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}
.tw-bs4 .btn-outline-warning:focus, .tw-bs4 .btn-outline-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.tw-bs4 .btn-outline-warning.disabled, .tw-bs4 .btn-outline-warning:disabled {
  color: #ffc107;
  background-color: transparent;
}
.tw-bs4 .btn-outline-warning:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-warning:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}
.tw-bs4 .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.tw-bs4 .btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}
.tw-bs4 .btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.tw-bs4 .btn-outline-danger:focus, .tw-bs4 .btn-outline-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}
.tw-bs4 .btn-outline-danger.disabled, .tw-bs4 .btn-outline-danger:disabled {
  color: #dc3545;
  background-color: transparent;
}
.tw-bs4 .btn-outline-danger:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-danger:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.tw-bs4 .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}
.tw-bs4 .btn-outline-light {
  color: #f8f9fa;
  border-color: #f8f9fa;
}
.tw-bs4 .btn-outline-light:hover {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.tw-bs4 .btn-outline-light:focus, .tw-bs4 .btn-outline-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}
.tw-bs4 .btn-outline-light.disabled, .tw-bs4 .btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}
.tw-bs4 .btn-outline-light:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-light:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}
.tw-bs4 .btn-outline-light:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-light:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}
.tw-bs4 .btn-outline-dark {
  color: #343a40;
  border-color: #343a40;
}
.tw-bs4 .btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.tw-bs4 .btn-outline-dark:focus, .tw-bs4 .btn-outline-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}
.tw-bs4 .btn-outline-dark.disabled, .tw-bs4 .btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}
.tw-bs4 .btn-outline-dark:not(:disabled):not(.disabled):active, .tw-bs4 .btn-outline-dark:not(:disabled):not(.disabled).active, .tw-bs4 .show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.tw-bs4 .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .tw-bs4 .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .tw-bs4 .show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}
.tw-bs4 .btn-link {
  font-weight: 400;
  color: #007bff;
  text-decoration: none;
}
.tw-bs4 .btn-link:hover {
  color: #0056b3;
  text-decoration: underline;
}
.tw-bs4 .btn-link:focus, .tw-bs4 .btn-link.focus {
  text-decoration: underline;
  box-shadow: none;
}
.tw-bs4 .btn-link:disabled, .tw-bs4 .btn-link.disabled {
  color: #6c757d;
  pointer-events: none;
}
.tw-bs4 .btn-lg, .tw-bs4 .btn-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.tw-bs4 .btn-sm, .tw-bs4 .btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.tw-bs4 .btn-block {
  display: block;
  width: 100%;
}
.tw-bs4 .btn-block + .btn-block {
  margin-top: 0.5rem;
}
.tw-bs4 input[type="submit"].btn-block, .tw-bs4 input[type="reset"].btn-block, .tw-bs4 input[type="button"].btn-block {
  width: 100%;
}
.tw-bs4 .fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .fade {
    transition: none;
  }
}
.tw-bs4 .fade:not(.show) {
  opacity: 0;
}
.tw-bs4 .collapse:not(.show) {
  display: none;
}
.tw-bs4 .collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .collapsing {
    transition: none;
  }
}
.tw-bs4 .dropup, .tw-bs4 .dropright, .tw-bs4 .dropdown, .tw-bs4 .dropleft {
  position: relative;
}
.tw-bs4 .dropdown-toggle {
  white-space: nowrap;
}
.tw-bs4 .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.tw-bs4 .dropdown-toggle:empty::after {
  margin-left: 0;
}
.tw-bs4 .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}
.tw-bs4 .dropdown-menu-left {
  right: auto;
  left: 0;
}
.tw-bs4 .dropdown-menu-right {
  right: 0;
  left: auto;
}
@media (min-width: 576px) {
  .tw-bs4 .dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }
  .tw-bs4 .dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .dropdown-menu-md-left {
    right: auto;
    left: 0;
  }
  .tw-bs4 .dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }
  .tw-bs4 .dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }
  .tw-bs4 .dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
}
.tw-bs4 .dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.tw-bs4 .dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.tw-bs4 .dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}
.tw-bs4 .dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.tw-bs4 .dropright .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.tw-bs4 .dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}
.tw-bs4 .dropright .dropdown-toggle::after {
  vertical-align: 0;
}
.tw-bs4 .dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.tw-bs4 .dropleft .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.tw-bs4 .dropleft .dropdown-toggle::after {
  display: none;
}
.tw-bs4 .dropleft .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.tw-bs4 .dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}
.tw-bs4 .dropleft .dropdown-toggle::before {
  vertical-align: 0;
}
.tw-bs4 .dropdown-menu[x-placement^="top"], .tw-bs4 .dropdown-menu[x-placement^="right"], .tw-bs4 .dropdown-menu[x-placement^="bottom"], .tw-bs4 .dropdown-menu[x-placement^="left"] {
  right: auto;
  bottom: auto;
}
.tw-bs4 .dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef;
}
.tw-bs4 .dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.tw-bs4 .dropdown-item:hover, .tw-bs4 .dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}
.tw-bs4 .dropdown-item.active, .tw-bs4 .dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #007bff;
}
.tw-bs4 .dropdown-item.disabled, .tw-bs4 .dropdown-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: transparent;
}
.tw-bs4 .dropdown-menu.show {
  display: block;
}
.tw-bs4 .dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}
.tw-bs4 .dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: #212529;
}
.tw-bs4 .btn-group, .tw-bs4 .btn-group-vertical {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}
.tw-bs4 .btn-group > .btn, .tw-bs4 .btn-group-vertical > .btn {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
.tw-bs4 .btn-group > .btn:hover, .tw-bs4 .btn-group-vertical > .btn:hover {
  z-index: 1;
}
.tw-bs4 .btn-group > .btn:focus, .tw-bs4 .btn-group > .btn:active, .tw-bs4 .btn-group > .btn.active, .tw-bs4 .btn-group-vertical > .btn:focus, .tw-bs4 .btn-group-vertical > .btn:active, .tw-bs4 .btn-group-vertical > .btn.active {
  z-index: 1;
}
.tw-bs4 .btn-toolbar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.tw-bs4 .btn-toolbar .input-group {
  width: auto;
}
.tw-bs4 .btn-group > .btn:not(:first-child), .tw-bs4 .btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.tw-bs4 .btn-group > .btn:not(:last-child):not(.dropdown-toggle), .tw-bs4 .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.tw-bs4 .btn-group > .btn:not(:first-child), .tw-bs4 .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.tw-bs4 .dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
.tw-bs4 .dropdown-toggle-split::after, .tw-bs4 .dropup .dropdown-toggle-split::after, .tw-bs4 .dropright .dropdown-toggle-split::after {
  margin-left: 0;
}
.tw-bs4 .dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}
.tw-bs4 .btn-sm + .dropdown-toggle-split, .tw-bs4 .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}
.tw-bs4 .btn-lg + .dropdown-toggle-split, .tw-bs4 .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}
.tw-bs4 .btn-group-vertical {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: center;
  justify-content: center;
}
.tw-bs4 .btn-group-vertical > .btn, .tw-bs4 .btn-group-vertical > .btn-group {
  width: 100%;
}
.tw-bs4 .btn-group-vertical > .btn:not(:first-child), .tw-bs4 .btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.tw-bs4 .btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), .tw-bs4 .btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.tw-bs4 .btn-group-vertical > .btn:not(:first-child), .tw-bs4 .btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.tw-bs4 .btn-group-toggle > .btn, .tw-bs4 .btn-group-toggle > .btn-group > .btn {
  margin-bottom: 0;
}
.tw-bs4 .btn-group-toggle > .btn input[type="radio"], .tw-bs4 .btn-group-toggle > .btn input[type="checkbox"], .tw-bs4 .btn-group-toggle > .btn-group > .btn input[type="radio"], .tw-bs4 .btn-group-toggle > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.tw-bs4 .input-group {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}
.tw-bs4 .input-group > .form-control, .tw-bs4 .input-group > .form-control-plaintext, .tw-bs4 .input-group > .custom-select, .tw-bs4 .input-group > .custom-file {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}
.tw-bs4 .input-group > .form-control + .form-control, .tw-bs4 .input-group > .form-control + .custom-select, .tw-bs4 .input-group > .form-control + .custom-file, .tw-bs4 .input-group > .form-control-plaintext + .form-control, .tw-bs4 .input-group > .form-control-plaintext + .custom-select, .tw-bs4 .input-group > .form-control-plaintext + .custom-file, .tw-bs4 .input-group > .custom-select + .form-control, .tw-bs4 .input-group > .custom-select + .custom-select, .tw-bs4 .input-group > .custom-select + .custom-file, .tw-bs4 .input-group > .custom-file + .form-control, .tw-bs4 .input-group > .custom-file + .custom-select, .tw-bs4 .input-group > .custom-file + .custom-file {
  margin-left: -1px;
}
.tw-bs4 .input-group > .form-control:focus, .tw-bs4 .input-group > .custom-select:focus, .tw-bs4 .input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {
  z-index: 3;
}
.tw-bs4 .input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}
.tw-bs4 .input-group > .form-control:not(:last-child), .tw-bs4 .input-group > .custom-select:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.tw-bs4 .input-group > .form-control:not(:first-child), .tw-bs4 .input-group > .custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.tw-bs4 .input-group > .custom-file {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}
.tw-bs4 .input-group > .custom-file:not(:last-child) .custom-file-label, .tw-bs4 .input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.tw-bs4 .input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.tw-bs4 .input-group-prepend, .tw-bs4 .input-group-append {
  display: -ms-flexbox;
  display: flex;
}
.tw-bs4 .input-group-prepend .btn, .tw-bs4 .input-group-append .btn {
  position: relative;
  z-index: 2;
}
.tw-bs4 .input-group-prepend .btn:focus, .tw-bs4 .input-group-append .btn:focus {
  z-index: 3;
}
.tw-bs4 .input-group-prepend .btn + .btn, .tw-bs4 .input-group-prepend .btn + .input-group-text, .tw-bs4 .input-group-prepend .input-group-text + .input-group-text, .tw-bs4 .input-group-prepend .input-group-text + .btn, .tw-bs4 .input-group-append .btn + .btn, .tw-bs4 .input-group-append .btn + .input-group-text, .tw-bs4 .input-group-append .input-group-text + .input-group-text, .tw-bs4 .input-group-append .input-group-text + .btn {
  margin-left: -1px;
}
.tw-bs4 .input-group-prepend {
  margin-right: -1px;
}
.tw-bs4 .input-group-append {
  margin-left: -1px;
}
.tw-bs4 .input-group-text {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}
.tw-bs4 .input-group-text input[type="radio"], .tw-bs4 .input-group-text input[type="checkbox"] {
  margin-top: 0;
}
.tw-bs4 .input-group-lg > .form-control:not(textarea), .tw-bs4 .input-group-lg > .custom-select {
  height: calc(1.5em + 1rem + 2px);
}
.tw-bs4 .input-group-lg > .form-control, .tw-bs4 .input-group-lg > .custom-select, .tw-bs4 .input-group-lg > .input-group-prepend > .input-group-text, .tw-bs4 .input-group-lg > .input-group-append > .input-group-text, .tw-bs4 .input-group-lg > .input-group-prepend > .btn, .tw-bs4 .input-group-lg > .input-group-append > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.tw-bs4 .input-group-sm > .form-control:not(textarea), .tw-bs4 .input-group-sm > .custom-select {
  height: calc(1.5em + 0.5rem + 2px);
}
.tw-bs4 .input-group-sm > .form-control, .tw-bs4 .input-group-sm > .custom-select, .tw-bs4 .input-group-sm > .input-group-prepend > .input-group-text, .tw-bs4 .input-group-sm > .input-group-append > .input-group-text, .tw-bs4 .input-group-sm > .input-group-prepend > .btn, .tw-bs4 .input-group-sm > .input-group-append > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.tw-bs4 .input-group-lg > .custom-select, .tw-bs4 .input-group-sm > .custom-select {
  padding-right: 1.75rem;
}
.tw-bs4 .input-group > .input-group-prepend > .btn, .tw-bs4 .input-group > .input-group-prepend > .input-group-text, .tw-bs4 .input-group > .input-group-append:not(:last-child) > .btn, .tw-bs4 .input-group > .input-group-append:not(:last-child) > .input-group-text, .tw-bs4 .input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle), .tw-bs4 .input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.tw-bs4 .input-group > .input-group-append > .btn, .tw-bs4 .input-group > .input-group-append > .input-group-text, .tw-bs4 .input-group > .input-group-prepend:not(:first-child) > .btn, .tw-bs4 .input-group > .input-group-prepend:not(:first-child) > .input-group-text, .tw-bs4 .input-group > .input-group-prepend:first-child > .btn:not(:first-child), .tw-bs4 .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.tw-bs4 .custom-control {
  position: relative;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
}
.tw-bs4 .custom-control-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 1rem;
}
.tw-bs4 .custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
.tw-bs4 .custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #007bff;
  background-color: #007bff;
}
.tw-bs4 .custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #80bdff;
}
.tw-bs4 .custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #fff;
  background-color: #b3d7ff;
  border-color: #b3d7ff;
}
.tw-bs4 .custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d;
}
.tw-bs4 .custom-control-input:disabled ~ .custom-control-label::before {
  background-color: #e9ecef;
}
.tw-bs4 .custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}
.tw-bs4 .custom-control-label::before {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  border: #adb5bd solid 1px;
}
.tw-bs4 .custom-control-label::after {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: no-repeat 50% / 50% 50%;
}
.tw-bs4 .custom-checkbox .custom-control-label::before {
  border-radius: 0.25rem;
}
.tw-bs4 .custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");
}
.tw-bs4 .custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #007bff;
  background-color: #007bff;
}
.tw-bs4 .custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}
.tw-bs4 .custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}
.tw-bs4 .custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}
.tw-bs4 .custom-radio .custom-control-label::before {
  border-radius: 50%;
}
.tw-bs4 .custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.tw-bs4 .custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}
.tw-bs4 .custom-switch {
  padding-left: 2.25rem;
}
.tw-bs4 .custom-switch .custom-control-label::before {
  left: -2.25rem;
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}
.tw-bs4 .custom-switch .custom-control-label::after {
  top: calc(0.25rem + 2px);
  left: calc(-2.25rem + 2px);
  width: calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #adb5bd;
  border-radius: 0.5rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .custom-switch .custom-control-label::after {
    transition: none;
  }
}
.tw-bs4 .custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff;
  -webkit-transform: translateX(0.75rem);
  transform: translateX(0.75rem);
}
.tw-bs4 .custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}
.tw-bs4 .custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.tw-bs4 .custom-select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}
.tw-bs4 .custom-select[multiple], .tw-bs4 .custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: 0.75rem;
  background-image: none;
}
.tw-bs4 .custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef;
}
.tw-bs4 .custom-select::-ms-expand {
  display: none;
}
.tw-bs4 .custom-select-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
}
.tw-bs4 .custom-select-lg {
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
}
.tw-bs4 .custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin-bottom: 0;
}
.tw-bs4 .custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin: 0;
  opacity: 0;
}
.tw-bs4 .custom-file-input:focus ~ .custom-file-label {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef;
}
.tw-bs4 .custom-file-input:lang(en) ~ .custom-file-label::after {
  content: "Browse";
}
.tw-bs4 .custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse);
}
.tw-bs4 .custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}
.tw-bs4 .custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(1.5em + 0.75rem);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  content: "Browse";
  background-color: #e9ecef;
  border-left: inherit;
  border-radius: 0 0.25rem 0.25rem 0;
}
.tw-bs4 .custom-range {
  width: 100%;
  height: calc(1rem + 0.4rem);
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.tw-bs4 .custom-range:focus {
  outline: none;
}
.tw-bs4 .custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .custom-range::-moz-focus-outer {
  border: 0;
}
.tw-bs4 .custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .custom-range::-webkit-slider-thumb {
    transition: none;
  }
}
.tw-bs4 .custom-range::-webkit-slider-thumb:active {
  background-color: #b3d7ff;
}
.tw-bs4 .custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.tw-bs4 .custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .custom-range::-moz-range-thumb {
    transition: none;
  }
}
.tw-bs4 .custom-range::-moz-range-thumb:active {
  background-color: #b3d7ff;
}
.tw-bs4 .custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.tw-bs4 .custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .custom-range::-ms-thumb {
    transition: none;
  }
}
.tw-bs4 .custom-range::-ms-thumb:active {
  background-color: #b3d7ff;
}
.tw-bs4 .custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}
.tw-bs4 .custom-range::-ms-fill-lower {
  background-color: #dee2e6;
  border-radius: 1rem;
}
.tw-bs4 .custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #dee2e6;
  border-radius: 1rem;
}
.tw-bs4 .custom-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.tw-bs4 .custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}
.tw-bs4 .custom-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}
.tw-bs4 .custom-range:disabled::-moz-range-track {
  cursor: default;
}
.tw-bs4 .custom-range:disabled::-ms-thumb {
  background-color: #adb5bd;
}
.tw-bs4 .custom-control-label::before, .tw-bs4 .custom-file-label, .tw-bs4 .custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .custom-control-label::before, .tw-bs4 .custom-file-label, .tw-bs4 .custom-select {
    transition: none;
  }
}
.tw-bs4 .nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.tw-bs4 .nav-link {
  display: block;
  padding: 0.5rem 1rem;
}
.tw-bs4 .nav-link:hover, .tw-bs4 .nav-link:focus {
  text-decoration: none;
}
.tw-bs4 .nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}
.tw-bs4 .nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.tw-bs4 .nav-tabs .nav-item {
  margin-bottom: -1px;
}
.tw-bs4 .nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.tw-bs4 .nav-tabs .nav-link:hover, .tw-bs4 .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
}
.tw-bs4 .nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.tw-bs4 .nav-tabs .nav-link.active, .tw-bs4 .nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.tw-bs4 .nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.tw-bs4 .nav-pills .nav-link {
  border-radius: 0.25rem;
}
.tw-bs4 .nav-pills .nav-link.active, .tw-bs4 .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #007bff;
}
.tw-bs4 .nav-fill .nav-item {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}
.tw-bs4 .nav-justified .nav-item {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}
.tw-bs4 .tab-content > .tab-pane {
  display: none;
}
.tw-bs4 .tab-content > .active {
  display: block;
}
.tw-bs4 .navbar {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}
.tw-bs4 .navbar > .container, .tw-bs4 .navbar > .container-fluid {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.tw-bs4 .navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}
.tw-bs4 .navbar-brand:hover, .tw-bs4 .navbar-brand:focus {
  text-decoration: none;
}
.tw-bs4 .navbar-nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.tw-bs4 .navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.tw-bs4 .navbar-nav .dropdown-menu {
  position: static;
  float: none;
}
.tw-bs4 .navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.tw-bs4 .navbar-collapse {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-align: center;
  align-items: center;
}
.tw-bs4 .navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.tw-bs4 .navbar-toggler:hover, .tw-bs4 .navbar-toggler:focus {
  text-decoration: none;
}
.tw-bs4 .navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: no-repeat center center;
  background-size: 100% 100%;
}
@media (max-width: 575.98px) {
  .tw-bs4 .navbar-expand-sm > .container, .tw-bs4 .navbar-expand-sm > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 576px) {
  .tw-bs4 .navbar-expand-sm {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .tw-bs4 .navbar-expand-sm .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .tw-bs4 .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .tw-bs4 .navbar-expand-sm > .container, .tw-bs4 .navbar-expand-sm > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .tw-bs4 .navbar-expand-sm .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .tw-bs4 .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}
@media (max-width: 767.98px) {
  .tw-bs4 .navbar-expand-md > .container, .tw-bs4 .navbar-expand-md > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .navbar-expand-md {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .tw-bs4 .navbar-expand-md .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .tw-bs4 .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .tw-bs4 .navbar-expand-md > .container, .tw-bs4 .navbar-expand-md > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .tw-bs4 .navbar-expand-md .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .tw-bs4 .navbar-expand-md .navbar-toggler {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .tw-bs4 .navbar-expand-lg > .container, .tw-bs4 .navbar-expand-lg > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .navbar-expand-lg {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .tw-bs4 .navbar-expand-lg .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .tw-bs4 .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .tw-bs4 .navbar-expand-lg > .container, .tw-bs4 .navbar-expand-lg > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .tw-bs4 .navbar-expand-lg .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .tw-bs4 .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .tw-bs4 .navbar-expand-xl > .container, .tw-bs4 .navbar-expand-xl > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .navbar-expand-xl {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .tw-bs4 .navbar-expand-xl .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .tw-bs4 .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .tw-bs4 .navbar-expand-xl > .container, .tw-bs4 .navbar-expand-xl > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .tw-bs4 .navbar-expand-xl .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .tw-bs4 .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}
.tw-bs4 .navbar-expand {
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.tw-bs4 .navbar-expand > .container, .tw-bs4 .navbar-expand > .container-fluid {
  padding-right: 0;
  padding-left: 0;
}
.tw-bs4 .navbar-expand .navbar-nav {
  -ms-flex-direction: row;
  flex-direction: row;
}
.tw-bs4 .navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.tw-bs4 .navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.tw-bs4 .navbar-expand > .container, .tw-bs4 .navbar-expand > .container-fluid {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
.tw-bs4 .navbar-expand .navbar-collapse {
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}
.tw-bs4 .navbar-expand .navbar-toggler {
  display: none;
}
.tw-bs4 .navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}
.tw-bs4 .navbar-light .navbar-brand:hover, .tw-bs4 .navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}
.tw-bs4 .navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}
.tw-bs4 .navbar-light .navbar-nav .nav-link:hover, .tw-bs4 .navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 0, 0.7);
}
.tw-bs4 .navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}
.tw-bs4 .navbar-light .navbar-nav .show > .nav-link, .tw-bs4 .navbar-light .navbar-nav .active > .nav-link, .tw-bs4 .navbar-light .navbar-nav .nav-link.show, .tw-bs4 .navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}
.tw-bs4 .navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}
.tw-bs4 .navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.tw-bs4 .navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}
.tw-bs4 .navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}
.tw-bs4 .navbar-light .navbar-text a:hover, .tw-bs4 .navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}
.tw-bs4 .navbar-dark .navbar-brand {
  color: #fff;
}
.tw-bs4 .navbar-dark .navbar-brand:hover, .tw-bs4 .navbar-dark .navbar-brand:focus {
  color: #fff;
}
.tw-bs4 .navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}
.tw-bs4 .navbar-dark .navbar-nav .nav-link:hover, .tw-bs4 .navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}
.tw-bs4 .navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}
.tw-bs4 .navbar-dark .navbar-nav .show > .nav-link, .tw-bs4 .navbar-dark .navbar-nav .active > .nav-link, .tw-bs4 .navbar-dark .navbar-nav .nav-link.show, .tw-bs4 .navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}
.tw-bs4 .navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}
.tw-bs4 .navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.tw-bs4 .navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}
.tw-bs4 .navbar-dark .navbar-text a {
  color: #fff;
}
.tw-bs4 .navbar-dark .navbar-text a:hover, .tw-bs4 .navbar-dark .navbar-text a:focus {
  color: #fff;
}
.tw-bs4 .card {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}
.tw-bs4 .card > hr {
  margin-right: 0;
  margin-left: 0;
}
.tw-bs4 .card > .list-group:first-child .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.tw-bs4 .card > .list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.tw-bs4 .card-body {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1.25rem;
}
.tw-bs4 .card-title {
  margin-bottom: 0.75rem;
}
.tw-bs4 .card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}
.tw-bs4 .card-text:last-child {
  margin-bottom: 0;
}
.tw-bs4 .card-link:hover {
  text-decoration: none;
}
.tw-bs4 .card-link + .card-link {
  margin-left: 1.25rem;
}
.tw-bs4 .card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
.tw-bs4 .card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}
.tw-bs4 .card-header + .list-group .list-group-item:first-child {
  border-top: 0;
}
.tw-bs4 .card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}
.tw-bs4 .card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}
.tw-bs4 .card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}
.tw-bs4 .card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}
.tw-bs4 .card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
}
.tw-bs4 .card-img {
  width: 100%;
  border-radius: calc(0.25rem - 1px);
}
.tw-bs4 .card-img-top {
  width: 100%;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.tw-bs4 .card-img-bottom {
  width: 100%;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.tw-bs4 .card-deck {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
}
.tw-bs4 .card-deck .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .tw-bs4 .card-deck {
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .tw-bs4 .card-deck .card {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}
.tw-bs4 .card-group {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
}
.tw-bs4 .card-group > .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .tw-bs4 .card-group {
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
  }
  .tw-bs4 .card-group > .card {
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .tw-bs4 .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .tw-bs4 .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .tw-bs4 .card-group > .card:not(:last-child) .card-img-top, .tw-bs4 .card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .tw-bs4 .card-group > .card:not(:last-child) .card-img-bottom, .tw-bs4 .card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .tw-bs4 .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .tw-bs4 .card-group > .card:not(:first-child) .card-img-top, .tw-bs4 .card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .tw-bs4 .card-group > .card:not(:first-child) .card-img-bottom, .tw-bs4 .card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}
.tw-bs4 .card-columns .card {
  margin-bottom: 0.75rem;
}
@media (min-width: 576px) {
  .tw-bs4 .card-columns {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 1.25rem;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .tw-bs4 .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}
.tw-bs4 .accordion > .card {
  overflow: hidden;
}
.tw-bs4 .accordion > .card:not(:first-of-type) .card-header:first-child {
  border-radius: 0;
}
.tw-bs4 .accordion > .card:not(:first-of-type):not(:last-of-type) {
  border-bottom: 0;
  border-radius: 0;
}
.tw-bs4 .accordion > .card:first-of-type {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.tw-bs4 .accordion > .card:last-of-type {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.tw-bs4 .accordion > .card .card-header {
  margin-bottom: -1px;
}
.tw-bs4 .breadcrumb {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}
.tw-bs4 .breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.tw-bs4 .breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #6c757d;
  content: "/";
}
.tw-bs4 .breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
}
.tw-bs4 .breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: none;
}
.tw-bs4 .breadcrumb-item.active {
  color: #6c757d;
}
.tw-bs4 .pagination {
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}
.tw-bs4 .page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #007bff;
  background-color: #fff;
  border: 1px solid #dee2e6;
}
.tw-bs4 .page-link:hover {
  z-index: 2;
  color: #0056b3;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
.tw-bs4 .page-link:focus {
  z-index: 2;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.tw-bs4 .page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.tw-bs4 .page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.tw-bs4 .page-item.active .page-link {
  z-index: 1;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}
.tw-bs4 .page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dee2e6;
}
.tw-bs4 .pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
}
.tw-bs4 .pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.tw-bs4 .pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}
.tw-bs4 .pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}
.tw-bs4 .pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}
.tw-bs4 .pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}
.tw-bs4 .badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .badge {
    transition: none;
  }
}
.tw-bs4 a.badge:hover, .tw-bs4 a.badge:focus {
  text-decoration: none;
}
.tw-bs4 .badge:empty {
  display: none;
}
.tw-bs4 .btn .badge {
  position: relative;
  top: -1px;
}
.tw-bs4 .badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}
.tw-bs4 .badge-primary {
  color: #fff;
  background-color: #007bff;
}
.tw-bs4 a.badge-primary:hover, .tw-bs4 a.badge-primary:focus {
  color: #fff;
  background-color: #0062cc;
}
.tw-bs4 a.badge-primary:focus, .tw-bs4 a.badge-primary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}
.tw-bs4 .badge-secondary {
  color: #fff;
  background-color: #6c757d;
}
.tw-bs4 a.badge-secondary:hover, .tw-bs4 a.badge-secondary:focus {
  color: #fff;
  background-color: #545b62;
}
.tw-bs4 a.badge-secondary:focus, .tw-bs4 a.badge-secondary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}
.tw-bs4 .badge-success {
  color: #fff;
  background-color: #28a745;
}
.tw-bs4 a.badge-success:hover, .tw-bs4 a.badge-success:focus {
  color: #fff;
  background-color: #1e7e34;
}
.tw-bs4 a.badge-success:focus, .tw-bs4 a.badge-success.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}
.tw-bs4 .badge-info {
  color: #fff;
  background-color: #17a2b8;
}
.tw-bs4 a.badge-info:hover, .tw-bs4 a.badge-info:focus {
  color: #fff;
  background-color: #117a8b;
}
.tw-bs4 a.badge-info:focus, .tw-bs4 a.badge-info.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}
.tw-bs4 .badge-warning {
  color: #212529;
  background-color: #ffc107;
}
.tw-bs4 a.badge-warning:hover, .tw-bs4 a.badge-warning:focus {
  color: #212529;
  background-color: #d39e00;
}
.tw-bs4 a.badge-warning:focus, .tw-bs4 a.badge-warning.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.tw-bs4 .badge-danger {
  color: #fff;
  background-color: #dc3545;
}
.tw-bs4 a.badge-danger:hover, .tw-bs4 a.badge-danger:focus {
  color: #fff;
  background-color: #bd2130;
}
.tw-bs4 a.badge-danger:focus, .tw-bs4 a.badge-danger.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}
.tw-bs4 .badge-light {
  color: #212529;
  background-color: #f8f9fa;
}
.tw-bs4 a.badge-light:hover, .tw-bs4 a.badge-light:focus {
  color: #212529;
  background-color: #dae0e5;
}
.tw-bs4 a.badge-light:focus, .tw-bs4 a.badge-light.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}
.tw-bs4 .badge-dark {
  color: #fff;
  background-color: #343a40;
}
.tw-bs4 a.badge-dark:hover, .tw-bs4 a.badge-dark:focus {
  color: #fff;
  background-color: #1d2124;
}
.tw-bs4 a.badge-dark:focus, .tw-bs4 a.badge-dark.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}
.tw-bs4 .jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}
@media (min-width: 576px) {
  .tw-bs4 .jumbotron {
    padding: 4rem 2rem;
  }
}
.tw-bs4 .jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}
.tw-bs4 .alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.tw-bs4 .alert-heading {
  color: inherit;
}
.tw-bs4 .alert-link {
  font-weight: 700;
}
.tw-bs4 .alert-dismissible {
  padding-right: 4rem;
}
.tw-bs4 .alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
}
.tw-bs4 .alert-primary {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}
.tw-bs4 .alert-primary hr {
  border-top-color: #9fcdff;
}
.tw-bs4 .alert-primary .alert-link {
  color: #002752;
}
.tw-bs4 .alert-secondary {
  color: #383d41;
  background-color: #e2e3e5;
  border-color: #d6d8db;
}
.tw-bs4 .alert-secondary hr {
  border-top-color: #c8cbcf;
}
.tw-bs4 .alert-secondary .alert-link {
  color: #202326;
}
.tw-bs4 .alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}
.tw-bs4 .alert-success hr {
  border-top-color: #b1dfbb;
}
.tw-bs4 .alert-success .alert-link {
  color: #0b2e13;
}
.tw-bs4 .alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}
.tw-bs4 .alert-info hr {
  border-top-color: #abdde5;
}
.tw-bs4 .alert-info .alert-link {
  color: #062c33;
}
.tw-bs4 .alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}
.tw-bs4 .alert-warning hr {
  border-top-color: #ffe8a1;
}
.tw-bs4 .alert-warning .alert-link {
  color: #533f03;
}
.tw-bs4 .alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}
.tw-bs4 .alert-danger hr {
  border-top-color: #f1b0b7;
}
.tw-bs4 .alert-danger .alert-link {
  color: #491217;
}
.tw-bs4 .alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}
.tw-bs4 .alert-light hr {
  border-top-color: #ececf6;
}
.tw-bs4 .alert-light .alert-link {
  color: #686868;
}
.tw-bs4 .alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}
.tw-bs4 .alert-dark hr {
  border-top-color: #b9bbbe;
}
.tw-bs4 .alert-dark .alert-link {
  color: #040505;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
.tw-bs4 .progress {
  display: -ms-flexbox;
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}
.tw-bs4 .progress-bar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #007bff;
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .progress-bar {
    transition: none;
  }
}
.tw-bs4 .progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}
.tw-bs4 .progress-bar-animated {
  -webkit-animation: progress-bar-stripes 1s linear infinite;
  animation: progress-bar-stripes 1s linear infinite;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .progress-bar-animated {
    -webkit-animation: none;
    animation: none;
  }
}
.tw-bs4 .media {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
}
.tw-bs4 .media-body {
  -ms-flex: 1;
  flex: 1;
}
.tw-bs4 .list-group {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
}
.tw-bs4 .list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}
.tw-bs4 .list-group-item-action:hover, .tw-bs4 .list-group-item-action:focus {
  z-index: 1;
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}
.tw-bs4 .list-group-item-action:active {
  color: #212529;
  background-color: #e9ecef;
}
.tw-bs4 .list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.tw-bs4 .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.tw-bs4 .list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.tw-bs4 .list-group-item.disabled, .tw-bs4 .list-group-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
}
.tw-bs4 .list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}
.tw-bs4 .list-group-horizontal {
  -ms-flex-direction: row;
  flex-direction: row;
}
.tw-bs4 .list-group-horizontal .list-group-item {
  margin-right: -1px;
  margin-bottom: 0;
}
.tw-bs4 .list-group-horizontal .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}
.tw-bs4 .list-group-horizontal .list-group-item:last-child {
  margin-right: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}
@media (min-width: 576px) {
  .tw-bs4 .list-group-horizontal-sm {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .list-group-horizontal-sm .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .tw-bs4 .list-group-horizontal-sm .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .tw-bs4 .list-group-horizontal-sm .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .list-group-horizontal-md {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .list-group-horizontal-md .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .tw-bs4 .list-group-horizontal-md .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .tw-bs4 .list-group-horizontal-md .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .list-group-horizontal-lg {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .list-group-horizontal-lg .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .tw-bs4 .list-group-horizontal-lg .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .tw-bs4 .list-group-horizontal-lg .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .list-group-horizontal-xl {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .tw-bs4 .list-group-horizontal-xl .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .tw-bs4 .list-group-horizontal-xl .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .tw-bs4 .list-group-horizontal-xl .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
.tw-bs4 .list-group-flush .list-group-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.tw-bs4 .list-group-flush .list-group-item:last-child {
  margin-bottom: -1px;
}
.tw-bs4 .list-group-flush:first-child .list-group-item:first-child {
  border-top: 0;
}
.tw-bs4 .list-group-flush:last-child .list-group-item:last-child {
  margin-bottom: 0;
  border-bottom: 0;
}
.tw-bs4 .list-group-item-primary {
  color: #004085;
  background-color: #b8daff;
}
.tw-bs4 .list-group-item-primary.list-group-item-action:hover, .tw-bs4 .list-group-item-primary.list-group-item-action:focus {
  color: #004085;
  background-color: #9fcdff;
}
.tw-bs4 .list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #004085;
  border-color: #004085;
}
.tw-bs4 .list-group-item-secondary {
  color: #383d41;
  background-color: #d6d8db;
}
.tw-bs4 .list-group-item-secondary.list-group-item-action:hover, .tw-bs4 .list-group-item-secondary.list-group-item-action:focus {
  color: #383d41;
  background-color: #c8cbcf;
}
.tw-bs4 .list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #383d41;
  border-color: #383d41;
}
.tw-bs4 .list-group-item-success {
  color: #155724;
  background-color: #c3e6cb;
}
.tw-bs4 .list-group-item-success.list-group-item-action:hover, .tw-bs4 .list-group-item-success.list-group-item-action:focus {
  color: #155724;
  background-color: #b1dfbb;
}
.tw-bs4 .list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #155724;
  border-color: #155724;
}
.tw-bs4 .list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}
.tw-bs4 .list-group-item-info.list-group-item-action:hover, .tw-bs4 .list-group-item-info.list-group-item-action:focus {
  color: #0c5460;
  background-color: #abdde5;
}
.tw-bs4 .list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}
.tw-bs4 .list-group-item-warning {
  color: #856404;
  background-color: #ffeeba;
}
.tw-bs4 .list-group-item-warning.list-group-item-action:hover, .tw-bs4 .list-group-item-warning.list-group-item-action:focus {
  color: #856404;
  background-color: #ffe8a1;
}
.tw-bs4 .list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #856404;
  border-color: #856404;
}
.tw-bs4 .list-group-item-danger {
  color: #721c24;
  background-color: #f5c6cb;
}
.tw-bs4 .list-group-item-danger.list-group-item-action:hover, .tw-bs4 .list-group-item-danger.list-group-item-action:focus {
  color: #721c24;
  background-color: #f1b0b7;
}
.tw-bs4 .list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #721c24;
  border-color: #721c24;
}
.tw-bs4 .list-group-item-light {
  color: #818182;
  background-color: #fdfdfe;
}
.tw-bs4 .list-group-item-light.list-group-item-action:hover, .tw-bs4 .list-group-item-light.list-group-item-action:focus {
  color: #818182;
  background-color: #ececf6;
}
.tw-bs4 .list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #818182;
  border-color: #818182;
}
.tw-bs4 .list-group-item-dark {
  color: #1b1e21;
  background-color: #c6c8ca;
}
.tw-bs4 .list-group-item-dark.list-group-item-action:hover, .tw-bs4 .list-group-item-dark.list-group-item-action:focus {
  color: #1b1e21;
  background-color: #b9bbbe;
}
.tw-bs4 .list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #1b1e21;
  border-color: #1b1e21;
}
.tw-bs4 .close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}
.tw-bs4 .close:hover {
  color: #000;
  text-decoration: none;
}
.tw-bs4 .close:not(:disabled):not(.disabled):hover, .tw-bs4 .close:not(:disabled):not(.disabled):focus {
  opacity: 0.75;
}
.tw-bs4 button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.tw-bs4 a.close.disabled {
  pointer-events: none;
}
.tw-bs4 .toast {
  max-width: 350px;
  overflow: hidden;
  font-size: 0.875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  opacity: 0;
  border-radius: 0.25rem;
}
.tw-bs4 .toast:not(:last-child) {
  margin-bottom: 0.75rem;
}
.tw-bs4 .toast.showing {
  opacity: 1;
}
.tw-bs4 .toast.show {
  display: block;
  opacity: 1;
}
.tw-bs4 .toast.hide {
  display: none;
}
.tw-bs4 .toast-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.tw-bs4 .toast-body {
  padding: 0.75rem;
}
.tw-bs4 .modal-open {
  overflow: hidden;
}
.tw-bs4 .modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.tw-bs4 .modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}
.tw-bs4 .modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.tw-bs4 .modal.fade .modal-dialog {
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -50px);
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .modal.fade .modal-dialog {
    transition: none;
  }
}
.tw-bs4 .modal.show .modal-dialog {
  -webkit-transform: none;
  transform: none;
}
.tw-bs4 .modal-dialog-scrollable {
  display: -ms-flexbox;
  display: flex;
  max-height: calc(100% - 1rem);
}
.tw-bs4 .modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}
.tw-bs4 .modal-dialog-scrollable .modal-header, .tw-bs4 .modal-dialog-scrollable .modal-footer {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}
.tw-bs4 .modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}
.tw-bs4 .modal-dialog-centered {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - 1rem);
}
.tw-bs4 .modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  content: "";
}
.tw-bs4 .modal-dialog-centered.modal-dialog-scrollable {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
}
.tw-bs4 .modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}
.tw-bs4 .modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}
.tw-bs4 .modal-content {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}
.tw-bs4 .modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.tw-bs4 .modal-backdrop.fade {
  opacity: 0;
}
.tw-bs4 .modal-backdrop.show {
  opacity: 0.5;
}
.tw-bs4 .modal-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}
.tw-bs4 .modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}
.tw-bs4 .modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}
.tw-bs4 .modal-body {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem;
}
.tw-bs4 .modal-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.tw-bs4 .modal-footer > :not(:first-child) {
  margin-left: 0.25rem;
}
.tw-bs4 .modal-footer > :not(:last-child) {
  margin-right: 0.25rem;
}
.tw-bs4 .modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 576px) {
  .tw-bs4 .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .tw-bs4 .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .tw-bs4 .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }
  .tw-bs4 .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .tw-bs4 .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
  }
  .tw-bs4 .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .modal-lg, .tw-bs4 .modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .modal-xl {
    max-width: 1140px;
  }
}
.tw-bs4 .tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}
.tw-bs4 .tooltip.show {
  opacity: 0.9;
}
.tw-bs4 .tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tw-bs4 .tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}
.tw-bs4 .bs-tooltip-top, .tw-bs4 .bs-tooltip-auto[x-placement^="top"] {
  padding: 0.4rem 0;
}
.tw-bs4 .bs-tooltip-top .arrow, .tw-bs4 .bs-tooltip-auto[x-placement^="top"] .arrow {
  bottom: 0;
}
.tw-bs4 .bs-tooltip-top .arrow::before, .tw-bs4 .bs-tooltip-auto[x-placement^="top"] .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}
.tw-bs4 .bs-tooltip-right, .tw-bs4 .bs-tooltip-auto[x-placement^="right"] {
  padding: 0 0.4rem;
}
.tw-bs4 .bs-tooltip-right .arrow, .tw-bs4 .bs-tooltip-auto[x-placement^="right"] .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.tw-bs4 .bs-tooltip-right .arrow::before, .tw-bs4 .bs-tooltip-auto[x-placement^="right"] .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}
.tw-bs4 .bs-tooltip-bottom, .tw-bs4 .bs-tooltip-auto[x-placement^="bottom"] {
  padding: 0.4rem 0;
}
.tw-bs4 .bs-tooltip-bottom .arrow, .tw-bs4 .bs-tooltip-auto[x-placement^="bottom"] .arrow {
  top: 0;
}
.tw-bs4 .bs-tooltip-bottom .arrow::before, .tw-bs4 .bs-tooltip-auto[x-placement^="bottom"] .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}
.tw-bs4 .bs-tooltip-left, .tw-bs4 .bs-tooltip-auto[x-placement^="left"] {
  padding: 0 0.4rem;
}
.tw-bs4 .bs-tooltip-left .arrow, .tw-bs4 .bs-tooltip-auto[x-placement^="left"] .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.tw-bs4 .bs-tooltip-left .arrow::before, .tw-bs4 .bs-tooltip-auto[x-placement^="left"] .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}
.tw-bs4 .tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}
.tw-bs4 .popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}
.tw-bs4 .popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}
.tw-bs4 .popover .arrow::before, .tw-bs4 .popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}
.tw-bs4 .bs-popover-top, .tw-bs4 .bs-popover-auto[x-placement^="top"] {
  margin-bottom: 0.5rem;
}
.tw-bs4 .bs-popover-top > .arrow, .tw-bs4 .bs-popover-auto[x-placement^="top"] > .arrow {
  bottom: calc((0.5rem + 1px) * -1);
}
.tw-bs4 .bs-popover-top > .arrow::before, .tw-bs4 .bs-popover-auto[x-placement^="top"] > .arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.tw-bs4 .bs-popover-top > .arrow::after, .tw-bs4 .bs-popover-auto[x-placement^="top"] > .arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}
.tw-bs4 .bs-popover-right, .tw-bs4 .bs-popover-auto[x-placement^="right"] {
  margin-left: 0.5rem;
}
.tw-bs4 .bs-popover-right > .arrow, .tw-bs4 .bs-popover-auto[x-placement^="right"] > .arrow {
  left: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.tw-bs4 .bs-popover-right > .arrow::before, .tw-bs4 .bs-popover-auto[x-placement^="right"] > .arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.tw-bs4 .bs-popover-right > .arrow::after, .tw-bs4 .bs-popover-auto[x-placement^="right"] > .arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}
.tw-bs4 .bs-popover-bottom, .tw-bs4 .bs-popover-auto[x-placement^="bottom"] {
  margin-top: 0.5rem;
}
.tw-bs4 .bs-popover-bottom > .arrow, .tw-bs4 .bs-popover-auto[x-placement^="bottom"] > .arrow {
  top: calc((0.5rem + 1px) * -1);
}
.tw-bs4 .bs-popover-bottom > .arrow::before, .tw-bs4 .bs-popover-auto[x-placement^="bottom"] > .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.tw-bs4 .bs-popover-bottom > .arrow::after, .tw-bs4 .bs-popover-auto[x-placement^="bottom"] > .arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.tw-bs4 .bs-popover-bottom .popover-header::before, .tw-bs4 .bs-popover-auto[x-placement^="bottom"] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7;
}
.tw-bs4 .bs-popover-left, .tw-bs4 .bs-popover-auto[x-placement^="left"] {
  margin-right: 0.5rem;
}
.tw-bs4 .bs-popover-left > .arrow, .tw-bs4 .bs-popover-auto[x-placement^="left"] > .arrow {
  right: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.tw-bs4 .bs-popover-left > .arrow::before, .tw-bs4 .bs-popover-auto[x-placement^="left"] > .arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.tw-bs4 .bs-popover-left > .arrow::after, .tw-bs4 .bs-popover-auto[x-placement^="left"] > .arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}
.tw-bs4 .popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.tw-bs4 .popover-header:empty {
  display: none;
}
.tw-bs4 .popover-body {
  padding: 0.5rem 0.75rem;
  color: #212529;
}
.tw-bs4 .carousel {
  position: relative;
}
.tw-bs4 .carousel.pointer-event {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}
.tw-bs4 .carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.tw-bs4 .carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}
.tw-bs4 .carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: -webkit-transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .carousel-item {
    transition: none;
  }
}
.tw-bs4 .carousel-item.active, .tw-bs4 .carousel-item-next, .tw-bs4 .carousel-item-prev {
  display: block;
}
.tw-bs4 .carousel-item-next:not(.carousel-item-left), .tw-bs4 .active.carousel-item-right {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}
.tw-bs4 .carousel-item-prev:not(.carousel-item-right), .tw-bs4 .active.carousel-item-left {
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}
.tw-bs4 .carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  -webkit-transform: none;
  transform: none;
}
.tw-bs4 .carousel-fade .carousel-item.active, .tw-bs4 .carousel-fade .carousel-item-next.carousel-item-left, .tw-bs4 .carousel-fade .carousel-item-prev.carousel-item-right {
  z-index: 1;
  opacity: 1;
}
.tw-bs4 .carousel-fade .active.carousel-item-left, .tw-bs4 .carousel-fade .active.carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: 0s 0.6s opacity;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .carousel-fade .active.carousel-item-left, .tw-bs4 .carousel-fade .active.carousel-item-right {
    transition: none;
  }
}
.tw-bs4 .carousel-control-prev, .tw-bs4 .carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 15%;
  color: #fff;
  text-align: center;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .carousel-control-prev, .tw-bs4 .carousel-control-next {
    transition: none;
  }
}
.tw-bs4 .carousel-control-prev:hover, .tw-bs4 .carousel-control-prev:focus, .tw-bs4 .carousel-control-next:hover, .tw-bs4 .carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}
.tw-bs4 .carousel-control-prev {
  left: 0;
}
.tw-bs4 .carousel-control-next {
  right: 0;
}
.tw-bs4 .carousel-control-prev-icon, .tw-bs4 .carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: no-repeat 50% / 100% 100%;
}
.tw-bs4 .carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e");
}
.tw-bs4 .carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e");
}
.tw-bs4 .carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}
.tw-bs4 .carousel-indicators li {
  box-sizing: content-box;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .tw-bs4 .carousel-indicators li {
    transition: none;
  }
}
.tw-bs4 .carousel-indicators .active {
  opacity: 1;
}
.tw-bs4 .carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}
@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.tw-bs4 .spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border 0.75s linear infinite;
  animation: spinner-border 0.75s linear infinite;
}
.tw-bs4 .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}
@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}
@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}
.tw-bs4 .spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: spinner-grow 0.75s linear infinite;
  animation: spinner-grow 0.75s linear infinite;
}
.tw-bs4 .spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}
.tw-bs4 .align-baseline {
  vertical-align: baseline !important;
}
.tw-bs4 .align-top {
  vertical-align: top !important;
}
.tw-bs4 .align-middle {
  vertical-align: middle !important;
}
.tw-bs4 .align-bottom {
  vertical-align: bottom !important;
}
.tw-bs4 .align-text-bottom {
  vertical-align: text-bottom !important;
}
.tw-bs4 .align-text-top {
  vertical-align: text-top !important;
}
.tw-bs4 .bg-primary {
  background-color: #007bff !important;
}
.tw-bs4 a.bg-primary:hover, .tw-bs4 a.bg-primary:focus, .tw-bs4 button.bg-primary:hover, .tw-bs4 button.bg-primary:focus {
  background-color: #0062cc !important;
}
.tw-bs4 .bg-secondary {
  background-color: #6c757d !important;
}
.tw-bs4 a.bg-secondary:hover, .tw-bs4 a.bg-secondary:focus, .tw-bs4 button.bg-secondary:hover, .tw-bs4 button.bg-secondary:focus {
  background-color: #545b62 !important;
}
.tw-bs4 .bg-success {
  background-color: #28a745 !important;
}
.tw-bs4 a.bg-success:hover, .tw-bs4 a.bg-success:focus, .tw-bs4 button.bg-success:hover, .tw-bs4 button.bg-success:focus {
  background-color: #1e7e34 !important;
}
.tw-bs4 .bg-info {
  background-color: #17a2b8 !important;
}
.tw-bs4 a.bg-info:hover, .tw-bs4 a.bg-info:focus, .tw-bs4 button.bg-info:hover, .tw-bs4 button.bg-info:focus {
  background-color: #117a8b !important;
}
.tw-bs4 .bg-warning {
  background-color: #ffc107 !important;
}
.tw-bs4 a.bg-warning:hover, .tw-bs4 a.bg-warning:focus, .tw-bs4 button.bg-warning:hover, .tw-bs4 button.bg-warning:focus {
  background-color: #d39e00 !important;
}
.tw-bs4 .bg-danger {
  background-color: #dc3545 !important;
}
.tw-bs4 a.bg-danger:hover, .tw-bs4 a.bg-danger:focus, .tw-bs4 button.bg-danger:hover, .tw-bs4 button.bg-danger:focus {
  background-color: #bd2130 !important;
}
.tw-bs4 .bg-light {
  background-color: #f8f9fa !important;
}
.tw-bs4 a.bg-light:hover, .tw-bs4 a.bg-light:focus, .tw-bs4 button.bg-light:hover, .tw-bs4 button.bg-light:focus {
  background-color: #dae0e5 !important;
}
.tw-bs4 .bg-dark {
  background-color: #343a40 !important;
}
.tw-bs4 a.bg-dark:hover, .tw-bs4 a.bg-dark:focus, .tw-bs4 button.bg-dark:hover, .tw-bs4 button.bg-dark:focus {
  background-color: #1d2124 !important;
}
.tw-bs4 .bg-white {
  background-color: #fff !important;
}
.tw-bs4 .bg-transparent {
  background-color: transparent !important;
}
.tw-bs4 .border {
  border: 1px solid #dee2e6 !important;
}
.tw-bs4 .border-top {
  border-top: 1px solid #dee2e6 !important;
}
.tw-bs4 .border-right {
  border-right: 1px solid #dee2e6 !important;
}
.tw-bs4 .border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}
.tw-bs4 .border-left {
  border-left: 1px solid #dee2e6 !important;
}
.tw-bs4 .border-0 {
  border: 0 !important;
}
.tw-bs4 .border-top-0 {
  border-top: 0 !important;
}
.tw-bs4 .border-right-0 {
  border-right: 0 !important;
}
.tw-bs4 .border-bottom-0 {
  border-bottom: 0 !important;
}
.tw-bs4 .border-left-0 {
  border-left: 0 !important;
}
.tw-bs4 .border-primary {
  border-color: #007bff !important;
}
.tw-bs4 .border-secondary {
  border-color: #6c757d !important;
}
.tw-bs4 .border-success {
  border-color: #28a745 !important;
}
.tw-bs4 .border-info {
  border-color: #17a2b8 !important;
}
.tw-bs4 .border-warning {
  border-color: #ffc107 !important;
}
.tw-bs4 .border-danger {
  border-color: #dc3545 !important;
}
.tw-bs4 .border-light {
  border-color: #f8f9fa !important;
}
.tw-bs4 .border-dark {
  border-color: #343a40 !important;
}
.tw-bs4 .border-white {
  border-color: #fff !important;
}
.tw-bs4 .rounded-sm {
  border-radius: 0.2rem !important;
}
.tw-bs4 .rounded {
  border-radius: 0.25rem !important;
}
.tw-bs4 .rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}
.tw-bs4 .rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}
.tw-bs4 .rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}
.tw-bs4 .rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}
.tw-bs4 .rounded-lg {
  border-radius: 0.3rem !important;
}
.tw-bs4 .rounded-circle {
  border-radius: 50% !important;
}
.tw-bs4 .rounded-pill {
  border-radius: 50rem !important;
}
.tw-bs4 .rounded-0 {
  border-radius: 0 !important;
}
.tw-bs4 .clearfix::after {
  display: block;
  clear: both;
  content: "";
}
.tw-bs4 .d-none {
  display: none !important;
}
.tw-bs4 .d-inline {
  display: inline !important;
}
.tw-bs4 .d-inline-block {
  display: inline-block !important;
}
.tw-bs4 .d-block {
  display: block !important;
}
.tw-bs4 .d-table {
  display: table !important;
}
.tw-bs4 .d-table-row {
  display: table-row !important;
}
.tw-bs4 .d-table-cell {
  display: table-cell !important;
}
.tw-bs4 .d-flex {
  display: -ms-flexbox !important;
  display: flex !important;
}
.tw-bs4 .d-inline-flex {
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
}
@media (min-width: 576px) {
  .tw-bs4 .d-sm-none {
    display: none !important;
  }
  .tw-bs4 .d-sm-inline {
    display: inline !important;
  }
  .tw-bs4 .d-sm-inline-block {
    display: inline-block !important;
  }
  .tw-bs4 .d-sm-block {
    display: block !important;
  }
  .tw-bs4 .d-sm-table {
    display: table !important;
  }
  .tw-bs4 .d-sm-table-row {
    display: table-row !important;
  }
  .tw-bs4 .d-sm-table-cell {
    display: table-cell !important;
  }
  .tw-bs4 .d-sm-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .tw-bs4 .d-sm-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .d-md-none {
    display: none !important;
  }
  .tw-bs4 .d-md-inline {
    display: inline !important;
  }
  .tw-bs4 .d-md-inline-block {
    display: inline-block !important;
  }
  .tw-bs4 .d-md-block {
    display: block !important;
  }
  .tw-bs4 .d-md-table {
    display: table !important;
  }
  .tw-bs4 .d-md-table-row {
    display: table-row !important;
  }
  .tw-bs4 .d-md-table-cell {
    display: table-cell !important;
  }
  .tw-bs4 .d-md-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .tw-bs4 .d-md-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .d-lg-none {
    display: none !important;
  }
  .tw-bs4 .d-lg-inline {
    display: inline !important;
  }
  .tw-bs4 .d-lg-inline-block {
    display: inline-block !important;
  }
  .tw-bs4 .d-lg-block {
    display: block !important;
  }
  .tw-bs4 .d-lg-table {
    display: table !important;
  }
  .tw-bs4 .d-lg-table-row {
    display: table-row !important;
  }
  .tw-bs4 .d-lg-table-cell {
    display: table-cell !important;
  }
  .tw-bs4 .d-lg-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .tw-bs4 .d-lg-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .d-xl-none {
    display: none !important;
  }
  .tw-bs4 .d-xl-inline {
    display: inline !important;
  }
  .tw-bs4 .d-xl-inline-block {
    display: inline-block !important;
  }
  .tw-bs4 .d-xl-block {
    display: block !important;
  }
  .tw-bs4 .d-xl-table {
    display: table !important;
  }
  .tw-bs4 .d-xl-table-row {
    display: table-row !important;
  }
  .tw-bs4 .d-xl-table-cell {
    display: table-cell !important;
  }
  .tw-bs4 .d-xl-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .tw-bs4 .d-xl-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
@media print {
  .tw-bs4 .d-print-none {
    display: none !important;
  }
  .tw-bs4 .d-print-inline {
    display: inline !important;
  }
  .tw-bs4 .d-print-inline-block {
    display: inline-block !important;
  }
  .tw-bs4 .d-print-block {
    display: block !important;
  }
  .tw-bs4 .d-print-table {
    display: table !important;
  }
  .tw-bs4 .d-print-table-row {
    display: table-row !important;
  }
  .tw-bs4 .d-print-table-cell {
    display: table-cell !important;
  }
  .tw-bs4 .d-print-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .tw-bs4 .d-print-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}
.tw-bs4 .embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}
.tw-bs4 .embed-responsive::before {
  display: block;
  content: "";
}
.tw-bs4 .embed-responsive .embed-responsive-item, .tw-bs4 .embed-responsive iframe, .tw-bs4 .embed-responsive embed, .tw-bs4 .embed-responsive object, .tw-bs4 .embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
.tw-bs4 .embed-responsive-21by9::before {
  padding-top: 42.85714%;
}
.tw-bs4 .embed-responsive-16by9::before {
  padding-top: 56.25%;
}
.tw-bs4 .embed-responsive-4by3::before {
  padding-top: 75%;
}
.tw-bs4 .embed-responsive-1by1::before {
  padding-top: 100%;
}
.tw-bs4 .flex-row {
  -ms-flex-direction: row !important;
  flex-direction: row !important;
}
.tw-bs4 .flex-column {
  -ms-flex-direction: column !important;
  flex-direction: column !important;
}
.tw-bs4 .flex-row-reverse {
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important;
}
.tw-bs4 .flex-column-reverse {
  -ms-flex-direction: column-reverse !important;
  flex-direction: column-reverse !important;
}
.tw-bs4 .flex-wrap {
  -ms-flex-wrap: wrap !important;
  flex-wrap: wrap !important;
}
.tw-bs4 .flex-nowrap {
  -ms-flex-wrap: nowrap !important;
  flex-wrap: nowrap !important;
}
.tw-bs4 .flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
  flex-wrap: wrap-reverse !important;
}
.tw-bs4 .flex-fill {
  -ms-flex: 1 1 auto !important;
  flex: 1 1 auto !important;
}
.tw-bs4 .flex-grow-0 {
  -ms-flex-positive: 0 !important;
  flex-grow: 0 !important;
}
.tw-bs4 .flex-grow-1 {
  -ms-flex-positive: 1 !important;
  flex-grow: 1 !important;
}
.tw-bs4 .flex-shrink-0 {
  -ms-flex-negative: 0 !important;
  flex-shrink: 0 !important;
}
.tw-bs4 .flex-shrink-1 {
  -ms-flex-negative: 1 !important;
  flex-shrink: 1 !important;
}
.tw-bs4 .justify-content-start {
  -ms-flex-pack: start !important;
  justify-content: flex-start !important;
}
.tw-bs4 .justify-content-end {
  -ms-flex-pack: end !important;
  justify-content: flex-end !important;
}
.tw-bs4 .justify-content-center {
  -ms-flex-pack: center !important;
  justify-content: center !important;
}
.tw-bs4 .justify-content-between {
  -ms-flex-pack: justify !important;
  justify-content: space-between !important;
}
.tw-bs4 .justify-content-around {
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
}
.tw-bs4 .align-items-start {
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}
.tw-bs4 .align-items-end {
  -ms-flex-align: end !important;
  align-items: flex-end !important;
}
.tw-bs4 .align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}
.tw-bs4 .align-items-baseline {
  -ms-flex-align: baseline !important;
  align-items: baseline !important;
}
.tw-bs4 .align-items-stretch {
  -ms-flex-align: stretch !important;
  align-items: stretch !important;
}
.tw-bs4 .align-content-start {
  -ms-flex-line-pack: start !important;
  align-content: flex-start !important;
}
.tw-bs4 .align-content-end {
  -ms-flex-line-pack: end !important;
  align-content: flex-end !important;
}
.tw-bs4 .align-content-center {
  -ms-flex-line-pack: center !important;
  align-content: center !important;
}
.tw-bs4 .align-content-between {
  -ms-flex-line-pack: justify !important;
  align-content: space-between !important;
}
.tw-bs4 .align-content-around {
  -ms-flex-line-pack: distribute !important;
  align-content: space-around !important;
}
.tw-bs4 .align-content-stretch {
  -ms-flex-line-pack: stretch !important;
  align-content: stretch !important;
}
.tw-bs4 .align-self-auto {
  -ms-flex-item-align: auto !important;
  align-self: auto !important;
}
.tw-bs4 .align-self-start {
  -ms-flex-item-align: start !important;
  align-self: flex-start !important;
}
.tw-bs4 .align-self-end {
  -ms-flex-item-align: end !important;
  align-self: flex-end !important;
}
.tw-bs4 .align-self-center {
  -ms-flex-item-align: center !important;
  align-self: center !important;
}
.tw-bs4 .align-self-baseline {
  -ms-flex-item-align: baseline !important;
  align-self: baseline !important;
}
.tw-bs4 .align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important;
}
@media (min-width: 576px) {
  .tw-bs4 .flex-sm-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .tw-bs4 .flex-sm-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .tw-bs4 .flex-sm-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .tw-bs4 .flex-sm-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .tw-bs4 .flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .tw-bs4 .flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .tw-bs4 .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .tw-bs4 .flex-sm-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .tw-bs4 .flex-sm-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .tw-bs4 .flex-sm-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .tw-bs4 .flex-sm-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .tw-bs4 .flex-sm-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .tw-bs4 .justify-content-sm-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .tw-bs4 .justify-content-sm-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .tw-bs4 .justify-content-sm-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .tw-bs4 .justify-content-sm-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .tw-bs4 .justify-content-sm-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .tw-bs4 .align-items-sm-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .tw-bs4 .align-items-sm-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .tw-bs4 .align-items-sm-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .tw-bs4 .align-items-sm-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .tw-bs4 .align-items-sm-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .tw-bs4 .align-content-sm-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .tw-bs4 .align-content-sm-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .tw-bs4 .align-content-sm-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .tw-bs4 .align-content-sm-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .tw-bs4 .align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .tw-bs4 .align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .tw-bs4 .align-self-sm-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .tw-bs4 .align-self-sm-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .tw-bs4 .align-self-sm-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .tw-bs4 .align-self-sm-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .tw-bs4 .align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .tw-bs4 .align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .flex-md-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .tw-bs4 .flex-md-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .tw-bs4 .flex-md-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .tw-bs4 .flex-md-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .tw-bs4 .flex-md-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .tw-bs4 .flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .tw-bs4 .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .tw-bs4 .flex-md-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .tw-bs4 .flex-md-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .tw-bs4 .flex-md-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .tw-bs4 .flex-md-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .tw-bs4 .flex-md-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .tw-bs4 .justify-content-md-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .tw-bs4 .justify-content-md-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .tw-bs4 .justify-content-md-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .tw-bs4 .justify-content-md-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .tw-bs4 .justify-content-md-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .tw-bs4 .align-items-md-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .tw-bs4 .align-items-md-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .tw-bs4 .align-items-md-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .tw-bs4 .align-items-md-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .tw-bs4 .align-items-md-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .tw-bs4 .align-content-md-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .tw-bs4 .align-content-md-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .tw-bs4 .align-content-md-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .tw-bs4 .align-content-md-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .tw-bs4 .align-content-md-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .tw-bs4 .align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .tw-bs4 .align-self-md-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .tw-bs4 .align-self-md-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .tw-bs4 .align-self-md-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .tw-bs4 .align-self-md-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .tw-bs4 .align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .tw-bs4 .align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .flex-lg-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .tw-bs4 .flex-lg-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .tw-bs4 .flex-lg-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .tw-bs4 .flex-lg-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .tw-bs4 .flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .tw-bs4 .flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .tw-bs4 .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .tw-bs4 .flex-lg-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .tw-bs4 .flex-lg-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .tw-bs4 .flex-lg-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .tw-bs4 .flex-lg-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .tw-bs4 .flex-lg-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .tw-bs4 .justify-content-lg-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .tw-bs4 .justify-content-lg-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .tw-bs4 .justify-content-lg-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .tw-bs4 .justify-content-lg-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .tw-bs4 .justify-content-lg-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .tw-bs4 .align-items-lg-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .tw-bs4 .align-items-lg-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .tw-bs4 .align-items-lg-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .tw-bs4 .align-items-lg-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .tw-bs4 .align-items-lg-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .tw-bs4 .align-content-lg-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .tw-bs4 .align-content-lg-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .tw-bs4 .align-content-lg-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .tw-bs4 .align-content-lg-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .tw-bs4 .align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .tw-bs4 .align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .tw-bs4 .align-self-lg-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .tw-bs4 .align-self-lg-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .tw-bs4 .align-self-lg-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .tw-bs4 .align-self-lg-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .tw-bs4 .align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .tw-bs4 .align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .flex-xl-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .tw-bs4 .flex-xl-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .tw-bs4 .flex-xl-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .tw-bs4 .flex-xl-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .tw-bs4 .flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .tw-bs4 .flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .tw-bs4 .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .tw-bs4 .flex-xl-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .tw-bs4 .flex-xl-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .tw-bs4 .flex-xl-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .tw-bs4 .flex-xl-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .tw-bs4 .flex-xl-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .tw-bs4 .justify-content-xl-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .tw-bs4 .justify-content-xl-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .tw-bs4 .justify-content-xl-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .tw-bs4 .justify-content-xl-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .tw-bs4 .justify-content-xl-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .tw-bs4 .align-items-xl-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .tw-bs4 .align-items-xl-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .tw-bs4 .align-items-xl-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .tw-bs4 .align-items-xl-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .tw-bs4 .align-items-xl-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .tw-bs4 .align-content-xl-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .tw-bs4 .align-content-xl-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .tw-bs4 .align-content-xl-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .tw-bs4 .align-content-xl-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .tw-bs4 .align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .tw-bs4 .align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .tw-bs4 .align-self-xl-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .tw-bs4 .align-self-xl-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .tw-bs4 .align-self-xl-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .tw-bs4 .align-self-xl-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .tw-bs4 .align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .tw-bs4 .align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}
.tw-bs4 .float-left {
  float: left !important;
}
.tw-bs4 .float-right {
  float: right !important;
}
.tw-bs4 .float-none {
  float: none !important;
}
@media (min-width: 576px) {
  .tw-bs4 .float-sm-left {
    float: left !important;
  }
  .tw-bs4 .float-sm-right {
    float: right !important;
  }
  .tw-bs4 .float-sm-none {
    float: none !important;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .float-md-left {
    float: left !important;
  }
  .tw-bs4 .float-md-right {
    float: right !important;
  }
  .tw-bs4 .float-md-none {
    float: none !important;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .float-lg-left {
    float: left !important;
  }
  .tw-bs4 .float-lg-right {
    float: right !important;
  }
  .tw-bs4 .float-lg-none {
    float: none !important;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .float-xl-left {
    float: left !important;
  }
  .tw-bs4 .float-xl-right {
    float: right !important;
  }
  .tw-bs4 .float-xl-none {
    float: none !important;
  }
}
.tw-bs4 .overflow-auto {
  overflow: auto !important;
}
.tw-bs4 .overflow-hidden {
  overflow: hidden !important;
}
.tw-bs4 .position-static {
  position: static !important;
}
.tw-bs4 .position-relative {
  position: relative !important;
}
.tw-bs4 .position-absolute {
  position: absolute !important;
}
.tw-bs4 .position-fixed {
  position: fixed !important;
}
.tw-bs4 .position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}
.tw-bs4 .fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}
.tw-bs4 .fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}
@supports ((position: -webkit-sticky) or (position: sticky)) {
  .tw-bs4 .sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
.tw-bs4 .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.tw-bs4 .sr-only-focusable:active, .tw-bs4 .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
.tw-bs4 .shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}
.tw-bs4 .shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.tw-bs4 .shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
.tw-bs4 .shadow-none {
  box-shadow: none !important;
}
.tw-bs4 .w-25 {
  width: 25% !important;
}
.tw-bs4 .w-50 {
  width: 50% !important;
}
.tw-bs4 .w-75 {
  width: 75% !important;
}
.tw-bs4 .w-100 {
  width: 100% !important;
}
.tw-bs4 .w-auto {
  width: auto !important;
}
.tw-bs4 .h-25 {
  height: 25% !important;
}
.tw-bs4 .h-50 {
  height: 50% !important;
}
.tw-bs4 .h-75 {
  height: 75% !important;
}
.tw-bs4 .h-100 {
  height: 100% !important;
}
.tw-bs4 .h-auto {
  height: auto !important;
}
.tw-bs4 .mw-100 {
  max-width: 100% !important;
}
.tw-bs4 .mh-100 {
  max-height: 100% !important;
}
.tw-bs4 .min-vw-100 {
  min-width: 100vw !important;
}
.tw-bs4 .min-vh-100 {
  min-height: 100vh !important;
}
.tw-bs4 .vw-100 {
  width: 100vw !important;
}
.tw-bs4 .vh-100 {
  height: 100vh !important;
}
.tw-bs4 .stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0, 0, 0, 0);
}
.tw-bs4 .m-0 {
  margin: 0 !important;
}
.tw-bs4 .mt-0, .tw-bs4 .my-0 {
  margin-top: 0 !important;
}
.tw-bs4 .mr-0, .tw-bs4 .mx-0 {
  margin-right: 0 !important;
}
.tw-bs4 .mb-0, .tw-bs4 .my-0 {
  margin-bottom: 0 !important;
}
.tw-bs4 .ml-0, .tw-bs4 .mx-0 {
  margin-left: 0 !important;
}
.tw-bs4 .m-1 {
  margin: 0.25rem !important;
}
.tw-bs4 .mt-1, .tw-bs4 .my-1 {
  margin-top: 0.25rem !important;
}
.tw-bs4 .mr-1, .tw-bs4 .mx-1 {
  margin-right: 0.25rem !important;
}
.tw-bs4 .mb-1, .tw-bs4 .my-1 {
  margin-bottom: 0.25rem !important;
}
.tw-bs4 .ml-1, .tw-bs4 .mx-1 {
  margin-left: 0.25rem !important;
}
.tw-bs4 .m-2 {
  margin: 0.5rem !important;
}
.tw-bs4 .mt-2, .tw-bs4 .my-2 {
  margin-top: 0.5rem !important;
}
.tw-bs4 .mr-2, .tw-bs4 .mx-2 {
  margin-right: 0.5rem !important;
}
.tw-bs4 .mb-2, .tw-bs4 .my-2 {
  margin-bottom: 0.5rem !important;
}
.tw-bs4 .ml-2, .tw-bs4 .mx-2 {
  margin-left: 0.5rem !important;
}
.tw-bs4 .m-3 {
  margin: 1rem !important;
}
.tw-bs4 .mt-3, .tw-bs4 .my-3 {
  margin-top: 1rem !important;
}
.tw-bs4 .mr-3, .tw-bs4 .mx-3 {
  margin-right: 1rem !important;
}
.tw-bs4 .mb-3, .tw-bs4 .my-3 {
  margin-bottom: 1rem !important;
}
.tw-bs4 .ml-3, .tw-bs4 .mx-3 {
  margin-left: 1rem !important;
}
.tw-bs4 .m-4 {
  margin: 1.5rem !important;
}
.tw-bs4 .mt-4, .tw-bs4 .my-4 {
  margin-top: 1.5rem !important;
}
.tw-bs4 .mr-4, .tw-bs4 .mx-4 {
  margin-right: 1.5rem !important;
}
.tw-bs4 .mb-4, .tw-bs4 .my-4 {
  margin-bottom: 1.5rem !important;
}
.tw-bs4 .ml-4, .tw-bs4 .mx-4 {
  margin-left: 1.5rem !important;
}
.tw-bs4 .m-5 {
  margin: 3rem !important;
}
.tw-bs4 .mt-5, .tw-bs4 .my-5 {
  margin-top: 3rem !important;
}
.tw-bs4 .mr-5, .tw-bs4 .mx-5 {
  margin-right: 3rem !important;
}
.tw-bs4 .mb-5, .tw-bs4 .my-5 {
  margin-bottom: 3rem !important;
}
.tw-bs4 .ml-5, .tw-bs4 .mx-5 {
  margin-left: 3rem !important;
}
.tw-bs4 .p-0 {
  padding: 0 !important;
}
.tw-bs4 .pt-0, .tw-bs4 .py-0 {
  padding-top: 0 !important;
}
.tw-bs4 .pr-0, .tw-bs4 .px-0 {
  padding-right: 0 !important;
}
.tw-bs4 .pb-0, .tw-bs4 .py-0 {
  padding-bottom: 0 !important;
}
.tw-bs4 .pl-0, .tw-bs4 .px-0 {
  padding-left: 0 !important;
}
.tw-bs4 .p-1 {
  padding: 0.25rem !important;
}
.tw-bs4 .pt-1, .tw-bs4 .py-1 {
  padding-top: 0.25rem !important;
}
.tw-bs4 .pr-1, .tw-bs4 .px-1 {
  padding-right: 0.25rem !important;
}
.tw-bs4 .pb-1, .tw-bs4 .py-1 {
  padding-bottom: 0.25rem !important;
}
.tw-bs4 .pl-1, .tw-bs4 .px-1 {
  padding-left: 0.25rem !important;
}
.tw-bs4 .p-2 {
  padding: 0.5rem !important;
}
.tw-bs4 .pt-2, .tw-bs4 .py-2 {
  padding-top: 0.5rem !important;
}
.tw-bs4 .pr-2, .tw-bs4 .px-2 {
  padding-right: 0.5rem !important;
}
.tw-bs4 .pb-2, .tw-bs4 .py-2 {
  padding-bottom: 0.5rem !important;
}
.tw-bs4 .pl-2, .tw-bs4 .px-2 {
  padding-left: 0.5rem !important;
}
.tw-bs4 .p-3 {
  padding: 1rem !important;
}
.tw-bs4 .pt-3, .tw-bs4 .py-3 {
  padding-top: 1rem !important;
}
.tw-bs4 .pr-3, .tw-bs4 .px-3 {
  padding-right: 1rem !important;
}
.tw-bs4 .pb-3, .tw-bs4 .py-3 {
  padding-bottom: 1rem !important;
}
.tw-bs4 .pl-3, .tw-bs4 .px-3 {
  padding-left: 1rem !important;
}
.tw-bs4 .p-4 {
  padding: 1.5rem !important;
}
.tw-bs4 .pt-4, .tw-bs4 .py-4 {
  padding-top: 1.5rem !important;
}
.tw-bs4 .pr-4, .tw-bs4 .px-4 {
  padding-right: 1.5rem !important;
}
.tw-bs4 .pb-4, .tw-bs4 .py-4 {
  padding-bottom: 1.5rem !important;
}
.tw-bs4 .pl-4, .tw-bs4 .px-4 {
  padding-left: 1.5rem !important;
}
.tw-bs4 .p-5 {
  padding: 3rem !important;
}
.tw-bs4 .pt-5, .tw-bs4 .py-5 {
  padding-top: 3rem !important;
}
.tw-bs4 .pr-5, .tw-bs4 .px-5 {
  padding-right: 3rem !important;
}
.tw-bs4 .pb-5, .tw-bs4 .py-5 {
  padding-bottom: 3rem !important;
}
.tw-bs4 .pl-5, .tw-bs4 .px-5 {
  padding-left: 3rem !important;
}
.tw-bs4 .m-n1 {
  margin: -0.25rem !important;
}
.tw-bs4 .mt-n1, .tw-bs4 .my-n1 {
  margin-top: -0.25rem !important;
}
.tw-bs4 .mr-n1, .tw-bs4 .mx-n1 {
  margin-right: -0.25rem !important;
}
.tw-bs4 .mb-n1, .tw-bs4 .my-n1 {
  margin-bottom: -0.25rem !important;
}
.tw-bs4 .ml-n1, .tw-bs4 .mx-n1 {
  margin-left: -0.25rem !important;
}
.tw-bs4 .m-n2 {
  margin: -0.5rem !important;
}
.tw-bs4 .mt-n2, .tw-bs4 .my-n2 {
  margin-top: -0.5rem !important;
}
.tw-bs4 .mr-n2, .tw-bs4 .mx-n2 {
  margin-right: -0.5rem !important;
}
.tw-bs4 .mb-n2, .tw-bs4 .my-n2 {
  margin-bottom: -0.5rem !important;
}
.tw-bs4 .ml-n2, .tw-bs4 .mx-n2 {
  margin-left: -0.5rem !important;
}
.tw-bs4 .m-n3 {
  margin: -1rem !important;
}
.tw-bs4 .mt-n3, .tw-bs4 .my-n3 {
  margin-top: -1rem !important;
}
.tw-bs4 .mr-n3, .tw-bs4 .mx-n3 {
  margin-right: -1rem !important;
}
.tw-bs4 .mb-n3, .tw-bs4 .my-n3 {
  margin-bottom: -1rem !important;
}
.tw-bs4 .ml-n3, .tw-bs4 .mx-n3 {
  margin-left: -1rem !important;
}
.tw-bs4 .m-n4 {
  margin: -1.5rem !important;
}
.tw-bs4 .mt-n4, .tw-bs4 .my-n4 {
  margin-top: -1.5rem !important;
}
.tw-bs4 .mr-n4, .tw-bs4 .mx-n4 {
  margin-right: -1.5rem !important;
}
.tw-bs4 .mb-n4, .tw-bs4 .my-n4 {
  margin-bottom: -1.5rem !important;
}
.tw-bs4 .ml-n4, .tw-bs4 .mx-n4 {
  margin-left: -1.5rem !important;
}
.tw-bs4 .m-n5 {
  margin: -3rem !important;
}
.tw-bs4 .mt-n5, .tw-bs4 .my-n5 {
  margin-top: -3rem !important;
}
.tw-bs4 .mr-n5, .tw-bs4 .mx-n5 {
  margin-right: -3rem !important;
}
.tw-bs4 .mb-n5, .tw-bs4 .my-n5 {
  margin-bottom: -3rem !important;
}
.tw-bs4 .ml-n5, .tw-bs4 .mx-n5 {
  margin-left: -3rem !important;
}
.tw-bs4 .m-auto {
  margin: auto !important;
}
.tw-bs4 .mt-auto, .tw-bs4 .my-auto {
  margin-top: auto !important;
}
.tw-bs4 .mr-auto, .tw-bs4 .mx-auto {
  margin-right: auto !important;
}
.tw-bs4 .mb-auto, .tw-bs4 .my-auto {
  margin-bottom: auto !important;
}
.tw-bs4 .ml-auto, .tw-bs4 .mx-auto {
  margin-left: auto !important;
}
@media (min-width: 576px) {
  .tw-bs4 .m-sm-0 {
    margin: 0 !important;
  }
  .tw-bs4 .mt-sm-0, .tw-bs4 .my-sm-0 {
    margin-top: 0 !important;
  }
  .tw-bs4 .mr-sm-0, .tw-bs4 .mx-sm-0 {
    margin-right: 0 !important;
  }
  .tw-bs4 .mb-sm-0, .tw-bs4 .my-sm-0 {
    margin-bottom: 0 !important;
  }
  .tw-bs4 .ml-sm-0, .tw-bs4 .mx-sm-0 {
    margin-left: 0 !important;
  }
  .tw-bs4 .m-sm-1 {
    margin: 0.25rem !important;
  }
  .tw-bs4 .mt-sm-1, .tw-bs4 .my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .tw-bs4 .mr-sm-1, .tw-bs4 .mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .tw-bs4 .mb-sm-1, .tw-bs4 .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .tw-bs4 .ml-sm-1, .tw-bs4 .mx-sm-1 {
    margin-left: 0.25rem !important;
  }
  .tw-bs4 .m-sm-2 {
    margin: 0.5rem !important;
  }
  .tw-bs4 .mt-sm-2, .tw-bs4 .my-sm-2 {
    margin-top: 0.5rem !important;
  }
  .tw-bs4 .mr-sm-2, .tw-bs4 .mx-sm-2 {
    margin-right: 0.5rem !important;
  }
  .tw-bs4 .mb-sm-2, .tw-bs4 .my-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .tw-bs4 .ml-sm-2, .tw-bs4 .mx-sm-2 {
    margin-left: 0.5rem !important;
  }
  .tw-bs4 .m-sm-3 {
    margin: 1rem !important;
  }
  .tw-bs4 .mt-sm-3, .tw-bs4 .my-sm-3 {
    margin-top: 1rem !important;
  }
  .tw-bs4 .mr-sm-3, .tw-bs4 .mx-sm-3 {
    margin-right: 1rem !important;
  }
  .tw-bs4 .mb-sm-3, .tw-bs4 .my-sm-3 {
    margin-bottom: 1rem !important;
  }
  .tw-bs4 .ml-sm-3, .tw-bs4 .mx-sm-3 {
    margin-left: 1rem !important;
  }
  .tw-bs4 .m-sm-4 {
    margin: 1.5rem !important;
  }
  .tw-bs4 .mt-sm-4, .tw-bs4 .my-sm-4 {
    margin-top: 1.5rem !important;
  }
  .tw-bs4 .mr-sm-4, .tw-bs4 .mx-sm-4 {
    margin-right: 1.5rem !important;
  }
  .tw-bs4 .mb-sm-4, .tw-bs4 .my-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .tw-bs4 .ml-sm-4, .tw-bs4 .mx-sm-4 {
    margin-left: 1.5rem !important;
  }
  .tw-bs4 .m-sm-5 {
    margin: 3rem !important;
  }
  .tw-bs4 .mt-sm-5, .tw-bs4 .my-sm-5 {
    margin-top: 3rem !important;
  }
  .tw-bs4 .mr-sm-5, .tw-bs4 .mx-sm-5 {
    margin-right: 3rem !important;
  }
  .tw-bs4 .mb-sm-5, .tw-bs4 .my-sm-5 {
    margin-bottom: 3rem !important;
  }
  .tw-bs4 .ml-sm-5, .tw-bs4 .mx-sm-5 {
    margin-left: 3rem !important;
  }
  .tw-bs4 .p-sm-0 {
    padding: 0 !important;
  }
  .tw-bs4 .pt-sm-0, .tw-bs4 .py-sm-0 {
    padding-top: 0 !important;
  }
  .tw-bs4 .pr-sm-0, .tw-bs4 .px-sm-0 {
    padding-right: 0 !important;
  }
  .tw-bs4 .pb-sm-0, .tw-bs4 .py-sm-0 {
    padding-bottom: 0 !important;
  }
  .tw-bs4 .pl-sm-0, .tw-bs4 .px-sm-0 {
    padding-left: 0 !important;
  }
  .tw-bs4 .p-sm-1 {
    padding: 0.25rem !important;
  }
  .tw-bs4 .pt-sm-1, .tw-bs4 .py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .tw-bs4 .pr-sm-1, .tw-bs4 .px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .tw-bs4 .pb-sm-1, .tw-bs4 .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .tw-bs4 .pl-sm-1, .tw-bs4 .px-sm-1 {
    padding-left: 0.25rem !important;
  }
  .tw-bs4 .p-sm-2 {
    padding: 0.5rem !important;
  }
  .tw-bs4 .pt-sm-2, .tw-bs4 .py-sm-2 {
    padding-top: 0.5rem !important;
  }
  .tw-bs4 .pr-sm-2, .tw-bs4 .px-sm-2 {
    padding-right: 0.5rem !important;
  }
  .tw-bs4 .pb-sm-2, .tw-bs4 .py-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .tw-bs4 .pl-sm-2, .tw-bs4 .px-sm-2 {
    padding-left: 0.5rem !important;
  }
  .tw-bs4 .p-sm-3 {
    padding: 1rem !important;
  }
  .tw-bs4 .pt-sm-3, .tw-bs4 .py-sm-3 {
    padding-top: 1rem !important;
  }
  .tw-bs4 .pr-sm-3, .tw-bs4 .px-sm-3 {
    padding-right: 1rem !important;
  }
  .tw-bs4 .pb-sm-3, .tw-bs4 .py-sm-3 {
    padding-bottom: 1rem !important;
  }
  .tw-bs4 .pl-sm-3, .tw-bs4 .px-sm-3 {
    padding-left: 1rem !important;
  }
  .tw-bs4 .p-sm-4 {
    padding: 1.5rem !important;
  }
  .tw-bs4 .pt-sm-4, .tw-bs4 .py-sm-4 {
    padding-top: 1.5rem !important;
  }
  .tw-bs4 .pr-sm-4, .tw-bs4 .px-sm-4 {
    padding-right: 1.5rem !important;
  }
  .tw-bs4 .pb-sm-4, .tw-bs4 .py-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .tw-bs4 .pl-sm-4, .tw-bs4 .px-sm-4 {
    padding-left: 1.5rem !important;
  }
  .tw-bs4 .p-sm-5 {
    padding: 3rem !important;
  }
  .tw-bs4 .pt-sm-5, .tw-bs4 .py-sm-5 {
    padding-top: 3rem !important;
  }
  .tw-bs4 .pr-sm-5, .tw-bs4 .px-sm-5 {
    padding-right: 3rem !important;
  }
  .tw-bs4 .pb-sm-5, .tw-bs4 .py-sm-5 {
    padding-bottom: 3rem !important;
  }
  .tw-bs4 .pl-sm-5, .tw-bs4 .px-sm-5 {
    padding-left: 3rem !important;
  }
  .tw-bs4 .m-sm-n1 {
    margin: -0.25rem !important;
  }
  .tw-bs4 .mt-sm-n1, .tw-bs4 .my-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .tw-bs4 .mr-sm-n1, .tw-bs4 .mx-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .tw-bs4 .mb-sm-n1, .tw-bs4 .my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .tw-bs4 .ml-sm-n1, .tw-bs4 .mx-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .tw-bs4 .m-sm-n2 {
    margin: -0.5rem !important;
  }
  .tw-bs4 .mt-sm-n2, .tw-bs4 .my-sm-n2 {
    margin-top: -0.5rem !important;
  }
  .tw-bs4 .mr-sm-n2, .tw-bs4 .mx-sm-n2 {
    margin-right: -0.5rem !important;
  }
  .tw-bs4 .mb-sm-n2, .tw-bs4 .my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }
  .tw-bs4 .ml-sm-n2, .tw-bs4 .mx-sm-n2 {
    margin-left: -0.5rem !important;
  }
  .tw-bs4 .m-sm-n3 {
    margin: -1rem !important;
  }
  .tw-bs4 .mt-sm-n3, .tw-bs4 .my-sm-n3 {
    margin-top: -1rem !important;
  }
  .tw-bs4 .mr-sm-n3, .tw-bs4 .mx-sm-n3 {
    margin-right: -1rem !important;
  }
  .tw-bs4 .mb-sm-n3, .tw-bs4 .my-sm-n3 {
    margin-bottom: -1rem !important;
  }
  .tw-bs4 .ml-sm-n3, .tw-bs4 .mx-sm-n3 {
    margin-left: -1rem !important;
  }
  .tw-bs4 .m-sm-n4 {
    margin: -1.5rem !important;
  }
  .tw-bs4 .mt-sm-n4, .tw-bs4 .my-sm-n4 {
    margin-top: -1.5rem !important;
  }
  .tw-bs4 .mr-sm-n4, .tw-bs4 .mx-sm-n4 {
    margin-right: -1.5rem !important;
  }
  .tw-bs4 .mb-sm-n4, .tw-bs4 .my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }
  .tw-bs4 .ml-sm-n4, .tw-bs4 .mx-sm-n4 {
    margin-left: -1.5rem !important;
  }
  .tw-bs4 .m-sm-n5 {
    margin: -3rem !important;
  }
  .tw-bs4 .mt-sm-n5, .tw-bs4 .my-sm-n5 {
    margin-top: -3rem !important;
  }
  .tw-bs4 .mr-sm-n5, .tw-bs4 .mx-sm-n5 {
    margin-right: -3rem !important;
  }
  .tw-bs4 .mb-sm-n5, .tw-bs4 .my-sm-n5 {
    margin-bottom: -3rem !important;
  }
  .tw-bs4 .ml-sm-n5, .tw-bs4 .mx-sm-n5 {
    margin-left: -3rem !important;
  }
  .tw-bs4 .m-sm-auto {
    margin: auto !important;
  }
  .tw-bs4 .mt-sm-auto, .tw-bs4 .my-sm-auto {
    margin-top: auto !important;
  }
  .tw-bs4 .mr-sm-auto, .tw-bs4 .mx-sm-auto {
    margin-right: auto !important;
  }
  .tw-bs4 .mb-sm-auto, .tw-bs4 .my-sm-auto {
    margin-bottom: auto !important;
  }
  .tw-bs4 .ml-sm-auto, .tw-bs4 .mx-sm-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .m-md-0 {
    margin: 0 !important;
  }
  .tw-bs4 .mt-md-0, .tw-bs4 .my-md-0 {
    margin-top: 0 !important;
  }
  .tw-bs4 .mr-md-0, .tw-bs4 .mx-md-0 {
    margin-right: 0 !important;
  }
  .tw-bs4 .mb-md-0, .tw-bs4 .my-md-0 {
    margin-bottom: 0 !important;
  }
  .tw-bs4 .ml-md-0, .tw-bs4 .mx-md-0 {
    margin-left: 0 !important;
  }
  .tw-bs4 .m-md-1 {
    margin: 0.25rem !important;
  }
  .tw-bs4 .mt-md-1, .tw-bs4 .my-md-1 {
    margin-top: 0.25rem !important;
  }
  .tw-bs4 .mr-md-1, .tw-bs4 .mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .tw-bs4 .mb-md-1, .tw-bs4 .my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .tw-bs4 .ml-md-1, .tw-bs4 .mx-md-1 {
    margin-left: 0.25rem !important;
  }
  .tw-bs4 .m-md-2 {
    margin: 0.5rem !important;
  }
  .tw-bs4 .mt-md-2, .tw-bs4 .my-md-2 {
    margin-top: 0.5rem !important;
  }
  .tw-bs4 .mr-md-2, .tw-bs4 .mx-md-2 {
    margin-right: 0.5rem !important;
  }
  .tw-bs4 .mb-md-2, .tw-bs4 .my-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .tw-bs4 .ml-md-2, .tw-bs4 .mx-md-2 {
    margin-left: 0.5rem !important;
  }
  .tw-bs4 .m-md-3 {
    margin: 1rem !important;
  }
  .tw-bs4 .mt-md-3, .tw-bs4 .my-md-3 {
    margin-top: 1rem !important;
  }
  .tw-bs4 .mr-md-3, .tw-bs4 .mx-md-3 {
    margin-right: 1rem !important;
  }
  .tw-bs4 .mb-md-3, .tw-bs4 .my-md-3 {
    margin-bottom: 1rem !important;
  }
  .tw-bs4 .ml-md-3, .tw-bs4 .mx-md-3 {
    margin-left: 1rem !important;
  }
  .tw-bs4 .m-md-4 {
    margin: 1.5rem !important;
  }
  .tw-bs4 .mt-md-4, .tw-bs4 .my-md-4 {
    margin-top: 1.5rem !important;
  }
  .tw-bs4 .mr-md-4, .tw-bs4 .mx-md-4 {
    margin-right: 1.5rem !important;
  }
  .tw-bs4 .mb-md-4, .tw-bs4 .my-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .tw-bs4 .ml-md-4, .tw-bs4 .mx-md-4 {
    margin-left: 1.5rem !important;
  }
  .tw-bs4 .m-md-5 {
    margin: 3rem !important;
  }
  .tw-bs4 .mt-md-5, .tw-bs4 .my-md-5 {
    margin-top: 3rem !important;
  }
  .tw-bs4 .mr-md-5, .tw-bs4 .mx-md-5 {
    margin-right: 3rem !important;
  }
  .tw-bs4 .mb-md-5, .tw-bs4 .my-md-5 {
    margin-bottom: 3rem !important;
  }
  .tw-bs4 .ml-md-5, .tw-bs4 .mx-md-5 {
    margin-left: 3rem !important;
  }
  .tw-bs4 .p-md-0 {
    padding: 0 !important;
  }
  .tw-bs4 .pt-md-0, .tw-bs4 .py-md-0 {
    padding-top: 0 !important;
  }
  .tw-bs4 .pr-md-0, .tw-bs4 .px-md-0 {
    padding-right: 0 !important;
  }
  .tw-bs4 .pb-md-0, .tw-bs4 .py-md-0 {
    padding-bottom: 0 !important;
  }
  .tw-bs4 .pl-md-0, .tw-bs4 .px-md-0 {
    padding-left: 0 !important;
  }
  .tw-bs4 .p-md-1 {
    padding: 0.25rem !important;
  }
  .tw-bs4 .pt-md-1, .tw-bs4 .py-md-1 {
    padding-top: 0.25rem !important;
  }
  .tw-bs4 .pr-md-1, .tw-bs4 .px-md-1 {
    padding-right: 0.25rem !important;
  }
  .tw-bs4 .pb-md-1, .tw-bs4 .py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .tw-bs4 .pl-md-1, .tw-bs4 .px-md-1 {
    padding-left: 0.25rem !important;
  }
  .tw-bs4 .p-md-2 {
    padding: 0.5rem !important;
  }
  .tw-bs4 .pt-md-2, .tw-bs4 .py-md-2 {
    padding-top: 0.5rem !important;
  }
  .tw-bs4 .pr-md-2, .tw-bs4 .px-md-2 {
    padding-right: 0.5rem !important;
  }
  .tw-bs4 .pb-md-2, .tw-bs4 .py-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .tw-bs4 .pl-md-2, .tw-bs4 .px-md-2 {
    padding-left: 0.5rem !important;
  }
  .tw-bs4 .p-md-3 {
    padding: 1rem !important;
  }
  .tw-bs4 .pt-md-3, .tw-bs4 .py-md-3 {
    padding-top: 1rem !important;
  }
  .tw-bs4 .pr-md-3, .tw-bs4 .px-md-3 {
    padding-right: 1rem !important;
  }
  .tw-bs4 .pb-md-3, .tw-bs4 .py-md-3 {
    padding-bottom: 1rem !important;
  }
  .tw-bs4 .pl-md-3, .tw-bs4 .px-md-3 {
    padding-left: 1rem !important;
  }
  .tw-bs4 .p-md-4 {
    padding: 1.5rem !important;
  }
  .tw-bs4 .pt-md-4, .tw-bs4 .py-md-4 {
    padding-top: 1.5rem !important;
  }
  .tw-bs4 .pr-md-4, .tw-bs4 .px-md-4 {
    padding-right: 1.5rem !important;
  }
  .tw-bs4 .pb-md-4, .tw-bs4 .py-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .tw-bs4 .pl-md-4, .tw-bs4 .px-md-4 {
    padding-left: 1.5rem !important;
  }
  .tw-bs4 .p-md-5 {
    padding: 3rem !important;
  }
  .tw-bs4 .pt-md-5, .tw-bs4 .py-md-5 {
    padding-top: 3rem !important;
  }
  .tw-bs4 .pr-md-5, .tw-bs4 .px-md-5 {
    padding-right: 3rem !important;
  }
  .tw-bs4 .pb-md-5, .tw-bs4 .py-md-5 {
    padding-bottom: 3rem !important;
  }
  .tw-bs4 .pl-md-5, .tw-bs4 .px-md-5 {
    padding-left: 3rem !important;
  }
  .tw-bs4 .m-md-n1 {
    margin: -0.25rem !important;
  }
  .tw-bs4 .mt-md-n1, .tw-bs4 .my-md-n1 {
    margin-top: -0.25rem !important;
  }
  .tw-bs4 .mr-md-n1, .tw-bs4 .mx-md-n1 {
    margin-right: -0.25rem !important;
  }
  .tw-bs4 .mb-md-n1, .tw-bs4 .my-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .tw-bs4 .ml-md-n1, .tw-bs4 .mx-md-n1 {
    margin-left: -0.25rem !important;
  }
  .tw-bs4 .m-md-n2 {
    margin: -0.5rem !important;
  }
  .tw-bs4 .mt-md-n2, .tw-bs4 .my-md-n2 {
    margin-top: -0.5rem !important;
  }
  .tw-bs4 .mr-md-n2, .tw-bs4 .mx-md-n2 {
    margin-right: -0.5rem !important;
  }
  .tw-bs4 .mb-md-n2, .tw-bs4 .my-md-n2 {
    margin-bottom: -0.5rem !important;
  }
  .tw-bs4 .ml-md-n2, .tw-bs4 .mx-md-n2 {
    margin-left: -0.5rem !important;
  }
  .tw-bs4 .m-md-n3 {
    margin: -1rem !important;
  }
  .tw-bs4 .mt-md-n3, .tw-bs4 .my-md-n3 {
    margin-top: -1rem !important;
  }
  .tw-bs4 .mr-md-n3, .tw-bs4 .mx-md-n3 {
    margin-right: -1rem !important;
  }
  .tw-bs4 .mb-md-n3, .tw-bs4 .my-md-n3 {
    margin-bottom: -1rem !important;
  }
  .tw-bs4 .ml-md-n3, .tw-bs4 .mx-md-n3 {
    margin-left: -1rem !important;
  }
  .tw-bs4 .m-md-n4 {
    margin: -1.5rem !important;
  }
  .tw-bs4 .mt-md-n4, .tw-bs4 .my-md-n4 {
    margin-top: -1.5rem !important;
  }
  .tw-bs4 .mr-md-n4, .tw-bs4 .mx-md-n4 {
    margin-right: -1.5rem !important;
  }
  .tw-bs4 .mb-md-n4, .tw-bs4 .my-md-n4 {
    margin-bottom: -1.5rem !important;
  }
  .tw-bs4 .ml-md-n4, .tw-bs4 .mx-md-n4 {
    margin-left: -1.5rem !important;
  }
  .tw-bs4 .m-md-n5 {
    margin: -3rem !important;
  }
  .tw-bs4 .mt-md-n5, .tw-bs4 .my-md-n5 {
    margin-top: -3rem !important;
  }
  .tw-bs4 .mr-md-n5, .tw-bs4 .mx-md-n5 {
    margin-right: -3rem !important;
  }
  .tw-bs4 .mb-md-n5, .tw-bs4 .my-md-n5 {
    margin-bottom: -3rem !important;
  }
  .tw-bs4 .ml-md-n5, .tw-bs4 .mx-md-n5 {
    margin-left: -3rem !important;
  }
  .tw-bs4 .m-md-auto {
    margin: auto !important;
  }
  .tw-bs4 .mt-md-auto, .tw-bs4 .my-md-auto {
    margin-top: auto !important;
  }
  .tw-bs4 .mr-md-auto, .tw-bs4 .mx-md-auto {
    margin-right: auto !important;
  }
  .tw-bs4 .mb-md-auto, .tw-bs4 .my-md-auto {
    margin-bottom: auto !important;
  }
  .tw-bs4 .ml-md-auto, .tw-bs4 .mx-md-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .m-lg-0 {
    margin: 0 !important;
  }
  .tw-bs4 .mt-lg-0, .tw-bs4 .my-lg-0 {
    margin-top: 0 !important;
  }
  .tw-bs4 .mr-lg-0, .tw-bs4 .mx-lg-0 {
    margin-right: 0 !important;
  }
  .tw-bs4 .mb-lg-0, .tw-bs4 .my-lg-0 {
    margin-bottom: 0 !important;
  }
  .tw-bs4 .ml-lg-0, .tw-bs4 .mx-lg-0 {
    margin-left: 0 !important;
  }
  .tw-bs4 .m-lg-1 {
    margin: 0.25rem !important;
  }
  .tw-bs4 .mt-lg-1, .tw-bs4 .my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .tw-bs4 .mr-lg-1, .tw-bs4 .mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .tw-bs4 .mb-lg-1, .tw-bs4 .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .tw-bs4 .ml-lg-1, .tw-bs4 .mx-lg-1 {
    margin-left: 0.25rem !important;
  }
  .tw-bs4 .m-lg-2 {
    margin: 0.5rem !important;
  }
  .tw-bs4 .mt-lg-2, .tw-bs4 .my-lg-2 {
    margin-top: 0.5rem !important;
  }
  .tw-bs4 .mr-lg-2, .tw-bs4 .mx-lg-2 {
    margin-right: 0.5rem !important;
  }
  .tw-bs4 .mb-lg-2, .tw-bs4 .my-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .tw-bs4 .ml-lg-2, .tw-bs4 .mx-lg-2 {
    margin-left: 0.5rem !important;
  }
  .tw-bs4 .m-lg-3 {
    margin: 1rem !important;
  }
  .tw-bs4 .mt-lg-3, .tw-bs4 .my-lg-3 {
    margin-top: 1rem !important;
  }
  .tw-bs4 .mr-lg-3, .tw-bs4 .mx-lg-3 {
    margin-right: 1rem !important;
  }
  .tw-bs4 .mb-lg-3, .tw-bs4 .my-lg-3 {
    margin-bottom: 1rem !important;
  }
  .tw-bs4 .ml-lg-3, .tw-bs4 .mx-lg-3 {
    margin-left: 1rem !important;
  }
  .tw-bs4 .m-lg-4 {
    margin: 1.5rem !important;
  }
  .tw-bs4 .mt-lg-4, .tw-bs4 .my-lg-4 {
    margin-top: 1.5rem !important;
  }
  .tw-bs4 .mr-lg-4, .tw-bs4 .mx-lg-4 {
    margin-right: 1.5rem !important;
  }
  .tw-bs4 .mb-lg-4, .tw-bs4 .my-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .tw-bs4 .ml-lg-4, .tw-bs4 .mx-lg-4 {
    margin-left: 1.5rem !important;
  }
  .tw-bs4 .m-lg-5 {
    margin: 3rem !important;
  }
  .tw-bs4 .mt-lg-5, .tw-bs4 .my-lg-5 {
    margin-top: 3rem !important;
  }
  .tw-bs4 .mr-lg-5, .tw-bs4 .mx-lg-5 {
    margin-right: 3rem !important;
  }
  .tw-bs4 .mb-lg-5, .tw-bs4 .my-lg-5 {
    margin-bottom: 3rem !important;
  }
  .tw-bs4 .ml-lg-5, .tw-bs4 .mx-lg-5 {
    margin-left: 3rem !important;
  }
  .tw-bs4 .p-lg-0 {
    padding: 0 !important;
  }
  .tw-bs4 .pt-lg-0, .tw-bs4 .py-lg-0 {
    padding-top: 0 !important;
  }
  .tw-bs4 .pr-lg-0, .tw-bs4 .px-lg-0 {
    padding-right: 0 !important;
  }
  .tw-bs4 .pb-lg-0, .tw-bs4 .py-lg-0 {
    padding-bottom: 0 !important;
  }
  .tw-bs4 .pl-lg-0, .tw-bs4 .px-lg-0 {
    padding-left: 0 !important;
  }
  .tw-bs4 .p-lg-1 {
    padding: 0.25rem !important;
  }
  .tw-bs4 .pt-lg-1, .tw-bs4 .py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .tw-bs4 .pr-lg-1, .tw-bs4 .px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .tw-bs4 .pb-lg-1, .tw-bs4 .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .tw-bs4 .pl-lg-1, .tw-bs4 .px-lg-1 {
    padding-left: 0.25rem !important;
  }
  .tw-bs4 .p-lg-2 {
    padding: 0.5rem !important;
  }
  .tw-bs4 .pt-lg-2, .tw-bs4 .py-lg-2 {
    padding-top: 0.5rem !important;
  }
  .tw-bs4 .pr-lg-2, .tw-bs4 .px-lg-2 {
    padding-right: 0.5rem !important;
  }
  .tw-bs4 .pb-lg-2, .tw-bs4 .py-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .tw-bs4 .pl-lg-2, .tw-bs4 .px-lg-2 {
    padding-left: 0.5rem !important;
  }
  .tw-bs4 .p-lg-3 {
    padding: 1rem !important;
  }
  .tw-bs4 .pt-lg-3, .tw-bs4 .py-lg-3 {
    padding-top: 1rem !important;
  }
  .tw-bs4 .pr-lg-3, .tw-bs4 .px-lg-3 {
    padding-right: 1rem !important;
  }
  .tw-bs4 .pb-lg-3, .tw-bs4 .py-lg-3 {
    padding-bottom: 1rem !important;
  }
  .tw-bs4 .pl-lg-3, .tw-bs4 .px-lg-3 {
    padding-left: 1rem !important;
  }
  .tw-bs4 .p-lg-4 {
    padding: 1.5rem !important;
  }
  .tw-bs4 .pt-lg-4, .tw-bs4 .py-lg-4 {
    padding-top: 1.5rem !important;
  }
  .tw-bs4 .pr-lg-4, .tw-bs4 .px-lg-4 {
    padding-right: 1.5rem !important;
  }
  .tw-bs4 .pb-lg-4, .tw-bs4 .py-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .tw-bs4 .pl-lg-4, .tw-bs4 .px-lg-4 {
    padding-left: 1.5rem !important;
  }
  .tw-bs4 .p-lg-5 {
    padding: 3rem !important;
  }
  .tw-bs4 .pt-lg-5, .tw-bs4 .py-lg-5 {
    padding-top: 3rem !important;
  }
  .tw-bs4 .pr-lg-5, .tw-bs4 .px-lg-5 {
    padding-right: 3rem !important;
  }
  .tw-bs4 .pb-lg-5, .tw-bs4 .py-lg-5 {
    padding-bottom: 3rem !important;
  }
  .tw-bs4 .pl-lg-5, .tw-bs4 .px-lg-5 {
    padding-left: 3rem !important;
  }
  .tw-bs4 .m-lg-n1 {
    margin: -0.25rem !important;
  }
  .tw-bs4 .mt-lg-n1, .tw-bs4 .my-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .tw-bs4 .mr-lg-n1, .tw-bs4 .mx-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .tw-bs4 .mb-lg-n1, .tw-bs4 .my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .tw-bs4 .ml-lg-n1, .tw-bs4 .mx-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .tw-bs4 .m-lg-n2 {
    margin: -0.5rem !important;
  }
  .tw-bs4 .mt-lg-n2, .tw-bs4 .my-lg-n2 {
    margin-top: -0.5rem !important;
  }
  .tw-bs4 .mr-lg-n2, .tw-bs4 .mx-lg-n2 {
    margin-right: -0.5rem !important;
  }
  .tw-bs4 .mb-lg-n2, .tw-bs4 .my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }
  .tw-bs4 .ml-lg-n2, .tw-bs4 .mx-lg-n2 {
    margin-left: -0.5rem !important;
  }
  .tw-bs4 .m-lg-n3 {
    margin: -1rem !important;
  }
  .tw-bs4 .mt-lg-n3, .tw-bs4 .my-lg-n3 {
    margin-top: -1rem !important;
  }
  .tw-bs4 .mr-lg-n3, .tw-bs4 .mx-lg-n3 {
    margin-right: -1rem !important;
  }
  .tw-bs4 .mb-lg-n3, .tw-bs4 .my-lg-n3 {
    margin-bottom: -1rem !important;
  }
  .tw-bs4 .ml-lg-n3, .tw-bs4 .mx-lg-n3 {
    margin-left: -1rem !important;
  }
  .tw-bs4 .m-lg-n4 {
    margin: -1.5rem !important;
  }
  .tw-bs4 .mt-lg-n4, .tw-bs4 .my-lg-n4 {
    margin-top: -1.5rem !important;
  }
  .tw-bs4 .mr-lg-n4, .tw-bs4 .mx-lg-n4 {
    margin-right: -1.5rem !important;
  }
  .tw-bs4 .mb-lg-n4, .tw-bs4 .my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }
  .tw-bs4 .ml-lg-n4, .tw-bs4 .mx-lg-n4 {
    margin-left: -1.5rem !important;
  }
  .tw-bs4 .m-lg-n5 {
    margin: -3rem !important;
  }
  .tw-bs4 .mt-lg-n5, .tw-bs4 .my-lg-n5 {
    margin-top: -3rem !important;
  }
  .tw-bs4 .mr-lg-n5, .tw-bs4 .mx-lg-n5 {
    margin-right: -3rem !important;
  }
  .tw-bs4 .mb-lg-n5, .tw-bs4 .my-lg-n5 {
    margin-bottom: -3rem !important;
  }
  .tw-bs4 .ml-lg-n5, .tw-bs4 .mx-lg-n5 {
    margin-left: -3rem !important;
  }
  .tw-bs4 .m-lg-auto {
    margin: auto !important;
  }
  .tw-bs4 .mt-lg-auto, .tw-bs4 .my-lg-auto {
    margin-top: auto !important;
  }
  .tw-bs4 .mr-lg-auto, .tw-bs4 .mx-lg-auto {
    margin-right: auto !important;
  }
  .tw-bs4 .mb-lg-auto, .tw-bs4 .my-lg-auto {
    margin-bottom: auto !important;
  }
  .tw-bs4 .ml-lg-auto, .tw-bs4 .mx-lg-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .m-xl-0 {
    margin: 0 !important;
  }
  .tw-bs4 .mt-xl-0, .tw-bs4 .my-xl-0 {
    margin-top: 0 !important;
  }
  .tw-bs4 .mr-xl-0, .tw-bs4 .mx-xl-0 {
    margin-right: 0 !important;
  }
  .tw-bs4 .mb-xl-0, .tw-bs4 .my-xl-0 {
    margin-bottom: 0 !important;
  }
  .tw-bs4 .ml-xl-0, .tw-bs4 .mx-xl-0 {
    margin-left: 0 !important;
  }
  .tw-bs4 .m-xl-1 {
    margin: 0.25rem !important;
  }
  .tw-bs4 .mt-xl-1, .tw-bs4 .my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .tw-bs4 .mr-xl-1, .tw-bs4 .mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .tw-bs4 .mb-xl-1, .tw-bs4 .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .tw-bs4 .ml-xl-1, .tw-bs4 .mx-xl-1 {
    margin-left: 0.25rem !important;
  }
  .tw-bs4 .m-xl-2 {
    margin: 0.5rem !important;
  }
  .tw-bs4 .mt-xl-2, .tw-bs4 .my-xl-2 {
    margin-top: 0.5rem !important;
  }
  .tw-bs4 .mr-xl-2, .tw-bs4 .mx-xl-2 {
    margin-right: 0.5rem !important;
  }
  .tw-bs4 .mb-xl-2, .tw-bs4 .my-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .tw-bs4 .ml-xl-2, .tw-bs4 .mx-xl-2 {
    margin-left: 0.5rem !important;
  }
  .tw-bs4 .m-xl-3 {
    margin: 1rem !important;
  }
  .tw-bs4 .mt-xl-3, .tw-bs4 .my-xl-3 {
    margin-top: 1rem !important;
  }
  .tw-bs4 .mr-xl-3, .tw-bs4 .mx-xl-3 {
    margin-right: 1rem !important;
  }
  .tw-bs4 .mb-xl-3, .tw-bs4 .my-xl-3 {
    margin-bottom: 1rem !important;
  }
  .tw-bs4 .ml-xl-3, .tw-bs4 .mx-xl-3 {
    margin-left: 1rem !important;
  }
  .tw-bs4 .m-xl-4 {
    margin: 1.5rem !important;
  }
  .tw-bs4 .mt-xl-4, .tw-bs4 .my-xl-4 {
    margin-top: 1.5rem !important;
  }
  .tw-bs4 .mr-xl-4, .tw-bs4 .mx-xl-4 {
    margin-right: 1.5rem !important;
  }
  .tw-bs4 .mb-xl-4, .tw-bs4 .my-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .tw-bs4 .ml-xl-4, .tw-bs4 .mx-xl-4 {
    margin-left: 1.5rem !important;
  }
  .tw-bs4 .m-xl-5 {
    margin: 3rem !important;
  }
  .tw-bs4 .mt-xl-5, .tw-bs4 .my-xl-5 {
    margin-top: 3rem !important;
  }
  .tw-bs4 .mr-xl-5, .tw-bs4 .mx-xl-5 {
    margin-right: 3rem !important;
  }
  .tw-bs4 .mb-xl-5, .tw-bs4 .my-xl-5 {
    margin-bottom: 3rem !important;
  }
  .tw-bs4 .ml-xl-5, .tw-bs4 .mx-xl-5 {
    margin-left: 3rem !important;
  }
  .tw-bs4 .p-xl-0 {
    padding: 0 !important;
  }
  .tw-bs4 .pt-xl-0, .tw-bs4 .py-xl-0 {
    padding-top: 0 !important;
  }
  .tw-bs4 .pr-xl-0, .tw-bs4 .px-xl-0 {
    padding-right: 0 !important;
  }
  .tw-bs4 .pb-xl-0, .tw-bs4 .py-xl-0 {
    padding-bottom: 0 !important;
  }
  .tw-bs4 .pl-xl-0, .tw-bs4 .px-xl-0 {
    padding-left: 0 !important;
  }
  .tw-bs4 .p-xl-1 {
    padding: 0.25rem !important;
  }
  .tw-bs4 .pt-xl-1, .tw-bs4 .py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .tw-bs4 .pr-xl-1, .tw-bs4 .px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .tw-bs4 .pb-xl-1, .tw-bs4 .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .tw-bs4 .pl-xl-1, .tw-bs4 .px-xl-1 {
    padding-left: 0.25rem !important;
  }
  .tw-bs4 .p-xl-2 {
    padding: 0.5rem !important;
  }
  .tw-bs4 .pt-xl-2, .tw-bs4 .py-xl-2 {
    padding-top: 0.5rem !important;
  }
  .tw-bs4 .pr-xl-2, .tw-bs4 .px-xl-2 {
    padding-right: 0.5rem !important;
  }
  .tw-bs4 .pb-xl-2, .tw-bs4 .py-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .tw-bs4 .pl-xl-2, .tw-bs4 .px-xl-2 {
    padding-left: 0.5rem !important;
  }
  .tw-bs4 .p-xl-3 {
    padding: 1rem !important;
  }
  .tw-bs4 .pt-xl-3, .tw-bs4 .py-xl-3 {
    padding-top: 1rem !important;
  }
  .tw-bs4 .pr-xl-3, .tw-bs4 .px-xl-3 {
    padding-right: 1rem !important;
  }
  .tw-bs4 .pb-xl-3, .tw-bs4 .py-xl-3 {
    padding-bottom: 1rem !important;
  }
  .tw-bs4 .pl-xl-3, .tw-bs4 .px-xl-3 {
    padding-left: 1rem !important;
  }
  .tw-bs4 .p-xl-4 {
    padding: 1.5rem !important;
  }
  .tw-bs4 .pt-xl-4, .tw-bs4 .py-xl-4 {
    padding-top: 1.5rem !important;
  }
  .tw-bs4 .pr-xl-4, .tw-bs4 .px-xl-4 {
    padding-right: 1.5rem !important;
  }
  .tw-bs4 .pb-xl-4, .tw-bs4 .py-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .tw-bs4 .pl-xl-4, .tw-bs4 .px-xl-4 {
    padding-left: 1.5rem !important;
  }
  .tw-bs4 .p-xl-5 {
    padding: 3rem !important;
  }
  .tw-bs4 .pt-xl-5, .tw-bs4 .py-xl-5 {
    padding-top: 3rem !important;
  }
  .tw-bs4 .pr-xl-5, .tw-bs4 .px-xl-5 {
    padding-right: 3rem !important;
  }
  .tw-bs4 .pb-xl-5, .tw-bs4 .py-xl-5 {
    padding-bottom: 3rem !important;
  }
  .tw-bs4 .pl-xl-5, .tw-bs4 .px-xl-5 {
    padding-left: 3rem !important;
  }
  .tw-bs4 .m-xl-n1 {
    margin: -0.25rem !important;
  }
  .tw-bs4 .mt-xl-n1, .tw-bs4 .my-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .tw-bs4 .mr-xl-n1, .tw-bs4 .mx-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .tw-bs4 .mb-xl-n1, .tw-bs4 .my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .tw-bs4 .ml-xl-n1, .tw-bs4 .mx-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .tw-bs4 .m-xl-n2 {
    margin: -0.5rem !important;
  }
  .tw-bs4 .mt-xl-n2, .tw-bs4 .my-xl-n2 {
    margin-top: -0.5rem !important;
  }
  .tw-bs4 .mr-xl-n2, .tw-bs4 .mx-xl-n2 {
    margin-right: -0.5rem !important;
  }
  .tw-bs4 .mb-xl-n2, .tw-bs4 .my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .tw-bs4 .ml-xl-n2, .tw-bs4 .mx-xl-n2 {
    margin-left: -0.5rem !important;
  }
  .tw-bs4 .m-xl-n3 {
    margin: -1rem !important;
  }
  .tw-bs4 .mt-xl-n3, .tw-bs4 .my-xl-n3 {
    margin-top: -1rem !important;
  }
  .tw-bs4 .mr-xl-n3, .tw-bs4 .mx-xl-n3 {
    margin-right: -1rem !important;
  }
  .tw-bs4 .mb-xl-n3, .tw-bs4 .my-xl-n3 {
    margin-bottom: -1rem !important;
  }
  .tw-bs4 .ml-xl-n3, .tw-bs4 .mx-xl-n3 {
    margin-left: -1rem !important;
  }
  .tw-bs4 .m-xl-n4 {
    margin: -1.5rem !important;
  }
  .tw-bs4 .mt-xl-n4, .tw-bs4 .my-xl-n4 {
    margin-top: -1.5rem !important;
  }
  .tw-bs4 .mr-xl-n4, .tw-bs4 .mx-xl-n4 {
    margin-right: -1.5rem !important;
  }
  .tw-bs4 .mb-xl-n4, .tw-bs4 .my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .tw-bs4 .ml-xl-n4, .tw-bs4 .mx-xl-n4 {
    margin-left: -1.5rem !important;
  }
  .tw-bs4 .m-xl-n5 {
    margin: -3rem !important;
  }
  .tw-bs4 .mt-xl-n5, .tw-bs4 .my-xl-n5 {
    margin-top: -3rem !important;
  }
  .tw-bs4 .mr-xl-n5, .tw-bs4 .mx-xl-n5 {
    margin-right: -3rem !important;
  }
  .tw-bs4 .mb-xl-n5, .tw-bs4 .my-xl-n5 {
    margin-bottom: -3rem !important;
  }
  .tw-bs4 .ml-xl-n5, .tw-bs4 .mx-xl-n5 {
    margin-left: -3rem !important;
  }
  .tw-bs4 .m-xl-auto {
    margin: auto !important;
  }
  .tw-bs4 .mt-xl-auto, .tw-bs4 .my-xl-auto {
    margin-top: auto !important;
  }
  .tw-bs4 .mr-xl-auto, .tw-bs4 .mx-xl-auto {
    margin-right: auto !important;
  }
  .tw-bs4 .mb-xl-auto, .tw-bs4 .my-xl-auto {
    margin-bottom: auto !important;
  }
  .tw-bs4 .ml-xl-auto, .tw-bs4 .mx-xl-auto {
    margin-left: auto !important;
  }
}
.tw-bs4 .text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}
.tw-bs4 .text-justify {
  text-align: justify !important;
}
.tw-bs4 .text-wrap {
  white-space: normal !important;
}
.tw-bs4 .text-nowrap {
  white-space: nowrap !important;
}
.tw-bs4 .text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tw-bs4 .text-left {
  text-align: left !important;
}
.tw-bs4 .text-right {
  text-align: right !important;
}
.tw-bs4 .text-center {
  text-align: center !important;
}
@media (min-width: 576px) {
  .tw-bs4 .text-sm-left {
    text-align: left !important;
  }
  .tw-bs4 .text-sm-right {
    text-align: right !important;
  }
  .tw-bs4 .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .tw-bs4 .text-md-left {
    text-align: left !important;
  }
  .tw-bs4 .text-md-right {
    text-align: right !important;
  }
  .tw-bs4 .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .tw-bs4 .text-lg-left {
    text-align: left !important;
  }
  .tw-bs4 .text-lg-right {
    text-align: right !important;
  }
  .tw-bs4 .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .tw-bs4 .text-xl-left {
    text-align: left !important;
  }
  .tw-bs4 .text-xl-right {
    text-align: right !important;
  }
  .tw-bs4 .text-xl-center {
    text-align: center !important;
  }
}
.tw-bs4 .text-lowercase {
  text-transform: lowercase !important;
}
.tw-bs4 .text-uppercase {
  text-transform: uppercase !important;
}
.tw-bs4 .text-capitalize {
  text-transform: capitalize !important;
}
.tw-bs4 .font-weight-light {
  font-weight: 300 !important;
}
.tw-bs4 .font-weight-lighter {
  font-weight: lighter !important;
}
.tw-bs4 .font-weight-normal {
  font-weight: 400 !important;
}
.tw-bs4 .font-weight-bold {
  font-weight: 700 !important;
}
.tw-bs4 .font-weight-bolder {
  font-weight: bolder !important;
}
.tw-bs4 .font-italic {
  font-style: italic !important;
}
.tw-bs4 .text-white {
  color: #fff !important;
}
.tw-bs4 .text-primary {
  color: #007bff !important;
}
.tw-bs4 a.text-primary:hover, .tw-bs4 a.text-primary:focus {
  color: #0056b3 !important;
}
.tw-bs4 .text-secondary {
  color: #6c757d !important;
}
.tw-bs4 a.text-secondary:hover, .tw-bs4 a.text-secondary:focus {
  color: #494f54 !important;
}
.tw-bs4 .text-success {
  color: #28a745 !important;
}
.tw-bs4 a.text-success:hover, .tw-bs4 a.text-success:focus {
  color: #19692c !important;
}
.tw-bs4 .text-info {
  color: #17a2b8 !important;
}
.tw-bs4 a.text-info:hover, .tw-bs4 a.text-info:focus {
  color: #0f6674 !important;
}
.tw-bs4 .text-warning {
  color: #ffc107 !important;
}
.tw-bs4 a.text-warning:hover, .tw-bs4 a.text-warning:focus {
  color: #ba8b00 !important;
}
.tw-bs4 .text-danger {
  color: #dc3545 !important;
}
.tw-bs4 a.text-danger:hover, .tw-bs4 a.text-danger:focus {
  color: #a71d2a !important;
}
.tw-bs4 .text-light {
  color: #f8f9fa !important;
}
.tw-bs4 a.text-light:hover, .tw-bs4 a.text-light:focus {
  color: #cbd3da !important;
}
.tw-bs4 .text-dark {
  color: #343a40 !important;
}
.tw-bs4 a.text-dark:hover, .tw-bs4 a.text-dark:focus {
  color: #121416 !important;
}
.tw-bs4 .text-body {
  color: #212529 !important;
}
.tw-bs4 .text-muted {
  color: #6c757d !important;
}
.tw-bs4 .text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}
.tw-bs4 .text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}
.tw-bs4 .text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.tw-bs4 .text-decoration-none {
  text-decoration: none !important;
}
.tw-bs4 .text-break {
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}
.tw-bs4 .text-reset {
  color: inherit !important;
}
.tw-bs4 .visible {
  visibility: visible !important;
}
.tw-bs4 .invisible {
  visibility: hidden !important;
}
@media print {
  .tw-bs4 *, .tw-bs4 *::before, .tw-bs4 *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  .tw-bs4 a:not(.btn) {
    text-decoration: underline;
  }
  .tw-bs4 abbr[title]::after {
    content: " (" attr(title) ")";
  }
  .tw-bs4 pre {
    white-space: pre-wrap !important;
  }
  .tw-bs4 pre, .tw-bs4 blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }
  .tw-bs4 thead {
    display: table-header-group;
  }
  .tw-bs4 tr, .tw-bs4 img {
    page-break-inside: avoid;
  }
  .tw-bs4 p, .tw-bs4 h2, .tw-bs4 h3 {
    orphans: 3;
    widows: 3;
  }
  .tw-bs4 h2, .tw-bs4 h3 {
    page-break-after: avoid;
  }
  @page {
    size: a3;
  }
  .tw-bs4 body {
    min-width: 992px !important;
  }
  .tw-bs4 .container {
    min-width: 992px !important;
  }
  .tw-bs4 .navbar {
    display: none;
  }
  .tw-bs4 .badge {
    border: 1px solid #000;
  }
  .tw-bs4 .table {
    border-collapse: collapse !important;
  }
  .tw-bs4 .table td, .tw-bs4 .table th {
    background-color: #fff !important;
  }
  .tw-bs4 .table-bordered th, .tw-bs4 .table-bordered td {
    border: 1px solid #dee2e6 !important;
  }
  .tw-bs4 .table-dark {
    color: inherit;
  }
  .tw-bs4 .table-dark th, .tw-bs4 .table-dark td, .tw-bs4 .table-dark thead th, .tw-bs4 .table-dark tbody + tbody {
    border-color: #dee2e6;
  }
  .tw-bs4 .table .thead-dark th {
    color: inherit;
    border-color: #dee2e6;
  }
}



