/**
 * @file style-dfi.css
 *
 * Style for dynamic featured image plugin
 *
 * Copyright (c) 2013, Anki<PERSON> <<EMAIL>, http://ankitpokhrel.com.np>
 */

.dfiAddNew {
  float: left;
  margin-top: 3px;
}

.dfiRemove {
    float: right;
}

.dfiLinks{
    padding: 2px 0;
}

.dfiClearFloat {
    clear: both;
}

.featured-meta-box a:not(.hasFeaturedImage):not(.dfiAddNew) {
    display: inline-block !important;
    margin: 3px 0;
}

img.dfiImg {
    max-width: 258px;
}

img.dfiImg[src=""]{
  display: none;
}

img.dfiImgEmpty {
    display: none;
}

.dfiLoading {
  background: url("../img/spinner.gif") no-repeat;
  display: inline-block;
  height: 20px;
  position: relative;
  top: 2px;
  width: 20px;
}

.dfiFeaturedImage {
  border: 2px dashed #AAA;
  color: #CCC;
  height: 130px;
  margin: 10px 0 !important;
  padding: 10px;
  text-align: center;
  width: 235px;
}

.dfiFeaturedImage:hover{
    border-color: #999;
}

.dfiFeaturedImage > span {
  display: inline-block;
  font-size: 54px;
  margin-top: 17%;
  margin-right: 11%;
}

.hasFeaturedImage {
  border: none;
  width: auto;
  height: auto;
  margin: 0 !important;
  position: absolute;
  top: 30%;
  left: 37%;
  display: none;
  z-index: 999;
}

.dashicons {
  text-decoration: none !important;
}

.dashicons:hover, .dfiFeaturedImage:hover {
  color: #2ea2cc !important;
}
