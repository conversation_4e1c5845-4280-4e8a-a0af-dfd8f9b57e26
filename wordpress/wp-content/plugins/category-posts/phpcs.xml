<?xml version="1.0"?>
<ruleset name="CategoryPostsWidget Plugin">
    <description>A custom set of rules to check for a WPized WordPress project</description>

	<rule ref="WordPress">
		<exclude name="WordPress.Files.FileName.InvalidClassFileName" />
		<exclude name="WordPress.NamingConventions" />
		<exclude name="WordPress.VIP.RestrictedFunctions" />
		<exclude name="WordPress.VIP.SlowDBQuery.slow_db_query_meta_query" />
		<exclude name="WordPress.WP.CapitalPDangit" />
		<exclude name="WordPress.WP.I18n.Missing.Translators.Comment" />
		<exclude name="Generic.Formatting.MultipleStatementAlignment.NotSameWarning" />
	</rule>

	<rule ref="WordPress.WP.DeprecatedClasses">
        <properties>
            <property name="minimum_supported_version" value="4.4"/>
        </properties>
    </rule>

    <rule ref="WordPress.WP.DeprecatedFunctions">
        <properties>
            <property name="minimum_supported_version" value="4.4"/>
        </properties>
    </rule>

    <rule ref="WordPress.WP.DeprecatedParameters">
        <properties>
            <property name="minimum_supported_version" value="4.4"/>
        </properties>
    </rule>

</ruleset>
