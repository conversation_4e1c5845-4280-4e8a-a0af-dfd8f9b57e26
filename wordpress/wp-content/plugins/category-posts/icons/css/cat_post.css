@font-face {
  font-family: 'cat_post';
  src: url('../font/cat_post.eot?21642608');
  src: url('../font/cat_post.eot?21642608#iefix') format('embedded-opentype'),
       url('../font/cat_post.woff2?21642608') format('woff2'),
       url('../font/cat_post.woff?21642608') format('woff'),
       url('../font/cat_post.ttf?21642608') format('truetype'),
       url('../font/cat_post.svg?21642608#cat_post') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'cat_post';
    src: url('../font/cat_post.svg?21642608#cat_post') format('svg');
  }
}
*/
 
 [class^="cat-post-icon-"]:before, [class*=" cat-post-icon-"]:before {
  font-family: "cat_post";
  font-style: normal;
  font-weight: normal;
  speak: none;
 
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */
 
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
 
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
 
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
 
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
 
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
 
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}
 
.cat-post-icon-image:before { content: '\e800'; } /* '' */
.cat-post-icon-video:before { content: '\e801'; } /* '' */
.cat-post-icon-chat:before { content: '\e802'; } /* '' */
.cat-post-icon-audio:before { content: '\e803'; } /* '' */
.cat-post-icon-gallery:before { content: '\e805'; } /* '' */
.cat-post-icon-standard:before { content: '\e806'; } /* '' */
.cat-post-icon-pin:before { content: '\e807'; } /* '' */
.cat-post-icon-link:before { content: '\e809'; } /* '' */
.cat-post-icon-status:before { content: '\e80a'; } /* '' */
.cat-post-icon-aside:before { content: '\f0f6'; } /* '' */
.cat-post-icon-quote:before { content: '\f10d'; } /* '' */