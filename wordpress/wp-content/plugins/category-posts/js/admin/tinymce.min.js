!function(t){var e="categoryPosts",o="category-posts";tinymce.create("tinymce.plugins."+e,{init:function(n,t){n.addButton(e,{title:n.getLang(o+".tooltip"),cmd:"categoryPosts_shortcode",text:"+[CP]"}),n.addCommand("categoryPosts_shortcode",function(){n.windowManager.open({title:n.getLang(o+".title"),body:[{type:"textbox",name:"title",label:n.getLang(o+".name")},{type:"container",html:'<a style="color:blue;textdecoration:underline;cursor:pointer" href="'+n.getLang(o+".profiile_url")+'">'+n.getLang(o+".hide_message")+"</a>"}],onsubmit:function(t){var e="[catposts";""!=t.data.title&&(e+=' name="'+t.data.title+'"'),e+="]",n.selection.setContent(e)}})})},createControl:function(t,e){return null},getInfo:function(){return{longname:"Insert category post shortcode",author:"TipTopPress",version:"4.7"}}}),tinymce.PluginManager.add("categoryPosts",tinymce.plugins.categoryPosts)}(jQuery);