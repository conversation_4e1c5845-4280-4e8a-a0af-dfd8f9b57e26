var cwp_namespace={php_settings_var:"categoryPosts",widget_class:".category-widget-cont",template_panel_prefix:".categoryposts-data-panel-",open_panels:{},template_change_timer:null,clickHandler:function(e){jQuery(e).toggleClass("open").next().stop().slideToggle();var t=jQuery(e).data("panel"),a=jQuery(e).parent().parent().parent().parent().parent().attr("id"),n={};this.open_panels.hasOwnProperty(a)&&(n=this.open_panels[a]),n.hasOwnProperty(t)?delete n[t]:n[t]=!0,this.open_panels[a]=n},toggleCatSelection:function(e){var t=jQuery(e).find("option:selected").attr("value"),a=e.parentElement.parentElement.parentElement.parentElement;"0"==t?(jQuery(a).find(".categoryPosts-title_link").hide(),jQuery(a).find(".categoryPosts-title_link_url").show(),jQuery(a).find(".categoryPosts-no_cat_childs").hide()):(jQuery(a).find(".categoryPosts-title_link").show(),jQuery(a).find(".categoryPosts-title_link_url").hide(),jQuery(a).find(".categoryPosts-no_cat_childs").show())},toggleDisableFontStyles:function(e){var t=jQuery(e).find("input").attr("checked"),a=e.parentElement.parentElement;"checked"==t?jQuery(a).find(".categoryposts-data-panel-general-disable-font-styles").hide():jQuery(a).find(".categoryposts-data-panel-general-disable-font-styles").show()},toggleDateFormat:function(e){var t=jQuery(e).val(),a=e.parentElement.parentElement;"other"!=t?jQuery(a).find(".categoryPosts-date_format").hide():jQuery(a).find(".categoryPosts-date_format").show()},toggleDateRange:function(e){var t=jQuery(e).val(),a=e.parentElement.parentElement;switch(jQuery(a).find(".categoryPosts-date-range p").hide(),jQuery(a).find(".categoryPosts-date-range").show(),t){case"off":jQuery(a).find(".categoryPosts-date-range").hide();break;case"days_ago":jQuery(a).find(".categoryPosts-days_ago").show();break;case"between_dates":jQuery(a).find(".categoryPosts-start_date").show(),jQuery(a).find(".categoryPosts-end_date").show()}},toggleNoMatch:function(e){var t=jQuery(e).val(),a=e.parentElement.parentElement;"text"==t?jQuery(a).find(".categoryPosts-no-match-text").show():jQuery(a).find(".categoryPosts-no-match-text").hide()},toggleTemplateHelp:function(e,t){t.preventDefault();var a=e.parentElement.parentElement.parentElement.parentElement;jQuery(a).find(".cat-post-template-help").toggle("slow")},toggleAssignedCategoriesTop:function(e){var t=jQuery(e).find("input").attr("checked"),a=e.parentElement.parentElement;"checked"==t?jQuery(a).find(".categoryposts-details-panel-assigned-cat-top").show():jQuery(a).find(".categoryposts-details-panel-assigned-cat-top").hide()},toggleHideTitle:function(e){var t=jQuery(e).attr("checked"),a=e.parentElement.parentElement.parentElement;"checked"!=t?jQuery(a).find(".categoryposts-data-panel-title-settings").show():jQuery(a).find(".categoryposts-data-panel-title-settings").hide()},toggleLoadMore:function(e){var t=jQuery(e).attr("checked"),a=e.parentElement.parentElement.parentElement;"checked"!=t?jQuery(a).find(".loadmore-settings").hide():jQuery(a).find(".loadmore-settings").show()},selectPremadeTemplate:function(e){var t=e.parentElement.parentElement.parentElement,a=e.parentElement.parentElement,n=jQuery(a).find("select"),o="%title%";switch(value=n.val(),value){case"title":o="%title%";break;case"title_excerpt":o="%title%\n\n%excerpt%";break;case"title_thumb":o="%title%\n\n%thumb%";break;case"title_thum_excerpt":o="%title%\n\n%thumb%\n%excerpt%";break;case"everything":o="%title%\n\n",o+="%date%\n\n",o+="%thumb%\n",o+='<span class="dashicons dashicons-admin-comments"></span> %commentnum% ',o+='<span class="dashicons dashicons-admin-users"></span> %author%\n',o+="%excerpt%",o+="Categories: %category% ",o+='<span class="dashicons dashicons-tag"></span> %post_tag%'}var l=jQuery(t).find("textarea");l.val(o),l.trigger("input","change")},autoCloseOpenPanels:function(e){if(tiptoppress[this.php_settings_var].accordion&&!jQuery(e).hasClass("open")){var t=jQuery(e).parent().find(".open");this.clickHandler(t)}},defaultThumbnailSelection:function(e,t,a){var n=wp.media({title:t,multiple:!1,library:{type:"image"},button:{text:a}});return n.on("close",function(){var t=n.state().get("selection").toJSON();if(1==t.length){var a=t[0],o='<img src="'+a.url+'" ';o+='width="60" ',o+='height="60" ',o+="/>",jQuery(e).parent().prev().find(".default_thumb_img").html(o),jQuery(e).parent().find(".cwp_default_thumb_remove").show(),jQuery(e).parent().prev().find(".default_thumb_id").val(a.id).change()}}),n.open(),!1},removeDefaultThumbnailSelection:function(e){return jQuery(e).parent().prev().find(".default_thumb_img").html(cwp_default_thumb_selection.none),jQuery(e).hide(),jQuery(e).parent().prev().find(".default_thumb_id").val(0).change(),!1},templateChange:function(e){function t(){var t=jQuery(e).val(),a=tiptoppress[this.php_settings_var].template_tags,n=jQuery(e.parentElement.parentElement.parentElement.parentElement);for(var o in a)-1!==t.indexOf(a[o])?n.find(this.template_panel_prefix+a[o]).show():n.find(this.template_panel_prefix+a[o]).hide()}null!=this.template_change_timer&&clearTimeout(this.template_change_timer),this.template_change_timer=setTimeout(t.bind(this),250)},thumbnailSizeChange:function(e){var t,a,n=jQuery(e),o=n.closest(".categoryposts-data-panel-thumb").find(".thumb_h"),l=n.closest(".categoryposts-data-panel-thumb").find(".thumb_w");return n.hasClass("smaller")?(a=l.val()/1.015,t=o.val()/1.015):n.hasClass("quarter")?(a=l.val()/4,t=o.val()/4):n.hasClass("half")?(t=o.val()/2,a=l.val()/2):n.hasClass("double")?(t=2*o.val(),a=2*l.val()):n.hasClass("bigger")?(a=1.02*l.val(),t=1.02*o.val()):n.hasClass("square")?l.val()>=o.val()?(a=o.val(),t=o.val()):(a=l.val(),t=l.val()):n.hasClass("standard")?l.val()>=o.val()?(a=4*o.val()/3,t=o.val()):(a=o.val()/4*3,t=o.val()):n.hasClass("wide")?l.val()>=o.val()?(a=16*o.val()/9,t=o.val()):(a=o.val()/16*9,t=o.val()):n.hasClass("switch")?(t=l.val(),a=o.val()):(a=n.data("thumb-w"),t=n.data("thumb-h")),l.val(Math.floor(a)),o.val(Math.floor(t)),l.trigger("input","change"),o.trigger("input","change"),!1},thumbnailFluidWidthChange:function(e){var t=jQuery(e),a=t.closest(".categoryposts-data-panel-thumb").find(".thumb_h");return t.closest("label").find("span").html(t.val()+"%"),a.val(0),a.trigger("input","change"),!1},openAddPlaceholder:function(e){var t=jQuery(e);return t.closest(".cat-post-add_premade_templates").find(".cpwp-placeholder-dropdown-menu").toggle(),t.closest(".cat-post-add_premade_templates").find(".cpwp-placeholder-dropdown-menu span").off("click").on("click",function(){var e=jQuery(this).data("value");switch(e){case"NewLine":e="\n";break;case"EmptyLine":e="\n\n";break;default:e="%"+e+"%"}var t=this.parentElement.parentElement.parentElement,a=jQuery(t).find("textarea"),n=a[0].selectionStart,o=a.val();a.val(o.substring(0,n)+e+o.substring(n)),a[0].selectionStart=n+e.length,a[0].selectionEnd=n+e.length,a.focus(),a.trigger("input","change")}),t.closest(".cat-post-add_premade_templates").find(".cpwp-placeholder-dropdown-menu").on("mouseenter",function(){jQuery(this).addClass("cpw-doNotClose")}),t.closest(".cat-post-add_premade_templates").find(".cpwp-placeholder-dropdown-menu").on("mouseleave",function(){jQuery(this).removeClass("cpw-doNotClose")}),!1},selectPlaceholderHelper:function(e){var t=jQuery(e),a=t[0].selectionStart,n=t.val(),o=n.substring(0,a).lastIndexOf("%"),l=a+n.substring(a).indexOf("%")+1,s=n.substring(o,l);return!(s.indexOf("\n")>=0||s.indexOf(" ")>=0||s.length<=2)&&(t[0].selectionStart=o,t[0].selectionEnd=l,!1)}};jQuery(document).ready(function(){function e(){jQuery(".cwp_default_thumb_select").off("click").on("click",function(){cwp_namespace.defaultThumbnailSelection(this,cwp_default_thumb_selection.frame_title,cwp_default_thumb_selection.button_title)}),jQuery(document).on("change",t+" .categoryposts-data-panel-filter-cat",function(){cwp_namespace.toggleCatSelection(this)}),jQuery(".cwp_default_thumb_remove").off("click").on("click",function(){cwp_namespace.removeDefaultThumbnailSelection(this)}),jQuery(t+"-assigned_categories").off("click").on("click",function(){cwp_namespace.toggleAssignedCategoriesTop(this)}),jQuery(document).on("click",t+" .categoryPosts-hide_title input",function(){cwp_namespace.toggleHideTitle(this)}),jQuery(document).on("click",t+" .categoryPosts-enable_loadmore input",function(){cwp_namespace.toggleLoadMore(this)}),jQuery(document).on("change",t+" .categoryPosts-preset_date_format select",function(){cwp_namespace.toggleDateFormat(this)}),jQuery(document).on("change",t+" .categoryPosts-date_range select",function(){cwp_namespace.toggleDateRange(this)}),jQuery(document).on("change",t+" .categoryPosts-no_match_handling select",function(){cwp_namespace.toggleNoMatch(this)}),jQuery(t+" a.toggle-template-help").off("click").on("click",function(e){cwp_namespace.toggleTemplateHelp(this,e)}),jQuery(document).on("click",t+" .cat-post-premade_templates button",function(){cwp_namespace.selectPremadeTemplate(this)}),jQuery(document).on("change",t+" .cat-post-premade_templates select",function(e){e.preventDefault(),e.stopPropagation()}),jQuery(document).on("input",t+" .categoryPosts-template textarea",function(){cwp_namespace.templateChange(this)}),jQuery(document).on("click",t+" .cat-post-thumb-change-size button",function(){cwp_namespace.thumbnailSizeChange(this)}),jQuery(document).on("change",t+" .thumb_fluid_width",function(){cwp_namespace.thumbnailFluidWidthChange(this)}),jQuery(t+" .cpwp-open-placholder-dropdown-menu").off("click").on("click",function(){cwp_namespace.openAddPlaceholder(this)}),jQuery(document).on("onfocusout, blur",t+" .cpwp-open-placholder-dropdown-menu,"+t+" .categoryPosts-template textarea",function(){jQuery(this).closest(t+" .categoryPosts-template").parent().find(".cpwp-placeholder-dropdown-menu").not(".cpw-doNotClose").hide()}),jQuery(document).on("mousedown",t+" .categoryPosts-template textarea",function(){var e=this;setTimeout(function(){cwp_namespace.selectPlaceholderHelper(e)},0)})}var t=".category-widget-cont";jQuery(".category-widget-cont h4").click(function(){cwp_namespace.autoCloseOpenPanels(this),cwp_namespace.clickHandler(this)}),jQuery(document).on("widget-added widget-updated panelsopen",function(t,a){jQuery(".category-widget-cont h4").off("click").on("click",function(){cwp_namespace.autoCloseOpenPanels(this),cwp_namespace.clickHandler(this)}),jQuery(".cwp_default_thumb_select").off("click").on("click",function(){cwp_namespace.defaultThumbnailSelection(this,cwp_default_thumb_selection.frame_title,cwp_default_thumb_selection.button_title)}),jQuery(".cwp_default_thumb_remove").off("click").on("click",function(){cwp_namespace.removeDefaultThumbnailSelection(this)});var n=jQuery(a).attr("id");if(cwp_namespace.open_panels.hasOwnProperty(n)){var o=cwp_namespace.open_panels[n];for(var l in o)jQuery(a).find("[data-panel="+l+"]").toggleClass("open").next().stop().show()}e()}),e()});
