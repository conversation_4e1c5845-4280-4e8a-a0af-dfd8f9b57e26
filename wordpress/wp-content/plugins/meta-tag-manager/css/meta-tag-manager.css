/* .mtm.postbox { background-color:#dedede;} */
.mtm-menu-builder {
  margin-bottom: 15px;
}
.mtm-field-template {
  display: none;
  visibility: hidden;
}
.mtm-field-placeholder {
  width: 100%;
  border: 1px dashed #aaa;
  background-color: #ddd;
  font-size: 18px;
  padding: 30px 0px;
  color: #555;
  text-align: center;
  margin: 10px 0px;
  cursor: pointer;
}
.no-reference.no-context .mtm-field-placeholder {
  padding: 20px 0px;
}
.mtm-field {
  display: block;
  clear: both;
  width: 100%;
  border: 1px solid #c3c3c3;
  background-color: #fff;
  margin: 10px 0px;
}
.mtm-field.closed .mtm-field-data {
  display: none;
  visibility: hidden;
}
.mtm-field .mtm-field-actions {
  clear: both;
}
/* Field Header / Title section */
.mtm-field-header {
  padding: 10px 0px;
  cursor: pointer;
}
.mtm-col-sort {
  display: block;
  float: left;
  width: 20px;
  height: 20px;
  padding: 10px 0px 0px 10px;
  margin: 0px;
  font-size: 20px;
  align: center;
  cursor: move;
  color: #dedede;
}
.no-reference.no-context .mtm-col-sort {
  padding: 0px 0px 0px 10px;
}
/* Display Title for each Card */
.mtm-field-title {
  margin-left: 40px;
  min-height: 50px;
}
.no-reference.no-context .mtm-field-title {
  min-height: 20px;
}
.mtm-field-title .mtm-meta-reference {
  font-size: 18px;
  padding-bottom: 5px;
}
.mtm-field-title .mtm-meta-reference-value {
  display: inline-block;
}
.mtm-field-title .mtm-meta-context {
  font-size: 12px;
  padding-bottom: 3px;
  color: #bcbcbc;
  font-weight: bold;
  font-style: italic;
}
.mtm-field-title .mtm-meta-context .dashicons {
  font-size: 15px;
  vertical-align: baseline;
}
.mtm-field-title code {
  font-weight: bold;
}
.mtm-field-title code .mtm-meta-type-val,
.mtm-field-title code .mtm-meta-content-value {
  font-style: italic;
  font-weight: normal;
  background-color: #fefefe;
}
.mtm-field-title code .mtm-meta-content.hidden {
  display: none;
}
/* Header toggles */
.mtm-field-header-toggle:hover,
.mtm-field-section-toggle:hover {
  cursor: pointer;
}
.mtm-field-header-toggle:before,
.mtm-field-section-toggle:before {
  float: right;
  width: 32px;
  height: 32px;
  content: "\f142";
  display: inline-block;
  font: normal 20px/1 dashicons;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-decoration: none !important;
}
.mtm-field.closed .mtm-field-header-toggle:before {
  content: "\f140";
}
/* Field form data */
.mtm-field-data {
  /* display: none; */
  border-top: 1px solid #e5e5e5;
  padding: 10px 10px 15px;
}
.mtm-field-input {
  margin: 0px 0px 15px 0px;
  clear: both;
}
.mtm-field-input-label {
  display: inline-block;
  margin-bottom: 3px;
  padding: 2px;
  font-weight: bold;
  display: block;
}
.mtm-field-input-label em {
  font-style: normal;
  background: #eaeaea;
  padding: 1px 2px;
}
.mtm-field-input input,
.mtm-field-input select {
  width: 100%;
  padding: 4px;
  font-size: 13px;
  /* matches selectize */
}
.mtm-field-type-custom {
  clear: both;
}
.mtm-field-type-custom > div {
  clear: none;
  float: left;
  width: 48%;
  margin: 10px 1%;
}
/* Specific type field selectors */
.mtm-field-input.mtm-field-type-type {
  width: 20%;
  float: left;
  clear: none;
}
.mtm-field-input.mtm-field-type-value {
  width: 79% !important;
  margin-left: 21%;
  clear: none;
}
.mtm-field-input input.mtm-field-input-tag-value {
  float: left;
  clear: none;
}
.mtm-field-section {
  margin-top: 20px;
}
.mtm-field-section-header {
  border-bottom: 1px solid #e3e3e3;
  padding-bottom: 10px;
}
.mtm-field-section-title {
  font-weight: bold;
  font-size: 15px;
}
.mtm-field-section-data {
  margin: 10px 10px 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e3e3e3;
}
/*.mtm-builder .selectize-input { padding:4px; }*/
.mtm-settings .selectize-dropdown-content .optgroup .option {
  padding-left: 10px;
}
.mtm-settings .selectize-dropdown-content .optgroup .optgroup-header {
  font-weight: bold;
}
.mtm-settings .selectize-control.multi .selectize-input > .item {
  background: #5cb85c;
  border-color: #4cae4c;
  color: #fff;
}
.mtm-settings .selectize-control.multi .selectize-input > .item .remove {
  border-color: #4cae4c;
}
.mtm-builder .mtm-field button.mtm-field-remove {
  background-color: #f4e3e3;
  border-color: #d8c5c5;
  color: #565656;
}
.mtm-builder .mtm-field a.mtm-field-remove {
  cursor: pointer;
  color: #d66b6b;
}
.mtm-builder button .dashicons {
  padding: 3px 0px;
  margin-left: -5px;
  color: #888;
}
.mtm-builder button.mtm-add-field .dashicons {
  padding: 4px 0px;
}
.mtm-builder .mtm-actions {
  padding-top: 15px;
}
/* Settings Page */
.mtm-settings select:invalid {
  color: gray;
}
.mtm-settings h3 {
  border-top: 1px solid #dedede;
  padding-top: 25px;
  margin-top: 20px;
}
.mtm-settings .postbox .handlediv {
  display: none;
  visibility: hidden;
}
.mtm-settings .postbox .inside {
  border-top: 1px solid #dedede;
  margin-top: 0px;
  padding-top: 10px;
}
.mtm-settings .postbox h3 {
  font-size: 16px;
}
.mtm-settings .postbox td,
.mtm-settings .postbox th {
  vertical-align: top;
}
.mtm-settings .postbox th {
  padding: 15px 20px;
  margin: 0 !important;
  font-size: 0.97em;
}
.mtm-settings .postbox .mtm-boxheader {
  font-style: italic;
  margin: 0;
  padding: 10px 5px;
}
.mtm-settings .postbox tr.mtm-header td {
  font-style: italic;
  padding: 10px 5px;
  margin: 0;
}
.mtm-settings .postbox tr.mtm-header h4,
.mtm-settings .postbox .postbox h4 {
  font-weight: bold;
  font-size: 15px;
  font-style: normal;
  border-bottom: 1px solid #dedede;
  margin: 0 0 10px;
  padding: 0 0 10px;
}
.mtm-settings .postbox tr.mtm-subheader td {
  font-style: italic;
  margin: 0;
  padding: 5px 20px 2px;
}
.mtm-settings .postbox tr.mtm-subheader h5 {
  font-style: normal;
  margin: 10px 0;
  padding: 0 0 5px;
  font-weight: bold;
  font-size: 15px;
  border-bottom: 1px solid #efefef;
  color: #000;
}
/* Schema Settings */
.mtm-settings .mtm-image-upload-preview {
  margin-bottom: 10px;
}
.mtm-settings #mtm_schema_profiles_other_row {
  display: none;
}
.mtm-settings .mtm-schema-profiles-other a.mtm-schema-profile-remove {
  text-decoration: none;
  color: black;
}
/* Go Pro Stuff */
.mtm-settings table.features {
  border-spacing: 0;
  border: 1px solid #dedede;
}
.mtm-settings table.features th,
table.features td {
  background: #fff;
  border: 0;
  border-bottom: 1px solid #dedede;
  padding: 5px;
}
.mtm-settings table.features tbody tr {
  border-bottom: 1px solid #EFEFEF;
}
.mtm-settings table.features tbody tr:first-child th {
  background: #EFEFEF;
}
.mtm-settings table.features tbody th {
  text-align: left;
  padding-left: 10px;
}
.mtm-settings table.features tr th:not(:first-child),
table.features tr td:not(:first-child) {
  width: 150px;
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
}
.mtm-settings table.features .dashicons-yes-alt {
  color: darkgreen;
  font-size: 20px;
}
.mtm-settings table.features tfoot th span {
  display: block;
  margin-bottom: 5px;
  color: #8bc976;
  font-size: 18px;
}
.mtm-settings table.features tfoot th span.deal {
  color: #FC9840;
  font-size: 14px;
}
.mtm-settings table.features tbody th[data-title] {
  cursor: pointer;
}
/*! Tippy.js v6.2.7 - https://unpkg.com/tippy.js@6.2.7/themes/light-border.css */
.tippy-box[data-theme~=light-border] {
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 8, 16, 0.15);
  color: #333;
  box-shadow: 0 4px 14px -2px rgba(0, 8, 16, 0.08);
}
.tippy-box[data-theme~=light-border] > .tippy-backdrop {
  background-color: #fff;
}
.tippy-box[data-theme~=light-border] > .tippy-arrow:after,
.tippy-box[data-theme~=light-border] > .tippy-svg-arrow:after {
  content: "";
  position: absolute;
  z-index: -1;
}
.tippy-box[data-theme~=light-border] > .tippy-arrow:after {
  border-color: transparent;
  border-style: solid;
}
.tippy-box[data-theme~=light-border][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #fff;
}
.tippy-box[data-theme~=light-border][data-placement^=top] > .tippy-arrow:after {
  border-top-color: rgba(0, 8, 16, 0.2);
  border-width: 7px 7px 0;
  top: 17px;
  left: 1px;
}
.tippy-box[data-theme~=light-border][data-placement^=top] > .tippy-svg-arrow > svg {
  top: 16px;
}
.tippy-box[data-theme~=light-border][data-placement^=top] > .tippy-svg-arrow:after {
  top: 17px;
}
.tippy-box[data-theme~=light-border][data-placement^=bottom] > .tippy-arrow:before {
  border-bottom-color: #fff;
  bottom: 16px;
}
.tippy-box[data-theme~=light-border][data-placement^=bottom] > .tippy-arrow:after {
  border-bottom-color: rgba(0, 8, 16, 0.2);
  border-width: 0 7px 7px;
  bottom: 17px;
  left: 1px;
}
.tippy-box[data-theme~=light-border][data-placement^=bottom] > .tippy-svg-arrow > svg {
  bottom: 16px;
}
.tippy-box[data-theme~=light-border][data-placement^=bottom] > .tippy-svg-arrow:after {
  bottom: 17px;
}
.tippy-box[data-theme~=light-border][data-placement^=left] > .tippy-arrow:before {
  border-left-color: #fff;
}
.tippy-box[data-theme~=light-border][data-placement^=left] > .tippy-arrow:after {
  border-left-color: rgba(0, 8, 16, 0.2);
  border-width: 7px 0 7px 7px;
  left: 17px;
  top: 1px;
}
.tippy-box[data-theme~=light-border][data-placement^=left] > .tippy-svg-arrow > svg {
  left: 11px;
}
.tippy-box[data-theme~=light-border][data-placement^=left] > .tippy-svg-arrow:after {
  left: 12px;
}
.tippy-box[data-theme~=light-border][data-placement^=right] > .tippy-arrow:before {
  border-right-color: #fff;
  right: 16px;
}
.tippy-box[data-theme~=light-border][data-placement^=right] > .tippy-arrow:after {
  border-width: 7px 7px 7px 0;
  right: 17px;
  top: 1px;
  border-right-color: rgba(0, 8, 16, 0.2);
}
.tippy-box[data-theme~=light-border][data-placement^=right] > .tippy-svg-arrow > svg {
  right: 11px;
}
.tippy-box[data-theme~=light-border][data-placement^=right] > .tippy-svg-arrow:after {
  right: 12px;
}
.tippy-box[data-theme~=light-border] > .tippy-svg-arrow {
  fill: #fff;
}
.tippy-box[data-theme~=light-border] > .tippy-svg-arrow:after {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCA2czEuNzk2LS4wMTMgNC42Ny0zLjYxNUM1Ljg1MS45IDYuOTMuMDA2IDggMGMxLjA3LS4wMDYgMi4xNDguODg3IDMuMzQzIDIuMzg1QzE0LjIzMyA2LjAwNSAxNiA2IDE2IDZIMHoiIGZpbGw9InJnYmEoMCwgOCwgMTYsIDAuMikiLz48L3N2Zz4=);
  background-size: 16px 6px;
  width: 16px;
  height: 6px;
}
.selectize-control.plugin-drag_drop.multi > .selectize-input > div.ui-sortable-placeholder {
  visibility: visible !important;
  background: #f2f2f2 !important;
  background: rgba(0, 0, 0, 0.06) !important;
  border: 0 none !important;
  -webkit-box-shadow: inset 0 0 12px 4px #fff;
  box-shadow: inset 0 0 12px 4px #fff;
}
.selectize-control.plugin-drag_drop .ui-sortable-placeholder::after {
  content: '!';
  visibility: hidden;
}
.selectize-control.plugin-drag_drop .ui-sortable-helper {
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.selectize-dropdown-header {
  position: relative;
  padding: 5px 8px;
  border-bottom: 1px solid #d0d0d0;
  background: #f8f8f8;
  -webkit-border-radius: 3px 3px 0 0;
  -moz-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0;
}
.selectize-dropdown-header-close {
  position: absolute;
  right: 8px;
  top: 50%;
  color: #303030;
  opacity: 0.4;
  margin-top: -12px;
  line-height: 20px;
  font-size: 20px !important;
}
.selectize-dropdown-header-close:hover {
  color: #000000;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup {
  border-right: 1px solid #f2f2f2;
  border-top: 0 none;
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:last-child {
  border-right: 0 none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:before {
  display: none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup-header {
  border-top: 0 none;
}
.selectize-control.plugin-remove_button [data-value] {
  position: relative;
  padding-right: 24px !important;
}
.selectize-control.plugin-remove_button [data-value] .remove {
  z-index: 1;
  /* fixes ie bug (see #392) */
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 17px;
  text-align: center;
  font-weight: bold;
  font-size: 12px;
  color: inherit;
  text-decoration: none;
  vertical-align: middle;
  display: inline-block;
  padding: 2px 0 0 0;
  border-left: 1px solid #d0d0d0;
  -webkit-border-radius: 0 2px 2px 0;
  -moz-border-radius: 0 2px 2px 0;
  border-radius: 0 2px 2px 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.selectize-control.plugin-remove_button [data-value] .remove:hover {
  background: rgba(0, 0, 0, 0.05);
}
.selectize-control.plugin-remove_button [data-value].active .remove {
  border-left-color: #cacaca;
}
.selectize-control.plugin-remove_button .disabled [data-value] .remove:hover {
  background: none;
}
.selectize-control.plugin-remove_button .disabled [data-value] .remove {
  border-left-color: #ffffff;
}
.selectize-control.plugin-remove_button .remove-single {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 23px;
}
.selectize-control {
  position: relative;
}
.selectize-dropdown,
.selectize-input,
.selectize-input input {
  color: #303030;
  font-family: inherit;
  font-size: 13px;
  line-height: 18px;
  -webkit-font-smoothing: inherit;
}
.selectize-input,
.selectize-control.single .selectize-input.input-active {
  background: #fff;
  cursor: text;
  display: inline-block;
}
.selectize-input {
  border: 1px solid #d0d0d0;
  padding: 8px 8px;
  display: inline-block;
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.selectize-control.multi .selectize-input.has-items {
  padding: 6px 8px 3px;
}
.selectize-input.full {
  background-color: #fff;
}
.selectize-input.disabled,
.selectize-input.disabled * {
  cursor: default !important;
}
.selectize-input.focus {
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
}
.selectize-input.dropdown-active {
  -webkit-border-radius: 3px 3px 0 0;
  -moz-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0;
}
.selectize-input > * {
  vertical-align: baseline;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
}
.selectize-control.multi .selectize-input > div {
  cursor: pointer;
  margin: 0 3px 3px 0;
  padding: 2px 6px;
  background: #f2f2f2;
  color: #303030;
  border: 0 solid #d0d0d0;
}
.selectize-control.multi .selectize-input > div.active {
  background: #e8e8e8;
  color: #303030;
  border: 0 solid #cacaca;
}
.selectize-control.multi .selectize-input.disabled > div,
.selectize-control.multi .selectize-input.disabled > div.active {
  color: #7d7d7d;
  background: #ffffff;
  border: 0 solid #ffffff;
}
.selectize-input > input {
  display: inline-block !important;
  padding: 0 !important;
  min-height: 0 !important;
  max-height: none !important;
  max-width: 100% !important;
  margin: 0 2px 0 0 !important;
  text-indent: 0 !important;
  border: 0 none !important;
  background: none !important;
  line-height: inherit !important;
  -webkit-user-select: auto !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
.selectize-input > input::-ms-clear {
  display: none;
}
.selectize-input > input:focus {
  outline: none !important;
}
.selectize-input > input[placeholder] {
  box-sizing: initial;
}
.selectize-input::after {
  content: ' ';
  display: block;
  clear: left;
}
.selectize-input.dropdown-active::before {
  content: ' ';
  display: block;
  position: absolute;
  background: #f0f0f0;
  height: 1px;
  bottom: 0;
  left: 0;
  right: 0;
}
.selectize-dropdown {
  position: absolute;
  z-index: 10;
  border: 1px solid #d0d0d0;
  background: #fff;
  margin: -1px 0 0 0;
  border-top: 0 none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -webkit-border-radius: 0 0 3px 3px;
  -moz-border-radius: 0 0 3px 3px;
  border-radius: 0 0 3px 3px;
}
.selectize-dropdown [data-selectable] {
  cursor: pointer;
  overflow: hidden;
}
.selectize-dropdown [data-selectable] .highlight {
  background: rgba(125, 168, 208, 0.2);
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
}
.selectize-dropdown .option,
.selectize-dropdown .optgroup-header {
  padding: 5px 8px;
}
.selectize-dropdown .option,
.selectize-dropdown [data-disabled],
.selectize-dropdown [data-disabled] [data-selectable].option {
  cursor: inherit;
  opacity: 0.5;
}
.selectize-dropdown [data-selectable].option {
  opacity: 1;
}
.selectize-dropdown .optgroup:first-child .optgroup-header {
  border-top: 0 none;
}
.selectize-dropdown .optgroup-header {
  color: #303030;
  background: #fff;
  cursor: default;
}
.selectize-dropdown .active {
  background-color: #f5fafd;
  color: #495c68;
}
.selectize-dropdown .active.create {
  color: #495c68;
}
.selectize-dropdown .create {
  color: rgba(48, 48, 48, 0.5);
}
.selectize-dropdown-content {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 200px;
  -webkit-overflow-scrolling: touch;
}
.selectize-control.single .selectize-input,
.selectize-control.single .selectize-input input {
  cursor: pointer;
}
.selectize-control.single .selectize-input.input-active,
.selectize-control.single .selectize-input.input-active input {
  cursor: text;
}
.selectize-control.single .selectize-input:after {
  content: ' ';
  display: block;
  position: absolute;
  top: 50%;
  right: 15px;
  margin-top: -3px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  border-color: #808080 transparent transparent transparent;
}
.selectize-control.single .selectize-input.dropdown-active:after {
  margin-top: -4px;
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent #808080 transparent;
}
.selectize-control.rtl.single .selectize-input:after {
  left: 15px;
  right: auto;
}
.selectize-control.rtl .selectize-input > input {
  margin: 0 4px 0 -2px !important;
}
.selectize-control .selectize-input.disabled {
  opacity: 0.5;
  background-color: #fafafa;
}
/*# sourceMappingURL=selectize.css.map */
/*# sourceMappingURL=meta-tag-manager.css.map */