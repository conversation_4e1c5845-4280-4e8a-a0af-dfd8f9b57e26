<?php

/**
 * Core wordpress file list
 *
 * @package   Duplicator
 * @copyright (c) 2022, Snap Creek LLC
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 */

defined('ABSPATH') || defined('DUPXABSPATH') || exit;

/*
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 */

// @phpstan-ignore-next-line
self::$corePathList = array(
        'wp-login.php' => "f",
        'wp-config-sample.php' => "f",
        'license.txt' => "f",
        'wp-activate.php' => "f",
        'wp-comments-post.php' => "f",
        'wp-signup.php' => "f",
        'wp-mail.php' => "f",
        'wp-links-opml.php' => "f",
        'wp-load.php' => "f",
        'wp-blog-header.php' => "f",
        'readme.html' => "f",
        'wp-includes' => array(
            'category-template.php' => "f",
            'class-wp-recovery-mode-email-service.php' => "f",
            'default-constants.php' => "f",
            'class-wp-http-requests-hooks.php' => "f",
            'class-wp-block-patterns-registry.php' => "f",
            'media.php' => "f",
            'block-patterns.php' => "f",
            'functions.php' => "f",
            'ms-network.php' => "f",
            'class-requests.php' => "f",
            'cache.php' => "f",
            'class-wp-dependency.php' => "f",
            'class-wp-http-proxy.php' => "f",
            'class-phpass.php' => "f",
            'ms-settings.php' => "f",
            'style-engine.php' => "f",
            'class-wp-customize-manager.php' => "f",
            'class-wp-block-styles-registry.php' => "f",
            'class-wp-recovery-mode-key-service.php' => "f",
            'class-walker-category.php' => "f",
            'class-wp-metadata-lazyloader.php' => "f",
            'block-template-utils.php' => "f",
            'blocks.php' => "f",
            'l10n.php' => "f",
            'cron.php' => "f",
            'template.php' => "f",
            'class-wp-widget-factory.php' => "f",
            'class-wp-network.php' => "f",
            'https-migration.php' => "f",
            'nav-menu-template.php' => "f",
            'class-wp-matchesmapregex.php' => "f",
            'feed-rdf.php' => "f",
            'https-detection.php' => "f",
            'widgets' => array(
                'class-wp-widget-pages.php' => "f",
                'class-wp-widget-text.php' => "f",
                'class-wp-widget-custom-html.php' => "f",
                'class-wp-widget-recent-comments.php' => "f",
                'class-wp-widget-tag-cloud.php' => "f",
                'class-wp-widget-links.php' => "f",
                'class-wp-widget-block.php' => "f",
                'class-wp-widget-media-video.php' => "f",
                'class-wp-widget-archives.php' => "f",
                'class-wp-widget-search.php' => "f",
                'class-wp-widget-calendar.php' => "f",
                'class-wp-widget-meta.php' => "f",
                'class-wp-widget-media.php' => "f",
                'class-wp-nav-menu-widget.php' => "f",
                'class-wp-widget-media-audio.php' => "f",
                'class-wp-widget-recent-posts.php' => "f",
                'class-wp-widget-categories.php' => "f",
                'class-wp-widget-rss.php' => "f",
                'class-wp-widget-media-image.php' => "f",
                'class-wp-widget-media-gallery.php' => "f"
            ),
            'class-wp-paused-extensions-storage.php' => "f",
            'class-walker-page-dropdown.php' => "f",
            'class-wp-text-diff-renderer-inline.php' => "f",
            'theme-compat' => array(
                'sidebar.php' => "f",
                'embed-404.php' => "f",
                'embed-content.php' => "f",
                'footer-embed.php' => "f",
                'header-embed.php' => "f",
                'header.php' => "f",
                'footer.php' => "f",
                'embed.php' => "f",
                'comments.php' => "f",
                'comments-popup.php' => "f"
            ),
            'class-phpmailer.php' => "f",
            'class-wp-post-type.php' => "f",
            'block-supports' => array(
                'elements.php' => "f",
                'layout.php' => "f",
                'dimensions.php' => "f",
                'border.php' => "f",
                'duotone.php' => "f",
                'generated-classname.php' => "f",
                'align.php' => "f",
                'colors.php' => "f",
                'typography.php' => "f",
                'utils.php' => "f",
                'custom-classname.php' => "f",
                'spacing.php' => "f"
            ),
            'class-wp-locale-switcher.php' => "f",
            'load.php' => "f",
            'class-wp-recovery-mode-cookie-service.php' => "f",
            'class-wp-role.php' => "f",
            'pluggable-deprecated.php' => "f",
            'class-wp-oembed-controller.php' => "f",
            'class-wp-term.php' => "f",
            'class-smtp.php' => "f",
            'capabilities.php' => "f",
            'IXR' => array(
                'class-IXR-server.php' => "f",
                'class-IXR-date.php' => "f",
                'class-IXR-request.php' => "f",
                'class-IXR-error.php' => "f",
                'class-IXR-client.php' => "f",
                'class-IXR-base64.php' => "f",
                'class-IXR-clientmulticall.php' => "f",
                'class-IXR-value.php' => "f",
                'class-IXR-message.php' => "f",
                'class-IXR-introspectionserver.php' => "f"
            ),
            'class-wp-text-diff-renderer-table.php' => "f",
            'class-wp-xmlrpc-server.php' => "f",
            'class-wp-site-query.php' => "f",
            'class-wp-admin-bar.php' => "f",
            'session.php' => "f",
            'class-wp-block-type-registry.php' => "f",
            'rewrite.php' => "f",
            'registration-functions.php' => "f",
            'option.php' => "f",
            'class-wp-styles.php' => "f",
            'class-walker-comment.php' => "f",
            'post-formats.php' => "f",
            'class-wp-customize-setting.php' => "f",
            'pluggable.php' => "f",
            'class-wp-block-type.php' => "f",
            'class-wp-http-response.php' => "f",
            'class-wp-error.php' => "f",
            'deprecated.php' => "f",
            'blocks' => array(
                'loginout' => array(
                    'block.json' => "f"
                ),
                'pullquote' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'site-tagline.php' => "f",
                'gallery' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'code' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'comments' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'more' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'post-featured-image' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'comment-content' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'social-link.php' => "f",
                'pattern' => array(
                    'block.json' => "f"
                ),
                'query-no-results' => array(
                    'block.json' => "f"
                ),
                'comments-pagination-previous' => array(
                    'block.json' => "f"
                ),
                'post-navigation-link.php' => "f",
                'home-link.php' => "f",
                'image' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-terms.php' => "f",
                'latest-posts' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-date' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'block' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'media-text' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'site-title' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'archives.php' => "f",
                'spacer' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'comments-pagination-numbers.php' => "f",
                'post-author.php' => "f",
                'comments-title' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'buttons' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'video' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'cover' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-template' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'image.php' => "f",
                'template-part.php' => "f",
                'subhead' => array(
                    'block.json' => "f"
                ),
                'site-logo' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'legacy-widget' => array(
                    'block.json' => "f"
                ),
                'audio' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'comment-reply-link' => array(
                    'block.json' => "f"
                ),
                'comment-date' => array(
                    'block.json' => "f"
                ),
                'widget-group' => array(
                    'block.json' => "f"
                ),
                'read-more' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'table' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'latest-comments.php' => "f",
                'query.php' => "f",
                'query-pagination' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'social-links' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'comments-pagination-previous.php' => "f",
                'button' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'navigation-submenu.php' => "f",
                'term-description' => array(
                    'block.json' => "f"
                ),
                'rss' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'columns' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'calendar.php' => "f",
                'calendar' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'page-list' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'quote' => array(
                    'style-rtl.min.css' => "f",
                    'theme-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-excerpt.php' => "f",
                'post-comments' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'comments-pagination-next.php' => "f",
                'post-navigation-link' => array(
                    'block.json' => "f"
                ),
                'query-title.php' => "f",
                'text-columns' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'comment-author-name.php' => "f",
                'post-content.php' => "f",
                'post-title.php' => "f",
                'post-comments.php' => "f",
                'query-pagination-numbers.php' => "f",
                'query-pagination-previous' => array(
                    'block.json' => "f"
                ),
                'separator' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'archives' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'list-item' => array(
                    'block.json' => "f"
                ),
                'social-link' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'avatar' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'site-title.php' => "f",
                'post-template.php' => "f",
                'comment-author-name' => array(
                    'block.json' => "f"
                ),
                'missing' => array(
                    'block.json' => "f"
                ),
                'shortcode.php' => "f",
                'verse' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'categories' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'term-description.php' => "f",
                'post-title' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'classic' => array(
                    'block.json' => "f"
                ),
                'post-terms' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-date.php' => "f",
                'tag-cloud' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'latest-posts.php' => "f",
                'comments-query-loop' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'rss.php' => "f",
                'require-dynamic-blocks.php' => "f",
                'query-title' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'comments-pagination-numbers' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'post-author-biography' => array(
                    'block.json' => "f"
                ),
                'embed' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-comments-form.php' => "f",
                'shortcode' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'query-pagination-previous.php' => "f",
                'comment-edit-link' => array(
                    'block.json' => "f"
                ),
                'query-no-results.php' => "f",
                'comments-pagination-next' => array(
                    'block.json' => "f"
                ),
                'file' => array(
                    'style-rtl.min.css' => "f",
                    'view.min.asset.php' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'view.js' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'view.min.js' => "f",
                    'editor-rtl.min.css' => "f",
                    'view.asset.php' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'freeform' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'legacy-widget.php' => "f",
                'categories.php' => "f",
                'blocks-json.php' => "f",
                'site-tagline' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'home-link' => array(
                    'block.json' => "f"
                ),
                'read-more.php' => "f",
                'navigation-link.php' => "f",
                'index.php' => "f",
                'comment-template' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'page-list.php' => "f",
                'template-part' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'theme.min.css' => "f"
                ),
                'comments-pagination.php' => "f",
                'site-logo.php' => "f",
                'comment-reply-link.php' => "f",
                'search' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'list' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-excerpt' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'column' => array(
                    'block.json' => "f"
                ),
                'group' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'theme-rtl.css' => "f",
                    'editor-rtl.css' => "f",
                    'theme-rtl.min.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'theme.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'theme.min.css' => "f",
                    'style.min.css' => "f"
                ),
                'pattern.php' => "f",
                'preformatted' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'comments-pagination' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'navigation-link' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'html' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'comment-content.php' => "f",
                'nextpage' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'post-featured-image.php' => "f",
                'comments.php' => "f",
                'latest-comments' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'query-pagination-next.php' => "f",
                'comment-template.php' => "f",
                'query-pagination-numbers' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'navigation-submenu' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'comment-date.php' => "f",
                'comments-title.php' => "f",
                'block.php' => "f",
                'heading' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-comments-form' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'cover.php' => "f",
                'widget-group.php' => "f",
                'gallery.php' => "f",
                'search.php' => "f",
                'post-content' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'tag-cloud.php' => "f",
                'avatar.php' => "f",
                'comment-edit-link.php' => "f",
                'navigation.php' => "f",
                'post-author' => array(
                    'style-rtl.min.css' => "f",
                    'block.json' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'file.php' => "f",
                'query-pagination.php' => "f",
                'require-static-blocks.php' => "f",
                'query-pagination-next' => array(
                    'block.json' => "f"
                ),
                'query' => array(
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f"
                ),
                'navigation' => array(
                    'style-rtl.min.css' => "f",
                    'view.min.asset.php' => "f",
                    'editor.min.css' => "f",
                    'view-modal.js' => "f",
                    'editor.css' => "f",
                    'view.js' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'view.min.js' => "f",
                    'view-modal.min.js' => "f",
                    'view-modal.min.asset.php' => "f",
                    'editor-rtl.min.css' => "f",
                    'view-modal.asset.php' => "f",
                    'view.asset.php' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'loginout.php' => "f",
                'paragraph' => array(
                    'style-rtl.min.css' => "f",
                    'editor.min.css' => "f",
                    'editor.css' => "f",
                    'editor-rtl.css' => "f",
                    'block.json' => "f",
                    'editor-rtl.min.css' => "f",
                    'style.css' => "f",
                    'style-rtl.css' => "f",
                    'style.min.css' => "f"
                ),
                'post-author-biography.php' => "f"
            ),
            'default-filters.php' => "f",
            'ms-load.php' => "f",
            'class-wp-feed-cache.php' => "f",
            'plugin.php' => "f",
            'fonts' => array(
                'dashicons.svg' => "f",
                'dashicons.woff2' => "f",
                'dashicons.ttf' => "f",
                'dashicons.woff' => "f",
                'dashicons.eot' => "f"
            ),
            'query.php' => "f",
            'class-pop3.php' => "f",
            'style-engine' => array(
                'class-wp-style-engine-css-rule.php' => "f",
                'class-wp-style-engine-css-declarations.php' => "f",
                'class-wp-style-engine.php' => "f",
                'class-wp-style-engine-css-rules-store.php' => "f",
                'class-wp-style-engine-processor.php' => "f"
            ),
            'class-wp-user-request.php' => "f",
            'class-wp-user-meta-session-tokens.php' => "f",
            'class-wp-oembed.php' => "f",
            'class-wp-editor.php' => "f",
            'class-wp-image-editor-gd.php' => "f",
            'class.wp-scripts.php' => "f",
            'class-walker-nav-menu.php' => "f",
            'author-template.php' => "f",
            'ms-blogs.php' => "f",
            'class-wp-simplepie-sanitize-kses.php' => "f",
            'class-wp-walker.php' => "f",
            'taxonomy.php' => "f",
            'compat.php' => "f",
            'class-wp-block.php' => "f",
            'category.php' => "f",
            'atomlib.php' => "f",
            'class.wp-dependencies.php' => "f",
            'class-wp-recovery-mode.php' => "f",
            'Requests' => array(
                'Utility' => array(
                    'CaseInsensitiveDictionary.php' => "f",
                    'FilteredIterator.php' => "f"
                ),
                'IDNAEncoder.php' => "f",
                'Transport' => array(
                    'cURL.php' => "f",
                    'fsockopen.php' => "f"
                ),
                'Proxy.php' => "f",
                'IPv6.php' => "f",
                'Exception.php' => "f",
                'Hooks.php' => "f",
                'Proxy' => array(
                    'HTTP.php' => "f"
                ),
                'Response.php' => "f",
                'IRI.php' => "f",
                'SSL.php' => "f",
                'Auth' => array(
                    'Basic.php' => "f"
                ),
                'Response' => array(
                    'Headers.php' => "f"
                ),
                'Session.php' => "f",
                'Exception' => array(
                    'Transport' => array(
                        'cURL.php' => "f"
                    ),
                    'HTTP' => array(
                        '431.php' => "f",
                        '415.php' => "f",
                        '428.php' => "f",
                        '414.php' => "f",
                        '408.php' => "f",
                        '417.php' => "f",
                        '502.php' => "f",
                        '306.php' => "f",
                        '412.php' => "f",
                        '410.php' => "f",
                        '416.php' => "f",
                        '505.php' => "f",
                        '401.php' => "f",
                        '305.php' => "f",
                        '404.php' => "f",
                        '403.php' => "f",
                        '411.php' => "f",
                        '500.php' => "f",
                        '429.php' => "f",
                        '504.php' => "f",
                        '402.php' => "f",
                        '501.php' => "f",
                        '405.php' => "f",
                        '409.php' => "f",
                        '406.php' => "f",
                        '413.php' => "f",
                        '304.php' => "f",
                        '418.php' => "f",
                        '511.php' => "f",
                        '407.php' => "f",
                        '503.php' => "f",
                        '400.php' => "f",
                        'Unknown.php' => "f"
                    ),
                    'HTTP.php' => "f",
                    'Transport.php' => "f"
                ),
                'Auth.php' => "f",
                'Cookie' => array(
                    'Jar.php' => "f"
                ),
                'Transport.php' => "f",
                'Cookie.php' => "f",
                'Hooker.php' => "f"
            ),
            'robots-template.php' => "f",
            'revision.php' => "f",
            'wp-diff.php' => "f",
            'class-wp-roles.php' => "f",
            'sitemaps' => array(
                'class-wp-sitemaps-index.php' => "f",
                'class-wp-sitemaps-renderer.php' => "f",
                'class-wp-sitemaps-stylesheet.php' => "f",
                'class-wp-sitemaps-registry.php' => "f",
                'class-wp-sitemaps.php' => "f",
                'class-wp-sitemaps-provider.php' => "f",
                'providers' => array(
                    'class-wp-sitemaps-posts.php' => "f",
                    'class-wp-sitemaps-taxonomies.php' => "f",
                    'class-wp-sitemaps-users.php' => "f"
                )
            ),
            'js' => array(
                'hoverIntent.js' => "f",
                'wp-a11y.min.js' => "f",
                'media-audiovideo.js' => "f",
                'zxcvbn-async.js' => "f",
                'media-models.js' => "f",
                'twemoji.js' => "f",
                'wp-pointer.js' => "f",
                'customize-preview-nav-menus.min.js' => "f",
                'wp-pointer.min.js' => "f",
                'json2.min.js' => "f",
                'backbone.js' => "f",
                'swfupload' => array(
                    'handlers.js' => "f",
                    'license.txt' => "f",
                    'handlers.min.js' => "f",
                    'swfupload.js' => "f",
                    'plugins' => array(
                        'swfupload.swfobject.js' => "f",
                        'swfupload.cookies.js' => "f",
                        'swfupload.queue.js' => "f",
                        'swfupload.speed.js' => "f"
                    ),
                    'swfupload.swf' => "f"
                ),
                'twemoji.min.js' => "f",
                'mce-view.min.js' => "f",
                'swfobject.js' => "f",
                'colorpicker.min.js' => "f",
                'wp-emoji.js' => "f",
                'customize-preview-widgets.min.js' => "f",
                'zxcvbn.min.js' => "f",
                'wp-ajax-response.js' => "f",
                'wp-util.js' => "f",
                'underscore.js' => "f",
                'wp-emoji.min.js' => "f",
                'customize-preview-nav-menus.js' => "f",
                'underscore.min.js' => "f",
                'admin-bar.min.js' => "f",
                'wp-sanitize.js' => "f",
                'imagesloaded.min.js' => "f",
                'thickbox' => array(
                    'loadingAnimation.gif' => "f",
                    'thickbox.css' => "f",
                    'thickbox.js' => "f",
                    'macFFBgHack.png' => "f"
                ),
                'wplink.min.js' => "f",
                'tinymce' => array(
                    'wp-mce-help.php' => "f",
                    'themes' => array(
                        'modern' => array(
                            'theme.js' => "f",
                            'theme.min.js' => "f"
                        ),
                        'inlite' => array(
                            'theme.js' => "f",
                            'theme.min.js' => "f"
                        )
                    ),
                    'wp-tinymce.js' => "f",
                    'langs' => array(
                        'wp-langs-en.js' => "f"
                    ),
                    'wp-tinymce.js.gz' => "f",
                    'license.txt' => "f",
                    'utils' => array(
                        'mctabs.js' => "f",
                        'form_utils.js' => "f",
                        'editable_selects.js' => "f",
                        'validate.js' => "f"
                    ),
                    'plugins' => array(
                        'wordpress' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'directionality' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'image' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpview' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpeditimage' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'hr' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'lists' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpfullscreen' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wptextpattern' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'link' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'fullscreen' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpembed' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wplink' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpdialogs' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'textcolor' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpemoji' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'colorpicker' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'paste' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'compat3x' => array(
                            'css' => array(
                                'dialog.css' => "f"
                            ),
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'charmap' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'tabfocus' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpgallery' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'media' => array(
                            'plugin.js' => "f",
                            'moxieplayer.swf' => "f",
                            'plugin.min.js' => "f"
                        ),
                        'wpautoresize' => array(
                            'plugin.js' => "f",
                            'plugin.min.js' => "f"
                        )
                    ),
                    'wp-tinymce.php' => "f",
                    'tiny_mce_popup.js' => "f",
                    'skins' => array(
                        'wordpress' => array(
                            'wp-content.css' => "f",
                            'images' => array(
                                'gallery-2x.png' => "f",
                                'playlist-video.png' => "f",
                                'more-2x.png' => "f",
                                'pagebreak.png' => "f",
                                'audio.png' => "f",
                                'playlist-audio.png' => "f",
                                'dashicon-no-alt.png' => "f",
                                'dashicon-no.png' => "f",
                                'dashicon-edit.png' => "f",
                                'embedded.png' => "f",
                                'video.png' => "f",
                                'pagebreak-2x.png' => "f",
                                'gallery.png' => "f",
                                'more.png' => "f"
                            )
                        ),
                        'lightgray' => array(
                            'content.inline.min.css' => "f",
                            'content.min.css' => "f",
                            'skin.ie7.min.css' => "f",
                            'fonts' => array(
                                'tinymce-small.json' => "f",
                                'tinymce.ttf' => "f",
                                'tinymce-small.svg' => "f",
                                'tinymce-small.woff' => "f",
                                'tinymce.json' => "f",
                                'readme.md' => "f",
                                'tinymce.svg' => "f",
                                'tinymce.woff' => "f",
                                'tinymce-small.ttf' => "f",
                                'tinymce-small.eot' => "f",
                                'tinymce.eot' => "f"
                            ),
                            'img' => array(
                                'loader.gif' => "f",
                                'object.gif' => "f",
                                'trans.gif' => "f",
                                'anchor.gif' => "f"
                            ),
                            'skin.min.css' => "f"
                        )
                    ),
                    'tinymce.min.js' => "f"
                ),
                'wp-emoji-release.min.js' => "f",
                'wp-emoji-loader.min.js' => "f",
                'wp-list-revisions.min.js' => "f",
                'wp-auth-check.js' => "f",
                'api-request.min.js' => "f",
                'customize-models.js' => "f",
                'clipboard.min.js' => "f",
                'customize-preview.min.js' => "f",
                'quicktags.js' => "f",
                'wp-list-revisions.js' => "f",
                'customize-models.min.js' => "f",
                'wp-util.min.js' => "f",
                'customize-preview-widgets.js' => "f",
                'admin-bar.js' => "f",
                'wpdialog.min.js' => "f",
                'wp-lists.min.js' => "f",
                'wp-emoji-loader.js' => "f",
                'clipboard.js' => "f",
                'autosave.min.js' => "f",
                'quicktags.min.js' => "f",
                'customize-selective-refresh.js' => "f",
                'jcrop' => array(
                    'Jcrop.gif' => "f",
                    'jquery.Jcrop.min.js' => "f",
                    'jquery.Jcrop.min.css' => "f"
                ),
                'wp-backbone.js' => "f",
                'media-editor.js' => "f",
                'media-editor.min.js' => "f",
                'plupload' => array(
                    'handlers.js' => "f",
                    'moxie.js' => "f",
                    'moxie.min.js' => "f",
                    'license.txt' => "f",
                    'plupload.flash.swf' => "f",
                    'wp-plupload.min.js' => "f",
                    'handlers.min.js' => "f",
                    'plupload.full.min.js' => "f",
                    'wp-plupload.js' => "f",
                    'plupload.silverlight.xap' => "f",
                    'plupload.js' => "f",
                    'plupload.min.js' => "f"
                ),
                'media-grid.js' => "f",
                'hoverintent-js.min.js' => "f",
                'media-views.min.js' => "f",
                'wp-embed.js' => "f",
                'heartbeat.min.js' => "f",
                'wp-api.min.js' => "f",
                'customize-views.js' => "f",
                'customize-loader.js' => "f",
                'autosave.js' => "f",
                'crop' => array(
                    'cropper.js' => "f",
                    'cropper.css' => "f",
                    'marqueeHoriz.gif' => "f",
                    'marqueeVert.gif' => "f"
                ),
                'wp-auth-check.min.js' => "f",
                'jquery' => array(
                    'jquery.js' => "f",
                    'jquery.form.min.js' => "f",
                    'jquery.ui.touch-punch.js' => "f",
                    'jquery.table-hotkeys.js' => "f",
                    'jquery.query.js' => "f",
                    'jquery.hotkeys.min.js' => "f",
                    'jquery-migrate.min.js' => "f",
                    'jquery.schedule.js' => "f",
                    'jquery.hotkeys.js' => "f",
                    'suggest.js' => "f",
                    'jquery-migrate.js' => "f",
                    'jquery.form.js' => "f",
                    'jquery.min.js' => "f",
                    'ui' => array(
                        'effect-bounce.js' => "f",
                        'jquery.ui.dialog.min.js' => "f",
                        'jquery.ui.menu.min.js' => "f",
                        'autocomplete.js' => "f",
                        'spinner.js' => "f",
                        'progressbar.min.js' => "f",
                        'jquery.ui.core.min.js' => "f",
                        'datepicker.js' => "f",
                        'spinner.min.js' => "f",
                        'dialog.min.js' => "f",
                        'effect-pulsate.js' => "f",
                        'jquery.ui.position.min.js' => "f",
                        'jquery.ui.effect-drop.min.js' => "f",
                        'checkboxradio.js' => "f",
                        'slider.js' => "f",
                        'accordion.min.js' => "f",
                        'effect-clip.min.js' => "f",
                        'controlgroup.min.js' => "f",
                        'jquery.ui.sortable.min.js' => "f",
                        'jquery.ui.effect.min.js' => "f",
                        'effect-explode.min.js' => "f",
                        'jquery.ui.effect-transfer.min.js' => "f",
                        'dialog.js' => "f",
                        'jquery.ui.draggable.min.js' => "f",
                        'draggable.min.js' => "f",
                        'effect-fold.min.js' => "f",
                        'jquery.ui.spinner.min.js' => "f",
                        'accordion.js' => "f",
                        'effect-fade.min.js' => "f",
                        'effect-size.js' => "f",
                        'droppable.min.js' => "f",
                        'resizable.js' => "f",
                        'effect-explode.js' => "f",
                        'effect.js' => "f",
                        'effect-fade.js' => "f",
                        'effect.min.js' => "f",
                        'effect-drop.min.js' => "f",
                        'jquery.ui.effect-slide.min.js' => "f",
                        'selectable.min.js' => "f",
                        'effect-transfer.js' => "f",
                        'tooltip.min.js' => "f",
                        'effect-shake.js' => "f",
                        'selectmenu.js' => "f",
                        'effect-shake.min.js' => "f",
                        'droppable.js' => "f",
                        'effect-highlight.min.js' => "f",
                        'jquery.ui.datepicker.min.js' => "f",
                        'effect-drop.js' => "f",
                        'effect-blind.min.js' => "f",
                        'button.js' => "f",
                        'effect-slide.js' => "f",
                        'core.js' => "f",
                        'effect-highlight.js' => "f",
                        'sortable.min.js' => "f",
                        'position.min.js' => "f",
                        'effect-fold.js' => "f",
                        'jquery.ui.effect-pulsate.min.js' => "f",
                        'jquery.ui.mouse.min.js' => "f",
                        'jquery.ui.effect-explode.min.js' => "f",
                        'jquery.ui.autocomplete.min.js' => "f",
                        'effect-size.min.js' => "f",
                        'jquery.ui.effect-bounce.min.js' => "f",
                        'tabs.js' => "f",
                        'core.min.js' => "f",
                        'jquery.ui.progressbar.min.js' => "f",
                        'effect-clip.js' => "f",
                        'effect-puff.min.js' => "f",
                        'jquery.ui.selectable.min.js' => "f",
                        'checkboxradio.min.js' => "f",
                        'effect-transfer.min.js' => "f",
                        'menu.min.js' => "f",
                        'jquery.ui.tabs.min.js' => "f",
                        'resizable.min.js' => "f",
                        'effect-scale.js' => "f",
                        'jquery.ui.effect-highlight.min.js' => "f",
                        'jquery.ui.tooltip.min.js' => "f",
                        'jquery.ui.droppable.min.js' => "f",
                        'mouse.min.js' => "f",
                        'jquery.ui.effect-fade.min.js' => "f",
                        'controlgroup.js' => "f",
                        'menu.js' => "f",
                        'jquery.ui.effect-clip.min.js' => "f",
                        'jquery.ui.accordion.min.js' => "f",
                        'jquery.ui.resizable.min.js' => "f",
                        'effect-pulsate.min.js' => "f",
                        'effect-bounce.min.js' => "f",
                        'widget.min.js' => "f",
                        'jquery.ui.effect-blind.min.js' => "f",
                        'tabs.min.js' => "f",
                        'datepicker.min.js' => "f",
                        'button.min.js' => "f",
                        'selectmenu.min.js' => "f",
                        'effect-blind.js' => "f",
                        'jquery.ui.effect-shake.min.js' => "f",
                        'slider.min.js' => "f",
                        'effect-scale.min.js' => "f",
                        'draggable.js' => "f",
                        'jquery.ui.slider.min.js' => "f",
                        'mouse.js' => "f",
                        'progressbar.js' => "f",
                        'jquery.ui.effect-scale.min.js' => "f",
                        'sortable.js' => "f",
                        'jquery.ui.button.min.js' => "f",
                        'tooltip.js' => "f",
                        'effect-slide.min.js' => "f",
                        'jquery.ui.widget.min.js' => "f",
                        'effect-puff.js' => "f",
                        'autocomplete.min.js' => "f",
                        'selectable.js' => "f",
                        'jquery.ui.effect-fold.min.js' => "f"
                    ),
                    'jquery.table-hotkeys.min.js' => "f",
                    'suggest.min.js' => "f",
                    'jquery.serialize-object.js' => "f",
                    'jquery.masonry.min.js' => "f",
                    'jquery.color.min.js' => "f"
                ),
                'wpdialog.js' => "f",
                'shortcode.js' => "f",
                'colorpicker.js' => "f",
                'tw-sack.min.js' => "f",
                'heartbeat.js' => "f",
                'wp-api.js' => "f",
                'customize-base.js' => "f",
                'media-models.min.js' => "f",
                'wp-custom-header.js' => "f",
                'media-audiovideo.min.js' => "f",
                'mce-view.js' => "f",
                'media-views.js' => "f",
                'wp-embed-template.js' => "f",
                'hoverIntent.min.js' => "f",
                'wp-embed.min.js' => "f",
                'utils.min.js' => "f",
                'customize-views.min.js' => "f",
                'customize-base.min.js' => "f",
                'customize-selective-refresh.min.js' => "f",
                'wp-ajax-response.min.js' => "f",
                'zxcvbn-async.min.js' => "f",
                'wp-lists.js' => "f",
                'comment-reply.min.js' => "f",
                'wp-embed-template.min.js' => "f",
                'wp-backbone.min.js' => "f",
                'wp-a11y.js' => "f",
                'backbone.min.js' => "f",
                'dist' => array(
                    'api-fetch.js' => "f",
                    'notices.min.js' => "f",
                    'edit-site.js' => "f",
                    'data-controls.min.js' => "f",
                    'wordcount.min.js' => "f",
                    'edit-widgets.min.js' => "f",
                    'editor.min.js' => "f",
                    'rich-text.js' => "f",
                    'dom.js' => "f",
                    'element.js' => "f",
                    'widgets.js' => "f",
                    'widgets.min.js' => "f",
                    'blocks.min.js' => "f",
                    'components.min.js' => "f",
                    'edit-widgets.js' => "f",
                    'editor.js' => "f",
                    'blob.min.js' => "f",
                    'components.js' => "f",
                    'url.js' => "f",
                    'i18n.min.js' => "f",
                    'data-controls.js' => "f",
                    'deprecated.js' => "f",
                    'preferences-persistence.min.js' => "f",
                    'priority-queue.js' => "f",
                    'token-list.min.js' => "f",
                    'reusable-blocks.js' => "f",
                    'dom-ready.js' => "f",
                    'a11y.min.js' => "f",
                    'element.min.js' => "f",
                    'keycodes.js' => "f",
                    'list-reusable-blocks.js' => "f",
                    'autop.js' => "f",
                    'warning.js' => "f",
                    'escape-html.js' => "f",
                    'blob.js' => "f",
                    'block-directory.min.js' => "f",
                    'dom-ready.min.js' => "f",
                    'annotations.js' => "f",
                    'viewport.js' => "f",
                    'edit-site.min.js' => "f",
                    'viewport.min.js' => "f",
                    'server-side-render.js' => "f",
                    'autop.min.js' => "f",
                    'compose.js' => "f",
                    'plugins.js' => "f",
                    'block-directory.js' => "f",
                    'primitives.js' => "f",
                    'block-editor.min.js' => "f",
                    'primitives.min.js' => "f",
                    'keyboard-shortcuts.js' => "f",
                    'html-entities.min.js' => "f",
                    'notices.js' => "f",
                    'wordcount.js' => "f",
                    'style-engine.min.js' => "f",
                    'core-data.js' => "f",
                    'customize-widgets.min.js' => "f",
                    'compose.min.js' => "f",
                    'warning.min.js' => "f",
                    'url.min.js' => "f",
                    'block-serialization-default-parser.js' => "f",
                    'data.min.js' => "f",
                    'edit-post.js' => "f",
                    'server-side-render.min.js' => "f",
                    'redux-routine.min.js' => "f",
                    'i18n.js' => "f",
                    'redux-routine.js' => "f",
                    'escape-html.min.js' => "f",
                    'edit-post.min.js' => "f",
                    'shortcode.js' => "f",
                    'token-list.js' => "f",
                    'preferences.js' => "f",
                    'date.min.js' => "f",
                    'hooks.min.js' => "f",
                    'html-entities.js' => "f",
                    'core-data.min.js' => "f",
                    'keycodes.min.js' => "f",
                    'priority-queue.min.js' => "f",
                    'blocks.js' => "f",
                    'format-library.min.js' => "f",
                    'development' => array(
                        'react-refresh-runtime.js' => "f",
                        'react-refresh-entry.min.js' => "f",
                        'react-refresh-runtime.min.js' => "f",
                        'react-refresh-entry.js' => "f"
                    ),
                    'is-shallow-equal.min.js' => "f",
                    'date.js' => "f",
                    'list-reusable-blocks.min.js' => "f",
                    'block-library.js' => "f",
                    'style-engine.js' => "f",
                    'plugins.min.js' => "f",
                    'nux.min.js' => "f",
                    'media-utils.js' => "f",
                    'is-shallow-equal.js' => "f",
                    'media-utils.min.js' => "f",
                    'dom.min.js' => "f",
                    'customize-widgets.js' => "f",
                    'vendor' => array(
                        'moment.min.js' => "f",
                        'wp-polyfill-element-closest.min.js' => "f",
                        'wp-polyfill-fetch.js' => "f",
                        'wp-polyfill-node-contains.js' => "f",
                        'moment.js' => "f",
                        'regenerator-runtime.min.js' => "f",
                        'wp-polyfill-object-fit.min.js' => "f",
                        'wp-polyfill-url.js' => "f",
                        'react.js' => "f",
                        'wp-polyfill.min.js' => "f",
                        'wp-polyfill-formdata.js' => "f",
                        'wp-polyfill-element-closest.js' => "f",
                        'wp-polyfill-dom-rect.min.js' => "f",
                        'react.min.js' => "f",
                        'wp-polyfill-dom-rect.js' => "f",
                        'lodash.min.js' => "f",
                        'wp-polyfill-fetch.min.js' => "f",
                        'wp-polyfill-object-fit.js' => "f",
                        'react-dom.min.js' => "f",
                        'wp-polyfill-formdata.min.js' => "f",
                        'wp-polyfill-node-contains.min.js' => "f",
                        'react-dom.js' => "f",
                        'regenerator-runtime.js' => "f",
                        'wp-polyfill-url.min.js' => "f",
                        'wp-polyfill.js' => "f",
                        'lodash.js' => "f"
                    ),
                    'annotations.min.js' => "f",
                    'block-editor.js' => "f",
                    'data.js' => "f",
                    'preferences-persistence.js' => "f",
                    'rich-text.min.js' => "f",
                    'hooks.js' => "f",
                    'api-fetch.min.js' => "f",
                    'a11y.js' => "f",
                    'block-serialization-default-parser.min.js' => "f",
                    'shortcode.min.js' => "f",
                    'format-library.js' => "f",
                    'block-library.min.js' => "f",
                    'deprecated.min.js' => "f",
                    'nux.js' => "f",
                    'keyboard-shortcuts.min.js' => "f",
                    'preferences.min.js' => "f",
                    'reusable-blocks.min.js' => "f"
                ),
                'api-request.js' => "f",
                'customize-loader.min.js' => "f",
                'imgareaselect' => array(
                    'jquery.imgareaselect.js' => "f",
                    'jquery.imgareaselect.min.js' => "f",
                    'border-anim-v.gif' => "f",
                    'border-anim-h.gif' => "f",
                    'imgareaselect.css' => "f"
                ),
                'customize-preview.js' => "f",
                'utils.js' => "f",
                'wplink.js' => "f",
                'comment-reply.js' => "f",
                'mediaelement' => array(
                    'controls.png' => "f",
                    'mediaelement-migrate.js' => "f",
                    'renderers' => array(
                        'vimeo.js' => "f",
                        'vimeo.min.js' => "f"
                    ),
                    'mediaelement-migrate.min.js' => "f",
                    'mediaelementplayer-legacy.min.css' => "f",
                    'jumpforward.png' => "f",
                    'skipback.png' => "f",
                    'wp-mediaelement.css' => "f",
                    'background.png' => "f",
                    'mediaelement-and-player.min.js' => "f",
                    'mediaelement.min.js' => "f",
                    'mejs-controls.svg' => "f",
                    'mediaelementplayer.min.css' => "f",
                    'bigplay.png' => "f",
                    'controls.svg' => "f",
                    'wp-mediaelement.js' => "f",
                    'mediaelement.js' => "f",
                    'mediaelementplayer-legacy.css' => "f",
                    'mejs-controls.png' => "f",
                    'mediaelement-and-player.js' => "f",
                    'wp-mediaelement.min.js' => "f",
                    'wp-playlist.js' => "f",
                    'bigplay.svg' => "f",
                    'wp-mediaelement.min.css' => "f",
                    'mediaelementplayer.css' => "f",
                    'wp-playlist.min.js' => "f",
                    'loading.gif' => "f",
                    'froogaloop.min.js' => "f"
                ),
                'media-grid.min.js' => "f",
                'wp-custom-header.min.js' => "f",
                'masonry.min.js' => "f",
                'shortcode.min.js' => "f",
                'wp-sanitize.min.js' => "f",
                'tw-sack.js' => "f",
                'json2.js' => "f",
                'codemirror' => array(
                    'esprima.js' => "f",
                    'htmlhint-kses.js' => "f",
                    'csslint.js' => "f",
                    'jsonlint.js' => "f",
                    'codemirror.min.css' => "f",
                    'codemirror.min.js' => "f",
                    'fakejshint.js' => "f",
                    'htmlhint.js' => "f",
                    'jshint.js' => "f"
                )
            ),
            'css' => array(
                'wp-embed-template.min.css' => "f",
                'admin-bar-rtl.css' => "f",
                'wp-auth-check.min.css' => "f",
                'buttons.min.css' => "f",
                'wp-embed-template-ie.min.css' => "f",
                'dashicons.css' => "f",
                'buttons.css' => "f",
                'editor.min.css' => "f",
                'buttons-rtl.css' => "f",
                'jquery-ui-dialog.css' => "f",
                'dashicons.min.css' => "f",
                'jquery-ui-dialog-rtl.min.css' => "f",
                'wp-embed-template-ie.css' => "f",
                'customize-preview.css' => "f",
                'editor.css' => "f",
                'editor-rtl.css' => "f",
                'admin-bar.min.css' => "f",
                'wp-auth-check-rtl.min.css' => "f",
                'admin-bar-rtl.min.css' => "f",
                'wp-pointer.min.css' => "f",
                'wp-pointer-rtl.css' => "f",
                'admin-bar.css' => "f",
                'customize-preview-rtl.min.css' => "f",
                'jquery-ui-dialog.min.css' => "f",
                'wp-pointer-rtl.min.css' => "f",
                'wp-pointer.css' => "f",
                'classic-themes.css' => "f",
                'editor-rtl.min.css' => "f",
                'media-views.min.css' => "f",
                'customize-preview-rtl.css' => "f",
                'media-views-rtl.min.css' => "f",
                'buttons-rtl.min.css' => "f",
                'jquery-ui-dialog-rtl.css' => "f",
                'wp-embed-template.css' => "f",
                'dist' => array(
                    'widgets' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'customize-widgets' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'list-reusable-blocks' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'reusable-blocks' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'format-library' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'nux' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'edit-post' => array(
                        'style-rtl.min.css' => "f",
                        'classic-rtl.min.css' => "f",
                        'classic.min.css' => "f",
                        'style.css' => "f",
                        'classic-rtl.css' => "f",
                        'style-rtl.css' => "f",
                        'classic.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'block-directory' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'editor' => array(
                        'style-rtl.min.css' => "f",
                        'editor-styles-rtl.css' => "f",
                        'editor-styles-rtl.min.css' => "f",
                        'editor-styles.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'editor-styles.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'edit-site' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'block-library' => array(
                        'style-rtl.min.css' => "f",
                        'reset.min.css' => "f",
                        'elements.min.css' => "f",
                        'elements-rtl.css' => "f",
                        'editor.min.css' => "f",
                        'elements-rtl.min.css' => "f",
                        'editor-elements-rtl.min.css' => "f",
                        'common.min.css' => "f",
                        'classic-rtl.min.css' => "f",
                        'editor.css' => "f",
                        'theme-rtl.css' => "f",
                        'editor-rtl.css' => "f",
                        'common-rtl.min.css' => "f",
                        'theme-rtl.min.css' => "f",
                        'reset-rtl.min.css' => "f",
                        'reset-rtl.css' => "f",
                        'classic.min.css' => "f",
                        'editor-rtl.min.css' => "f",
                        'theme.css' => "f",
                        'editor-elements.min.css' => "f",
                        'elements.css' => "f",
                        'style.css' => "f",
                        'classic-rtl.css' => "f",
                        'style-rtl.css' => "f",
                        'classic.css' => "f",
                        'common.css' => "f",
                        'common-rtl.css' => "f",
                        'theme.min.css' => "f",
                        'editor-elements.css' => "f",
                        'reset.css' => "f",
                        'style.min.css' => "f",
                        'editor-elements-rtl.css' => "f"
                    ),
                    'edit-widgets' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    ),
                    'block-editor' => array(
                        'style-rtl.min.css' => "f",
                        'default-editor-styles-rtl.css' => "f",
                        'default-editor-styles.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'default-editor-styles.min.css' => "f",
                        'style.min.css' => "f",
                        'default-editor-styles-rtl.min.css' => "f"
                    ),
                    'components' => array(
                        'style-rtl.min.css' => "f",
                        'style.css' => "f",
                        'style-rtl.css' => "f",
                        'style.min.css' => "f"
                    )
                ),
                'media-views-rtl.css' => "f",
                'customize-preview.min.css' => "f",
                'wp-auth-check.css' => "f",
                'media-views.css' => "f",
                'classic-themes.min.css' => "f",
                'wp-auth-check-rtl.css' => "f"
            ),
            'bookmark-template.php' => "f",
            'rest-api.php' => "f",
            'theme-templates.php' => "f",
            'class-http.php' => "f",
            'php-compat' => array(
                'readonly.php' => "f"
            ),
            'post-template.php' => "f",
            'user.php' => "f",
            'class-wp-comment.php' => "f",
            'class-wp-customize-section.php' => "f",
            'registration.php' => "f",
            'Text' => array(
                'Diff.php' => "f",
                'Diff' => array(
                    'Engine' => array(
                        'string.php' => "f",
                        'xdiff.php' => "f",
                        'native.php' => "f",
                        'shell.php' => "f"
                    ),
                    'Renderer.php' => "f",
                    'Renderer' => array(
                        'inline.php' => "f"
                    )
                )
            ),
            'class-json.php' => "f",
            'shortcodes.php' => "f",
            'class-wp-theme-json-schema.php' => "f",
            'vars.php' => "f",
            'spl-autoload-compat.php' => "f",
            'template-canvas.php' => "f",
            'class-wp-taxonomy.php' => "f",
            'class-wp-site.php' => "f",
            'post.php' => "f",
            'version.php' => "f",
            'ms-files.php' => "f",
            'block-i18n.json' => "f",
            'certificates' => array(
                'ca-bundle.crt' => "f"
            ),
            'admin-bar.php' => "f",
            'class-wp-meta-query.php' => "f",
            'link-template.php' => "f",
            'class-snoopy.php' => "f",
            'class-wp-scripts.php' => "f",
            'class-wp-image-editor.php' => "f",
            'class-wp-widget.php' => "f",
            'feed-atom-comments.php' => "f",
            'class-wp-http-streams.php' => "f",
            'functions.wp-scripts.php' => "f",
            'rss.php' => "f",
            'PHPMailer' => array(
                'PHPMailer.php' => "f",
                'Exception.php' => "f",
                'SMTP.php' => "f"
            ),
            'class-wp-feed-cache-transient.php' => "f",
            'class-wp-user.php' => "f",
            'post-thumbnail-template.php' => "f",
            'class-wp-recovery-mode-link-service.php' => "f",
            'class-IXR.php' => "f",
            'general-template.php' => "f",
            'ms-site.php' => "f",
            'wlwmanifest.xml' => "f",
            'class-wp-session-tokens.php' => "f",
            'rest-api' => array(
                'class-wp-rest-response.php' => "f",
                'fields' => array(
                    'class-wp-rest-term-meta-fields.php' => "f",
                    'class-wp-rest-post-meta-fields.php' => "f",
                    'class-wp-rest-meta-fields.php' => "f",
                    'class-wp-rest-user-meta-fields.php' => "f",
                    'class-wp-rest-comment-meta-fields.php' => "f"
                ),
                'endpoints' => array(
                    'class-wp-rest-edit-site-export-controller.php' => "f",
                    'class-wp-rest-block-patterns-controller.php' => "f",
                    'class-wp-rest-menu-locations-controller.php' => "f",
                    'class-wp-rest-block-renderer-controller.php' => "f",
                    'class-wp-rest-block-types-controller.php' => "f",
                    'class-wp-rest-taxonomies-controller.php' => "f",
                    'class-wp-rest-search-controller.php' => "f",
                    'class-wp-rest-terms-controller.php' => "f",
                    'class-wp-rest-url-details-controller.php' => "f",
                    'class-wp-rest-widget-types-controller.php' => "f",
                    'class-wp-rest-autosaves-controller.php' => "f",
                    'class-wp-rest-posts-controller.php' => "f",
                    'class-wp-rest-blocks-controller.php' => "f",
                    'class-wp-rest-block-directory-controller.php' => "f",
                    'class-wp-rest-settings-controller.php' => "f",
                    'class-wp-rest-pattern-directory-controller.php' => "f",
                    'class-wp-rest-block-pattern-categories-controller.php' => "f",
                    'class-wp-rest-widgets-controller.php' => "f",
                    'class-wp-rest-menu-items-controller.php' => "f",
                    'class-wp-rest-post-statuses-controller.php' => "f",
                    'class-wp-rest-templates-controller.php' => "f",
                    'class-wp-rest-post-types-controller.php' => "f",
                    'class-wp-rest-comments-controller.php' => "f",
                    'class-wp-rest-themes-controller.php' => "f",
                    'class-wp-rest-global-styles-controller.php' => "f",
                    'class-wp-rest-application-passwords-controller.php' => "f",
                    'class-wp-rest-plugins-controller.php' => "f",
                    'class-wp-rest-attachments-controller.php' => "f",
                    'class-wp-rest-revisions-controller.php' => "f",
                    'class-wp-rest-sidebars-controller.php' => "f",
                    'class-wp-rest-menus-controller.php' => "f",
                    'class-wp-rest-site-health-controller.php' => "f",
                    'class-wp-rest-users-controller.php' => "f",
                    'class-wp-rest-controller.php' => "f"
                ),
                'search' => array(
                    'class-wp-rest-search-handler.php' => "f",
                    'class-wp-rest-post-search-handler.php' => "f",
                    'class-wp-rest-post-format-search-handler.php' => "f",
                    'class-wp-rest-term-search-handler.php' => "f"
                ),
                'class-wp-rest-server.php' => "f",
                'class-wp-rest-request.php' => "f"
            ),
            'media-template.php' => "f",
            'class-wp.php' => "f",
            'images' => array(
                'blank.gif' => "f",
                'wpicons-2x.png' => "f",
                'toggle-arrow.png' => "f",
                'rss.png' => "f",
                'w-logo-blue-white-bg.png' => "f",
                'xit-2x.gif' => "f",
                'uploader-icons-2x.png' => "f",
                'wlw' => array(
                    'wp-comments.png' => "f",
                    'wp-watermark.png' => "f",
                    'wp-icon.png' => "f"
                ),
                'arrow-pointer-blue.png' => "f",
                'down_arrow.gif' => "f",
                'admin-bar-sprite-2x.png' => "f",
                'crystal' => array(
                    'document.png' => "f",
                    'audio.png' => "f",
                    'license.txt' => "f",
                    'default.png' => "f",
                    'text.png' => "f",
                    'interactive.png' => "f",
                    'spreadsheet.png' => "f",
                    'archive.png' => "f",
                    'video.png' => "f",
                    'code.png' => "f"
                ),
                'uploader-icons.png' => "f",
                'w-logo-blue.png' => "f",
                'down_arrow-2x.gif' => "f",
                'xit.gif' => "f",
                'wpspin-2x.gif' => "f",
                'wpicons.png' => "f",
                'arrow-pointer-blue-2x.png' => "f",
                'toggle-arrow-2x.png' => "f",
                'rss-2x.png' => "f",
                'media' => array(
                    'document.png' => "f",
                    'audio.png' => "f",
                    'default.png' => "f",
                    'text.png' => "f",
                    'interactive.png' => "f",
                    'spreadsheet.png' => "f",
                    'archive.png' => "f",
                    'video.png' => "f",
                    'code.png' => "f"
                ),
                'spinner-2x.gif' => "f",
                'smilies' => array(
                    'icon_sad.gif' => "f",
                    'icon_question.gif' => "f",
                    'mrgreen.png' => "f",
                    'icon_mrgreen.gif' => "f",
                    'icon_surprised.gif' => "f",
                    'frownie.png' => "f",
                    'icon_razz.gif' => "f",
                    'simple-smile.png' => "f",
                    'icon_neutral.gif' => "f",
                    'icon_eek.gif' => "f",
                    'icon_confused.gif' => "f",
                    'icon_biggrin.gif' => "f",
                    'icon_exclaim.gif' => "f",
                    'icon_idea.gif' => "f",
                    'icon_mad.gif' => "f",
                    'icon_cool.gif' => "f",
                    'icon_rolleyes.gif' => "f",
                    'rolleyes.png' => "f",
                    'icon_redface.gif' => "f",
                    'icon_wink.gif' => "f",
                    'icon_lol.gif' => "f",
                    'icon_arrow.gif' => "f",
                    'icon_evil.gif' => "f",
                    'icon_cry.gif' => "f",
                    'icon_smile.gif' => "f",
                    'icon_twisted.gif' => "f"
                ),
                'spinner.gif' => "f",
                'admin-bar-sprite.png' => "f",
                'icon-pointer-flag.png' => "f",
                'icon-pointer-flag-2x.png' => "f",
                'wpspin.gif' => "f"
            ),
            'template-loader.php' => "f",
            'class-wp-embed.php' => "f",
            'rss-functions.php' => "f",
            'class-wp-http-requests-response.php' => "f",
            'class-wp-ajax-response.php' => "f",
            'date.php' => "f",
            'class-wpdb.php' => "f",
            'class-wp-simplepie-file.php' => "f",
            'class-wp-theme-json-resolver.php' => "f",
            'meta.php' => "f",
            'kses.php' => "f",
            'class-wp-tax-query.php' => "f",
            'class-wp-network-query.php' => "f",
            'class-wp-hook.php' => "f",
            'ms-deprecated.php' => "f",
            'class-walker-category-dropdown.php' => "f",
            'class-wp-rewrite.php' => "f",
            'class-simplepie.php' => "f",
            'class.wp-styles.php' => "f",
            'class-wp-block-supports.php' => "f",
            'comment.php' => "f",
            'class-wp-theme-json-data.php' => "f",
            'sitemaps.php' => "f",
            'block-editor.php' => "f",
            'pomo' => array(
                'entry.php' => "f",
                'mo.php' => "f",
                'translations.php' => "f",
                'streams.php' => "f",
                'po.php' => "f",
                'plural-forms.php' => "f"
            ),
            'class-wp-customize-panel.php' => "f",
            'class-wp-http-cookie.php' => "f",
            'ms-default-filters.php' => "f",
            'class-walker-page.php' => "f",
            'bookmark.php' => "f",
            'customize' => array(
                'class-wp-customize-background-image-setting.php' => "f",
                'class-wp-customize-nav-menu-setting.php' => "f",
                'class-wp-customize-filter-setting.php' => "f",
                'class-wp-customize-date-time-control.php' => "f",
                'class-wp-customize-header-image-control.php' => "f",
                'class-wp-customize-nav-menus-panel.php' => "f",
                'class-wp-customize-nav-menu-item-control.php' => "f",
                'class-wp-customize-nav-menu-control.php' => "f",
                'class-wp-customize-custom-css-setting.php' => "f",
                'class-wp-widget-form-customize-control.php' => "f",
                'class-wp-customize-code-editor-control.php' => "f",
                'class-wp-widget-area-customize-control.php' => "f",
                'class-wp-customize-new-menu-control.php' => "f",
                'class-wp-customize-theme-control.php' => "f",
                'class-wp-customize-new-menu-section.php' => "f",
                'class-wp-customize-site-icon-control.php' => "f",
                'class-wp-customize-cropped-image-control.php' => "f",
                'class-wp-customize-upload-control.php' => "f",
                'class-wp-customize-image-control.php' => "f",
                'class-wp-customize-nav-menu-section.php' => "f",
                'class-wp-sidebar-block-editor-control.php' => "f",
                'class-wp-customize-background-image-control.php' => "f",
                'class-wp-customize-header-image-setting.php' => "f",
                'class-wp-customize-nav-menu-location-control.php' => "f",
                'class-wp-customize-partial.php' => "f",
                'class-wp-customize-nav-menu-auto-add-control.php' => "f",
                'class-wp-customize-sidebar-section.php' => "f",
                'class-wp-customize-themes-section.php' => "f",
                'class-wp-customize-themes-panel.php' => "f",
                'class-wp-customize-nav-menu-item-setting.php' => "f",
                'class-wp-customize-nav-menu-name-control.php' => "f",
                'class-wp-customize-nav-menu-locations-control.php' => "f",
                'class-wp-customize-background-position-control.php' => "f",
                'class-wp-customize-color-control.php' => "f",
                'class-wp-customize-media-control.php' => "f",
                'class-wp-customize-selective-refresh.php' => "f"
            ),
            'class-wp-customize-control.php' => "f",
            'class-wp-locale.php' => "f",
            'theme.php' => "f",
            'class-wp-user-query.php' => "f",
            'class-wp-http-ixr-client.php' => "f",
            'ms-functions.php' => "f",
            'SimplePie' => array(
                'Locator.php' => "f",
                'Author.php' => "f",
                'Sanitize.php' => "f",
                'Registry.php' => "f",
                'Caption.php' => "f",
                'HTTP' => array(
                    'Parser.php' => "f"
                ),
                'Item.php' => "f",
                'Exception.php' => "f",
                'File.php' => "f",
                'Rating.php' => "f",
                'Restriction.php' => "f",
                'IRI.php' => "f",
                'Parser.php' => "f",
                'Enclosure.php' => "f",
                'Cache.php' => "f",
                'XML' => array(
                    'Declaration' => array(
                        'Parser.php' => "f"
                    )
                ),
                'Decode' => array(
                    'HTML' => array(
                        'Entities.php' => "f"
                    )
                ),
                'Misc.php' => "f",
                'Copyright.php' => "f",
                'Parse' => array(
                    'Date.php' => "f"
                ),
                'Content' => array(
                    'Type' => array(
                        'Sniffer.php' => "f"
                    )
                ),
                'Source.php' => "f",
                'Net' => array(
                    'IPv6.php' => "f"
                ),
                'Credit.php' => "f",
                'Cache' => array(
                    'DB.php' => "f",
                    'Memcache.php' => "f",
                    'MySQL.php' => "f",
                    'Redis.php' => "f",
                    'File.php' => "f",
                    'Base.php' => "f",
                    'Memcached.php' => "f"
                ),
                'Core.php' => "f",
                'gzdecode.php' => "f",
                'Category.php' => "f"
            ),
            'class-wp-textdomain-registry.php' => "f",
            'class-oembed.php' => "f",
            'class-wp-object-cache.php' => "f",
            'class-wp-dependencies.php' => "f",
            'assets' => array(
                'script-loader-react-refresh-runtime.php' => "f",
                'script-loader-react-refresh-entry.min.php' => "f",
                'script-loader-packages.min.php' => "f",
                'script-loader-react-refresh-runtime.min.php' => "f",
                'script-loader-packages.php' => "f",
                'script-loader-react-refresh-entry.php' => "f"
            ),
            'class-feed.php' => "f",
            'class-wp-term-query.php' => "f",
            'class-wp-application-passwords.php' => "f",
            'class-wp-fatal-error-handler.php' => "f",
            'ms-default-constants.php' => "f",
            'feed-rss2.php' => "f",
            'locale.php' => "f",
            'class-wp-list-util.php' => "f",
            'embed.php' => "f",
            'class-wp-customize-widgets.php' => "f",
            'class-wp-block-template.php' => "f",
            'feed-rss2-comments.php' => "f",
            'default-widgets.php' => "f",
            'class-wp-http.php' => "f",
            'class-wp-block-parser.php' => "f",
            'error-protection.php' => "f",
            'ID3' => array(
                'getid3.lib.php' => "f",
                'module.tag.lyrics3.php' => "f",
                'license.commercial.txt' => "f",
                'module.audio.dts.php' => "f",
                'license.txt' => "f",
                'getid3.php' => "f",
                'module.audio.ac3.php' => "f",
                'module.audio.flac.php' => "f",
                'module.tag.apetag.php' => "f",
                'module.audio-video.matroska.php' => "f",
                'module.audio-video.quicktime.php' => "f",
                'module.audio.ogg.php' => "f",
                'module.audio.mp3.php' => "f",
                'module.audio-video.flv.php' => "f",
                'readme.txt' => "f",
                'module.tag.id3v2.php' => "f",
                'module.audio-video.riff.php' => "f",
                'module.audio-video.asf.php' => "f",
                'module.tag.id3v1.php' => "f"
            ),
            'global-styles-and-settings.php' => "f",
            'embed-template.php' => "f",
            'update.php' => "f",
            'class-wp-block-editor-context.php' => "f",
            'theme.json' => "f",
            'comment-template.php' => "f",
            'nav-menu.php' => "f",
            'class-wp-query.php' => "f",
            'widgets.php' => "f",
            'formatting.php' => "f",
            'http.php' => "f",
            'class-wp-http-curl.php' => "f",
            'class-wp-block-pattern-categories-registry.php' => "f",
            'cache-compat.php' => "f",
            'feed-atom.php' => "f",
            'block-template.php' => "f",
            'feed.php' => "f",
            'class-wp-block-list.php' => "f",
            'class-wp-customize-nav-menus.php' => "f",
            'class-wp-http-encoding.php' => "f",
            'class-wp-theme.php' => "f",
            'canonical.php' => "f",
            'sodium_compat' => array(
                'lib' => array(
                    'namespaced.php' => "f",
                    'constants.php' => "f",
                    'php72compat_const.php' => "f",
                    'sodium_compat.php' => "f",
                    'php72compat.php' => "f",
                    'stream-xchacha20.php' => "f",
                    'ristretto255.php' => "f"
                ),
                'autoload-php7.php' => "f",
                'src' => array(
                    'Core32' => array(
                        'Salsa20.php' => "f",
                        'Util.php' => "f",
                        'BLAKE2b.php' => "f",
                        'HSalsa20.php' => "f",
                        'SipHash.php' => "f",
                        'Poly1305' => array(
                            'State.php' => "f"
                        ),
                        'XSalsa20.php' => "f",
                        'Poly1305.php' => "f",
                        'X25519.php' => "f",
                        'Int64.php' => "f",
                        'Curve25519.php' => "f",
                        'Int32.php' => "f",
                        'HChaCha20.php' => "f",
                        'Curve25519' => array(
                            'Fe.php' => "f",
                            'H.php' => "f",
                            'Ge' => array(
                                'Precomp.php' => "f",
                                'P2.php' => "f",
                                'P1p1.php' => "f",
                                'Cached.php' => "f",
                                'P3.php' => "f"
                            ),
                            'README.md' => "f"
                        ),
                        'ChaCha20.php' => "f",
                        'XChaCha20.php' => "f",
                        'Ed25519.php' => "f",
                        'ChaCha20' => array(
                            'Ctx.php' => "f",
                            'IetfCtx.php' => "f"
                        ),
                        'SecretStream' => array(
                            'State.php' => "f"
                        )
                    ),
                    'Compat.php' => "f",
                    'Crypto32.php' => "f",
                    'File.php' => "f",
                    'Core' => array(
                        'Salsa20.php' => "f",
                        'Util.php' => "f",
                        'BLAKE2b.php' => "f",
                        'HSalsa20.php' => "f",
                        'SipHash.php' => "f",
                        'Base64' => array(
                            'UrlSafe.php' => "f",
                            'Original.php' => "f",
                            'Common.php' => "f"
                        ),
                        'Poly1305' => array(
                            'State.php' => "f"
                        ),
                        'XSalsa20.php' => "f",
                        'Poly1305.php' => "f",
                        'X25519.php' => "f",
                        'Curve25519.php' => "f",
                        'HChaCha20.php' => "f",
                        'Curve25519' => array(
                            'Fe.php' => "f",
                            'H.php' => "f",
                            'Ge' => array(
                                'Precomp.php' => "f",
                                'P2.php' => "f",
                                'P1p1.php' => "f",
                                'Cached.php' => "f",
                                'P3.php' => "f"
                            ),
                            'README.md' => "f"
                        ),
                        'Ristretto255.php' => "f",
                        'ChaCha20.php' => "f",
                        'XChaCha20.php' => "f",
                        'Ed25519.php' => "f",
                        'ChaCha20' => array(
                            'Ctx.php' => "f",
                            'IetfCtx.php' => "f"
                        ),
                        'SecretStream' => array(
                            'State.php' => "f"
                        )
                    ),
                    'Crypto.php' => "f",
                    'SodiumException.php' => "f",
                    'PHP52' => array(
                        'SplFixedArray.php' => "f"
                    )
                ),
                'composer.json' => "f",
                'namespaced' => array(
                    'Compat.php' => "f",
                    'File.php' => "f",
                    'Core' => array(
                        'Salsa20.php' => "f",
                        'Util.php' => "f",
                        'BLAKE2b.php' => "f",
                        'HSalsa20.php' => "f",
                        'SipHash.php' => "f",
                        'Poly1305' => array(
                            'State.php' => "f"
                        ),
                        'Poly1305.php' => "f",
                        'X25519.php' => "f",
                        'Curve25519.php' => "f",
                        'Xsalsa20.php' => "f",
                        'HChaCha20.php' => "f",
                        'Curve25519' => array(
                            'Fe.php' => "f",
                            'H.php' => "f",
                            'Ge' => array(
                                'Precomp.php' => "f",
                                'P2.php' => "f",
                                'P1p1.php' => "f",
                                'Cached.php' => "f",
                                'P3.php' => "f"
                            )
                        ),
                        'ChaCha20.php' => "f",
                        'XChaCha20.php' => "f",
                        'Ed25519.php' => "f",
                        'ChaCha20' => array(
                            'Ctx.php' => "f",
                            'IetfCtx.php' => "f"
                        )
                    ),
                    'Crypto.php' => "f"
                ),
                'autoload.php' => "f",
                'LICENSE' => "f"
            ),
            'class-wp-post.php' => "f",
            'random_compat' => array(
                'random_bytes_dev_urandom.php' => "f",
                'random_bytes_libsodium.php' => "f",
                'random_bytes_libsodium_legacy.php' => "f",
                'error_polyfill.php' => "f",
                'random_bytes_openssl.php' => "f",
                'random.php' => "f",
                'random_bytes_mcrypt.php' => "f",
                'cast_to_int.php' => "f",
                'random_int.php' => "f",
                'random_bytes_com_dotnet.php' => "f",
                'byte_safe_strings.php' => "f"
            ),
            'feed-rss.php' => "f",
            'class-wp-date-query.php' => "f",
            'functions.wp-styles.php' => "f",
            'script-loader.php' => "f",
            'class-wp-image-editor-imagick.php' => "f",
            'block-patterns' => array(
                'quote.php' => "f",
                'heading-paragraph.php' => "f",
                'query-grid-posts.php' => "f",
                'text-two-columns.php' => "f",
                'social-links-shared-background-color.php' => "f",
                'query-medium-posts.php' => "f",
                'query-offset-posts.php' => "f",
                'text-two-columns-with-images.php' => "f",
                'text-three-columns-buttons.php' => "f",
                'query-small-posts.php' => "f",
                'three-buttons.php' => "f",
                'large-header-button.php' => "f",
                'two-buttons.php' => "f",
                'large-header.php' => "f",
                'query-standard-posts.php' => "f",
                'two-images.php' => "f",
                'query-large-title-posts.php' => "f"
            ),
            'theme-i18n.json' => "f",
            'class-wp-theme-json.php' => "f",
            'class-wp-comment-query.php' => "f",
            'wp-db.php' => "f"
        ),
        'wp-cron.php' => "f",
        'index.php' => "f",
        'xmlrpc.php' => "f",
        'wp-settings.php' => "f",
        'wp-trackback.php' => "f",
        'wp-admin' => array(
            'export.php' => "f",
            'media.php' => "f",
            'media-new.php' => "f",
            'freedoms.php' => "f",
            'my-sites.php' => "f",
            'edit-tags.php' => "f",
            'erase-personal-data.php' => "f",
            'edit-link-form.php' => "f",
            'ms-users.php' => "f",
            'customize.php' => "f",
            'user-new.php' => "f",
            'about.php' => "f",
            'export-personal-data.php' => "f",
            'edit-form-blocks.php' => "f",
            'load-scripts.php' => "f",
            'theme-editor.php' => "f",
            'load-styles.php' => "f",
            'setup-config.php' => "f",
            'upload.php' => "f",
            'tools.php' => "f",
            'upgrade.php' => "f",
            'edit-form-comment.php' => "f",
            'options-privacy.php' => "f",
            'themes.php' => "f",
            'users.php' => "f",
            'edit.php' => "f",
            'options-writing.php' => "f",
            'credits.php' => "f",
            'includes' => array(
                'class-custom-image-header.php' => "f",
                'image-edit.php' => "f",
                'export.php' => "f",
                'ms.php' => "f",
                'media.php' => "f",
                'class-wp-theme-install-list-table.php' => "f",
                'class-bulk-upgrader-skin.php' => "f",
                'class-wp-ms-themes-list-table.php' => "f",
                'class-wp-plugins-list-table.php' => "f",
                'class-wp-upgrader-skins.php' => "f",
                'class-wp-links-list-table.php' => "f",
                'class-walker-nav-menu-checklist.php' => "f",
                'class-wp-ms-users-list-table.php' => "f",
                'class-wp-privacy-data-export-requests-list-table.php' => "f",
                'class-bulk-theme-upgrader-skin.php' => "f",
                'template.php' => "f",
                'class-wp-press-this.php' => "f",
                'class-wp-filesystem-ftpsockets.php' => "f",
                'class-wp-site-health-auto-updates.php' => "f",
                'class-wp-internal-pointers.php' => "f",
                'class-wp-site-health.php' => "f",
                'image.php' => "f",
                'class-plugin-upgrader.php' => "f",
                'edit-tag-messages.php' => "f",
                'class-wp-upgrader.php' => "f",
                'class-theme-upgrader.php' => "f",
                'class-core-upgrader.php' => "f",
                'deprecated.php' => "f",
                'class-wp-comments-list-table.php' => "f",
                'class-wp-privacy-requests-table.php' => "f",
                'dashboard.php' => "f",
                'class-wp-themes-list-table.php' => "f",
                'plugin.php' => "f",
                'class-wp-posts-list-table.php' => "f",
                'upgrade.php' => "f",
                'class-file-upload-upgrader.php' => "f",
                'class-wp-filesystem-base.php' => "f",
                'class-wp-ajax-upgrader-skin.php' => "f",
                'class-wp-privacy-policy-content.php' => "f",
                'class-wp-media-list-table.php' => "f",
                'class-wp-filesystem-ftpext.php' => "f",
                'class-wp-ms-sites-list-table.php' => "f",
                'class-wp-users-list-table.php' => "f",
                'taxonomy.php' => "f",
                'credits.php' => "f",
                'class-wp-list-table.php' => "f",
                'noop.php' => "f",
                'revision.php' => "f",
                'class-language-pack-upgrader-skin.php' => "f",
                'class-wp-post-comments-list-table.php' => "f",
                'class-wp-privacy-data-removal-requests-list-table.php' => "f",
                'import.php' => "f",
                'menu.php' => "f",
                'class-wp-community-events.php' => "f",
                'class-pclzip.php' => "f",
                'user.php' => "f",
                'class-theme-installer-skin.php' => "f",
                'class-wp-filesystem-ssh2.php' => "f",
                'class-wp-upgrader-skin.php' => "f",
                'plugin-install.php' => "f",
                'class-wp-screen.php' => "f",
                'post.php' => "f",
                'class-wp-importer.php' => "f",
                'list-table.php' => "f",
                'class-ftp-pure.php' => "f",
                'class-bulk-plugin-upgrader-skin.php' => "f",
                'continents-cities.php' => "f",
                'class-wp-site-icon.php' => "f",
                'theme-install.php' => "f",
                'class-wp-plugin-install-list-table.php' => "f",
                'class-wp-application-passwords-list-table.php' => "f",
                'admin.php' => "f",
                'options.php' => "f",
                'class-plugin-upgrader-skin.php' => "f",
                'class-language-pack-upgrader.php' => "f",
                'class-plugin-installer-skin.php' => "f",
                'translation-install.php' => "f",
                'misc.php' => "f",
                'class-wp-terms-list-table.php' => "f",
                'class-wp-debug-data.php' => "f",
                'class-walker-category-checklist.php' => "f",
                'ms-deprecated.php' => "f",
                'class-wp-automatic-updater.php' => "f",
                'comment.php' => "f",
                'screen.php' => "f",
                'bookmark.php' => "f",
                'admin-filters.php' => "f",
                'theme.php' => "f",
                'class-wp-list-table-compat.php' => "f",
                'class-automatic-upgrader-skin.php' => "f",
                'privacy-tools.php' => "f",
                'class-walker-nav-menu-edit.php' => "f",
                'class-custom-background.php' => "f",
                'network.php' => "f",
                'class-ftp.php' => "f",
                'update.php' => "f",
                'nav-menu.php' => "f",
                'widgets.php' => "f",
                'class-wp-filesystem-direct.php' => "f",
                'schema.php' => "f",
                'update-core.php' => "f",
                'ms-admin-filters.php' => "f",
                'class-theme-upgrader-skin.php' => "f",
                'ajax-actions.php' => "f",
                'file.php' => "f",
                'class-ftp-sockets.php' => "f",
                'meta-boxes.php' => "f"
            ),
            'ms-options.php' => "f",
            'link.php' => "f",
            'revision.php' => "f",
            'privacy.php' => "f",
            'js' => array(
                'xfn.min.js' => "f",
                'theme-plugin-editor.min.js' => "f",
                'comment.min.js' => "f",
                'tags.min.js' => "f",
                'auth-app.min.js' => "f",
                'theme.js' => "f",
                'press-this.js' => "f",
                'privacy-tools.min.js' => "f",
                'user-profile.min.js' => "f",
                'media.js' => "f",
                'word-count.min.js' => "f",
                'password-strength-meter.min.js' => "f",
                'editor.min.js' => "f",
                'site-health.js' => "f",
                'media-gallery.js' => "f",
                'language-chooser.js' => "f",
                'custom-background.js' => "f",
                'widgets' => array(
                    'media-image-widget.min.js' => "f",
                    'media-image-widget.js' => "f",
                    'text-widgets.min.js' => "f",
                    'media-gallery-widget.js' => "f",
                    'media-audio-widget.js' => "f",
                    'text-widgets.js' => "f",
                    'custom-html-widgets.min.js' => "f",
                    'media-gallery-widget.min.js' => "f",
                    'media-widgets.min.js' => "f",
                    'media-video-widget.min.js' => "f",
                    'media-audio-widget.min.js' => "f",
                    'media-video-widget.js' => "f",
                    'custom-html-widgets.js' => "f",
                    'media-widgets.js' => "f"
                ),
                'widgets.js' => "f",
                'widgets.min.js' => "f",
                'accordion.min.js' => "f",
                'wp-fullscreen-stub.min.js' => "f",
                'editor.js' => "f",
                'inline-edit-tax.min.js' => "f",
                'theme-plugin-editor.js' => "f",
                'password-strength-meter.js' => "f",
                'image-edit.min.js' => "f",
                'editor-expand.min.js' => "f",
                'inline-edit-post.js' => "f",
                'color-picker.js' => "f",
                'user-suggest.min.js' => "f",
                'edit-comments.min.js' => "f",
                'link.js' => "f",
                'plugin-install.min.js' => "f",
                'xfn.js' => "f",
                'media.min.js' => "f",
                'revisions.min.js' => "f",
                'accordion.js' => "f",
                'color-picker.min.js' => "f",
                'common.js' => "f",
                'svg-painter.js' => "f",
                'wp-fullscreen.min.js' => "f",
                'set-post-thumbnail.min.js' => "f",
                'post.js' => "f",
                'application-passwords.min.js' => "f",
                'farbtastic.js' => "f",
                'gallery.js' => "f",
                'image-edit.js' => "f",
                'site-health.min.js' => "f",
                'link.min.js' => "f",
                'tags-box.js' => "f",
                'customize-nav-menus.js' => "f",
                'nav-menu.js' => "f",
                'media-gallery.min.js' => "f",
                'nav-menu.min.js' => "f",
                'editor-expand.js' => "f",
                'word-count.js' => "f",
                'privacy-tools.js' => "f",
                'code-editor.js' => "f",
                'post.min.js' => "f",
                'press-this.min.js' => "f",
                'customize-widgets.min.js' => "f",
                'theme.min.js' => "f",
                'wp-fullscreen.js' => "f",
                'edit-comments.js' => "f",
                'inline-edit-tax.js' => "f",
                'customize-controls.js' => "f",
                'user-profile.js' => "f",
                'media-upload.js' => "f",
                'revisions.js' => "f",
                'dashboard.min.js' => "f",
                'dashboard.js' => "f",
                'comment.js' => "f",
                'customize-controls.min.js' => "f",
                'custom-header.js' => "f",
                'inline-edit-post.min.js' => "f",
                'set-post-thumbnail.js' => "f",
                'wp-fullscreen-stub.js' => "f",
                'updates.js' => "f",
                'code-editor.min.js' => "f",
                'updates.min.js' => "f",
                'iris.min.js' => "f",
                'svg-painter.min.js' => "f",
                'common.min.js' => "f",
                'customize-nav-menus.min.js' => "f",
                'application-passwords.js' => "f",
                'bookmarklet.min.js' => "f",
                'tags.js' => "f",
                'customize-widgets.js' => "f",
                'gallery.min.js' => "f",
                'bookmarklet.js' => "f",
                'postbox.js' => "f",
                'tags-suggest.min.js' => "f",
                'auth-app.js' => "f",
                'postbox.min.js' => "f",
                'tags-box.min.js' => "f",
                'tags-suggest.js' => "f",
                'language-chooser.min.js' => "f",
                'plugin-install.js' => "f",
                'user-suggest.js' => "f",
                'custom-background.min.js' => "f",
                'media-upload.min.js' => "f"
            ),
            'css' => array(
                'forms-rtl.min.css' => "f",
                'customize-controls-rtl.css' => "f",
                'site-icon.min.css' => "f",
                'themes-rtl.min.css' => "f",
                'press-this-editor-rtl.css' => "f",
                'revisions-rtl.min.css' => "f",
                'press-this-rtl.css' => "f",
                'wp-admin.css' => "f",
                'install-rtl.css' => "f",
                'code-editor-rtl.min.css' => "f",
                'widgets.min.css' => "f",
                'themes-rtl.css' => "f",
                'list-tables-rtl.css' => "f",
                'press-this-editor.min.css' => "f",
                'forms.min.css' => "f",
                'customize-nav-menus-rtl.css' => "f",
                'l10n.css' => "f",
                'list-tables.min.css' => "f",
                'press-this.css' => "f",
                'media-rtl.css' => "f",
                'customize-nav-menus.css' => "f",
                'admin-menu.css' => "f",
                'color-picker-rtl.min.css' => "f",
                'login-rtl.min.css' => "f",
                'press-this-editor.css' => "f",
                'color-picker-rtl.css' => "f",
                'nav-menus.css' => "f",
                'site-icon-rtl.css' => "f",
                'ie.css' => "f",
                'edit.css' => "f",
                'edit.min.css' => "f",
                'media.min.css' => "f",
                'about-rtl.min.css' => "f",
                'widgets-rtl.min.css' => "f",
                'common.min.css' => "f",
                'customize-nav-menus.min.css' => "f",
                'press-this-rtl.min.css' => "f",
                'revisions-rtl.css' => "f",
                'farbtastic.min.css' => "f",
                'install-rtl.min.css' => "f",
                'themes.css' => "f",
                'l10n-rtl.min.css' => "f",
                'edit-rtl.min.css' => "f",
                'nav-menus-rtl.min.css' => "f",
                'site-icon-rtl.min.css' => "f",
                'media-rtl.min.css' => "f",
                'colors' => array(
                    'sunrise' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    'blue' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    'ectoplasm' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    'modern' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    '_mixins.scss' => "f",
                    '_admin.scss' => "f",
                    'coffee' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    'ocean' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    'light' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    'midnight' => array(
                        'colors.min.css' => "f",
                        'colors.scss' => "f",
                        'colors-rtl.min.css' => "f",
                        'colors.css' => "f",
                        'colors-rtl.css' => "f"
                    ),
                    '_variables.scss' => "f"
                ),
                'revisions.min.css' => "f",
                'about.min.css' => "f",
                'common-rtl.min.css' => "f",
                'customize-nav-menus-rtl.min.css' => "f",
                'customize-controls-rtl.min.css' => "f",
                'site-health.css' => "f",
                'code-editor-rtl.css' => "f",
                'edit-rtl.css' => "f",
                'forms.css' => "f",
                'wp-admin-rtl.min.css' => "f",
                'code-editor.css' => "f",
                'customize-widgets-rtl.min.css' => "f",
                'color-picker.min.css' => "f",
                'l10n-rtl.css' => "f",
                'site-icon.css' => "f",
                'customize-widgets.css' => "f",
                'color-picker.css' => "f",
                'dashboard.css' => "f",
                'deprecated-media-rtl.css' => "f",
                'press-this-editor-rtl.min.css' => "f",
                'deprecated-media.min.css' => "f",
                'customize-widgets.min.css' => "f",
                'admin-menu-rtl.min.css' => "f",
                'widgets.css' => "f",
                'revisions.css' => "f",
                'dashboard-rtl.css' => "f",
                'admin-menu-rtl.css' => "f",
                'widgets-rtl.css' => "f",
                'login.min.css' => "f",
                'nav-menus-rtl.css' => "f",
                'ie-rtl.min.css' => "f",
                'ie.min.css' => "f",
                'install.css' => "f",
                'press-this.min.css' => "f",
                'about.css' => "f",
                'admin-menu.min.css' => "f",
                'login-rtl.css' => "f",
                'farbtastic.css' => "f",
                'dashboard.min.css' => "f",
                'forms-rtl.css' => "f",
                'l10n.min.css' => "f",
                'ie-rtl.css' => "f",
                'themes.min.css' => "f",
                'install.min.css' => "f",
                'site-health.min.css' => "f",
                'customize-widgets-rtl.css' => "f",
                'farbtastic-rtl.css' => "f",
                'wp-admin-rtl.css' => "f",
                'common.css' => "f",
                'list-tables.css' => "f",
                'customize-controls.css' => "f",
                'common-rtl.css' => "f",
                'login.css' => "f",
                'dashboard-rtl.min.css' => "f",
                'list-tables-rtl.min.css' => "f",
                'nav-menus.min.css' => "f",
                'deprecated-media.css' => "f",
                'about-rtl.css' => "f",
                'customize-controls.min.css' => "f",
                'site-health-rtl.css' => "f",
                'site-health-rtl.min.css' => "f",
                'media.css' => "f",
                'code-editor.min.css' => "f",
                'deprecated-media-rtl.min.css' => "f",
                'farbtastic-rtl.min.css' => "f",
                'wp-admin.min.css' => "f"
            ),
            'site-health-info.php' => "f",
            'user' => array(
                'freedoms.php' => "f",
                'about.php' => "f",
                'credits.php' => "f",
                'privacy.php' => "f",
                'menu.php' => "f",
                'user-edit.php' => "f",
                'admin.php' => "f",
                'profile.php' => "f",
                'index.php' => "f"
            ),
            'options-permalink.php' => "f",
            'import.php' => "f",
            'edit-form-advanced.php' => "f",
            'menu.php' => "f",
            'term.php' => "f",
            'custom-background.php' => "f",
            'link-manager.php' => "f",
            'network' => array(
                'freedoms.php' => "f",
                'site-info.php' => "f",
                'setup.php' => "f",
                'user-new.php' => "f",
                'about.php' => "f",
                'theme-editor.php' => "f",
                'upgrade.php' => "f",
                'themes.php' => "f",
                'users.php' => "f",
                'edit.php' => "f",
                'credits.php' => "f",
                'privacy.php' => "f",
                'site-themes.php' => "f",
                'menu.php' => "f",
                'site-settings.php' => "f",
                'user-edit.php' => "f",
                'plugin-install.php' => "f",
                'theme-install.php' => "f",
                'settings.php' => "f",
                'admin.php' => "f",
                'profile.php' => "f",
                'plugins.php' => "f",
                'site-new.php' => "f",
                'index.php' => "f",
                'site-users.php' => "f",
                'plugin-editor.php' => "f",
                'sites.php' => "f",
                'update.php' => "f",
                'update-core.php' => "f"
            ),
            'install-helper.php' => "f",
            'user-edit.php' => "f",
            'async-upload.php' => "f",
            'plugin-install.php' => "f",
            'post.php' => "f",
            'options-discussion.php' => "f",
            'widgets-form.php' => "f",
            'theme-install.php' => "f",
            'admin.php' => "f",
            'options-reading.php' => "f",
            'options.php' => "f",
            'images' => array(
                'freedom-2.svg' => "f",
                'about-color-palette.svg' => "f",
                'list.png' => "f",
                'icons32-vs-2x.png' => "f",
                'resize-rtl.gif' => "f",
                'about-texture.png' => "f",
                'privacy.png' => "f",
                'about-header-privacy.svg' => "f",
                'media-button.png' => "f",
                'comment-grey-bubble-2x.png' => "f",
                'freedom-4.svg' => "f",
                'menu-2x.png' => "f",
                'xit-2x.gif' => "f",
                'sort-2x.gif' => "f",
                'media-button-video.gif' => "f",
                'about-color-palette-vert.svg' => "f",
                'icons32.png' => "f",
                'bubble_bg.gif' => "f",
                'media-button-other.gif' => "f",
                'menu.png' => "f",
                'browser-rtl.png' => "f",
                'align-left-2x.png' => "f",
                'browser.png' => "f",
                'sort.gif' => "f",
                'media-button-2x.png' => "f",
                'post-formats.png' => "f",
                'align-center.png' => "f",
                'icons32-2x.png' => "f",
                'resize-2x.gif' => "f",
                'imgedit-icons.png' => "f",
                'align-right-2x.png' => "f",
                'wpspin_light.gif' => "f",
                'freedom-3.svg' => "f",
                'wordpress-logo.svg' => "f",
                'wheel.png' => "f",
                'about-header-brushes.svg' => "f",
                'list-2x.png' => "f",
                'resize.gif' => "f",
                'w-logo-blue.png' => "f",
                'xit.gif' => "f",
                'imgedit-icons-2x.png' => "f",
                'stars.png' => "f",
                'arrows-2x.png' => "f",
                'about-badge.svg' => "f",
                'menu-vs.png' => "f",
                'about-header-credits.svg' => "f",
                'align-right.png' => "f",
                'align-left.png' => "f",
                'post-formats32.png' => "f",
                'freedoms.png' => "f",
                'align-none-2x.png' => "f",
                'wpspin_light-2x.gif' => "f",
                'freedom-1.svg' => "f",
                'wordpress-logo-white.svg' => "f",
                'resize-rtl-2x.gif' => "f",
                'w-logo-white.png' => "f",
                'se.png' => "f",
                'media-button-image.gif' => "f",
                'yes.png' => "f",
                'wordpress-logo.png' => "f",
                'generic.png' => "f",
                'icons32-vs.png' => "f",
                'menu-vs-2x.png' => "f",
                'align-center-2x.png' => "f",
                'media-button-music.gif' => "f",
                'spinner-2x.gif' => "f",
                'align-none.png' => "f",
                'spinner.gif' => "f",
                'about-header-about.svg' => "f",
                'comment-grey-bubble.png' => "f",
                'arrows.png' => "f",
                'privacy.svg' => "f",
                'date-button-2x.gif' => "f",
                'stars-2x.png' => "f",
                'no.png' => "f",
                'date-button.gif' => "f",
                'bubble_bg-2x.gif' => "f",
                'post-formats-vs.png' => "f",
                'post-formats32-vs.png' => "f",
                'about-header-freedoms.svg' => "f",
                'loading.gif' => "f",
                'mask.png' => "f",
                'marker.png' => "f"
            ),
            'profile.php' => "f",
            'plugins.php' => "f",
            'install.php' => "f",
            'options-head.php' => "f",
            'link-parse-opml.php' => "f",
            'admin-functions.php' => "f",
            'moderation.php' => "f",
            'comment.php' => "f",
            'index.php' => "f",
            'ms-sites.php' => "f",
            'site-health.php' => "f",
            'link-add.php' => "f",
            'edit-comments.php' => "f",
            'plugin-editor.php' => "f",
            'maint' => array(
                'repair.php' => "f"
            ),
            'nav-menus.php' => "f",
            'privacy-policy-guide.php' => "f",
            'network.php' => "f",
            'custom-header.php' => "f",
            'options-general.php' => "f",
            'options-media.php' => "f",
            'post-new.php' => "f",
            'update.php' => "f",
            'widgets.php' => "f",
            'ms-upgrade-network.php' => "f",
            'media-upload.php' => "f",
            'widgets-form-blocks.php' => "f",
            'admin-header.php' => "f",
            'update-core.php' => "f",
            'ms-themes.php' => "f",
            'ms-delete-site.php' => "f",
            'upgrade-functions.php' => "f",
            'menu-header.php' => "f",
            'ms-admin.php' => "f",
            'site-editor.php' => "f",
            'authorize-application.php' => "f",
            'admin-footer.php' => "f",
            'ms-edit.php' => "f",
            'press-this.php' => "f",
            'admin-ajax.php' => "f",
            'admin-post.php' => "f",
            'edit-tag-form.php' => "f"
        )
    );
