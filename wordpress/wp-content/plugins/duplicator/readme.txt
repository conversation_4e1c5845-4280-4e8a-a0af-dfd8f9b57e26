=== Duplicator - WordPress Migration & Backup Plugin ===
Contributors: corylamleorg, bobriley
Tags: migration, backup, duplicate, move, migrate, restore, transfer, clone, automate, copy site, migrator
Requires at least: 4.0
Tested up to: 6.1
Requires PHP: 5.3.8
Stable tag: 1.5.3
License: GPLv2
WordPress migration and backups are much easier with Duplicator! Clone, backup, move and transfer an entire site from one location to another.

== Description ==

> With over **30 million downloads** Duplicator successfully gives WordPress users the ability to migrate, copy, move or clone a site from one location to another and also serves as a simple backup utility. Duplicator handles serialized and base64 serialized replacements.  Standard WordPress migration and WordPress backups are easily handled by this plugin as are **zero downtime migrations**.

For complete details visit [snapcreek.com](https://snapcreek.com/duplicator/?utm_source=duplicator_free&utm_medium=wp_org&utm_content=desc_details&utm_campaign=duplicator_free).

= Quick Video Demo =
http://www.youtube.com/watch?v=oc73jtvHWYQ

= Overview =
Duplicator is the most powerful migrator available. It enables you to:

* Move, migrate or clone a WordPress site between domains or hosts with **zero downtime**
* Pull down a live site to localhost for development
* Transfer a WordPress site from one host to another
* Manually backup a WordPress site or parts of a site
* Duplicate a live site to a staging area or vice versa
* Bundle up an entire WordPress site for easy reuse or distribution
* Perform a full WordPress migration without struggling with messy import/export sql scripts

= Migrate WordPress and Run WordPress Backups =
Duplicator creates a package that bundles all the site's plugins, themes, content, database and WordPress files into a simple zip file called a package. This package can then be used to easily migrate a WordPress site to any location you wish.  Move on the same server, across servers and pretty much any location a WordPress site can be hosted.  *WordPress is not required for installation* since the package contains all site files.

= Improve Your Workflow with Pre-Bundled Sites =
Duplicator lets you make your own preconfigured sites to eliminate rework.  Instead of manually configuring your favorite theme, set of plugins or content over and over, now just configure a single site and bundle it up into a Duplicator package. Once you have the bundled site, you can migrate the WordPress site over and over to different locations to instantly create many preconfigured sites!

= Duplicator Pro =
Duplicator Pro takes Duplicator to the next level with features you'll really appreciate, such as:

* Drag and Drop installs - just drag an archive to the destination site!
* Scheduled backups
* Cloud Storage to Dropbox, Google Drive, Microsoft OneDrive, Amazon S3 and FTP/SFTP
* A special 2-step streamlined installer mode for mega-fast installs
* Recovery Points added for very fast emergency site restores
* Support for Managed hosts such as WordPress.com, WPEngine, GoDaddy Managed, and more
* Multi-threaded to support larger web sites &amp; databases
* Migrate an entire multisite WordPress network in one shot
* Install a multisite subsite as a new standalone website
* Database and user creation *in the installer* with cPanel API
* Connect to cPanel directly from installer
* Custom plugin hooks for developers
* Email notifications
* Professional support
* ... and much more!

Check out [Duplicator Pro](https://snapcreek.com/duplicator/?utm_source=duplicator_free&utm_medium=wp_org&utm_content=wpo_premium&utm_campaign=duplicator_pro) today!

= Please Note =
The underlying logic to backup WordPress, move WordPress and transfer WordPress are complex and it's impossible to know how each system is setup; this is why your feedback is important to us.  Thanks for helping us to make WordPress the best blogging platform in the world.

= Disclaimer =
This plugin does require some technical knowledge.  If you plan to migrate WordPress or backup WordPress please use it at your own risk and don't forget to back up your files and databases beforehand. If you need to move or backup WordPress and would like additional help please visit the Duplicator [resources section](https://snapcreek.com/duplicator/docs/faqs-tech?utm_source=duplicator_free&utm_medium=wp_org&utm_content=free_disclaimer&utm_campaign=duplicator_free#faq-resource-030-q) .

= Active Contributors =
<li>[Andrea Leoni](https://profiles.wordpress.org/andreamk/) (Development)</li>
<li>[Paal Joachim Romdahl](http://www.easywebdesigntutorials.com) (Training)</li>
<li>[Hans-M. Herbrand](http://www.web266.de) (German) </li>
<li>[Nicolas Richer](http://nicolasricher.fr) (French)</li>

== Screenshots ==

1. Main Interface for all Packages
2. Create Package Step 1
3. Create Package Step 2
4. Build Process
5. Installer Screen

== Frequently Asked Questions ==

= Does Duplicator have a knowledge base or FAQ? =
Yes. Please see [all documents](https://snapcreek.com/duplicator/docs/?utm_source=duplicator_free&utm_medium=wp_org&utm_content=faq_docs&utm_campaign=duplicator_free) at snapcreek.com.

= Installation Instructions =
1. Upload `duplicator` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Click on the Duplicator link from the main menu
4. Check out the help by clicking the help icon and create your first package.

The Duplicator requires php 5.3 or higher.


= Are there any videos I can watch? =
Yes.  Please see the [video section](https://snapcreek.com/duplicator/docs/faqs-tech?utm_source=duplicator_free&utm_medium=wp_org&utm_content=faq_videos&utm_campaign=duplicator_free#faq-resource-070-q) on the FAQ.

= Is this plugin compatible with WordPress multisite (MU)? =
Duplicator isn't, however [Duplicator Pro](https://snapcreek.com/duplicator/?utm_source=duplicator_free&utm_medium=wp_org&utm_content=faq_dpro_multisiteinfo&utm_campaign=duplicator_pro) supports full multisite network migrations/backups and also can install a multisite subsite as a standalone site.

= Where can I get more help and support for this plugin? =
Visit the [Duplicator support](https://snapcreek.com/duplicator/docs/faqs-tech?utm_source=duplicator_free&utm_medium=wp_org&utm_content=faq_support&utm_campaign=duplicator_free#faq-resource-030-q) section at snapcreek.com


== Changelog ==

Please see the following url:
[https://snapcreek.com/duplicator/docs/changelog?lite](https://snapcreek.com/duplicator/docs/changelog?lite&utm_source=duplicator_free&utm_medium=wp_org&utm_content=changelog_support&utm_campaign=duplicator_free)



== Upgrade Notice ==

Please use our ticketing system when submitting your logs.  Please do not post to the forums.
