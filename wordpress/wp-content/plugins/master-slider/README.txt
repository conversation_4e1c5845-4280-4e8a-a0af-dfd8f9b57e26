=== Master Slider - Responsive Touch Slider ===
Contributors: averta, averta_support
Donate link: http://averta.net/
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl.html
Tags: slider, image slider, wordpress slider, video slider, best slider plugin, slideshow, swipe, touch slider, content slider, SEO, vertical slider, HTML5 slider, hardware accelerate, animation, mobile slider, iOS, android, youtube slider, horizontal slider, responsive slider, fullscreen slider, post slider, photo slider, online album, mobile slider, WordPress slider,wpml, ,ultisite, wistia, woocommerce, product slider, woocommerce slider, portfolio, gallery, lightweight slider
Requires PHP: 5.4
Requires at least: 4.6
Tested up to: 5.0.0
Stable tag: 3.5.3

Build SEO friendly sliders fast and easy with Master Slider. The most advanced responsive HTML5 WordPress slider plugin, with touch swipe navigation that works smoothly on desktop and devices.

== Description ==

>[Demos](http://avt.li/mswftem "Live demos of Master Slider free version") | [Features](http://avt.li/mswfea "All features") | [Documentation](http://avt.li/mswfdoc "Master Slider documentation") | [All Video tutorials](http://avt.li/msfvids "Watch all video tutorials") | [Support](http://wordpress.org/support/plugin/master-slider "Free support")

[youtube https://www.youtube.com/watch?v=nyjpGEHwOn8]


= Overview =

Master Slider is a free SEO friendly, responsive image and video slider that truly works on all major devices, and it has super smooth hardware accelerated transitions. It supports touch navigation with pure swipe gesture that you have never experienced before.
With our 8 Starter Samples, creating slideshows has never been so fast and enjoyable!

>Looking for a perfect Free WordPress theme optimized for both Master Slider and Elementor? [Get Phlox theme](http://avt.li/msphpp "Phlox theme - Free Minimal and Responsive WordPress Theme")


Master Slider is built using WordPress best practices both on the front and the back end. This results in an efficient, robust and intuitive plugin. It works with any theme, including WordPress Default Themes.



= Features =

• The Most SEO Friendly Slider Plugin in Market!
• Easy to Use Interface
• Simply Create Fully Responsive and Device Optimized Sliders with 8 starter samples
• Use Sliders Cross-browser and Works Well on All Popular Browsers! (Tested IE8+ and other modern browsers)
• Superlative Lightweight Outputs in Compare with Other Plugins
• HTML5 Valid and Clean Markups
• Touch Swipe Navigation
• Manage Slideshows with Autoplay Timing Options!
• 24h Support with Expert Agents (check out our rates on WordPress)
• Drag and Drop Slider Creation
• Extremely User Friendly Admin Panel
• CSS3 Transitions with jQuery Fallback
• Hardware Accelerated CSS3 3D Transforms
• Optimized for Any Screen Sizes and Touch Devices
• Smart Loading Assets
• 6+ Interactive Slide Transitions
• Smart auto crop
• Loop and linear sliding
• Shuffle Ordering Slides Option
• Auto-height Slider
• Vertical and Horizontal Direction Navigation
• Fully Customizable Thumbnail and Tabs
• Vertical and Horizontal Bullets
• Mouse Wheel Navigation
• Customizable Arrows
• Exclusive Widget and Shortcodes
• Scroll Handle Slide Indicator
• Smart Memory Management
• 6 Modern and Unique Skins
• Timer-bar and Circle Timer UI Controls
• Custom User Roles and Capabilities
• 5 Image Positioning Options (fill, fit, tile,...)
• Advanced Import And Export Tool
• Built-in Cache Boosting
• Translate Ready
• Multisite Compatible
• Extensive Developer API
* [Full List of Features](http://masterslider.com/wordpress/free/?mslf)


= Compatible Browsers =

* IE8+
* Firefox
* Safari
* Opera
* Chrome
* iOS browser
* Android browser


= Master Slider Pro Features =

* Full-width, Full-screen and Boxed Layout
* Post Slider - with Advanced Filtering Tool (Any Post-type)
* WooCommerce Product Slider - With Advanced Filtering Tool
* Flickr Slider - Make Slider Dynamically From Flickr Photosets or User Latest Photos
* Facebook Slider - Make Image Gallery Dynamically From Facebook Public Images
* HD Video Backgrounds for Slides
* Embedding YouTube and Vimeo Videos
* Animated Layers
* 4 Different Layer Types (Text, Image, Button and Video)
* Huge Verity of Layer Transitions
* Transition In and Out Available for Each Layer
* Hotspots and Tooltips over Slides
* Deep-linking
* Parallax Effect while Scrolling
* Layers Parallax Effect while Swiping
* Layers Parallax Effect while Moving Mouse over Slide
* Boxed with Visible Nearby Slides
* Auto Height and Auto Fill Layout
* Binding Special Actions to Layers (Go to slide, scroll down, play, pause, ..)
* Custom Pattern and Color Overlay
* Auto Resizing and Aligning Layers while Resizing
* Option to Linking Slides, Layers and Hotspots
* 30+ Ready to Use Sample Sliders (One Click Import)
* Nice Looking and Easy to Use Layout
* WYSIWYG Drag & Drop Editor
* Easy to Use Timeline animation tool
* Visual Style Editor
* Visual Transition Effect Editor
* Visual Button Editor
* Easy Aligning Layers over Stage
* Snapping Option on Drag & Drop Layers
* 9 Different Positioning Origin Points for Each Layer
* Realtime Animation Preview
* Moving Layers by Arrow Keys
* Using WordPress Native Text Editor
* Using Google Fonts (600+ Fonts)
* [Full List of Pro Features](http://masterslider.com/wordpress/pro/?mslf)


Upgrade to [Pro Version](http://masterslider.com/purchase/?mslf)


= Demo for PRO Version =

* [Master Slider Demo Sliders](http://avt.li/mswptem)
* [Master Slider Features](http://avt.li/mswpfea)
* [Master Slider Manual](http://avt.li/mswpdoc)


= Documentations =

If you have any question about working with "Master Slider", you can take a look at [online documentations](http://masterslider.com/doc/wp/free/)


= Translation =

French. Special thanks to [Thomas](http://www.thomasgrimaud.fr)
Brazilian Portuguese. Special thanks to [WebPress](http://www.themeforest.net/user/WebPress-CodeLayer)
Serbian. Special thanks to [Borisa Djuraskovic](http://www.webhostinghub.com)

You can start translating Master Slider by our [online translation service](http://translate.averta.net/projects/masterslider/free-version).


== Installation ==

= Minimum Requirements =

* WordPress 4.6 or greater
* PHP version 5.4 or greater
* MySQL version 5.0 or greater


**This section describes how to install the plugin and get it working**


= Automatic installation (easiest way) =

To do an automatic install of Master Slider, log in to your WordPress dashboard, navigate to the Plugins menu and click Add New.

In the search field type "Master Slider" and click Search Plugins. Once you have found it you can install it by simply clicking "Install Now".


= Manual installation =

**Uploading in WordPress Dashboard**

1. Download `master-slider.zip`
2. Navigate to the 'Add New' in the plugins dashboard
3. Navigate to the 'Upload' area
4. Select `master-slider.zip` from your computer
5. Click 'Install Now'
6. Activate the plugin in the Plugin dashboard

**Using FTP**

1. Download `master-slider.zip`
2. Extract the `master-slider` directory to your computer
3. Upload the `master-slider` directory to the `/wp-content/plugins/` directory
4. Activate the plugin in the Plugin dashboard

The WordPress codex contains [instructions on how to install a WordPress plugin](http://codex.wordpress.org/Managing_Plugins#Manual_Plugin_Installation).



= Updating =

You can use automatic update to update the plugin safely.


== Frequently Asked Questions ==

= Where can I find Master Slider documentation and user guides =

If you have any question about working with "Master Slider", you can take a look at [online documentations](http://docs.averta.net/display/MSWPF/)

= When I preview the slider in admin page everything looks fine, but when I add it to my website the slider looks different. How can I fix that? =
It is a known issue with few poor coded themes that can be easily fixed. In fact, some styles in your theme conflicts with the styles in Master Slider. You can simply submit a ticket in support section, our support agents will advice you how to fix the conflicts.

= Will Master Slider work with my theme? =
Master Slider works with any theme, including the default WordPress themes.

= Can you recommend a free theme that is optimized for Master Slider =
Fortunately there are plenty of options out there, including the default WordPress themes too. But the one that we recommend most is [Phlox theme](http://avt.li/msphpp "Phlox theme - Free Minimal and Responsive WordPress Theme") which is a free, versatile and optimized theme for Master Slider.

= I added text to slide (slide info), but it does not appear in slider =
You need to add "slide info" control too. To do that, go for editing slider, under "slider controls" tab, select and add "slide info" control

= After I've removed a slider, it still appears on my website =
Maybe you have a cache plugin enabled on your website or Master Slider's built-in cache is enabled.

= Where can I report bugs? =
Bugs can be reported in our [support forums](http://wordpress.org/tags/master-slider).



== Screenshots ==

1. Slider without thumbnail (light skin 1)
2. Slider with thumbnail on bottom (light skin 1)
3. Slider with thumbnail on right (light skin 5)
4. Slider with thumbnail on left (light skin 3)
5. Slider with slide info on right (light skin 3)
6. Slider with slide info on bottom (light skin 1)
7. Sliders listing table
8. Slider setting
9. Drag and Drop slide creation
10. 6 Highly customizable slider controls
11. Flexible slider controls


== Changelog ==

= 3.5.3 =

= Version 3.5.3/ (01.11.2018) =
- [Fix]: Fixing an issue with deprecated functions.
- [Fix]: Compatibility with PHP 7.2 added.

= Version 3.5.1/ (06.05.2018) =
- [Fix]: Addressed an issue with displaying some sliders in the admin panel.

= Version 3.5.0/ (29.04.2018) =
- [Fix]: Addressed an issue that prevents calling the slider callbacks.
- [Fix]: An issue with accessing the sample sliders panel fixed.

= Version 3.4.4/ (18.04.2018) =
- [Fix]: An issue with dynamic tags in tab control fixed.
- [Improvement]: Bug fix and performance improvement.

= Version 3.4.1/ (17.12.2017) =
- [Fix]: Improvement in wp transient API while object cache is enabled on some web hosts.

= Version 3.3.0/ (25.11.2017) =
- [Improvement]: Improvements and compatibility for WordPress 4.9.0 added.

= Version 3.1.6/ (09.10.2017) =
- [Improvement]: Minor bugs fix.

= Version 3.1.1/ (17.08.2017) =
- [Improvement]: Performance improvement and bug fix.

= Version 3.0.2 / (12.07.2017) =
- [Improvement]: Some minor bugs fixed.

= Version 3.0.0 / (17.06.2017) =
- [Fix]: An issue with overlay color for slides fixed.
- [Improvement]: The corresponding styles will be generated after duplicating the slider automatically.
- [Improvement]: Some minor bugs fixed.

= Version 2.9.8 / (15.4.2017) =
- [Fix]: Italian translation added
- [Fix]: Minor bugs fixed

= Version 2.9.7 / (26.3.2017) =
- [Fix]: A minor issue with PHP 7 fixed

= Version 2.9.6 / (15.3.2017) =
- [Fix]: Minor bugs fixes

= Version 2.9.5 / (21.9.2016) =
- [Fix]: Minor bugs fixes

= Version 2.9.0 / (19.8.2016) =
- [Improvement]: Better compatibility with Legacy WordPress themes
- [Fix]: Minor bugs fixes

= Version 2.8.1 / (11.7.2016) =
- [Improvement]: Security improvement

= Version 2.7.1 / (4.10.2015) =
- [Fix]: Minor issue with an undefined variable fixed

= Version 2.7.0 / (26.9.2015) =
- [Improvement]: Now the slide info sections with no conent are no longer visible
- [Improvement]: Now Master Slider is fully compatible with new upcoming translation service on wp.org
- [Improvement]: Now auto crop is disabled for thumbnail when user set a custom thumbnail

= Version 2.6.4 / (22.9.2015) =
- [Fix]: Legacy widget constructor replaced with new one

= Version 2.6.3 / (12.9.2015) =
- [Fix]: Addressed an issue with undefined IDs while adding shortcodes to WordPress editor
- [New]: Russian translation by: oharreg

= Version 2.6.2 / (1.9.2015) =
- [Fix]: Security improvement.

= Version 2.5.1 / (12.7.2015) =
- [New]: Plugin thumbnail changed.

= Version 2.5.0 / (30.6.2015) =
- [Fix]: Addressed an issue with IE9.
- [Improvement]: Under the hood improvement

= Version 2.4.0 / (8.5.2015) =
- [Improvement]: Some improvement for serving assets under SSL.
- [Fix]: Default minHeight changed to 0.
- [Fix]: Some CSS reset rules added to links on slider.

= Version 2.4.0 / (7.5.2015) =
- [New]: Arrows for the thumblist control added.
- [New]: Search box for searching through the sliders added.
- [New]: New option `minHeight` added which specifies min height value for the slider, it prevents slider to be displayed tiny in small screens.
- [New]: New option for thumblist to change the slides while moving mouse cursor over thumbnails.
- [New]: Brand new sticky preview and save button with new interface in admin panel.
- [Fix]: A conflict with new API changes in WP 4.2 for gallery fixed.
- [Fix]: An issue with some special characters in slide info fixed.
- [Improvement]: Interface improvement for slider box.
- [Improvement]: Improvement in slider performance.
- [Fix]: An issue with sorting sliders base on date_modified fixed.
- [Fix]: Escaping the brackets for rel attribute on slides.
- [Fix]: Addressed an issue while auto hide in controls did not function properly on touch devices.
- [Fix]: Addressed an issue while inserting larger numbers than slides number in preload field.
- [Fix]: An issue with encoding brackets in custom styles fixed
- [Fix]: An issue in calculating thumbnails width fixed.
- [Fix]: An issue in aligning thumbnails which doesn't have fillMode option fixed.
- [Improvement]: Improvements under the hood.
- Master Slider is 100% secure in vulnerability check

= Version 2.2.1 / (6.4.2015) =
- [Fix]: Some minor bugs fixed.

= Version 2.2.1 / (21.3.2015) =
- [New]: Introducing "Start On Hover" feature, to display the slider when it appears in browser viewport.
- [Improvement]: API methods updated
- [Fix]: Some minor JavaScript errors fixed.

= Version 2.1.3 / (5.3.2015) =
- [Fix]: Address an issue with rendering 3D transitions on firefox and mouse wheel
- [New]: French. Special thanks to Thomas:www.thomasgrimaud.fr
- [New]: Brazilian Portuguese. Special thanks to WebPress
- [New]: Serbian. Special thanks to Borisa Djuraskovic:www.webhostinghub.com
- [Fix]: Throwing error after destroying slider with API destroy method.
- [Fix]: Addressed a Javascript error in IE9.
- [Fix]: Addressed a bug which prevents using bracket in slide title and alt

= Version 2.0.4 / (1.2.2015) =
- [New]: New API added to let developers change default values in admin panel

= Version 2.0.0 / (28.1.2015) =
- [New]: Introducing multiple image Upload
- [New]: Autoplay option for video slides added
- [New]: Custom css editor added to admin panel
- [Improvement]: Major improvement in linking slides
- [Improvement]: Some improvement in API for managing front end assets
- [Improvement]: improvement in aligning the content of tab control
- [Improvement]: User interface improvement
- [Improvement]: Lots of improvement in slider performance
- [Improvement]: Adding compatibility with wheel navigation by Magic Mouse or Trackpad in OS X
- [Fix]: Addressed and issue with rendering issue in latest version of Firefox
- [Fix]: An issue with slide link in latest version of Firefox fixed
- [Fix]: An conflict with modal window in WordPress 4.1 fixed
- [Fix]: An issue with a missing file fixed
- [Fix]: An issue with slider fallbacks fixed
- [Fix]: An issue with wheel navigation with non-loop slider fixed
- [Fix]: Fixed unexpected js errors when swipe navigation is disabled
- [Fix]: An issue removeEventListener method fixed
- [Update]: Annoying glow on buttons and links in admin panel removed
- [Update]: Slider base styles updated
- [Update]: Translate files updated

= Version 1.4.4 / (8.1.2015) =
- Addressed a bug which stopped the sliders in some WordPress site

= Version 1.4.2 / (7.1.2015) =
- [Improvement]: Improvement in loading front end assets
- [New]: New developer API added
- [Fix]: Minor bugs fixed

= Version 1.4.0 / (19.12.2014) =
- A conflict with modal in WordPress 4.1 fixed

= Version 1.3.9 / (22.11.2014) =
- Some class names renamed to eliminate the conflict with others themes and plugins.

= Version 1.3.8 / (16.11.2014) =
- Mino bugs fixed

= Version 1.3.6 / (22.10.2014) =
- Improvement in swipe on android devices

= Version 1.3.4 / (6.10.2014) =
- Improvement in loading admin assets

= Version 1.3.3 / (29.09.2014) =
- Improvement in Master Slider's built-in cache

= Version 1.3.2 / (28.09.2014) =
- Minor bugs fixed
- Translate file updated

= Version 1.3.1 / (22.09.2014) =
- Documentation updated

= Version 1.3.0 / (19.09.2014) =
- [Fix]: Addressed an issue with grap cursors in internet explorer
- [Fix]: An issue with creating styles on multisite fixed
- [New]: New developer API to change origin_uploads_url for importing medias
- [Improvement]: Improvement in admin interface

= Version 1.2.5 / (17.09.2014) =
- Performance Improvement
- Minor bugs fixed

= Version 1.2.1 / (11.09.2014) =
- Minor bug in admin panel fixed

= Version 1.2.0 / (08.09.2014) =
- [New Feature]: New button added to WordPress editor in order to add Master Slider shorcodes with just simple click
- [Improvement]: Improving responsiveness of admin panel
- [Improvement]: Interface compatibility with WordPress 4.0 in admin area applied

= Version 1.1.0 / (06.09.2014) =
* compatibility with WordPress 4.0.0 added
* Minor bugs fixed

= Version 1.0.10 / (07.08.2014) =
* Slide info is now shortcode enabled
* compatibility with WordPress 3.9.2 added

= Version 1.0.9 / (22.07.2014) =
* A minor bug in plugins page fixed

= Version 1.0.8 / (21.07.2014) =
* A link added to plugins page for rating this plugin

= Version 1.0.7 / (19.07.2014) =
* [New Feature]: New option added to global setting that let you load plugins's scripts on all pages.
			   ( Useful when loading slider via Ajax )
* An issue with text editor fixed

= Version 1.0.6 / (17.07.2014) =
* [New]: Introduction video added

= Version 1.0.5 / (15.07.2014) =
* [Fix]: An issue with uninstalling the plugin fixed

= Version 1.0.1 / (12.07.2014) =
* initial release



== Upgrade Notice ==

= Version 3.5.3/ (01.11.2018) =
- [Fix]: Fixing an issue with deprecated functions.
- [Fix]: Compatibility with PHP 7.2 added.
