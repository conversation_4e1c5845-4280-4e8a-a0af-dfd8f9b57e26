
/** masterslider admin menu icon **/
#toplevel_page_master-slider .menu-icon-generic div.wp-menu-image:before { content: ' ';}
#toplevel_page_master-slider .menu-icon-generic div.wp-menu-image {
	background: url( ../images/master-admin-menu.svg ) no-repeat 7px 7px !important;
	background-size: 23px auto !important;
	opacity:0.6;
}
.mce-i-msp_shortcodes_button {
	background: url( ../images/master-dark.svg ) no-repeat 0 2px !important;
	left: 3px;
	position: relative !important;
	top: -1px;
}
#toplevel_page_masterslider .menu-icon-generic.wp-menu-open div.wp-menu-image,
#toplevel_page_masterslider .menu-icon-generic.current div.wp-menu-image {
	opacity:1;
}
/** visual composer element **/
.icon-vc-msslider-el ,
.wpb_masterslider_pb .wpb_element_wrapper { background-image: url( ../images/thirdparty/master-slider-32x32.png ) !important; }

.msp-banner-wrapper.updated {
    margin-left:0;
    border:none;
    padding:0;
    clear:both;
    position:relative;
}
.msp-banner-wrapper.updated > p {
    padding:15px;
}
.msp-banner-wrapper .msp-banner-media {
    display:block;
    text-align:center;
    width:100%;
}
.msp-banner-wrapper .msp-banner-media > * {
    margin-left:auto;
    margin-right:auto;
}
.msp-banner-wrapper.updated .notice-dismiss{
    padding:7px;
    top:7px;
}
.msp-banner-wrapper > pre {
    outline: 1px solid #e2e2e2;
    padding: 15px;
    margin: 0;
}
