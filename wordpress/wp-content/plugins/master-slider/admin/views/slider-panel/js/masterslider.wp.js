/*! 
 * Master Slider WordPress Lite Panel 
 * Copyright © 2017 All Rights Reserved. 
 *
 * <AUTHOR> [www.averta.net]
 * @version 2.50.3
 * @date May 2017
 */
window.MSPanel=Ember.Application.create({rootElement:"#msp-root"}),MSPanel.version="2.50.3",MSPanel.SliderID=parseQueryString(window.location.search).slider_id||__MSP_SLIDER_ID||"100",MSPanel.SliderSlug=__MSP_SLIDER_ALIAS||"ms-"+MSPanel.SliderID,MSPanel.dependedControllers=[],String.prototype.jfmt=function(){return"".fmt.apply(this.replace(/%s|%d/,"%@"),arguments)},window.$=jQuery.noConflict(),jQuery.ui.dialog.prototype._focusTabbable=function(){},MSPanel.Router.map(function(){this.resource("settings"),this.resource("slides",{path:"/"}),this.resource("controls"),this.resource("callbacks"),this.resource("error"),this.resource("pro-features")}),MSPanel.Router.reopen({location:"none"}),MSPanel.ApplicationRoute=Ember.Route.extend({model:function(){var setting=MSPanel.Settings.find();0===setting.get("length")&&MSPanel.Settings.create().save()}}),MSPanel.SettingsRoute=Ember.Route.extend({model:function(){return MSPanel.Settings.find(1)},setupController:function(controller,model){controller.set("model",model),controller.setup()}}),MSPanel.SlidesRoute=Ember.Route.extend({model:function(){return MSPanel.Slide.find()},setupController:function(controller,model){controller.set("model",model),controller.set("sliderSettings",MSPanel.Settings.find(1)),controller.setup()}}),MSPanel.ControlsRoute=Ember.Route.extend({model:function(){return MSPanel.Control.find()},setupController:function(controller,model){controller.set("model",model),controller.setup(),this.activate()},activate:function(){var controller=this.get("controller");controller&&controller.set("controlOptions","empty-template")}}),MSPanel.CallbacksRoute=Ember.Route.extend({model:function(){return MSPanel.Callback.find()},setupController:function(controller,model){controller.set("model",model),controller.setup()}}),function(){var attr=Ember.attr,hasMany=Ember.hasMany,belongsTo=Ember.belongsTo,regp=/https\:|http\:/,WPPath={serialize:function(path){return void 0==path?path:regp.test(path)?path.replace(__MS.upload_dir,""):path.replace("/wp-content/uploads","")},deserialize:function(path){return void 0==path?path:regp.test(path)?path:__MS.upload_dir+path}},defaults=window.__MSP_DEF_OPTIONS||{};MSPanel.Settings=Ember.Model.extend({id:attr("number"),snapping:attr("boolean",{defaultValue:!0}),bgImageThumb:attr(WPPath),disableControls:attr("boolean",{defaultValue:!1}),name:attr("string",{defaultValue:__MSP_LAN.sm_001}),slug:attr("string"),width:attr("number",{defaultValue:defaults.width||1e3}),height:attr("number",{defaultValue:defaults.height||500}),wrapperWidth:attr("number"),minHeight:attr("number"),wrapperWidthUnit:attr("string",{defaultValue:"px"}),autoCrop:attr("boolean",{defaultValue:defaults.autoCrop||!1}),type:attr("string"),sliderId:attr("string"),autofillTarget:attr("string"),enableOverlayLayers:attr("boolean",{defaultValue:defaults.enableOverlayLayers||!0}),layout:attr("string",{defaultValue:defaults.layout||"boxed"}),autoHeight:attr("boolean",{defaultValue:defaults.autoHeight||!1}),trView:attr("string",{defaultValue:defaults.transition||"basic"}),speed:attr("number",{defaultValue:defaults.speed||20}),space:attr("number",{defaultValue:defaults.space||0}),start:attr("number",{defaultValue:defaults.start}),grabCursor:attr("boolean",{defaultValue:defaults.grabCursor}),swipe:attr("boolean",{defaultValue:defaults.swipe}),mouse:attr("boolean",{defaultValue:defaults.mouse}),wheel:attr("boolean",{defaultValue:defaults.wheel}),keyboard:attr("boolean",{defaultValue:defaults.keyboard}),autoplay:attr("boolean",{defaultValue:defaults.autoplay}),loop:attr("boolean",{defaultValue:defaults.loop}),shuffle:attr("boolean",{defaultValue:defaults.shuffle}),preload:attr("string",{defaultValue:defaults.preload}),overPause:attr("boolean",{defaultValue:defaults.overPause}),endPause:attr("boolean",{defaultValue:defaults.endPause}),hideLayers:attr("boolean",{defaultValue:defaults.hideLayers}),dir:attr("string",{defaultValue:defaults.dir}),parallaxMode:attr("srting",{defaultValue:defaults.parallaxMode}),useDeepLink:attr("string",{defaultValue:!1}),deepLink:attr("string"),deepLinkType:attr("string",{defaultValue:"path"}),mobileBGVideo:attr("boolean",{defaultValue:defaults.mobileBGVideo}),startOnAppear:attr("boolean",{defaultValue:defaults.startOnAppear}),scrollParallax:attr("boolean"),scrollParallaxMove:attr("number",{defaultValue:30}),scrollParallaxBGMove:attr("number",{defaultValue:50}),scrollParallaxFade:attr("boolean",{defaultValue:!0}),centerControls:attr("boolean",{defaultValue:defaults.centerControls}),instantShowLayers:attr("boolean",{defaultValue:defaults.instantShowLayers}),fullscreenMargin:attr("number"),inlineStyle:attr("string"),className:attr("string",{defaultValue:defaults.className}),bgColor:attr("string"),bgImage:attr(WPPath),customStyle:attr("string"),skin:attr("string",{defaultValue:defaults.skin}),msTemplate:attr("string",{defaultValue:"custom"}),msTemplateClass:attr("string",{defaultValue:""}),usedFonts:attr("string"),fbtoken:attr("string"),apiKey:attr("string"),setId:attr("string"),setType:attr("string"),imgCount:attr("number"),thumbSize:attr("srting"),imgSize:attr("string"),postType:attr("string"),postCats:attr(Array),postTags:attr(Array),postCount:attr("number"),postImageType:attr("string"),postOrder:attr("string"),postOrderDir:attr("string"),postExcerptLen:attr("number"),postExcludeIds:attr("string"),postExcludeNoImg:attr("boolean"),postIncludeIds:attr("string"),postOffset:attr("number"),postLinkSlide:attr("boolean"),postLinkTarget:attr("string"),postSlideBg:attr("string"),postSlideBgthumb:attr("string"),wcOnlyInstock:attr("boolean"),wcOnlyFeatured:attr("boolean"),wcOnlyOnsale:attr("boolean")}),MSPanel.Slide=Ember.Model.extend({id:attr("number"),timeline_h:attr("number",{defaultValue:200}),bgThumb:attr(WPPath),thumbOrginal:attr(WPPath),isOverlayLayers:attr("boolean",{defaultValue:!1}),order:attr("number"),ishide:attr("boolean"),bg:attr(WPPath),duration:attr("number",{defaultValue:defaults.duration||3}),msId:attr("string"),fillMode:attr("string",{defaultValue:defaults.slideFillMode||"fill"}),thumb:attr(WPPath),info:attr("string"),link:attr("string"),linkTarget:attr("string"),linkTitle:attr("string"),linkRel:attr("string"),linkClass:attr("string"),linkId:attr("string"),video:attr("string"),bgColor:attr("string"),autoplayVideo:attr("boolean"),pattern:attr("string"),colorOverlay:attr("string"),bgv_mp4:attr("string"),bgv_ogg:attr("string"),bgv_webm:attr("string"),bgv_fillmode:attr("string",{defaultValue:defaults.sliderVideoFillMode||"fill"}),bgv_loop:attr("boolean",{defaultValue:defaults.slideVideoLoop}),bgv_mute:attr("boolean",{defaultValue:defaults.slideVideoMute}),bgv_autopause:attr("boolean",{defaultValue:defaults.slideVideoAutopause}),cssId:attr("string"),cssClass:attr("string"),bgAlt:attr("string"),bgTitle:attr("string"),layers:hasMany("MSPanel.Layer",{key:"layer_ids"})}),MSPanel.Layer=Ember.Model.extend({id:attr("number"),name:attr("string"),isLocked:attr("boolean",{defaultValue:!1}),isHided:attr("boolean",{defaultValue:!1}),isSoloed:attr("boolean",{defaultValue:!1}),slide:belongsTo("MSPanel.Slide",{key:"slide"}),styleModel:belongsTo("MSPanel.Style",{key:"styleModel",embedded:!1}),showEffect:belongsTo("MSPanel.Effect",{key:"showEffect",embedded:!1}),showTransform:attr("string",{defaultValue:""}),showOrigin:attr("string",{defaultValue:""}),showFade:attr("boolean",{defaultValue:!0}),hideEffect:belongsTo("MSPanel.Effect",{key:"hideEffect",embedded:!1}),hideTransform:attr("string",{defaultValue:""}),hideOrigin:attr("string",{defaultValue:""}),hideFade:attr("boolean",{defaultValue:!0}),imgThumb:attr(WPPath),stageOffsetX:attr("number",{defaultValue:0}),stageOffsetY:attr("number",{defaultValue:0}),order:attr("number"),type:attr("string"),position:attr("string",{defaultValue:"normal"}),msId:attr("string"),cssClass:attr("string"),cssId:attr("string"),title:attr("string"),rel:attr("string"),noSwipe:attr("string",{defaultValue:!1}),content:attr("string",{defaultValue:defaults.layerContent||"Lorem Ipsum"}),img:attr(WPPath),imgAlt:attr("string"),video:attr("string",{defaultValue:"http://player.vimeo.com/video/11721242"}),align:attr("string",{defaultValue:"top"}),useAction:attr("boolean",{defaultValue:!1}),action:attr("string"),toSlide:attr("number"),link:attr("string"),linkTarget:attr("string"),scrollDuration:attr("number",{defaultValue:2}),scrollTarget:attr("string"),actionTargetLayer:attr("string"),offsetX:attr("number",{defaultValue:0}),offsetY:attr("number",{defaultValue:0}),width:attr("number"),height:attr("number"),resize:attr("boolean",{defaultValue:!0}),fixed:attr("boolean",{defaultValue:!1}),widthlimit:attr("number",{defaultValue:"0"}),origin:attr("string",{defaultValue:"tl"}),stayHover:attr("boolean",{defaultValue:!0}),className:attr("string"),parallax:attr("string"),wait:attr("boolean",{defaultValue:defaults.layerWait}),masked:attr("boolean"),maskCustomSize:attr("boolean"),maskWidth:attr("number"),maskHeight:attr("number"),overlayTargetSlides:attr("string"),overlayTargetSlidesAction:attr("string",{defaultValue:"show"}),showDuration:attr("number",{defaultValue:1}),showDelay:attr("number",{defaultValue:0}),showEase:attr("string",{defaultValue:"easeOutQuint"}),showEffFunc:attr("string"),useHide:attr("boolean",{defaultValue:!1}),hideDuration:attr("number",{defaultValue:1}),hideDelay:attr("number",{defaultValue:1}),hideEase:attr("string",{defaultValue:"easeOutQuint"}),hideEffFunc:attr("string"),btnClass:attr("string",{defaultValue:"ms-btn ms-default-btn"}),autoplayVideo:attr("boolean")}),MSPanel.Style=Ember.Model.extend({id:attr("number"),name:attr("string"),type:attr("string"),className:attr("string"),backgroundColor:attr("string"),paddingTop:attr("number"),paddingRight:attr("number"),paddingBottom:attr("number"),paddingLeft:attr("number"),borderTop:attr("number"),borderRight:attr("number"),borderBottom:attr("number"),borderLeft:attr("number"),borderColor:attr("string"),borderRadius:attr("number"),borderStyle:attr("string"),fontFamily:attr("string"),fontWeight:attr("string",{defaultValue:"normal"}),fontSize:attr("number"),textAlign:attr("string"),letterSpacing:attr("number"),lineHeight:attr("string",{defaultValue:"normal"}),whiteSpace:attr("string"),color:attr("string"),custom:attr("string")}),MSPanel.PresetStyle=MSPanel.Style.extend({}),MSPanel.Effect=Ember.Model.extend({id:attr("number"),name:attr("string"),type:attr("string"),fade:attr("boolean",{defaultValue:!0}),translateX:attr("number"),translateY:attr("number"),translateZ:attr("number"),scaleX:attr("number"),scaleY:attr("number"),rotate:attr("number"),rotateX:attr("number"),rotateY:attr("number"),rotateZ:attr("number"),skewX:attr("number"),skewY:attr("number"),originX:attr("number"),originY:attr("number"),originZ:attr("number")}),MSPanel.PresetEffect=MSPanel.Effect.extend({}),MSPanel.Control=Ember.Model.extend({id:attr("number"),label:attr("string"),name:attr("string"),autoHide:attr("boolean",{defaultValue:!0}),overVideo:attr("boolean",{defaultValue:!0}),cssClass:attr("string"),cssId:attr("string"),margin:attr("number"),dir:attr("string"),color:attr("string"),radius:attr("number"),stroke:attr("number"),speed:attr("number"),space:attr("number"),type:attr("string"),insertThumb:attr("boolean"),arrows:attr("boolean"),hoverChange:attr("boolean"),width:attr("number"),height:attr("number"),align:attr("string"),inset:attr("boolean"),size:attr("number"),hideUnder:attr("number"),fillMode:attr("string")}),MSPanel.Callback=Ember.Model.extend({id:attr("number"),label:attr("string"),name:attr("string"),content:attr("string",{defaultValue:"function(event){\n  var api = event.target;\n}"})}),MSPanel.ButtonStyle=Ember.Model.extend({id:attr("number"),className:attr("string"),normal:attr("string"),hover:attr("string"),active:attr("string"),style:attr("string",{defaultValue:"ms-btn-box"}),size:attr("string",{defaultValue:"ms-btn-n"})});var decodeFix=function(str){var decoded=B64.decode(str);return decoded.slice(0,decoded.lastIndexOf("}")+1)};MSPanel.data=__MSP_DATA?JSON.parse(decodeFix(__MSP_DATA)):{meta:{}},MSPanel.PSData=__MSP_PRESET_STYLE?JSON.parse(decodeFix(__MSP_PRESET_STYLE)):{meta:{}},MSPanel.PEData=__MSP_PRESET_EFFECT?JSON.parse(decodeFix(__MSP_PRESET_EFFECT)):{meta:{}},MSPanel.PBData=__MSP_PRESET_BUTTON?JSON.parse(decodeFix(__MSP_PRESET_BUTTON)):{meta:{}},MSPanel.Settings.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.data}),MSPanel.Slide.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.data}),MSPanel.Layer.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.data}),MSPanel.Style.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.data}),MSPanel.Effect.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.data}),MSPanel.Control.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.data}),MSPanel.Callback.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.data}),MSPanel.PresetStyle.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.PSData}),MSPanel.PresetEffect.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.PEData}),MSPanel.ButtonStyle.adapter=Ember.OfflineAdapter.create({applicationData:MSPanel.PBData})}(),MSPanel.SlideFrame=Ember.View.extend({classNames:["msp-slideframe"],classNameBindings:["selected:active"],selected:!1,thumb_src:"",showbtnclass:"msp-ico msp-ico-whitehide",template:Ember.Handlebars.compile('<div class="msp-img-cont">{{#if view.hasImg}}<div class="msp-imgselect-preview" {{bind-attr style=view.preview}}></div>{{/if}}</div><span class="msp-frame-slideorder">#{{view.order}}</span><div class="msp-framehandle"><ul><li><a title="'+__MSP_LAN.ui_001+'" href="#" {{action "hideswitch" target=view}}><span {{bind-attr class=view.showbtnclass}}></span></a></li><li><a title="'+__MSP_LAN.ui_002+'" href="#" {{action "duplicate" target=view}}><span class="msp-ico msp-ico-whiteduplicate"></span></a></li><li><a title="'+__MSP_LAN.ui_003+'" href="#" {{action "remove" target=view}}><span class="msp-ico msp-ico-whiteremove"></span></a></li></ul></div>'),click:function(){this.get("controller").send("select",this.get("slide"))},onValueChanged:function(){var hasImg=!Ember.isEmpty(this.get("slide.bg")),hasThumb=!Ember.isEmpty(this.get("slide.thumb"));this.beginPropertyChanges(),this.set("hasImg",hasImg||hasThumb),hasImg?this.set("preview","background-image:url("+this.get("slide.bgThumb")+");"):hasThumb&&this.set("preview","background-image:url("+this.get("slide.thumb")+");"),this.endPropertyChanges()}.observes("slide.bg","slide.thumb").on("didInsertElement"),onSelect:function(){var slide=this.get("slide");this.set("selected",slide===this.get("controller.currentSlide"))}.observes("controller.currentSlide").on("init"),hideChange:function(){this.get("slide.ishide")?this.set("showbtnclass","msp-ico msp-ico-whitehide msp-ico-whiteshow"):this.set("showbtnclass","msp-ico msp-ico-whitehide")}.observes("slide.ishide").on("init"),order:function(){return this.get("slide.order")+1}.property("slide.order"),actions:{duplicate:function(){this.get("controller").duplicateSlide(this.get("slide"))},hideswitch:function(){this.set("slide.ishide",!this.get("slide.ishide"))},remove:function(){confirm(__MSP_LAN.ui_004)&&this.get("controller").removeSlide(this.get("slide"))}}}),MSPanel.SlideList=Ember.View.extend({tagName:"div",classNames:["msp-slides-container"],template:Ember.Handlebars.compile('<ul class="msp-slides sortable">{{#each item in controller}}{{#if item.isOverlayLayers}}{{else}}<li class="msp-slideframe-item" {{bind-attr data-id=item.id}}>{{view MSPanel.SlideFrame slide=item}}</li>{{/if}}{{/each}}<li class="msp-addslide-cont"><div class="msp-addslide" {{action "addSlides"}}><span class="msp-ico msp-ico-grayaddlarge"></span><span class="msp-addslide-label">Add Slide</span></div></li> </ul>'),didInsertElement:function(){var that=this;this.$().find(".sortable").sortable({placeholder:"msp-frames-srtplaceholder",items:">li:not(.msp-addslide-cont)",delay:100,update:function(){that.updateSort()},create:function(){that.updateSort()}})},updateSort:function(){var indexes={};$(".msp-slideframe-item").each(function(index){indexes[$(this).data("id")]=index}),this.$().find(".sortable").sortable("cancel"),this.get("controller").updateSlidesSort(indexes)},onSelect:function(){}.observes("controller.currentSlide").on("init"),actions:{switchToOverlays:function(){this.get("controller.currentSlide.isOverlayLayers")||this.set("controller.currentSlide",this.get("controller.overlayLayersSlide"))}}}),MSPanel.ImgSelect=Ember.View.extend({classNames:["msp-imgselect"],value:"",hasImg:!1,frame:null,slideBg:!1,template:Ember.Handlebars.compile('<div class="msp-img-cont">{{#if view.hasImg}}<div class="msp-imgselect-preview" {{bind-attr style=view.preview}})"></div>{{/if}}</div>{{#if view.hasImg}}<button {{action removeImg target="view"}} class="msp-img-btn"><span class="msp-ico msp-ico-grayremove"></span></button>{{else}}<button {{action addImg target="view"}} class="msp-img-btn"><span class="msp-ico msp-ico-grayadd"></span></button>{{/if}}'),willDestroyElement:function(){var frame=this.get("frame");frame&&(frame.detach(),frame.remove(),frame=null,this.set("frame",null))},onValueChanged:function(){this.beginPropertyChanges(),this.set("hasImg",!Ember.isEmpty(this.get("value"))),this.set("preview","background-image:url("+this.get("thumb")+");"),this.endPropertyChanges()}.observes("value").on("didInsertElement"),actions:{removeImg:function(){this.beginPropertyChanges(),this.set("value",void 0),this.set("thumb",void 0),this.endPropertyChanges()},addImg:function(){if("undefined"!=typeof wp){var that=this,frame=this.get("frame");if(frame)return void frame.open();var frame=wp.media.frames.frame=wp.media({title:"Select Image",multiple:!1,frame:"select",library:{type:"image"},button:{text:"Add Image"}});frame.on("select",function(){var attachment=frame.state().get("selection").first().toJSON();that.set("thumb",(attachment.sizes.thumbnail||attachment.sizes.full).url),that.set("value",attachment.url)}),frame.open(),this.set("frame",frame)}}}}),MSPanel.Select=Ember.Select.extend({tagName:"div",classNames:["msp-ddlist"],layout:Ember.Handlebars.compile("<select>{{yield}}</select>"),value:null,width:100,didInsertElement:function(){var that=this;this.$("select").on("change",function(){var option=that.$("select option:selected");that.set("value",option.attr("value"))}).width(this.get("width")),this.onValueChanged()},onValueChanged:function(){Ember.isEmpty(this.get("value"))||this.$("select").val(this.get("value"))}.observes("value")}),MSPanel.URLTarget=MSPanel.Select.extend({onInit:function(){var contents=[{lable:__MSP_LAN.ui_005,value:"_self"},{lable:__MSP_LAN.ui_006,value:"_blank"},{lable:__MSP_LAN.ui_007,value:"_parent"},{lable:__MSP_LAN.ui_008,value:"_top"}];this.set("content",contents),this.set("optionValuePath","content.value"),this.set("optionLabelPath","content.lable"),this.set("width",200)}.on("init")}),MSPanel.Fillmode=Ember.View.extend({classNames:["msp-fill-dd"],type:"slide",value:"fill",index:1,template:Ember.Handlebars.compile("<select>{{#each item in view.contents}}<option {{bind-attr value=item.value data-imagesrc=item.img}}>{{item.text}}</option>{{/each}}</select>"),didInsertElement:function(){var that=this,isFirst=!0;this.$("select").ddslick({width:154,onSelected:function(selected){!isFirst&&that.set("value",selected.selectedData.value),isFirst=!1}}),this.onValueChanged()},onValueChanged:function(){Ember.isEmpty(this.get("value"))||this.$(".dd-container").ddslick("select",{index:this.get("valuedic")[this.get("value")]})}.observes("value"),onInit:function(){var contents,valuedic;"slide"===this.get("type")?(contents=[{value:"fill",text:__MSP_LAN.ui_009,img:__MSP_PATH+"images/fill.png"},{value:"fit",text:__MSP_LAN.ui_010,img:__MSP_PATH+"images/fit.png"},{value:"center",text:__MSP_LAN.ui_011,img:__MSP_PATH+"images/center.png"},{value:"stretch",text:__MSP_LAN.ui_012,img:__MSP_PATH+"images/stretch.png"},{value:"tile",text:__MSP_LAN.ui_013,img:__MSP_PATH+"images/tile.png"}],valuedic={fill:0,fit:1,center:2,stretch:3,tile:4}):"video"===this.get("type")&&(contents=[{value:"fill",text:__MSP_LAN.ui_009,img:__MSP_PATH+"images/fill.png"},{value:"fit",text:__MSP_LAN.ui_010,img:__MSP_PATH+"images/fit.png"}],valuedic={fill:0,fit:1,none:2}),this.set("contents",contents),this.set("valuedic",valuedic)}.on("init")}),MSPanel.AddLayer=Ember.View.extend({classNames:["msp-addlayer"],template:Ember.Handlebars.compile('<button {{action newLayer view.value}} class="msp-add-btn msp-addlayer-btn"><span class="msp-ico msp-ico-whiteadd"></span></button><div class="msp-addlayer-dd"><select>{{#each item in view.layertypes}}<option {{bind-attr value=item.value data-imagesrc=item.img}}>{{item.lable}}</option>{{/each}}</select></div>'),didInsertElement:function(){var that=this;this.$().find("select").ddslick({width:154,onSelected:function(selected){that.set("value",selected.selectedData.value)}})},onInit:function(){for(var layertypes=[],clt=this.get("controller.layertypes"),i=0,l=clt.length;i!==l;i++)layertypes.push({value:clt[i].value,lable:clt[i].lable,img:__MSP_PATH+"images/layertypes/"+clt[i].value+".png"});this.set("layertypes",layertypes)}.on("init")}),MSPanel.AlignBtns=Ember.View.extend({classNames:["msp-align-btns"],target:null,template:Ember.Handlebars.compile('<button title="'+__MSP_LAN.ui_015+'" {{action "alignLayer" "top" target=view.target}} class="msp-align-btn"><span class="msp-ico msp-ico-altop"></span></button><button title="'+__MSP_LAN.ui_016+'" {{action "alignLayer" "mid" target=view.target}} class="msp-align-btn"><span class="msp-ico msp-ico-almid"></span></button><button title="'+__MSP_LAN.ui_017+'" {{action "alignLayer" "bot" target=view.target}} class="msp-align-btn"><span class="msp-ico msp-ico-albot"></span></button><div class="msp-btn-space"></div><button title="'+__MSP_LAN.ui_018+'" {{action "alignLayer" "left" target=view.target}} class="msp-align-btn"><span class="msp-ico msp-ico-alleft"></span></button><button title="'+__MSP_LAN.ui_019+'" {{action "alignLayer" "center" target=view.target}} class="msp-align-btn"><span class="msp-ico msp-ico-alcenter"></span></button><button title="'+__MSP_LAN.ui_020+'" {{action "alignLayer" "right" target=view.target}} class="msp-align-btn"><span class="msp-ico msp-ico-alright"></span></button>')}),MSPanel.PositionOrigin=Ember.View.extend({classNames:["msp-origin-control"],layer:null,selectedNode:null,template:Ember.Handlebars.compile('<table><tbody><tr><td title="Top left" class="msp-origin-btn msp-origin-tl" data-origin="tl"></td><td title="Top center" class="msp-origin-btn msp-origin-tc" data-origin="tc"></td><td title="Top right" class="msp-origin-btn msp-origin-tr" data-origin="tr"></td></tr><tr><td title="Middle left" class="msp-origin-btn msp-origin-ml" data-origin="ml"></td><td title="Middle center" class="msp-origin-btn msp-origin-mc" data-origin="mc"></td><td title="Middle right" class="msp-origin-btn msp-origin-mr" data-origin="mr"></td></tr><tr><td title="Bottom left" class="msp-origin-btn msp-origin-bl" data-origin="bl"></td><td title="Bottom center" class="msp-origin-btn msp-origin-bc" data-origin="bc"></td><td title="Bottom right" class="msp-origin-btn msp-origin-br" data-origin="br"></td></tr></tbody></table>'),didInsertElement:function(){var that=this;this.$(".msp-origin-btn").click(function(){Ember.isEmpty(that.get("layer"))||that.set("layer.origin",$(this).data("origin"))}),this.onValueChanged()},onValueChanged:function(){var selectedNode=this.get("selectedNode");if(Ember.isEmpty(selectedNode)||selectedNode.removeClass("msp-origin-btn-selected"),Ember.isEmpty(this.get("layer")))return void this.$().addClass("msp-origin-control-disabled");this.$().removeClass("msp-origin-control-disabled");var value=this.get("layer.origin"),newNode=this.$(".msp-origin-"+value).addClass("msp-origin-btn-selected");this.set("selectedNode",newNode)}.observes("layer","layer.origin")}),MSPanel.ButtonsList=Ember.View.extend({classNames:["msp-buttons-container"],layer:null,template:Ember.Handlebars.compile('{{#each button in controller.buttonClasses}}<div class="msp-button-container" {{action "selectButton" button target=view}}><div class="msp-button-cell"><span {{bind-attr class=":ms-btn button.style button.size button.className"}}>Button</span></div></div>{{/each}}'),didInsertElement:function(){this.onValueChanged()},onValueChanged:function(){var className=this.get("layer.btnClass"),lastSelected=this.get("lastSelected");Ember.isEmpty(className)||(className=className.split(" ").pop(),Ember.isEmpty(lastSelected)||this.$("."+lastSelected).parent().removeClass("active"),this.$("."+className).parent().addClass("active"),this.set("lastSelected",className))}.observes("layer","layer.btnClass"),actions:{selectButton:function(button){this.set("layer.btnClass","ms-btn "+button.get("style")+" "+button.get("size")+" "+button.get("className"))}}}),MSPanel.ActionList=Ember.View.extend({classNames:["msp-action-list"],layer:null,showSlideNum:null,showDuration:null,template:Ember.Handlebars.compile('{{#dropdwon-List value=view.layer.action width=180}}<option value="next">'+(__MSP_LAN.ui_021||"Goto next slide")+'</option><option value="previous">'+(__MSP_LAN.ui_022||"Goto previous slide")+'</option><option value="gotoSlide">'+(__MSP_LAN.ui_025||"Goto slide")+'</option><option value="pause">'+(__MSP_LAN.ui_023||"Pause timer")+'</option><option value="resume">'+(__MSP_LAN.ui_024||"Resume timer")+'</option><option value="scrollToEnd">'+(__MSP_LAN.ui_028||"Scroll to bottom of slider")+'</option><option value="scrollTo">'+(__MSP_LAN.ui_030||"Scroll to an element in page")+'</option>{{/dropdwon-List}}{{#if view.showSlideNum}}<div class="msp-form-space-med"></div>'+(__MSP_LAN.ui_026||"Slide number : ")+' {{number-input value=view.layer.toSlide}}{{/if}}{{#if view.showDuration}}<div class="msp-form-space-med"></div>'+(__MSP_LAN.ui_029||"Scroll animation duration : ")+' {{number-input value=view.layer.scrollDuration}} s{{/if}}{{#if view.showTarget}}<div class="msp-form-space-med"></div>'+(__MSP_LAN.ui_029||"Scroll animation duration : ")+' {{number-input value=view.layer.scrollDuration}} s<div class="msp-form-space-med"></div>'+(__MSP_LAN.ui_031||"Target element : ")+" {{input value=view.layer.scrollTarget}}{{/if}}"),onValueChanged:function(){var value=this.get("layer.action");this.set("showSlideNum","gotoSlide"===value),this.set("showDuration","scrollToEnd"===value),this.set("showTarget","scrollTo"===value)}.observes("layer","layer.action").on("init")}),MSPanel.SimpleCodeBlock=Ember.View.extend({classNames:["msp-shortcode-box"],template:Ember.Handlebars.compile('<input type="text" readonly {{bind-attr value=view.value}}>'),width:150,didInsertElement:function(){this.$("input").on("click",function(){$(this).select()}).width(this.get("width"))}}),MSPanel.SettingsView=Ember.View.extend({didInsertElement:function(){this.set("controller.mainView",this)}}),MSPanel.SlidesView=Ember.View.extend({didInsertElement:function(){this.set("controller.mainView",this)}}),MSPanel.StageArea=Ember.View.extend({classNames:["msp-stage-area"],template:Ember.Handlebars.compile('{{view MSPanel.Stage}}{{#if noticeMsg}}<div class="msp-stage-msg"><span class="msp-ico msp-ico-notice"></span>{{{noticeMsg}}}</div>{{/if}}')}),MSPanel.Stage=Ember.View.extend({classNames:["msp-slide-stage"],attributeBindings:["style"],template:Ember.Handlebars.compile('<div id="stage-bg" class="msp-stage-bg"></div><div id="overlaybox" {{bind-attr class=":msp-stage-pattern :ms-pattern controller.slide.pattern"}}></div>'),resize:function(){var w=this.get("controller.sliderSettings.width"),h=this.get("controller.sliderSettings.height");this.set("width",w),this.set("height",h),this.$().css({width:w,height:h})}.observes("controller.sliderSettings.width","controller.sliderSettings.height","parentView.zoom").on("didInsertElement"),didInsertElement:function(){var BG=this.$("#stage-bg"),BGImage=$("<img/>");BGImage.css("visibelity","hidden").each($.jqLoadFix);var aligner=new MSAligner(this.get("controller.slide.fillMode"),BG,BGImage);this.set("bgAligner",aligner),this.set("bgImg",BGImage),this.onBGChange()},onBGColorChange:function(){var color=this.get("controller.slide.bgColor");Ember.isEmpty(color)?this.$("#stage-bg").css("background-color",""):this.$("#stage-bg").css("background-color",color)}.observes("controller.slide.bgColor").on("didInsertElement"),onColorOverlayChange:function(){var color=this.get("controller.slide.colorOverlay");Ember.isEmpty(color)?this.$("#overlaybox").css("background-color",""):this.$("#overlaybox").css("background-color",color)}.observes("controller.slide.colorOverlay").on("didInsertElement"),onBGChange:function(){var alinger=this.get("bgAligner");alinger&&alinger.reset();var bg=this.get("controller.slide.bg"),bgImg=this.get("bgImg");if(Ember.isEmpty(bg))bgImg.detach();else{var that=this;bgImg.appendTo(this.$("#stage-bg")),bgImg.preloadImg(bg,function(event){that._onBGLoad(event)}),bgImg.attr("src",bg)}}.observes("controller.slide.bg"),_onBGLoad:function(event){var aligner=this.get("bgAligner");aligner&&(aligner.init(event.width,event.height),aligner.align(),this.get("bgImg").css("visibelity",""))},onFillModeChanged:function(){var aligner=this.get("bgAligner");aligner.changeType(this.get("controller.slide.fillMode"))}.observes("controller.slide.fillMode"),willDestroyElement:function(){$(document).off("keydown",this.moveLayer),this.set("bgAligner",null)}}),MSPanel.ControlBtn=Ember.View.extend({control:null,tagName:"div",active:!1,classNames:["msp-control-btn"],classNameBindings:["active:msp-blue-btn"],template:Ember.Handlebars.compile('<span class="msp-control-label">{{view.control.label}}</span><a href="#" {{action "removeControl" target=view bubbles=false}}><span class="msp-control-removes msp-ico msp-ico-whiteremove"></span></a>'),didInsertElement:function(){},onActiveChange:function(){this.set("active",this.get("controller.currentControl")===this.get("control")),this.get("active")&&this.get("controller").send("showControlOptions")}.observes("controller.currentControl").on("init"),click:function(){this.get("active")||this.set("controller.currentControl",this.get("control"))},actions:{removeControl:function(){confirm('Are you sure want to remove "'+this.get("control.label")+'" control?')&&this.get("controller").send("removeControl",this.get("control"))}}}),function($){function WPEditorTemplate(id){var newEditor=$(hiddenEditor);return newEditor.find("link").remove(),newEditor.html().replace(/msp-hidden/g,id)}MSPanel.MetaBoxComponent=Ember.Component.extend({tagName:"div",classNames:["msp-metabox"],layout:Ember.Handlebars.compile('<div class="msp-metabox-handle"><h3 class="msp-metabox-title">{{title}}</h3><div class="msp-metabox-toggle"></div></div>{{yield}}<div class="clear"> </div>')}),Ember.TEMPLATES["components/tabs-panel"]=Ember.Handlebars.compile("{{yield}}"),MSPanel.TabsPanelComponent=Ember.Component.extend({tagName:"div",attributeBindings:["id"],classNames:["msp-metabox msp-metabox-tabs"],didInsertElement:function(){this.$().avertaLiveTabs()}}),MSPanel.SwitchBoxComponent=Ember.Component.extend({classNames:["msp-switchbox"],offlable:"OFF",onlable:"ON",value:!1,layout:Ember.Handlebars.compile('<div class="msp-switch-cont"><span class="msp-switch-off">{{view.offlable}}</span><div class="msp-switch-handle"></div><span class="msp-switch-on">{{view.onlable}}</span></div>'),click:function(){var that=this;that.set("value",!that.get("value"))},update:function(){this.get("value")?this.$().addClass("switched"):this.$().removeClass("switched")}.observes("value").on("didInsertElement")}),MSPanel.AddDynamicTag=Ember.View.extend({classNames:["msp-add-dynamic-tags"],editorId:null,template:Ember.Handlebars.compile('<button {{action "addTag" target=view}} class="msp-add-btn"><span class="msp-ico msp-ico-whiteadd"></span></button><div class="msp-ddlist"></div>'),didInsertElement:function(){for(var select=$("<select></select>").appendTo(this.$(".msp-ddlist")).width(220),i=0,l=MSPanel.dynamicTags.length;i!==l;i++)select.append('<option value="'+MSPanel.dynamicTags[i].tag+'">'+MSPanel.dynamicTags[i].name+"</option>");
this.set("select",select)},actions:{addTag:function(){var id=this.get("editorId");tinymce&&$("#wp-"+id+"-wrap").hasClass("tmce-active")?tinymce.get(id).execCommand("insertHTML",!1,this.get("select").val()):$("#"+id).insertAtCaret(this.get("select").val())}}});var hiddenEditor=jQuery("#mspHiddenEditor")[0].outerHTML,__tmc_msp_id=0;MSPanel.WPEditor=Ember.View.extend({classNames:["msp-wp-editor"],_id:null,template:null,tab:null,tabs:null,onInit:function(){var id="msp-wpeditor-"+__tmc_msp_id;this.set("_id",id),this.set("template",Ember.Handlebars.compile(WPEditorTemplate(id))),__tmc_msp_id++}.on("init"),didInsertElement:function(){var tabs=this.get("tabs");return Ember.isEmpty(tabs)?void this.createEditor():void $("#"+tabs).bind("avtTabChange",{that:this},this.refreshEditor)},refreshEditor:function(event,tab){var that=event.data.that;that.get("tab")===tab&&that.createEditor()},createEditor:function(){if(this.get("inited")!==!0){this.set("inited",!0);var id=this.get("_id"),that=this;if(window.tinymce){var settings=$.extend({},window.tinyMCEPreInit.mceInit["msp-hidden"]||{});settings.forced_root_block="",settings.force_br_newlines=!0,settings.force_p_newlines=!1,settings.wpautop=!1,"3"==tinyMCE.majorVersion?(settings.body_class=settings.elements=id,settings.setup=function(ed){ed.onInit.add(function(){that.initEditor(tinyMCE.getInstanceById(id))})},tinymce.init(settings)):"4"==tinyMCE.majorVersion&&(settings.body_class="content post-type-post post-status-auto-draft post-format-standard",settings.selector="#"+id,settings.setup=function(ed){ed.on("init",function(){that.initEditor(tinyMCE.get(id))})},tinymce.init(settings))}var qtags,qtagSettings=$.extend({},window.tinyMCEPreInit.qtInit["msp-hidden"]||{});qtagSettings.id=id,"function"==typeof QTags&&(qtags=quicktags(qtagSettings),QTags.buttonsInitDone=!1,QTags._buttonsInit(),that.set("qtags",qtags),window.tinymce?switchEditors.go(id,"html"):this.onValueChanged(),this.$("textarea#"+this.get("_id")).on("change keyup paste",function(){that.set("value",$(this).val())}))}},initEditor:function(mce){function internalUpdate(){that.set("internalChange",!0),that.set("value",mce.getContent()),that.set("internalChange",!1)}var id=this.get("_id"),that=(this.get("value"),this);this.$(".wp-editor-wrap").on("mousedown",function(){wpActiveEditor=id}),"3"==tinyMCE.majorVersion?(mce.onChange.add(internalUpdate),mce.onKeyUp.add(internalUpdate)):"4"==tinyMCE.majorVersion&&(mce.on("change",internalUpdate),mce.on("keyup",internalUpdate)),this.$().click(internalUpdate),setTimeout(function(){switchEditors.go(id,"html"),switchEditors.go(id,"tmce")},100),this.set("mce",mce),this.onValueChanged()},onValueChanged:function(){if(this.get("inited")){var value=this.get("value");if(this.$("textarea#"+this.get("_id")).val(value),this.get("internalChange"))return void this.set("internalChange",!1);if(window.tinymce){var mce=this.get("mce");Ember.isEmpty(mce)||null==value?null==value&&mce.setContent(" "):mce.setContent(value)}}}.observes("value"),willDestroyElement:function(){if(this.get("inited")){window.tinymce&&tinymce.remove(this.get("_id"));var qtags=this.get("qtags");qtags&&($(qtags.toolbar).remove(),qtags.toolbar=null,qtags=null,QTags.instances[this.get("_id")]&&delete QTags.instances[this.get("_id")],this.$("textarea#"+this.get("_id")).remove());var tabs=this.get("tabs");Ember.isEmpty(tabs)||$("#"+tabs).unbind("avtTabChange",this.refreshEditor)}}}),jQuery.ui&&jQuery.ui.spinner&&(jQuery.ui.spinner.prototype._events.mousewheel=function(event,delta){if(delta&&this.element.is(":focus")){if(!this.spinning&&!this._start(event))return!1;this._spin((delta>0?1:-1)*this.options.step,event),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay(function(){this.spinning&&this._stop(event)},100),event.preventDefault()}}),MSPanel.NumberInputView=Ember.View.extend({step:1,min:0,tagName:"input",attributeBindings:["type"],lastValue:null,type:"text",didInsertElement:function(){var that=this,input=this.$(),updateValue=function(){var value=input.spinner("value");that.set("internalChange",!0),isNaN(value)||null==value?that.set("value",void 0):that.set("value",parseFloat(value))};input.on("change",updateValue).spinner({step:this.get("step"),numberFormat:"n",min:this.get("min"),max:this.get("max"),spin:updateValue,stop:updateValue}).spinner("value",this.get("value"))},onValueChanged:function(){this.get("internalChange")&&this.set("internalChange",!1),this.$().spinner("value",this.get("value"))}.observes("value")}),Ember.Handlebars.helper("number-input",MSPanel.NumberInputView),MSPanel.ColorPickerComponent=Ember.Component.extend({tagName:"input",classNames:"msp-color-picker",value:null,didInsertElement:function(){var that=this;this.$().spectrum({color:this.get("value"),allowEmpty:!0,showInput:!0,showAlpha:!0,clickoutFiresChange:!0,preferredFormat:"hex6",change:function(color){null===color?that.set("value",null):that.set("value",color.toString())}})},willDestroyElement:function(){this.$().spectrum("destroy")},onValueChanged:function(){this.$().spectrum("set",this.get("value"))}.observes("value")}),MSPanel.DropdwonListComponent=Ember.Component.extend({tagName:"div",classNames:["msp-ddlist"],layout:Ember.Handlebars.compile("<select>{{yield}}</select>"),value:null,width:100,didInsertElement:function(){var that=this;this.$("select").on("change",function(){var option=that.$("select option:selected");that.set("value",option.attr("value"))}).width(this.get("width")),this.onValueChanged()},onValueChanged:function(){Ember.isEmpty(this.get("value"))||this.$("select").val(this.get("value"))}.observes("value")}),MSPanel.MultiDropdwonListComponent=Ember.Component.extend({tagName:"div",classNames:["msp-ddlist","msp-ddlist-multiselect"],layout:Ember.Handlebars.compile("<select {{bind-attr size=view.size}} multiple>{{yield}}</select>"),value:null,width:100,size:7,didInsertElement:function(){var that=this;this.$("select").on("change",function(){that.set("value",$(this).val())}).width(this.get("width")),this.onValueChanged()},onValueChanged:function(){Ember.isEmpty(this.get("value"))||this.$("select").val(this.get("value"))}.observes("value")}),MSPanel.GoogleFontsComponent=Ember.Component.extend({tagName:"div",classNames:["msp-ddlist","msp-gfonts-select"],defaultTemplate:Ember.Handlebars.compile('<select><option value="--" selected>Loading fonts..</option></select>'),value:null,variants:null,width:210,didInsertElement:function(){var that=this;GFonts.getList(function(){that.$("select").html('<option value="--" selected>-- select --</option>'+GFonts.generateSelectList()),that.onValueChanged(),that.$("select").trigger("change")}),this.$("select").on("change",function(){var option=that.$("select option:selected");"--"===option.val()?(that.set("value",void 0),that.set("variants",void 0)):(that.set("value",option.attr("value")),that.set("variants",option.attr("data-variants")))}).width(this.get("width")),that.onValueChanged()},onValueChanged:function(){this.$("select").val(Ember.isEmpty(this.get("value"))?"--":this.get("value"))}.observes("value")}),MSPanel.GoogleFontWeightsComponent=Ember.Component.extend({tagName:"div",classNames:["msp-ddlist","msp-gfonts-select msp-gfonts-weight"],defaultTemplate:Ember.Handlebars.compile("<select></select>"),variants:null,value:null,width:120,didInsertElement:function(){var that=this;this.$("select").on("change",function(){var option=that.$("select option:selected");return 0===option.length?void that.$("select").val("normal"):void("--"===option.val()?that.set("value",null):that.set("value",option.attr("value")))}).width(this.get("width")),that.updateVariants()},updateVariants:function(){var variants=this.get("variants");if(Ember.isEmpty(variants))this.$("select").html("<option>Select font</option>");else{var options="",value=this.get("value");variants=variants.split(",");for(var i=0,l=variants.length;i!==l;i++)-1===variants[i].indexOf("italic")&&(options+="regular"===variants[i]?'<option value="normal"'+("normal"===value?"selected":"")+">Normal</option>":'<option value="'+variants[i]+'"'+(value===variants[i]?"selected":"")+">"+variants[i]+"</option>");this.$("select").html(options).trigger("change")}}.observes("variants")}),MSPanel.CodeMirrorComponent=Ember.Component.extend({classNames:["msp-codemirror"],width:250,height:200,mode:"css",tab:null,tabs:null,layout:Ember.Handlebars.compile("<textarea>{{yield}}</textarea>"),didInsertElement:function(){this.$().width(this.get("width")).height(this.get("height"));var that=this,editor=CodeMirror.fromTextArea(this.$(">textarea")[0],{lineNumbers:!0,mode:this.get("mode")});editor.on("change",function(){that.set("internalChange",!0),that.set("value",editor.getValue())}),this.set("editor",editor);var value=this.get("value");Ember.isEmpty(value)||editor.setValue(value);var tabs=this.get("tabs");Ember.isEmpty(tabs)||$("#"+tabs).bind("avtTabChange",{that:this},this.refreshEditor)},onValueChanged:function(){return this.get("internalChange")===!0?void this.set("internalChange",!1):(this.get("editor").setValue(this.get("value")),void this.set("internalChange",!1))}.observes("value"),refreshEditor:function(event,tab){var that=event.data.that;that.get("tab")===tab&&that.get("editor").refresh()},willDestroyElement:function(){var tabs=this.get("tabs");Ember.isEmpty(tabs)||$("#"+tabs).unbind("avtTabChange",this.refreshEditor);var editor=this.get("editor");editor.toTextArea(),editor=null,this.set("editor",null)}});for(var patterns='<div class="msp-pattern-prev ms-pattern"></div>',i=1;30>=i;i++)patterns+='<div class="msp-pattern-prev ms-pattern ms-patt-'+i+'" data-pattern="ms-patt-'+i+'" style="'+(i>15?"background-color:black;":"")+'"></div>';MSPanel.PatternPickerComponent=Ember.Component.extend({tagName:"div",classNames:["msp-pattern-picker"],value:null,patternBoxIsOpen:!1,layout:Ember.Handlebars.compile('<div class="msp-pattern-control sp-replacer sp-light"><div {{bind-attr class=":ms-pattern-preview :ms-pattern view.value"}}></div><div class="sp-dd">▼</div></div><div class="msp-patterns-box">'+patterns+"</div>"),didInsertElement:function(){this.$(".msp-pattern-control").on("click",{that:this},this.togglePatternbox),$(document).on("click",{that:this},this.closePatternbox),this.$(".msp-patterns-box").css("display","none").on("click",function(e){e.stopPropagation()}).find(".ms-pattern").on("click",{that:this},this.selectPattern)},togglePatternbox:function(e){var that=e.data.that;e.stopPropagation(),that.get("patternBoxIsOpen")&&that.closePatternbox(e),that.$(".msp-patterns-box").css("display",""),that.set("patternBoxIsOpen",!0)},closePatternbox:function(e){var that=e.data.that;that.get("patternBoxIsOpen")&&(that.$(".msp-patterns-box").css("display","none"),that.set("patternBoxIsOpen",!1))},selectPattern:function(e){e.stopPropagation();var that=e.data.that;that.set("value",$(this).data("pattern"))},willDestroyElement:function(){this.$(".msp-patterns-box").off("click",this.togglePatternbox).find(".ms-pattern").off("click"),$(document).off("click",this.closePatternbox)}})}(jQuery),MSPanel.pushData=null,MSPanel.ApplicationController=Ember.Controller.extend({isFlickr:"flickr"===__MSP_TYPE,isFacebook:"facebook"===__MSP_TYPE,isPost:"post"===__MSP_TYPE,isWcproduct:"wc-product"===__MSP_TYPE,sliderId:MSPanel.SliderID,isSending:!1,statusMsg:"",hasError:!1,onInit:function(){window.$||(window.$=jQuery.noConflict()),MSPanel.Settings.find(),MSPanel.Slide.find(),MSPanel.Layer.find(),MSPanel.Style.find(),MSPanel.Effect.find(),MSPanel.PresetStyle.find(),MSPanel.PresetEffect.find(),MSPanel.Control.find(),MSPanel.Callback.find(),MSPanel.ButtonStyle.find(),this.set("disableControls",MSPanel.Settings.find(0).get("disableControls"));var that=this;MSPanel.pushData=function(){that.prepareData()},MSPanel.createButton=this.createButton,"wc-product"===__MSP_TYPE&&null==__MSP_POST&&null!=__WC_INSTALL_URL&&(this.set("hasError",!0),this.set("errorTemplate","wooc-error"),this.set("wooLink",__WC_INSTALL_URL)),this.generateButtonStyles(),this.set("shortCode",'[masterslider id="'+this.get("sliderId")+'"]'),this.set("phpFunction","<?php masterslider("+this.get("sliderId")+"); ?>"),jQuery("#panelLoading").remove(),window._msp_init_timeout&&clearTimeout(window._msp_init_timeout),$(window).scroll(function(){$(window).scrollTop()+$(window).height()>=$(document).height()-45?($("#saveBar").removeClass("msp-sticky-bar"),$("#saveBarPlaceHolder").css("display","none")):($("#saveBar").addClass("msp-sticky-bar"),$("#saveBarPlaceHolder").css("display",""))}).trigger("scroll"),$("#timeAgo").timeago(),setInterval($.proxy(this.updateSavedTime,this),3e4),$(document).bind("keydown",function(e){return(e.metaKey||e.ctrlKey)&&83==e.which?(e.preventDefault(),that.get("isSending")||that.send("saveAll"),!1):void 0})}.on("init"),updateSliderSlugShortCodes:function(){var alias=this.get("sliderSlug");Ember.isEmpty(alias)&&(alias=MSPanel.SliderSlug),this.set("shortCodeSlug",'[masterslider alias="'+alias+'"]'),this.set("phpFunctionSlug",'<?php masterslider("'+alias+'"); ?>')}.observes("sliderSlug").on("init"),prepareData:function(){var fonts={},font_str="";MSPanel.Style.find().forEach(function(record){var font=record.get("fontFamily"),weight=record.get("fontWeight");Ember.isEmpty(font)||(fonts[font]||(fonts[font]=[]),"normal"===weight&&(weight="regular"),Ember.isEmpty(weight)||-1!==fonts[font].indexOf(weight)||fonts[font].push(weight))});for(var font in fonts)font_str+=font.replace(/\s/,"+")+":"+fonts[font].join(",")+"|";MSPanel.Settings.find(1).set("usedFonts",font_str.slice(0,-1)),this.saveRecords(MSPanel.Settings.find()),this.saveRecords(MSPanel.Slide.find()),this.saveRecords(MSPanel.Layer.find()),this.saveRecords(MSPanel.Style.find()),this.saveRecords(MSPanel.Effect.find()),this.saveRecords(MSPanel.PresetStyle.find()),this.saveRecords(MSPanel.PresetEffect.find()),this.saveRecords(MSPanel.Control.find()),this.saveRecords(MSPanel.Callback.find()),this.saveRecords(MSPanel.ButtonStyle.find())},generateButtonStyles:function(){var styles=MSPanel.ButtonStyle.find(),css="",$styleElement=$("#msp-buttons");styles.forEach(function(style){css+="."+style.get("className")+" {"+style.get("normal")+"}\n."+style.get("className")+":hover {"+style.get("hover")+"}\n."+style.get("className")+":active {"+style.get("active")+"}\n"}),0===$styleElement.length?$styleElement=$('<style id="msp-buttons"></style>').text(css).appendTo($("head")):$styleElement.text(css)},actions:{saveAll:function(){this.prepareData(),this.sendData()},showPreview:function(event){window.lunchMastersliderPreview&&lunchMastersliderPreview(event)}},saveRecords:function(records){records.forEach(function(record){record.save()})},sendData:function(){this.set("statusMsg",__MSP_LAN.ap_001),this.set("isSending",!0),this.set("savingStatus","msp-saving");var that=this;jQuery.post(__MS.ajax_url,{action:"msp_panel_handler",nonce:jQuery("#msp-main-wrapper").data("nonce"),msp_data:B64.encode(JSON.stringify(MSPanel.data)),preset_style:B64.encode(JSON.stringify(MSPanel.PSData)),preset_effect:B64.encode(JSON.stringify(MSPanel.PEData)),buttons:B64.encode(JSON.stringify(MSPanel.PBData)),slider_id:MSPanel.SliderID},function(res){that.set("statusMsg",res.message),that.set("isSending",!1),res.success===!0?(that.set("savingStatus","msp-save-succeed"),that.startAutoHideMsg()):that.set("savingStatus","msp-save-error")})},startAutoHideMsg:function(){var timeout=this.get("msgTimeout"),that=this;Ember.isEmpty(timeout)||clearTimeout(timeout),this.set("timeout",setTimeout(function(){that.set("savingStatus","msp-save-hide msp-save-succeed"),$("#timeAgo").attr("title",(new Date).toISOString()),that.updateSavedTime()},2e3))},updateSavedTime:function(){var timeEle=$("#timeAgo");timeEle.attr("title")&&$("#timeAgo").timeago("updateFromDOM")},createButton:function(normal,hover,active,style,size){var newPreset=MSPanel.ButtonStyle.create({normal:normal,hover:hover,active:active,size:size,style:style});newPreset.save(),newPreset.set("className","msp-preset-btn-"+newPreset.get("id")),newPreset.save()}}),MSPanel.SettingsController=Ember.ObjectController.extend({customSlider:window.__MSP_TYPE&&"custom"===window.__MSP_TYPE,sliderSkins:__MSP_SKINS,needs:["application","controls"],showAutoHeight:!1,showNearbyNum:!1,showWrapperWidth:!1,preloadMethod:null,setup:function(){var preload=this.get("preload");"all"===preload||"-1"===preload?this.set("preloadMethod",preload):this.set("preloadMethod","nearby"),this.set("draftMSTemplate",this.get("msTemplate"))},sliderLayoutChanged:function(){var layout=this.get("layout");"fullscreen"===layout||"autofill"===layout?(this.set("showAutoHeight",!1),this.set("autoHeight",!1)):this.set("showAutoHeight",!0),this.set("showWrapperWidth","boxed"===layout||"partialview"===layout),this.set("showAutoFillTarget","autofill"===layout),this.set("showMinHeight","fullscreen"!==layout&&"autofill"!==layout&&!this.get("autoHeight")),this.set("showFSMargin","fullscreen"===layout)}.observes("layout","autoHeight").on("setup"),preloadSetup:function(){var preloadMethod=this.get("preloadMethod");if("nearby"===preloadMethod){this.set("showNearbyNum",!0);var preload=this.get("preload");("all"===preload||"-1"===preload)&&this.set("preload","0")}else this.set("showNearbyNum",!1),this.set("preload",preloadMethod)}.observes("preloadMethod").on("setup"),actions:{}}),MSPanel.SlidesController=Ember.ArrayController.extend({customSlider:window.__MSP_TYPE&&"custom"===window.__MSP_TYPE,_order:-1,bgImgSelector:null,sortProperties:["order"],stylesController:null,effectsController:null,buttonsController:null,layersList:[],layersController:null,mainView:null,isFirst:!0,currentSlide:null,setup:function(){if(Ember.isEmpty(this.get("sliderSettings.type"))&&this.set("sliderSettings.type",__MSP_TYPE),this.set("sliderSettings.sliderId",MSPanel.SliderID),0!==this.get("length")){var slide=this.get("firstObject");this.set("currentSlide",slide),this.updateOrder()}},slide:function(){return this.get("currentSlide")}.property("currentSlide"),duplicateSlide:function(slide){var slideProp=slide.toJSON();delete slideProp.id;var newSlide=MSPanel.Slide.create(slideProp);newSlide.set("order",slide.get("order")+1),this.forEach(function(_slide){var slide_order=_slide.get("order"),nslide_order=newSlide.get("order");slide_order>=nslide_order&&_slide!==newSlide&&_slide.set("order",slide_order+1)}),newSlide.save(),this.updateOrder()},updateSlidesSort:function(indexes){this.beginPropertyChanges(),this.forEach(function(slide){slide.set("order",indexes[slide.get("id")])},this),this.endPropertyChanges(),this.set("_order",this.get("lastObject.order"))},updateOrder:function(){var i=0;this.forEach(function(slide){slide.get("isOverlayLayers")||slide.set("order",i++)}),this.set("_order",i-1)},removeSlide:function(slide){slide.deleteRecord(),0!==this.get("length")&&(this.send("select",this.get("firstObject")),this.updateOrder())},onImageSelect:function(){var uploaderFrame=this.get("uploaderFrame"),selection=uploaderFrame.state().get("selection"),self=this;selection.map(function(attachment){attachment=attachment.toJSON(),self.generateSlide(attachment,attachment.url,(attachment.sizes.thumbnail||attachment.sizes.full).url)}),this.send("select",this.get("lastObject"))},generateSlide:function(attachment,bg,thumb){var slide=MSPanel.Slide.create({order:this.get("_order")+1,bg:bg,bgThumb:thumb,bgAlt:attachment.alt,bgTitle:attachment.title});this.set("_order",this.get("_order")+1),slide.save(),this.updateOrder()},actions:{addSlides:function(){var uploaderFrame=this.get("uploaderFrame");Ember.isEmpty(uploaderFrame)&&(uploaderFrame=wp.media.frames.frame=wp.media({title:__MSP_LAN.slc_001||"Select background image for new slide. (Multiple selection is available)",multiple:!0,frame:"select",library:{type:"image"},button:{text:__MSP_LAN.slc_002||"Create Slide(s)"}}),uploaderFrame.on("select",$.proxy(this.onImageSelect,this)),this.set("uploaderFrame",uploaderFrame)),uploaderFrame.open()},newSlide:function(){var slide=MSPanel.Slide.create({order:this.get("_order")+1});this.set("currentSlide",slide),this.set("_order",this.get("_order")+1),slide.save(),this.updateOrder()},select:function(slide){slide!==this.get("currentSlide")&&this.set("currentSlide",slide)}}}),MSPanel.ControlsController=Ember.ArrayController.extend({needs:"application",controls:[{used:!1,label:__MSP_LAN.cc_001,value:"arrows"},{used:!1,label:__MSP_LAN.cc_002,value:"timebar"},{used:!1,label:__MSP_LAN.cc_003,value:"bullets"},{used:!1,label:__MSP_LAN.cc_004,value:"circletimer"},{used:!1,label:__MSP_LAN.cc_005,value:"scrollbar"},{used:!1,label:__MSP_LAN.cc_006,value:"slideinfo"},{used:!1,label:__MSP_LAN.cc_007,value:"thumblist"}],selectedControl:null,availableControls:[],noMore:!1,currentControl:null,setup:function(){var that=this;this.forEach(function(control){that.findControlObj(control.get("name")).used=!0}),this.set("availableControls",this.findAvailableControls())},onInsertThumb:function(){var ctr=this.get("currentControl");Ember.isEmpty(ctr)||("tabs"===this.get("currentControl.type")?this.set("isTab",!0):this.set("isTab",!1))}.observes("currentControl.type").on("didInsertElement"),actions:{addControl:function(){var control,controlName=this.get("selectedControl"),controlObj=this.findControlObj(controlName);control=MSPanel.Control.create(this.getDefaultValues(controlName)),control.set("label",controlObj.label),controlObj.used=!0,this.set("availableControls",this.findAvailableControls()),control.save(),this.set("currentControl",control)},removeControl:function(control){this.findControlObj(control.get("name")).used=!1,this.set("availableControls",this.findAvailableControls()),control.deleteRecord(),this.set("currentControl",this.get("firstObject")),this.send("showControlOptions")},showControlOptions:function(){var currentControl=this.get("currentControl");Ember.isEmpty(currentControl)?this.set("controlOptions","empty-template"):this.set("controlOptions",currentControl.get("name")+"-options")}},findControlObj:function(control){for(var controls=this.get("controls"),i=0,l=controls.length;i!==l;i++)if(controls[i].value===control)return controls[i];return null},findAvailableControls:function(){for(var avc=[],controls=this.get("controls"),i=0,l=controls.length;i!==l;i++)controls[i].used||avc.push(controls[i]);return this.set("noMore",0===avc.length),this.set("selectedControl",avc[0]?avc[0].value:null),avc},getDefaultValues:function(control){var values={name:control};switch(values.inset=!("slideinfo"===control||"thumblist"===control),control){case"timebar":values.align="bottom",values.color="#FFFFFF",values.autoHide=!1,values.width=4;break;case"bullets":values.align="bottom",values.dir="h",values.margin=10,values.space=6;break;case"circletimer":values.color="#A2A2A2",values.stroke=10,values.radius=4,values.autoHide=!1;break;case"scrollbar":values.align="top",values.dir="h",values.color="#3D3D3D",values.margin=10,values.autoHide=!1,values.width=4;break;case"slideinfo":values.align="bottom",values.margin=10,values.autoHide=!1;break;case"thumblist":values.align="bottom",values.space=5,values.width=100,values.height=80,values.margin=10,values.fillMode="fill",values.autoHide=!1}return values}}),MSPanel.CallbacksController=Ember.ArrayController.extend({callbacks:[{used:!1,label:__MSP_LAN.cb_011,value:"INIT"},{used:!1,label:__MSP_LAN.cb_001,value:"CHANGE_START"},{used:!1,label:__MSP_LAN.cb_002,value:"CHANGE_END"},{used:!1,label:__MSP_LAN.cb_003,value:"WAITING"},{used:!1,label:__MSP_LAN.cb_004,value:"RESIZE"},{used:!1,label:__MSP_LAN.cb_005,value:"VIDEO_PLAY"},{used:!1,label:__MSP_LAN.cb_006,value:"VIDEO_CLOSE"},{used:!1,label:__MSP_LAN.cb_007,value:"SWIPE_START"},{used:!1,label:__MSP_LAN.cb_008,value:"SWIPE_MOVE"},{used:!1,label:__MSP_LAN.cb_009,value:"SWIPE_END"}],availableCallbacks:[],noMore:!1,selectedCallback:null,setup:function(){var that=this;this.forEach(function(callback){that.findCallbackObj(callback.get("name")).used=!0}),this.set("availableCallbacks",this.findAvailableCallbacks())},actions:{addCallback:function(){var callback,callbackName=this.get("selectedCallback"),callbackObj=this.findCallbackObj(callbackName);callback=MSPanel.Callback.create({name:callbackObj.value,label:callbackObj.label}),callbackObj.used=!0,this.set("availableCallbacks",this.findAvailableCallbacks()),callback.save()},removeCallback:function(callback){confirm(__MSP_LAN.cb_010.jfmt(callback.get("label")))&&(this.findCallbackObj(callback.get("name")).used=!1,this.set("availableCallbacks",this.findAvailableCallbacks()),callback.deleteRecord())}},findCallbackObj:function(callback){for(var callbacks=this.get("callbacks"),i=0,l=callbacks.length;i!==l;i++)if(callbacks[i].value===callback)return callbacks[i];return null},findAvailableCallbacks:function(){for(var avc=[],callbacks=this.get("callbacks"),i=0,l=callbacks.length;i!==l;i++)callbacks[i].used||avc.push(callbacks[i]);return this.set("noMore",0===avc.length),this.set("selectedCallback",avc[0]?avc[0].value:null),avc}});