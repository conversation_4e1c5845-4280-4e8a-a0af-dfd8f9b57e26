!function(){var VERSION="0.0.14";Ember.libraries&&Ember.libraries.register("Ember Model",VERSION)}(),function(){function mustImplement(message){var fn=function(){var className=this.constructor.toString();throw new Error(message.replace("{{className}}",className))};return fn.isUnimplemented=!0,fn}Ember.Adapter=Ember.Object.extend({find:mustImplement("{{className}} must implement find"),findQuery:mustImplement("{{className}} must implement findQuery"),findMany:mustImplement("{{className}} must implement findMany"),findAll:mustImplement("{{className}} must implement findAll"),createRecord:mustImplement("{{className}} must implement createRecord"),saveRecord:mustImplement("{{className}} must implement saveRecord"),deleteRecord:mustImplement("{{className}} must implement deleteRecord"),load:function(record,id,data){record.load(id,data)}})}(),function(){var get=Ember.get,set=Ember.set;Ember.FixtureAdapter=Ember.Adapter.extend({_counter:0,_findData:function(klass,id){var fixtures=klass.FIXTURES,idAsString=id.toString(),primaryKey=get(klass,"primaryKey"),data=Ember.A(fixtures).find(function(el){return el[primaryKey].toString()===idAsString});return data},_setPrimaryKey:function(record){var klass=record.constructor,primaryKey=(klass.FIXTURES,get(klass,"primaryKey"));record.get(primaryKey)||set(record,primaryKey,this._generatePrimaryKey())},_generatePrimaryKey:function(){var counter=this.get("_counter");return this.set("_counter",counter+1),"fixture-"+counter},find:function(record,id){var data=this._findData(record.constructor,id);return new Ember.RSVP.Promise(function(resolve){Ember.run.later(this,function(){Ember.run(record,record.load,id,data),resolve(record)},0)})},findMany:function(klass,records,ids){for(var requestedData=(klass.FIXTURES,[]),i=0,l=ids.length;l>i;i++)requestedData.push(this._findData(klass,ids[i]));return new Ember.RSVP.Promise(function(resolve){Ember.run.later(this,function(){Ember.run(records,records.load,klass,requestedData),resolve(records)},0)})},findAll:function(klass,records){var fixtures=klass.FIXTURES;return new Ember.RSVP.Promise(function(resolve){Ember.run.later(this,function(){Ember.run(records,records.load,klass,fixtures),resolve(records)},0)})},createRecord:function(record){var klass=record.constructor,fixtures=klass.FIXTURES,self=this;return new Ember.RSVP.Promise(function(resolve){Ember.run.later(this,function(){var json,rootKey=record.constructor.rootKey;self._setPrimaryKey(record),json=rootKey?record.toJSON()[rootKey]:record.toJSON(),fixtures.push(klass.findFromCacheOrLoad(json)),record.didCreateRecord(),resolve(record)},0)})},saveRecord:function(record){return new Ember.RSVP.Promise(function(resolve){Ember.run.later(this,function(){record.didSaveRecord(),resolve(record)},0)})},deleteRecord:function(record){return new Ember.RSVP.Promise(function(resolve){Ember.run.later(this,function(){record.didDeleteRecord(),resolve(record)},0)})}})}(),function(){var get=Ember.get,set=Ember.set;Ember.RecordArray=Ember.ArrayProxy.extend(Ember.Evented,{isLoaded:!1,isLoading:Ember.computed.not("isLoaded"),load:function(klass,data){set(this,"content",this.materializeData(klass,data)),this.notifyLoaded()},loadForFindMany:function(klass){var self=this,content=get(this,"_ids").map(function(id){return klass.cachedRecordForId(id,self.container)});set(this,"content",Ember.A(content)),this.notifyLoaded()},notifyLoaded:function(){set(this,"isLoaded",!0),this.trigger("didLoad")},materializeData:function(klass,data){var self=this;return Ember.A(data.map(function(el){return klass.findFromCacheOrLoad(el,self.container)}))},reload:function(){var promises,modelClass=this.get("modelClass"),self=this;return set(this,"isLoaded",!1),modelClass._findAllRecordArray===this?modelClass.adapter.findAll(modelClass,this):this._query?modelClass.adapter.findQuery(modelClass,this,this._query):(promises=this.map(function(record){return record.reload()}),Ember.RSVP.all(promises).then(function(){self.notifyLoaded()}))}})}(),function(){var get=Ember.get;Ember.FilteredRecordArray=Ember.RecordArray.extend({init:function(){if(!get(this,"modelClass"))throw new Error("FilteredRecordArrays must be created with a modelClass");if(!get(this,"filterFunction"))throw new Error("FilteredRecordArrays must be created with a filterFunction");if(!get(this,"filterProperties"))throw new Error("FilteredRecordArrays must be created with filterProperties");var modelClass=get(this,"modelClass");modelClass.registerRecordArray(this),this.registerObservers(),this.updateFilter(),this._super()},updateFilter:function(){var self=this,results=[];get(this,"modelClass").forEachCachedRecord(function(record){self.filterFunction(record)&&results.push(record)}),this.set("content",Ember.A(results))},updateFilterForRecord:function(record){var results=get(this,"content");this.filterFunction(record)&&!results.contains(record)&&results.pushObject(record)},registerObservers:function(){var self=this;get(this,"modelClass").forEachCachedRecord(function(record){self.registerObserversOnRecord(record)})},registerObserversOnRecord:function(record){for(var self=this,filterProperties=get(this,"filterProperties"),i=0,l=get(filterProperties,"length");l>i;i++)record.addObserver(filterProperties[i],self,"updateFilterForRecord")}})}(),function(){var get=Ember.get,set=Ember.set;Ember.ManyArray=Ember.RecordArray.extend({_records:null,originalContent:null,_modifiedRecords:null,unloadObject:function(record){var obj=get(this,"content").findBy("clientId",record._reference.clientId);get(this,"content").removeObject(obj);var originalObj=get(this,"originalContent").findBy("clientId",record._reference.clientId);get(this,"originalContent").removeObject(originalObj)},isDirty:function(){var originalContent=get(this,"originalContent"),originalContentLength=get(originalContent,"length"),content=get(this,"content"),contentLength=get(content,"length");if(originalContentLength!==contentLength)return!0;if(this._modifiedRecords&&this._modifiedRecords.length)return!0;for(var isDirty=!1,i=0,l=contentLength;l>i;i++)if(!originalContent.contains(content[i])){isDirty=!0;break}return isDirty}.property("content.[]","originalContent.[]","_modifiedRecords.[]"),objectAtContent:function(idx){var content=get(this,"content");if(content.length){var observerNeeded=content[idx].record?!1:!0,record=this.materializeRecord(idx,this.container);if(observerNeeded){var isDirtyRecord=record.get("isDirty"),isNewRecord=record.get("isNew");(isDirtyRecord||isNewRecord)&&this._modifiedRecords.pushObject(content[idx]),Ember.addObserver(content[idx],"record.isDirty",this,"recordStateChanged"),record.registerParentHasManyArray(this)}return record}},save:function(){return Ember.RSVP.all(this.map(function(record){return record.save()}))},replaceContent:function(index,removed,added){added=Ember.EnumerableUtils.map(added,function(record){return record._reference},this),this._super(index,removed,added)},_contentWillChange:function(){var content=get(this,"content");content&&(this.arrayWillChange(content,0,get(content,"length"),0),content.removeArrayObserver(this),this._setupOriginalContent(content))}.observesBefore("content"),_contentDidChange:function(){var content=get(this,"content");content&&(content.addArrayObserver(this),this.arrayDidChange(content,0,0,get(content,"length")))}.observes("content"),arrayWillChange:function(item,idx,removedCnt){for(var content=item,i=idx;idx+removedCnt>i;i++){var currentItem=content[i];currentItem&&currentItem.record&&(this._modifiedRecords.removeObject(currentItem),currentItem.record.unregisterParentHasManyArray(this),Ember.removeObserver(currentItem,"record.isDirty",this,"recordStateChanged"))}},arrayDidChange:function(item,idx,removedCnt,addedCnt){for(var parent=get(this,"parent"),relationshipKey=get(this,"relationshipKey"),isDirty=get(this,"isDirty"),content=item,i=idx;idx+addedCnt>i;i++){var currentItem=content[i];if(currentItem&&currentItem.record){var isDirtyRecord=currentItem.record.get("isDirty"),isNewRecord=currentItem.record.get("isNew");(isDirtyRecord||isNewRecord)&&this._modifiedRecords.pushObject(currentItem),Ember.addObserver(currentItem,"record.isDirty",this,"recordStateChanged"),currentItem.record.registerParentHasManyArray(this)}}isDirty?parent._relationshipBecameDirty(relationshipKey):parent._relationshipBecameClean(relationshipKey)},load:function(content){Ember.setProperties(this,{content:content,originalContent:content.slice()}),set(this,"_modifiedRecords",[])},revert:function(){this._setupOriginalContent()},_setupOriginalContent:function(content){content=content||get(this,"content"),content&&set(this,"originalContent",content.slice()),set(this,"_modifiedRecords",[])},init:function(){this._super(),this._setupOriginalContent(),this._contentDidChange()},recordStateChanged:function(obj){var parent=get(this,"parent"),relationshipKey=get(this,"relationshipKey");obj.record.get("isDirty")?(-1===this._modifiedRecords.indexOf(obj)&&this._modifiedRecords.pushObject(obj),parent._relationshipBecameDirty(relationshipKey)):(this._modifiedRecords.indexOf(obj)>-1&&this._modifiedRecords.removeObject(obj),this.get("isDirty")||parent._relationshipBecameClean(relationshipKey))}}),Ember.HasManyArray=Ember.ManyArray.extend({materializeRecord:function(idx,container){var klass=get(this,"modelClass"),content=get(this,"content"),reference=content.objectAt(idx),record=reference.record;return record?(record.container||(record.container=container),record):klass._findFetchById(reference.id,!1,container)},toJSON:function(){var ids=[],content=this.get("content");return content.forEach(function(reference){reference.id&&ids.push(reference.id)}),ids}}),Ember.EmbeddedHasManyArray=Ember.ManyArray.extend({create:function(attrs){var klass=get(this,"modelClass"),record=klass.create(attrs);return this.pushObject(record),record},materializeRecord:function(idx,container){var record,klass=get(this,"modelClass"),primaryKey=get(klass,"primaryKey"),content=get(this,"content"),reference=content.objectAt(idx),attrs=reference.data;return reference.record?record=reference.record:(record=klass.create({_reference:reference,container:container}),reference.record=record,attrs&&record.load(attrs[primaryKey],attrs)),record.container=container,record},toJSON:function(){return this.map(function(record){return record.toJSON()})}})}(),function(){function contains(array,element){for(var i=0,l=array.length;l>i;i++)if(array[i]===element)return!0;return!1}function concatUnique(toArray,fromArray){for(var e,i=0,l=fromArray.length;l>i;i++)e=fromArray[i],contains(toArray,e)||toArray.push(e);return toArray}var get=Ember.get,set=Ember.set,underscore=(Ember.setProperties,Ember.meta,Ember.String.underscore);Ember.run.queues.push("data"),Ember.Model=Ember.Object.extend(Ember.Evented,{isLoaded:!0,isLoading:Ember.computed.not("isLoaded"),isNew:!0,isDeleted:!1,_dirtyAttributes:null,getAttr:function(key,value){return value},isDirty:function(){var dirtyAttributes=get(this,"_dirtyAttributes");return dirtyAttributes&&0!==dirtyAttributes.length||!1}.property("_dirtyAttributes.length"),_relationshipBecameDirty:function(name){var dirtyAttributes=get(this,"_dirtyAttributes");dirtyAttributes.contains(name)||dirtyAttributes.pushObject(name)},_relationshipBecameClean:function(name){var dirtyAttributes=get(this,"_dirtyAttributes");dirtyAttributes.removeObject(name)},dataKey:function(key){var camelizeKeys=get(this.constructor,"camelizeKeys"),meta=this.constructor.metaForProperty(key);return meta.options&&meta.options.key?camelizeKeys?underscore(meta.options.key):meta.options.key:camelizeKeys?underscore(key):key},init:function(){this._createReference(),this._dirtyAttributes||set(this,"_dirtyAttributes",[]),this._super()},_createReference:function(){var reference=this._reference,id=this.getPrimaryKey();return reference?reference.id!==id&&(reference.id=id,this.constructor._cacheReference(reference)):(reference=this.constructor._getOrCreateReferenceForId(id),reference.record=this,this._reference=reference),reference.id||(reference.id=id),reference},getPrimaryKey:function(){return get(this,get(this.constructor,"primaryKey"))},load:function(id,hash){var data={};data[get(this.constructor,"primaryKey")]=id,set(this,"_data",Ember.merge(data,hash)),this.getWithDefault("_dirtyAttributes",[]).clear(),this._reloadHasManys();for(var relationshipKey,relationship,relationshipMeta,relationshipData,relationshipType,relationships=this.constructor._relationships||[],meta=Ember.meta(this),i=0,l=relationships.length;l>i;i++)relationshipKey=relationships[i],relationship=meta.descs[relationshipKey],relationshipMeta=relationship.meta(),relationshipMeta.options.embedded&&(relationshipType=relationshipMeta.type,"string"==typeof relationshipType&&(relationshipType=Ember.get(Ember.lookup,relationshipType)||this.container.lookupFactory("model:"+relationshipType)),relationshipData=data[relationshipKey],relationshipData&&relationshipType.load(relationshipData));set(this,"isNew",!1),set(this,"isLoaded",!0),this._createReference(),this.trigger("didLoad")},didDefineProperty:function(proto,key,value){if(value instanceof Ember.Descriptor){var meta=value.meta(),klass=proto.constructor;meta.isAttribute?(klass._attributes||(klass._attributes=[]),klass._attributes.push(key)):meta.isRelationship&&(klass._relationships||(klass._relationships=[]),klass._relationships.push(key),meta.relationshipKey=key)}},serializeHasMany:function(key){return this.get(key).toJSON()},serializeBelongsTo:function(key,meta){if(meta.options.embedded){var record=this.get(key);return record?record.toJSON():null}var primaryKey=get(meta.getType(this),"primaryKey");return this.get(key+"."+primaryKey)},toJSON:function(){var key,meta,json={},attributes=this.constructor.getAttributes(),relationships=this.constructor.getRelationships(),properties=attributes?this.getProperties(attributes):{},rootKey=get(this.constructor,"rootKey");for(key in properties)meta=this.constructor.metaForProperty(key),json[this.dataKey(key)]=meta.type&&meta.type.serialize?meta.type.serialize(properties[key]):meta.type&&Ember.Model.dataTypes[meta.type]?Ember.Model.dataTypes[meta.type].serialize(properties[key]):properties[key];if(relationships)for(var data,relationshipKey,i=0;i<relationships.length;i++)key=relationships[i],meta=this.constructor.metaForProperty(key),relationshipKey=meta.options.key||key,data="belongsTo"===meta.kind?this.serializeBelongsTo(key,meta):this.serializeHasMany(key,meta),json[relationshipKey]=data;if(rootKey){var jsonRoot={};return jsonRoot[rootKey]=json,jsonRoot}return json},save:function(){var adapter=this.constructor.adapter;if(set(this,"isSaving",!0),get(this,"isNew"))return adapter.createRecord(this);if(get(this,"isDirty"))return adapter.saveRecord(this);var self=this,promise=new Ember.RSVP.Promise(function(resolve){resolve(self)});return set(this,"isSaving",!1),promise},reload:function(){return this.getWithDefault("_dirtyAttributes",[]).clear(),this.constructor.reload(this.get(get(this.constructor,"primaryKey")),this.container)},revert:function(){this.getWithDefault("_dirtyAttributes",[]).clear(),this.notifyPropertyChange("_data"),this._reloadHasManys(!0)},didCreateRecord:function(){{var primaryKey=get(this.constructor,"primaryKey");get(this,primaryKey)}set(this,"isNew",!1),set(this,"_dirtyAttributes",[]),this.constructor.addToRecordArrays(this),this.trigger("didCreateRecord"),this.didSaveRecord()},didSaveRecord:function(){set(this,"isSaving",!1),this.trigger("didSaveRecord"),this.get("isDirty")&&this._copyDirtyAttributesToData()},deleteRecord:function(){return this.constructor.adapter.deleteRecord(this)},didDeleteRecord:function(){this.constructor.removeFromRecordArrays(this),set(this,"isDeleted",!0),this.trigger("didDeleteRecord")},_copyDirtyAttributesToData:function(){if(this._dirtyAttributes){var key,dirtyAttributes=this._dirtyAttributes,data=get(this,"_data");data||(data={},set(this,"_data",data));for(var i=0,l=dirtyAttributes.length;l>i;i++)key=dirtyAttributes[i],data[this.dataKey(key)]=this.cacheFor(key);set(this,"_dirtyAttributes",[]),this._resetDirtyStateInNestedObjects(this)}},_resetDirtyStateInNestedObjects:function(object){var i,obj;if(object._hasManyArrays)for(i=0;i<object._hasManyArrays.length;i++){var array=object._hasManyArrays[i];if(array.revert(),array.embedded)for(var j=0;j<array.get("length");j++)obj=array.objectAt(j),obj._copyDirtyAttributesToData()}if(object._belongsTo)for(i=0;i<object._belongsTo.length;i++){var belongsTo=object._belongsTo[i];belongsTo.options.embedded&&(obj=this.get(belongsTo.relationshipKey),obj&&obj._copyDirtyAttributesToData())}},_registerHasManyArray:function(array){this._hasManyArrays||(this._hasManyArrays=Ember.A([])),this._hasManyArrays.pushObject(array)},registerParentHasManyArray:function(array){this._parentHasManyArrays||(this._parentHasManyArrays=Ember.A([])),this._parentHasManyArrays.pushObject(array)},unregisterParentHasManyArray:function(array){this._parentHasManyArrays&&this._parentHasManyArrays.removeObject(array)},_reloadHasManys:function(reverting){if(this._hasManyArrays){var i,j;for(i=0;i<this._hasManyArrays.length;i++){var array=this._hasManyArrays[i],hasManyContent=this._getHasManyContent(get(array,"key"),get(array,"modelClass"),get(array,"embedded"));if(!reverting)for(j=0;j<array.get("length");j++)array.objectAt(j).get("isNew")&&!array.objectAt(j).get("isDeleted")&&hasManyContent.addObject(array.objectAt(j)._reference);array.load(hasManyContent)}}},_getHasManyContent:function(key,type,embedded){var content=get(this,"_data."+key);if(content){var mapFunction,primaryKey,reference;embedded?(primaryKey=get(type,"primaryKey"),mapFunction=function(attrs){return reference=type._getOrCreateReferenceForId(attrs[primaryKey]),reference.data=attrs,reference}):mapFunction=function(id){return type._getOrCreateReferenceForId(id)},content=Ember.EnumerableUtils.map(content,mapFunction)}return Ember.A(content||[])},_registerBelongsTo:function(key){this._belongsTo||(this._belongsTo=Ember.A([])),this._belongsTo.pushObject(key)}}),Ember.Model.reopenClass({primaryKey:"id",adapter:Ember.Adapter.create(),_clientIdCounter:1,getAttributes:function(){this.proto();var attributes=this._attributes||[];return"function"==typeof this.superclass.getAttributes&&(attributes=this.superclass.getAttributes().concat(attributes)),attributes},getRelationships:function(){this.proto();var relationships=this._relationships||[];return"function"==typeof this.superclass.getRelationships&&(relationships=this.superclass.getRelationships().concat(relationships)),relationships},fetch:function(id){return arguments.length?Ember.isArray(id)?this._findFetchMany(id,!0):"object"==typeof id?this._findFetchQuery(id,!0):this._findFetchById(id,!0):this._findFetchAll(!0)},find:function(id){return arguments.length?Ember.isArray(id)?this._findFetchMany(id,!1):"object"==typeof id?this._findFetchQuery(id,!1):this._findFetchById(id,!1):this._findFetchAll(!1)},findQuery:function(params){return this._findFetchQuery(params,!1)},fetchQuery:function(params){return this._findFetchQuery(params,!0)},_findFetchQuery:function(params,isFetch,container){var records=Ember.RecordArray.create({modelClass:this,_query:params,container:container}),promise=this.adapter.findQuery(this,records,params);return isFetch?promise:records},findMany:function(ids){return this._findFetchMany(ids,!1)},fetchMany:function(ids){return this._findFetchMany(ids,!0)},_findFetchMany:function(ids,isFetch,container){Ember.assert("findFetchMany requires an array",Ember.isArray(ids));var deferred,records=Ember.RecordArray.create({_ids:ids,modelClass:this,container:container});return this.recordArrays||(this.recordArrays=[]),this.recordArrays.push(records),this._currentBatchIds?(concatUnique(this._currentBatchIds,ids),this._currentBatchRecordArrays.push(records)):(this._currentBatchIds=concatUnique([],ids),this._currentBatchRecordArrays=[records]),isFetch&&(deferred=Ember.RSVP.defer(),Ember.set(deferred,"resolveWith",records),this._currentBatchDeferreds||(this._currentBatchDeferreds=[]),this._currentBatchDeferreds.push(deferred)),Ember.run.scheduleOnce("data",this,this._executeBatch,container),isFetch?deferred.promise:records},findAll:function(){return this._findFetchAll(!1)},fetchAll:function(){return this._findFetchAll(!0)},_findFetchAll:function(isFetch,container){var self=this,currentFetchPromise=this._currentFindFetchAllPromise;if(isFetch&&currentFetchPromise)return currentFetchPromise;if(this._findAllRecordArray)return isFetch?new Ember.RSVP.Promise(function(resolve){resolve(self._findAllRecordArray)}):this._findAllRecordArray;var records=this._findAllRecordArray=Ember.RecordArray.create({modelClass:this,container:container}),promise=this._currentFindFetchAllPromise=this.adapter.findAll(this,records);return promise["finally"](function(){self._currentFindFetchAllPromise=null}),promise.then&&promise.then(null,function(){return self._findAllRecordArray=null,Ember.RSVP.reject.apply(null,arguments)}),isFetch?promise:records},findById:function(id){return this._findFetchById(id,!1)},fetchById:function(id){return this._findFetchById(id,!0)},_findFetchById:function(id,isFetch,container){{var deferredOrPromise,record=this.cachedRecordForId(id,container),isLoaded=get(record,"isLoaded");get(this,"adapter")}return isLoaded?isFetch?new Ember.RSVP.Promise(function(resolve){resolve(record)}):record:(deferredOrPromise=this._fetchById(record,id),isFetch?deferredOrPromise:record)},_currentBatchIds:null,_currentBatchRecordArrays:null,_currentBatchDeferreds:null,reload:function(id,container){var record=this.cachedRecordForId(id,container);return record.set("isLoaded",!1),this._fetchById(record,id)},_fetchById:function(record,id){var deferred,adapter=get(this,"adapter");return adapter.findMany&&!adapter.findMany.isUnimplemented?(this._currentBatchIds?contains(this._currentBatchIds,id)||this._currentBatchIds.push(id):(this._currentBatchIds=[id],this._currentBatchRecordArrays=[]),deferred=Ember.RSVP.defer(),Ember.set(deferred,"resolveWith",record),this._currentBatchDeferreds||(this._currentBatchDeferreds=[]),this._currentBatchDeferreds.push(deferred),Ember.run.scheduleOnce("data",this,this._executeBatch,record.container),deferred.promise):adapter.find(record,id)},_executeBatch:function(container){var promise,i,batchIds=this._currentBatchIds,batchRecordArrays=this._currentBatchRecordArrays,batchDeferreds=this._currentBatchDeferreds,self=this,requestIds=[];for(this._currentBatchIds=null,this._currentBatchRecordArrays=null,this._currentBatchDeferreds=null,i=0;i<batchIds.length;i++)this.cachedRecordForId(batchIds[i]).get("isLoaded")||requestIds.push(batchIds[i]);if(1===requestIds.length)promise=get(this,"adapter").find(this.cachedRecordForId(requestIds[0],container),requestIds[0]);else{var recordArray=Ember.RecordArray.create({_ids:batchIds,container:container});0===requestIds.length?(promise=new Ember.RSVP.Promise(function(resolve){resolve(recordArray)}),recordArray.notifyLoaded()):promise=get(this,"adapter").findMany(this,recordArray,requestIds)}promise.then(function(){for(var i=0,l=batchRecordArrays.length;l>i;i++)batchRecordArrays[i].loadForFindMany(self);if(batchDeferreds)for(i=0,l=batchDeferreds.length;l>i;i++){var resolveWith=Ember.get(batchDeferreds[i],"resolveWith");batchDeferreds[i].resolve(resolveWith)}}).then(null,function(errorXHR){if(batchDeferreds)for(var i=0,l=batchDeferreds.length;l>i;i++)batchDeferreds[i].reject(errorXHR)})},getCachedReferenceRecord:function(id,container){var ref=this._getReferenceById(id);return ref&&ref.record?(ref.record.container||(ref.record.container=container),ref.record):void 0},cachedRecordForId:function(id,container){var record;if(this["transient"]||(record=this.getCachedReferenceRecord(id,container)),!record){var primaryKey=get(this,"primaryKey"),attrs={isLoaded:!1};if(attrs[primaryKey]=id,attrs.container=container,record=this.create(attrs),!this["transient"]){var sideloadedData=this.sideloadedData&&this.sideloadedData[id];sideloadedData&&record.load(id,sideloadedData)}}return record},addToRecordArrays:function(record){this._findAllRecordArray&&this._findAllRecordArray.addObject(record),this.recordArrays&&this.recordArrays.forEach(function(recordArray){recordArray instanceof Ember.FilteredRecordArray?(recordArray.registerObserversOnRecord(record),recordArray.updateFilter()):recordArray.addObject(record)})},unload:function(record){this.removeFromHasManyArrays(record),this.removeFromRecordArrays(record);var primaryKey=record.get(get(this,"primaryKey"));this.removeFromCache(primaryKey)},clearCache:function(){this.sideloadedData=void 0,this._referenceCache=void 0,this._findAllRecordArray=void 0},removeFromCache:function(key){this.sideloadedData&&this.sideloadedData[key]&&delete this.sideloadedData[key],this._referenceCache&&this._referenceCache[key]&&delete this._referenceCache[key]},removeFromHasManyArrays:function(record){record._parentHasManyArrays&&(record._parentHasManyArrays.forEach(function(hasManyArray){hasManyArray.unloadObject(record)}),record._parentHasManyArrays=null)},removeFromRecordArrays:function(record){this._findAllRecordArray&&this._findAllRecordArray.removeObject(record),this.recordArrays&&this.recordArrays.forEach(function(recordArray){recordArray.removeObject(record)})},findFromCacheOrLoad:function(data,container){var record;return record=data[get(this,"primaryKey")]?this.cachedRecordForId(data[get(this,"primaryKey")],container):this.create({isLoaded:!1,container:container}),record.load(data[get(this,"primaryKey")],data),record},registerRecordArray:function(recordArray){this.recordArrays||(this.recordArrays=[]),this.recordArrays.push(recordArray)},unregisterRecordArray:function(recordArray){this.recordArrays&&Ember.A(this.recordArrays).removeObject(recordArray)},forEachCachedRecord:function(callback){if(this._referenceCache){var ids=Object.keys(this._referenceCache);ids.map(function(id){return this._getReferenceById(id).record},this).forEach(callback)}},load:function(hashes,container){"array"!==Ember.typeOf(hashes)&&(hashes=[hashes]),this.sideloadedData||(this.sideloadedData={});for(var i=0,l=hashes.length;l>i;i++){var hash=hashes[i],primaryKey=hash[get(this,"primaryKey")],record=this.getCachedReferenceRecord(primaryKey,container);record?record.load(primaryKey,hash):this.sideloadedData[primaryKey]=hash}},_getReferenceById:function(id){return this._referenceCache||(this._referenceCache={}),this._referenceCache[id]},_getOrCreateReferenceForId:function(id){var reference=this._getReferenceById(id);return reference||(reference=this._createReference(id)),reference},_createReference:function(id){this._referenceCache||(this._referenceCache={}),Ember.assert("The id "+id+" has already been used with another record of type "+this.toString()+".",!id||!this._referenceCache[id]);var reference={id:id,clientId:this._clientIdCounter++};return this._cacheReference(reference),reference},_cacheReference:function(reference){this._referenceCache||(this._referenceCache={}),Ember.isEmpty(reference.id)||(this._referenceCache[reference.id]=reference)}})}(),function(){function getType(record){var type=this.type;if("string"==typeof this.type&&this.type&&(this.type=get(Ember.lookup,this.type),!this.type)){var store=record.container.lookup("store:main");this.type=store.modelFor(type),this.type.reopenClass({adapter:store.adapterFor(type)})}return this.type}var get=Ember.get;Ember.hasMany=function(type,options){options=options||{};var meta={type:type,isRelationship:!0,options:options,kind:"hasMany",getType:getType};return Ember.computed(function(propertyKey,newContentArray,existingArray){type=meta.getType(this),Ember.assert("Type cannot be empty",!Ember.isEmpty(type));var key=options.key||propertyKey;return arguments.length>1?existingArray.setObjects(newContentArray):this.getHasMany(key,type,meta,this.container)}).property().meta(meta)},Ember.Model.reopen({getHasMany:function(key,type,meta,container){var embedded=meta.options.embedded,collectionClass=embedded?Ember.EmbeddedHasManyArray:Ember.HasManyArray,collection=collectionClass.create({parent:this,modelClass:type,content:this._getHasManyContent(key,type,embedded),embedded:embedded,key:key,relationshipKey:meta.relationshipKey,container:container});return this._registerHasManyArray(collection),collection}})}(),function(){function storeFor(record){return record.container?record.container.lookup("store:main"):null}function getType(record){var type=this.type;if("string"==typeof this.type&&this.type&&(type=Ember.get(Ember.lookup,this.type),!type)){var store=storeFor(record);type=store.modelFor(this.type),type.reopenClass({adapter:store.adapterFor(this.type)})}return type}var get=Ember.get,set=Ember.set;Ember.belongsTo=function(type,options){options=options||{};var meta={type:type,isRelationship:!0,options:options,kind:"belongsTo",getType:getType};return Ember.computed(function(propertyKey,value,oldValue){type=meta.getType(this),Ember.assert("Type cannot be empty.",!Ember.isEmpty(type));var key=options.key||propertyKey,dirtyAttributes=get(this,"_dirtyAttributes"),createdDirtyAttributes=!1,self=this,dirtyChanged=function(sender){sender.get("isDirty")?self._relationshipBecameDirty(propertyKey):self._relationshipBecameClean(propertyKey)};if(dirtyAttributes||(dirtyAttributes=[],createdDirtyAttributes=!0),arguments.length>1)return value&&Ember.assert(Ember.String.fmt("Attempted to set property of type: %@ with a value of type: %@",[value.constructor,type]),value instanceof type),oldValue!==value?dirtyAttributes.pushObject(propertyKey):dirtyAttributes.removeObject(propertyKey),createdDirtyAttributes&&set(this,"_dirtyAttributes",dirtyAttributes),meta.options.embedded&&(oldValue&&oldValue.removeObserver("isDirty",dirtyChanged),value&&value.addObserver("isDirty",dirtyChanged)),void 0===value?null:value;var store=storeFor(this);return value=this.getBelongsTo(key,type,meta,store),this._registerBelongsTo(meta),null!==value&&meta.options.embedded&&(value.get("isDirty"),value.addObserver("isDirty",dirtyChanged)),value}).property("_data").meta(meta)},Ember.Model.reopen({getBelongsTo:function(key,type,meta,store){var record,idOrAttrs=get(this,"_data."+key);if(Ember.isNone(idOrAttrs))return null;if(meta.options.embedded){var primaryKey=get(type,"primaryKey"),id=idOrAttrs[primaryKey];record=type.create({isLoaded:!1,id:id,container:this.container}),record.load(id,idOrAttrs)}else record=store?store._findSync(meta.type,idOrAttrs):type.find(idOrAttrs);return record}})}(),function(){function deserialize(value,type){return type&&type.deserialize?type.deserialize(value):type&&Ember.Model.dataTypes[type]?Ember.Model.dataTypes[type].deserialize(value):value}function serialize(value,type){return type&&type.serialize?type.serialize(value):type&&Ember.Model.dataTypes[type]?Ember.Model.dataTypes[type].serialize(value):value}var get=Ember.get,set=Ember.set,meta=Ember.meta;Ember.Model.dataTypes={},Ember.Model.dataTypes[Date]={deserialize:function(string){return string?new Date(string):null},serialize:function(date){return date?date.toISOString():null},isEqual:function(obj1,obj2){return obj1 instanceof Date&&(obj1=this.serialize(obj1)),obj2 instanceof Date&&(obj2=this.serialize(obj2)),obj1===obj2}},Ember.Model.dataTypes[Number]={deserialize:function(string){return string||0===string?Number(string):null},serialize:function(number){return number||0===number?Number(number):null}},Ember.attr=function(type,options){return Ember.computed(function(key,value){var data=get(this,"_data"),dataKey=this.dataKey(key),dataValue=data&&get(data,dataKey),beingCreated=meta(this).proto===this,dirtyAttributes=get(this,"_dirtyAttributes"),createdDirtyAttributes=!1;return dirtyAttributes||(dirtyAttributes=[],createdDirtyAttributes=!0),2===arguments.length?(beingCreated&&(data||(data={},set(this,"_data",data)),dataValue=data[dataKey]=value),dataValue!==serialize(value,type)?dirtyAttributes.pushObject(key):dirtyAttributes.removeObject(key),createdDirtyAttributes&&set(this,"_dirtyAttributes",dirtyAttributes),value):null==dataValue&&options&&null!=options.defaultValue?Ember.copy(options.defaultValue):this.getAttr(key,deserialize(dataValue,type))
}).property("_data").meta({isAttribute:!0,type:type,options:options})}}(),function(){var get=Ember.get;Ember.RESTAdapter=Ember.Adapter.extend({find:function(record,id){var url=this.buildURL(record.constructor,id),self=this;return this.ajax(url).then(function(data){return self.didFind(record,id,data),record})},didFind:function(record,id,data){var rootKey=get(record.constructor,"rootKey"),dataToLoad=rootKey?get(data,rootKey):data;record.load(id,dataToLoad)},findAll:function(klass,records){var url=this.buildURL(klass),self=this;return this.ajax(url).then(function(data){return self.didFindAll(klass,records,data),records})},didFindAll:function(klass,records,data){var collectionKey=get(klass,"collectionKey"),dataToLoad=collectionKey?get(data,collectionKey):data;records.load(klass,dataToLoad)},findQuery:function(klass,records,params){var url=this.buildURL(klass),self=this;return this.ajax(url,params).then(function(data){return self.didFindQuery(klass,records,params,data),records})},didFindQuery:function(klass,records,params,data){var collectionKey=get(klass,"collectionKey"),dataToLoad=collectionKey?get(data,collectionKey):data;records.load(klass,dataToLoad)},createRecord:function(record){var url=this.buildURL(record.constructor),self=this;return this.ajax(url,record.toJSON(),"POST").then(function(data){return self.didCreateRecord(record,data),record})},didCreateRecord:function(record,data){this._loadRecordFromData(record,data),record.didCreateRecord()},saveRecord:function(record){var primaryKey=get(record.constructor,"primaryKey"),url=this.buildURL(record.constructor,get(record,primaryKey)),self=this;return this.ajax(url,record.toJSON(),"PUT").then(function(data){return self.didSaveRecord(record,data),record})},didSaveRecord:function(record,data){this._loadRecordFromData(record,data),record.didSaveRecord()},deleteRecord:function(record){var primaryKey=get(record.constructor,"primaryKey"),url=this.buildURL(record.constructor,get(record,primaryKey)),self=this;return this.ajax(url,record.toJSON(),"DELETE").then(function(data){self.didDeleteRecord(record,data)})},didDeleteRecord:function(record){record.didDeleteRecord()},ajax:function(url,params,method,settings){return this._ajax(url,params,method||"GET",settings)},buildURL:function(klass,id){var urlRoot=get(klass,"url"),urlSuffix=get(klass,"urlSuffix")||"";if(!urlRoot)throw new Error("Ember.RESTAdapter requires a `url` property to be specified");return Ember.isEmpty(id)?urlRoot+urlSuffix:urlRoot+"/"+id+urlSuffix},ajaxSettings:function(url,method){return{url:url,type:method,dataType:"json"}},_ajax:function(url,params,method,settings){return settings||(settings=this.ajaxSettings(url,method)),new Ember.RSVP.Promise(function(resolve,reject){params&&("GET"===method?settings.data=params:(settings.contentType="application/json; charset=utf-8",settings.data=JSON.stringify(params))),settings.success=function(json){Ember.run(null,resolve,json)},settings.error=function(jqXHR){jqXHR&&"object"==typeof jqXHR&&(jqXHR.then=null),Ember.run(null,reject,jqXHR)},Ember.$.ajax(settings)})},_loadRecordFromData:function(record,data){var rootKey=get(record.constructor,"rootKey"),primaryKey=get(record.constructor,"primaryKey");data&&(data=rootKey?get(data,rootKey):data,Ember.isEmpty(data)||record.load(data[primaryKey],data))}})}(),function(){var get=Ember.get;Ember.loadPromise=function(target){if(Ember.isNone(target))return null;if(target.then)return target;var deferred=Ember.RSVP.defer();return get(target,"isLoaded")&&!get(target,"isNew")?deferred.resolve(target):target.one("didLoad",this,function(){deferred.resolve(target)}),deferred.promise}}(),function(){if(Ember.DataAdapter){var get=Ember.get,capitalize=Ember.String.capitalize,underscore=Ember.String.underscore,DebugAdapter=Ember.DataAdapter.extend({getFilters:function(){return[{name:"isNew",desc:"New"},{name:"isModified",desc:"Modified"},{name:"isClean",desc:"Clean"}]},detect:function(klass){return klass!==Ember.Model&&Ember.Model.detect(klass)},columnsForType:function(type){var columns=[],count=0,self=this;return type.getAttributes().forEach(function(name){if(count++>self.attributeLimit)return!1;var desc=capitalize(underscore(name).replace("_"," "));columns.push({name:name,desc:desc})}),columns},getRecords:function(type){var records=[];return type.forEachCachedRecord(function(record){records.push(record)}),records},getRecordColumnValues:function(record){var self=this,count=0,columnValues={id:get(record,"id")};return record.constructor.getAttributes().forEach(function(key){if(count++>self.attributeLimit)return!1;var value=get(record,key);columnValues[key]=value}),columnValues},getRecordKeywords:function(record){var keywords=[],keys=Ember.A(["id"]);return record.constructor.getAttributes().forEach(function(key){keys.push(key)}),keys.forEach(function(key){keywords.push(get(record,key))}),keywords},getRecordFilterValues:function(record){return{isNew:record.get("isNew"),isModified:record.get("isDirty")&&!record.get("isNew"),isClean:!record.get("isDirty")}},getRecordColor:function(record){var color="black";return record.get("isNew")?color="green":record.get("isDirty")&&(color="blue"),color},observeRecord:function(record,recordUpdated){var releaseMethods=Ember.A(),self=this,keysToObserve=Ember.A(["id","isNew","isDirty"]);record.constructor.getAttributes().forEach(function(key){keysToObserve.push(key)}),keysToObserve.forEach(function(key){var handler=function(){recordUpdated(self.wrapRecord(record))};Ember.addObserver(record,key,handler),releaseMethods.push(function(){Ember.removeObserver(record,key,handler)})});var release=function(){releaseMethods.forEach(function(fn){fn()})};return release}});Ember.onLoad("Ember.Application",function(Application){Application.initializer({name:"data-adapter",initialize:function(container,application){application.register("data-adapter:main",DebugAdapter)}})})}}(),function(){function NIL(){}Ember.Model.Store=Ember.Object.extend({container:null,modelFor:function(type){return this.container.lookupFactory("model:"+type)},adapterFor:function(type){var adapter=this.modelFor(type).adapter,container=this.container;return adapter&&adapter!==Ember.Model.adapter?adapter:(adapter=container.lookupFactory("adapter:"+type)||container.lookupFactory("adapter:application")||container.lookupFactory("adapter:REST"),adapter?adapter.create():adapter)},createRecord:function(type,props){var klass=this.modelFor(type);return klass.reopenClass({adapter:this.adapterFor(type)}),klass.create(Ember.merge({container:this.container},props))},find:function(type,id){return 1===arguments.length&&(id=NIL),this._find(type,id,!0)},_find:function(type,id,async){var klass=this.modelFor(type);return klass.reopenClass({adapter:this.adapterFor(type)}),id===NIL?klass._findFetchAll(async,this.container):Ember.isArray(id)?klass._findFetchMany(id,async,this.container):"object"==typeof id?klass._findFetchQuery(id,async,this.container):klass._findFetchById(id,async,this.container)},_findSync:function(type,id){return this._find(type,id,!1)}}),Ember.onLoad("Ember.Application",function(Application){Application.initializer({name:"store",initialize:function(container,application){application.register("store:main",container.lookupFactory("store:application")||Ember.Model.Store),application.inject("route","store","store:main"),application.inject("controller","store","store:main")}})})}();
//# sourceMappingURL=ember-model.min.js.map