
/* ----------------------------------
		Master Slider Panel
		Switchbox component
-------------------------------------*/

.msp-switchbox{	 
	width: 73px;
	height: 33px;
	border: solid 1px #DEDEDE;
	border-radius: 100px;
	color: #777;
	background-color: #F5F5F5;
	cursor: pointer;
	overflow: hidden;

	margin-right: 10px;
	line-height: 26px;
	font-size: 0.88em;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	display: inline-block;
	vertical-align: middle;
	-webkit-box-shadow: 7px 7px 0px rgba(0, 0, 0, 0.01) inset;
	-moz-webkit-box-shadow: 7px 7px 0px rgba(0, 0, 0, 0.01) inset;
	-ms-webkit-box-shadow: 7px 7px 0px rgba(0, 0, 0, 0.01) inset;
	-o-webkit-box-shadow: 7px 7px 0px rgba(0, 0, 0, 0.01) inset;
}
	
.msp-switchbox .msp-switch-cont{
	position: relative; 
	left:0;
	-webkit-transition: all 0.25s;
	transition: all 0.25s;
} 
.msp-switchbox.switched .msp-switch-cont{left:40px;}

   
.msp-switchbox .msp-switch-handle{
	display: block;
	width: 23px;
	height: 23px;
	margin: 4px;
	text-indent: 30px;
	background-color: white;
	border-radius: 44px;
	border:solid 1px #dedede;
	-webkit-transition: all 0.25s;
	transition: all 0.25s;
}

.msp-switchbox.switched .msp-switch-handle{
	background-color: #2ea2cc;
	border-color: #2ea2cc;
}

.msp-switchbox .msp-switch-on{
	position: absolute;
	display: block;
	left: -26px;
	top: 0;
}

.msp-switchbox .msp-switch-off{
	position: absolute;
	display: block;
	left:35px;
}

/**
* Native dropdown list
*/
.msp-ddlist {
	display: inline-block;
	position: relative;
	border: solid 1px #dedede;
}

.msp-ddlist>select {
	border: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	height: auto;
	box-shadow: none;
	padding: 8px;
	height: 34px;
	outline: none;
}


.msp-ddlist:after {
	content: "";
	display: block;
	background: url(../images/ui-spritesheet.png) no-repeat -200px -72px;
	width: 10px;
	height: 10px;
	position: absolute;
	top: 11px;
	right: 10px;
}

.msp-ddlist-multiselect {
    vertical-align: top;
}
.msp-ddlist-multiselect>select {
    padding: 5px;
}
.msp-ddlist-multiselect:after {
    display: none;
}

/*Color Picker*/
.sp-replacer {
	border-color: #dedede;
	background: white;
	padding: 6px 7px;
	vertical-align: top;
}

.sp-clear-display {
	background-image: url(../images/no-color.gif);
}

.sp-replacer:hover, .sp-replacer.sp-active {
	border-color: #B3B3B3;
}

.sp-preview {
	border-radius: 3px;
	width: 20px;
	margin: 0 9px 0 1px;
	overflow: hidden;
	border-color: #dedede;
}
.sp-dd {
	text-indent: -99999px;	  
	background: url(../images/ui-spritesheet.png) #FFF no-repeat -200px -72px;
	width: 10px;
	height: 8px;
	margin-top: 5px;
}

.sp-container {
	border-color: #dedede;
	background: white;
	box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.07);
}
.sp-input {
	border-radius: 0;
	border-color: #dedede;
	box-shadow: 5px 5px #F0F0F0 inset;
	border-style: solid;
	background: #f5f5f5;
	outline: none;
}
.sp-color, .sp-hue, .sp-clear {
	border-color: #dedede;
}
.sp-alpha-inner {
	border-color: #dedede;
}
.sp-alpha-handle {
	border-color: #AAAAAA;
}
.sp-container button {
	border-radius: 0;
	background: #FFF;
	border: none;
	border-bottom: solid 2px #F3F3F3;
	color: #444;
	outline: solid 1px #DEDEDE;
	font-size: 13px;
	text-shadow: none;
	padding: 7px 13px;
}
.sp-container button:hover {
	background: #F7F7F7;
	text-shadow: none;
	border: none;
	border-bottom: solid 2px #F3F3F3;
}

/**
 * code mirror
 */

.CodeMirror{
	height: 100%;
	border: solid 1px #dedede;
}