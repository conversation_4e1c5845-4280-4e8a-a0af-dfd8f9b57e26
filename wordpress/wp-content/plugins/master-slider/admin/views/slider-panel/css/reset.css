/*==============================================================
 	$CSS Reset & Clearfix
 *==============================================================*/
html,body { height:100%;}
.msp-container select,.msp-container input,.msp-container textarea,.msp-container button { font:99% sans-serif; }
.msp-container pre,.msp-container code,.msp-container kbd,.msp-container samp { font-family: monospace, sans-serif; }
.msp-container code, .msp-container kbd, .msp-container samp { word-wrap: break-word;line-height: 1.6; }
.msp-container html { overflow-y: scroll; height: 100%;}
.msp-container a { text-decoration: none; }
.msp-container a:hover, .msp-container a:active, .msp-container a:focus { outline: none; }
.msp-container ul,.msp-container ol { margin: 0;} .msp-container ol { list-style-type: decimal; }
.msp-container nav ul,.msp-container nav li { margin: 0; list-style:none; list-style-image: none; }
.msp-container small { font-size: 85%; } .msp-container strong,.msp-container th { font-weight: bold; } .msp-container td { vertical-align: top; }
.msp-container sub, .msp-container sup { font-size: 75%; line-height: 0; position: relative; } .msp-container sup { top: -0.5em; } .msp-container sub { bottom: -0.25em; }
.msp-container pre { white-space: pre; white-space: pre-wrap; word-wrap: break-word; padding: 15px; }
.msp-container textarea { overflow: auto; }
.msp-container .ie6 legend,.msp-container .ie7 legend { margin-left: -7px; } 
.msp-container input[type="radio"] { vertical-align: text-bottom; }
.msp-container input[type="checkbox"] { vertical-align: bottom; }
.msp-container input[type=search] { -webkit-appearance: none; }
.msp-container input[type="search"]::-webkit-search-decoration,.msp-container input[type="search"]::-webkit-search-cancel-button {display: none;}
.msp-container .ie7 input[type="checkbox"] { vertical-align: baseline; }
.msp-container .ie6 input { vertical-align: text-bottom; }
.msp-container label,.msp-container input[type="button"],.msp-container input[type="submit"],.msp-container input[type="image"],.msp-container button { cursor: pointer; }
.msp-container button, .msp-container input,.msp-container select,.msp-container textarea { margin: 0; }
.msp-container input:valid, .msp-container textarea:valid   {  }
.msp-container input:invalid,.msp-container textarea:invalid {
   border-radius: 1px; -moz-box-shadow: 0px 0px 5px red; -webkit-box-shadow: 0px 0px 5px red; box-shadow: 0px 0px 5px red;}
.msp-container .no-boxshadow input:invalid, .msp-container .no-boxshadow textarea:invalid { background-color: #f0dddd; }
.msp-container ::-moz-selection{ background: #FF5E99; color:#fff; text-shadow: none; }
/*.msp-container a:link { -webkit-tap-highlight-color: #FF5E99; }*/
.msp-container button {  width: auto; overflow: visible; }
.msp-container a:focus{
	-webkit-box-shadow: none;
	box-shadow: none;
}
/**
 ======================================
 	$Domument
 ======================================*/
/*-----------------
   $Body
-----------------*/
.msp-container {}
.msp-container,
.msp-container select,
.msp-container input,
.msp-container textarea {
  color: #666;
  -webkit-font-smoothing: antialiased; /* Fix for webkit rendering */
  -webkit-text-size-adjust: 100%;
}
.msp-container li 	{ margin:0; }

.msp-container .ie7 img 	{ -ms-interpolation-mode: bicubic; }
.msp-container .hidden 	{ display: none !important; }
.msp-container .left 		{ float: left; }
.msp-container .right 		{ float: right; }
.msp-container .center		{ margin-left: auto; margin-right: auto; display: block; clear: both; }
.clear 		{ clear: both; display: block; font-size: 0; height: 0; line-height: 0; width:100%;}
/*----------------- */