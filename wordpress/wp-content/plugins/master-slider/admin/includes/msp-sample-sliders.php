<?php

function msp_get_slider_starter_sections () {

    $sections = array(
        array(
            'id'    => 'main_types',
            'title' => __( 'Slider Types', 'master-slider' ),
            'desc'  => ''
        ),
        array(
            'id'    => 'masterslider_samples_group1',
            'title' => __( 'Sample Sliders', 'master-slider' ),
            'desc'  => ''
        ),
        array(
            'id'      => 'masterslider_pro_custom_samples1',
            'title'   => '',
            'desc'    => '',
            'content' => '<h4>'. __( 'Starter Templates (Pro version)', MSWP_TEXT_DOMAIN ) .'</h4><p>'. __( 'To kick start with Master Slider ready to use sliders.', MSWP_TEXT_DOMAIN ) .'</p><a href="http://avt.li/msfunlock" class="msp-ac-btn msp-btn-blue" target="_blank">'. __( 'Upgrade to unlock', MSWP_TEXT_DOMAIN ) .'</a>'
        ),
        array(
            'id'    => 'masterslider_dynamic_group',
            'title' => __( 'Dynamic Sliders', MSWP_TEXT_DOMAIN ),
            'desc'  => ''
        )
    );

    return apply_filters( 'masterslider_starter_sections', $sections );
}


function msp_get_slider_starter_fields () {

  $starters = array();

  $starters['main_types'] = array(

    array(
      'label'   => __( 'Custom Slider', 'master-slider' ),
      'id'    => 'custom-slider',
      'slidertype'=> 'custom',
      'importdata'=> '',
      'image_dir' => '',
      'selected'  => 'true',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/slider-types/pt_custom.png'
    )

  );

  $starters['masterslider_samples_group1'] = array(

    array(
      'label'   => __( 'Slider with Horizontal Thumbnails', 'master-slider' ),
      'id'    => 'slider-with-thumbs-1',
      'slidertype'=> 'custom',
      'importdata'=> 'eyJzbGlkZXJzX2RhdGEiOnsiNTIiOnsidGl0bGUiOiJTbGlkZXIgd2l0aCBIb3Jpem9udGFsIFRodW1ibmFpbHMiLCJwYXJhbXMiOiJleUp0WlhSaElqcDdJbE5sZEhScGJtZHpJV2xrY3lJNklqRWlMQ0pUWlhSMGFXNW5jeUZ1WlhoMFNXUWlPaklzSWxOc2FXUmxJV2xrY3lJNklqRXNNaXd6TERRc05TdzJMRGNzT1N3eE1pd3hOQ0lzSWxOc2FXUmxJVzVsZUhSSlpDSTZNVFVzSWtOdmJuUnliMndoYVdSeklqb2lNU3d5TERNaUxDSkRiMjUwY205c0lXNWxlSFJKWkNJNk5IMHNJazFUVUdGdVpXd3VVMlYwZEdsdVozTWlPbnNpTVNJNkludGNJbWxrWENJNlhDSXhYQ0lzWENKemJtRndjR2x1WjF3aU9uUnlkV1VzWENKdVlXMWxYQ0k2WENKVGJHbGtaWElnZDJsMGFDQkliM0pwZW05dWRHRnNJRlJvZFcxaWJtRnBiSE5jSWl4Y0luZHBaSFJvWENJNk9EQXdMRndpYUdWcFoyaDBYQ0k2TkRnd0xGd2lkM0poY0hCbGNsZHBaSFJvWENJNk9EQXdMRndpZDNKaGNIQmxjbGRwWkhSb1ZXNXBkRndpT2x3aWNIaGNJaXhjSW1GMWRHOURjbTl3WENJNlptRnNjMlVzWENKMGVYQmxYQ0k2WENKamRYTjBiMjFjSWl4Y0luTnNhV1JsY2tsa1hDSTZYQ0kxTWx3aUxGd2liR0Y1YjNWMFhDSTZYQ0ppYjNobFpGd2lMRndpWVhWMGIwaGxhV2RvZEZ3aU9tWmhiSE5sTEZ3aWRISldhV1YzWENJNlhDSmlZWE5wWTF3aUxGd2ljM0JsWldSY0lqb3lNQ3hjSW5Od1lXTmxYQ0k2TUN4Y0luTjBZWEowWENJNk1TeGNJbWR5WVdKRGRYSnpiM0pjSWpwMGNuVmxMRndpYzNkcGNHVmNJanAwY25WbExGd2liVzkxYzJWY0lqcDBjblZsTEZ3aWQyaGxaV3hjSWpwbVlXeHpaU3hjSW1GMWRHOXdiR0Y1WENJNlptRnNjMlVzWENKc2IyOXdYQ0k2Wm1Gc2MyVXNYQ0p6YUhWbVpteGxYQ0k2Wm1Gc2MyVXNYQ0p3Y21Wc2IyRmtYQ0k2WENJdE1Wd2lMRndpYjNabGNsQmhkWE5sWENJNmRISjFaU3hjSW1WdVpGQmhkWE5sWENJNlptRnNjMlVzWENKb2FXUmxUR0Y1WlhKelhDSTZabUZzYzJVc1hDSmthWEpjSWpwY0ltaGNJaXhjSW5CaGNtRnNiR0Y0VFc5a1pWd2lPbHdpYzNkcGNHVmNJaXhjSW1ObGJuUmxja052Ym5SeWIyeHpYQ0k2ZEhKMVpTeGNJbWx1YzNSaGJuUlRhRzkzVEdGNVpYSnpYQ0k2Wm1Gc2MyVXNYQ0ppWjBOdmJHOXlYQ0k2WENJak1EQXdNREF3WENJc1hDSnphMmx1WENJNlhDSnRjeTF6YTJsdUxXUmxabUYxYkhSY0lpeGNJbTF6VkdWdGNHeGhkR1ZjSWpwY0ltTjFjM1J2YlZ3aUxGd2liWE5VWlcxd2JHRjBaVU5zWVhOelhDSTZYQ0pjSWl4Y0luVnpaV1JHYjI1MGMxd2lPbHdpWENKOUluMHNJazFUVUdGdVpXd3VVMnhwWkdVaU9uc2lNU0k2SW50Y0ltbGtYQ0k2WENJeFhDSXNYQ0owYVcxbGJHbHVaVjlvWENJNk1qQXdMRndpWW1kVWFIVnRZbHdpT2x3aUx6SXdNVEl2TVRBdk1USXhMVEUxTUhneE5UQXVhbkJuWENJc1hDSnZjbVJsY2x3aU9qQXNYQ0ppWjF3aU9sd2lMekl3TVRJdk1UQXZNVEl4TG1wd1oxd2lMRndpWkhWeVlYUnBiMjVjSWpvekxGd2labWxzYkUxdlpHVmNJanBjSW1acGJHeGNJaXhjSW1KbmRsOW1hV3hzYlc5a1pWd2lPbHdpWm1sc2JGd2lMRndpWW1kMlgyeHZiM0JjSWpwMGNuVmxMRndpWW1kMlgyMTFkR1ZjSWpwMGNuVmxMRndpWW1kMlgyRjFkRzl3WVhWelpWd2lPbVpoYkhObExGd2liR0Y1WlhKZmFXUnpYQ0k2VzExOUlpd2lNaUk2SW50Y0ltbGtYQ0k2TWl4Y0luUnBiV1ZzYVc1bFgyaGNJam95TURBc1hDSmlaMVJvZFcxaVhDSTZYQ0l2TWpBeE1pOHhNQzh5TFRFMU1IZ3hOVEF1YW5CblhDSXNYQ0p2Y21SbGNsd2lPakVzWENKaVoxd2lPbHdpTHpJd01USXZNVEF2TWk1cWNHZGNJaXhjSW1SMWNtRjBhVzl1WENJNk15eGNJbVpwYkd4TmIyUmxYQ0k2WENKbWFXeHNYQ0lzWENKaVozWmZabWxzYkcxdlpHVmNJanBjSW1acGJHeGNJaXhjSW1KbmRsOXNiMjl3WENJNmRISjFaU3hjSW1KbmRsOXRkWFJsWENJNmRISjFaU3hjSW1KbmRsOWhkWFJ2Y0dGMWMyVmNJanBtWVd4elpTeGNJbXhoZVdWeVgybGtjMXdpT2x0ZGZTSXNJak1pT2lKN1hDSnBaRndpT2pNc1hDSjBhVzFsYkdsdVpWOW9YQ0k2TWpBd0xGd2lZbWRVYUhWdFlsd2lPbHdpTHpJd01USXZNVEF2TVRFdE1UVXdlREUxTUM1cWNHZGNJaXhjSW05eVpHVnlYQ0k2TWl4Y0ltSm5YQ0k2WENJdk1qQXhNaTh4TUM4eE1TNXFjR2RjSWl4Y0ltUjFjbUYwYVc5dVhDSTZNeXhjSW1acGJHeE5iMlJsWENJNlhDSm1hV3hzWENJc1hDSmlaM1pmWm1sc2JHMXZaR1ZjSWpwY0ltWnBiR3hjSWl4Y0ltSm5kbDlzYjI5d1hDSTZkSEoxWlN4Y0ltSm5kbDl0ZFhSbFhDSTZkSEoxWlN4Y0ltSm5kbDloZFhSdmNHRjFjMlZjSWpwbVlXeHpaU3hjSW14aGVXVnlYMmxrYzF3aU9sdGRmU0lzSWpRaU9pSjdYQ0pwWkZ3aU9qUXNYQ0owYVcxbGJHbHVaVjlvWENJNk1qQXdMRndpWW1kVWFIVnRZbHdpT2x3aUx6SXdNVEl2TVRBdk15MHhOVEI0TVRVd0xtcHdaMXdpTEZ3aWIzSmtaWEpjSWpvekxGd2lZbWRjSWpwY0lpOHlNREV5THpFd0x6TXVhbkJuWENJc1hDSmtkWEpoZEdsdmJsd2lPak1zWENKbWFXeHNUVzlrWlZ3aU9sd2labWxzYkZ3aUxGd2lZbWQyWDJacGJHeHRiMlJsWENJNlhDSm1hV3hzWENJc1hDSmlaM1pmYkc5dmNGd2lPblJ5ZFdVc1hDSmlaM1pmYlhWMFpWd2lPblJ5ZFdVc1hDSmlaM1pmWVhWMGIzQmhkWE5sWENJNlptRnNjMlVzWENKc1lYbGxjbDlwWkhOY0lqcGJYWDBpTENJMUlqb2llMXdpYVdSY0lqbzFMRndpZEdsdFpXeHBibVZmYUZ3aU9qSXdNQ3hjSW1KblZHaDFiV0pjSWpwY0lpOHlNREV5THpFd0x6a3RNVFV3ZURFMU1DNXFjR2RjSWl4Y0ltOXlaR1Z5WENJNk5DeGNJbUpuWENJNlhDSXZNakF4TWk4eE1DODVMbXB3WjF3aUxGd2laSFZ5WVhScGIyNWNJam96TEZ3aVptbHNiRTF2WkdWY0lqcGNJbVpwYkd4Y0lpeGNJbUpuZGw5bWFXeHNiVzlrWlZ3aU9sd2labWxzYkZ3aUxGd2lZbWQyWDJ4dmIzQmNJanAwY25WbExGd2lZbWQyWDIxMWRHVmNJanAwY25WbExGd2lZbWQyWDJGMWRHOXdZWFZ6WlZ3aU9tWmhiSE5sTEZ3aWJHRjVaWEpmYVdSelhDSTZXMTE5SWl3aU5pSTZJbnRjSW1sa1hDSTZOaXhjSW5ScGJXVnNhVzVsWDJoY0lqb3lNREFzWENKaVoxUm9kVzFpWENJNlhDSXZNakF4TWk4eE1DODRMVEUxTUhneE5UQXVhbkJuWENJc1hDSnZjbVJsY2x3aU9qVXNYQ0ppWjF3aU9sd2lMekl3TVRJdk1UQXZPQzVxY0dkY0lpeGNJbVIxY21GMGFXOXVYQ0k2TXl4Y0ltWnBiR3hOYjJSbFhDSTZYQ0ptYVd4c1hDSXNYQ0ppWjNaZlptbHNiRzF2WkdWY0lqcGNJbVpwYkd4Y0lpeGNJbUpuZGw5c2IyOXdYQ0k2ZEhKMVpTeGNJbUpuZGw5dGRYUmxYQ0k2ZEhKMVpTeGNJbUpuZGw5aGRYUnZjR0YxYzJWY0lqcG1ZV3h6WlN4Y0lteGhlV1Z5WDJsa2Mxd2lPbHRkZlNJc0lqY2lPaUo3WENKcFpGd2lPamNzWENKMGFXMWxiR2x1WlY5b1hDSTZNakF3TEZ3aVltZFVhSFZ0WWx3aU9sd2lMekl3TVRJdk1UQXZOQzB4TlRCNE1UVXdMbXB3WjF3aUxGd2liM0prWlhKY0lqbzJMRndpWW1kY0lqcGNJaTh5TURFeUx6RXdMelF1YW5CblhDSXNYQ0prZFhKaGRHbHZibHdpT2pNc1hDSm1hV3hzVFc5a1pWd2lPbHdpWm1sc2JGd2lMRndpWW1kMlgyWnBiR3h0YjJSbFhDSTZYQ0ptYVd4c1hDSXNYQ0ppWjNaZmJHOXZjRndpT25SeWRXVXNYQ0ppWjNaZmJYVjBaVndpT25SeWRXVXNYQ0ppWjNaZllYVjBiM0JoZFhObFhDSTZabUZzYzJVc1hDSnNZWGxsY2w5cFpITmNJanBiWFgwaUxDSTVJam9pZTF3aWFXUmNJam81TEZ3aWRHbHRaV3hwYm1WZmFGd2lPakl3TUN4Y0ltSm5WR2gxYldKY0lqcGNJaTh5TURFeUx6RXdMekV6TFRFMU1IZ3hOVEF1YW5CblhDSXNYQ0p2Y21SbGNsd2lPamNzWENKaVoxd2lPbHdpTHpJd01USXZNVEF2TVRNdWFuQm5YQ0lzWENKa2RYSmhkR2x2Ymx3aU9qTXNYQ0ptYVd4c1RXOWtaVndpT2x3aVptbHNiRndpTEZ3aVltZDJYMlpwYkd4dGIyUmxYQ0k2WENKbWFXeHNYQ0lzWENKaVozWmZiRzl2Y0Z3aU9uUnlkV1VzWENKaVozWmZiWFYwWlZ3aU9uUnlkV1VzWENKaVozWmZZWFYwYjNCaGRYTmxYQ0k2Wm1Gc2MyVXNYQ0pzWVhsbGNsOXBaSE5jSWpwYlhYMGlMQ0l4TWlJNkludGNJbWxrWENJNk1USXNYQ0owYVcxbGJHbHVaVjlvWENJNk1qQXdMRndpWW1kVWFIVnRZbHdpT2x3aUx6SXdNVEl2TVRBdk1UWXRNVFV3ZURFMU1DNXFjR2RjSWl4Y0ltOXlaR1Z5WENJNk9DeGNJbUpuWENJNlhDSXZNakF4TWk4eE1DOHhOaTVxY0dkY0lpeGNJbVIxY21GMGFXOXVYQ0k2TXl4Y0ltWnBiR3hOYjJSbFhDSTZYQ0ptYVd4c1hDSXNYQ0ppWjNaZlptbHNiRzF2WkdWY0lqcGNJbVpwYkd4Y0lpeGNJbUpuZGw5c2IyOXdYQ0k2ZEhKMVpTeGNJbUpuZGw5dGRYUmxYQ0k2ZEhKMVpTeGNJbUpuZGw5aGRYUnZjR0YxYzJWY0lqcG1ZV3h6WlN4Y0lteGhlV1Z5WDJsa2Mxd2lPbHRkZlNJc0lqRTBJam9pZTF3aWFXUmNJam94TkN4Y0luUnBiV1ZzYVc1bFgyaGNJam95TURBc1hDSmlaMVJvZFcxaVhDSTZYQ0l2TWpBeE1pOHhNQzh4TlRFdE1UVXdlREUxTUM1cWNHZGNJaXhjSW05eVpHVnlYQ0k2T1N4Y0ltSm5YQ0k2WENJdk1qQXhNaTh4TUM4eE5URXVhbkJuWENJc1hDSmtkWEpoZEdsdmJsd2lPak1zWENKbWFXeHNUVzlrWlZ3aU9sd2labWxzYkZ3aUxGd2lZbWQyWDJacGJHeHRiMlJsWENJNlhDSm1hV3hzWENJc1hDSmlaM1pmYkc5dmNGd2lPblJ5ZFdVc1hDSmlaM1pmYlhWMFpWd2lPblJ5ZFdVc1hDSmlaM1pmWVhWMGIzQmhkWE5sWENJNlptRnNjMlVzWENKc1lYbGxjbDlwWkhOY0lqcGJYWDBpZlN3aVRWTlFZVzVsYkM1RGIyNTBjbTlzSWpwN0lqRWlPaUo3WENKcFpGd2lPbHdpTVZ3aUxGd2liR0ZpWld4Y0lqcGNJa0Z5Y205M2Mxd2lMRndpYm1GdFpWd2lPbHdpWVhKeWIzZHpYQ0lzWENKaGRYUnZTR2xrWlZ3aU9tWmhiSE5sTEZ3aWIzWmxjbFpwWkdWdlhDSTZkSEoxWlN4Y0ltbHVjMlYwWENJNmRISjFaWDBpTENJeUlqb2llMXdpYVdSY0lqb3lMRndpYkdGaVpXeGNJanBjSWtKMWJHeGxkSE5jSWl4Y0ltNWhiV1ZjSWpwY0ltSjFiR3hsZEhOY0lpeGNJbUYxZEc5SWFXUmxYQ0k2Wm1Gc2MyVXNYQ0p2ZG1WeVZtbGtaVzljSWpwMGNuVmxMRndpYldGeVoybHVYQ0k2TVRBc1hDSmthWEpjSWpwY0ltaGNJaXhjSW1Gc2FXZHVYQ0k2WENKaWIzUjBiMjFjSWl4Y0ltbHVjMlYwWENJNmRISjFaWDBpTENJeklqb2llMXdpYVdSY0lqb3pMRndpYkdGaVpXeGNJanBjSWxSb2RXMWliR2x6ZEM5VVlXSnpYQ0lzWENKdVlXMWxYQ0k2WENKMGFIVnRZbXhwYzNSY0lpeGNJbUYxZEc5SWFXUmxYQ0k2Wm1Gc2MyVXNYQ0p2ZG1WeVZtbGtaVzljSWpwMGNuVmxMRndpYldGeVoybHVYQ0k2TlN4Y0luTndZV05sWENJNk5TeGNJbmRwWkhSb1hDSTZNVFF3TEZ3aWFHVnBaMmgwWENJNk9EQXNYQ0poYkdsbmJsd2lPbHdpWW05MGRHOXRYQ0lzWENKcGJuTmxkRndpT21aaGJITmxMRndpWm1sc2JFMXZaR1ZjSWpwY0ltWnBiR3hjSW4waWZYMD0iLCJ0eXBlIjoiY3VzdG9tIiwic2xpZGVzX251bSI6IjEwIn19LCJvcmlnaW5fdXBsb2Fkc191cmwiOiJodHRwOlwvXC9kZW1vLmF2ZXJ0YS5uZXRcL3RoZW1lc1wvbG90dXNcL2R1bW15LWFnZW5jeVwvd3AtY29udGVudFwvdXBsb2FkcyJ9',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/slider-with-horizontal-thumbs.jpg'
    ),
    array(
      'label'   => __( 'Slider Without Thumbnails', 'master-slider' ),
      'id'    => 'slider-without-thumbs-1',
      'slidertype'=> 'custom',
      'importdata'=> '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',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/simple-slider.jpg'
    ),
    array(
      'label'   => __( 'Slider with Vertical Thumbnails', 'master-slider' ),
      'id'    => 'slider-with-thumbs-2',
      'slidertype'=> 'custom',
      'importdata'=> '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',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/slider-with-vertical-thumbs.jpg'
    ),
    array(
      'label'   => __( 'Fullwidth Slider', 'master-slider' ),
      'id'    => 'fullwith-without-thumbs-1',
      'slidertype'=> 'custom',
      'importdata'=> '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',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/fullwidth-slider.jpg'
    ),
    array(
      'label'   => __( 'Simple Autoplay Slider', 'master-slider' ),
      'id'    => 'slider-autoplay-1',
      'slidertype'=> 'custom',
      'importdata'=> '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',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/autoplay-slider.jpg'
    ),
    array(
      'label'   => __( 'Fullwidth Slider with Thumbnails', 'master-slider' ),
      'id'    => 'fullwith-with-thumbs-1',
      'slidertype'=> 'custom',
      'importdata'=> '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',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/fullwidth-slider-with-thumbs.jpg'
    ),
    array(
      'label'   => __( 'Slider with Slide Info', 'master-slider' ),
      'id'    => 'slider-with-slideinfo-1',
      'slidertype'=> 'custom',
      'importdata'=> '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',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/slider-with-slide-info.jpg'
    ),
    array(
      'label'   => __( 'Slider with Slide Info V2', 'master-slider' ),
      'id'    => 'slider-with-slideinfo-2',
      'slidertype'=> 'custom',
      'importdata'=> 'eyJzbGlkZXJzX2RhdGEiOnsiNTkiOnsidGl0bGUiOiJTaW1wbGUgU2xpZGVyIHdpdGggU2xpZGUgSW5mbyBWMiIsInBhcmFtcyI6ImV5SnRaWFJoSWpwN0lsTmxkSFJwYm1keklXbGtjeUk2SWpFaUxDSlRaWFIwYVc1bmN5RnVaWGgwU1dRaU9qSXNJbE5zYVdSbElXbGtjeUk2SWpFc01pd3pMRFFzTlN3MkxEY3NPU3d4TWl3eE5DSXNJbE5zYVdSbElXNWxlSFJKWkNJNk1UWXNJa052Ym5SeWIyd2hhV1J6SWpvaU1TdzBMRFVzTmlJc0lrTnZiblJ5YjJ3aGJtVjRkRWxrSWpvM2ZTd2lUVk5RWVc1bGJDNVRaWFIwYVc1bmN5STZleUl4SWpvaWUxd2lhV1JjSWpwY0lqRmNJaXhjSW5OdVlYQndhVzVuWENJNmRISjFaU3hjSW01aGJXVmNJanBjSWxOcGJYQnNaU0JUYkdsa1pYSWdkMmwwYUNCVGJHbGtaU0JKYm1adklGWXlYQ0lzWENKM2FXUjBhRndpT2pnd01DeGNJbWhsYVdkb2RGd2lPalE0TUN4Y0luZHlZWEJ3WlhKWGFXUjBhRndpT2pFd05qQXNYQ0ozY21Gd2NHVnlWMmxrZEdoVmJtbDBYQ0k2WENKd2VGd2lMRndpWVhWMGIwTnliM0JjSWpwbVlXeHpaU3hjSW5SNWNHVmNJanBjSW1OMWMzUnZiVndpTEZ3aWMyeHBaR1Z5U1dSY0lqcGNJalU1WENJc1hDSnNZWGx2ZFhSY0lqcGNJbUp2ZUdWa1hDSXNYQ0poZFhSdlNHVnBaMmgwWENJNlptRnNjMlVzWENKMGNsWnBaWGRjSWpwY0ltSmhjMmxqWENJc1hDSnpjR1ZsWkZ3aU9qSXdMRndpYzNCaFkyVmNJam93TEZ3aWMzUmhjblJjSWpveExGd2laM0poWWtOMWNuTnZjbHdpT25SeWRXVXNYQ0p6ZDJsd1pWd2lPblJ5ZFdVc1hDSnRiM1Z6WlZ3aU9uUnlkV1VzWENKM2FHVmxiRndpT21aaGJITmxMRndpWVhWMGIzQnNZWGxjSWpwbVlXeHpaU3hjSW14dmIzQmNJanBtWVd4elpTeGNJbk5vZFdabWJHVmNJanBtWVd4elpTeGNJbkJ5Wld4dllXUmNJanBjSWkweFhDSXNYQ0p2ZG1WeVVHRjFjMlZjSWpwMGNuVmxMRndpWlc1a1VHRjFjMlZjSWpwbVlXeHpaU3hjSW1ocFpHVk1ZWGxsY25OY0lqcG1ZV3h6WlN4Y0ltUnBjbHdpT2x3aWFGd2lMRndpY0dGeVlXeHNZWGhOYjJSbFhDSTZYQ0p6ZDJsd1pWd2lMRndpWTJWdWRHVnlRMjl1ZEhKdmJITmNJanAwY25WbExGd2lhVzV6ZEdGdWRGTm9iM2RNWVhsbGNuTmNJanBtWVd4elpTeGNJbUpuUTI5c2IzSmNJanBjSWlNd01EQXdNREJjSWl4Y0luTnJhVzVjSWpwY0ltMXpMWE5yYVc0dGJHbG5hSFF0TWx3aUxGd2liWE5VWlcxd2JHRjBaVndpT2x3aVkzVnpkRzl0WENJc1hDSnRjMVJsYlhCc1lYUmxRMnhoYzNOY0lqcGNJbHdpTEZ3aWRYTmxaRVp2Ym5SelhDSTZYQ0pjSW4waWZTd2lUVk5RWVc1bGJDNVRiR2xrWlNJNmV5SXhJam9pZTF3aWFXUmNJanBjSWpGY0lpeGNJblJwYldWc2FXNWxYMmhjSWpveU1EQXNYQ0ppWjFSb2RXMWlYQ0k2WENJdk1qQXhNaTh4TUM4eE1qRXRNVFV3ZURFMU1DNXFjR2RjSWl4Y0ltOXlaR1Z5WENJNk5DeGNJbUpuWENJNlhDSXZNakF4TWk4eE1DOHhNakV1YW5CblhDSXNYQ0prZFhKaGRHbHZibHdpT2pNc1hDSm1hV3hzVFc5a1pWd2lPbHdpWm1sc2JGd2lMRndpYVc1bWIxd2lPbHdpUEdneUlITjBlV3hsUFZ4Y1hDSjBaWGgwTFdGc2FXZHVPaUJzWldaME8xeGNYQ0krVTJGdGNHeGxJRlJwZEd4bHdxQTFQQzlvTWo0OGNDQnpkSGxzWlQxY1hGd2lkR1Y0ZEMxaGJHbG5iam9nYkdWbWREdGNYRndpUGt4dmNtVnRJR2x3YzNWdElHUnZiRzl5SUhOcGRDQmhiV1YwTENCamIyNXpaV04wWlhSMWNpQmhaR2x3YVhOcFkybHVaeUJsYkdsMExDQnpaV1FnWkc4Z1pXbDFjMjF2WkNCMFpXMXdiM0lnYVc1amFXUnBaSFZ1ZENCMWRDQnNZV0p2Y21VZ1pYUWdaRzlzYjNKbElHMWhaMjVoSUdGc2FYRjFZUzRnVlhRZ1pXNXBiU0JoWkNCdGFXNXBiU0IyWlc1cFlXMHNJSEYxYVhNZ2JtOXpkSEoxWkNCbGVHVnlZMmwwWVhScGIyNGdkV3hzWVcxamJ5QnNZV0p2Y21seklHNXBjMmtnZFhRZ1lXeHBjWFZwY0NCbGVDQmxZU0JqYjIxdGIyUnZJR052Ym5ObGNYVmhkQzRnUkhWcGN5QmhkWFJsSUdseWRYSmxJR1J2Ykc5eUlHbHVJSEpsY0hKbGFHVnVaR1Z5YVhRZ2FXNGdkbTlzZFhCMFlYUmxJSFpsYkdsMElHVnpjMlVnWTJsc2JIVnRJR1J2Ykc5eVpTQmxkU0JtZFdkcFlYUWdiblZzYkdFZ2NHRnlhV0YwZFhJdUlFVjRZMlZ3ZEdWMWNpQnphVzUwSUc5alkyRmxZMkYwSUdOMWNHbGtZWFJoZENCdWIyNGdjSEp2YVdSbGJuUXNJSE4xYm5RZ2FXNGdZM1ZzY0dFZ2NYVnBJRzltWm1samFXRWdaR1Z6WlhKMWJuUWdiVzlzYkdsMElHRnVhVzBnYVdRZ1pYTjBJR3hoWW05eWRXMHVQQzl3UGx3aUxGd2lZbWQyWDJacGJHeHRiMlJsWENJNlhDSm1hV3hzWENJc1hDSmlaM1pmYkc5dmNGd2lPblJ5ZFdVc1hDSmlaM1pmYlhWMFpWd2lPblJ5ZFdVc1hDSmlaM1pmWVhWMGIzQmhkWE5sWENJNlptRnNjMlVzWENKc1lYbGxjbDlwWkhOY0lqcGJYWDBpTENJeUlqb2llMXdpYVdSY0lqb3lMRndpZEdsdFpXeHBibVZmYUZ3aU9qSXdNQ3hjSW1KblZHaDFiV0pjSWpwY0lpOHlNREV5THpFd0x6SXRNVFV3ZURFMU1DNXFjR2RjSWl4Y0ltOXlaR1Z5WENJNk1peGNJbUpuWENJNlhDSXZNakF4TWk4eE1DOHlMbXB3WjF3aUxGd2laSFZ5WVhScGIyNWNJam96TEZ3aVptbHNiRTF2WkdWY0lqcGNJbVpwYkd4Y0lpeGNJbWx1Wm05Y0lqcGNJanhvTWlCemRIbHNaVDFjWEZ3aWRHVjRkQzFoYkdsbmJqb2diR1ZtZER0Y1hGd2lQbE5oYlhCc1pTQlVhWFJzWmNLZ016d3ZhREkrUEhBZ2MzUjViR1U5WEZ4Y0luUmxlSFF0WVd4cFoyNDZJR3hsWm5RN1hGeGNJajVNYjNKbGJTQnBjSE4xYlNCa2IyeHZjaUJ6YVhRZ1lXMWxkQ3dnWTI5dWMyVmpkR1YwZFhJZ1lXUnBjR2x6YVdOcGJtY2daV3hwZEN3Z2MyVmtJR1J2SUdWcGRYTnRiMlFnZEdWdGNHOXlJR2x1WTJsa2FXUjFiblFnZFhRZ2JHRmliM0psSUdWMElHUnZiRzl5WlNCdFlXZHVZU0JoYkdseGRXRXVJRlYwSUdWdWFXMGdZV1FnYldsdWFXMGdkbVZ1YVdGdExDQnhkV2x6SUc1dmMzUnlkV1FnWlhobGNtTnBkR0YwYVc5dUlIVnNiR0Z0WTI4Z2JHRmliM0pwY3lCdWFYTnBJSFYwSUdGc2FYRjFhWEFnWlhnZ1pXRWdZMjl0Ylc5a2J5QmpiMjV6WlhGMVlYUXVJRVIxYVhNZ1lYVjBaU0JwY25WeVpTQmtiMnh2Y2lCcGJpQnlaWEJ5WldobGJtUmxjbWwwSUdsdUlIWnZiSFZ3ZEdGMFpTQjJaV3hwZENCbGMzTmxJR05wYkd4MWJTQmtiMnh2Y21VZ1pYVWdablZuYVdGMElHNTFiR3hoSUhCaGNtbGhkSFZ5TGlCRmVHTmxjSFJsZFhJZ2MybHVkQ0J2WTJOaFpXTmhkQ0JqZFhCcFpHRjBZWFFnYm05dUlIQnliMmxrWlc1MExDQnpkVzUwSUdsdUlHTjFiSEJoSUhGMWFTQnZabVpwWTJsaElHUmxjMlZ5ZFc1MElHMXZiR3hwZENCaGJtbHRJR2xrSUdWemRDQnNZV0p2Y25WdExqd3ZjRDVjSWl4Y0ltSm5kbDltYVd4c2JXOWtaVndpT2x3aVptbHNiRndpTEZ3aVltZDJYMnh2YjNCY0lqcDBjblZsTEZ3aVltZDJYMjExZEdWY0lqcDBjblZsTEZ3aVltZDJYMkYxZEc5d1lYVnpaVndpT21aaGJITmxMRndpYkdGNVpYSmZhV1J6WENJNlcxMTlJaXdpTXlJNkludGNJbWxrWENJNk15eGNJblJwYldWc2FXNWxYMmhjSWpveU1EQXNYQ0ppWjFSb2RXMWlYQ0k2WENJdk1qQXhNaTh4TUM4eE1TMHhOVEI0TVRVd0xtcHdaMXdpTEZ3aWIzSmtaWEpjSWpvekxGd2lZbWRjSWpwY0lpOHlNREV5THpFd0x6RXhMbXB3WjF3aUxGd2laSFZ5WVhScGIyNWNJam96TEZ3aVptbHNiRTF2WkdWY0lqcGNJbVpwYkd4Y0lpeGNJbWx1Wm05Y0lqcGNJanhvTWlCemRIbHNaVDFjWEZ3aWRHVjRkQzFoYkdsbmJqb2diR1ZtZER0Y1hGd2lQbE5oYlhCc1pTQlVhWFJzWmNLZ05Ed3ZhREkrUEhBZ2MzUjViR1U5WEZ4Y0luUmxlSFF0WVd4cFoyNDZJR3hsWm5RN1hGeGNJajVNYjNKbGJTQnBjSE4xYlNCa2IyeHZjaUJ6YVhRZ1lXMWxkQ3dnWTI5dWMyVmpkR1YwZFhJZ1lXUnBjR2x6YVdOcGJtY2daV3hwZEN3Z2MyVmtJR1J2SUdWcGRYTnRiMlFnZEdWdGNHOXlJR2x1WTJsa2FXUjFiblFnZFhRZ2JHRmliM0psSUdWMElHUnZiRzl5WlNCdFlXZHVZU0JoYkdseGRXRXVJRlYwSUdWdWFXMGdZV1FnYldsdWFXMGdkbVZ1YVdGdExDQnhkV2x6SUc1dmMzUnlkV1FnWlhobGNtTnBkR0YwYVc5dUlIVnNiR0Z0WTI4Z2JHRmliM0pwY3lCdWFYTnBJSFYwSUdGc2FYRjFhWEFnWlhnZ1pXRWdZMjl0Ylc5a2J5QmpiMjV6WlhGMVlYUXVJRVIxYVhNZ1lYVjBaU0JwY25WeVpTQmtiMnh2Y2lCcGJpQnlaWEJ5WldobGJtUmxjbWwwSUdsdUlIWnZiSFZ3ZEdGMFpTQjJaV3hwZENCbGMzTmxJR05wYkd4MWJTQmtiMnh2Y21VZ1pYVWdablZuYVdGMElHNTFiR3hoSUhCaGNtbGhkSFZ5TGlCRmVHTmxjSFJsZFhJZ2MybHVkQ0J2WTJOaFpXTmhkQ0JqZFhCcFpHRjBZWFFnYm05dUlIQnliMmxrWlc1MExDQnpkVzUwSUdsdUlHTjFiSEJoSUhGMWFTQnZabVpwWTJsaElHUmxjMlZ5ZFc1MElHMXZiR3hwZENCaGJtbHRJR2xrSUdWemRDQnNZV0p2Y25WdExqd3ZjRDVjSWl4Y0ltSm5kbDltYVd4c2JXOWtaVndpT2x3aVptbHNiRndpTEZ3aVltZDJYMnh2YjNCY0lqcDBjblZsTEZ3aVltZDJYMjExZEdWY0lqcDBjblZsTEZ3aVltZDJYMkYxZEc5d1lYVnpaVndpT21aaGJITmxMRndpYkdGNVpYSmZhV1J6WENJNlcxMTlJaXdpTkNJNkludGNJbWxrWENJNk5DeGNJblJwYldWc2FXNWxYMmhjSWpveU1EQXNYQ0ppWjFSb2RXMWlYQ0k2WENJdk1qQXhNaTh4TUM4ekxURTFNSGd4TlRBdWFuQm5YQ0lzWENKdmNtUmxjbHdpT2pVc1hDSmlaMXdpT2x3aUx6SXdNVEl2TVRBdk15NXFjR2RjSWl4Y0ltUjFjbUYwYVc5dVhDSTZNeXhjSW1acGJHeE5iMlJsWENJNlhDSm1hV3hzWENJc1hDSnBibVp2WENJNlhDSThhRElnYzNSNWJHVTlYRnhjSW5SbGVIUXRZV3hwWjI0NklHeGxablE3WEZ4Y0lqNVRZVzF3YkdVZ1ZHbDBiR1hDb0RZOEwyZ3lQanh3SUhOMGVXeGxQVnhjWENKMFpYaDBMV0ZzYVdkdU9pQnNaV1owTzF4Y1hDSStURzl5WlcwZ2FYQnpkVzBnWkc5c2IzSWdjMmwwSUdGdFpYUXNJR052Ym5ObFkzUmxkSFZ5SUdGa2FYQnBjMmxqYVc1bklHVnNhWFFzSUhObFpDQmtieUJsYVhWemJXOWtJSFJsYlhCdmNpQnBibU5wWkdsa2RXNTBJSFYwSUd4aFltOXlaU0JsZENCa2IyeHZjbVVnYldGbmJtRWdZV3hwY1hWaExpQlZkQ0JsYm1sdElHRmtJRzFwYm1sdElIWmxibWxoYlN3Z2NYVnBjeUJ1YjNOMGNuVmtJR1Y0WlhKamFYUmhkR2x2YmlCMWJHeGhiV052SUd4aFltOXlhWE1nYm1semFTQjFkQ0JoYkdseGRXbHdJR1Y0SUdWaElHTnZiVzF2Wkc4Z1kyOXVjMlZ4ZFdGMExpQkVkV2x6SUdGMWRHVWdhWEoxY21VZ1pHOXNiM0lnYVc0Z2NtVndjbVZvWlc1a1pYSnBkQ0JwYmlCMmIyeDFjSFJoZEdVZ2RtVnNhWFFnWlhOelpTQmphV3hzZFcwZ1pHOXNiM0psSUdWMUlHWjFaMmxoZENCdWRXeHNZU0J3WVhKcFlYUjFjaTRnUlhoalpYQjBaWFZ5SUhOcGJuUWdiMk5qWVdWallYUWdZM1Z3YVdSaGRHRjBJRzV2YmlCd2NtOXBaR1Z1ZEN3Z2MzVnVkQ0JwYmlCamRXeHdZU0J4ZFdrZ2IyWm1hV05wWVNCa1pYTmxjblZ1ZENCdGIyeHNhWFFnWVc1cGJTQnBaQ0JsYzNRZ2JHRmliM0oxYlM0OEwzQStYQ0lzWENKaVozWmZabWxzYkcxdlpHVmNJanBjSW1acGJHeGNJaXhjSW1KbmRsOXNiMjl3WENJNmRISjFaU3hjSW1KbmRsOXRkWFJsWENJNmRISjFaU3hjSW1KbmRsOWhkWFJ2Y0dGMWMyVmNJanBtWVd4elpTeGNJbXhoZVdWeVgybGtjMXdpT2x0ZGZTSXNJalVpT2lKN1hDSnBaRndpT2pVc1hDSjBhVzFsYkdsdVpWOW9YQ0k2TWpBd0xGd2lZbWRVYUhWdFlsd2lPbHdpTHpJd01USXZNVEF2T1MweE5UQjRNVFV3TG1wd1oxd2lMRndpYjNKa1pYSmNJam8yTEZ3aVltZGNJanBjSWk4eU1ERXlMekV3THprdWFuQm5YQ0lzWENKa2RYSmhkR2x2Ymx3aU9qTXNYQ0ptYVd4c1RXOWtaVndpT2x3aVptbHNiRndpTEZ3aWFXNW1iMXdpT2x3aVBHZ3lJSE4wZVd4bFBWeGNYQ0owWlhoMExXRnNhV2R1T2lCc1pXWjBPMXhjWENJK1UyRnRjR3hsSUZScGRHeGx3cUEzUEM5b01qNDhjQ0J6ZEhsc1pUMWNYRndpZEdWNGRDMWhiR2xuYmpvZ2JHVm1kRHRjWEZ3aVBreHZjbVZ0SUdsd2MzVnRJR1J2Ykc5eUlITnBkQ0JoYldWMExDQmpiMjV6WldOMFpYUjFjaUJoWkdsd2FYTnBZMmx1WnlCbGJHbDBMQ0J6WldRZ1pHOGdaV2wxYzIxdlpDQjBaVzF3YjNJZ2FXNWphV1JwWkhWdWRDQjFkQ0JzWVdKdmNtVWdaWFFnWkc5c2IzSmxJRzFoWjI1aElHRnNhWEYxWVM0Z1ZYUWdaVzVwYlNCaFpDQnRhVzVwYlNCMlpXNXBZVzBzSUhGMWFYTWdibTl6ZEhKMVpDQmxlR1Z5WTJsMFlYUnBiMjRnZFd4c1lXMWpieUJzWVdKdmNtbHpJRzVwYzJrZ2RYUWdZV3hwY1hWcGNDQmxlQ0JsWVNCamIyMXRiMlJ2SUdOdmJuTmxjWFZoZEM0Z1JIVnBjeUJoZFhSbElHbHlkWEpsSUdSdmJHOXlJR2x1SUhKbGNISmxhR1Z1WkdWeWFYUWdhVzRnZG05c2RYQjBZWFJsSUhabGJHbDBJR1Z6YzJVZ1kybHNiSFZ0SUdSdmJHOXlaU0JsZFNCbWRXZHBZWFFnYm5Wc2JHRWdjR0Z5YVdGMGRYSXVJRVY0WTJWd2RHVjFjaUJ6YVc1MElHOWpZMkZsWTJGMElHTjFjR2xrWVhSaGRDQnViMjRnY0hKdmFXUmxiblFzSUhOMWJuUWdhVzRnWTNWc2NHRWdjWFZwSUc5bVptbGphV0VnWkdWelpYSjFiblFnYlc5c2JHbDBJR0Z1YVcwZ2FXUWdaWE4wSUd4aFltOXlkVzB1UEM5d1Bsd2lMRndpWW1kMlgyWnBiR3h0YjJSbFhDSTZYQ0ptYVd4c1hDSXNYQ0ppWjNaZmJHOXZjRndpT25SeWRXVXNYQ0ppWjNaZmJYVjBaVndpT25SeWRXVXNYQ0ppWjNaZllYVjBiM0JoZFhObFhDSTZabUZzYzJVc1hDSnNZWGxsY2w5cFpITmNJanBiWFgwaUxDSTJJam9pZTF3aWFXUmNJam8yTEZ3aWRHbHRaV3hwYm1WZmFGd2lPakl3TUN4Y0ltSm5WR2gxYldKY0lqcGNJaTh5TURFeUx6RXdMemd0TVRVd2VERTFNQzVxY0dkY0lpeGNJbTl5WkdWeVhDSTZNU3hjSW1KblhDSTZYQ0l2TWpBeE1pOHhNQzg0TG1wd1oxd2lMRndpWkhWeVlYUnBiMjVjSWpvekxGd2labWxzYkUxdlpHVmNJanBjSW1acGJHeGNJaXhjSW1sdVptOWNJanBjSWp4b01pQnpkSGxzWlQxY1hGd2lkR1Y0ZEMxaGJHbG5iam9nYkdWbWREdGNYRndpUGxOaGJYQnNaU0JVYVhSc1pjS2dNand2YURJK1BIQWdjM1I1YkdVOVhGeGNJblJsZUhRdFlXeHBaMjQ2SUd4bFpuUTdYRnhjSWo1TWIzSmxiU0JwY0hOMWJTQmtiMnh2Y2lCemFYUWdZVzFsZEN3Z1kyOXVjMlZqZEdWMGRYSWdZV1JwY0dsemFXTnBibWNnWld4cGRDd2djMlZrSUdSdklHVnBkWE50YjJRZ2RHVnRjRzl5SUdsdVkybGthV1IxYm5RZ2RYUWdiR0ZpYjNKbElHVjBJR1J2Ykc5eVpTQnRZV2R1WVNCaGJHbHhkV0V1SUZWMElHVnVhVzBnWVdRZ2JXbHVhVzBnZG1WdWFXRnRMQ0J4ZFdseklHNXZjM1J5ZFdRZ1pYaGxjbU5wZEdGMGFXOXVJSFZzYkdGdFkyOGdiR0ZpYjNKcGN5QnVhWE5wSUhWMElHRnNhWEYxYVhBZ1pYZ2daV0VnWTI5dGJXOWtieUJqYjI1elpYRjFZWFF1SUVSMWFYTWdZWFYwWlNCcGNuVnlaU0JrYjJ4dmNpQnBiaUJ5WlhCeVpXaGxibVJsY21sMElHbHVJSFp2YkhWd2RHRjBaU0IyWld4cGRDQmxjM05sSUdOcGJHeDFiU0JrYjJ4dmNtVWdaWFVnWm5WbmFXRjBJRzUxYkd4aElIQmhjbWxoZEhWeUxpQkZlR05sY0hSbGRYSWdjMmx1ZENCdlkyTmhaV05oZENCamRYQnBaR0YwWVhRZ2JtOXVJSEJ5YjJsa1pXNTBMQ0J6ZFc1MElHbHVJR04xYkhCaElIRjFhU0J2Wm1acFkybGhJR1JsYzJWeWRXNTBJRzF2Ykd4cGRDQmhibWx0SUdsa0lHVnpkQ0JzWVdKdmNuVnRMand2Y0Q1Y0lpeGNJbUpuZGw5bWFXeHNiVzlrWlZ3aU9sd2labWxzYkZ3aUxGd2lZbWQyWDJ4dmIzQmNJanAwY25WbExGd2lZbWQyWDIxMWRHVmNJanAwY25WbExGd2lZbWQyWDJGMWRHOXdZWFZ6WlZ3aU9tWmhiSE5sTEZ3aWJHRjVaWEpmYVdSelhDSTZXMTE5SWl3aU55STZJbnRjSW1sa1hDSTZOeXhjSW5ScGJXVnNhVzVsWDJoY0lqb3lNREFzWENKaVoxUm9kVzFpWENJNlhDSXZNakF4TWk4eE1DODBMVEUxTUhneE5UQXVhbkJuWENJc1hDSnZjbVJsY2x3aU9qY3NYQ0ppWjF3aU9sd2lMekl3TVRJdk1UQXZOQzVxY0dkY0lpeGNJbVIxY21GMGFXOXVYQ0k2TXl4Y0ltWnBiR3hOYjJSbFhDSTZYQ0ptYVd4c1hDSXNYQ0pwYm1adlhDSTZYQ0k4YURJZ2MzUjViR1U5WEZ4Y0luUmxlSFF0WVd4cFoyNDZJR3hsWm5RN1hGeGNJajVUWVcxd2JHVWdWR2wwYkdYQ29EZzhMMmd5UGp4d0lITjBlV3hsUFZ4Y1hDSjBaWGgwTFdGc2FXZHVPaUJzWldaME8xeGNYQ0krVEc5eVpXMGdhWEJ6ZFcwZ1pHOXNiM0lnYzJsMElHRnRaWFFzSUdOdmJuTmxZM1JsZEhWeUlHRmthWEJwYzJsamFXNW5JR1ZzYVhRc0lITmxaQ0JrYnlCbGFYVnpiVzlrSUhSbGJYQnZjaUJwYm1OcFpHbGtkVzUwSUhWMElHeGhZbTl5WlNCbGRDQmtiMnh2Y21VZ2JXRm5ibUVnWVd4cGNYVmhMaUJWZENCbGJtbHRJR0ZrSUcxcGJtbHRJSFpsYm1saGJTd2djWFZwY3lCdWIzTjBjblZrSUdWNFpYSmphWFJoZEdsdmJpQjFiR3hoYldOdklHeGhZbTl5YVhNZ2JtbHphU0IxZENCaGJHbHhkV2x3SUdWNElHVmhJR052YlcxdlpHOGdZMjl1YzJWeGRXRjBMaUJFZFdseklHRjFkR1VnYVhKMWNtVWdaRzlzYjNJZ2FXNGdjbVZ3Y21Wb1pXNWtaWEpwZENCcGJpQjJiMngxY0hSaGRHVWdkbVZzYVhRZ1pYTnpaU0JqYVd4c2RXMGdaRzlzYjNKbElHVjFJR1oxWjJsaGRDQnVkV3hzWVNCd1lYSnBZWFIxY2k0Z1JYaGpaWEIwWlhWeUlITnBiblFnYjJOallXVmpZWFFnWTNWd2FXUmhkR0YwSUc1dmJpQndjbTlwWkdWdWRDd2djM1Z1ZENCcGJpQmpkV3h3WVNCeGRXa2diMlptYVdOcFlTQmtaWE5sY25WdWRDQnRiMnhzYVhRZ1lXNXBiU0JwWkNCbGMzUWdiR0ZpYjNKMWJTNDhMM0ErWENJc1hDSmlaM1pmWm1sc2JHMXZaR1ZjSWpwY0ltWnBiR3hjSWl4Y0ltSm5kbDlzYjI5d1hDSTZkSEoxWlN4Y0ltSm5kbDl0ZFhSbFhDSTZkSEoxWlN4Y0ltSm5kbDloZFhSdmNHRjFjMlZjSWpwbVlXeHpaU3hjSW14aGVXVnlYMmxrYzF3aU9sdGRmU0lzSWpraU9pSjdYQ0pwWkZ3aU9qa3NYQ0owYVcxbGJHbHVaVjlvWENJNk1qQXdMRndpWW1kVWFIVnRZbHdpT2x3aUx6SXdNVEl2TVRBdk1UTXRNVFV3ZURFMU1DNXFjR2RjSWl4Y0ltOXlaR1Z5WENJNk1DeGNJbUpuWENJNlhDSXZNakF4TWk4eE1DOHhNeTVxY0dkY0lpeGNJbVIxY21GMGFXOXVYQ0k2TXl4Y0ltWnBiR3hOYjJSbFhDSTZYQ0ptYVd4c1hDSXNYQ0pwYm1adlhDSTZYQ0k4YURJZ2MzUjViR1U5WEZ4Y0luUmxlSFF0WVd4cFoyNDZJR3hsWm5RN1hGeGNJajVUWVcxd2JHVWdWR2wwYkdVZ01Ud3ZhREkrUEhBZ2MzUjViR1U5WEZ4Y0luUmxlSFF0WVd4cFoyNDZJR3hsWm5RN1hGeGNJajVNYjNKbGJTQnBjSE4xYlNCa2IyeHZjaUJ6YVhRZ1lXMWxkQ3dnWTI5dWMyVmpkR1YwZFhJZ1lXUnBjR2x6YVdOcGJtY2daV3hwZEN3Z2MyVmtJR1J2SUdWcGRYTnRiMlFnZEdWdGNHOXlJR2x1WTJsa2FXUjFiblFnZFhRZ2JHRmliM0psSUdWMElHUnZiRzl5WlNCdFlXZHVZU0JoYkdseGRXRXVJRlYwSUdWdWFXMGdZV1FnYldsdWFXMGdkbVZ1YVdGdExDQnhkV2x6SUc1dmMzUnlkV1FnWlhobGNtTnBkR0YwYVc5dUlIVnNiR0Z0WTI4Z2JHRmliM0pwY3lCdWFYTnBJSFYwSUdGc2FYRjFhWEFnWlhnZ1pXRWdZMjl0Ylc5a2J5QmpiMjV6WlhGMVlYUXVJRVIxYVhNZ1lYVjBaU0JwY25WeVpTQmtiMnh2Y2lCcGJpQnlaWEJ5WldobGJtUmxjbWwwSUdsdUlIWnZiSFZ3ZEdGMFpTQjJaV3hwZENCbGMzTmxJR05wYkd4MWJTQmtiMnh2Y21VZ1pYVWdablZuYVdGMElHNTFiR3hoSUhCaGNtbGhkSFZ5TGlCRmVHTmxjSFJsZFhJZ2MybHVkQ0J2WTJOaFpXTmhkQ0JqZFhCcFpHRjBZWFFnYm05dUlIQnliMmxrWlc1MExDQnpkVzUwSUdsdUlHTjFiSEJoSUhGMWFTQnZabVpwWTJsaElHUmxjMlZ5ZFc1MElHMXZiR3hwZENCaGJtbHRJR2xrSUdWemRDQnNZV0p2Y25WdExqd3ZjRDVjSWl4Y0ltSm5kbDltYVd4c2JXOWtaVndpT2x3aVptbHNiRndpTEZ3aVltZDJYMnh2YjNCY0lqcDBjblZsTEZ3aVltZDJYMjExZEdWY0lqcDBjblZsTEZ3aVltZDJYMkYxZEc5d1lYVnpaVndpT21aaGJITmxMRndpYkdGNVpYSmZhV1J6WENJNlcxMTlJaXdpTVRJaU9pSjdYQ0pwWkZ3aU9qRXlMRndpZEdsdFpXeHBibVZmYUZ3aU9qSXdNQ3hjSW1KblZHaDFiV0pjSWpwY0lpOHlNREV5THpFd0x6RTJMVEUxTUhneE5UQXVhbkJuWENJc1hDSnZjbVJsY2x3aU9qZ3NYQ0ppWjF3aU9sd2lMekl3TVRJdk1UQXZNVFl1YW5CblhDSXNYQ0prZFhKaGRHbHZibHdpT2pNc1hDSm1hV3hzVFc5a1pWd2lPbHdpWm1sc2JGd2lMRndpYVc1bWIxd2lPbHdpUEdneUlITjBlV3hsUFZ4Y1hDSjBaWGgwTFdGc2FXZHVPaUJzWldaME8xeGNYQ0krVTJGdGNHeGxJRlJwZEd4bHdxQTVQQzlvTWo0OGNDQnpkSGxzWlQxY1hGd2lkR1Y0ZEMxaGJHbG5iam9nYkdWbWREdGNYRndpUGt4dmNtVnRJR2x3YzNWdElHUnZiRzl5SUhOcGRDQmhiV1YwTENCamIyNXpaV04wWlhSMWNpQmhaR2x3YVhOcFkybHVaeUJsYkdsMExDQnpaV1FnWkc4Z1pXbDFjMjF2WkNCMFpXMXdiM0lnYVc1amFXUnBaSFZ1ZENCMWRDQnNZV0p2Y21VZ1pYUWdaRzlzYjNKbElHMWhaMjVoSUdGc2FYRjFZUzRnVlhRZ1pXNXBiU0JoWkNCdGFXNXBiU0IyWlc1cFlXMHNJSEYxYVhNZ2JtOXpkSEoxWkNCbGVHVnlZMmwwWVhScGIyNGdkV3hzWVcxamJ5QnNZV0p2Y21seklHNXBjMmtnZFhRZ1lXeHBjWFZwY0NCbGVDQmxZU0JqYjIxdGIyUnZJR052Ym5ObGNYVmhkQzRnUkhWcGN5QmhkWFJsSUdseWRYSmxJR1J2Ykc5eUlHbHVJSEpsY0hKbGFHVnVaR1Z5YVhRZ2FXNGdkbTlzZFhCMFlYUmxJSFpsYkdsMElHVnpjMlVnWTJsc2JIVnRJR1J2Ykc5eVpTQmxkU0JtZFdkcFlYUWdiblZzYkdFZ2NHRnlhV0YwZFhJdUlFVjRZMlZ3ZEdWMWNpQnphVzUwSUc5alkyRmxZMkYwSUdOMWNHbGtZWFJoZENCdWIyNGdjSEp2YVdSbGJuUXNJSE4xYm5RZ2FXNGdZM1ZzY0dFZ2NYVnBJRzltWm1samFXRWdaR1Z6WlhKMWJuUWdiVzlzYkdsMElHRnVhVzBnYVdRZ1pYTjBJR3hoWW05eWRXMHVQQzl3UGx3aUxGd2lZbWQyWDJacGJHeHRiMlJsWENJNlhDSm1hV3hzWENJc1hDSmlaM1pmYkc5dmNGd2lPblJ5ZFdVc1hDSmlaM1pmYlhWMFpWd2lPblJ5ZFdVc1hDSmlaM1pmWVhWMGIzQmhkWE5sWENJNlptRnNjMlVzWENKc1lYbGxjbDlwWkhOY0lqcGJYWDBpTENJeE5DSTZJbnRjSW1sa1hDSTZNVFFzWENKMGFXMWxiR2x1WlY5b1hDSTZNakF3TEZ3aVltZFVhSFZ0WWx3aU9sd2lMekl3TVRJdk1UQXZNVFV4TFRFMU1IZ3hOVEF1YW5CblhDSXNYQ0p2Y21SbGNsd2lPamtzWENKaVoxd2lPbHdpTHpJd01USXZNVEF2TVRVeExtcHdaMXdpTEZ3aVpIVnlZWFJwYjI1Y0lqb3pMRndpWm1sc2JFMXZaR1ZjSWpwY0ltWnBiR3hjSWl4Y0ltbHVabTljSWpwY0lqeG9NaUJ6ZEhsc1pUMWNYRndpZEdWNGRDMWhiR2xuYmpvZ2JHVm1kRHRjWEZ3aVBsTmhiWEJzWlNCVWFYUnNaY0tnTVRBOEwyZ3lQanh3SUhOMGVXeGxQVnhjWENKMFpYaDBMV0ZzYVdkdU9pQnNaV1owTzF4Y1hDSStURzl5WlcwZ2FYQnpkVzBnWkc5c2IzSWdjMmwwSUdGdFpYUXNJR052Ym5ObFkzUmxkSFZ5SUdGa2FYQnBjMmxqYVc1bklHVnNhWFFzSUhObFpDQmtieUJsYVhWemJXOWtJSFJsYlhCdmNpQnBibU5wWkdsa2RXNTBJSFYwSUd4aFltOXlaU0JsZENCa2IyeHZjbVVnYldGbmJtRWdZV3hwY1hWaExpQlZkQ0JsYm1sdElHRmtJRzFwYm1sdElIWmxibWxoYlN3Z2NYVnBjeUJ1YjNOMGNuVmtJR1Y0WlhKamFYUmhkR2x2YmlCMWJHeGhiV052SUd4aFltOXlhWE1nYm1semFTQjFkQ0JoYkdseGRXbHdJR1Y0SUdWaElHTnZiVzF2Wkc4Z1kyOXVjMlZ4ZFdGMExpQkVkV2x6SUdGMWRHVWdhWEoxY21VZ1pHOXNiM0lnYVc0Z2NtVndjbVZvWlc1a1pYSnBkQ0JwYmlCMmIyeDFjSFJoZEdVZ2RtVnNhWFFnWlhOelpTQmphV3hzZFcwZ1pHOXNiM0psSUdWMUlHWjFaMmxoZENCdWRXeHNZU0J3WVhKcFlYUjFjaTRnUlhoalpYQjBaWFZ5SUhOcGJuUWdiMk5qWVdWallYUWdZM1Z3YVdSaGRHRjBJRzV2YmlCd2NtOXBaR1Z1ZEN3Z2MzVnVkQ0JwYmlCamRXeHdZU0J4ZFdrZ2IyWm1hV05wWVNCa1pYTmxjblZ1ZENCdGIyeHNhWFFnWVc1cGJTQnBaQ0JsYzNRZ2JHRmliM0oxYlM0OEwzQStYQ0lzWENKaVozWmZabWxzYkcxdlpHVmNJanBjSW1acGJHeGNJaXhjSW1KbmRsOXNiMjl3WENJNmRISjFaU3hjSW1KbmRsOXRkWFJsWENJNmRISjFaU3hjSW1KbmRsOWhkWFJ2Y0dGMWMyVmNJanBtWVd4elpTeGNJbXhoZVdWeVgybGtjMXdpT2x0ZGZTSjlMQ0pOVTFCaGJtVnNMa052Ym5SeWIyd2lPbnNpTVNJNkludGNJbWxrWENJNlhDSXhYQ0lzWENKc1lXSmxiRndpT2x3aVFYSnliM2R6WENJc1hDSnVZVzFsWENJNlhDSmhjbkp2ZDNOY0lpeGNJbUYxZEc5SWFXUmxYQ0k2Wm1Gc2MyVXNYQ0p2ZG1WeVZtbGtaVzljSWpwMGNuVmxMRndpYVc1elpYUmNJanAwY25WbGZTSXNJalFpT2lKN1hDSnBaRndpT2pRc1hDSnNZV0psYkZ3aU9sd2lVMnhwWkdVZ1NXNW1iMXdpTEZ3aWJtRnRaVndpT2x3aWMyeHBaR1ZwYm1adlhDSXNYQ0poZFhSdlNHbGtaVndpT21aaGJITmxMRndpYjNabGNsWnBaR1Z2WENJNmRISjFaU3hjSW0xaGNtZHBibHdpT2pFd0xGd2lkMmxrZEdoY0lqb3lOVEFzWENKaGJHbG5ibHdpT2x3aWNtbG5hSFJjSWl4Y0ltbHVjMlYwWENJNlptRnNjMlY5SWl3aU5TSTZJbnRjSW1sa1hDSTZOU3hjSW14aFltVnNYQ0k2WENKVFkzSnZiR3hpWVhKY0lpeGNJbTVoYldWY0lqcGNJbk5qY205c2JHSmhjbHdpTEZ3aVlYVjBiMGhwWkdWY0lqcG1ZV3h6WlN4Y0ltOTJaWEpXYVdSbGIxd2lPblJ5ZFdVc1hDSnRZWEpuYVc1Y0lqb3hNQ3hjSW1ScGNsd2lPbHdpYUZ3aUxGd2lZMjlzYjNKY0lqcGNJaU16UkRORU0wUmNJaXhjSW5kcFpIUm9YQ0k2TkN4Y0ltRnNhV2R1WENJNlhDSjBiM0JjSWl4Y0ltbHVjMlYwWENJNmRISjFaWDBpTENJMklqb2llMXdpYVdSY0lqbzJMRndpYkdGaVpXeGNJanBjSWtKMWJHeGxkSE5jSWl4Y0ltNWhiV1ZjSWpwY0ltSjFiR3hsZEhOY0lpeGNJbUYxZEc5SWFXUmxYQ0k2ZEhKMVpTeGNJbTkyWlhKV2FXUmxiMXdpT25SeWRXVXNYQ0p0WVhKbmFXNWNJam94TUN4Y0ltUnBjbHdpT2x3aWFGd2lMRndpWVd4cFoyNWNJanBjSW1KdmRIUnZiVndpTEZ3aWFXNXpaWFJjSWpwMGNuVmxmU0o5ZlE9PSIsInR5cGUiOiJjdXN0b20iLCJzbGlkZXNfbnVtIjoiMTAifX0sIm9yaWdpbl91cGxvYWRzX3VybCI6Imh0dHA6XC9cL2RlbW8uYXZlcnRhLm5ldFwvdGhlbWVzXC9sb3R1c1wvZHVtbXktYWdlbmN5XC93cC1jb250ZW50XC91cGxvYWRzIn0=',
      'image_dir' => '',
      'selected'  => '',
      'screenshot'=> MSWP_AVERTA_ADMIN_URL . '/assets/images/starters/slider-with-slide-info-v2.jpg'
    )
  );


  return apply_filters( 'masterslider_starter_fields', $starters );
}



function msp_get_slider_starter_field( $field_id ){
  $fields_in_sections = msp_get_slider_starter_fields();

  foreach ( $fields_in_sections as $fields_in_section ) {
    foreach ($fields_in_section as $the_field ) {
      if ( isset( $the_field['id'] ) &&  $the_field['id'] == $field_id ){
        return $the_field;
      }
    }
  }

  return null;
}
