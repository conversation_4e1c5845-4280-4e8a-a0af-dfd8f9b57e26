(function(){var i=function(n,t,i){return"http://api.flickr.com/services/rest/?method=flickr.photosets.getPhotos&api_key="+n+"&photoset_id="+t+"&per_page="+i+"&format=json&jsoncallback=?"},r=function(n,t,i){return"http://api.flickr.com/services/rest/?&method=flickr.people.getPublicPhotos&api_key="+n+"&user_id="+t+"&per_page="+i+"&format=json&jsoncallback=?"},u=function(n,t){return"http://api.flickr.com/services/rest/?&method=flickr.photos.getInfo&api_key="+n+"&photo_id="+t+"&format=json&jsoncallback=?"},t=function(n,t,i,r,u){return"http://farm"+n+".staticflickr.com/"+t+"/"+i+"_"+r+u+".jpg"},n;window.MSFlickr=function(n,t){var f={count:10,type:"photoset",desc:!0,title:!0,author:!0,thumbs:!0,descEff:"fade",titleEff:"fade",authorEff:"fade",autoplayDelay:4,thumbSize:"q",imgSize:"c"},u;if(this.count=0,this.slider=n,this.slider.preventInit=!0,!t.key){this.errMsg("Flickr API Key required. Please add it in gallery options.");return}$.extend(f,t),this.options=f,u=this,this.options.type==="photoset"?$.getJSON(i(this.options.key,this.options.id,this.options.count),function(n){u._photosData(n)}):$.getJSON(r(this.options.key,this.options.id,this.options.count),function(n){u.options.type="photos",u._photosData(n)}),this.options.imgSize!==""&&(this.options.imgSize="_"+this.options.imgSize),this.options.thumbSize="_"+this.options.thumbSize},n=MSFlickr.prototype,n._photosData=function(n){if(n.stat==="fail"){this.errMsg("Flickr API ERROR#"+n.code+": "+n.message);return}var i=this,r=this.options.author||this.options.desc;$.each(n[this.options.type].photo,function(n,f){var e='<div class="ms-slide" id="slide-'+n+'" data-delay="'+i.options.autoplayDelay+'">',o=f.title;e+='<img src="masterslider/blank.gif" data-src="'+t(f.farm,f.server,f.id,f.secret,i.options.imgSize)+'" alt="'+o+'" />',i.options.thumbs&&(e+='<img class="ms-thumb" src="'+t(f.farm,f.server,f.id,f.secret,i.options.thumbSize)+'" alt="'+o+'" />'),i.options.title&&(e+='<div class="ms-layer ms-fkr-title" data-effect="'+i.options.titleEff+'" data-duration = "1000"  data-ease = "easeOutQuad"  data-type = "text">'+o+"<\/div>"),e+="<\/div>",r&&(i.count++,$.getJSON(u(i.options.key,f.id),function(t){i.options.desc&&$("#slide-"+n).append('<div class="ms-layer ms-fkr-desc" data-effect="'+i.options.descEff+'" data-duration = "1000"  data-ease = "easeOutQuad"  data-type = "text">'+t.photo.description._content+"<\/div>"),i.options.author&&$("#slide-"+n).append('<div class="ms-layer ms-fkr-author" data-effect="'+i.options.authorEff+'" data-duration = "1000"  data-ease = "easeOutQuad"  data-type = "text">'+t.photo.owner.realname+"<\/div>"),i.count--,i.count===0&&i._initSlider()})),$(e).appendTo(i.slider.$element)}),this.count===0&&i._initSlider()},n.errMsg=function(n){this.slider.$element.css("display","block"),this.errEle||(this.errEle=$('<div style="font-family:Arial; color:red; font-size:12px; position:absolute; top:10px; left:10px"><\/div>').appendTo(this.slider.$loading)),this.errEle.html(n)},n._initSlider=function(){this.slider.preventInit=!1,this.slider._init()}})()