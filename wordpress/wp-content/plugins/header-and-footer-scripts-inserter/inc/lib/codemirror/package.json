{"name": "codemirror", "version": "5.38.0", "main": "lib/codemirror.js", "style": "lib/codemirror.css", "description": "Full-featured in-browser code editor", "license": "MIT", "directories": {"lib": "./lib"}, "scripts": {"build": "rollup -c", "watch": "rollup -w -c", "prepare": "npm run-script build", "test": "node ./test/run.js", "lint": "bin/lint"}, "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "bugs": "http://github.com/codemirror/CodeMirror/issues", "keywords": ["JavaScript", "CodeMirror", "Editor"], "homepage": "http://codemirror.net", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "https://github.com/codemirror/CodeMirror.git"}, "jspm": {"directories": {}, "dependencies": {}, "devDependencies": {}}}