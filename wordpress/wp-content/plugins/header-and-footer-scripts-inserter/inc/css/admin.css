/*
 * Style sheet for plugin settings page
 *
 * @package     Head and Footer Scripts Inserter
 * <AUTHOR>
 * @link        https://www.spacexchimp.com
 * @copyright   Copyright (c) 2016-2019 Space X-Chimp. All Rights Reserved.
 */


/* Main
 -------------------------------------------------------------- */
body {
    background: #f1f1f1;
}
p {
    font-family: Verdana, Geneva, sans-serif;
}
.postbox a {
    text-decoration: none;
}

/* Title and Description of page
 -------------------------------------------------------------- */
.sxc-header {
    padding: 30px !important;
    background-color: #333;
    color: #e0dfdc;
    font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
    font-size: 60px !important;
    text-align: center;
    text-transform: uppercase;
    text-rendering: optimizeLegibility;
    text-shadow:
                 0 -1px 0 #fff,
                 0 1px 0 #2e2e2e,
                 0 2px 0 #2a2a2a,
                 0 3px 0 #262626,
                 0 4px 0 #222,
                 0 5px 0 #1e1e1e,
                 0 6px 0 #1a1a1a,
                 0 7px 0 #161616,
                 0 8px 0 #121212,
                 0 22px 30px #000;
    letter-spacing: 0.1em;
    line-height: 50px !important;
    overflow: hidden;
}
.sxc-header span {
    content: '\A';
    display: block;
    margin-top: 15px;
    font-size: 16px;
    letter-spacing: 0.2em;
    text-shadow: none;
    line-height: 20px;
}
.sxc-header span a,
.sxc-header span a:hover {
    color: #fff;
}
.sxc-header span a:hover {
    text-shadow: none;
    text-decoration: none;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}
.sxc-header .version {
    position: absolute;
    right: 40px;
    color: #a6a6a6;
    text-shadow: none;
    letter-spacing: 0.1em;
}

/* Titles and Descriptions of sections
 -------------------------------------------------------------- */
.title {
    font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
    font-size: 16px !important;
    font-weight: 400;
    border-bottom: 1px solid #eee;
}

/* Sidebar
 -------------------------------------------------------------- */
#side-sortables {
    position: relative;
}
#side-sortables .postbox {
    text-align: center;
}
#side-sortables .banner .inside {
    margin: 0;
    padding: 5px 0;
}
#side-sortables .banner img {
    width: 265px;
}
#side-sortables .banner .btn {
    width: 95%;
    height: 40px;
    margin-bottom: 0;
}

/* Support - Tab and Addition section
 -------------------------------------------------------------- */
#tab-support .inside {
    min-height: 270px;
}
#tab-support .image-with-button img[alt="Thanks!"] {
    display: block;
}
#support-addition {
    display: none;
    margin-top: 30px;
}

/* Responsive page
 -------------------------------------------------------------- */
@media (max-width: 860px) {
    .inner-sidebar {
        display: none !important;
    }
    #support-addition {
        display: block !important;
    }
    #post-body-content {
        margin-right: 0 !important;
    }
}

/* Tabs pages
 -------------------------------------------------------------- */
.tab-page {
    display: none;
}
.tab-page.active {
    display: block;
}

/* Tabs navigation menu
 -------------------------------------------------------------- */
.tabs-nav {
    font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
    font-weight: 400;
    font-size: 16px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 50px;
    margin: -11px 0 18px 0;
    background: #fff;
    border: 1px solid #d9d9d9;
    box-shadow: 0 1px 2px 0 #d9d9d9;
    -webkit-border-radius: 0.3rem;
    -moz-border-radius: 0.3rem;
    border-radius: 0.3rem;
}
.tabs-nav::after {
    content: '';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}
.tabs-nav li {
    position: relative;
    display: block;
    float: left;
    margin-bottom: 0;
}
.tabs-nav li:first-of-type a {
    -webkit-border-radius: 3px 0 0 3px;
    -moz-border-radius: 3px 0 0 3px;
    border-radius: 3px 0 0 3px;
}
.tabs-nav li.active::before {
    position: absolute;
    content: '';
    top: 0;
    right: 0;
    height: 100%;
    width: 1px;
    background: #f2f2f2;
}
.tabs-nav li.active::after {
    background-color: #f2f2f2;
    position: absolute;
    content: '';
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -moz-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -o-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    transform: translateX(-50%) translateY(-50%) rotate(45deg);
    margin: 0.8px 0 0;
    width: 0.6em;
    height: 0.6em;
    border: none;
    border-bottom: 1px solid #d4d4d5;
    border-right: 1px solid #d4d4d5;
    -webkit-transition: background 0.1s ease;
    -moz-transition: background 0.1s ease;
    -o-transition: background 0.1s ease;
    transition: background 0.1s ease;
}
.tabs-nav li a {
    position: relative;
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    line-height: 1.8;
    color: grey;
    border-left: 1px solid;
    border-right: 1px solid;
    border-color: #fff;
}
.tabs-nav li a:hover,
.tabs-nav li a:focus {
    text-decoration: none;
    background: #f8f8f8;
    border-color: #eee;
}
.tabs-nav li.active a,
.tabs-nav li.active a:hover,
.tabs-nav li.active a:focus {
    background: #f4f4f4;
    color: #000;
    font-weight: normal;
    border-color: #eee;
    -webkit-box-shadow: inset 0 3px 5px #d9d9d9;
    -moz-box-shadow: inset 0 3px 5px #d9d9d9;
    box-shadow: inset 0 3px 5px #d9d9d9;
}
@media (max-width: 500px) {
    .tabs-nav li:last-of-type {
        display: none;
    }
}
@media (max-width: 413px) {
    .tabs-nav li:nth-last-of-type(2) {
        display: none;
    }
}

/* Modal - PopUp windows
 -------------------------------------------------------------- */
.modal-dialog {
    margin-top: 15%;
}
.hello-message .modal-body {
    height: 180px;
}
.hello-message .modal-body img {
    float: left;
    width: 150px;
    height: 150px;
    padding: 10px;
    margin-right: 20px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
}
.hello-message .modal-body .emoji {
    float: none;
}
.hello-message .modal-body p {
    font-size: 16px;
}
.hello-message .modal-body p:first-of-type {
    padding-top: 5%;
}
.error-message .modal-body {
    padding: 30px;
    text-align: center;
}

/* Messages
 -------------------------------------------------------------- */
#setting-error-settings_updated {
    display: none;
}
#message.updated i {
    padding-right: 5px;
    color: green;
    font-size: 1.5em;
    vertical-align: middle;
}
#message.error i {
    padding-right: 5px;
    color: red;
    font-size: 2.5em;
    vertical-align: middle;
}
#message.error span {
    display: inline-block;
    vertical-align: middle;
}

/* Notes
 -------------------------------------------------------------- */
.note {
    padding: 0.3em 1em;
    border-left: 3px solid #05c2ff;
    border-right: 3px solid #05c2ff;
    background: #eafaff;
    font-family: Verdana, Geneva, sans-serif;
    color: #646464;
    padding-left: 15px;
    letter-spacing: 0.1px;
}

/* Help text
 -------------------------------------------------------------- */
td.help-text {
    font-family: Verdana, Geneva, sans-serif;
    font-size: 12px;
    font-style: italic;
    float: left;
    clear: left;
    color: #797979;
    line-height: 15px;
    padding-top: 0;
}

/* Spoilers
 -------------------------------------------------------------- */
.panel-group {
    margin-top: 10px;
}
.panel-heading a {
    text-decoration: none;
}
.panel-title,
.panel-title::before,
.panel-body::before {
    font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
    font-size: 16px;
    font-weight: 400;
    color: #6d6d6d;
    text-shadow: 1px 1px 0 #fff;
}
.panel-title::before {
    content: "Q.";
    display: inline-block;
    color: red;
    padding-right: 8px;
}
.panel-title::after {
    content: "\25BC";
    float: right;
    margin: 0;
    background: none;
    border: 0;
    cursor: pointer;
    color: gray;
}
.panel-title > div {
    display: inline;
}
.panel-body::before {
    content: "A.";
    display: inline-block;
    color: #00a5e2;
    padding-right: 8px;
}
.panel-body {
    display: inline-block;
    width: 100%;
    font-family: Verdana, Geneva, sans-serif;
    font-size: 13px;
}
.panel-body > div {
    display: inline;
}

/* Button: Save - Main
 -------------------------------------------------------------- */
.button-save-main {
    width: 100%;
    margin-bottom: 20px;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

/* Button: Save - Top
 -------------------------------------------------------------- */
.button-save-top {
    position: fixed;
    z-index: 999;
    top: 32px;
    width: 55px;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    text-align: left;
    transition: all 300ms;
}
.button-save-top:hover {
    width: 186px;
}
.button-save-top i {
    font-size: 28px;
    vertical-align: bottom;
}
.button-save-top span {
    margin-left: 10px;
    visibility: hidden;
    opacity: 0;
}
.button-save-top:hover span {
    visibility: visible;
    opacity: 1;
}
@media (max-width: 782px) {
    .button-save-top {
        top: 46px;
    }
}

/* Button: Labeled
 -------------------------------------------------------------- */
.button-labeled {
    margin: 5px 0 15px 0;
    padding-top: 0;
    padding-bottom: 0;
    max-width: 100%;
    overflow: hidden;
    color: #32689a;
    font-family: "Lucida Grande", Tahoma, sans-serif;
    font-size: 18px;
    text-shadow: 1px 1px 0 #fff;
    border-bottom-color: rgba(0, 0, 0, 0.4);
    -webkit-border-radius: 3px !important;
    -moz-border-radius: 3px !important;
    border-radius: 3px !important;
    background-image: -moz-linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05) 49%, rgba(0, 0, 0, 0.05) 51%, rgba(0, 0, 0, 0.1));
    background-image: -ms-linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05) 49%, rgba(0, 0, 0, 0.05) 51%, rgba(0, 0, 0, 0.1));
    background-image: -o-linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05) 49%, rgba(0, 0, 0, 0.05) 51%, rgba(0, 0, 0, 0.1));
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.1)), color-stop(49%, rgba(255, 255, 255, 0.05)), color-stop(51%, rgba(0, 0, 0, 0.05)), to(rgba(0, 0, 0, 0.1)));
    background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05) 49%, rgba(0, 0, 0, 0.05) 51%, rgba(0, 0, 0, 0.1));
    background-image: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05) 49%, rgba(0, 0, 0, 0.05) 51%, rgba(0, 0, 0, 0.1));
}
.button-labeled:hover,
.button-labeled:focus {
    color: #32689a;
    background-position: 0;
    background-color: transparent;
    background-image: -moz-linear-gradient(rgba(255, 255, 255, 0.15) 49%, rgba(0, 0, 0, 0.1) 51%, rgba(0, 0, 0, 0.15));
    background-image: -ms-linear-gradient(rgba(255, 255, 255, 0.15) 49%, rgba(0, 0, 0, 0.1) 51%, rgba(0, 0, 0, 0.15));
    background-image: -o-linear-gradient(rgba(255, 255, 255, 0.15) 49%, rgba(0, 0, 0, 0.1) 51%, rgba(0, 0, 0, 0.15));
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.15)), color-stop(49%, rgba(255, 255, 255, 0.15)), color-stop(51%, rgba(0, 0, 0, 0.1)), to(rgba(0, 0, 0, 0.15)));
    background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0.15) 49%, rgba(0, 0, 0, 0.1) 51%, rgba(0, 0, 0, 0.15));
    background-image: linear-gradient(rgba(255, 255, 255, 0.15) 49%, rgba(0, 0, 0, 0.1) 51%, rgba(0, 0, 0, 0.15));
}
.button-labeled .btn-label {
    position: relative;
    display: inline-block;
    width: 48px;
    left: -12px;
    padding: 6px 12px;
    border-right: 1px solid #ccc;
}

/* Button: PayPal
 -------------------------------------------------------------- */
.paypal {
    max-width: 100%;
    font-size: 18px;
    overflow: hidden;
}

/* Custom List
 -------------------------------------------------------------- */
.custom-list {
    margin-top: 15px;
    margin-left: 50px;
    margin-bottom: 30px;
    list-style-type: none;
}
.custom-list li {
    counter-increment: step-counter;
    margin-bottom: 15px;
    font-family: Verdana, Geneva, sans-serif;
}
.custom-list li::before {
    content: '+';
    margin-left: -30px;
    margin-right: 8px;
    padding: 4px 7px;
    font-size: 90%;
    font-weight: bold;
    background-color: #00c8c8;
    color: white;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
}

/* Custom List Numbers
 -------------------------------------------------------------- */
.custom-counter {
    margin-top: 15px;
    margin-left: 50px;
    margin-bottom: 30px;
    list-style-type: none;
}
.custom-counter li {
    counter-increment: step-counter;
    margin-bottom: 15px;
    font-family: Verdana, Geneva, sans-serif;
}
.custom-counter li::before {
    content: counter(step-counter);
    margin-left: -30px;
    margin-right: 8px;
    padding: 4px 7px;
    font-size: 90%;
    font-weight: bold;
    background-color: #00c8c8;
    color: white;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
}

/* Control: Color
 -------------------------------------------------------------- */
.wp-picker-container a {
    text-decoration: none;
}
.wp-color-result {
    height: 30px;
}
.wp-color-result::after {
    line-height: 28px;
}

/* Form-table
 -------------------------------------------------------------- */
.form-table {
    clear: none;
    margin-left: 20px;
    width: -webkit-calc(100% - 40px);
    width: calc(100% - 40px);
    font-family: Verdana, Geneva, sans-serif;
    font-size: 13px;
}
.form-table tr {
    vertical-align: top;
}
.form-table th,
.form-table td {
    padding-bottom: 10px;
}
.form-table th[scope="row"] {
    font-weight: normal;
}

/* Control: Field
 -------------------------------------------------------------- */
.control-field,
.control-textarea {
    position: relative;
    max-width: 600px;
    width: 100%;
    padding-top: 5px;
    background: #fcfcfc;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

/* Control: Number
 -------------------------------------------------------------- */
.control-number {
    width: 150px;
}
.control-number input {
    width: 100px;
    height: 34px;
}
.control-number input[type=number]::-webkit-inner-spin-button,
.control-number input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* SPECIFIC TO THIS PLUGIN
 -------------------------------------------------------------- */

/* Messages */
.not-saved {
    display: none;
    margin-right: 10px;
    font-family: Helvetica;
    vertical-align: middle;
    color: red;
}

/* Help text */
form .help-text {
    margin-top: 30px;
    margin-left: 6px;
}

/* CodeMirror Editor */
.CodeMirror {
    height: 200px !important;
    border: 1px solid #ddd;
    margin-left: 5px;
    margin-right: 5px;
}
.CodeMirror pre.CodeMirror-placeholder {
    color: #999;
}

/* Submit button */
input[type="submit"] {
    margin-top: 20px;
}
