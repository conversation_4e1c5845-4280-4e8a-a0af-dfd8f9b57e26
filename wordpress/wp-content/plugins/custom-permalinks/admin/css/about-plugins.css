.wrap {
    margin: 2em auto 1em;
    max-width: 1090px;
}
.wrap .float {
    width: calc(100% - 188px);
    float: left;
    box-sizing: border-box;
}
.wrap .float:nth-child(2) {
    width: 128px;
    margin: 40px 0 70px 50px;
}
.wrap h1 {
    margin: 40px auto 20px;
    font-size: 2.5em;
}
.tagline p {
    font-size: 1.3em;
    margin-bottom: 1em;
    margin: 0 auto 1em;
}
p a {
    text-decoration: none;
}
#footer-left a {
	text-decoration: underline;
}
a .star svg polygon {
    fill: #f9d539;
}
a .star svg:hover polygon {
    fill: #ff7c00;
}
.product {
    width: 100%;
    display: inline-block;
    text-align: center;
}
.product > span {
    font-size: 1.3em;
    margin-bottom: 1em;
    display: block;
    text-align: left;
    margin: 0 auto 1em;
}
.product h2 {
    display: block;
    margin: 1em auto;
    font-size: 2.2em;
    text-align:left;
}
.product .box {
    background-color: #fff;
    width: 300px;
    min-height: 200px;
    text-align: center;
    padding: 20px;
    box-shadow: 2px 2px 3px #d1d1d1;
    display: inline-block;
    margin: 10px;
    vertical-align: top;
}
.product .tagline{
    text-align: center;
}
.product .img {
    background: #eee;
    border-radius: 50%;
    width: 150px;
    height: 150px;
    margin: 0 auto;
}
.product .img svg,
.product .img img {
    max-width: 70%;
    margin-top: 25px;
}
.product .h3 {
    margin-bottom: 5px;
}

.product p {
    color: #888;
    font-size: 14px;
    line-height: 26px;
    text-align: left;
    transition: max-height 450ms 100ms ease;
    margin: 1em 0 2em;
}
@media(min-width:746px) {
	.product p {
		min-height: 185px;
		max-height: 135px;
		overflow: hidden;
	}
	.product .box:hover p {
		max-height: 300px
	}
}
.box.recommended {
    position: relative;
    background: rgb(232, 235, 238);
}
.box.recommended * {
    position: relative;
    z-index:2;
}
.box.recommended:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    transition: all 350ms ease;
    z-index: 1;
    background: aliceblue;
    right: 0;
    top: 0;
    background: linear-gradient(35deg, rgb(232, 235, 238) 62%,rgba(181,198,208,1) 50%);
    transform-origin: top right;
}
@keyframes blinking {
0%,100%{opacity:0.1}
25%,75%{opacity:1}
}
.box.recommended:after {
    content: "RECOMMENDED";
    position: absolute;
    top: 41px;
    right: -11px;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    transform: rotateZ(35deg);
    font-family: sans-serif;
    z-index: 1;
    animation: blinking 2s infinite linear;
}
.box.recommended:hover:before {
    height: 175px;
    width: 150px;
}
.box.recommended:hover:after {
    animation: blinking 2s 1 linear;
}

.box > a {
    display: inline-block;
    float: right;
    background: rgb(181,198,208);
    color: #fff;
    background: linear-gradient(-35deg, rgba(216,225,231,0.8) 12%,rgb(181,198,208) 13%);
    text-decoration: none;
    font-size:20px;
    padding: 8px 17px;
    border-radius: 3px;
    transition: all 250ms ease;
}
.box > a:hover {
    font-size: 22px;
}
@media(max-width:991px) {
	.wrap {
		max-width: 725px;
	}
}
@media(max-width:767px) {
	.wrap {
		max-width: 640px;
	}
	.product .box {
		width: 258px;
		min-height: 410px;
	}
}
@media(max-width:650px){
	.wrap {
		max-width: 460px;
	}
	.product .box {
		width: 400px;
		max-width: 90%;
		min-height: inherit;
		margin-left: 0;
		margin-right: 0;
		box-sizing: border-box;
	}
	.wrap .float {
		width: 100%;
	}
	.wrap .float:nth-child(2) {
		margin: 40px 0 70px 50px;
		position: absolute;
		right: 20px;
	}
	.wrap h1 {
		margin: 70px 0 30px;
		width: 300px;
		line-height: 1.2em;
	}
}
@media(max-width:450px) {
	.wrap h1 {
		margin: 0px 0 150px;
		font-size: 2em;
		width: 300px;
	}
	.wrap .float:nth-child(2) {
		right: inherit;
		margin: 80px auto 0;
		text-align: center;
	}
}