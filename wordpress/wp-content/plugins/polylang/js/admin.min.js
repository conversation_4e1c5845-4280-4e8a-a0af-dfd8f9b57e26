jQuery(document).ready(function(e){function t(){var t=e(this).find(":selected"),i=t.data("url");if(i){var n=e(this).iconselectmenu("widget").children(":last"),s=t.data("width"),l=t.data("height");e("<img class='ui-icon' />").prop("src",i).prop("width",s).prop("height",l).appendTo(n)}}var i;e("table.languages").on({focusin:function(){clearTimeout(i),focusedRowActions=e(this).find(".row-actions"),e(".row-actions").not(this).removeClass("visible"),focusedRowActions.addClass("visible")},focusout:function(){i=setTimeout(function(){focusedRowActions.removeClass("visible")},30)}},"tr"),e.widget("custom.iconselectmenu",e.ui.selectmenu,{_renderItem:function(t,i){var n=e("<li>",{text:i.label}),s=e(i.element),l=s.data("url");if(l){var a=s.data("width"),c=s.data("height");e("<img class='ui-icon' />").prop("src",l).prop("width",a).prop("height",c).appendTo(n)}return n.appendTo(t)}}),e("#flag_list").iconselectmenu({create:t,select:t}),e("#lang_list").change(function(){var i=e(this).val().split(":"),n=e("option:selected",this).text().split(" - ");e("#lang_slug").val(i[0]),e("#lang_locale").val(i[1]),e('input[name="rtl"]').val([i[2]]),e("#lang_name").val(n[0]),e('#flag_list option[value="'+i[3]+'"]').attr("selected","selected"),e("#flag_list").iconselectmenu("destroy").iconselectmenu({create:t,select:t})}),e(".translation input").keypress(function(t){13===t.keyCode&&(t.preventDefault(),e("#submit").click())}),e("#the-list").on("click",".configure>a",function(){return e(".pll-configure").hide().prev().show(),e(this).closest("tr").hide().next().show(),!1}),e("#the-list").on("click",".cancel",function(){e(this).closest("tr").hide().prev().show()}),e("#the-list").on("click",".save",function(){var t=e(this).closest("tr"),i=t.attr("id").split("-"),n={action:"pll_save_options",pll_ajax_settings:!0,module:i[i.length-1],_pll_nonce:e("#_pll_nonce").val()};n=t.find(":input").serialize()+"&"+e.param(n),e.post(ajaxurl,n,function(i){var n=wpAjax.parseAjaxResponse(i,"ajax-response");e.each(n.responses,function(){switch(this.what){case"license-update":e("#pll-license-"+this.data).replaceWith(this.supplemental.html);break;case"success":t.hide().prev().show();case"error":e(".settings-error").remove(),e("h1").after(this.data),e(".notice.is-dismissible").each(function(){var t=e(this),i=e('<button type="button" class="notice-dismiss"><span class="screen-reader-text"></span></button>'),n=commonL10n.dismiss||"";i.find(".screen-reader-text").text(n),t.append(i),i.on("click.wp-dismiss-notice",function(i){i.preventDefault(),t.fadeTo(100,0,function(){e(this).slideUp(100,function(){e(this).remove()})})})})}})})}),e(".pll-configure").keypress(function(t){13===t.keyCode&&(t.preventDefault(),e(this).find(".save").click()),27===t.keyCode&&(t.preventDefault(),e(this).find(".cancel").click())}),e("input[name='force_lang']").change(function(){function t(e,t){t?e.show():e.hide()}var i=e(this).val();t(e("#pll-domains-table"),3==i),t(e("#pll-hide-default"),3>i),t(e("#pll-rewrite"),2>i),t(e("#pll-redirect-lang"),2>i)}),e(".pll-deactivate-license").on("click",function(){var t={action:"pll_deactivate_license",pll_ajax_settings:!0,id:e(this).attr("id"),_pll_nonce:e("#_pll_nonce").val()};e.post(ajaxurl,t,function(t){e("#pll-license-"+t.id).replaceWith(t.html)})})});
