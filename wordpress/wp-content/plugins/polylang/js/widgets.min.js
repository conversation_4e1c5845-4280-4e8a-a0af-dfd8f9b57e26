jQuery(function(e){function t(t){if(o){t=e(t);var n=e(".widget-top .widget-title h3",t),l=e(".pll-lang-choice option:selected",t).val(),i=l&&o.hasOwnProperty(l)?o[l]:null;if(i){i+=" &nbsp; ";var d=e(".pll-lang",n);d.length?d.html(i):(flag='<span class="pll-lang">'+i+"</span>",n.prepend(flag))}else e(".pll-lang",n).remove()}}function n(e){e.extended(wp.customize.Widgets.WidgetControl)&&(e.embedWidgetContent(),t(e.container.find(".widget")))}var l,i,o;"undefined"!=typeof pll_widgets&&pll_widgets.hasOwnProperty("flags")&&(o=pll_widgets.flags),"undefined"!=typeof wp.customize?(l=e("#customize-controls"),i=".customize-control .widget",wp.customize.control.each(n),wp.customize.control.bind("add",n)):(l=e("#widgets-right"),i=".widget"),e(i,l).each(function(){t(this)}),l.on("change",".pll-lang-choice",function(){t(e(this).parents(".widget"))})});
