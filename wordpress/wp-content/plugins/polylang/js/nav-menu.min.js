jQuery(document).ready(function(a){a("#update-nav-menu").bind("click",function(b){if(b.target&&b.target.className&&-1!=b.target.className.indexOf("item-edit")){a("input[value='#pll_switcher'][type=text]").parent().parent().parent().each(function(){var d=a(this).attr("id").substring(19);a(this).children("p:not( .field-move )").remove();h=a("<input>").attr({type:"hidden",id:"edit-menu-item-title-"+d,name:"menu-item-title["+d+"]",value:pll_data.title});a(this).append(h);h=a("<input>").attr({type:"hidden",id:"edit-menu-item-url-"+d,name:"menu-item-url["+d+"]",value:"#pll_switcher"});a(this).append(h);h=a("<input>").attr({type:"hidden",id:"edit-menu-item-pll-detect-"+d,name:"menu-item-pll-detect["+d+"]",value:1});a(this).append(h);ids=Array("hide_if_no_translation","hide_current","force_home","show_flags","show_names","dropdown");for(var c=0;c<ids.length;c++){p=a("<p>").attr("class","description");a(this).prepend(p);label=a("<label>").attr("for","edit-menu-item-"+ids[c]+"-"+d).text(" "+pll_data.strings[ids[c]]);p.append(label);cb=a("<input>").attr({type:"checkbox",id:"edit-menu-item-"+ids[c]+"-"+d,name:"menu-item-"+ids[c]+"["+d+"]",value:1});if((typeof(pll_data.val[d])!="undefined"&&pll_data.val[d][ids[c]]==1)||(typeof(pll_data.val[d])=="undefined"&&ids[c]=="show_names")){cb.prop("checked",true)}label.prepend(cb)}});a(".menu-item-data-object-id").each(function(){var d=a(this).val();var c=["names-","flags-"];a.each(c,function(f,e){a("#edit-menu-item-show_"+e+d).change(function(){if("checked"!=a(this).attr("checked")){a("#edit-menu-item-show_"+c[1-f]+d).prop("checked",true)}})})})}})});
