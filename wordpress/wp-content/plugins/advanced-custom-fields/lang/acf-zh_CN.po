msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2015-08-11 23:50+0200\n"
"PO-Revision-Date: 2018-03-14 09:58+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:63
msgid "Advanced Custom Fields"
msgstr "高级自定义字段"

#: acf.php:205 admin/admin.php:61
msgid "Field Groups"
msgstr "字段组"

#: acf.php:206
msgid "Field Group"
msgstr "字段组"

#: acf.php:207 acf.php:239 admin/admin.php:62
#: pro/fields/flexible-content.php:517
msgid "Add New"
msgstr "新建"

#: acf.php:208
msgid "Add New Field Group"
msgstr "添加字段组"

#: acf.php:209
msgid "Edit Field Group"
msgstr "编辑字段组"

#: acf.php:210
msgid "New Field Group"
msgstr "新建字段组"

#: acf.php:211
msgid "View Field Group"
msgstr "查看字段组"

#: acf.php:212
msgid "Search Field Groups"
msgstr "搜索字段组"

#: acf.php:213
msgid "No Field Groups found"
msgstr "没有找到字段组"

#: acf.php:214
msgid "No Field Groups found in Trash"
msgstr "回收站中没有找到字段组"

#: acf.php:237 admin/field-group.php:182 admin/field-group.php:213
#: admin/field-groups.php:519
msgid "Fields"
msgstr "字段"

#: acf.php:238
msgid "Field"
msgstr "字段"

#: acf.php:240
msgid "Add New Field"
msgstr "添加新字段"

#: acf.php:241
msgid "Edit Field"
msgstr "编辑字段"

#: acf.php:242 admin/views/field-group-fields.php:18
#: admin/views/settings-info.php:111
msgid "New Field"
msgstr "新字段"

#: acf.php:243
msgid "View Field"
msgstr "新字段"

#: acf.php:244
msgid "Search Fields"
msgstr "搜索字段"

#: acf.php:245
msgid "No Fields found"
msgstr "没找到字段"

#: acf.php:246
msgid "No Fields found in Trash"
msgstr "回收站里没有字段"

#: acf.php:268 admin/field-group.php:283 admin/field-groups.php:583
#: admin/views/field-group-options.php:18
msgid "Disabled"
msgstr "禁用"

#: acf.php:273
#, php-format
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] "禁用 <span class=\"count\">(%s)</span>"

#: admin/admin.php:57 admin/views/field-group-options.php:120
msgid "Custom Fields"
msgstr "字段"

#: admin/field-group.php:68 admin/field-group.php:69 admin/field-group.php:71
msgid "Field group updated."
msgstr "字段组已更新。"

#: admin/field-group.php:70
msgid "Field group deleted."
msgstr "字段组已删除。"

#: admin/field-group.php:73
msgid "Field group published."
msgstr "字段组已发布。"

#: admin/field-group.php:74
msgid "Field group saved."
msgstr "字段组已保存。"

#: admin/field-group.php:75
msgid "Field group submitted."
msgstr "字段组已提交。"

#: admin/field-group.php:76
msgid "Field group scheduled for."
msgstr "字段组已定时。"

#: admin/field-group.php:77
msgid "Field group draft updated."
msgstr "字段组草稿已更新。"

#: admin/field-group.php:176
msgid "Move to trash. Are you sure?"
msgstr "确定要删除吗？"

#: admin/field-group.php:177
msgid "checked"
msgstr "已选"

#: admin/field-group.php:178
msgid "No toggle fields available"
msgstr "没有可用的切换字段"

#: admin/field-group.php:179
msgid "Field group title is required"
msgstr "字段组的标题是必填项"

#: admin/field-group.php:180 api/api-field-group.php:607
msgid "copy"
msgstr "复制"

#: admin/field-group.php:181
#: admin/views/field-group-field-conditional-logic.php:67
#: admin/views/field-group-field-conditional-logic.php:162
#: admin/views/field-group-locations.php:23
#: admin/views/field-group-locations.php:131 api/api-helpers.php:3262
msgid "or"
msgstr "或"

#: admin/field-group.php:183
msgid "Parent fields"
msgstr "父字段"

#: admin/field-group.php:184
msgid "Sibling fields"
msgstr "兄弟字段"

#: admin/field-group.php:185
msgid "Move Custom Field"
msgstr "移动自定义字段"

#: admin/field-group.php:186
msgid "This field cannot be moved until its changes have been saved"
msgstr "保存这个字段的修改以后才能移动这个字段"

#: admin/field-group.php:187
msgid "Null"
msgstr "Null"

#: admin/field-group.php:188 core/input.php:128
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "如果浏览其它页面，会丢失当前所做的修改"

#: admin/field-group.php:189
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "\"field_\" 这个字符串不能作为字段名字的开始部分"

#: admin/field-group.php:214
msgid "Location"
msgstr "位置"

#: admin/field-group.php:215
msgid "Settings"
msgstr "设置"

#: admin/field-group.php:253
msgid "Field Keys"
msgstr "字段 Keys"

#: admin/field-group.php:283 admin/views/field-group-options.php:17
msgid "Active"
msgstr "激活"

#: admin/field-group.php:744
msgid "Front Page"
msgstr "首页"

#: admin/field-group.php:745
msgid "Posts Page"
msgstr "文章页"

#: admin/field-group.php:746
msgid "Top Level Page (no parent)"
msgstr "顶级页面 (无父页面)"

#: admin/field-group.php:747
msgid "Parent Page (has children)"
msgstr "父页面（有子页）"

#: admin/field-group.php:748
msgid "Child Page (has parent)"
msgstr "子页面（有父页面）"

#: admin/field-group.php:764
msgid "Default Template"
msgstr "默认模板"

#: admin/field-group.php:786
msgid "Logged in"
msgstr "登录"

#: admin/field-group.php:787
msgid "Viewing front end"
msgstr "查看前端"

#: admin/field-group.php:788
msgid "Viewing back end"
msgstr "查看后端"

#: admin/field-group.php:807
msgid "Super Admin"
msgstr "超级管理员"

#: admin/field-group.php:818 admin/field-group.php:826
#: admin/field-group.php:840 admin/field-group.php:847
#: admin/field-group.php:862 admin/field-group.php:872 fields/file.php:235
#: fields/image.php:226 pro/fields/gallery.php:653
msgid "All"
msgstr "所有"

#: admin/field-group.php:827
msgid "Add / Edit"
msgstr "添加 / 编辑"

#: admin/field-group.php:828
msgid "Register"
msgstr "注册"

#: admin/field-group.php:1059
msgid "Move Complete."
msgstr "移动完成。"

#: admin/field-group.php:1060
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "%s 字段现在会在 %s 字段组里"

#: admin/field-group.php:1062
msgid "Close Window"
msgstr "关闭窗口"

#: admin/field-group.php:1097
msgid "Please select the destination for this field"
msgstr "请选择这个字段的位置"

#: admin/field-group.php:1104
msgid "Move Field"
msgstr "移动字段"

#: admin/field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "启用 <span class=\"count\">(%s)</span>"

#: admin/field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "字段组已被复制。%s"

#: admin/field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s 字段组已被复制。"

#: admin/field-groups.php:228
#, php-format
msgid "Field group synchronised. %s"
msgstr "字段组已同步。 %s"

#: admin/field-groups.php:232
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s 字段组已同步。"

#: admin/field-groups.php:403 admin/field-groups.php:573
msgid "Sync available"
msgstr "有可用同步"

#: admin/field-groups.php:516
msgid "Title"
msgstr "标题"

#: admin/field-groups.php:517 admin/views/field-group-options.php:98
#: admin/views/update-network.php:20 admin/views/update-network.php:28
msgid "Description"
msgstr "描述"

#: admin/field-groups.php:518 admin/views/field-group-options.php:10
msgid "Status"
msgstr "状态"

#: admin/field-groups.php:616 admin/settings-info.php:76
#: pro/admin/views/settings-updates.php:111
msgid "Changelog"
msgstr "更新日志"

#: admin/field-groups.php:617
msgid "See what's new in"
msgstr "查看更新内容于"

#: admin/field-groups.php:617
msgid "version"
msgstr "版本"

#: admin/field-groups.php:619
msgid "Resources"
msgstr "资源"

#: admin/field-groups.php:621
msgid "Getting Started"
msgstr "起步"

#: admin/field-groups.php:622 pro/admin/settings-updates.php:73
#: pro/admin/views/settings-updates.php:17
msgid "Updates"
msgstr "更新"

#: admin/field-groups.php:623
msgid "Field Types"
msgstr "字段类型"

#: admin/field-groups.php:624
msgid "Functions"
msgstr "功能"

#: admin/field-groups.php:625
msgid "Actions"
msgstr "操作"

#: admin/field-groups.php:626 fields/relationship.php:718
msgid "Filters"
msgstr "过滤器"

#: admin/field-groups.php:627
msgid "'How to' guides"
msgstr "新手向导"

#: admin/field-groups.php:628
msgid "Tutorials"
msgstr "教程"

#: admin/field-groups.php:633
msgid "Created by"
msgstr "创建者"

#: admin/field-groups.php:673
msgid "Duplicate this item"
msgstr "复制此项"

#: admin/field-groups.php:673 admin/field-groups.php:685
#: admin/views/field-group-field.php:58 pro/fields/flexible-content.php:516
msgid "Duplicate"
msgstr "复制"

#: admin/field-groups.php:724
#, php-format
msgid "Select %s"
msgstr "选择 %s"

#: admin/field-groups.php:730
msgid "Synchronise field group"
msgstr "同步字段组"

#: admin/field-groups.php:730 admin/field-groups.php:750
msgid "Sync"
msgstr "同步"

#: admin/settings-addons.php:51 admin/views/settings-addons.php:9
msgid "Add-ons"
msgstr "附加功能"

#: admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>错误</b>，无法加载扩展列表"

#: admin/settings-info.php:50
msgid "Info"
msgstr "信息"

#: admin/settings-info.php:75
msgid "What's New"
msgstr "更新日志"

#: admin/settings-tools.php:54 admin/views/settings-tools-export.php:9
#: admin/views/settings-tools.php:31
msgid "Tools"
msgstr "工具"

#: admin/settings-tools.php:151 admin/settings-tools.php:365
msgid "No field groups selected"
msgstr "没选择字段组"

#: admin/settings-tools.php:188
msgid "No file selected"
msgstr "没选择文件"

#: admin/settings-tools.php:201
msgid "Error uploading file. Please try again"
msgstr "文件上传失败，请重试"

#: admin/settings-tools.php:210
msgid "Incorrect file type"
msgstr "文本类型不对"

#: admin/settings-tools.php:227
msgid "Import file empty"
msgstr "导入的文件是空白的"

#: admin/settings-tools.php:323
#, php-format
msgid "<b>Success</b>. Import tool added %s field groups: %s"
msgstr "<b>成功</b>，导入工具添加了 %s 字段组： %s"

#: admin/settings-tools.php:332
#, php-format
msgid ""
"<b>Warning</b>. Import tool detected %s field groups already exist and have "
"been ignored: %s"
msgstr "<b>警告</b>，导入工具检测到 %s 字段组已经存在了。忽略的字段组：%s"

#: admin/update.php:113
msgid "Upgrade ACF"
msgstr "升级 ACF"

#: admin/update.php:143
msgid "Review sites & upgrade"
msgstr "检查网站并升级"

#: admin/update.php:298
msgid "Upgrade"
msgstr "升级"

#: admin/update.php:328
msgid "Upgrade Database"
msgstr "升级数据库"

#: admin/views/field-group-field-conditional-logic.php:29
msgid "Conditional Logic"
msgstr "条件逻辑"

#: admin/views/field-group-field-conditional-logic.php:40
#: admin/views/field-group-field.php:137 fields/checkbox.php:246
#: fields/message.php:117 fields/page_link.php:568 fields/page_link.php:582
#: fields/post_object.php:434 fields/post_object.php:448 fields/select.php:411
#: fields/select.php:425 fields/select.php:439 fields/select.php:453
#: fields/tab.php:172 fields/taxonomy.php:770 fields/taxonomy.php:784
#: fields/taxonomy.php:798 fields/taxonomy.php:812 fields/user.php:457
#: fields/user.php:471 fields/wysiwyg.php:384
#: pro/admin/views/settings-updates.php:93
msgid "Yes"
msgstr "是"

#: admin/views/field-group-field-conditional-logic.php:41
#: admin/views/field-group-field.php:138 fields/checkbox.php:247
#: fields/message.php:118 fields/page_link.php:569 fields/page_link.php:583
#: fields/post_object.php:435 fields/post_object.php:449 fields/select.php:412
#: fields/select.php:426 fields/select.php:440 fields/select.php:454
#: fields/tab.php:173 fields/taxonomy.php:685 fields/taxonomy.php:771
#: fields/taxonomy.php:785 fields/taxonomy.php:799 fields/taxonomy.php:813
#: fields/user.php:458 fields/user.php:472 fields/wysiwyg.php:385
#: pro/admin/views/settings-updates.php:103
msgid "No"
msgstr "否"

#: admin/views/field-group-field-conditional-logic.php:65
msgid "Show this field if"
msgstr "显示此字段的条件"

#: admin/views/field-group-field-conditional-logic.php:111
#: admin/views/field-group-locations.php:88
msgid "is equal to"
msgstr "等于"

#: admin/views/field-group-field-conditional-logic.php:112
#: admin/views/field-group-locations.php:89
msgid "is not equal to"
msgstr "不等于"

#: admin/views/field-group-field-conditional-logic.php:149
#: admin/views/field-group-locations.php:118
msgid "and"
msgstr "与"

#: admin/views/field-group-field-conditional-logic.php:164
#: admin/views/field-group-locations.php:133
msgid "Add rule group"
msgstr "添加规则组"

#: admin/views/field-group-field.php:54 admin/views/field-group-field.php:57
msgid "Edit field"
msgstr "编辑字段"

#: admin/views/field-group-field.php:57 pro/fields/gallery.php:355
msgid "Edit"
msgstr "编辑"

#: admin/views/field-group-field.php:58
msgid "Duplicate field"
msgstr "复制字段"

#: admin/views/field-group-field.php:59
msgid "Move field to another group"
msgstr "把字段移动到其它群组"

#: admin/views/field-group-field.php:59
msgid "Move"
msgstr "移动"

#: admin/views/field-group-field.php:60
msgid "Delete field"
msgstr "删除字段"

#: admin/views/field-group-field.php:60 pro/fields/flexible-content.php:515
msgid "Delete"
msgstr "删除"

#: admin/views/field-group-field.php:68 fields/oembed.php:212
#: fields/taxonomy.php:886
msgid "Error"
msgstr "错误"

#: fields/oembed.php:220 fields/taxonomy.php:900
msgid "Error."
msgstr "错误。"

#: admin/views/field-group-field.php:68
msgid "Field type does not exist"
msgstr "字段类型不存在"

#: admin/views/field-group-field.php:81
msgid "Field Label"
msgstr "字段标签"

#: admin/views/field-group-field.php:82
msgid "This is the name which will appear on the EDIT page"
msgstr "在编辑界面显示的名字"

#: admin/views/field-group-field.php:93
msgid "Field Name"
msgstr "字段名称"

#: admin/views/field-group-field.php:94
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "单个字符串，不能有空格，可以用横线或下画线。"

#: admin/views/field-group-field.php:105
msgid "Field Type"
msgstr "字段类型"

#: admin/views/field-group-field.php:118 fields/tab.php:143
msgid "Instructions"
msgstr "说明"

#: admin/views/field-group-field.php:119
msgid "Instructions for authors. Shown when submitting data"
msgstr "显示给内容作者的说明文字，在提交数据时显示"

#: admin/views/field-group-field.php:130
msgid "Required?"
msgstr "必填？"

#: admin/views/field-group-field.php:158
msgid "Wrapper Attributes"
msgstr "包装属性"

#: admin/views/field-group-field.php:164
msgid "width"
msgstr "宽度"

#: admin/views/field-group-field.php:178
msgid "class"
msgstr "class"

#: admin/views/field-group-field.php:191
msgid "id"
msgstr "id"

#: admin/views/field-group-field.php:203
msgid "Close Field"
msgstr "关闭字段"

#: admin/views/field-group-fields.php:29
msgid "Order"
msgstr "序号"

#: admin/views/field-group-fields.php:30 pro/fields/flexible-content.php:541
msgid "Label"
msgstr "标签"

#: admin/views/field-group-fields.php:31 pro/fields/flexible-content.php:554
msgid "Name"
msgstr "名称"

#: admin/views/field-group-fields.php:32
msgid "Type"
msgstr "类型"

#: admin/views/field-group-fields.php:44
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr "没有字段，点击<strong>添加</strong>按钮创建第一个字段。"

#: admin/views/field-group-fields.php:51
msgid "Drag and drop to reorder"
msgstr "拖拽排序"

#: admin/views/field-group-fields.php:54
msgid "+ Add Field"
msgstr "+ 添加字段"

#: admin/views/field-group-locations.php:5
msgid "Rules"
msgstr "规则"

#: admin/views/field-group-locations.php:6
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "创建一组规则以确定自定义字段在哪个编辑界面上显示"

#: admin/views/field-group-locations.php:21
msgid "Show this field group if"
msgstr "显示此字段组的条件"

#: admin/views/field-group-locations.php:41
#: admin/views/field-group-locations.php:47
msgid "Post"
msgstr "内容"

#: admin/views/field-group-locations.php:42 fields/relationship.php:724
msgid "Post Type"
msgstr "内容类型"

#: admin/views/field-group-locations.php:43
msgid "Post Status"
msgstr "内容状态"

#: admin/views/field-group-locations.php:44
msgid "Post Format"
msgstr "内容格式"

#: admin/views/field-group-locations.php:45
msgid "Post Category"
msgstr "内容类别"

#: admin/views/field-group-locations.php:46
msgid "Post Taxonomy"
msgstr "内容分类法"

#: admin/views/field-group-locations.php:49
#: admin/views/field-group-locations.php:53
msgid "Page"
msgstr "页面"

#: admin/views/field-group-locations.php:50
msgid "Page Template"
msgstr "页面模板"

#: admin/views/field-group-locations.php:51
msgid "Page Type"
msgstr "页面类型"

#: admin/views/field-group-locations.php:52
msgid "Page Parent"
msgstr "父级页面"

#: admin/views/field-group-locations.php:55 fields/user.php:36
msgid "User"
msgstr "用户"

#: admin/views/field-group-locations.php:56
msgid "Current User"
msgstr "当前用户"

#: admin/views/field-group-locations.php:57
msgid "Current User Role"
msgstr "当前用户角色"

#: admin/views/field-group-locations.php:58
msgid "User Form"
msgstr "用户表单"

#: admin/views/field-group-locations.php:59
msgid "User Role"
msgstr "用户角色"

#: admin/views/field-group-locations.php:61 pro/admin/options-page.php:48
msgid "Forms"
msgstr "表单"

#: admin/views/field-group-locations.php:62
msgid "Attachment"
msgstr "附件"

#: admin/views/field-group-locations.php:63
msgid "Taxonomy Term"
msgstr "分类词汇"

#: admin/views/field-group-locations.php:64
msgid "Comment"
msgstr "评论"

#: admin/views/field-group-locations.php:65
msgid "Widget"
msgstr "小工具"

#: admin/views/field-group-options.php:25
msgid "Style"
msgstr "样式"

#: admin/views/field-group-options.php:32
msgid "Standard (WP metabox)"
msgstr "标准（WP Metabox）"

#: admin/views/field-group-options.php:33
msgid "Seamless (no metabox)"
msgstr "无缝（无 metabox）"

#: admin/views/field-group-options.php:40
msgid "Position"
msgstr "位置"

#: admin/views/field-group-options.php:47
msgid "High (after title)"
msgstr "高（标题之后）"

#: admin/views/field-group-options.php:48
msgid "Normal (after content)"
msgstr "正常（内容之后）"

#: admin/views/field-group-options.php:49
msgid "Side"
msgstr "边栏"

#: admin/views/field-group-options.php:57
msgid "Label placement"
msgstr "标签位置"

#: admin/views/field-group-options.php:64 fields/tab.php:159
msgid "Top aligned"
msgstr "顶部对齐"

#: admin/views/field-group-options.php:65 fields/tab.php:160
msgid "Left aligned"
msgstr "左对齐"

#: admin/views/field-group-options.php:72
msgid "Instruction placement"
msgstr "说明位置"

#: admin/views/field-group-options.php:79
msgid "Below labels"
msgstr "标签之下"

#: admin/views/field-group-options.php:80
msgid "Below fields"
msgstr "字段之下"

#: admin/views/field-group-options.php:87
msgid "Order No."
msgstr "序号"

#: admin/views/field-group-options.php:88
msgid "Field groups with a lower order will appear first"
msgstr "序号小的字段组会排在最前面"

#: admin/views/field-group-options.php:99
msgid "Shown in field group list"
msgstr "在字段组列表中显示"

#: admin/views/field-group-options.php:109
msgid "Hide on screen"
msgstr "隐藏元素"

#: admin/views/field-group-options.php:110
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>选择</b>需要在编辑界面<b>隐藏</b>的条目。 "

#: admin/views/field-group-options.php:110
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"如果多个字段组同时出现在编辑界面，会使用第一个字段组里的选项（就是序号最小的"
"那个字段组）"

#: admin/views/field-group-options.php:117
msgid "Permalink"
msgstr "固定链接"

#: admin/views/field-group-options.php:118
msgid "Content Editor"
msgstr "内容编辑器"

#: admin/views/field-group-options.php:119
msgid "Excerpt"
msgstr "摘要"

#: admin/views/field-group-options.php:121
msgid "Discussion"
msgstr "讨论"

#: admin/views/field-group-options.php:122
msgid "Comments"
msgstr "评论"

#: admin/views/field-group-options.php:123
msgid "Revisions"
msgstr "修订"

#: admin/views/field-group-options.php:124
msgid "Slug"
msgstr "别名"

#: admin/views/field-group-options.php:125
msgid "Author"
msgstr "作者"

#: admin/views/field-group-options.php:126
msgid "Format"
msgstr "格式"

#: admin/views/field-group-options.php:127
msgid "Page Attributes"
msgstr "页面属性"

#: admin/views/field-group-options.php:128 fields/relationship.php:737
msgid "Featured Image"
msgstr "特色图像"

#: admin/views/field-group-options.php:129
msgid "Categories"
msgstr "类别"

#: admin/views/field-group-options.php:130
msgid "Tags"
msgstr "标签"

#: admin/views/field-group-options.php:131
msgid "Send Trackbacks"
msgstr "发送 Trackbacks"

#: admin/views/settings-addons.php:23
msgid "Download & Install"
msgstr "下载并安装"

#: admin/views/settings-addons.php:42
msgid "Installed"
msgstr "已安装"

#: admin/views/settings-info.php:9
msgid "Welcome to Advanced Custom Fields"
msgstr "欢迎使用高级自定义字段"

#: admin/views/settings-info.php:10
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr "感谢升级到更好的 ACF %s，你会喜欢上它的。"

#: admin/views/settings-info.php:23
msgid "A smoother custom field experience"
msgstr "平滑的自定义字段体验"

#: admin/views/settings-info.php:28
msgid "Improved Usability"
msgstr "改善用户体验"

#: admin/views/settings-info.php:29
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Select2 这个库，改善了内容对象，分类法，选择列表等字段的用户体验与速度。"

#: admin/views/settings-info.php:33
msgid "Improved Design"
msgstr "改善的设计"

#: admin/views/settings-info.php:34
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr "很多字段变漂亮了，比如相册，关系，oEmbed 。"

#: admin/views/settings-info.php:38
msgid "Improved Data"
msgstr "改善的数据"

#: admin/views/settings-info.php:39
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"重新设计了数据结构，让子字段独立于它的爸爸。这样我们可以把字段放到父字段里，"
"也可以从父字段里拿出来。"

#: admin/views/settings-info.php:45
msgid "Goodbye Add-ons. Hello PRO"
msgstr "再见了扩展，欢迎专业版"

#: admin/views/settings-info.php:50
msgid "Introducing ACF PRO"
msgstr "ACF 专业版介绍"

#: admin/views/settings-info.php:51
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "我们改进了为您提供高级功能的方法。"

#: admin/views/settings-info.php:52
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"之前的 4 个高级功能扩展现在被组合成了一个新的 <a href=\"%s\">ACF 专业版</a>。"
"许可证分为两种，个人与开发者，现在这些高级功能更实惠也更易用。"

#: admin/views/settings-info.php:56
msgid "Powerful Features"
msgstr "强大的功能"

#: admin/views/settings-info.php:57
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF 专业版有重复数据，弹性内容布局，相册功能，还可以创建页面的管理选项。"

#: admin/views/settings-info.php:58
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "了解更多关于 <a href=\"%s\">ACF PRO 的功能</a>。"

#: admin/views/settings-info.php:62
msgid "Easy Upgrading"
msgstr "便捷的升级"

#: admin/views/settings-info.php:63
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr "<a href=\"%s\">登录到商店帐户</a>，可以方便以后升级。"

#: admin/views/settings-info.php:64
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"阅读 <a href=\"%s\">升级手册</a>，需要帮助请联系 <a href=\"%s\">客服</a>"

#: admin/views/settings-info.php:72
msgid "Under the Hood"
msgstr "工作原理"

#: admin/views/settings-info.php:77
msgid "Smarter field settings"
msgstr "更聪明的字段设置"

#: admin/views/settings-info.php:78
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF 现在用单独的内容对象字段设置"

#: admin/views/settings-info.php:82
msgid "More AJAX"
msgstr "更多 AJAX"

#: admin/views/settings-info.php:83
msgid "More fields use AJAX powered search to speed up page loading"
msgstr "更多字段使用 AJAX 搜索，这让页面加载速度更快"

#: admin/views/settings-info.php:87
msgid "Local JSON"
msgstr "本地 JSON"

#: admin/views/settings-info.php:88
msgid "New auto export to JSON feature improves speed"
msgstr "改进了新的自动导出 JSON 功能的速度"

#: admin/views/settings-info.php:94
msgid "Better version control"
msgstr "更好的版本控制"

#: admin/views/settings-info.php:95
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr "新的自动 JSON 导出功能让字段设置可以包含在版本控制里"

#: admin/views/settings-info.php:99
msgid "Swapped XML for JSON"
msgstr "用 JSON 替代 XML"

#: admin/views/settings-info.php:100
msgid "Import / Export now uses JSON in favour of XML"
msgstr "导入 / 导出现在用 JSON 代替以前的 XML"

#: admin/views/settings-info.php:104
msgid "New Forms"
msgstr "新表单"

#: admin/views/settings-info.php:105
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr "字段现在可以用在评论，小工具还有所有的用户表单上。"

#: admin/views/settings-info.php:112
msgid "A new field for embedding content has been added"
msgstr "新添加了一个嵌入内容用的字段"

#: admin/views/settings-info.php:116
msgid "New Gallery"
msgstr "新相册"

#: admin/views/settings-info.php:117
msgid "The gallery field has undergone a much needed facelift"
msgstr "改进了相册字段的显示"

#: admin/views/settings-info.php:121
msgid "New Settings"
msgstr "新设置"

#: admin/views/settings-info.php:122
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr "字段组设置添加了标签位置与介绍位置"

#: admin/views/settings-info.php:128
msgid "Better Front End Forms"
msgstr "更好的前端表单"

#: admin/views/settings-info.php:129
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() 现在可以在提交的时候创建新的内容"

#: admin/views/settings-info.php:133
msgid "Better Validation"
msgstr "更好的验证方式"

#: admin/views/settings-info.php:134
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "表单验证现在使用 PHP + AJAX 的方式"

#: admin/views/settings-info.php:138
msgid "Relationship Field"
msgstr "关系字段"

#: admin/views/settings-info.php:139
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr "新的用来过滤的关系字段设置（搜索，内容类型，分类法）"

#: admin/views/settings-info.php:145
msgid "Moving Fields"
msgstr "移动字段"

#: admin/views/settings-info.php:146
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr "新的字段组功能可以让我们在群组与爸爸之间移动字段"

#: admin/views/settings-info.php:150 fields/page_link.php:36
msgid "Page Link"
msgstr "页面链接"

#: admin/views/settings-info.php:151
msgid "New archives group in page_link field selection"
msgstr "在 page_link 字段选择里的新的存档群组"

#: admin/views/settings-info.php:155
msgid "Better Options Pages"
msgstr "选项页面"

#: admin/views/settings-info.php:156
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr "选项页面的新功能，可以让你同时创建父菜单与子菜单页面"

#: admin/views/settings-info.php:165
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "你会喜欢在 %s 里做的修改。"

#: admin/views/settings-tools-export.php:13
msgid "Export Field Groups to PHP"
msgstr "导出字段组到PHP"

#: admin/views/settings-tools-export.php:17
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"下面的代码可以用来创建一个本地版本的所选字段组。本地字段组加载更快，可以版本"
"控制。你可以把下面这些代码放在你的主题的 functions.php 文件里。"

#: admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "选择字段组"

#: admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "导出字段组"

#: admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"选择你想导出的字段组，然后选择导出的方法。使用 <b>下载</b> 按钮可以导出一个 ."
"json 文件，你可以在其它的网站里导入它。使用 <b>生成</b> 按钮可以导出 PHP 代"
"码，这些代码可以放在你的主题或插件里。"

#: admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "下载导出文件"

#: admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "生成导出代码"

#: admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "导入字段组"

#: admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"选择你想导入的 Advanced Custom Fields JSON 文件，然后点击 <b>导入</b> 按钮可"
"以导入 JSON 文件里定义的字段组。"

#: admin/views/settings-tools.php:77 fields/file.php:46
msgid "Select File"
msgstr "选择文件"

#: admin/views/settings-tools.php:86
msgid "Import"
msgstr "导入"

#: admin/views/update-network.php:8 admin/views/update.php:8
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Advanced Custom Fields 数据库升级"

#: admin/views/update-network.php:10
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click “Upgrade Database”."
msgstr "下面的网站需要升级数据库，点击 “升级数据库” 。"

#: admin/views/update-network.php:19 admin/views/update-network.php:27
msgid "Site"
msgstr "网站"

#: admin/views/update-network.php:47
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "网站需要从  %s 升级到 %s"

#: admin/views/update-network.php:49
msgid "Site is up to date"
msgstr "网站已是最新版"

#: admin/views/update-network.php:62 admin/views/update.php:16
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr "数据库升级完成，<a href=\"%s\">返回网络面板</a>"

#: admin/views/update-network.php:101 admin/views/update-notice.php:35
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr "升级前最好先备份一下。确定现在升级吗？"

#: admin/views/update-network.php:157
msgid "Upgrade complete"
msgstr "升级完成"

#: admin/views/update-network.php:161
msgid "Upgrading data to"
msgstr "升级数据到"

#: admin/views/update-notice.php:23
msgid "Database Upgrade Required"
msgstr "需要升级数据库"

#: admin/views/update-notice.php:25
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "感谢升级 %s v%s!"

#: admin/views/update-notice.php:25
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr "先把数据库更新到最新版。"

#: admin/views/update.php:12
msgid "Reading upgrade tasks..."
msgstr "阅读更新任务..."

#: admin/views/update.php:14
#, php-format
msgid "Upgrading data to version %s"
msgstr "升级数据到 %s 版本"

#: admin/views/update.php:16
msgid "See what's new"
msgstr "查看更新"

#: admin/views/update.php:110
msgid "No updates available."
msgstr "没有可用更新。"

#: api/api-helpers.php:821
msgid "Thumbnail"
msgstr "缩略图"

#: api/api-helpers.php:822
msgid "Medium"
msgstr "中"

#: api/api-helpers.php:823
msgid "Large"
msgstr "大"

#: api/api-helpers.php:871
msgid "Full Size"
msgstr "原图"

#: api/api-helpers.php:1581
msgid "(no title)"
msgstr "(无标题)"

#: api/api-helpers.php:3183
#, php-format
msgid "Image width must be at least %dpx."
msgstr "图像宽度至少得是 %dpx。"

#: api/api-helpers.php:3188
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "图像宽度最大不能超过 %dpx。"

#: api/api-helpers.php:3204
#, php-format
msgid "Image height must be at least %dpx."
msgstr "图像高度至少得是 %dpx。"

#: api/api-helpers.php:3209
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "图像高度最大不能超过 %dpx。"

#: api/api-helpers.php:3227
#, php-format
msgid "File size must be at least %s."
msgstr "文件尺寸至少得是 %s。"

#: api/api-helpers.php:3232
#, php-format
msgid "File size must must not exceed %s."
msgstr "文件尺寸最大不能超过 %s。"

#: api/api-helpers.php:3266
#, php-format
msgid "File type must be %s."
msgstr "字段类型必须是 %s。"

#: api/api-template.php:1289 pro/fields/gallery.php:564
msgid "Update"
msgstr "更新"

#: api/api-template.php:1290
msgid "Post updated"
msgstr "内容已更新"

#: core/field.php:131
msgid "Basic"
msgstr "基本"

#: core/field.php:132
msgid "Content"
msgstr "内容"

#: core/field.php:133
msgid "Choice"
msgstr "选项"

#: core/field.php:134
msgid "Relational"
msgstr "关系"

#: core/field.php:135
msgid "jQuery"
msgstr "jQuery"

#: core/field.php:136 fields/checkbox.php:226 fields/radio.php:231
#: pro/fields/flexible-content.php:512 pro/fields/repeater.php:392
msgid "Layout"
msgstr "样式"

#: core/input.php:129
msgid "Expand Details"
msgstr "展开"

#: core/input.php:130
msgid "Collapse Details"
msgstr "折叠"

#: core/input.php:131
msgid "Validation successful"
msgstr "验证成功"

#: core/input.php:132
msgid "Validation failed"
msgstr "验证失败"

#: core/input.php:133
msgid "1 field requires attention"
msgstr "1 个字段需要注意"

#: core/input.php:134
#, php-format
msgid "%d fields require attention"
msgstr "%d 个字段需要注意"

#: core/input.php:135
msgid "Restricted"
msgstr "限制"

#: core/input.php:533
#, php-format
msgid "%s value is required"
msgstr "%s 的值是必填项"

#: fields/checkbox.php:36 fields/taxonomy.php:752
msgid "Checkbox"
msgstr "复选框"

#: fields/checkbox.php:144
msgid "Toggle All"
msgstr "切换所有"

#: fields/checkbox.php:208 fields/radio.php:193 fields/select.php:388
msgid "Choices"
msgstr "选项"

#: fields/checkbox.php:209 fields/radio.php:194 fields/select.php:389
msgid "Enter each choice on a new line."
msgstr "输入选项，每行一个"

#: fields/checkbox.php:209 fields/radio.php:194 fields/select.php:389
msgid "For more control, you may specify both a value and label like this:"
msgstr "如果需要更多控制，你按照一下格式，定义一个值和标签对："

#: fields/checkbox.php:209 fields/radio.php:194 fields/select.php:389
msgid "red : Red"
msgstr " red : Red "

#: fields/checkbox.php:217 fields/color_picker.php:158 fields/email.php:124
#: fields/number.php:150 fields/radio.php:222 fields/select.php:397
#: fields/text.php:148 fields/textarea.php:145 fields/true_false.php:115
#: fields/url.php:117 fields/wysiwyg.php:345
msgid "Default Value"
msgstr "默认值"

#: fields/checkbox.php:218 fields/select.php:398
msgid "Enter each default value on a new line"
msgstr "每行输入一个默认值"

#: fields/checkbox.php:232 fields/radio.php:237
msgid "Vertical"
msgstr "垂直"

#: fields/checkbox.php:233 fields/radio.php:238
msgid "Horizontal"
msgstr "水平"

#: fields/checkbox.php:240
msgid "Toggle"
msgstr "切换"

#: fields/checkbox.php:241
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "添加一个可以切换所有选择的复选框"

#: fields/color_picker.php:36
msgid "Color Picker"
msgstr "颜色选择"

#: fields/color_picker.php:94
msgid "Clear"
msgstr "清除"

#: fields/color_picker.php:95
msgid "Default"
msgstr "默认"

#: fields/color_picker.php:96
msgid "Select Color"
msgstr "选择颜色"

#: fields/date_picker.php:36
msgid "Date Picker"
msgstr "日期选择"

#: fields/date_picker.php:72
msgid "Done"
msgstr "完成"

#: fields/date_picker.php:73
msgid "Today"
msgstr "今天"

#: fields/date_picker.php:76
msgid "Show a different month"
msgstr "显示其他月份"

#: fields/date_picker.php:149
msgid "Display Format"
msgstr "显示格式"

#: fields/date_picker.php:150
msgid "The format displayed when editing a post"
msgstr "编辑内容的时候显示的格式"

#: fields/date_picker.php:164
msgid "Return format"
msgstr "返回格式"

#: fields/date_picker.php:165
msgid "The format returned via template functions"
msgstr "通过模板函数返回的格式"

#: fields/date_picker.php:180
msgid "Week Starts On"
msgstr "每周开始于"

#: fields/email.php:36
msgid "Email"
msgstr "电子邮件"

#: fields/email.php:125 fields/number.php:151 fields/radio.php:223
#: fields/text.php:149 fields/textarea.php:146 fields/url.php:118
#: fields/wysiwyg.php:346
msgid "Appears when creating a new post"
msgstr "创建新内容的时候显示"

#: fields/email.php:133 fields/number.php:159 fields/password.php:137
#: fields/text.php:157 fields/textarea.php:154 fields/url.php:126
msgid "Placeholder Text"
msgstr "点位符文本"

#: fields/email.php:134 fields/number.php:160 fields/password.php:138
#: fields/text.php:158 fields/textarea.php:155 fields/url.php:127
msgid "Appears within the input"
msgstr "在 input 内部显示"

#: fields/email.php:142 fields/number.php:168 fields/password.php:146
#: fields/text.php:166
msgid "Prepend"
msgstr "前置"

#: fields/email.php:143 fields/number.php:169 fields/password.php:147
#: fields/text.php:167
msgid "Appears before the input"
msgstr "在 input 前面显示"

#: fields/email.php:151 fields/number.php:177 fields/password.php:155
#: fields/text.php:175
msgid "Append"
msgstr "追加"

#: fields/email.php:152 fields/number.php:178 fields/password.php:156
#: fields/text.php:176
msgid "Appears after the input"
msgstr "在 input 后面显示"

#: fields/file.php:36
msgid "File"
msgstr "文件"

#: fields/file.php:47
msgid "Edit File"
msgstr "编辑文件"

#: fields/file.php:48
msgid "Update File"
msgstr "更新文件"

#: fields/file.php:49 pro/fields/gallery.php:55
msgid "uploaded to this post"
msgstr "上传到这个内容"

#: fields/file.php:142
msgid "File Name"
msgstr "文件名"

#: fields/file.php:146
msgid "File Size"
msgstr "文件尺寸"

#: fields/file.php:169
msgid "No File selected"
msgstr "没有选择文件"

#: fields/file.php:169
msgid "Add File"
msgstr "添加文件"

#: fields/file.php:214 fields/image.php:195 fields/taxonomy.php:821
msgid "Return Value"
msgstr "返回值"

#: fields/file.php:215 fields/image.php:196
msgid "Specify the returned value on front end"
msgstr "指定前端返回的值"

#: fields/file.php:220
msgid "File Array"
msgstr "文件数组"

#: fields/file.php:221
msgid "File URL"
msgstr "文件URL"

#: fields/file.php:222
msgid "File ID"
msgstr "文件ID"

#: fields/file.php:229 fields/image.php:220 pro/fields/gallery.php:647
msgid "Library"
msgstr "库"

#: fields/file.php:230 fields/image.php:221 pro/fields/gallery.php:648
msgid "Limit the media library choice"
msgstr "限制媒体库的选择"

#: fields/file.php:236 fields/image.php:227 pro/fields/gallery.php:654
msgid "Uploaded to post"
msgstr "上传到内容"

#: fields/file.php:243 fields/image.php:234 pro/fields/gallery.php:661
msgid "Minimum"
msgstr "最小"

#: fields/file.php:244 fields/file.php:255
msgid "Restrict which files can be uploaded"
msgstr "限制什么类型的文件可以上传"

#: fields/file.php:247 fields/file.php:258 fields/image.php:257
#: fields/image.php:290 pro/fields/gallery.php:684 pro/fields/gallery.php:717
msgid "File size"
msgstr "文件尺寸"

#: fields/file.php:254 fields/image.php:267 pro/fields/gallery.php:694
msgid "Maximum"
msgstr "最大"

#: fields/file.php:265 fields/image.php:300 pro/fields/gallery.php:727
msgid "Allowed file types"
msgstr "允许的文字类型"

#: fields/file.php:266 fields/image.php:301 pro/fields/gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr "用英文逗号分隔开，留空则为全部类型"

#: fields/google-map.php:36
msgid "Google Map"
msgstr "谷歌地图"

#: fields/google-map.php:51
msgid "Locating"
msgstr "定位"

#: fields/google-map.php:52
msgid "Sorry, this browser does not support geolocation"
msgstr "抱歉，浏览器不支持定位"

#: fields/google-map.php:135
msgid "Clear location"
msgstr "清除位置"

#: fields/google-map.php:140
msgid "Find current location"
msgstr "搜索当前位置"

#: fields/google-map.php:141
msgid "Search for address..."
msgstr "搜索地址... "

#: fields/google-map.php:173 fields/google-map.php:184
msgid "Center"
msgstr "居中"

#: fields/google-map.php:174 fields/google-map.php:185
msgid "Center the initial map"
msgstr "居中显示初始地图"

#: fields/google-map.php:198
msgid "Zoom"
msgstr "缩放"

#: fields/google-map.php:199
msgid "Set the initial zoom level"
msgstr "设置初始缩放级别"

#: fields/google-map.php:208 fields/image.php:246 fields/image.php:279
#: fields/oembed.php:262 pro/fields/gallery.php:673 pro/fields/gallery.php:706
msgid "Height"
msgstr "高度"

#: fields/google-map.php:209
msgid "Customise the map height"
msgstr "自定义地图高度"

#: fields/image.php:36
msgid "Image"
msgstr "图像"

#: fields/image.php:51
msgid "Select Image"
msgstr "选择图像"

#: fields/image.php:52 pro/fields/gallery.php:53
msgid "Edit Image"
msgstr "编辑图片"

#: fields/image.php:53 pro/fields/gallery.php:54
msgid "Update Image"
msgstr "更新图像"

#: fields/image.php:54
msgid "Uploaded to this post"
msgstr "上传到这个内容"

#: fields/image.php:55
msgid "All images"
msgstr "所有图片"

#: fields/image.php:147
msgid "No image selected"
msgstr "没有选择图片"

#: fields/image.php:147
msgid "Add Image"
msgstr "添加图片"

#: fields/image.php:201
msgid "Image Array"
msgstr "图像数组"

#: fields/image.php:202
msgid "Image URL"
msgstr "图像 URL"

#: fields/image.php:203
msgid "Image ID"
msgstr "图像ID"

#: fields/image.php:210 pro/fields/gallery.php:637
msgid "Preview Size"
msgstr "预览图大小"

#: fields/image.php:211 pro/fields/gallery.php:638
msgid "Shown when entering data"
msgstr "输入数据时显示"

#: fields/image.php:235 fields/image.php:268 pro/fields/gallery.php:662
#: pro/fields/gallery.php:695
msgid "Restrict which images can be uploaded"
msgstr "限制可以上传的图像"

#: fields/image.php:238 fields/image.php:271 fields/oembed.php:251
#: pro/fields/gallery.php:665 pro/fields/gallery.php:698
msgid "Width"
msgstr "宽度"

#: fields/message.php:36 fields/message.php:103 fields/true_false.php:106
msgid "Message"
msgstr "消息"

#: fields/message.php:104
msgid "Please note that all text will first be passed through the wp function "
msgstr "请注意，所有文本将首页通过WP过滤功能"

#: fields/message.php:112
msgid "Escape HTML"
msgstr "转义 HTML"

#: fields/message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "显示 HTML 文本，而不是渲染 HTML"

#: fields/number.php:36
msgid "Number"
msgstr "号码"

#: fields/number.php:186
msgid "Minimum Value"
msgstr "最小值"

#: fields/number.php:195
msgid "Maximum Value"
msgstr "最大值"

#: fields/number.php:204
msgid "Step Size"
msgstr "步长"

#: fields/number.php:242
msgid "Value must be a number"
msgstr "值必须是数字"

#: fields/number.php:260
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "值要大于等于 %d"

#: fields/number.php:268
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "值要小于等于 %d"

#: fields/oembed.php:36
msgid "oEmbed"
msgstr "oEmbed"

#: fields/oembed.php:199
msgid "Enter URL"
msgstr "输入 URL"

#: fields/oembed.php:212
msgid "No embed found for the given URL."
msgstr "在 URL 里没发现嵌入。"

#: fields/oembed.php:248 fields/oembed.php:259
msgid "Embed Size"
msgstr "嵌入尺寸"

#: fields/page_link.php:206
msgid "Archives"
msgstr "存档"

#: fields/page_link.php:535 fields/post_object.php:401
#: fields/relationship.php:690
msgid "Filter by Post Type"
msgstr "按内容类型筛选"

#: fields/page_link.php:543 fields/post_object.php:409
#: fields/relationship.php:698
msgid "All post types"
msgstr "所有内容类型"

#: fields/page_link.php:549 fields/post_object.php:415
#: fields/relationship.php:704
msgid "Filter by Taxonomy"
msgstr "按分类筛选"

#: fields/page_link.php:557 fields/post_object.php:423
#: fields/relationship.php:712
msgid "All taxonomies"
msgstr "所有分类法"

#: fields/page_link.php:563 fields/post_object.php:429 fields/select.php:406
#: fields/taxonomy.php:765 fields/user.php:452
msgid "Allow Null?"
msgstr "是否允许空值？"

#: fields/page_link.php:577 fields/post_object.php:443 fields/select.php:420
#: fields/user.php:466
msgid "Select multiple values?"
msgstr "是否选择多个值？"

#: fields/password.php:36
msgid "Password"
msgstr "密码"

#: fields/post_object.php:36 fields/post_object.php:462
#: fields/relationship.php:769
msgid "Post Object"
msgstr "文章对象"

#: fields/post_object.php:457 fields/relationship.php:764
msgid "Return Format"
msgstr "返回格式"

#: fields/post_object.php:463 fields/relationship.php:770
msgid "Post ID"
msgstr "Post ID"

#: fields/radio.php:36
msgid "Radio Button"
msgstr "单选按钮"

#: fields/radio.php:202
msgid "Other"
msgstr "其他"

#: fields/radio.php:206
msgid "Add 'other' choice to allow for custom values"
msgstr "为自定义值添加 'other' 选择"

#: fields/radio.php:212
msgid "Save Other"
msgstr "保存其它"

#: fields/radio.php:216
msgid "Save 'other' values to the field's choices"
msgstr "存档为字段的选择的 'other' 的值"

#: fields/relationship.php:36
msgid "Relationship"
msgstr "关系"

#: fields/relationship.php:48
msgid "Minimum values reached ( {min} values )"
msgstr "已到最小值 ( {min} values )"

#: fields/relationship.php:49
msgid "Maximum values reached ( {max} values )"
msgstr "达到了最大值 ( {max} 值 ) "

#: fields/relationship.php:50
msgid "Loading"
msgstr "加载"

#: fields/relationship.php:51
msgid "No matches found"
msgstr "没找到匹配的结果"

#: fields/relationship.php:571
msgid "Search..."
msgstr "搜索..."

#: fields/relationship.php:580
msgid "Select post type"
msgstr "选择内容类型"

#: fields/relationship.php:593
msgid "Select taxonomy"
msgstr "选择分类"

#: fields/relationship.php:723
msgid "Search"
msgstr "搜索"

#: fields/relationship.php:725 fields/taxonomy.php:36 fields/taxonomy.php:735
msgid "Taxonomy"
msgstr "分类法"

#: fields/relationship.php:732
msgid "Elements"
msgstr "元素"

#: fields/relationship.php:733
msgid "Selected elements will be displayed in each result"
msgstr "选择的元素将在每个结果中显示。"

#: fields/relationship.php:744
msgid "Minimum posts"
msgstr "最小内容"

#: fields/relationship.php:753
msgid "Maximum posts"
msgstr "最大文章数"

#: fields/select.php:36 fields/select.php:174 fields/taxonomy.php:757
msgid "Select"
msgstr "选择"

#: fields/select.php:434
msgid "Stylised UI"
msgstr "装饰的界面"

#: fields/select.php:448
msgid "Use AJAX to lazy load choices?"
msgstr "使用 AJAX 惰性选择？"

#: fields/tab.php:36
msgid "Tab"
msgstr "选项卡"

#: fields/tab.php:128
msgid "Warning"
msgstr "警告"

#: fields/tab.php:133
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr "标签字段不能在 Table 样式的重复字段或者灵活内容字段布局里正常显示"

#: fields/tab.php:146
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr "使用 \"标签字段\" 可以把字段组织起来更好地在编辑界面上显示。"

#: fields/tab.php:148
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"在这个 \"tab field\" (或直到定义了其它的 \"tab field\" ) 以下的所有字段，都会"
"被用这个字段标签作为标题的标签（Tab）组织到一块。"

#: fields/tab.php:155
msgid "Placement"
msgstr "位置"

#: fields/tab.php:167
msgid "End-point"
msgstr "端点"

#: fields/tab.php:168
msgid "Use this field as an end-point and start a new group of tabs"
msgstr "使用这个字段作为端点去创建新的标签群组"

#: fields/taxonomy.php:565
#, php-format
msgid "Add new %s "
msgstr "添加新的 %s"

#: fields/taxonomy.php:704
msgid "None"
msgstr "None"

#: fields/taxonomy.php:736
msgid "Select the taxonomy to be displayed"
msgstr "选择要显示的分类法"

#: fields/taxonomy.php:745
msgid "Appearance"
msgstr "外观"

#: fields/taxonomy.php:746
msgid "Select the appearance of this field"
msgstr "为这个字段选择外观"

#: fields/taxonomy.php:751
msgid "Multiple Values"
msgstr "多选"

#: fields/taxonomy.php:753
msgid "Multi Select"
msgstr "多选"

#: fields/taxonomy.php:755
msgid "Single Value"
msgstr "单个值"

#: fields/taxonomy.php:756
msgid "Radio Buttons"
msgstr "单选框"

#: fields/taxonomy.php:779
msgid "Create Terms"
msgstr "创建分类词汇"

#: fields/taxonomy.php:780
msgid "Allow new terms to be created whilst editing"
msgstr "在编辑时允许可以创建新的分类词汇"

#: fields/taxonomy.php:793
msgid "Save Terms"
msgstr "保存分类词汇"

#: fields/taxonomy.php:794
msgid "Connect selected terms to the post"
msgstr "连接所选分类词汇到内容"

#: fields/taxonomy.php:807
msgid "Load Terms"
msgstr "加载分类词汇"

#: fields/taxonomy.php:808
msgid "Load value from posts terms"
msgstr "载入内容分类词汇的值"

#: fields/taxonomy.php:826
msgid "Term Object"
msgstr "对象缓存"

#: fields/taxonomy.php:827
msgid "Term ID"
msgstr "内容ID"

#: fields/taxonomy.php:886
#, php-format
msgid "User unable to add new %s"
msgstr "用户无法添加新的 %s"

#: fields/taxonomy.php:899
#, php-format
msgid "%s already exists"
msgstr "%s 已存在"

#: fields/taxonomy.php:940
#, php-format
msgid "%s added"
msgstr "%s 已添加"

#: fields/taxonomy.php:985
msgid "Add"
msgstr "添加"

#: fields/text.php:36
msgid "Text"
msgstr "文本"

#: fields/text.php:184 fields/textarea.php:163
msgid "Character Limit"
msgstr "字符限制"

#: fields/text.php:185 fields/textarea.php:164
msgid "Leave blank for no limit"
msgstr "留空则不限制"

#: fields/textarea.php:36
msgid "Text Area"
msgstr "文本段"

#: fields/textarea.php:172
msgid "Rows"
msgstr "行"

#: fields/textarea.php:173
msgid "Sets the textarea height"
msgstr "设置文本区域的高度"

#: fields/textarea.php:182
msgid "New Lines"
msgstr "新行"

#: fields/textarea.php:183
msgid "Controls how new lines are rendered"
msgstr "控制怎么显示新行"

#: fields/textarea.php:187
msgid "Automatically add paragraphs"
msgstr "自动添加段落"

#: fields/textarea.php:188
msgid "Automatically add &lt;br&gt;"
msgstr "自动添加 &lt;br&gt;"

#: fields/textarea.php:189
msgid "No Formatting"
msgstr "无格式"

#: fields/true_false.php:36
msgid "True / False"
msgstr "真/假"

#: fields/true_false.php:107
msgid "eg. Show extra content"
msgstr "例如：显示附加内容"

#: fields/url.php:36
msgid "Url"
msgstr "地址"

#: fields/url.php:160
msgid "Value must be a valid URL"
msgstr "值必须是有效的地址"

#: fields/user.php:437
msgid "Filter by role"
msgstr "根据角色过滤"

#: fields/user.php:445
msgid "All user roles"
msgstr "所有用户角色"

#: fields/wysiwyg.php:37
msgid "Wysiwyg Editor"
msgstr "可视化编辑器"

#: fields/wysiwyg.php:297
msgid "Visual"
msgstr "显示"

#: fields/wysiwyg.php:298
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "文本"

#: fields/wysiwyg.php:354
msgid "Tabs"
msgstr "标签"

#: fields/wysiwyg.php:359
msgid "Visual & Text"
msgstr "显示与文本"

#: fields/wysiwyg.php:360
msgid "Visual Only"
msgstr "只有显示"

#: fields/wysiwyg.php:361
msgid "Text Only"
msgstr "纯文本"

#: fields/wysiwyg.php:368
msgid "Toolbar"
msgstr "工具条"

#: fields/wysiwyg.php:378
msgid "Show Media Upload Buttons?"
msgstr "是否显示媒体上传按钮？"

#: forms/post.php:297 pro/admin/options-page.php:373
msgid "Edit field group"
msgstr "编辑字段组"

#: pro/acf-pro.php:24
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields 专业版"

#: pro/acf-pro.php:175
msgid "Flexible Content requires at least 1 layout"
msgstr "灵活内容字段需要至少一个布局"

#: pro/admin/options-page.php:48
msgid "Options Page"
msgstr "选项页面"

#: pro/admin/options-page.php:83
msgid "No options pages exist"
msgstr "还没有选项页面"

#: pro/admin/options-page.php:298
msgid "Options Updated"
msgstr "选项已更新"

#: pro/admin/options-page.php:304
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"这个选项页上还没有自定义字段群组。<a href=\"%s\">创建自定义字段群组</a>"

#: pro/admin/settings-updates.php:137
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>错误</b>，不能连接到更新服务器"

#: pro/admin/settings-updates.php:267 pro/admin/settings-updates.php:338
msgid "<b>Connection Error</b>. Sorry, please try again"
msgstr "<b>连接错误</b>，再试一次"

#: pro/admin/views/options-page.php:48
msgid "Publish"
msgstr "发布"

#: pro/admin/views/options-page.php:54
msgid "Save Options"
msgstr "保存"

#: pro/admin/views/settings-updates.php:11
msgid "Deactivate License"
msgstr "关闭许可证"

#: pro/admin/views/settings-updates.php:11
msgid "Activate License"
msgstr "激活许可证"

#: pro/admin/views/settings-updates.php:21
msgid "License"
msgstr "许可"

#: pro/admin/views/settings-updates.php:24
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see"
msgstr "解锁更新，输入许可证号。还没有许可证号，请看"

#: pro/admin/views/settings-updates.php:24
msgid "details & pricing"
msgstr "详情与定价"

#: pro/admin/views/settings-updates.php:33
msgid "License Key"
msgstr "许可证号"

#: pro/admin/views/settings-updates.php:65
msgid "Update Information"
msgstr "更新信息"

#: pro/admin/views/settings-updates.php:72
msgid "Current Version"
msgstr "当前版本"

#: pro/admin/views/settings-updates.php:80
msgid "Latest Version"
msgstr "最新版本"

#: pro/admin/views/settings-updates.php:88
msgid "Update Available"
msgstr "可用更新"

#: pro/admin/views/settings-updates.php:96
msgid "Update Plugin"
msgstr "更新插件"

#: pro/admin/views/settings-updates.php:98
msgid "Please enter your license key above to unlock updates"
msgstr "在上面输入许可证号解锁更新"

#: pro/admin/views/settings-updates.php:104
msgid "Check Again"
msgstr "重新检查"

#: pro/admin/views/settings-updates.php:121
msgid "Upgrade Notice"
msgstr "更新通知"

#: pro/api/api-options-page.php:22 pro/api/api-options-page.php:23
msgid "Options"
msgstr "选项"

#: pro/core/updates.php:186
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>"
msgstr ""
"启用更新，先在 <a href=\"%s\">更新</a> 页面输入许可证。还没有许可证，请查看 "
"<a href=\"%s\">详情与定价</a>"

#: pro/fields/flexible-content.php:36
msgid "Flexible Content"
msgstr "大段内容"

#: pro/fields/flexible-content.php:42 pro/fields/repeater.php:43
msgid "Add Row"
msgstr "添加行"

#: pro/fields/flexible-content.php:45
msgid "layout"
msgstr "布局"

#: pro/fields/flexible-content.php:46
msgid "layouts"
msgstr "布局"

#: pro/fields/flexible-content.php:47
msgid "remove {layout}?"
msgstr "删除 {layout}？"

#: pro/fields/flexible-content.php:48
msgid "This field requires at least {min} {identifier}"
msgstr "这个字段需要至少 {min} {identifier}"

#: pro/fields/flexible-content.php:49
msgid "This field has a limit of {max} {identifier}"
msgstr "这个字段限制最大为 {max} {identifier}"

#: pro/fields/flexible-content.php:50
msgid "This field requires at least {min} {label} {identifier}"
msgstr "这个字段需要至少 {min} {label} {identifier}"

#: pro/fields/flexible-content.php:51
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "{label} 已到最大限制 ({max} {identifier})"

#: pro/fields/flexible-content.php:52
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} 可用 (max {max})"

#: pro/fields/flexible-content.php:53
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} 需要 (min {min})"

#: pro/fields/flexible-content.php:211
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "点击下面的 \"%s\" 按钮创建布局"

#: pro/fields/flexible-content.php:369
msgid "Add layout"
msgstr "添加布局"

#: pro/fields/flexible-content.php:372
msgid "Remove layout"
msgstr "删除布局"

#: pro/fields/flexible-content.php:514
msgid "Reorder Layout"
msgstr "重排序布局"

#: pro/fields/flexible-content.php:514
msgid "Reorder"
msgstr "重排序"

#: pro/fields/flexible-content.php:515
msgid "Delete Layout"
msgstr "删除布局"

#: pro/fields/flexible-content.php:516
msgid "Duplicate Layout"
msgstr "复制布局"

#: pro/fields/flexible-content.php:517
msgid "Add New Layout"
msgstr "添加新布局"

#: pro/fields/flexible-content.php:561
msgid "Display"
msgstr "显示"

#: pro/fields/flexible-content.php:572 pro/fields/repeater.php:399
msgid "Table"
msgstr "表"

#: pro/fields/flexible-content.php:573 pro/fields/repeater.php:400
msgid "Block"
msgstr "区块"

#: pro/fields/flexible-content.php:574 pro/fields/repeater.php:401
msgid "Row"
msgstr "行"

#: pro/fields/flexible-content.php:589
msgid "Min"
msgstr "最小"

#: pro/fields/flexible-content.php:602
msgid "Max"
msgstr "最大"

#: pro/fields/flexible-content.php:630 pro/fields/repeater.php:408
msgid "Button Label"
msgstr "按钮标签"

#: pro/fields/flexible-content.php:639
msgid "Minimum Layouts"
msgstr "最小布局"

#: pro/fields/flexible-content.php:648
msgid "Maximum Layouts"
msgstr "最大布局"

#: pro/fields/gallery.php:36
msgid "Gallery"
msgstr "相册"

#: pro/fields/gallery.php:52
msgid "Add Image to Gallery"
msgstr "添加图片到相册"

#: pro/fields/gallery.php:56
msgid "Maximum selection reached"
msgstr "已到最大选择"

#: pro/fields/gallery.php:335
msgid "Length"
msgstr "长度"

#: pro/fields/gallery.php:355
msgid "Remove"
msgstr "删除"

#: pro/fields/gallery.php:535
msgid "Add to gallery"
msgstr "添加到相册"

#: pro/fields/gallery.php:539
msgid "Bulk actions"
msgstr "批量动作"

#: pro/fields/gallery.php:540
msgid "Sort by date uploaded"
msgstr "按上传日期排序"

#: pro/fields/gallery.php:541
msgid "Sort by date modified"
msgstr "按修改日期排序"

#: pro/fields/gallery.php:542
msgid "Sort by title"
msgstr "按标题排序"

#: pro/fields/gallery.php:543
msgid "Reverse current order"
msgstr "颠倒当前排序"

#: pro/fields/gallery.php:561
msgid "Close"
msgstr "关闭"

#: pro/fields/gallery.php:619
msgid "Minimum Selection"
msgstr "最小选择"

#: pro/fields/gallery.php:628
msgid "Maximum Selection"
msgstr "最大选择"

#: pro/fields/gallery.php:809
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s 需要至少 %s 个选择"

#: pro/fields/repeater.php:36
msgid "Repeater"
msgstr "重复器"

#: pro/fields/repeater.php:46
msgid "Minimum rows reached ({min} rows)"
msgstr "已到最小行数 ({min} 行)"

#: pro/fields/repeater.php:47
msgid "Maximum rows reached ({max} rows)"
msgstr "已到最大行数 ({max} 行)"

#: pro/fields/repeater.php:259
msgid "Drag to reorder"
msgstr "拖拽排序"

#: pro/fields/repeater.php:301
msgid "Add row"
msgstr "添加行"

#: pro/fields/repeater.php:302
msgid "Remove row"
msgstr "删除行"

#: pro/fields/repeater.php:350
msgid "Sub Fields"
msgstr "子字段"

#: pro/fields/repeater.php:372
msgid "Minimum Rows"
msgstr "最小行数"

#: pro/fields/repeater.php:382
msgid "Maximum Rows"
msgstr "最大行数"

#. Plugin Name of the plugin/theme
msgid "Advanced Custom Fields Pro"
msgstr "Advanced Custom Fields 专业版"

#. Plugin URI of the plugin/theme
msgid "http://www.advancedcustomfields.com/"
msgstr "http://www.advancedcustomfields.com/"

#. Description of the plugin/theme
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "用强大专业的字段定制 WordPress。"

#. Author of the plugin/theme
msgid "elliot condon"
msgstr "elliot condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#, fuzzy
#~ msgid "Show Field Keys"
#~ msgstr "显示字段密钥："

#, fuzzy
#~ msgid "Private"
#~ msgstr "激活"

#, fuzzy
#~ msgid "Revision"
#~ msgstr "版本控制"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "字段组排序<br />从低到高。"

#, fuzzy
#~ msgid "ACF PRO Required"
#~ msgstr "(必填项)"

#, fuzzy
#~ msgid "Update Database"
#~ msgstr "升级数据库"

#, fuzzy
#~ msgid "Data Upgrade"
#~ msgstr "升级"

#, fuzzy
#~ msgid "Data is at the latest version."
#~ msgstr "非常感谢你升级插件到最新版本！"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "加载&保存条目到文章。"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr "在文章上加载值，保存时更新文章条目。"

#, fuzzy
#~ msgid "image"
#~ msgstr "图像"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "关系"

#, fuzzy
#~ msgid "unload"
#~ msgstr "下载"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "字段组已发布。"

#, fuzzy
#~ msgid "move_field"
#~ msgstr "保存字段"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "大段内容"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "相册"

#, fuzzy
#~ msgid "repeater"
#~ msgstr "复制"

#~ msgid "Custom field updated."
#~ msgstr "自定义字段已更新。"

#~ msgid "Custom field deleted."
#~ msgstr "自定义字段已删除。"

#, fuzzy
#~ msgid "Import/Export"
#~ msgstr "重要"

#~ msgid "Column Width"
#~ msgstr "分栏宽度"

#, fuzzy
#~ msgid "Attachment Details"
#~ msgstr "附件已更新"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "验证失败，下面一个或多个字段是必需的。"

#~ msgid "Field group restored to revision from %s"
#~ msgstr "字段组已恢复到版本%s"

#~ msgid "No ACF groups selected"
#~ msgstr "没有选择ACF组"

#~ msgid "Repeater Field"
#~ msgstr "复制字段"

#~ msgid ""
#~ "Create infinite rows of repeatable data with this versatile interface!"
#~ msgstr "使用这个方面的界面为重复数据创建无限行。 "

#~ msgid "Gallery Field"
#~ msgstr "相册字段"

#~ msgid "Create image galleries in a simple and intuitive interface!"
#~ msgstr "使用简单直观的界面创建画廊！"

#~ msgid "Create global data to use throughout your website!"
#~ msgstr "创建整个站点可用的全局数据。"

#~ msgid "Flexible Content Field"
#~ msgstr "多样内容字段"

#~ msgid "Create unique designs with a flexible content layout manager!"
#~ msgstr "通过强大的内容布局管理功能创建一个独有的设计。"

#~ msgid "Gravity Forms Field"
#~ msgstr "Gravity表单字段"

#~ msgid "Creates a select field populated with Gravity Forms!"
#~ msgstr "创建一个由Gravity表单处理的选择字段。"

#~ msgid "Date & Time Picker"
#~ msgstr "日期&时间选择器"

#~ msgid "jQuery date & time picker"
#~ msgstr "jQuery 日期 & 时间选择器"

#~ msgid "Find addresses and coordinates of a desired location"
#~ msgstr "查找需要的位置的地址和坐标。"

#~ msgid "Contact Form 7 Field"
#~ msgstr "Contact Form 7 字段"

#~ msgid "Assign one or more contact form 7 forms to a post"
#~ msgstr "分配一个或多个contact form 7表单到文章"

#~ msgid "Advanced Custom Fields Add-Ons"
#~ msgstr "自定义字段附加功能"

#~ msgid ""
#~ "The following Add-ons are available to increase the functionality of the "
#~ "Advanced Custom Fields plugin."
#~ msgstr "下面的附加项可以提高插件功能。"

#~ msgid ""
#~ "Each Add-on can be installed as a separate plugin (receives updates) or "
#~ "included in your theme (does not receive updates)."
#~ msgstr ""
#~ "每个附件都可以作为一个单独的插件安装（可以获取更新）或包含在你的主题中（不"
#~ "能获取更新）"

#~ msgid "Purchase & Install"
#~ msgstr "购买和安装"

#~ msgid "Export"
#~ msgstr "导出"

#~ msgid "Select the field groups to be exported"
#~ msgstr "选择需要导出的字段组。"

#~ msgid "Export to XML"
#~ msgstr "导出到XML"

#~ msgid "Export to PHP"
#~ msgstr "导出到PHP"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr "ACF将创建一个兼容WP导入插件的.xml文件。"

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field "
#~ "groups. This is useful for migrating fields groups between Wp websites."
#~ msgstr ""
#~ "导入字段组将出现在可编辑字段组后面，在几个WP站点之间迁移字段组时，这将非常"
#~ "有用。"

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr "从列表中选择字段组，然后点击 \"导出XML\" "

#~ msgid "Save the .xml file when prompted"
#~ msgstr "导出后保存.xml文件"

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr "转到工具 &raquo; 导入，然后选择WordPress "

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "安装WP导入插件后开始"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "上传并导入.xml文件"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "选择用户，忽略导入附件"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "成功了，使用愉快！"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACP将导出可以包含到主题中的PHP代码"

#~ msgid ""
#~ "Registered field groups <b>will not</b> appear in the list of editable "
#~ "field groups. This is useful for including fields in themes."
#~ msgstr ""
#~ "已注册字段<b>不会</b>出现在可编辑分组中，这对主题中包含的字段非常有用。"

#~ msgid ""
#~ "Please note that if you export and register field groups within the same "
#~ "WP, you will see duplicate fields on your edit screens. To fix this, "
#~ "please move the original field group to the trash or remove the code from "
#~ "your functions.php file."
#~ msgstr ""
#~ "请注意，如果在同一个网站导出并注册字段组，您会在您的编辑屏幕上看到重复的字"
#~ "段，为了解决这个问题，请将原字段组移动到回收站或删除您的functions.php文件"
#~ "中的代码。"

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr "参加列表中选择表单组，然后点击 \"生成PHP\""

#~ msgid "Copy the PHP code generated"
#~ msgstr "复制生成的PHP代码。"

#~ msgid "Paste into your functions.php file"
#~ msgstr "请插入您的function.php文件"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr "要激活附加组件，编辑和应用代码中的前几行。"

#~ msgid "Notes"
#~ msgstr "注意"

#~ msgid "Include in theme"
#~ msgstr "包含在主题中"

#~ msgid ""
#~ "The Advanced Custom Fields plugin can be included within a theme. To do "
#~ "so, move the ACF plugin inside your theme and add the following code to "
#~ "your functions.php file:"
#~ msgstr ""
#~ "字段插件可以包含到主题中，如果需要进行此操作，请移动字段插件到themes文件夹"
#~ "并添加以下代码到functions.php文件："

#~ msgid ""
#~ "To remove all visual interfaces from the ACF plugin, you can use a "
#~ "constant to enable lite mode. Add the following code to you functions.php "
#~ "file <b>before</b> the include_once code:"
#~ msgstr ""
#~ "要删除所有ACF插件的可视化界面，你可以用一个常数，使精简版模式，将下面的代"
#~ "码添加到functions.php文件中include_once代码<b>之前</b>。"

#~ msgid "Back to export"
#~ msgstr "返回到导出器"

#~ msgid ""
#~ "/**\n"
#~ " *  Install Add-ons\n"
#~ " *  \n"
#~ " *  The following code will include all 4 premium Add-Ons in your theme.\n"
#~ " *  Please do not attempt to include a file which does not exist. This "
#~ "will produce an error.\n"
#~ " *  \n"
#~ " *  All fields must be included during the 'acf/register_fields' action.\n"
#~ " *  Other types of Add-ons (like the options page) can be included "
#~ "outside of this action.\n"
#~ " *  \n"
#~ " *  The following code assumes you have a folder 'add-ons' inside your "
#~ "theme.\n"
#~ " *\n"
#~ " *  IMPORTANT\n"
#~ " *  Add-ons may be included in a premium theme as outlined in the terms "
#~ "and conditions.\n"
#~ " *  However, they are NOT to be included in a premium / free plugin.\n"
#~ " *  For more information, please read http://www.advancedcustomfields.com/"
#~ "terms-conditions/\n"
#~ " */"
#~ msgstr ""
#~ "/ **\n"
#~ " *安装附加组件\n"
#~ " *\n"
#~ " *下面的代码将包括所有4个高级附加组件到您的主题\n"
#~ " *请不要试图包含一个不存在的文件，这将产生一个错误。\n"
#~ " *\n"
#~ " *所有字段都必须在'acf/register_fields'动作执行时包含。\n"
#~ " *其他类型的加载项（如选项页）可以包含在这个动作之外。\n"
#~ " *\n"
#~ " *下面的代码假定你在你的主题里面有一个“add-ons”文件夹。\n"
#~ " *\n"
#~ " *重要\n"
#~ " *附加组件可能在一个高级主题中包含下面的条款及条件。\n"
#~ " *但是，他们都没有被列入高级或免费插件。\n"
#~ " *欲了解更多信息，请读取http://www.advancedcustomfields.com/terms-"
#~ "conditions/\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " *  Register Field Groups\n"
#~ " *\n"
#~ " *  The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " *  You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * 注册字段组\n"
#~ " *\n"
#~ " * register_field_group函数接受一个包含注册字段组有关数据的数组\n"
#~ " *您可以编辑您认为合适的数组，然而，如果数组不兼容ACF，这可能会导致错误\n"
#~ " */"

#~ msgid "Vote"
#~ msgstr "投票"

#~ msgid "Follow"
#~ msgstr "关注"

#~ msgid "Activation codes have grown into plugins!"
#~ msgstr "激活码成为了插件！"

#~ msgid ""
#~ "Add-ons are now activated by downloading and installing individual "
#~ "plugins. Although these plugins will not be hosted on the wordpress.org "
#~ "repository, each Add-on will continue to receive updates in the usual way."
#~ msgstr ""
#~ "附加组件现在通过下载和安装单独的插件激活，虽然这些插件不在wordpress.org库"
#~ "托管，每个附加组件将通过合适的方式得到更新。"

#~ msgid "All previous Add-ons have been successfully installed"
#~ msgstr "所有附加功能已安装！"

#~ msgid "This website uses premium Add-ons which need to be downloaded"
#~ msgstr "此站点使用的高级功能需要下载。"

#~ msgid "Download your activated Add-ons"
#~ msgstr "下载已激活的附加功能"

#~ msgid ""
#~ "This website does not use premium Add-ons and will not be affected by "
#~ "this change."
#~ msgstr "此站点未使用高级功能，这个改变没有影响。"

#~ msgid "Easier Development"
#~ msgstr "快速开发"

#~ msgid "New Field Types"
#~ msgstr "新字段类型"

#~ msgid "Email Field"
#~ msgstr "电子邮件字段"

#~ msgid "Password Field"
#~ msgstr "密码字段"

#~ msgid "Custom Field Types"
#~ msgstr "自定义字段类型"

#~ msgid ""
#~ "Creating your own field type has never been easier! Unfortunately, "
#~ "version 3 field types are not compatible with version 4."
#~ msgstr ""
#~ "创建您自己的字段类型从未如此简单！不幸的是，版本3的字段类型不兼容版本4。"

#~ msgid "Migrating your field types is easy, please"
#~ msgstr "数据迁移非常简单，请"

#~ msgid "follow this tutorial"
#~ msgstr "跟随这个向导"

#~ msgid "to learn more."
#~ msgstr "了解更多。"

#~ msgid "Actions &amp; Filters"
#~ msgstr "动作&amp;过滤器"

#~ msgid ""
#~ "All actions & filters have recieved a major facelift to make customizing "
#~ "ACF even easier! Please"
#~ msgstr "所有动作和过滤器得到了一次重大改版一遍更方便的定制ACF！请"

#~ msgid "read this guide"
#~ msgstr "阅读此向导"

#~ msgid "to find the updated naming convention."
#~ msgstr "找到更新命名约定。"

#~ msgid "Preview draft is now working!"
#~ msgstr "预览功能已经可用！"

#~ msgid "This bug has been squashed along with many other little critters!"
#~ msgstr "这个错误已经与许多其他小动物一起被压扁了！"

#~ msgid "See the full changelog"
#~ msgstr "查看全部更新日志"

#~ msgid "Database Changes"
#~ msgstr "数据库改变"

#~ msgid ""
#~ "Absolutely <strong>no</strong> changes have been made to the database "
#~ "between versions 3 and 4. This means you can roll back to version 3 "
#~ "without any issues."
#~ msgstr ""
#~ "数据库在版本3和4之间<strong>没有</strong>任何修改，这意味你可以安全回滚到"
#~ "版本3而不会遇到任何问题。"

#~ msgid "Potential Issues"
#~ msgstr "潜在问题"

#~ msgid ""
#~ "Do to the sizable changes surounding Add-ons, field types and action/"
#~ "filters, your website may not operate correctly. It is important that you "
#~ "read the full"
#~ msgstr ""
#~ "需要在附加组件，字段类型和动作/过滤之间做重大修改时，你可的网站可能会出现"
#~ "一些问题，所有强烈建议阅读全部"

#~ msgid "Migrating from v3 to v4"
#~ msgstr "从V3迁移到V4"

#~ msgid "guide to view the full list of changes."
#~ msgstr "查看所有更新列表。"

#~ msgid "Really Important!"
#~ msgstr "非常重要！"

#~ msgid ""
#~ "If you updated the ACF plugin without prior knowledge of such changes, "
#~ "Please roll back to the latest"
#~ msgstr "如果你没有收到更新通知而升级到了ACF插件，请回滚到最近的一个版本。"

#~ msgid "version 3"
#~ msgstr "版本 3"

#~ msgid "of this plugin."
#~ msgstr "这个插件"

#~ msgid "Thank You"
#~ msgstr "谢谢！"

#~ msgid ""
#~ "A <strong>BIG</strong> thank you to everyone who has helped test the "
#~ "version 4 beta and for all the support I have received."
#~ msgstr "非常感谢帮助我测试版本4的所有人。"

#~ msgid "Without you all, this release would not have been possible!"
#~ msgstr "没有你们，此版本可能还没有发布。"

#~ msgid "Changelog for"
#~ msgstr "更新日志："

#~ msgid "Learn more"
#~ msgstr "了解更多"

#~ msgid "Overview"
#~ msgstr "预览"

#~ msgid ""
#~ "Previously, all Add-ons were unlocked via an activation code (purchased "
#~ "from the ACF Add-ons store). New to v4, all Add-ons act as separate "
#~ "plugins which need to be individually downloaded, installed and updated."
#~ msgstr ""
#~ "在此之前，所有附加组件通过一个激活码（从ACF附加组件的商店购买）解锁，到了"
#~ "版本V4，所有附加组件作为单独的插件下载，安装和更新。"

#~ msgid ""
#~ "This page will assist you in downloading and installing each available "
#~ "Add-on."
#~ msgstr "此页将帮助您下载和安装每个可用的附加组件。"

#~ msgid "Available Add-ons"
#~ msgstr "可用附加功能"

#~ msgid ""
#~ "The following Add-ons have been detected as activated on this website."
#~ msgstr "在此网站上检测到以下附加已激活。"

#~ msgid "Activation Code"
#~ msgstr "激活码"

#~ msgid "Installation"
#~ msgstr "安装"

#~ msgid "For each Add-on available, please perform the following:"
#~ msgstr "对于每个可以用附加组件，请执行以下操作："

#~ msgid "Download the Add-on plugin (.zip file) to your desktop"
#~ msgstr "下载附加功能（.zip文件）到电脑。"

#~ msgid "Navigate to"
#~ msgstr "链接到"

#~ msgid "Plugins > Add New > Upload"
#~ msgstr "插件>添加>上传"

#~ msgid ""
#~ "Use the uploader to browse, select and install your Add-on (.zip file)"
#~ msgstr "使用文件上载器，浏览，选择并安装附加组件（zip文件）"

#~ msgid ""
#~ "Once the plugin has been uploaded and installed, click the 'Activate "
#~ "Plugin' link"
#~ msgstr "插件上传并安装后，点击'激活插件'链接。"

#~ msgid "The Add-on is now installed and activated!"
#~ msgstr "附加功能已安装并启用。"

#~ msgid "Awesome. Let's get to work"
#~ msgstr "太棒了！我们开始吧。"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "修改字段组选项'在页面上显示'"

#~ msgid "Modifying field option 'taxonomy'"
#~ msgstr "修改字段选项'分类法'"

#~ msgid "Moving user custom fields from wp_options to wp_usermeta'"
#~ msgstr "从wp_options移动用户自定义字段到wp_usermeta"

#~ msgid "blue : Blue"
#~ msgstr " blue : Blue "

#~ msgid "eg: #ffffff"
#~ msgstr "如: #ffffff "

#~ msgid "Dummy"
#~ msgstr "二进制"

#~ msgid "File Object"
#~ msgstr "文件对象"

#~ msgid "File Updated."
#~ msgstr "文件已更新"

#~ msgid "Media attachment updated."
#~ msgstr "媒体附件已更新。"

#~ msgid "Add Selected Files"
#~ msgstr "添加已选择文件"

#~ msgid "Image Object"
#~ msgstr "对象图像"

#~ msgid "Image Updated."
#~ msgstr "图片已更新"

#~ msgid "No images selected"
#~ msgstr "没有选择图片"

#~ msgid "Add Selected Images"
#~ msgstr "添加所选图片"

#~ msgid "Text &amp; HTML entered here will appear inline with the fields"
#~ msgstr "在这里输入的文本和HTML将和此字段一起出现。"

#~ msgid "Enter your choices one per line"
#~ msgstr "输入选项，每行一个"

#~ msgid "Red"
#~ msgstr "红"

#~ msgid "Blue"
#~ msgstr "蓝"

#~ msgid "Post Type Select"
#~ msgstr "文章类型选择"

#~ msgid "You can use multiple tabs to break up your fields into sections."
#~ msgstr "你可以使用选项卡分割字段到多个区域。"

#~ msgid "Define how to render html tags"
#~ msgstr "定义怎么生成html标签"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "定义怎么处理html标签和换行"

#~ msgid ""
#~ "This format will determin the value saved to the database and returned "
#~ "via the API"
#~ msgstr "此格式将决定存储在数据库中的值，并通过API返回。"

#~ msgid "\"yymmdd\" is the most versatile save format. Read more about"
#~ msgstr "\"yymmdd\" 是最常用的格式，如需了解更多，请参考"

#~ msgid "jQuery date formats"
#~ msgstr "jQuery日期格式"

#~ msgid "This format will be seen by the user when entering a value"
#~ msgstr "这是用户输入日期后看到的格式。"

#~ msgid ""
#~ "\"dd/mm/yy\" or \"mm/dd/yy\" are the most used Display Formats. Read more "
#~ "about"
#~ msgstr "\"dd/mm/yy\" 或 \"mm/dd/yy\" 为最常用的显示格式，了解更多"

#~ msgid "Field Order"
#~ msgstr "字段顺序"

#~ msgid "Edit this Field"
#~ msgstr "编辑当前字段"

#~ msgid "Docs"
#~ msgstr "文档"

#~ msgid "Field Instructions"
#~ msgstr "字段说明"

#~ msgid "Show this field when"
#~ msgstr "符合这些规则中的"

#~ msgid "all"
#~ msgstr "所有"

#~ msgid "any"
#~ msgstr "任一个"

#~ msgid "these rules are met"
#~ msgstr "项时，显示此字段"

#~ msgid "Taxonomy Term (Add / Edit)"
#~ msgstr "分类法条目（添加/编辑）"

#~ msgid "Media Attachment (Edit)"
#~ msgstr "媒体附件（编辑）"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "使用激活码解锁附加功能"

#~ msgid "Normal"
#~ msgstr "普通"

#~ msgid "No Metabox"
#~ msgstr "无Metabox"

#~ msgid "Add-Ons"
#~ msgstr "附加"

#~ msgid "Just updated to version 4?"
#~ msgstr "刚更新到版本4？"

#~ msgid ""
#~ "Activation codes have changed to plugins! Download your purchased add-ons"
#~ msgstr "激活码已改变了插件，请下载已购买的附加功能。"

#~ msgid "here"
#~ msgstr "这里"

#~ msgid "match"
#~ msgstr "符合"

#~ msgid "of the above"
#~ msgstr "  "

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr "阅读文档，学习功能和发现一些小提示，然后应用到你下一个网站项目中。"

#~ msgid "Visit the ACF website"
#~ msgstr "访问ACF网站"

#~ msgid "Add File to Field"
#~ msgstr "添加文件"

#~ msgid "Add Image to Field"
#~ msgstr "添加图片"

#~ msgid "Repeater field deactivated"
#~ msgstr "检测到复制字段"

#~ msgid "Gallery field deactivated"
#~ msgstr "检测到相册字段"

#~ msgid "Repeater field activated"
#~ msgstr "复制插件已激活。"

#~ msgid "Options page activated"
#~ msgstr "选项页面已激活"

#~ msgid "Flexible Content field activated"
#~ msgstr "多样内容字段已激活"

#~ msgid "Gallery field activated"
#~ msgstr "插件激活成功。"

#~ msgid "License key unrecognised"
#~ msgstr "许可密钥未注册"

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr "可以购买一个许可证来激活附加功能，每个许可证可用于许多站点。"

#~ msgid "Inactive"
#~ msgstr "未禁用"

#~ msgid "Register Field Groups"
#~ msgstr "注册字段组"

#~ msgid "Create PHP"
#~ msgstr "创建PHP"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "高级自动设置"

#~ msgid "requires a database upgrade"
#~ msgstr "数据库需要升级"

#~ msgid "why?"
#~ msgstr "为什么？"

#~ msgid "Please"
#~ msgstr "请"

#~ msgid "backup your database"
#~ msgstr "备份数据库"

#~ msgid "then click"
#~ msgstr "然后点击"

#~ msgid "No choices to choose from"
#~ msgstr "选择表单没有选"

#~ msgid "+ Add Row"
#~ msgstr "添加行"

#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr "没有字段，点击<strong>添加</strong>按钮创建第一个字段。"

#~ msgid "Close Sub Field"
#~ msgstr "选择子字段"

#~ msgid "+ Add Sub Field"
#~ msgstr "添加子字段"

#~ msgid "Alternate Text"
#~ msgstr "替换文本"

#~ msgid "Caption"
#~ msgstr "标题"

#~ msgid "Thumbnail is advised"
#~ msgstr "建设使用缩略图"

#~ msgid "Image Updated"
#~ msgstr "图片已更新"

#~ msgid "Grid"
#~ msgstr "栅格"

#~ msgid "List"
#~ msgstr "列表"

#~ msgid "1 image selected"
#~ msgstr "已选择1张图片"

#~ msgid "{count} images selected"
#~ msgstr "选择了 {count}张图片"

#~ msgid "Added"
#~ msgstr "已添加"

#~ msgid "Image already exists in gallery"
#~ msgstr "图片已在相册中"

#~ msgid "Repeater Fields"
#~ msgstr "复制字段"

#~ msgid "Table (default)"
#~ msgstr "表格（默认）"

#~ msgid "Run filter \"the_content\"?"
#~ msgstr "是否运行过滤器 \"the_content\"?"

#~ msgid "Media (Edit)"
#~ msgstr "媒体（编辑）"
