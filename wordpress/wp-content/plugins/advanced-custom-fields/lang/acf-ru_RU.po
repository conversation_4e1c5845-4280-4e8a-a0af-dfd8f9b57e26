msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2018-08-18 18:25+0300\n"
"PO-Revision-Date: 2019-01-05 10:08+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:80
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:392 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Группы полей"

#: acf.php:393
msgid "Field Group"
msgstr "Группа полей"

#: acf.php:394 acf.php:426 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New"
msgstr "Добавить"

#: acf.php:395
msgid "Add New Field Group"
msgstr "Создание новой группы полей"

#: acf.php:396
msgid "Edit Field Group"
msgstr "Редактирование группы полей"

#: acf.php:397
msgid "New Field Group"
msgstr "Новая группа полей"

#: acf.php:398
msgid "View Field Group"
msgstr "Просмотреть группу полей"

#: acf.php:399
msgid "Search Field Groups"
msgstr "Поиск групп полей"

#: acf.php:400
msgid "No Field Groups found"
msgstr "Группы полей не найдены."

#: acf.php:401
msgid "No Field Groups found in Trash"
msgstr "Группы полей не найдены в корзине."

#: acf.php:424 includes/admin/admin-field-group.php:202
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Поля"

#: acf.php:425
msgid "Field"
msgstr "Поле"

#: acf.php:427
msgid "Add New Field"
msgstr "Добавить новое поле"

#: acf.php:428
msgid "Edit Field"
msgstr "Изменить поле"

#: acf.php:429 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Новое поле"

#: acf.php:430
msgid "View Field"
msgstr "Просмотреть поле"

#: acf.php:431
msgid "Search Fields"
msgstr "Поиск полей"

#: acf.php:432
msgid "No Fields found"
msgstr "Поля не найдены"

#: acf.php:433
msgid "No Fields found in Trash"
msgstr "Поля не найдены в Корзине"

#: acf.php:472 includes/admin/admin-field-group.php:384
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Неактивно"

#: acf.php:477
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Неактивен <span class=\"count\">(%s)</span>"
msgstr[1] "Неактивны <span class=\"count\">(%s)</span>"
msgstr[2] "Неактивно <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Группа полей обновлена."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Группа полей удалена."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Группа полей опубликована."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Группа полей сохранена."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Группа полей отправлена."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Группа полей запланирована на"

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Черновик группы полей обновлен."

#: includes/admin/admin-field-group.php:153
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Имя поля не должно начинаться со строки \"field_\""

#: includes/admin/admin-field-group.php:154
msgid "This field cannot be moved until its changes have been saved"
msgstr "Это поле не может быть перемещено до сохранения изменений"

#: includes/admin/admin-field-group.php:155
msgid "Field group title is required"
msgstr "Введите название для группы полей"

#: includes/admin/admin-field-group.php:156
msgid "Move to trash. Are you sure?"
msgstr "Отправить в корзину. Вы уверены?"

#: includes/admin/admin-field-group.php:157
msgid "No toggle fields available"
msgstr "Нет доступных полей с выбором значений."

#: includes/admin/admin-field-group.php:158
msgid "Move Custom Field"
msgstr "Переместить поле"

# Maybe non-translateable too.
#: includes/admin/admin-field-group.php:159
msgid "Checked"
msgstr "Выбрано"

#: includes/admin/admin-field-group.php:160 includes/api/api-field.php:289
msgid "(no label)"
msgstr "(нет заголовка)"

#: includes/admin/admin-field-group.php:161
msgid "(this field)"
msgstr " (текущее поле)"

#: includes/admin/admin-field-group.php:162
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "копия"

#: includes/admin/admin-field-group.php:163
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:4055
msgid "or"
msgstr "или"

#: includes/admin/admin-field-group.php:164
msgid "Null"
msgstr "null"

#: includes/admin/admin-field-group.php:203
msgid "Location"
msgstr "Условия отображения"

#: includes/admin/admin-field-group.php:204
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Настройки"

#: includes/admin/admin-field-group.php:354
msgid "Field Keys"
msgstr "Ключи полей"

#: includes/admin/admin-field-group.php:384
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Активные"

#: includes/admin/admin-field-group.php:750
msgid "Move Complete."
msgstr "Перемещение выполнено."

#: includes/admin/admin-field-group.php:751
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Теперь поле %s может быть найдено в группе полей %s"

#: includes/admin/admin-field-group.php:752
msgid "Close Window"
msgstr "Закрыть окно"

#: includes/admin/admin-field-group.php:793
msgid "Please select the destination for this field"
msgstr "Пожалуйста выберите местоположение для этого поля"

#: includes/admin/admin-field-group.php:800
msgid "Move Field"
msgstr "Переместить поле"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Активна <span class=\"count\">(%s)</span>"
msgstr[1] "Активно <span class=\"count\">(%s)</span>"
msgstr[2] "Активны <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Группа полей была дублирована. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s группа полей дублирована."
msgstr[1] "%s группы полей дублировано."
msgstr[2] "%s групп полей дублировано."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Группу полей было синхронизировано. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s группа полей синхронизирована."
msgstr[1] "%s группы полей синхронизированы."
msgstr[2] "%s групп полей синхронизировано."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Синхронизация доступна"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "Заголовок"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "Описание"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Статус"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Настраивайте WordPress с помощью интуитивно понятных и мощных дополнительных "
"полей."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Журнал изменений"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Что нового в <a href=\"%s\">версии %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Источники"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Сайт"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Документация"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Поддержка"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Спасибо вам за использование <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:667
msgid "Duplicate this item"
msgstr "Дублировать элемент"

#: includes/admin/admin-field-groups.php:667
#: includes/admin/admin-field-groups.php:683
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate"
msgstr "Дублировать"

#: includes/admin/admin-field-groups.php:700
#: includes/fields/class-acf-field-google-map.php:164
#: includes/fields/class-acf-field-relationship.php:674
msgid "Search"
msgstr "Поиск"

#: includes/admin/admin-field-groups.php:759
#, php-format
msgid "Select %s"
msgstr "Выберите %s"

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr "Синхронизировать группу полей"

#: includes/admin/admin-field-groups.php:767
#: includes/admin/admin-field-groups.php:797
msgid "Sync"
msgstr "Синхронизация"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Применить"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Массовые операции"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Инструменты"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Группы полей"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Обновить базу данных"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Проверить сайт и обновить"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Возникла ошибка при обработке запроса"

#: includes/admin/install.php:210 includes/admin/views/install.php:104
msgid "No updates available."
msgstr "На данный момент обновлений нет."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Дополнения"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Ошибка</b>. Невозможно загрузить список  дополнений"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Информация"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Что нового"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Экспорт групп полей"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Генерировать PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Группы полей не выбраны"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Импортировано %s группу полей."
msgstr[1] "Импортировано %s группы полей"
msgstr[2] "Импортировано %s групп полей"

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Выберите группы полей"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Выберите группы полей, которые вы хотите экспортировать, а также метод "
"экспорта. Используйте кнопку <b>Загрузить файл</b> для загрузки JSON файла "
"или <b>Генерировать код</b> для получения кода, который можно интегрировать "
"в шаблон."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Экспорт файла"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Указанный код может быть использован для регистрации группы полей "
"непосредственно в шаблоне. Локальная группа полей может предоставить много "
"преимуществ в виде большей скорости загрузки, упрощения контроля версий и "
"динамических полей. Просто скопируйте и вставьте указанный ниже код в файл "
"functions.php или подключите его через внешний файл."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Скопировать в буфер обмена"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Скопировано"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Импорт групп полей"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr "Выберите файл конфигурации в формате JSON для импорта групп полей."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Выбрать файл"

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Импортировать файл"

#: includes/admin/tools/class-acf-admin-tool-import.php:100
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "Файл не выбран"

#: includes/admin/tools/class-acf-admin-tool-import.php:113
msgid "Error uploading file. Please try again"
msgstr "Ошибка при загрузке файла. Попробуйте еще раз"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid "Incorrect file type"
msgstr "Неправильный тип файла"

#: includes/admin/tools/class-acf-admin-tool-import.php:139
msgid "Import file empty"
msgstr "Импортируемый файл пуст"

#: includes/admin/tools/class-acf-admin-tool-import.php:247
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Импортировано %s группу полей"
msgstr[1] "Импортировано %s группы полей"
msgstr[2] "Импортировано %s групп полей"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Условная логика"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Показывать это поле, если"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "и"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Добавить группу условий"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:424
#: pro/fields/class-acf-field-repeater.php:294
msgid "Drag to reorder"
msgstr "Потяните для изменения порядка"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Редактировать поле"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "Редактировать"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Дублировать поле"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Переместить поле в другую группу"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Переместить"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Удалить поле"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete"
msgstr "Удалить"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Ярлык поля"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Имя поля на странице редактирования"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Имя поля"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Допускаются буквы, цифры, а также символы _ и -"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Тип поля"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Инструкции"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Инструкции, которые отображаются при редактировании"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Обязательное"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Атрибуты"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "ширина"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Закрыть поле"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Сортировка"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:428
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Label"
msgstr "Ярлык"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:610
msgid "Name"
msgstr "Имя"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Ключ"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Тип"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Нет полей. Нажмите на кнопку <strong>+ Добавить поле</strong>, чтобы создать "
"свое первое поле."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Добавить поле"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Условия"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Создайте набор правил для указания страниц, где следует отображать группу "
"полей"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Стиль отображения"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Стандартный"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Минимальный"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Расположение группы полей"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Вверху под заголовком"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Внизу после содержимого"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "На боковой панели"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Расположение меток"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Вверху"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Слева"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Расположение подсказок"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Под метками"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Под полями"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Порядковый номер"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr ""
"Если на одной странице одновременно выводятся несколько групп полей, то они "
"сортируются по порядковому номеру в порядке возрастания"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Отображаемое описание в списке групп"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Ссылка"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Текстовый редактор"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Цитата"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Обсуждение"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Комментарии"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Редакции"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Ярлык"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Автор"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Формат"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Атрибуты страницы"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:688
msgid "Featured Image"
msgstr "Миниатюра записи"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Рубрики"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Метки"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Отправить обратные ссылки"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Скрывание блоков"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"Выберите блоки, которые необходимо <b>скрыть</b> на странице редактирования."

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Если на странице редактирования присутствует несколько групп полей, то будут "
"использованы настройки первой из них (с наиболее низким значением порядка "
"очередности)"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Отображать группу полей, если"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Обновить сайты"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Обновление базы данных Advanced Custom Fields"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Следующие сайты требуют обновления базы данных. Выберите сайты для "
"обновления и нажмите %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Сайт"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Сайт требует обновления базы данных с %s на %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Сайт обновлен"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Обновление базы данных закончено. <a href=\"%s\">Вернуться к панели "
"управления сетью</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Мы настоятельно рекомендуем сделать резервную копию базы данных перед "
"началом работы. Вы уверены, что хотите запустить обновление сейчас?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Обновление завершено"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Обновление данных до версии %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Повторитель"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Гибкое содержание"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Галерея"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Страница с опциями"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Необходимо обновление базы данных"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Благодарим вас за обновление до %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Прежде чем вы начнете использовать классные новые функции, обновите "
"пожалуйста базу данных до последней версии."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Пожалуйста, убедитесь, что любые премиум-дополнения (%s) были предварительно "
"обновлены до последней версии."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Чтения задач обновления..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Обновление базы данных завершено. Ознакомьтесь со <a href=\"%s\">списком "
"изменений</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Загрузить и установить"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Установлено"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Добро пожаловать в Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Спасибо за обновление! ACF %s стал больше и лучше. Надеемся, что вам "
"понравится."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "Максимум удобства и возможностей"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Больше комфорта"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Благодаря популярной библиотеке Select2 мы повысили удобство и скорость "
"работы многих типов полей, таких как Объект записи, Ссылка на страницу, "
"Таксономия и Выбор."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Больше дизайна"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Многие поля поменяли свой внешний вид, чтобы сделать ACF действительно "
"красивым. Значительные изменения коснулись полей Галерея, Взаимоотношение и "
"oEmbed (новое поле)!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Больше данных"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Новая архитектура позволяет вложенным полям существовать независимо от "
"родительских. Просто перетаскивайте их из одного родительского поля в другое."

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Забудьте про дополнения. Встречайте PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Знакомство с ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Мы кардинально упрощаем внедрение премиального функционала!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Все 4 дополнения Premium включены в новой <a href=\"%s\">Pro-версии ACF</a> "
"и в лицензии разработчика, и в персональной лицензии. Еще никогда функционал "
"Premium не был так доступен!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Впечатляющий функционал"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO содержит ряд мощных инструментов, таких как Повторяющиеся данные, "
"Гибкое содержание и Галерея. Также есть возможность создавать дополнительные "
"страницы настроек в панели администратора."

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Узнайте больше о <a href=\"%s\">возможностях ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Простое обновление"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Для перехода на ACF PRO просто <a href=\"%s\">авторизуйтесь личном кабинете</"
"a> и получите бесплатную лицензию!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Мы также подготовили <a href=\"%s\">руководство по переходу</a>, чтобы "
"ответить на все ваши вопросы. Но если все же они появятся, свяжитесь с нашей "
"командой поддержки через <a href=\"%s\">систему помощи</a>."

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Что под капотом"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Умные настройки полей"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF теперь сохраняет настройки поля как отдельный объект записи"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Больше AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr "Поиск на AJAX в полях значительно ускоряет загрузку страниц"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Локальный JSON"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Новый автоматический экспорт в JSON повышает скорость работы"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Контроль версий"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"Новый автоматический экспорт в JSON позволяет контролировать версию настроек "
"полей"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "Swapped XML для JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Импорт / Экспорт теперь использует JSON вместо XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Новые формы"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Поля теперь могут быть отображены в комментариях, виджетах и "
"пользовательских формах!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Добавлено новое поле для встраиваемого контента"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Новая галерея"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Поле галереи претерпело столь необходимое визуальное преображение"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Новые настройки"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"В настройках группы полей теперь можно изменять расположение меток и "
"подсказок"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Улучшенные формы"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() теперь может создавать новую запись о представлении"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Улучшенное подтверждение"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"Подтверждение форм теперь происходит через PHP + AJAX вместо простого JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Взаимоотношение"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Новая настройка поля Взаимоотношения для Фильтров (Поиск, Тип записи, "
"Таксономия)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Перемещение полей"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Новый функционал групп полей позволяет перемещать поля между группами и "
"родительскими полями"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Ссылка на страницу"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Новая группа архивов в выборе поля page_link"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Страницы настроек"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Новые функции для страницы настроек позволяют создавать и родительские, и "
"дочерние меню"

#: includes/admin/views/settings-info.php:157
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Думаем, вам понравятся изменения в %s."

#: includes/api/api-helpers.php:1028
msgid "Thumbnail"
msgstr "Миниатюра"

#: includes/api/api-helpers.php:1029
msgid "Medium"
msgstr "Средний"

#: includes/api/api-helpers.php:1030
msgid "Large"
msgstr "Большой"

#: includes/api/api-helpers.php:1079
msgid "Full Size"
msgstr "Полный"

#: includes/api/api-helpers.php:1321 includes/api/api-helpers.php:1894
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(нет заголовка)"

#: includes/api/api-helpers.php:3976
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Изображение не должно быть уже чем %d пикселей."

#: includes/api/api-helpers.php:3981
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Изображение не должно быть шире чем %d пикселей."

#: includes/api/api-helpers.php:3997
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Изображение должно иметь высоту как минимум %d пикселей."

#: includes/api/api-helpers.php:4002
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Изображение должно иметь высоту не более чем %d пикселей."

#: includes/api/api-helpers.php:4020
#, php-format
msgid "File size must be at least %s."
msgstr "Размер файла должен быть не менее чем %s."

#: includes/api/api-helpers.php:4025
#, php-format
msgid "File size must must not exceed %s."
msgstr "Размер файла должен быть не более чем %s."

#: includes/api/api-helpers.php:4059
#, php-format
msgid "File type must be %s."
msgstr "Файл должен иметь тип: %s."

#: includes/assets.php:172
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Внесенные вами изменения будут утеряны, если вы покинете эту страницу"

#: includes/assets.php:175 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Выбрать"

#: includes/assets.php:176
msgctxt "verb"
msgid "Edit"
msgstr "Изменить"

#: includes/assets.php:177
msgctxt "verb"
msgid "Update"
msgstr "Обновить"

#: includes/assets.php:178
msgid "Uploaded to this post"
msgstr "Загружено для этой записи"

#: includes/assets.php:179
msgid "Expand Details"
msgstr "Показать детали"

#: includes/assets.php:180
msgid "Collapse Details"
msgstr "Скрыть детали"

#: includes/assets.php:181
msgid "Restricted"
msgstr "Ограничено"

#: includes/assets.php:182 includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Все изображения"

#: includes/assets.php:185
msgid "Validation successful"
msgstr "Проверка успешно выполнена"

#: includes/assets.php:186 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Проверка не пройдена"

#: includes/assets.php:187
msgid "1 field requires attention"
msgstr "1 поле требует вашего внимания"

#: includes/assets.php:188
#, php-format
msgid "%d fields require attention"
msgstr "%d полей требуют вашего внимания"

#: includes/assets.php:191
msgid "Are you sure?"
msgstr "Вы уверены?"

#: includes/assets.php:192 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Да"

#: includes/assets.php:193 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Нет"

#: includes/assets.php:194 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:141
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "Убрать"

#: includes/assets.php:195
msgid "Cancel"
msgstr "Отмена"

#: includes/assets.php:198
msgid "Has any value"
msgstr "заполнено"

#: includes/assets.php:199
msgid "Has no value"
msgstr "пустое"

#: includes/assets.php:200
msgid "Value is equal to"
msgstr "равно"

#: includes/assets.php:201
msgid "Value is not equal to"
msgstr "не равно"

#: includes/assets.php:202
msgid "Value matches pattern"
msgstr "соответствует выражению"

#: includes/assets.php:203
msgid "Value contains"
msgstr "содержит"

#: includes/assets.php:204
msgid "Value is greater than"
msgstr "больше чем"

#: includes/assets.php:205
msgid "Value is less than"
msgstr "меньше чем"

#: includes/assets.php:206
msgid "Selection is greater than"
msgstr "выбрано больше чем"

#: includes/assets.php:207
msgid "Selection is less than"
msgstr "выбрано меньше чем"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Тип поля не существует"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Неизвестно"

#: includes/fields.php:349
msgid "Basic"
msgstr "Основное"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Содержание"

#: includes/fields.php:351
msgid "Choice"
msgstr "Выбор"

#: includes/fields.php:352
msgid "Relational"
msgstr "Отношение"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:567
#: pro/fields/class-acf-field-flexible-content.php:616
#: pro/fields/class-acf-field-repeater.php:443
msgid "Layout"
msgstr "Блок"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Аккордеон"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Развернуто"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Отображать в развернутом виде при загрузке страницы"

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Разворачивание нескольких секций"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Разрешить одновременное разворачивание нескольких секций"

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Разделитель"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Определяет конечную точку предыдущего аккордеона. Данный аккордеон будет "
"невидим."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Группа кнопок"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:359
msgid "Choices"
msgstr "Варианты"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "Enter each choice on a new line."
msgstr "Введите каждый вариант выбора на новую строку."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Для большего контроля, вы можете ввести значение и ярлык по следующему "
"формату:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "red : Red"
msgstr "red : Красный"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:409
msgid "Allow Null?"
msgstr "Разрешить пустое значение?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:146
#: includes/fields/class-acf-field-select.php:368
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Default Value"
msgstr "Значение по умолчанию"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:147
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:398
msgid "Appears when creating a new post"
msgstr "Заполняется при создании новой записи"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Горизонтальная"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Вертикальная"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "Возвращаемое значение"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Укажите возвращаемое значение для поля"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:427
msgid "Value"
msgstr "Значение"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:429
msgid "Both (Array)"
msgstr "Оба (массив)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "Флажок (checkbox)"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Выбрать все"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Добавить новый вариант"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Разрешить пользовательские"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Разрешить добавление пользовательских вариантов"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Сохранить пользовательские"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Сохранить пользовательские варианты в настройках поля"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each default value on a new line"
msgstr "Введите каждое значение на новую строку."

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Переключить"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Добавить чекбокс для переключения всех чекбоксов"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Цвет"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Очистить"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "По умолчанию"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Выберите цвет"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Текущий цвет"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Дата"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Сегодня"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Дальше"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Назад"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Неделя"

#: includes/fields/class-acf-field-date_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Отображаемый формат"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Формат во время редактирования поля"

#: includes/fields/class-acf-field-date_picker.php:189
#: includes/fields/class-acf-field-date_picker.php:220
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Пользовательский:"

#: includes/fields/class-acf-field-date_picker.php:199
msgid "Save Format"
msgstr "Формат сохраняемого значения"

#: includes/fields/class-acf-field-date_picker.php:200
msgid "The format used when saving a value"
msgstr "Формат для сохранения в базе данных"

#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:715
#: includes/fields/class-acf-field-select.php:422
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:428
msgid "Return Format"
msgstr "Возвращаемый формат"

#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Формат возвращаемого значения"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "День начала недели"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Дата и время"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Выберите время"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Время"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Час"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Минута"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Секунда"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Миллисекунда"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Микросекунда"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Часовой пояс"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Сейчас"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Выбрать"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "ДП"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "Д"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "ПП"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "П"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Текст заглушки"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Появляется перед полем ввода"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:185
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Текст перед полем"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:186
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Текст перед полем ввода"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:194
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Текст после поля"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:195
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Текст после поля ввода"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Файл"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Изменить файл"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Обновить файл"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Имя файла"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:294
#: pro/fields/class-acf-field-gallery.php:705
#: pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "Размер файла"

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Добавить файл"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Массив"

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "Ссылка на файл"

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "ID файла"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "Библиотека"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "Ограничение количества выбранных элементов"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:236
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "Все"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:237
#: pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "Загружено в запись"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:244
#: pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "Минимум"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "Ограничить файлы, которые могут быть загружены"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "Максимум"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "Допустимые типы файлов"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Для разделения типов файлов используйте запятые. Оставьте поле пустым для "
"разрешения загрузки всех файлов"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Расположение на карте"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Извините, но ваш браузер не поддерживает определение местоположения"

#: includes/fields/class-acf-field-google-map.php:165
msgid "Clear location"
msgstr "Очистить местоположение"

#: includes/fields/class-acf-field-google-map.php:166
msgid "Find current location"
msgstr "Определить текущее местоположение"

#: includes/fields/class-acf-field-google-map.php:169
msgid "Search for address..."
msgstr "Поиск по адресу..."

#: includes/fields/class-acf-field-google-map.php:199
#: includes/fields/class-acf-field-google-map.php:210
msgid "Center"
msgstr "Центрировать"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-google-map.php:211
msgid "Center the initial map"
msgstr "Центрировать изначальную карту"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Zoom"
msgstr "Масштаб"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Set the initial zoom level"
msgstr "Укажите начальный масштаб"

#: includes/fields/class-acf-field-google-map.php:232
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:696
#: pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "Высота"

#: includes/fields/class-acf-field-google-map.php:233
msgid "Customise the map height"
msgstr "Настройка высоты карты"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Группа"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:379
msgid "Sub Fields"
msgstr "Вложенные поля"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Укажите способ отображения клонированных полей"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:627
#: pro/fields/class-acf-field-repeater.php:451
msgid "Block"
msgstr "Блок"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:626
#: pro/fields/class-acf-field-repeater.php:450
msgid "Table"
msgstr "Таблица"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:452
msgid "Row"
msgstr "Строка"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Изображение"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Выбрать изображение"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Редактировать изображение"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Обновить изображение"

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Изображение не выбрано"

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Добавить изображение"

#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr "Массив изображения"

#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr "Ссылка на изображение"

#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr "ID изображения"

#: includes/fields/class-acf-field-image.php:220
msgid "Preview Size"
msgstr "Размер изображения"

#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr "Размер отображаемого изображения при редактировании"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:685
#: pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "Ограничить изображения, которые могут быть загружены"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "Ширина"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Ссылка"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Выберите ссылку"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Откроется на новой вкладке"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Массив ссылок"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL ссылки"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Сообщение"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Перевод строк"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Способ перевода строк"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Автоматически добавлять параграфы"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Автоматически добавлять &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Без форматирования"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Очистка HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Преобразовывать HTML-теги в соответствующие комбинации символов для "
"отображения в виде текста"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Число"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:155
msgid "Minimum Value"
msgstr "Минимальное значение"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:165
msgid "Maximum Value"
msgstr "Максимальное значение"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:175
msgid "Step Size"
msgstr "Шаг изменения"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Значение должно быть числом"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Значение должно быть равным или больше чем %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Значение должно быть равным или меньшим чем %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "Медиа"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Введите адрес ссылки"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Размер медиа"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Архивы"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr "Родитель"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:641
msgid "Filter by Post Type"
msgstr "Фильтрация по типу записей"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:649
msgid "All post types"
msgstr "Все типы записей"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:655
msgid "Filter by Taxonomy"
msgstr "Фильтрация по таксономии"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:663
msgid "All taxonomies"
msgstr "Все таксономии"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Разрешить ссылки на архивы"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:387
#: includes/fields/class-acf-field-user.php:419
msgid "Select multiple values?"
msgstr "Выбрать несколько значений?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Пароль"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:720
msgid "Post Object"
msgstr "Объект записи"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:721
msgid "Post ID"
msgstr "ID записи"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Переключатель (radio)"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Другое"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Выберите значение \"Другое\", чтобы разрешить настраиваемые значения"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Сохранить значения"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Сохранить настраиваемые значения для поля выбора"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Диапазон"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Записи"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "Максимальное количество значений достигнуто ({max} значений)"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "Загрузка"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Совпадения не найдены"

#: includes/fields/class-acf-field-relationship.php:441
msgid "Select post type"
msgstr "Выберите тип записи"

#: includes/fields/class-acf-field-relationship.php:467
msgid "Select taxonomy"
msgstr "Выберите таксономию"

#: includes/fields/class-acf-field-relationship.php:557
msgid "Search..."
msgstr "Поиск..."

#: includes/fields/class-acf-field-relationship.php:669
msgid "Filters"
msgstr "Фильтры"

#: includes/fields/class-acf-field-relationship.php:675
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Тип записи"

#: includes/fields/class-acf-field-relationship.php:676
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Таксономия"

#: includes/fields/class-acf-field-relationship.php:683
msgid "Elements"
msgstr "Элементы"

#: includes/fields/class-acf-field-relationship.php:684
msgid "Selected elements will be displayed in each result"
msgstr "Выбранные элементы будут отображены в каждом результате"

#: includes/fields/class-acf-field-relationship.php:695
msgid "Minimum posts"
msgstr "Минимум записей"

#: includes/fields/class-acf-field-relationship.php:704
msgid "Maximum posts"
msgstr "Максимум записей"

#: includes/fields/class-acf-field-relationship.php:808
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s требует выбрать как минимум %s значение"
msgstr[1] "%s требует выбрать как минимум %s значения"
msgstr[2] "%s требует выбрать как минимум %s значений"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr "Выбор (select)"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Доступно одно значение, нажмите Enter для его выбора."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d значений доступно, используйте клавиши вверх и вниз для навигации."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Подходящие значения не найдены"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Пожалуйста, введите 1 символ или больше"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Пожалуйста, введите %d или больше символов"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Пожалуйста, удалите 1 символ"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Пожалуйста, удалите %d символов"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Вы можете выбрать только одно значение"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Вы можете выбрать только %d значений"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Загрузка других значений&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Поиск&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Не получилось загрузить"

#: includes/fields/class-acf-field-select.php:397
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Стилизованный интерфейс"

#: includes/fields/class-acf-field-select.php:407
msgid "Use AJAX to lazy load choices?"
msgstr "Использовать AJAX для загрузки вариантов выбора?"

#: includes/fields/class-acf-field-select.php:423
msgid "Specify the value returned"
msgstr "Укажите возвращаемое значение"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Разделитель"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Вкладка"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Расположение"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr "Используйте это поле в качестве разделителя между группами вкладок"

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Нет %s [нет терминов]"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "Выберите таксономию для отображения"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "Отображение"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "Выберите способ отображения поля"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "Несколько значений"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "Множественный выбор"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "Одно значение"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "Радио-кнопки"

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "Создание терминов"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Разрешнить создавать новые термины во время редактирования"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "Сохранение терминов"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "Связать выбранные термины с записью"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "Загрузить термины"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Загрузить значения из терминов записей"

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "Объект термина"

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "ID термина"

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "У пользователя нет возможности добавить новый %s"

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s уже существует"

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "%s добавлен"

#: includes/fields/class-acf-field-taxonomy.php:973
msgid "Add"
msgstr "Добавить"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Ограничение количества символов"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Оставьте пустым для снятия ограничений"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Область текста"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Строки"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Укажите высоту поля ввода"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Время"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Да / Нет"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Отображать текст рядом с переключателем"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Включено"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Текст в активном состоянии"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Выключено"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Текст в выключенном состоянии"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Ссылка"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Значение должно быть корректной ссылкой"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Пользователь"

#: includes/fields/class-acf-field-user.php:394
msgid "Filter by role"
msgstr "Фильтровать по группе"

#: includes/fields/class-acf-field-user.php:402
msgid "All user roles"
msgstr "Все группы пользователей"

#: includes/fields/class-acf-field-user.php:433
msgid "User Array"
msgstr "Массив с данными"

#: includes/fields/class-acf-field-user.php:434
msgid "User Object"
msgstr "Объект пользователя"

#: includes/fields/class-acf-field-user.php:435
msgid "User ID"
msgstr "ID пользователя"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Редактор WordPress"

#: includes/fields/class-acf-field-wysiwyg.php:346
msgid "Visual"
msgstr "Визуально"

#: includes/fields/class-acf-field-wysiwyg.php:347
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-wysiwyg.php:353
msgid "Click to initialize TinyMCE"
msgstr "Нажмите для запуска TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:406
msgid "Tabs"
msgstr "Вкладки"

#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Visual & Text"
msgstr "Визуально и текст"

#: includes/fields/class-acf-field-wysiwyg.php:412
msgid "Visual Only"
msgstr "Только визуальный редактор"

#: includes/fields/class-acf-field-wysiwyg.php:413
msgid "Text Only"
msgstr "Только текстовый редактор"

#: includes/fields/class-acf-field-wysiwyg.php:420
msgid "Toolbar"
msgstr "Панель инструментов"

#: includes/fields/class-acf-field-wysiwyg.php:435
msgid "Show Media Upload Buttons?"
msgstr "Кнопки загрузки медиа"

#: includes/fields/class-acf-field-wysiwyg.php:445
msgid "Delay initialization?"
msgstr "Отложенная инициализация"

#: includes/fields/class-acf-field-wysiwyg.php:446
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE не будет инициализирован до клика по полю"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:301
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Редактировать группу полей"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Проверка Email"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:588 pro/options-page.php:81
msgid "Update"
msgstr "Обновить"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Запись обновлена"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Обнаружен спам"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Запись"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Страница"

#: includes/locations.php:96
msgid "Forms"
msgstr "Формы"

#: includes/locations.php:247
msgid "is equal to"
msgstr "равно"

#: includes/locations.php:248
msgid "is not equal to"
msgstr "не равно"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Медиафайл"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Все %s форматы"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Комментарий"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Группа текущего пользователя"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Администратор"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Текущий пользователь"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Авторизирован"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Просматривает лицевую часть сайта"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Просматривает административную панель"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Пункт меню"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Меню"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Расположение меню"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Меню"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Родитель страницы"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Шаблон страницы"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Шаблон по умолчанию"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Тип страницы"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Главная страница"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Страница записей"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Страница верхнего уровня (без родителя)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Родительская страница (есть дочерние страницы)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Дочерняя страница (есть родительские страницы)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Рубрика записи"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Формат записи"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Статус записи"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Таксономия записи"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Шаблон записи"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Пользователь"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Администратор или редактор"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Обычный пользователь"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Группа пользователя"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Виджет"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s значение требуется"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Опубликовано"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"С этой страницей настроек не связаны группы полей. <a href=\"%s\">Создать "
"группу полей</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Ошибка</b>. Не удалось подключиться к серверу обновлений"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Обновление"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Деактивировать лицензию"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Активировать лицензию"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Информация о лицензии"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Для разблокирования обновлений введите лицензионный ключ ниже. Если у вас "
"его нет, то ознакомьтесь с <a href=\"%s\" target=\"_blank\">деталями</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Номер лицензии"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Обновления"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Текущая версия"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Последняя версия"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Обновления доступны"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Обновить плагин"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Пожалуйста введите ваш номер лицензии для разблокировки обновлений"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Проверить еще раз"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Замечания по обновлению"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Клон"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Выберите одно или несколько полей, которые вы хотите клонировать"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Способ отображения"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Выберите стиль отображения клонированных полей"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Группа (сгруппировать выбранные поля в одно и выводить вместо текущего)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Отдельно (выбранные поля выводятся отдельно вместо текущего)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Ярлыки будут отображаться как %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Префикс для ярлыков полей"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Значения будут сохранены как %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Префикс для названий полей"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Неизвестное поле"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Неизвестная группа полей"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Все поля группы %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:463
msgid "Add Row"
msgstr "Добавить"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:938
#: pro/fields/class-acf-field-flexible-content.php:1020
msgid "layout"
msgid_plural "layouts"
msgstr[0] "макет"
msgstr[1] "макета"
msgstr[2] "макетов"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "макеты"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:937
#: pro/fields/class-acf-field-flexible-content.php:1019
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Это поле требует как минимум {min} {label}  {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Это поле ограничено {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} доступно (максимум {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} требуется (минимум {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Для гибкого содержания требуется как минимум один макет"

#: pro/fields/class-acf-field-flexible-content.php:302
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Нажмите на кнопку \"%s\" ниже для начала создания собственного макета"

#: pro/fields/class-acf-field-flexible-content.php:427
msgid "Add layout"
msgstr "Добавить макет"

#: pro/fields/class-acf-field-flexible-content.php:428
msgid "Remove layout"
msgstr "Удалить макет"

#: pro/fields/class-acf-field-flexible-content.php:429
#: pro/fields/class-acf-field-repeater.php:296
msgid "Click to toggle"
msgstr "Нажмите для переключения"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder Layout"
msgstr "Переместить макет"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder"
msgstr "Переместить"

#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete Layout"
msgstr "Удалить макет"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate Layout"
msgstr "Дублировать макет"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New Layout"
msgstr "Добавить новый макет"

#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Min"
msgstr "Минимум"

#: pro/fields/class-acf-field-flexible-content.php:656
msgid "Max"
msgstr "Максимум"

#: pro/fields/class-acf-field-flexible-content.php:683
#: pro/fields/class-acf-field-repeater.php:459
msgid "Button Label"
msgstr "Текст кнопки добавления"

#: pro/fields/class-acf-field-flexible-content.php:692
msgid "Minimum Layouts"
msgstr "Мин. количество блоков"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Макс. количество блоков"

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr "Добавление изображений в галерею"

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr "Выбрано максимальное количество изображений"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Длина"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "Подпись"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "Текст в ALT"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Добавить изображения"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Сортировка"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "По дате загрузки"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "По дате изменения"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "По названию"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Инвертировать"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Закрыть"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Мин. количество изображений"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Макс. количество изображений"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "Добавить"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "Укажите куда добавлять новые вложения"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "Добавлять в конец"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "Добавлять в начало"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:656
msgid "Minimum rows reached ({min} rows)"
msgstr "Достигнуто минимальное количество ({min} элементов)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Достигнуто максимальное количество ({max} элементов)"

#: pro/fields/class-acf-field-repeater.php:333
msgid "Add row"
msgstr "Добавить"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Remove row"
msgstr "Удалить"

#: pro/fields/class-acf-field-repeater.php:412
msgid "Collapsed"
msgstr "Сокращенный заголовок"

#: pro/fields/class-acf-field-repeater.php:413
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Выберите поле, которое будет отображаться в качестве заголовка при "
"сворачивании блока"

#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum Rows"
msgstr "Мин. количество элементов"

#: pro/fields/class-acf-field-repeater.php:433
msgid "Maximum Rows"
msgstr "Макс. количество элементов"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Страницы с настройками отсуствуют"

#: pro/options-page.php:51
msgid "Options"
msgstr "Опции"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Настройки были обновлены"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Для разблокировки обновлений введите ваш лицензионный ключ на странице <a "
"href=\"%s\">Обновление</a>. Если у вас его нет, то ознакомьтесь с <a href="
"\"%s\" target=\"_blank\">деталями</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Эллиот Кондон"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Parent fields"
#~ msgstr "Родительские поля"

#~ msgid "Sibling fields"
#~ msgstr "Поля одного уровня вложенности"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Экспортировать группы полей в PHP"

#~ msgid "Download export file"
#~ msgstr "Загрузить файл"

#~ msgid "Generate export code"
#~ msgstr "Генерировать код"

#~ msgid "Import"
#~ msgstr "Импорт"

#~ msgid "Locating"
#~ msgstr "Определение местоположение"

#~ msgid "Error."
#~ msgstr "Ошибка."

#~ msgid "No embed found for the given URL."
#~ msgstr "По указанной вами ссылке медиаконтент не обнаружен."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Минимальное количество значений достигнуто ({min} значений)"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Вкладка может отображаться неправильно при добавлении в поля гибкого "
#~ "содержания и повторителя в табличном стиле"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Используйте вкладки для лучшей организации редактирования групп полей."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Все поля после поля со вкладкой группируются на отдельной вкладке с "
#~ "соответствующим названием."

#~ msgid "None"
#~ msgstr "Ничего"

#~ msgid "Taxonomy Term"
#~ msgstr "Таксономия"

#~ msgid "remove {layout}?"
#~ msgstr "удалить {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Это поле требует как минимум {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Максимальное ограничение {label} достигнуто ({max} {identifier})"

#~ msgid "Getting Started"
#~ msgstr "Приступаем к работе"

#~ msgid "Field Types"
#~ msgstr "Типы полей"

#~ msgid "Functions"
#~ msgstr "Функции"

#~ msgid "Actions"
#~ msgstr "Действия"

#~ msgid "Features"
#~ msgstr "Возможности"

#~ msgid "How to"
#~ msgstr "Гайды"

#~ msgid "Tutorials"
#~ msgstr "Уроки и туториалы"

#~ msgid "FAQ"
#~ msgstr "Вопросы и ответы"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr ""
#~ "Метаданные для терминов не удалось обновить (таблица termmeta не "
#~ "существует)"

#~ msgid "Error"
#~ msgstr "Ошибка"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "%d поле требует внимания."
#~ msgstr[1] "%d поля требует внимания."
#~ msgstr[2] "%d полей требует внимания."

#~ msgid ""
#~ "Error validating ACF PRO license URL (website does not match). Please re-"
#~ "activate your license"
#~ msgstr ""
#~ "Ошибка при проверке лицензии ACF PRO (адрес сайта не совпадает). "
#~ "Пожалуйста, переактивируйте лицензию"

#~ msgid "Customise WordPress with powerful, professional and intuitive fields"
#~ msgstr ""
#~ "Плагин для упрощения настройки и взаимодействия с дополнительными полями "
#~ "для содержимого"

#~ msgid "Disabled"
#~ msgstr "Отключено"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Отключено <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Отключено <span class=\"count\">(%s)</span>"
#~ msgstr[2] "Отключено <span class=\"count\">(%s)</span>"

#~ msgid "'How to' guides"
#~ msgstr "Руководства \"Как...\""

#~ msgid "Created by"
#~ msgstr "Создано"

#~ msgid "Error loading update"
#~ msgstr "Возникла ошибка при загрузке обновления"

#~ msgid "See what's new"
#~ msgstr "Посмотрите, что изменилось"

#~ msgid "eg. Show extra content"
#~ msgstr "Пример: Отображать дополнительное содержание"

#~ msgid ""
#~ "Error validating license URL (website does not match). Please re-activate "
#~ "your license"
#~ msgstr ""
#~ "Во время проверки лицензии, которая связана с адресом сайта, возникла "
#~ "ошибка. Пожалуйста, выполните активацию снова"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Импорт успешно завершен</b>. Было добавлено %s групп  полей: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Предупреждение</b>. Было обнаружено %s групп полей, которые уже "
#~ "существуют и были пропущены: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Обновить ACF"

#~ msgid "Upgrade"
#~ msgstr "Обновить"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "Следующие сайты требуют обновления базы данных. Выберите необходимые и "
#~ "нажмите на кнопку \"Обновить базу данных\""

#~ msgid "Select"
#~ msgstr "Выбор"

#~ msgid "Done"
#~ msgstr "Готово"

#~ msgid "Today"
#~ msgstr "Сегодня"

#~ msgid "Show a different month"
#~ msgstr "Показать другой месяц"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Ошибка подключения</b>. Извините, попробуйте еще раз"

#~ msgid "See what's new in"
#~ msgstr "Узнайте, что нового в"

#~ msgid "version"
#~ msgstr "версии"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Перетащите поле для смены очередности"

#~ msgid "Return format"
#~ msgstr "Возвращаемый формат"

#~ msgid "uploaded to this post"
#~ msgstr "загружено для этой записи"

#~ msgid "File Name"
#~ msgstr "Имя файла"

#~ msgid "File Size"
#~ msgstr "Размер файла"

#~ msgid "No File selected"
#~ msgstr "Файл не выбран"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr "Пожалуйста, заметьте, что весь текст пройдет через WP функцию"

#~ msgid "Warning"
#~ msgstr "Предупреждение"

#~ msgid "Save Options"
#~ msgstr "Сохранить настройки"

#~ msgid "License"
#~ msgstr "Лицензия"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Для раблокировки обновлений введите ваш номер лицензии ниже. Если у вас "
#~ "его нет, то ознакомьтесь с рекомендациями"

#~ msgid "details & pricing"
#~ msgstr "детали и цены"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Для получения обновлений введите номер лицензии на странице <a href=\"%s"
#~ "\">Обновление</a>. Вы можете его или приобрести на <a href=\"%s\">сайте "
#~ "автора плагина</a>."

#~ msgid "Field&nbsp;Groups"
#~ msgstr "Группы полей"

#~ msgid "Hide / Show All"
#~ msgstr "Скрыть / Показать все"

#~ msgid "Show Field Keys"
#~ msgstr "Показать ключи полей"

#~ msgid "Pending Review"
#~ msgstr "На утверждении"

#~ msgid "Draft"
#~ msgstr "Черновик"

#~ msgid "Future"
#~ msgstr "Отложенная публикация"

#~ msgid "Private"
#~ msgstr "Частная"

#~ msgid "Revision"
#~ msgstr "Редакция"

#~ msgid "Trash"
#~ msgstr "Корзина"

#~ msgid "Top Level Page (parent of 0)"
#~ msgstr "Самая верхняя страница (родитель 0)"

#~ msgid "Import / Export"
#~ msgstr "Импорт и экспорт"

#~ msgid "Logged in User Type"
#~ msgstr "Тип пользователя"

#~ msgid "Field groups are created in order <br />from lowest to highest"
#~ msgstr ""
#~ "Порядок отображения полей, начиная с самого меньшего значения и "
#~ "заканчивая самым большим"

#~ msgid "<b>Select</b> items to <b>hide</b> them from the edit screen"
#~ msgstr ""
#~ "<b>Выберите</b> элементы, которые необходимо <b>скрыть</b> на экране "
#~ "редактирования."

#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field "
#~ "group's options will be used. (the one with the lowest order number)"
#~ msgstr ""
#~ "Если на экране редактирования выводятся несколько групп полей, то группа "
#~ "c меньшим значением порядка очередности будет отображаться выше"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exiting "
#~ "way!"
#~ msgstr "Мы поменяли способ представления возможностей Premium!"

#~ msgid "ACF PRO Required"
#~ msgstr "Необходим ACF PRO"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "Мы обнаружили ситуацию, требующую вашего внимания: Этот сайт использует "
#~ "дополнения Premium (%s), которые больше не поддерживаются ACF."

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "Не волнуйтесь, вы можете просто откатить плагин и продолжить использовать "
#~ "знакомый вам ACF."

#~ msgid "Roll back to ACF v%s"
#~ msgstr "Вернуться к ACF  v%s"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "Узнать, почему ACF PRO необходим моему сайту"

#~ msgid "Update Database"
#~ msgstr "Обновление базы данных"

#~ msgid "Data Upgrade"
#~ msgstr "Обновление данных"

#~ msgid "Data upgraded successfully."
#~ msgstr "Данные успешно обновлены."

#~ msgid "Data is at the latest version."
#~ msgstr "Версия данных является последней."

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "%s обязательное поле не заполнено"
#~ msgstr[1] "%s обязательных поля не заполнено"
#~ msgstr[2] "%s обязательных полей не заполнено"

#~ msgid "No taxonomy filter"
#~ msgstr "Фильтрация по таксономии отсутствует"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Загрузить и сохранить термины в запись"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr ""
#~ "Загрузить значение основываясь на терминах записи и обновить термины "
#~ "записи при сохранении."

#~ msgid "Attachment Details"
#~ msgstr "Информация о вложении"

#~ msgid "Custom field updated."
#~ msgstr "Произвольное поле обновлено."

#~ msgid "Custom field deleted."
#~ msgstr "Произвольное поле удалено."

#~ msgid "Field group restored to revision from %s"
#~ msgstr "Группа полей восстановлена из редакции %s"

#~ msgid "Full"
#~ msgstr "Полный"

#~ msgid "No ACF groups selected"
#~ msgstr "Группы ACF не выбраны"

#~ msgid "Repeater Field"
#~ msgstr "Повторающееся поле"

#~ msgid ""
#~ "Create infinite rows of repeatable data with this versatile interface!"
#~ msgstr "Создавайте повторающиеся поля с этим многофунциональным аддоном!"

#~ msgid "Gallery Field"
#~ msgstr "Поле галереи"

#~ msgid "Create image galleries in a simple and intuitive interface!"
#~ msgstr "Создавайте галереи с этим простым и интуитивным интерфейсом!"

#~ msgid "Create global data to use throughout your website!"
#~ msgstr ""
#~ "Создайте глобальные данные, которые можно будет использовать по всему "
#~ "сайту."

#~ msgid "Flexible Content Field"
#~ msgstr "Гибкое содержание"

#~ msgid "Create unique designs with a flexible content layout manager!"
#~ msgstr "Создавайте уникальные дизайны с настраиваемым гибким макетом."

#~ msgid "Gravity Forms Field"
#~ msgstr "Поле \"Gravity Forms\""

#~ msgid "Creates a select field populated with Gravity Forms!"
#~ msgstr "Создает поля использующие Gravity Forms."

#~ msgid "Date & Time Picker"
#~ msgstr "Выбор даты и времени"

#~ msgid "jQuery date & time picker"
#~ msgstr "jQuery плагин выбора даты и времени"

#~ msgid "Location Field"
#~ msgstr "Поле местоположения"

#~ msgid "Find addresses and coordinates of a desired location"
#~ msgstr "Найдите адреса и координаты выбраного места."

#~ msgid "Contact Form 7 Field"
#~ msgstr "Поле \"Contact Form 7\""

#~ msgid "Assign one or more contact form 7 forms to a post"
#~ msgstr "Добавьте одно или больше форм \"Contact Form 7\" в запись."

#~ msgid "Advanced Custom Fields Add-Ons"
#~ msgstr "Расширенные произвольные поля. Аддоны"

#~ msgid ""
#~ "The following Add-ons are available to increase the functionality of the "
#~ "Advanced Custom Fields plugin."
#~ msgstr ""
#~ "Следующие аддоны могут увеличить функционал плагина \"Advanced Custom "
#~ "Fields\"."

#~ msgid ""
#~ "Each Add-on can be installed as a separate plugin (receives updates) or "
#~ "included in your theme (does not receive updates)."
#~ msgstr ""
#~ "Каждый аддон может быть установлен, как отдельный плагин (который "
#~ "обновляется), или же может быть включен в вашу тему (обновляться не "
#~ "будет)."

#~ msgid "Purchase & Install"
#~ msgstr "Купить и установить"

#~ msgid "Download"
#~ msgstr "Скачать"

#~ msgid "Select the field groups to be exported"
#~ msgstr "Выберите группы полей, которые надо экспортировать."

#~ msgid "Export to XML"
#~ msgstr "Экспортировать в XML файл"

#~ msgid "Export to PHP"
#~ msgstr "Экспортировать в PHP файл"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr "ACF создат .xml файл, который совместим с WP Import плагином."

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field "
#~ "groups. This is useful for migrating fields groups between Wp websites."
#~ msgstr ""
#~ "Импортированные группы полей <strong>появятся</strong> в списке "
#~ "редактируемых групп полей. Эта функция очень полезна в случае переезда с "
#~ "одного WP сайта на другой."

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr ""
#~ "Выберите группу(-ы) полей из списка и нажмите на кнопку \"Экспортировать "
#~ "в XML файл\"."

#~ msgid "Save the .xml file when prompted"
#~ msgstr "Сохраните .xml файл при запросе сохранить файл."

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr ""
#~ "Зайдите во \"Инструменты\" &raquo; \"Импорт\", и выберите \"WordPress\"."

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Установите WP Import плагин."

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Загрузите и импортируйте ваш экспортированный .xml файл."

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Выберите вашего пользователя и не импортируйте вложенные файлы."

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "Вот и все. Удачной работы с WordPress!"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACF создат код PHP, который можно будет включить в вашу тему."

#~ msgid ""
#~ "Registered field groups <b>will not</b> appear in the list of editable "
#~ "field groups. This is useful for including fields in themes."
#~ msgstr ""
#~ "Импортированные группы полей <strong>не появятся</strong> в списке  "
#~ "редактируемых групп полей. Данный способ удобен при необходимости "
#~ "включить поля в темы."

#~ msgid ""
#~ "Please note that if you export and register field groups within the same "
#~ "WP, you will see duplicate fields on your edit screens. To fix this, "
#~ "please move the original field group to the trash or remove the code from "
#~ "your functions.php file."
#~ msgstr ""
#~ "Пожалуйста, заметьте, если вы экспортируете а затем импортируете группы "
#~ "полей в один и тот же сайт WP, вы увидите дублированные поля на экране "
#~ "редактирования. Чтобы исправить это, перенесите оригинальную группы полей "
#~ "в корзину или удалите код из вашего \"<em>functions.php</em>\" файла."

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr ""
#~ "Выберите группу(-ы) полей из списка, затем нажмите на кнопку "
#~ "\"Экспортировать в PHP файл\"."

#~ msgid "Copy the PHP code generated"
#~ msgstr "Скопируйте сгенерированный PHP код."

#~ msgid "Paste into your functions.php file"
#~ msgstr "Вставьте его в ваш \"<em>functions.php</em>\" файл."

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "Чтобы активировать аддоны, отредактируйте и вставьте код в первые "
#~ "несколько строк."

#~ msgid "Notes"
#~ msgstr "Заметки"

#~ msgid "Include in theme"
#~ msgstr "Включить в тему"

#~ msgid ""
#~ "The Advanced Custom Fields plugin can be included within a theme. To do "
#~ "so, move the ACF plugin inside your theme and add the following code to "
#~ "your functions.php file:"
#~ msgstr ""
#~ "Плагин \"Advanced Custom Fields\" может быть включен в тему. Для этого, "
#~ "переместите плагин ACF в папку вашей темы, и добавьте следующий код в ваш "
#~ "\"<em>functions.php</em>\" файл:"

#~ msgid ""
#~ "To remove all visual interfaces from the ACF plugin, you can use a "
#~ "constant to enable lite mode. Add the following code to you functions.php "
#~ "file <b>before</b> the include_once code:"
#~ msgstr ""
#~ "Чтобы убрать весь визуальный интерфейс из плагина ACF, вы можете "
#~ "использовать константу, чтобы включить \"Режим Lite\". Добавьте следующий "
#~ "код в ваш \"<em>functions.php</em>\" файл <strong>перед</strong> "
#~ "<em>include_once</em>:"

#~ msgid "Back to export"
#~ msgstr "Вернуться к экспорту"

#~ msgid ""
#~ "/**\n"
#~ " *  Install Add-ons\n"
#~ " *  \n"
#~ " *  The following code will include all 4 premium Add-Ons in your theme.\n"
#~ " *  Please do not attempt to include a file which does not exist. This "
#~ "will produce an error.\n"
#~ " *  \n"
#~ " *  All fields must be included during the 'acf/register_fields' action.\n"
#~ " *  Other types of Add-ons (like the options page) can be included "
#~ "outside of this action.\n"
#~ " *  \n"
#~ " *  The following code assumes you have a folder 'add-ons' inside your "
#~ "theme.\n"
#~ " *\n"
#~ " *  IMPORTANT\n"
#~ " *  Add-ons may be included in a premium theme as outlined in the terms "
#~ "and conditions.\n"
#~ " *  However, they are NOT to be included in a premium / free plugin.\n"
#~ " *  For more information, please read http://www.advancedcustomfields.com/"
#~ "terms-conditions/\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " *  Установка аддонов\n"
#~ " *  \n"
#~ " *  Следующий код включит все 4 премиум аддона в вашу тему.\n"
#~ " *  Пожалуйста, не пытайтесь включить файл, который не существует. Это "
#~ "вызовет ошибку.\n"
#~ " *  \n"
#~ " *  Все поля должны быть включены во время 'acf/register_fields' "
#~ "действия.\n"
#~ " *  Другие типы аддонов (такие, как страница с опциями) могут быть "
#~ "включены вне этого действия.\n"
#~ " *  \n"
#~ " *  Следующий код предполагает, что у вас есть папка 'add-ons' в вашей "
#~ "теме.\n"
#~ " *\n"
#~ " *  ВАЖНО\n"
#~ " *  Аддоны могут быть включены в премиум темы, как указано в Правилах и "
#~ "условиях.\n"
#~ " *  Тем не менее, они не будут включены в бесплатный или премиум плагин.\n"
#~ " *  Для большей информации, пожалуйста, прочтите http://www."
#~ "advancedcustomfields.com/terms-conditions/\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " *  Register Field Groups\n"
#~ " *\n"
#~ " *  The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " *  You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " *  Регистрация группы полей\n"
#~ " *\n"
#~ " *  Функция 'register_field_group' принимает один массив, который держит "
#~ "соответственные данные, чтобы зарегистрировать группу полей.\n"
#~ " *  Вы можете редактировать этот массив, как посчитаете нужным. Однако, "
#~ "это может вызвать ошибки, если массив не совмествим с ACF.\n"
#~ " */"

#~ msgid "No field groups were selected"
#~ msgstr "Группы полей не выбраны"

#~ msgid "Show Field Key:"
#~ msgstr "Отображать ключ поля:"

#~ msgid "Vote"
#~ msgstr "Оценить"

#~ msgid "Follow"
#~ msgstr "Следить"

#~ msgid "Thank you for updating to the latest version!"
#~ msgstr "Благодарим за обновление до последней версии!"

#~ msgid ""
#~ "is more polished and enjoyable than ever before. We hope you like it."
#~ msgstr ""
#~ "еще более улучшен и интересен, чем когда либо. Мы надеемся, что вам он "
#~ "понравится."

#~ msgid "What’s New"
#~ msgstr "Что нового"

#~ msgid "Download Add-ons"
#~ msgstr "Скачать аддоны"

#~ msgid "Activation codes have grown into plugins!"
#~ msgstr "Коды активации выросли до плагинов!"

#~ msgid ""
#~ "Add-ons are now activated by downloading and installing individual "
#~ "plugins. Although these plugins will not be hosted on the wordpress.org "
#~ "repository, each Add-on will continue to receive updates in the usual way."
#~ msgstr ""
#~ "Аддоны теперь активируются скачивая и устанавливая индивидуальные "
#~ "плагины. Не смотря на то, что эти плагины не будут загружены на WordPress."
#~ "org, каждый аддон будет обновляться обычным способом."

#~ msgid "All previous Add-ons have been successfully installed"
#~ msgstr "Все предыдущие аддоны были успешно установлены."

#~ msgid "This website uses premium Add-ons which need to be downloaded"
#~ msgstr "Этот сайт использует премиум аддоны, которые должны быть скачаны."

#~ msgid "Download your activated Add-ons"
#~ msgstr "Скачайте свои активированные аддоны."

#~ msgid ""
#~ "This website does not use premium Add-ons and will not be affected by "
#~ "this change."
#~ msgstr ""
#~ "Этот сайт не использует премиум аддоны и не будет затронут этим "
#~ "изменением."

#~ msgid "Easier Development"
#~ msgstr "Упрощенная разработка"

#~ msgid "New Field Types"
#~ msgstr "Новые типы полей"

#~ msgid "Taxonomy Field"
#~ msgstr "Поле таксономии"

#~ msgid "User Field"
#~ msgstr "Поле пользователя"

#~ msgid "Email Field"
#~ msgstr "Поле email"

#~ msgid "Password Field"
#~ msgstr "Поле пароля"

#~ msgid "Custom Field Types"
#~ msgstr "Произвольные типы полей"

#~ msgid ""
#~ "Creating your own field type has never been easier! Unfortunately, "
#~ "version 3 field types are not compatible with version 4."
#~ msgstr ""
#~ "Создание собственного типа полей никогда не было проще! К сожалению, типы "
#~ "полей 3-ей версии не совместимы с версией 4."

#~ msgid "Migrating your field types is easy, please"
#~ msgstr "Миграция ваших типов полей очень проста, пожалуйста,"

#~ msgid "follow this tutorial"
#~ msgstr "следуйте этому уроку,"

#~ msgid "to learn more."
#~ msgstr "чтобы узнать больше."

#~ msgid "Actions &amp; Filters"
#~ msgstr "Действия и фильтры"

#~ msgid ""
#~ "All actions & filters have recieved a major facelift to make customizing "
#~ "ACF even easier! Please"
#~ msgstr ""
#~ "Все действия и фильтры получили крупное внешне обновление, чтобы сделать "
#~ "настраивание ACF еще более простым! Пожалуйста, "

#~ msgid "read this guide"
#~ msgstr "прочитайте этот гид,"

#~ msgid "to find the updated naming convention."
#~ msgstr "чтобы найти обновленное собрание названий."

#~ msgid "Preview draft is now working!"
#~ msgstr "Предпросмотр черновика теперь работает!"

#~ msgid "This bug has been squashed along with many other little critters!"
#~ msgstr ""
#~ "Эта ошибка была раздавленна наряду со многими другими мелкими тварями!"

#~ msgid "See the full changelog"
#~ msgstr "Посмотреть весь журнал изменений"

#~ msgid "Important"
#~ msgstr "Важно"

#~ msgid "Database Changes"
#~ msgstr "Изменения в базе данных"

#~ msgid ""
#~ "Absolutely <strong>no</strong> changes have been made to the database "
#~ "between versions 3 and 4. This means you can roll back to version 3 "
#~ "without any issues."
#~ msgstr ""
#~ "Не было абсолютно <strong>никаких</strong> изменений в базе данных между "
#~ "3-ьей и 4-ой версиями. Это значит, вы можете откатиться до 3-ьей версии "
#~ "без каких либо проблем."

#~ msgid "Potential Issues"
#~ msgstr "Потенциальные проблемы"

#~ msgid ""
#~ "Do to the sizable changes surounding Add-ons, field types and action/"
#~ "filters, your website may not operate correctly. It is important that you "
#~ "read the full"
#~ msgstr ""
#~ "В связи со значительными изменениями в аддонах, типах полей и действиях/"
#~ "фильтрах, ваш сайт может не работать корректно. Очень важно, чтобы вы "
#~ "прочитали полный гид"

#~ msgid "Migrating from v3 to v4"
#~ msgstr "Переезд с версии 3 до версии 4"

#~ msgid "guide to view the full list of changes."
#~ msgstr "для полного списка изменений."

#~ msgid "Really Important!"
#~ msgstr "Очень важно!"

#~ msgid ""
#~ "If you updated the ACF plugin without prior knowledge of such changes, "
#~ "Please roll back to the latest"
#~ msgstr ""
#~ "Если вы обновили плагин ACF без предварительных знаний об изменениях, "
#~ "пожалуйста, откатитесь до последней"

#~ msgid "version 3"
#~ msgstr "версиай 3"

#~ msgid "of this plugin."
#~ msgstr "этого плагина."

#~ msgid "Thank You"
#~ msgstr "Благодарим вас"

#~ msgid ""
#~ "A <strong>BIG</strong> thank you to everyone who has helped test the "
#~ "version 4 beta and for all the support I have received."
#~ msgstr ""
#~ "<strong>БОЛЬШОЕ</strong> спасибо всем, кто помог протестировать версию 4 "
#~ "бета и за всю поддержку, которую мне оказали."

#~ msgid "Without you all, this release would not have been possible!"
#~ msgstr "Без вас всех, этот релиз был бы невозможен!"

#~ msgid "Changelog for"
#~ msgstr "Журнал изменений по"

#~ msgid "Learn more"
#~ msgstr "Узнать больше"

#~ msgid "Overview"
#~ msgstr "Обзор"

#~ msgid ""
#~ "Previously, all Add-ons were unlocked via an activation code (purchased "
#~ "from the ACF Add-ons store). New to v4, all Add-ons act as separate "
#~ "plugins which need to be individually downloaded, installed and updated."
#~ msgstr ""
#~ "Раньше, все аддоны разблокировались с помощью когда активации (купленные "
#~ "в магазине аддонов ACF). Новинка в версии 4, все аддоны работают, как "
#~ "отдельные плагины, которые должны быть скачаны, установлены и обновлены "
#~ "отдельно."

#~ msgid ""
#~ "This page will assist you in downloading and installing each available "
#~ "Add-on."
#~ msgstr ""
#~ "Эта страница поможет вам скачать и установить каждый доступный аддон."

#~ msgid "Available Add-ons"
#~ msgstr "Доступные аддоны"

#~ msgid ""
#~ "The following Add-ons have been detected as activated on this website."
#~ msgstr "Следующие аддоны были обнаружены активированными на этом сайте."

#~ msgid "Activation Code"
#~ msgstr "Код активации"

#~ msgid "Installation"
#~ msgstr "Установка"

#~ msgid "For each Add-on available, please perform the following:"
#~ msgstr "Для каждого доступно аддона, выполните, пожалуйста, следующее:"

#~ msgid "Download the Add-on plugin (.zip file) to your desktop"
#~ msgstr "Скачайте плагин аддона (.zip файл) на ваш компьютер."

#~ msgid "Navigate to"
#~ msgstr "Перейти в"

#~ msgid "Plugins > Add New > Upload"
#~ msgstr ""
#~ "Откройте \"Плагины\" &raquo; \"Добавить новый\" &raquo; \"Загрузить\"."

#~ msgid ""
#~ "Use the uploader to browse, select and install your Add-on (.zip file)"
#~ msgstr "Найдите скачанный .zip файл, выберите его и установите."

#~ msgid ""
#~ "Once the plugin has been uploaded and installed, click the 'Activate "
#~ "Plugin' link"
#~ msgstr ""
#~ "Как только плагин будет загружен и установлен, нажмите на ссылку "
#~ "\"Активировать плагин\"."

#~ msgid "The Add-on is now installed and activated!"
#~ msgstr "Аддон теперь установлен и активирован!"

#~ msgid "Awesome. Let's get to work"
#~ msgstr "Превосходно! Приступим к работе."

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr ""
#~ "Проверка не удалась. Один или больше полей ниже обязательны к заполнению."

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "Изменение опций \"отображать на странице\" группы полей"

#~ msgid "Modifying field option 'taxonomy'"
#~ msgstr "Изменение опции \"таксономия\" поля"

#~ msgid "Moving user custom fields from wp_options to wp_usermeta'"
#~ msgstr ""
#~ "Перенос пользовательских произвольных полей из \"wp_options\" в "
#~ "\"wp_usermeta\""

#~ msgid "blue : Blue"
#~ msgstr "blue : Blue"

#~ msgid "eg: #ffffff"
#~ msgstr "Пример: #ffffff"

#~ msgid "Save format"
#~ msgstr "Сохранить формат"

#~ msgid ""
#~ "This format will determin the value saved to the database and returned "
#~ "via the API"
#~ msgstr ""
#~ "Этот формат определит значение сохраненное в базе данных и возвращенное "
#~ "через API."

#~ msgid "\"yymmdd\" is the most versatile save format. Read more about"
#~ msgstr "\"yymmdd\" самоый практичный формат. Прочитать больше о"

#~ msgid "jQuery date formats"
#~ msgstr "jQuery форматах дат"

#~ msgid "This format will be seen by the user when entering a value"
#~ msgstr "Этот формат будет виден пользователям при вводе значения."

#~ msgid ""
#~ "\"dd/mm/yy\" or \"mm/dd/yy\" are the most used Display Formats. Read more "
#~ "about"
#~ msgstr ""
#~ "\"dd/mm/yy\" или \"mm/dd/yy\" самые используемые форматы отображения. "
#~ "Прочитать больше о"

#~ msgid "Dummy"
#~ msgstr "Макет"

#~ msgid "No File Selected"
#~ msgstr "Файл не выбран"

#~ msgid "File Object"
#~ msgstr "Файловый объект"

#~ msgid "File Updated."
#~ msgstr "Файл обновлен."

#~ msgid "Media attachment updated."
#~ msgstr "Вложение медиа обновлено."

#~ msgid "No files selected"
#~ msgstr "Файлы не выбраны"

#~ msgid "Add Selected Files"
#~ msgstr "Добавить выбранные файлы"

#~ msgid "Image Object"
#~ msgstr "Изображаемый объект"

#~ msgid "Image Updated."
#~ msgstr "Изображение обновлено."

#~ msgid "No images selected"
#~ msgstr "Изображение не выбраны"

#~ msgid "Add Selected Images"
#~ msgstr "Добавить выбранные изображения"

#~ msgid "Text &amp; HTML entered here will appear inline with the fields"
#~ msgstr "Текст и HTML введенный сюда появится на одной строке с полями."

#~ msgid "Filter from Taxonomy"
#~ msgstr "Фильтровать по таксономии"

#~ msgid "Enter your choices one per line"
#~ msgstr "Введите каждый вариант выбора на новую строку."

#~ msgid "Red"
#~ msgstr "Red"

#~ msgid "Blue"
#~ msgstr "Blue"

#~ msgid "Post Type Select"
#~ msgstr "Выбор типа записи"

#~ msgid "Post Title"
#~ msgstr "Заголовок записи"

#~ msgid ""
#~ "All fields proceeding this \"tab field\" (or until another \"tab field\"  "
#~ "is defined) will appear grouped on the edit screen."
#~ msgstr ""
#~ "Все поля, которые следуют перед этим полем будут находиться в данной "
#~ "вкладке (или пока другое поле-вкладка не будет создано)."

#~ msgid "You can use multiple tabs to break up your fields into sections."
#~ msgstr ""
#~ "Вы можете использовать несколько вкладок, чтобы разделить свои поля на "
#~ "разделы."

#~ msgid "Formatting"
#~ msgstr "Форматирование"

#~ msgid "Define how to render html tags"
#~ msgstr "Определите, как отображать HTML теги."

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Определите, как отображать HTML теги и новые строки."

#~ msgid "auto &lt;br /&gt;"
#~ msgstr "автоматические &lt;br /&gt;"

# Must be non-translateable.
#~ msgid "new_field"
#~ msgstr "new_field"

#~ msgid "Field Order"
#~ msgstr "Очередность поля"

#~ msgid "Field Key"
#~ msgstr "Ключ поля"

#~ msgid "Edit this Field"
#~ msgstr "Редактировать это поле."

#~ msgid "Read documentation for this field"
#~ msgstr "Прочитайте документацию по этому полю."

#~ msgid "Docs"
#~ msgstr "Документация"

#~ msgid "Duplicate this Field"
#~ msgstr "Копировать это поле"

#~ msgid "Delete this Field"
#~ msgstr "Удалить это поле"

#~ msgid "Field Instructions"
#~ msgstr "Инструкции по полю"

#~ msgid "Show this field when"
#~ msgstr "Отображать это поле, когда"

#~ msgid "all"
#~ msgstr "все"

#~ msgid "any"
#~ msgstr "любое"

#~ msgid "these rules are met"
#~ msgstr "из этих условий придерживаются"

#~ msgid "Taxonomy Term (Add / Edit)"
#~ msgstr "Термин таксономии (Добавить / Редактировать)"

#~ msgid "User (Add / Edit)"
#~ msgstr "Пользователь (Добавить / Редактировать)"

#~ msgid "Media Attachment (Edit)"
#~ msgstr "Вложение медиа (Редактировать)"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "Разблокировать опции аддона с помощью кода активации."

#~ msgid "Normal"
#~ msgstr "Обычно"

#~ msgid "No Metabox"
#~ msgstr "Без метабокса"

#~ msgid "Standard Metabox"
#~ msgstr "Стандартный метабокс"
