msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-10-23 11:00+0300\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Ahrale | Atar4U.com <<EMAIL>>\n"
"Language: he_IL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:67
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:369 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "קבוצות שדות"

#: acf.php:370
msgid "Field Group"
msgstr "קבוצת שדות"

#: acf.php:371 acf.php:403 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New"
msgstr "הוספת חדש"

#: acf.php:372
msgid "Add New Field Group"
msgstr "הוספת קבוצת שדות חדשה"

#: acf.php:373
msgid "Edit Field Group"
msgstr "עריכת קבוצת שדות"

#: acf.php:374
msgid "New Field Group"
msgstr "קבוצת שדות חדשה"

#: acf.php:375
msgid "View Field Group"
msgstr "הצג את קבוצת השדות"

#: acf.php:376
msgid "Search Field Groups"
msgstr "חיפוש קבוצת שדות"

#: acf.php:377
msgid "No Field Groups found"
msgstr "אף קבוצת שדות לא נמצאה"

#: acf.php:378
msgid "No Field Groups found in Trash"
msgstr "אף קבוצת שדות לא נמצאה בפח"

#: acf.php:401 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:807
msgid "Fields"
msgstr "שדות"

#: acf.php:402
msgid "Field"
msgstr "שדה"

#: acf.php:404
msgid "Add New Field"
msgstr "הוספת שדה חדש"

#: acf.php:405
msgid "Edit Field"
msgstr "עריכת השדה"

#: acf.php:406 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "שדה חדש"

#: acf.php:407
msgid "View Field"
msgstr "הצג את השדה"

#: acf.php:408
msgid "Search Fields"
msgstr "חיפוש שדות"

#: acf.php:409
msgid "No Fields found"
msgstr "לא נמצאו שדות"

#: acf.php:410
msgid "No Fields found in Trash"
msgstr "לא נמצאו שדות בפח"

#: acf.php:449 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "לא פעיל"

#: acf.php:454
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "לא פעיל <span class=\"count\">(%s)</span>"
msgstr[1] "לא פעילים <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "קבוצת השדות עודכנה"

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "קבוצת השדות נמחקה."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "קבוצת השדות פורסמה."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "קבוצת השדות נשמרה."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "קבוצת השדות נשלחה."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "קבוצת השדות מתוכננת ל"

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "טיוטת קבוצת שדות עודכנה."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "מיקום"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "הגדרות"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "מועבר לפח. האם אתה בטוח?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "מסומן"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "אין שדות תיבות סימון זמינים"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "כותרת קבוצת שדות - חובה"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "העתק"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3964
msgid "or"
msgstr "או"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "שדות אב"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "שדות אחים"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "הזזת שדות מיוחדים"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "אי אפשר להזיז את השדה עד לשמירת השינויים שנעשו בו"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "ריק"

#: includes/admin/admin-field-group.php:281 includes/input.php:258
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "השינויים שעשית יאבדו אם תעבור לדף אחר"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "לא ניתן להשתמש במחרוזת \"field_\" בתחילת שם השדה"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "מפתחות שדה"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "פעיל"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "ההעברה הושלמה."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "אפשר עכשיו למצוא את שדה %s בתוך קבוצת השדות %s"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "סגור חלון"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "בבקשה בחר במיקום החדש עבור שדה זה"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "הזזת שדה"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "פעיל <span class=\"count\">(%s)</span>"
msgstr[1] "פעילים <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "קבוצת השדות שוכפלה. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s קבוצת השדה שוכפלה."
msgstr[1] "%s קבוצות השדות שוכפלו."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "קבוצת השדות סונכרנה. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s קבוצת השדות סונכרנה."
msgstr[1] "%s קבוצות השדות סונכרנו."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "סנכרון זמין"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "כותרת"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "תיאור"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "מצב"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "שדרגו את וורדפרס עם שדות מיוחדים באופן מקצועי, יעל ומהיר."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "גרסאות"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "מה חדש ב<a href=\"%s\">גרסה %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "עזרה"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "אתר"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "הוראות הפעלה"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "תמיכה"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "פרו"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "תודה שיצרת עם <a href=\"%s\">ACF</a>"

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "שכפל את הפריט הזה"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate"
msgstr "שיכפול"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:112
#: includes/fields/class-acf-field-relationship.php:656
msgid "Search"
msgstr "חיפוש"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "בחירה %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "סנכרון קבוצת שדות"

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "סינכרון"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr "החל"

#: includes/admin/admin-field-groups.php:798
msgid "Bulk Actions"
msgstr "עריכה קבוצתית"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "שדות מיוחדים"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "שדרוג מסד נתונים"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "סקירת אתרים ושדרוגים"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "שגיאה בבקשת האימות"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "אין עזכונים זמינים."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "תוספים"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "‏<b>שגיאה</b>. טעינת רשימת ההרחבות נכשלה"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "מידע"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "מה חדש"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "כלים"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "אף קבוצת שדות לא נבחרה"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:155
msgid "No file selected"
msgstr "לא נבחר קובץ"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "שגיאה בהעלאת הקובץ. בבקשה נסה שנית"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "סוג קובץ לא תקין"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "קובץ הייבוא ריק"

#: includes/admin/settings-tools.php:331
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "קבוצת שדות 1 יובאה"
msgstr[1] "%s קבוצות שדות יובאו"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "תנאי לוגי"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "הצגת השדה בתנאי ש"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:247
msgid "is equal to"
msgstr "שווה ל"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:248
msgid "is not equal to"
msgstr "לא שווה ל"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "וגם"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "הוספת קבוצת כללים"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "גרור ושחרר לסידור מחדש"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "עריכת שדה"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-file.php:137
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "עריכה"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "שכפול שדה"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "העברת שדה לקבוצה אחרת"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "שינוי מיקום"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "מחיקת שדה"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete"
msgstr "מחיקה"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "תווית השדה"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "השם שיופיע בדף העריכה"

#: includes/admin/views/field-group-field.php:77
msgid "Field Name"
msgstr "שם השדה"

#: includes/admin/views/field-group-field.php:78
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "מילה אחת, ללא רווחים. אפשר להשתמש במקף תחתי ובמקף אמצעי"

#: includes/admin/views/field-group-field.php:87
msgid "Field Type"
msgstr "סוג שדה"

#: includes/admin/views/field-group-field.php:98
#: includes/fields/class-acf-field-tab.php:88
msgid "Instructions"
msgstr "הוראות"

#: includes/admin/views/field-group-field.php:99
msgid "Instructions for authors. Shown when submitting data"
msgstr "הוראות למחברים. מוצג למעדכני התכנים באתר"

#: includes/admin/views/field-group-field.php:108
msgid "Required?"
msgstr "חובה?"

#: includes/admin/views/field-group-field.php:131
msgid "Wrapper Attributes"
msgstr "מאפייני עוטף"

#: includes/admin/views/field-group-field.php:137
msgid "width"
msgstr "רוחב"

#: includes/admin/views/field-group-field.php:152
msgid "class"
msgstr "מחלקה"

#: includes/admin/views/field-group-field.php:165
msgid "id"
msgstr "מזהה"

#: includes/admin/views/field-group-field.php:177
msgid "Close Field"
msgstr "סגור שדה"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "סדר"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:415
#: includes/fields/class-acf-field-radio.php:306
#: includes/fields/class-acf-field-select.php:432
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "תווית"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:595
msgid "Name"
msgstr "שם"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "מפתח"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "סוג"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"אין שדות. לחצו על כפתור <strong>+ הוספת שדה</strong> כדי ליצור את השדה "
"הראשון שלכם."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ הוספת שדה"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "כללים"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "יצירת מערכת כללים כדי לקבוע באילו מסכי עריכה יופיעו השדות המיוחדים"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "סגנון"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "רגיל (תיבת תיאור של וורדפרס)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "חלק (ללא תיבת תיאור)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "מיקום"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "גבוה (אחרי הכותרת)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "רגיל (אחרי התוכן)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "צד"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "מיקום תווית"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:102
msgid "Top aligned"
msgstr "מיושר למעלה"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:103
msgid "Left aligned"
msgstr "מיושר לשמאל"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "מיקום הוראות"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "מתחת לתוויות"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "מתחת לשדות"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "מיקום (order)"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "קבוצות שדות עם מיקום נמוך יופיעו ראשונות"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "מוצג ברשימת קבוצת השדות"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "הסתרה במסך"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>בחרו</b> פריטים שיהיו <b>נסתרים</b> במסך העריכה."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "קישור"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "עורך תוכן"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "מובאה"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "דיון"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "הערות"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "גרסאות עריכה"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "מזהה הפוסט"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "מחבר"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "פורמט"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "מאפייני עמוד"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:670
msgid "Featured Image"
msgstr "תמונה ראשית"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "קטגוריות"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "תגיות"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "שלח טראקבקים"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "הצגת קבוצת השדות הזו בתנאי ש"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr ""

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr ""

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr ""

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr ""

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr ""

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"מומלץ בחום לגבות את מאגר הנתונים לפני שממשיכים. האם אתם בטוחים שאתם רוצים "
"להריץ את העדכון כעת?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr ""

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "שדרוג נתונים לגרסה %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "שדה חזרה"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "תוכן גמיש"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "גלריה"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "עמוד אפשרויות"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "חובה לשדרג את מסד הנתונים"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "תודה שעדכנתם ל-%s גרסה %s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"לפני שאתם מתחילים להשתמש בתכונות המדהימות החדשות, בבקשה עדכנו את מאגר "
"הנתונים שלכם לגרסה העדכנית."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "קורא משימות שדרוג..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "הורדה והתקנה"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "מותקן"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "ברוכים הבאים לשדות מיוחדים מתקדמים"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"תודה שעידכנתם! ACF %s הוא גדול יותר וטוב יותר מאי פעם. מקווים שתאהבו אותו."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "חווית שדות מיוחדים חלקה יותר"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "שימושיות משופרת"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"הוספה של הספרייה הפופולרית Select2 שיפרה גם את השימושיות ואת המהירות בכמה "
"סוגי שדות, כולל: אובייקט פוסט, קישור דף, טקסונומיה ובחירה."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "עיצוב משופר"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"הרבה שדות עברו רענון ויזואלי כדי לגרום ל-ACF להיראות טוב מאי פעם! ניתן לראות "
"שינויים בולטים בשדה הגלריה, שדה היחסים, ובשדה ההטמעה (החדש)!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "נתונים משופרים"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"עיצוב מחדש של ארכיטקטורת המידע איפשר לשדות משנה להיות נפרדים מההורים שלהם. "
"דבר זה מאפשר לכם לגרור ולשחרר שדות לתוך ומחוץ לשדות אב."

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "להתראות הרחבות. שלום PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "הכירו את ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"כל ארבעת הרחבות הפרימיום אוחדו לתוך <a href=\"%s\">גרסת הפרו החדשה של ACF</"
"a>. עם הרשיונות הזמינים לשימוש אישי ולמפתחים, יכולות הפרימיום זולות יותר "
"ונגישות יותר מאי פעם."

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "תכונות עצמתיות"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"‏ACF PRO כולל תכונות עצמתיות כמו מידע שחוזר על עצמו, פריסות תוכן גמישות, שדה "
"גלריה יפה ואת היכולת ליצור דפי אפשרויות נוספים בממשק הניהול!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "קרא עוד על <a href=\"%s\">הפיצ׳רים של ACF PRO</a>"

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "שדרוג קל"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"כדי להקל על השידרוג, <a href=\"%s\">התחברו לחשבון שלכם</a> וקבלו חינם עותק "
"של ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"כתבנו גם <a href=\"%s\">מדריך שידרוג</a> כדי לענות על כל השאלות, אך אם עדיין "
"יש לכם שאלה, בבקשה צרו קשר עם צוות התמיכה שלנו דרך <a href=\"%s\">מוקד "
"התמיכה</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "מתחת למכסה המנוע"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "הגדרות חכמות יותר לשדות"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "‏ACF עכשיו שומר את הגדרות השדות שלו כאובייקטי פוסט בודדים"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "עוד AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr "יותר שדות משתמשים בחיפוש מבוסס AJAX כדי לשפר את מהירות טעינת הדף"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "‏JSON מקומי"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "תכונת ייצוא אוטומטי חדש ל-JSON משפר את המהירות"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "בקרת גרסאות טובה יותר"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr "תכונת חדש לייצוא אוטומטי ל-JSON מאפשר להגדרות השדות להיות מבוקרי גרסה"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "‏JSON במקום XML"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "ייבוא / ייצוא משתמש עכשיו ב-JSON במקום ב-XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "טפסים חדשים"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr "ניתן כעת למפות שדות לתגובות, ווידג׳טים וכל טפסי המשתמש!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "נוסף שדה חדש להטמעת תוכן"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "גלריה חדשה"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "שדה הגלריה עבר מתיחת פנים חיונית ביותר"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "הגדרות חדשות"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr "הגדרות קבוצות שדות נוספה למיקום התוויות ולמיקום ההוראות"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "טפסי צד קדמי משופרים"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "‏acf_form() יכול עכשיו ליצור פוסט חדש בעת השליחה"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "אימות נתונים משופר"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "אימות טפסים נעשה עכשיו עם PHP ו-AJAX במקום להשתמש רק ב-JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "שדה יחסים"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr "הגדרת שדה יחסים חדשה בשביל ׳סינונים׳ (חיפוש, סוג פוסט, טקסונומיה)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "שינוי מיקום שדות"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr "פונקציונליות קבוצות שדות חדשה מאפשרת לכם להעביר שדה בין קבוצות והורים"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "קישור לעמוד"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "קבוצת ארכיון חדשה בשדה הבחירה של page_link"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "דף אפשרויות משופר"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr "פונקציות חדשות לדף האפשרויות נותנות לכם ליצור דפי תפריט ראשיים ומשניים"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "אנחנו חושבים שתאהבו את השינויים ב%s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "יצוא קבוצות שדות לphp"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"ניתן להשתמש בקוד הבא כדי לרשום גרסה מקומית של קבוצות השדה הנבחרות. קבוצת "
"שדות מקומית יכולה להביא לתועלות רבות כמו זמני טעינה מהירים יותר, בקרת גרסאות "
"ושדות/הגדרות דינמיות. פשוט העתיקו והדביקו את הקוד הבא לקובץ functions‪.‬php "
"שבערכת העיצוב שלכם או הוסיפו אותו דרך קובץ חיצוני."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "בחירת קבוצת שדות"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "יצוא קבוצות שדות"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"בחרו בקבוצות השדות שברצונכם לייצא ואז בחרו במתודת הייצוא. השתמש בכפתור "
"ההורדה כדי לייצא קובץ json אותו תוכלו לייבא להתקנת ACF אחרת. השתמשו בכפתור "
"היצירה כדי לייצא קוד php אותו תוכלו להכניס לתוך ערכת העיצוב שלכם."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "הורדת קובץ ייצוא"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "יצירת קוד ייצוא"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "ייבוא קבוצות שדות"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"בחרו בקובץ השדות המיוחדים מסוג JSON שברצונכם לייבא. כשתלחצו על כפתור הייבוא "
"שמתחת, ACF ייבא את קבוצות השדות."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:35
msgid "Select File"
msgstr "בחר קובץ"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "ייבוא"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "תמונה ממוזערת"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "בינוני"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "גדול"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "גודל מלא"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1831
#: pro/fields/class-acf-field-clone.php:992
msgid "(no title)"
msgstr "(אין כותרת)"

#: includes/api/api-helpers.php:1868
#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
msgid "Parent"
msgstr ""

#: includes/api/api-helpers.php:3885
#, php-format
msgid "Image width must be at least %dpx."
msgstr ""

#: includes/api/api-helpers.php:3890
#, php-format
msgid "Image width must not exceed %dpx."
msgstr ""

#: includes/api/api-helpers.php:3906
#, php-format
msgid "Image height must be at least %dpx."
msgstr ""

#: includes/api/api-helpers.php:3911
#, php-format
msgid "Image height must not exceed %dpx."
msgstr ""

#: includes/api/api-helpers.php:3929
#, php-format
msgid "File size must be at least %s."
msgstr ""

#: includes/api/api-helpers.php:3934
#, php-format
msgid "File size must must not exceed %s."
msgstr ""

#: includes/api/api-helpers.php:3968
#, php-format
msgid "File type must be %s."
msgstr ""

#: includes/fields.php:144
msgid "Basic"
msgstr "בסיסי"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "תוכן"

#: includes/fields.php:146
msgid "Choice"
msgstr "בחירה"

#: includes/fields.php:147
msgid "Relational"
msgstr "יחסי"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:384
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:285
#: pro/fields/class-acf-field-clone.php:839
#: pro/fields/class-acf-field-flexible-content.php:552
#: pro/fields/class-acf-field-flexible-content.php:601
#: pro/fields/class-acf-field-repeater.php:450
msgid "Layout"
msgstr "פריסת תוכן"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "סוג השדה לא נמצא"

#: includes/fields.php:326
msgid "Unknown"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "בחירות"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "יש להקליד כל בחירה בשורה חדשה."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "לשליטה רבה יותר, אפשר לציין את הערך ואת התווית כך:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "red : אדום "

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:408
msgid "Allow Null?"
msgstr "לאפשר שדה ריק?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:276
#: includes/fields/class-acf-field-range.php:148
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "ערך ברירת המחדל"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:277
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr "מופיע כאשר יוצרים פוסט חדש"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:391
#: includes/fields/class-acf-field-radio.php:292
msgid "Horizontal"
msgstr "אופקי"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:390
#: includes/fields/class-acf-field-radio.php:291
msgid "Vertical"
msgstr "אנכי"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:408
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:299
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "ערך חוזר"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:409
#: includes/fields/class-acf-field-file.php:201
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:300
msgid "Specify the returned value on front end"
msgstr "הגדרת הערך המוחזר בצד הקדמי"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-radio.php:305
#: includes/fields/class-acf-field-select.php:431
msgid "Value"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:307
#: includes/fields/class-acf-field-select.php:433
msgid "Both (Array)"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "תיבת סימון"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "החלפת מצב הבחירה של כל הקבוצות"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:376
#: includes/fields/class-acf-field-select.php:378
msgid "Enter each default value on a new line"
msgstr "יש להקליד כל ערך ברירת מחדל בשורה חדשה"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Toggle"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:399
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "דוגם צבע"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "נקה"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "ברירת המחדל"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "בחירת צבע"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "בחירת תאריך"

#: includes/fields/class-acf-field-date_picker.php:33
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:34
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:35
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:36
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:37
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:181
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "פורמט תצוגה"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "הפורמט המוצג בעריכתםה פוסט"

#: includes/fields/class-acf-field-date_picker.php:216
#: includes/fields/class-acf-field-date_picker.php:247
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-date_time_picker.php:208
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:226
msgid "Save Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:227
msgid "The format used when saving a value"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:237
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:697
#: includes/fields/class-acf-field-select.php:426
#: includes/fields/class-acf-field-time_picker.php:124
msgid "Return Format"
msgstr "פורמט חוזר"

#: includes/fields/class-acf-field-date_picker.php:238
#: includes/fields/class-acf-field-date_time_picker.php:199
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "הפורמט המוחזר דרך פונקציות התבנית"

#: includes/fields/class-acf-field-date_picker.php:256
#: includes/fields/class-acf-field-date_time_picker.php:215
msgid "Week Starts On"
msgstr "השבוע מתחיל ביום"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:33
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:34
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:35
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:36
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:37
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:38
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:39
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:40
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:41
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:42
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:43
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr ""

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "אימייל"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "מציין טקסט"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "מופיע בתוך השדה"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:187
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "לפני"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "מופיע לפני השדה"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:196
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "אחרי"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "מופיע לאחר השדה"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "קובץ"

#: includes/fields/class-acf-field-file.php:36
msgid "Edit File"
msgstr "עריכת קובץ"

#: includes/fields/class-acf-field-file.php:37
msgid "Update File"
msgstr "עדכן קובץ"

#: includes/fields/class-acf-field-file.php:38
#: includes/fields/class-acf-field-image.php:43 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "משוייך לפוסט"

#: includes/fields/class-acf-field-file.php:126
msgid "File name"
msgstr ""

#: includes/fields/class-acf-field-file.php:130
#: includes/fields/class-acf-field-file.php:233
#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr ""

#: includes/fields/class-acf-field-file.php:139
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140 includes/input.php:269
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "הסר"

#: includes/fields/class-acf-field-file.php:155
msgid "Add File"
msgstr "הוספת קובץ"

#: includes/fields/class-acf-field-file.php:206
msgid "File Array"
msgstr "מערך קבצים"

#: includes/fields/class-acf-field-file.php:207
msgid "File URL"
msgstr "כתובת אינטרנט של הקובץ"

#: includes/fields/class-acf-field-file.php:208
msgid "File ID"
msgstr "מזהה הקובץ"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "ספריה"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr "הגבלת אפשרויות ספריית המדיה"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "הכל"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "הועלה לפוסט"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr ""

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-file.php:241
msgid "Restrict which files can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr ""

#: includes/fields/class-acf-field-file.php:251
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr ""

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "מפת גוגל"

#: includes/fields/class-acf-field-google-map.php:40
msgid "Locating"
msgstr "מאתר"

#: includes/fields/class-acf-field-google-map.php:41
msgid "Sorry, this browser does not support geolocation"
msgstr "מצטערים, דפדפן זה אינו תומך בזיהוי מיקום גיאוגרפי"

#: includes/fields/class-acf-field-google-map.php:113
msgid "Clear location"
msgstr "ניקוי מיקום"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Find current location"
msgstr "מציאת המיקום הנוכחי"

#: includes/fields/class-acf-field-google-map.php:117
msgid "Search for address..."
msgstr "חיפוש כתובת..."

#: includes/fields/class-acf-field-google-map.php:147
#: includes/fields/class-acf-field-google-map.php:158
msgid "Center"
msgstr "מרכוז"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center the initial map"
msgstr "מירכוז המפה הראשונית"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Zoom"
msgstr "זום"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Set the initial zoom level"
msgstr "הגדרת רמת הזום הראשונית"

#: includes/fields/class-acf-field-google-map.php:180
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:281
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "גובה"

#: includes/fields/class-acf-field-google-map.php:181
msgid "Customise the map height"
msgstr "התאמת גובה המפה"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr ""

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:389
msgid "Sub Fields"
msgstr "שדות משנה"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:840
msgid "Specify the style used to render the selected fields"
msgstr ""

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:845
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:458
msgid "Block"
msgstr "בלוק"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:846
#: pro/fields/class-acf-field-flexible-content.php:611
#: pro/fields/class-acf-field-repeater.php:457
msgid "Table"
msgstr "טבלה"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:847
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:459
msgid "Row"
msgstr "שורה"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "תמונה"

#: includes/fields/class-acf-field-image.php:40
msgid "Select Image"
msgstr "בחירת תמונה"

#: includes/fields/class-acf-field-image.php:41
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "עריכת תמונה"

#: includes/fields/class-acf-field-image.php:42
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "עדכון תמונה"

#: includes/fields/class-acf-field-image.php:44
msgid "All images"
msgstr "כל פריטי המדיה"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "לא נבחרה תמונה"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "הוספת תמונה"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "מערך תמונות"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "כתובת אינטרנט של התמונה"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "מזהה ייחודי של תמונה"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "גודל תצוגה"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr "מוצג בעת הזנת נתונים"

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:270
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr ""

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr ""

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr ""

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr ""

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr ""

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "הודעה"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "שורות חדשות"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "שליטה על אופן ההצגה של שורות חדשות "

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "הוספה אוטומטית של פסקאות"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "הוספה אוטומטית של &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "ללא עיצוב"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr ""

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "מספר"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:157
msgid "Minimum Value"
msgstr "ערך מינימום"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:167
msgid "Maximum Value"
msgstr "ערך מקסימום"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:177
msgid "Step Size"
msgstr "גודל הצעד"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "הערך חייב להיות מספר"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "הערך חייב להיות שווה או גדול יותר מ-%d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "הערך חייב להיות שווה או קטן יותר מ-%d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "‏שדה הטמעה"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "הקלד כתובת URL"

#: includes/fields/class-acf-field-oembed.php:234
#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "שגיאה."

#: includes/fields/class-acf-field-oembed.php:234
msgid "No embed found for the given URL."
msgstr "לא נמצא קוד הטמעה לכתובת ה-URL הנתונה."

#: includes/fields/class-acf-field-oembed.php:267
#: includes/fields/class-acf-field-oembed.php:278
msgid "Embed Size"
msgstr "גודל ההטמעה "

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "ארכיונים"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:623
msgid "Filter by Post Type"
msgstr "סינון על פי סוג פוסט"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:631
msgid "All post types"
msgstr "כל סוגי הפוסטים"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:637
msgid "Filter by Taxonomy"
msgstr "סינון לפי טקסונומיה"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:645
msgid "All taxonomies"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:396
#: includes/fields/class-acf-field-user.php:418
msgid "Select multiple values?"
msgstr "בחירת ערכים מרובים?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "ססמה"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:702
msgid "Post Object"
msgstr "אובייקט פוסט"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post ID"
msgstr "מזהה ייחודי לפוסט"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "כפתור רדיו"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "אחר"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "הוספת האפשרות 'אחר' כדי לאפשר ערכים מותאמים אישית"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "שמירת אחר"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "שמירת ערכי 'אחר' לאפשרויות השדה"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "יחסים"

#: includes/fields/class-acf-field-relationship.php:37
msgid "Minimum values reached ( {min} values )"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:38
msgid "Maximum values reached ( {max} values )"
msgstr "הגעתם לערך המקסימלי האפשרי ( ערכי {max} )"

#: includes/fields/class-acf-field-relationship.php:39
msgid "Loading"
msgstr "טוען"

#: includes/fields/class-acf-field-relationship.php:40
msgid "No matches found"
msgstr "לא נמצאו התאמות"

#: includes/fields/class-acf-field-relationship.php:423
msgid "Select post type"
msgstr "בחירת סוג פוסט"

#: includes/fields/class-acf-field-relationship.php:449
msgid "Select taxonomy"
msgstr "בחירת טקסונומיה"

#: includes/fields/class-acf-field-relationship.php:539
msgid "Search..."
msgstr "חיפוש..."

#: includes/fields/class-acf-field-relationship.php:651
msgid "Filters"
msgstr "מסננים (Filters)"

#: includes/fields/class-acf-field-relationship.php:657
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "סוג פוסט"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
msgid "Taxonomy"
msgstr "טקסונמיה"

#: includes/fields/class-acf-field-relationship.php:665
msgid "Elements"
msgstr "אלמנטים"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Selected elements will be displayed in each result"
msgstr "האלמנטים הנבחרים יוצגו בכל תוצאה"

#: includes/fields/class-acf-field-relationship.php:677
msgid "Minimum posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:686
msgid "Maximum posts"
msgstr "מספר פוסטים מרבי"

#: includes/fields/class-acf-field-relationship.php:790
#: pro/fields/class-acf-field-gallery.php:800
#, fuzzy, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s מחייב לפחות בחירה %s"
msgstr[1] "%s מחייב לפחות בחירה %s"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
msgctxt "noun"
msgid "Select"
msgstr ""

#: includes/fields/class-acf-field-select.php:38
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""

#: includes/fields/class-acf-field-select.php:39
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""

#: includes/fields/class-acf-field-select.php:40
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr ""

#: includes/fields/class-acf-field-select.php:41
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:42
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr ""

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr ""

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr ""

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr ""

#: includes/fields/class-acf-field-select.php:48
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr ""

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr ""

#: includes/fields/class-acf-field-select.php:255 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr ""

#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "ממשק משתמש מסוגנן"

#: includes/fields/class-acf-field-select.php:416
msgid "Use AJAX to lazy load choices?"
msgstr "להשתמש ב-AJAX כדי לטעון את האפשרויות לאחר שהדף עולה"

#: includes/fields/class-acf-field-select.php:427
msgid "Specify the value returned"
msgstr ""

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "לשונית"

#: includes/fields/class-acf-field-tab.php:82
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""
"שדה הלשונית יוצג באופן שגוי כשמוסיפים אותו לשדה חזרה שמוצג כטבלה או לשדה "
"פריסת תוכן גמישה"

#: includes/fields/class-acf-field-tab.php:83
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr ""
"השתמשו בלשוניות כדי לארגן את ממשק העריכה טוב יותר באמצעות קיבוץ השדות יחד."

#: includes/fields/class-acf-field-tab.php:84
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"כל השדות שאחרי \"שדה הלשונית\" הזה (או עד להגדרת שדה לשונית נוסף) יהיו "
"מקובצים יחד, כשהתווית של שדה זה תופיע ככותרת הלשונית."

#: includes/fields/class-acf-field-tab.php:98
msgid "Placement"
msgstr "מיקום"

#: includes/fields/class-acf-field-tab.php:110
msgid "End-point"
msgstr ""

#: includes/fields/class-acf-field-tab.php:111
msgid "Use this field as an end-point and start a new group of tabs"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "ללא"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "ערכים מרובים"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "בחירה מרובה"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr "ערך יחיד"

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr "כפתורי רדיו"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr "אוביקט ביטוי"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "מזהה הביטוי"

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:997
msgid "Add"
msgstr ""

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "טקסט"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "הגבלת מספר תווים"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "השאירו את השדה ריק אם אין מגבלת תווים"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "אזור טקסט"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "שורות"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "קובע את גובה אזור הטקסט"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "אמת / שקר"

#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159 includes/input.php:267
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "כן"

#: includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:169 includes/input.php:268
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "לא"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:165
msgid "Off Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:166
msgid "Text shown when inactive"
msgstr ""

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "כתובת ‏Url"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "הערך חייב להיות כתובת URL תקנית"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "משתמש"

#: includes/fields/class-acf-field-user.php:393
msgid "Filter by role"
msgstr "סינון על פי תפקיד"

#: includes/fields/class-acf-field-user.php:401
msgid "All user roles"
msgstr "כל תפקידי המשתמשים"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "עורך ויזואלי"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "ויזואלי"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "לשוניות"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "עורך ויזואלי ועורך טקסט"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "עורך ויזואלי בלבד"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "טקסט בלבד"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "סרגל כלים"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "להציג כפתורי העלאת מדיה?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr ""

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr ""

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr ""

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:573 pro/options-page.php:81
msgid "Update"
msgstr "עדכון"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "הפוסט עודכן"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr ""

#: includes/input.php:259
msgid "Expand Details"
msgstr "פרטים נוספים"

#: includes/input.php:260
msgid "Collapse Details"
msgstr "להסתיר פרטים"

#: includes/input.php:261
msgid "Validation successful"
msgstr "האימות עבר בהצלחה"

#: includes/input.php:262 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "האימות נכשל"

#: includes/input.php:263
msgid "1 field requires attention"
msgstr ""

#: includes/input.php:264
#, php-format
msgid "%d fields require attention"
msgstr ""

#: includes/input.php:265
msgid "Restricted"
msgstr ""

#: includes/input.php:266
msgid "Are you sure?"
msgstr ""

#: includes/input.php:270
msgid "Cancel"
msgstr ""

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "פוסט"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "עמוד"

#: includes/locations.php:96
msgid "Forms"
msgstr "שדות"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "קובץ מצורף"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr ""

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "תגובה"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr ""

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "מנהל על"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr ""

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr ""

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "עמוד אב"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "תבנית עמוד"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "תבנית ברירת המחדל"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "סוג עמוד"

#: includes/locations/class-acf-location-page-type.php:145
msgid "Front Page"
msgstr "עמוד ראשי"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Posts Page"
msgstr "עמוד פוסטים"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Top Level Page (no parent)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:148
msgid "Parent Page (has children)"
msgstr "עמוד אב (יש לו עמודים ילדים)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Child Page (has parent)"
msgstr "עמוד בן (יש לו עמוד אב)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "קטגורית פוסטים"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "פורמט פוסט"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "סטטוס פוסט"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "טקסונומית פוסט"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr ""

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "מונח טקסונומיה"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "טופס משתמש"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "הוספה / עריכה"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "הרשמה"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "תפקיד משתמש"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "ווידג׳ט"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr ""

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr ""

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "ערך %s נדרש"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "שדות מיוחדים מתקדמים פרו"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "פורסם"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"אף קבוצת שדות לא נמצאה בפח. <a href=\"%s\">יצירת קבוצת שדות מיוחדים</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "‏<b>שגיאה</b>. החיבור לשרת העדכון נכשל"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "עדכונים"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "ביטול הפעלת רשיון"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "הפעל את הרשיון"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "מפתח רשיון"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "מידע על העדכון"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "גרסה נוכחית"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "גרסה אחרונה"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "יש עדכון זמין"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "עדכון התוסף"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "הקלד בבקשה את מפתח הרשיון שלך לעיל כדי לשחרר את נעילת העדכונים"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "בדיקה חוזרת"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "הודעת שדרוג"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:808
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:825
msgid "Display"
msgstr "תצוגה"

#: pro/fields/class-acf-field-clone.php:826
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:831
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:832
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:853
#, php-format
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:856
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:867
#, php-format
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:870
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:988
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1027
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1031
#, php-format
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:470
msgid "Add Row"
msgstr "הוספת שורה חדשה"

#: pro/fields/class-acf-field-flexible-content.php:34
msgid "layout"
msgstr "פריסה"

#: pro/fields/class-acf-field-flexible-content.php:35
msgid "layouts"
msgstr "פריסות"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "מחיקת {פריסה}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr "לשדה זה דרושים לפחות {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr "לשדה זה יש מגבלה של  {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr "שדה זה דורש לפחות {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "הגעתם לערך המקסימלי של {label} האפשרי ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr "‏{available} {label} {identifier} זמינים (מקסימום {max})"

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr "‏{required} {label} {identifier} נדרש (מינימום {min})"

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr "דרושה לפחות פריסה אחת לתוכן הגמיש"

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "לחצו על כפתור \"%s\" שלמטה כדי להתחיל ביצירת הפריסה"

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "הוספת פריסה"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "הסרת פריסה"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder Layout"
msgstr "שינוי סדר פריסה"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder"
msgstr "סידור מחדש"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete Layout"
msgstr "מחיקת פריסת תוכן"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate Layout"
msgstr "שכפול פריסת תוכן"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New Layout"
msgstr "הוספת פריסת תוכן חדשה"

#: pro/fields/class-acf-field-flexible-content.php:628
msgid "Min"
msgstr "מינימום"

#: pro/fields/class-acf-field-flexible-content.php:641
msgid "Max"
msgstr "מקסימום"

#: pro/fields/class-acf-field-flexible-content.php:668
#: pro/fields/class-acf-field-repeater.php:466
msgid "Button Label"
msgstr "תווית כפתור"

#: pro/fields/class-acf-field-flexible-content.php:677
msgid "Minimum Layouts"
msgstr "מינימום פריסות"

#: pro/fields/class-acf-field-flexible-content.php:686
msgid "Maximum Layouts"
msgstr "מקסימום פריסות"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "הוספת תמונה לגלריה"

#: pro/fields/class-acf-field-gallery.php:45
msgid "Maximum selection reached"
msgstr "הגעתם למקסימום בחירה"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "אורך"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "הוספה לגלריה"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "עריכה קבוצתית"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "מיון לפי תאריך העלאה"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "מיון לפי תאריך שינוי"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "מיון לפי כותרת"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "הפוך סדר נוכחי"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "סגור"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "מינימום בחירה"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "מקסימום בחירה"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr "הגעתם למינימום שורות האפשרי ({min} שורות)"

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr "הגעתם למקסימום שורות האפשרי ({max} שורות)"

#: pro/fields/class-acf-field-repeater.php:343
msgid "Add row"
msgstr "הוספת שורה"

#: pro/fields/class-acf-field-repeater.php:344
msgid "Remove row"
msgstr "הסרת שורה"

#: pro/fields/class-acf-field-repeater.php:419
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:420
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:430
msgid "Minimum Rows"
msgstr "מינימום שורות"

#: pro/fields/class-acf-field-repeater.php:440
msgid "Maximum Rows"
msgstr "מקסימום שורות"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "לא קיים דף אפשרויות"

#: pro/options-page.php:51
msgid "Options"
msgstr "אפשרויות"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "האפשרויות עודכנו"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr ""

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr ""

#~ msgid "See what's new in"
#~ msgstr "מה חדש ב"

#~ msgid "version"
#~ msgstr "גרסה"

#~ msgid "Getting Started"
#~ msgstr "תחילת עבודה"

#~ msgid "Field Types"
#~ msgstr "סוגי שדות"

#~ msgid "Functions"
#~ msgstr "פונקציות"

#~ msgid "Actions"
#~ msgstr "פעולות (Actions)"

#~ msgid "'How to' guides"
#~ msgstr "מדריכי ׳איך לעשות׳"

#~ msgid "Tutorials"
#~ msgstr "הדרכות"

#~ msgid "Created by"
#~ msgstr "נוצר בידי"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "‏<b>הצלחה</b>. כלי הייבוא הוסיף %s קבוצות שדה: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "‏<b>אזהרה</b>. כלי הייבוא זיהה %s קבוצות שדה שכבר קיימות, ולפיכך הן לא "
#~ "יובאו: %s"

#~ msgid "Upgrade"
#~ msgstr "שדרוג"

#~ msgid "Error"
#~ msgstr "שגיאה"

#~ msgid "Drag and drop to reorder"
#~ msgstr "גררו ושחררו כדי לשנות את הסדר"

#~ msgid "See what's new"
#~ msgstr "בואו לראות מה חדש"

#~ msgid "Done"
#~ msgstr "בוצע"

#~ msgid "Today"
#~ msgstr "היום"

#~ msgid "Show a different month"
#~ msgstr "הצגת חודש אחר"

#~ msgid "Return format"
#~ msgstr "פורמט חוזר"

#~ msgid "uploaded to this post"
#~ msgstr "העלה לפוסט הזה"

#~ msgid "File Name"
#~ msgstr "שם קובץ"

#~ msgid "File Size"
#~ msgstr "גודל קובץ"

#~ msgid "No File selected"
#~ msgstr "לא נבחר קובץ"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr "שימו לב שכל הטקסט יועבר קודם דרך פונקציית וורדפרס "

#~ msgid "Select"
#~ msgstr "בחירה"

#~ msgid "Warning"
#~ msgstr "זהירות"

#~ msgid "eg. Show extra content"
#~ msgstr "למשל: הצגת תוכן נוסף"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "‏<b>שגיאת התחברות</b>. מצטערים, בבקשה נסה שנית"

#~ msgid "Save Options"
#~ msgstr "שמירת אפשרויות"

#~ msgid "License"
#~ msgstr "רשיון"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "כדי לאפשר קבלת עדכונים, נא להקליד את מפתח הרשיון שלך להלן. אם אין לכך "
#~ "מפתח רשיון, בבקשה בקר בדף "

#~ msgid "details & pricing"
#~ msgstr "פרטים ומחירים"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "כדי לאפשר עדכונים, בבקשה הקלד את מפתח הרשיון שלך בדף <a href=\"%s"
#~ "\">העדכונים</a>. אם אין לך מפתח רשיון, בבקשה עבור לדף <a href=\"%s"
#~ "\">פרטים ומחירים</a>"

#~ msgid "Field&nbsp;Groups"
#~ msgstr "שדות וקבוצות"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr "טעינת ערך המבוסס על המונחים של הפוסט ועדכון המונחים של הפוסט בשמירה"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "טעינה ושמירה של תנאים לפוסט"

#~ msgid "No taxonomy filter"
#~ msgstr "ללא סינון טקסונומיה"

#~ msgid "%s required fields below are empty"
#~ msgstr "%s שדות החובה שלהלן ריקים"

#~ msgid "1 required field below is empty"
#~ msgstr "שדה חובה אחד שלהלן ריק"

#~ msgid "%s requires at least %s selections"
#~ msgstr "%s מחייב לפחות %s בחירות"

#~ msgid "Data is at the latest version."
#~ msgstr "הנתונים הם בגרסה העדכנית ביותר."

#~ msgid "Data upgraded successfully."
#~ msgstr "שדרוג הנתונים הסתיים בהצלחה."

#~ msgid "Data Upgrade"
#~ msgstr "שדרוג נתונים"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exiting "
#~ "way!"
#~ msgstr "אנחנו משנים את אופן ההפצה של יכולות הפרימיום בצורה מלהיבה!"

#~ msgid "Update Database"
#~ msgstr "עדכון מאגר נתונים"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "למדו מדוע ACF PRO נחוץ לאתר שלכם"

#~ msgid "ACF PRO Required"
#~ msgstr "‏ACF PRO נדרש"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "שינמוך ל-ACF גרסה %s"

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "אל תלחצו, אתם יכולים פשוט לשנמך את גרסת התוסיף ולהמשיך להשתמש ב-ACF שאתם "
#~ "מכירים!"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "זיהינו בעיה שמחייבת את תשומת הלב שלכם: האתר הזה משתמש בהרחבות פרימיום "
#~ "(%s) שאינן תואמות עם ACF יותר."

#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field "
#~ "group's options will be used. (the one with the lowest order number)"
#~ msgstr ""
#~ "אם קבוצות שדות רבות מופיעות במסך העריכה של העמוד, הסדר ייקבע לפי ההגדרות "
#~ "בקבוצת השדות הראשונה. (זאת עם מספר הסדר הנמוך ביותר)"

#~ msgid "<b>Select</b> items to <b>hide</b> them from the edit screen"
#~ msgstr "בחרו פריטים שיוסתרו במסך העריכה"

#~ msgid "Field groups are created in order <br />from lowest to highest"
#~ msgstr "קבוצות שדות יסודרו<br /> מהנמוך ביותר לגבוה ביותר"

#~ msgid "Logged in User Type"
#~ msgstr "סוג משתמש מחובר"

#~ msgid "Top Level Page (parent of 0)"
#~ msgstr "עמוד ברמה הגבוהה ביותר (ללא הורה)"

#~ msgid "Trash"
#~ msgstr "פח"

#~ msgid "Revision"
#~ msgstr "גרסת עריכה"

#~ msgid "Private"
#~ msgstr "פרטי"

#~ msgid "Future"
#~ msgstr "עתידי"

#~ msgid "Draft"
#~ msgstr "טיוטה"

#~ msgid "Pending Review"
#~ msgstr "ממתין לסקירה"

#~ msgid "Show Field Keys"
#~ msgstr "הצגת מפתחות שדה:"

#~ msgid "Hide / Show All"
#~ msgstr "הצגה/הסתרת הכל"

#~ msgid "Import / Export"
#~ msgstr "ייבוא / ייצוא"
