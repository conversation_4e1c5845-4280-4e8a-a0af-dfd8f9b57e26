msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-06-27 15:33+1000\n"
"PO-Revision-Date: 2018-02-06 10:06+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: nb_NO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:63
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:355 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Feltgrupper"

#: acf.php:356
msgid "Field Group"
msgstr "Feltgruppe"

#: acf.php:357 acf.php:389 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New"
msgstr "Legg til ny"

#: acf.php:358
msgid "Add New Field Group"
msgstr "Legg til ny feltgruppe"

#: acf.php:359
msgid "Edit Field Group"
msgstr "Rediger feltgruppe"

#: acf.php:360
msgid "New Field Group"
msgstr "Ny feltgruppe"

#: acf.php:361
msgid "View Field Group"
msgstr "Vis feltgruppe"

#: acf.php:362
msgid "Search Field Groups"
msgstr "Søk i feltgrupper"

#: acf.php:363
msgid "No Field Groups found"
msgstr "Ingen feltgrupper funnet"

#: acf.php:364
msgid "No Field Groups found in Trash"
msgstr "Ingen feltgrupper funnet i papirkurven"

#: acf.php:387 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:857
msgid "Fields"
msgstr "Felt"

#: acf.php:388
msgid "Field"
msgstr "Felt"

#: acf.php:390
msgid "Add New Field"
msgstr "Legg til nytt felt"

#: acf.php:391
msgid "Edit Field"
msgstr "Rediger felt"

#: acf.php:392 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Nytt felt"

#: acf.php:393
msgid "View Field"
msgstr "Vis felt"

#: acf.php:394
msgid "Search Fields"
msgstr "Søkefelt"

#: acf.php:395
msgid "No Fields found"
msgstr "Ingen felter funnet"

#: acf.php:396
msgid "No Fields found in Trash"
msgstr "Ingen felt funnet i papirkurven"

#: acf.php:435 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Inaktiv"

#: acf.php:440
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inaktiv <span class=\"count\">(%s)</span>"
msgstr[1] "Inaktive <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Feltgruppe oppdatert."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Feltgruppe slettet."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Feltgruppe publisert."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Feltgruppe lagret."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Feltgruppe sendt inn."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Feltgruppe planlagt for"

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Feltgruppekladd oppdatert."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Sted"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "Innstillinger"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Flytt til papirkurven. Er du sikker?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "avkrysset"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "Ingen av/på- felter tilgjengelig"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "Feltgruppetittel er påkrevd"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:732
msgid "copy"
msgstr "kopier"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3970
msgid "or"
msgstr "eller"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Foreldrefelter"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Søskenfelter"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Flytt egendefinert felt"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "Dette feltet kan ikke flyttes før endringene er lagret"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Null"

#: includes/admin/admin-field-group.php:281 includes/input.php:257
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Endringene du har gjort vil gå tapt dersom du navigerer vekk fra denne siden"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Strengen  \"field_\" kan ikke brukes som starten på et feltnavn"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Feltnøkler"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Aktiv"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Flytting komplett."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "%s feltet finnes nå i %s feltgruppen"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Lukk vinduet"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Vennligst velg målet for dette feltet"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Flytt felt"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktive <span class=\"count\"> (%s)</span>"
msgstr[1] "Aktive <span class=\"count\"> (%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Feltgruppe duplisert. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s feltgruppe duplisert."
msgstr[1] "%s feltgrupper duplisert."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Feltgruppe synkronisert. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s feltgruppe synkronisert."
msgstr[1] "%s feltgrupper synkronisert."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Synkronisering tilgjengelig"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "Tittel"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "Beskrivelse"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Status"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "Tilpass WordPress med kraftige, profesjonelle og intuitive felt."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:111
msgid "Changelog"
msgstr "Endringslogg"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Se hva som er nytt i <a href=\"%s\">%s-utgaven</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Ressurser"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr ""

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Dokumentasjon"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Support"

#: includes/admin/admin-field-groups.php:623
#, fuzzy
msgid "Pro"
msgstr "Farvel Tillegg. Hei PRO"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Takk for at du bygger med <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "Dupliser dette elementet"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate"
msgstr "Dupliser"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:132
#: includes/fields/class-acf-field-relationship.php:737
msgid "Search"
msgstr "Søk"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "Velg %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "Synkroniser feltgruppe"

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "Synkroniser"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr ""

#: includes/admin/admin-field-groups.php:798
#, fuzzy
msgid "Bulk Actions"
msgstr "Massehandlinger"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Egendefinerte felt"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Oppgrader database"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Gå igjennom nettsteder og oppgrader"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Kunne ikke validere forespørselen"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Ingen oppdateringer tilgjengelige."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Tillegg"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Feil</b>. Kunne ikke laste liste over tillegg"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informasjon"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Hva er nytt"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "Verktøy"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "Ingen feltgrupper valgt"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:174
msgid "No file selected"
msgstr "Ingen fil valgt"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "Feil ved opplasting av fil. Vennligst prøv igjen"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "Feil filtype"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "Importfil tom"

#: includes/admin/settings-tools.php:331
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importerte 1 feltgruppe"
msgstr[1] "Importerte %s feltgrupper"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Betinget logikk"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Vis dette feltet hvis"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:243
msgid "is equal to"
msgstr "er lik"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:244
msgid "is not equal to"
msgstr "er ikke lik"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "og"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Legg til regelgruppe"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:420
#: pro/fields/class-acf-field-repeater.php:358
msgid "Drag to reorder"
msgstr "Dra for å endre rekkefølge"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Rediger felt"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:152
#: pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "Rediger"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Dupliser felt"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Flytt felt til en annen gruppe"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Flytt"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Slett felt"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete"
msgstr "Slett"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Feltetikett"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "Dette navnet vil vises på REDIGERING-siden"

#: includes/admin/views/field-group-field.php:78
msgid "Field Name"
msgstr "Feltnavn"

#: includes/admin/views/field-group-field.php:79
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Enkeltord, ingen mellomrom. Understreker og streker tillatt"

#: includes/admin/views/field-group-field.php:89
msgid "Field Type"
msgstr "Felttype"

#: includes/admin/views/field-group-field.php:101
#: includes/fields/class-acf-field-tab.php:102
msgid "Instructions"
msgstr "Instruksjoner"

#: includes/admin/views/field-group-field.php:102
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instruksjoner for forfattere. Vises når du sender inn data"

#: includes/admin/views/field-group-field.php:111
msgid "Required?"
msgstr "Påkrevd?"

#: includes/admin/views/field-group-field.php:134
msgid "Wrapper Attributes"
msgstr "Omslags-attributter"

#: includes/admin/views/field-group-field.php:140
msgid "width"
msgstr "bredde"

#: includes/admin/views/field-group-field.php:155
msgid "class"
msgstr "klasse"

#: includes/admin/views/field-group-field.php:168
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:180
msgid "Close Field"
msgstr "Lukk felt"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Rekkefølge"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-checkbox.php:317
#: includes/fields/class-acf-field-radio.php:321
#: includes/fields/class-acf-field-select.php:530
#: pro/fields/class-acf-field-flexible-content.php:599
msgid "Label"
msgstr "Etikett"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:970
#: pro/fields/class-acf-field-flexible-content.php:612
msgid "Name"
msgstr "Navn"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr ""

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Type"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Ingen felt. Klikk på <strong>+  Legg til felt</strong> knappen for å lage "
"ditt første felt."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Legg til felt"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regler"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Lag et sett regler for å bestemme hvilke redigeringsvinduer som vil bruke "
"disse feltene."

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stil"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standard (WP Metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Sømløs (ingen metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Posisjon"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Høy (etter tittel)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (etter innhold)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Side"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Etikettplassering"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:116
msgid "Top aligned"
msgstr "Toppjustert"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:117
msgid "Left aligned"
msgstr "Venstrejustert"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Instruksjonsplassering"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Nedenfor etiketter"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Nedenfor felt"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Rekkefølge"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Feltgrupper med lavere rekkefølge vises først"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Vist i feltgruppeliste"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Skjul på skjermen"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Velg</b> elementer som skal <b>skjules</b> fra redigeringsvinduet."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Hvis flere feltgrupper vises i et redigeringsvindu, vil den første "
"feltgruppens alternativer benyttes. (Den med laveste nummer i rekkefølgen)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Permalenke"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Innholdsredigerer"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Utdrag"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Diskusjon"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Kommentarer"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Revisjoner"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "URL-tamp"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Forfatter"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Sideattributter"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:751
msgid "Featured Image"
msgstr "Fremhevet bilde"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Kategorier"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Merkelapper"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Send tilbakesporinger"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Vis feltgruppen hvis"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Oppgrader nettsteder"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Databaseoppgradering for Advanced Custom Fields"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Følgende nettsteder krever en databaseoppgradering. Kryss av for de du vil "
"oppdatere og klikk deretter %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Nettsted"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Siden krever databaseoppgradering fra%s til%s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Nettstedet er oppdatert"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Databaseoppgradering er fullført. <a href=\"%s\">Gå tilbake til "
"nettverksdashboard</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Det anbefales sterkt at du sikkerhetskopierer databasen før du fortsetter. "
"Er du sikker på at du vil kjøre oppdateringen nå?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Oppgradering komplett"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Oppgradere data til versjon%s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:36
msgid "Repeater"
msgstr "Gjentaker"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:36
msgid "Flexible Content"
msgstr "Fleksibelt innhold"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:36
msgid "Gallery"
msgstr "Galleri"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:13
msgid "Options Page"
msgstr "Alternativer-side"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Databaseoppgradering er påkrevd"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Takk for at du oppgraderte til %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Før du begynner å bruke de nye funksjonene, må du oppdatere din database til "
"den nyeste versjonen."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Leser oppgraderingsoppgaver ..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Databaseoppgradering er fullført. <a href=\"%s\">Se hva som er nytt</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Last ned og installer"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Installert"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Velkommen til Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Takk for at du oppdaterte! ACF %s er større og bedre enn noen gang før. Vi "
"håper du liker det."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "En velfungerende opplevelse av egendefinerte felter"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Forbedret brukervennlighet"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Å inkludere det populære Select2-biblioteket har økt både brukervennlighet "
"og lastetid for flere felttyper, inkludert innleggsobjekter, sidelinker, "
"taksonomi og nedtrekksmenyer."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Forbedret design"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Mange felter har fått en visuell oppfriskning så ACF ser bedre ut enn på "
"lenge! Nevneverdige endringer sees på galleri-, relasjons- og oEmbedfelter!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Forbedret data"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Omskriving av dataarkitekturen tillater underfelter å leve uavhengig av "
"foreldrene sine. Det betyr at du kan dra og slippe felter til og fra "
"foreldrefeltene sine!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Farvel Tillegg. Hei PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Vi presenterer ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Vi endrer måten premium-funksjonalitet leveres på en spennende måte!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Alle fire premium-tilleggene har blitt kombinert i en ny <a href=\"%s\">Pro-"
"versjon av ACF</a>. Med både personlig- og utviklerlisenser tilgjengelig er "
"premiumfunksjonalitet billigere og mer tilgjengelig enn noensinne!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Kraftige funksjoner"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO inneholder kraftige funksjoner som repeterende data, fleksible "
"innholdsstrukturer, et vakkert gallerifelt og muligheten til å lage ekstra "
"administrasjonsegenskapssider!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Les mer om <a href=\"%s\">ACF PRO-funksjonaliteten</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Enkel oppgradering"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"For å gjøre oppgradering enklere, <a href=\"%s\">Logg inn på din konto</a> "
"og hent en gratis kopi av ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Vi har også skrevet en <a href=\"%s\">oppgraderingsveiledning</a> for å "
"besvare de fleste spørsmål, men skulle du fortsatt ha et spørsmål, ta "
"kontakt med via <a href=\"%s\">helpdesken</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Under panseret"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Smartere feltinnstillinger"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF lagrer nå feltegenskapene som individuelle innleggsobjekter"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Mer AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr "Flere felter bruker AJAX-drevet søk for å kutte ned innlastingstiden"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Lokal JSON"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Ny automatisk eksport til JSON sparer tid"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Bedre versjonskontroll"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr "Ny autoeksport til JSON lar feltinnstillinger bli versjonskontrollert"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "Byttet XML mot  JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Import / eksport bruker nå JSON istedenfor XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Nye skjemaer"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Feltene kan nå tilordnes til kommentarer, widgets og alle brukerskjemaer!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Et nytt felt for å bygge inn innhold er lagt til"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Nytt galleri"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Gallerietfeltet har gjennomgått en sårt tiltrengt ansiktsløftning"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Nye innstillinger"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Feltgruppeinnstillinger er lagt til for etikettplassering og "
"instruksjonsplassering"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Bedre frontend-skjemaer"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() kan nå lage et nytt innlegg ved innsending"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Bedre validering"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "Skjemavalidering skjer nå via PHP + AJAX framfor kun JavaScript"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Relasjonsfelt"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Nye relasjonsfeltinnstillinger for 'Filtre' (søk, innleggstype, taksonomi)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Flytte felt"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Ny feltgruppe-funksonalitet gir deg mulighet til å flytte felt mellom "
"grupper og foreldre"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:36
msgid "Page Link"
msgstr "Sidekobling"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Ny arkiver gruppe i page_link feltvalg"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Bedre sider for innstillinger"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Nye funksjoner på Valg-siden tillater oppretting av menysider for både "
"foreldre og barn"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Vi tror du vil elske endringene i %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Eksporter feltgrupper til PHP"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Følgende kode kan brukes for å registrere en lokal versjon av de(n) valgte "
"feltgruppen(e). En lokal feltgruppe kan gi mange fordeler som raskere "
"lastetid, versjonskontroll og dynamiske felter/innstillinger. Kopier og lim "
"inn den følgende koden i ditt temas functions.php-fil, eller inkluder det "
"med en ekstern fil."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "Velg feltgrupper"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "Eksporter feltgrupper"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Velg feltgruppene du vil eksportere og velg eksporteringsmetode. Bruk "
"nedlastingsknappen for å eksportere til en .json-fil du kan importere i en "
"annen installasjon av ACF. Bruk genererknappen for å eksportere PHP-kode du "
"kan legge inn i ditt tema."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Last ned eksportfil"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Generer eksportkode"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "Importer feltgrupper"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Velg ACF JSON-filen du vil importere. Når du klikker importerknappen under, "
"vil ACF importere feltgruppene."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:46
msgid "Select File"
msgstr "Velg fil"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "Importer"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "Miniatyrbilde"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "Medium"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "Stor"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "Full størrelse"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1837
#: pro/fields/class-acf-field-clone.php:1042
msgid "(no title)"
msgstr "(ingen tittel)"

#: includes/api/api-helpers.php:1874
#: includes/fields/class-acf-field-page_link.php:284
#: includes/fields/class-acf-field-post_object.php:283
#: includes/fields/class-acf-field-taxonomy.php:992
msgid "Parent"
msgstr "Forelder"

#: includes/api/api-helpers.php:3891
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Bildebredde må være minst %dpx."

#: includes/api/api-helpers.php:3896
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Bildebredden må ikke overstige %dpx."

#: includes/api/api-helpers.php:3912
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Bildehøyden må være minst %dpx."

#: includes/api/api-helpers.php:3917
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Bilde høyde må ikke overstige %dpx."

#: includes/api/api-helpers.php:3935
#, php-format
msgid "File size must be at least %s."
msgstr "Filstørrelse må være minst %s."

#: includes/api/api-helpers.php:3940
#, php-format
msgid "File size must must not exceed %s."
msgstr "Filstørrelsen må ikke overstige %s."

#: includes/api/api-helpers.php:3974
#, php-format
msgid "File type must be %s."
msgstr "Filtypen må være %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "Grunnleggende"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Innhold"

#: includes/fields.php:146
msgid "Choice"
msgstr "Valg"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relaterte"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149 includes/fields/class-acf-field-checkbox.php:286
#: includes/fields/class-acf-field-group.php:485
#: includes/fields/class-acf-field-radio.php:300
#: pro/fields/class-acf-field-clone.php:889
#: pro/fields/class-acf-field-flexible-content.php:569
#: pro/fields/class-acf-field-flexible-content.php:618
#: pro/fields/class-acf-field-repeater.php:514
msgid "Layout"
msgstr "Oppsett"

#: includes/fields.php:305
msgid "Field type does not exist"
msgstr "Felttype eksisterer ikke"

#: includes/fields.php:305
#, fuzzy
msgid "Unknown"
msgstr "Ukjent feltgruppe"

#: includes/fields/class-acf-field-checkbox.php:36
#: includes/fields/class-acf-field-taxonomy.php:786
msgid "Checkbox"
msgstr "Avkryssingsboks"

#: includes/fields/class-acf-field-checkbox.php:150
msgid "Toggle All"
msgstr "Velg/avvelg alle"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "Legg til nytt valg"

#: includes/fields/class-acf-field-checkbox.php:246
#: includes/fields/class-acf-field-radio.php:250
#: includes/fields/class-acf-field-select.php:466
msgid "Choices"
msgstr "Valg"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "Enter each choice on a new line."
msgstr "Skriv inn hvert valg på en ny linje."

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "For more control, you may specify both a value and label like this:"
msgstr "For mer kontroll, kan du angi både en verdi og merke som dette:"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "red : Red"
msgstr "svart : Svart"

#: includes/fields/class-acf-field-checkbox.php:255
msgid "Allow Custom"
msgstr "Tillat egendefinert"

#: includes/fields/class-acf-field-checkbox.php:260
msgid "Allow 'custom' values to be added"
msgstr "Tillat at \"egendefinerte\" verdier legges til"

#: includes/fields/class-acf-field-checkbox.php:266
msgid "Save Custom"
msgstr "Lagre egendefinert"

#: includes/fields/class-acf-field-checkbox.php:271
msgid "Save 'custom' values to the field's choices"
msgstr "Lagre \"egendefinerte\" verdier som alternativer i feltets valg"

#: includes/fields/class-acf-field-checkbox.php:277
#: includes/fields/class-acf-field-color_picker.php:146
#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-radio.php:291
#: includes/fields/class-acf-field-select.php:475
#: includes/fields/class-acf-field-text.php:142
#: includes/fields/class-acf-field-textarea.php:139
#: includes/fields/class-acf-field-true_false.php:150
#: includes/fields/class-acf-field-url.php:114
#: includes/fields/class-acf-field-wysiwyg.php:436
msgid "Default Value"
msgstr "Standardverdi"

#: includes/fields/class-acf-field-checkbox.php:278
#: includes/fields/class-acf-field-select.php:476
msgid "Enter each default value on a new line"
msgstr "Skriv inn hver standardverdi på en ny linje"

#: includes/fields/class-acf-field-checkbox.php:292
#: includes/fields/class-acf-field-radio.php:306
msgid "Vertical"
msgstr "Vertikal"

#: includes/fields/class-acf-field-checkbox.php:293
#: includes/fields/class-acf-field-radio.php:307
msgid "Horizontal"
msgstr "Horisontal"

#: includes/fields/class-acf-field-checkbox.php:300
msgid "Toggle"
msgstr "Veksle"

#: includes/fields/class-acf-field-checkbox.php:301
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Legg til ekstra avkryssingsboks for å velge alle alternativer"

#: includes/fields/class-acf-field-checkbox.php:310
#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:180
#: includes/fields/class-acf-field-radio.php:314
#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Return Value"
msgstr "Returverdi"

#: includes/fields/class-acf-field-checkbox.php:311
#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:207
#: includes/fields/class-acf-field-link.php:181
#: includes/fields/class-acf-field-radio.php:315
msgid "Specify the returned value on front end"
msgstr "Angi verdien returnert på frontend"

#: includes/fields/class-acf-field-checkbox.php:316
#: includes/fields/class-acf-field-radio.php:320
#: includes/fields/class-acf-field-select.php:529
msgid "Value"
msgstr "Verdi"

#: includes/fields/class-acf-field-checkbox.php:318
#: includes/fields/class-acf-field-radio.php:322
#: includes/fields/class-acf-field-select.php:531
msgid "Both (Array)"
msgstr "Begge (Array)"

#: includes/fields/class-acf-field-color_picker.php:36
msgid "Color Picker"
msgstr "Fargevelger"

#: includes/fields/class-acf-field-color_picker.php:83
msgid "Clear"
msgstr "Fjern"

#: includes/fields/class-acf-field-color_picker.php:84
msgid "Default"
msgstr "Standardverdi"

#: includes/fields/class-acf-field-color_picker.php:85
msgid "Select Color"
msgstr "Velg farge"

#: includes/fields/class-acf-field-color_picker.php:86
msgid "Current Color"
msgstr "Nåværende farge"

#: includes/fields/class-acf-field-date_picker.php:36
msgid "Date Picker"
msgstr "Datovelger"

#: includes/fields/class-acf-field-date_picker.php:44
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fullført"

#: includes/fields/class-acf-field-date_picker.php:45
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Idag"

#: includes/fields/class-acf-field-date_picker.php:46
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Neste"

#: includes/fields/class-acf-field-date_picker.php:47
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Forrige"

#: includes/fields/class-acf-field-date_picker.php:48
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "uke"

#: includes/fields/class-acf-field-date_picker.php:223
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-time_picker.php:127
msgid "Display Format"
msgstr "Visningsformat"

#: includes/fields/class-acf-field-date_picker.php:224
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:128
msgid "The format displayed when editing a post"
msgstr "Visningsformat når du redigerer et innlegg"

#: includes/fields/class-acf-field-date_picker.php:232
#: includes/fields/class-acf-field-date_picker.php:263
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:224
#: includes/fields/class-acf-field-time_picker.php:135
#: includes/fields/class-acf-field-time_picker.php:150
#, fuzzy
msgid "Custom:"
msgstr "Advanced Custom Fields"

#: includes/fields/class-acf-field-date_picker.php:242
msgid "Save Format"
msgstr "Lagre format"

#: includes/fields/class-acf-field-date_picker.php:243
msgid "The format used when saving a value"
msgstr "Formatet som brukes når du lagrer en verdi"

#: includes/fields/class-acf-field-date_picker.php:253
#: includes/fields/class-acf-field-date_time_picker.php:214
#: includes/fields/class-acf-field-post_object.php:447
#: includes/fields/class-acf-field-relationship.php:778
#: includes/fields/class-acf-field-select.php:524
#: includes/fields/class-acf-field-time_picker.php:142
msgid "Return Format"
msgstr "Format som skal returneres"

#: includes/fields/class-acf-field-date_picker.php:254
#: includes/fields/class-acf-field-date_time_picker.php:215
#: includes/fields/class-acf-field-time_picker.php:143
msgid "The format returned via template functions"
msgstr "Formatet som returneres via malfunksjoner"

#: includes/fields/class-acf-field-date_picker.php:272
#: includes/fields/class-acf-field-date_time_picker.php:231
msgid "Week Starts On"
msgstr "Uken starter på"

#: includes/fields/class-acf-field-date_time_picker.php:36
msgid "Date Time Picker"
msgstr "Dato/tid-velger"

#: includes/fields/class-acf-field-date_time_picker.php:44
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Velg tid"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Tid"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Time"

#: includes/fields/class-acf-field-date_time_picker.php:47
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minutt"

#: includes/fields/class-acf-field-date_time_picker.php:48
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekund"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisekund"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekund"

#: includes/fields/class-acf-field-date_time_picker.php:51
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Tidssone"

#: includes/fields/class-acf-field-date_time_picker.php:52
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nå"

#: includes/fields/class-acf-field-date_time_picker.php:53
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fullført"

#: includes/fields/class-acf-field-date_time_picker.php:54
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Velg"

#: includes/fields/class-acf-field-date_time_picker.php:56
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:57
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:60
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:61
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:36
msgid "Email"
msgstr "Epost"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-radio.php:292
#: includes/fields/class-acf-field-text.php:143
#: includes/fields/class-acf-field-textarea.php:140
#: includes/fields/class-acf-field-url.php:115
#: includes/fields/class-acf-field-wysiwyg.php:437
msgid "Appears when creating a new post"
msgstr "Vises når du oppretter et nytt innlegg"

#: includes/fields/class-acf-field-email.php:142
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:134
#: includes/fields/class-acf-field-text.php:151
#: includes/fields/class-acf-field-textarea.php:148
#: includes/fields/class-acf-field-url.php:123
msgid "Placeholder Text"
msgstr "Plassholdertekst"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:135
#: includes/fields/class-acf-field-text.php:152
#: includes/fields/class-acf-field-textarea.php:149
#: includes/fields/class-acf-field-url.php:124
msgid "Appears within the input"
msgstr "Vises i inndataene"

#: includes/fields/class-acf-field-email.php:151
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:143
#: includes/fields/class-acf-field-text.php:160
msgid "Prepend"
msgstr "Sett inn foran"

#: includes/fields/class-acf-field-email.php:152
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:144
#: includes/fields/class-acf-field-text.php:161
msgid "Appears before the input"
msgstr "Vises før inndata"

#: includes/fields/class-acf-field-email.php:160
#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-password.php:152
#: includes/fields/class-acf-field-text.php:169
msgid "Append"
msgstr "Tilføy"

#: includes/fields/class-acf-field-email.php:161
#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-password.php:153
#: includes/fields/class-acf-field-text.php:170
msgid "Appears after the input"
msgstr "Vises etter inndata"

#: includes/fields/class-acf-field-file.php:36
msgid "File"
msgstr "Fil"

#: includes/fields/class-acf-field-file.php:47
msgid "Edit File"
msgstr "Rediger fil"

#: includes/fields/class-acf-field-file.php:48
msgid "Update File"
msgstr "Oppdater fil"

#: includes/fields/class-acf-field-file.php:49
#: includes/fields/class-acf-field-image.php:54 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:55
msgid "Uploaded to this post"
msgstr "Lastet opp til dette innlegget"

#: includes/fields/class-acf-field-file.php:145
msgid "File name"
msgstr "Filnavn"

#: includes/fields/class-acf-field-file.php:149
#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:266
#: includes/fields/class-acf-field-image.php:295
#: pro/fields/class-acf-field-gallery.php:705
#: pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "Filstørrelse"

#: includes/fields/class-acf-field-file.php:174
msgid "Add File"
msgstr "Legg til fil"

#: includes/fields/class-acf-field-file.php:225
msgid "File Array"
msgstr "Filtabell"

#: includes/fields/class-acf-field-file.php:226
msgid "File URL"
msgstr "Fil-URL"

#: includes/fields/class-acf-field-file.php:227
msgid "File ID"
msgstr "Fil-ID"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "Bibliotek"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:232
#: pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "Begrense valg av mediebibliotek"

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:237
#: includes/locations/class-acf-location-attachment.php:105
#: includes/locations/class-acf-location-comment.php:83
#: includes/locations/class-acf-location-nav-menu.php:106
#: includes/locations/class-acf-location-taxonomy.php:83
#: includes/locations/class-acf-location-user-form.php:91
#: includes/locations/class-acf-location-user-role.php:108
#: includes/locations/class-acf-location-widget.php:87
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "Alle"

#: includes/fields/class-acf-field-file.php:241
#: includes/fields/class-acf-field-image.php:238
#: pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "Lastet opp til innlegg"

#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-image.php:245
#: pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:249
#: includes/fields/class-acf-field-file.php:260
msgid "Restrict which files can be uploaded"
msgstr "Begrense hvilke filer som kan lastes opp"

#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:270
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "Tillatte filtyper"

#: includes/fields/class-acf-field-file.php:271
#: includes/fields/class-acf-field-image.php:304
#: pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr "Kommaseparert liste. Tomt for alle typer"

#: includes/fields/class-acf-field-google-map.php:36
msgid "Google Map"
msgstr "Google-kart"

#: includes/fields/class-acf-field-google-map.php:51
msgid "Locating"
msgstr "Lokaliserer"

#: includes/fields/class-acf-field-google-map.php:52
msgid "Sorry, this browser does not support geolocation"
msgstr "Beklager, støtter denne nettleseren ikke geolokasjon"

#: includes/fields/class-acf-field-google-map.php:133
msgid "Clear location"
msgstr "Tøm plassering"

#: includes/fields/class-acf-field-google-map.php:134
msgid "Find current location"
msgstr "Finn nåværende posisjon"

#: includes/fields/class-acf-field-google-map.php:137
msgid "Search for address..."
msgstr "Søk etter adresse"

#: includes/fields/class-acf-field-google-map.php:167
#: includes/fields/class-acf-field-google-map.php:178
msgid "Center"
msgstr "Sentrer"

#: includes/fields/class-acf-field-google-map.php:168
#: includes/fields/class-acf-field-google-map.php:179
msgid "Center the initial map"
msgstr "Sentrer det første kartet"

#: includes/fields/class-acf-field-google-map.php:190
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:191
msgid "Set the initial zoom level"
msgstr "Angi initielt zoom-nivå"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-image.php:257
#: includes/fields/class-acf-field-image.php:286
#: includes/fields/class-acf-field-oembed.php:297
#: pro/fields/class-acf-field-gallery.php:696
#: pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "Høyde"

#: includes/fields/class-acf-field-google-map.php:201
msgid "Customise the map height"
msgstr "Tilpasse karthøyde"

#: includes/fields/class-acf-field-group.php:36
#, fuzzy
msgid "Group"
msgstr "Gruppe (viser valgt felt i en gruppe innenfor dette feltet)"

#: includes/fields/class-acf-field-group.php:469
#: pro/fields/class-acf-field-repeater.php:453
msgid "Sub Fields"
msgstr "Underfelt"

#: includes/fields/class-acf-field-group.php:486
#: pro/fields/class-acf-field-clone.php:890
msgid "Specify the style used to render the selected fields"
msgstr "Angi stilen som brukes til å gjengi de valgte feltene"

#: includes/fields/class-acf-field-group.php:491
#: pro/fields/class-acf-field-clone.php:895
#: pro/fields/class-acf-field-flexible-content.php:629
#: pro/fields/class-acf-field-repeater.php:522
msgid "Block"
msgstr "Blokk"

#: includes/fields/class-acf-field-group.php:492
#: pro/fields/class-acf-field-clone.php:896
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:521
msgid "Table"
msgstr "Tabell"

#: includes/fields/class-acf-field-group.php:493
#: pro/fields/class-acf-field-clone.php:897
#: pro/fields/class-acf-field-flexible-content.php:630
#: pro/fields/class-acf-field-repeater.php:523
msgid "Row"
msgstr "Rad"

#: includes/fields/class-acf-field-image.php:36
msgid "Image"
msgstr "Bilde"

#: includes/fields/class-acf-field-image.php:51
msgid "Select Image"
msgstr "Velg bilde"

#: includes/fields/class-acf-field-image.php:52
#: pro/fields/class-acf-field-gallery.php:53
msgid "Edit Image"
msgstr "Rediger bilde"

#: includes/fields/class-acf-field-image.php:53
#: pro/fields/class-acf-field-gallery.php:54
msgid "Update Image"
msgstr "Oppdater bilde"

#: includes/fields/class-acf-field-image.php:55
msgid "All images"
msgstr "Alle bilder"

#: includes/fields/class-acf-field-image.php:142
#: includes/fields/class-acf-field-link.php:153 includes/input.php:267
#: pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "Fjern"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "Ingen bilde valgt"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "Legg til bilde"

#: includes/fields/class-acf-field-image.php:212
msgid "Image Array"
msgstr "Filtabell"

#: includes/fields/class-acf-field-image.php:213
msgid "Image URL"
msgstr "Bilde-URL"

#: includes/fields/class-acf-field-image.php:214
msgid "Image ID"
msgstr "Bilde-ID"

#: includes/fields/class-acf-field-image.php:221
msgid "Preview Size"
msgstr "Forhåndsvisningsstørrelse"

#: includes/fields/class-acf-field-image.php:222
msgid "Shown when entering data"
msgstr "Vises når du skriver inn data"

#: includes/fields/class-acf-field-image.php:246
#: includes/fields/class-acf-field-image.php:275
#: pro/fields/class-acf-field-gallery.php:685
#: pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "Begrense hvilke bilder som kan lastes opp"

#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:278
#: includes/fields/class-acf-field-oembed.php:286
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "Bredde"

#: includes/fields/class-acf-field-link.php:36
#, fuzzy
msgid "Link"
msgstr "Sidekobling"

#: includes/fields/class-acf-field-link.php:146
#, fuzzy
msgid "Select Link"
msgstr "Velg fil"

#: includes/fields/class-acf-field-link.php:151
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:186
#, fuzzy
msgid "Link Array"
msgstr "Filtabell"

#: includes/fields/class-acf-field-link.php:187
#, fuzzy
msgid "Link URL"
msgstr "Fil-URL"

#: includes/fields/class-acf-field-message.php:36
#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-true_false.php:141
msgid "Message"
msgstr "Melding"

#: includes/fields/class-acf-field-message.php:124
#: includes/fields/class-acf-field-textarea.php:176
msgid "New Lines"
msgstr "Linjeskift"

#: includes/fields/class-acf-field-message.php:125
#: includes/fields/class-acf-field-textarea.php:177
msgid "Controls how new lines are rendered"
msgstr "Kontroller hvordan linjeskift gjengis"

#: includes/fields/class-acf-field-message.php:129
#: includes/fields/class-acf-field-textarea.php:181
msgid "Automatically add paragraphs"
msgstr "Automatisk legge til avsnitt"

#: includes/fields/class-acf-field-message.php:130
#: includes/fields/class-acf-field-textarea.php:182
msgid "Automatically add &lt;br&gt;"
msgstr "Legg til &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:131
#: includes/fields/class-acf-field-textarea.php:183
msgid "No Formatting"
msgstr "Ingen formatering"

#: includes/fields/class-acf-field-message.php:138
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:139
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Tillat HTML-kode til å vise oppføringsteksten i stedet for gjengivelse"

#: includes/fields/class-acf-field-number.php:36
msgid "Number"
msgstr "Tall"

#: includes/fields/class-acf-field-number.php:181
msgid "Minimum Value"
msgstr "Minste verdi"

#: includes/fields/class-acf-field-number.php:190
msgid "Maximum Value"
msgstr "Maksimal verdi"

#: includes/fields/class-acf-field-number.php:199
msgid "Step Size"
msgstr "Størrelse trinn"

#: includes/fields/class-acf-field-number.php:237
msgid "Value must be a number"
msgstr "Verdien må være et tall"

#: includes/fields/class-acf-field-number.php:255
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Verdien må være lik eller høyere enn %d"

#: includes/fields/class-acf-field-number.php:263
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Verdien må være lik eller lavere enn %d"

#: includes/fields/class-acf-field-oembed.php:36
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:237
msgid "Enter URL"
msgstr "Skriv inn URL"

#: includes/fields/class-acf-field-oembed.php:250
#: includes/fields/class-acf-field-taxonomy.php:904
msgid "Error."
msgstr "Feil."

#: includes/fields/class-acf-field-oembed.php:250
msgid "No embed found for the given URL."
msgstr "Fant ingen innbygging for den gitte URL-en."

#: includes/fields/class-acf-field-oembed.php:283
#: includes/fields/class-acf-field-oembed.php:294
msgid "Embed Size"
msgstr "Embed-størrelse"

#: includes/fields/class-acf-field-page_link.php:192
msgid "Archives"
msgstr "Arkiv"

#: includes/fields/class-acf-field-page_link.php:500
#: includes/fields/class-acf-field-post_object.php:399
#: includes/fields/class-acf-field-relationship.php:704
msgid "Filter by Post Type"
msgstr "Filtrer etter innleggstype"

#: includes/fields/class-acf-field-page_link.php:508
#: includes/fields/class-acf-field-post_object.php:407
#: includes/fields/class-acf-field-relationship.php:712
msgid "All post types"
msgstr "Alle innleggstyper"

#: includes/fields/class-acf-field-page_link.php:514
#: includes/fields/class-acf-field-post_object.php:413
#: includes/fields/class-acf-field-relationship.php:718
msgid "Filter by Taxonomy"
msgstr "Filtrer etter taksonomi"

#: includes/fields/class-acf-field-page_link.php:522
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-relationship.php:726
msgid "All taxonomies"
msgstr "Alle taksonomier"

#: includes/fields/class-acf-field-page_link.php:528
#: includes/fields/class-acf-field-post_object.php:427
#: includes/fields/class-acf-field-radio.php:259
#: includes/fields/class-acf-field-select.php:484
#: includes/fields/class-acf-field-taxonomy.php:799
#: includes/fields/class-acf-field-user.php:423
msgid "Allow Null?"
msgstr "Tillat Null?"

#: includes/fields/class-acf-field-page_link.php:538
msgid "Allow Archives URLs"
msgstr "Tillat arkiv-URL-er"

#: includes/fields/class-acf-field-page_link.php:548
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-select.php:494
#: includes/fields/class-acf-field-user.php:433
msgid "Select multiple values?"
msgstr "Velg flere verdier?"

#: includes/fields/class-acf-field-password.php:36
msgid "Password"
msgstr "Passord"

#: includes/fields/class-acf-field-post_object.php:36
#: includes/fields/class-acf-field-post_object.php:452
#: includes/fields/class-acf-field-relationship.php:783
msgid "Post Object"
msgstr "Innleggsobjekt"

#: includes/fields/class-acf-field-post_object.php:453
#: includes/fields/class-acf-field-relationship.php:784
msgid "Post ID"
msgstr "ID for innlegg"

#: includes/fields/class-acf-field-radio.php:36
msgid "Radio Button"
msgstr "Radioknapp"

#: includes/fields/class-acf-field-radio.php:269
msgid "Other"
msgstr "Andre"

#: includes/fields/class-acf-field-radio.php:274
msgid "Add 'other' choice to allow for custom values"
msgstr "Legg til 'andre'-valg for å tillate egendefinerte verdier"

#: includes/fields/class-acf-field-radio.php:280
msgid "Save Other"
msgstr "Lagre annen"

#: includes/fields/class-acf-field-radio.php:285
msgid "Save 'other' values to the field's choices"
msgstr "Lagre 'andre'-verdier til feltets valg"

#: includes/fields/class-acf-field-relationship.php:36
msgid "Relationship"
msgstr "Forhold"

#: includes/fields/class-acf-field-relationship.php:48
msgid "Minimum values reached ( {min} values )"
msgstr "Minimumsverdier nådd ({min} verdier)"

#: includes/fields/class-acf-field-relationship.php:49
msgid "Maximum values reached ( {max} values )"
msgstr "Maksimumsverdier nådd ( {max} verdier )"

#: includes/fields/class-acf-field-relationship.php:50
msgid "Loading"
msgstr "Laster"

#: includes/fields/class-acf-field-relationship.php:51
msgid "No matches found"
msgstr "Fant ingen treff"

#: includes/fields/class-acf-field-relationship.php:585
msgid "Search..."
msgstr "Søk …"

#: includes/fields/class-acf-field-relationship.php:594
msgid "Select post type"
msgstr "Velg innleggstype"

#: includes/fields/class-acf-field-relationship.php:607
msgid "Select taxonomy"
msgstr "Velg taksonomi"

#: includes/fields/class-acf-field-relationship.php:732
msgid "Filters"
msgstr "Filtre"

#: includes/fields/class-acf-field-relationship.php:738
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Innleggstype"

#: includes/fields/class-acf-field-relationship.php:739
#: includes/fields/class-acf-field-taxonomy.php:36
#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Taxonomy"
msgstr "Taksonomi"

#: includes/fields/class-acf-field-relationship.php:746
msgid "Elements"
msgstr "Elementer"

#: includes/fields/class-acf-field-relationship.php:747
msgid "Selected elements will be displayed in each result"
msgstr "Valgte elementer vises i hvert resultat"

#: includes/fields/class-acf-field-relationship.php:758
msgid "Minimum posts"
msgstr "Minimum antall innlegg"

#: includes/fields/class-acf-field-relationship.php:767
msgid "Maximum posts"
msgstr "Maksimalt antall innlegg"

#: includes/fields/class-acf-field-relationship.php:871
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s krever minst %s valgt"
msgstr[1] "%s krever minst %s valgte"

#: includes/fields/class-acf-field-select.php:36
#: includes/fields/class-acf-field-taxonomy.php:791
msgctxt "noun"
msgid "Select"
msgstr "Valg"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Ett resultat er tilgjengelig, trykk enter for å velge det."

#: includes/fields/class-acf-field-select.php:50
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultater er tilgjengelige, bruk opp- og nedpiltastene for å navigere."

#: includes/fields/class-acf-field-select.php:51
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Fant ingen treff"

#: includes/fields/class-acf-field-select.php:52
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Vennligst fyll inn ett eller flere tegn"

#: includes/fields/class-acf-field-select.php:53
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Vennligst fyll inn %d eller flere tegn"

#: includes/fields/class-acf-field-select.php:54
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Vennligst slett ett tegn"

#: includes/fields/class-acf-field-select.php:55
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Vennligst slett %d tegn"

#: includes/fields/class-acf-field-select.php:56
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Du kan bare velge ett element"

#: includes/fields/class-acf-field-select.php:57
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Du kan bare velge %d elementer"

#: includes/fields/class-acf-field-select.php:58
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Laster flere resultater &hellip;"

#: includes/fields/class-acf-field-select.php:59
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Søker&hellip;"

#: includes/fields/class-acf-field-select.php:60
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Lasting mislyktes"

#: includes/fields/class-acf-field-select.php:270 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Velg"

#: includes/fields/class-acf-field-select.php:504
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "Stilisert brukergrensesnitt"

#: includes/fields/class-acf-field-select.php:514
msgid "Use AJAX to lazy load choices?"
msgstr "Bruk AJAX for å laste valg i bakgrunnen ved behov?"

#: includes/fields/class-acf-field-select.php:525
msgid "Specify the value returned"
msgstr "Angi verdien som returneres"

#: includes/fields/class-acf-field-separator.php:36
msgid "Separator"
msgstr ""

#: includes/fields/class-acf-field-tab.php:36
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-tab.php:96
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""
"Fane-feltet vises ikke korrekt når det plasseres i et repeterende felt med "
"tabell-visning eller i et fleksibelt innholdsfelt"

#: includes/fields/class-acf-field-tab.php:97
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr "Bruk \"Fane-felt\" til å gruppere felter"

#: includes/fields/class-acf-field-tab.php:98
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"Alle felter som kommer etter dette \"fane-feltet\" (eller til et annet "
"\"fane-felt\" defineres) blir gruppert under overskriften til dette fane-"
"feltet."

#: includes/fields/class-acf-field-tab.php:112
msgid "Placement"
msgstr "Plassering"

#: includes/fields/class-acf-field-tab.php:124
msgid "End-point"
msgstr "Avslutning"

#: includes/fields/class-acf-field-tab.php:125
msgid "Use this field as an end-point and start a new group of tabs"
msgstr "Bruk dette feltet som en avslutning eller start en ny fane-gruppe"

#: includes/fields/class-acf-field-taxonomy.php:719
#: includes/fields/class-acf-field-true_false.php:95
#: includes/fields/class-acf-field-true_false.php:184 includes/input.php:266
#: pro/admin/views/html-settings-updates.php:103
msgid "No"
msgstr "Nei"

#: includes/fields/class-acf-field-taxonomy.php:738
msgid "None"
msgstr "Ingen"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Select the taxonomy to be displayed"
msgstr "Velg taksonomien som skal vises"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Appearance"
msgstr "Utseende"

#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Select the appearance of this field"
msgstr "Velg utseendet på dette feltet"

#: includes/fields/class-acf-field-taxonomy.php:785
msgid "Multiple Values"
msgstr "Flere verdier"

#: includes/fields/class-acf-field-taxonomy.php:787
msgid "Multi Select"
msgstr "Flervalgsboks"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Single Value"
msgstr "Enkeltverdi"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Radio Buttons"
msgstr "Radioknapper"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Create Terms"
msgstr "Opprett termer"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Allow new terms to be created whilst editing"
msgstr "Tillat at nye termer opprettes under redigering"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Save Terms"
msgstr "Lagre termer"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Connect selected terms to the post"
msgstr "Koble valgte termer til innlegget"

#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Load Terms"
msgstr "Hent termer"

#: includes/fields/class-acf-field-taxonomy.php:830
msgid "Load value from posts terms"
msgstr "Hent verdier fra andre innleggstermer"

#: includes/fields/class-acf-field-taxonomy.php:844
msgid "Term Object"
msgstr "Term-objekt"

#: includes/fields/class-acf-field-taxonomy.php:845
msgid "Term ID"
msgstr "Term-ID"

#: includes/fields/class-acf-field-taxonomy.php:904
#, php-format
msgid "User unable to add new %s"
msgstr "Brukeren kan ikke legge til ny %s"

#: includes/fields/class-acf-field-taxonomy.php:917
#, php-format
msgid "%s already exists"
msgstr "%s eksisterer allerede"

#: includes/fields/class-acf-field-taxonomy.php:958
#, php-format
msgid "%s added"
msgstr "%s lagt til"

#: includes/fields/class-acf-field-taxonomy.php:1003
msgid "Add"
msgstr "Legg til"

#: includes/fields/class-acf-field-text.php:36
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-text.php:178
#: includes/fields/class-acf-field-textarea.php:157
msgid "Character Limit"
msgstr "Karakterbegrensning"

#: includes/fields/class-acf-field-text.php:179
#: includes/fields/class-acf-field-textarea.php:158
msgid "Leave blank for no limit"
msgstr "La stå tomt for ingen grense"

#: includes/fields/class-acf-field-textarea.php:36
msgid "Text Area"
msgstr "Tekstområde"

#: includes/fields/class-acf-field-textarea.php:166
msgid "Rows"
msgstr "Rader"

#: includes/fields/class-acf-field-textarea.php:167
msgid "Sets the textarea height"
msgstr "Setter textarea-høyde"

#: includes/fields/class-acf-field-time_picker.php:36
msgid "Time Picker"
msgstr "Tidsvelger"

#: includes/fields/class-acf-field-true_false.php:36
msgid "True / False"
msgstr "Sann / Usann"

#: includes/fields/class-acf-field-true_false.php:94
#: includes/fields/class-acf-field-true_false.php:174 includes/input.php:265
#: pro/admin/views/html-settings-updates.php:93
msgid "Yes"
msgstr "Ja"

#: includes/fields/class-acf-field-true_false.php:142
msgid "Displays text alongside the checkbox"
msgstr "Viser tekst ved siden av avkryssingsboksen"

#: includes/fields/class-acf-field-true_false.php:170
msgid "On Text"
msgstr "På tekst"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when active"
msgstr "Teksten som vises når aktiv"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Av tekst"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Teksten som vises når inaktiv"

#: includes/fields/class-acf-field-url.php:36
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-url.php:165
msgid "Value must be a valid URL"
msgstr "Feltet må inneholde en gyldig URL"

#: includes/fields/class-acf-field-user.php:36 includes/locations.php:95
msgid "User"
msgstr "Bruker"

#: includes/fields/class-acf-field-user.php:408
msgid "Filter by role"
msgstr "Filtrer etter rolle"

#: includes/fields/class-acf-field-user.php:416
msgid "All user roles"
msgstr "Alle brukerroller"

#: includes/fields/class-acf-field-wysiwyg.php:36
msgid "Wysiwyg Editor"
msgstr "WYSIWYG Editor"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Visual"
msgstr "Visuell"

#: includes/fields/class-acf-field-wysiwyg.php:386
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-wysiwyg.php:392
msgid "Click to initialize TinyMCE"
msgstr "Klikk for å initialisere TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:445
msgid "Tabs"
msgstr "Faner"

#: includes/fields/class-acf-field-wysiwyg.php:450
msgid "Visual & Text"
msgstr "Visuell og tekst"

#: includes/fields/class-acf-field-wysiwyg.php:451
msgid "Visual Only"
msgstr "Bare visuell"

#: includes/fields/class-acf-field-wysiwyg.php:452
msgid "Text Only"
msgstr "Bare tekst"

#: includes/fields/class-acf-field-wysiwyg.php:459
msgid "Toolbar"
msgstr "Verktøylinje"

#: includes/fields/class-acf-field-wysiwyg.php:469
msgid "Show Media Upload Buttons?"
msgstr "Vise knapper for mediaopplasting?"

#: includes/fields/class-acf-field-wysiwyg.php:479
msgid "Delay initialization?"
msgstr "Utsette initialisering?"

#: includes/fields/class-acf-field-wysiwyg.php:480
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE blir ikke initialisert før feltet klikkes"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:304
msgid "Edit field group"
msgstr "Rediger feltgruppe"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Valider epot"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:588 pro/options-page.php:81
msgid "Update"
msgstr "Oppdater"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Innlegg oppdatert"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr "Søppel avdekket"

#: includes/input.php:258
msgid "Expand Details"
msgstr "Utvid detaljer"

#: includes/input.php:259
msgid "Collapse Details"
msgstr "Skjul detaljer"

#: includes/input.php:260
msgid "Validation successful"
msgstr "Vellykket validering"

#: includes/input.php:261 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Validering mislyktes"

#: includes/input.php:262
msgid "1 field requires attention"
msgstr "1 felt må ses på"

#: includes/input.php:263
#, php-format
msgid "%d fields require attention"
msgstr "%d felter må ses på"

#: includes/input.php:264
msgid "Restricted"
msgstr "Begrenset"

#: includes/input.php:268
msgid "Cancel"
msgstr ""

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Innlegg"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Side"

#: includes/locations.php:96
msgid "Forms"
msgstr "Skjemaer"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Vedlegg"

#: includes/locations/class-acf-location-attachment.php:113
#, php-format
msgid "All %s formats"
msgstr ""

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Kommentar"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Rolle nåværende bruker"

#: includes/locations/class-acf-location-current-user-role.php:114
msgid "Super Admin"
msgstr "Superadmin"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Nåværende bruker"

#: includes/locations/class-acf-location-current-user.php:101
msgid "Logged in"
msgstr "Logget inn"

#: includes/locations/class-acf-location-current-user.php:102
msgid "Viewing front end"
msgstr "Ser forside"

#: includes/locations/class-acf-location-current-user.php:103
msgid "Viewing back end"
msgstr "Ser adminside"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:113
#, fuzzy
msgid "Menu Locations"
msgstr "Sted"

#: includes/locations/class-acf-location-nav-menu.php:123
msgid "Menus"
msgstr ""

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Sideforelder"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Sidemal"

#: includes/locations/class-acf-location-page-template.php:102
#: includes/locations/class-acf-location-post-template.php:156
msgid "Default Template"
msgstr "Standardmal"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Sidetype"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Front Page"
msgstr "Forside"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Posts Page"
msgstr "Innleggsside"

#: includes/locations/class-acf-location-page-type.php:151
msgid "Top Level Page (no parent)"
msgstr "Toppnivåside (ingen forelder)"

#: includes/locations/class-acf-location-page-type.php:152
msgid "Parent Page (has children)"
msgstr "Foreldreside (har barn)"

#: includes/locations/class-acf-location-page-type.php:153
msgid "Child Page (has parent)"
msgstr "Barn-side (har foreldre)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Innleggskategori"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Innleggsformat"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Innleggsstatus"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Innleggstaksonomi"

#: includes/locations/class-acf-location-post-template.php:29
#, fuzzy
msgid "Post Template"
msgstr "Sidemal"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Taksonomi-term"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Brukerskjema"

#: includes/locations/class-acf-location-user-form.php:92
msgid "Add / Edit"
msgstr "Legg til / Rediger"

#: includes/locations/class-acf-location-user-form.php:93
msgid "Register"
msgstr "Registrer"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Brukerrolle"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Rediger"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Oppdater"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s verdi som kreves"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields Pro"

#: pro/admin/admin-options-page.php:196
msgid "Publish"
msgstr "Publiser"

#: pro/admin/admin-options-page.php:202
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Ingen egendefinerte feltgrupper funnet for denne valg-siden. <a href=\"%s"
"\">Opprette en egendefinert feltgruppe</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Feil</b>. Kan ikke koble til oppdateringsserveren"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:17
msgid "Updates"
msgstr "Oppdateringer"

#: pro/admin/views/html-settings-updates.php:11
msgid "Deactivate License"
msgstr "Deaktiver lisens"

#: pro/admin/views/html-settings-updates.php:11
msgid "Activate License"
msgstr "Aktiver lisens"

#: pro/admin/views/html-settings-updates.php:21
msgid "License Information"
msgstr "Lisensinformasjon"

#: pro/admin/views/html-settings-updates.php:24
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"For å låse opp oppdateringer må lisensnøkkelen skrives inn under. Se <a href="
"\"%s\" target=\"_blank\">detaljer og priser</a> dersom du ikke har "
"lisensnøkkel."

#: pro/admin/views/html-settings-updates.php:33
msgid "License Key"
msgstr "Lisensnøkkel"

#: pro/admin/views/html-settings-updates.php:65
msgid "Update Information"
msgstr "Oppdateringsinformasjon"

#: pro/admin/views/html-settings-updates.php:72
msgid "Current Version"
msgstr "Gjeldende versjon"

#: pro/admin/views/html-settings-updates.php:80
msgid "Latest Version"
msgstr "Siste versjon"

#: pro/admin/views/html-settings-updates.php:88
msgid "Update Available"
msgstr "Oppdatering tilgjengelig"

#: pro/admin/views/html-settings-updates.php:96
msgid "Update Plugin"
msgstr "Oppdater plugin"

#: pro/admin/views/html-settings-updates.php:98
msgid "Please enter your license key above to unlock updates"
msgstr "Oppgi lisensnøkkelen ovenfor for låse opp oppdateringer"

#: pro/admin/views/html-settings-updates.php:104
msgid "Check Again"
msgstr "Sjekk igjen"

#: pro/admin/views/html-settings-updates.php:121
msgid "Upgrade Notice"
msgstr "Oppgraderingsvarsel"

#: pro/fields/class-acf-field-clone.php:36
msgctxt "noun"
msgid "Clone"
msgstr "Klone"

#: pro/fields/class-acf-field-clone.php:858
msgid "Select one or more fields you wish to clone"
msgstr "Velg ett eller flere felt du ønsker å klone"

#: pro/fields/class-acf-field-clone.php:875
msgid "Display"
msgstr "Vis"

#: pro/fields/class-acf-field-clone.php:876
msgid "Specify the style used to render the clone field"
msgstr "Angi stil som brukes til å gjengi klone-feltet"

#: pro/fields/class-acf-field-clone.php:881
msgid "Group (displays selected fields in a group within this field)"
msgstr "Gruppe (viser valgt felt i en gruppe innenfor dette feltet)"

#: pro/fields/class-acf-field-clone.php:882
msgid "Seamless (replaces this field with selected fields)"
msgstr "Sømløs (erstatter dette feltet med utvalgte felter)"

#: pro/fields/class-acf-field-clone.php:903
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Etiketter vises som %s"

#: pro/fields/class-acf-field-clone.php:906
msgid "Prefix Field Labels"
msgstr "Prefiks feltetiketter"

#: pro/fields/class-acf-field-clone.php:917
#, php-format
msgid "Values will be saved as %s"
msgstr "Verdier vil bli lagret som %s"

#: pro/fields/class-acf-field-clone.php:920
msgid "Prefix Field Names"
msgstr "Prefiks feltnavn"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field"
msgstr "Ukjent felt"

#: pro/fields/class-acf-field-clone.php:1077
msgid "Unknown field group"
msgstr "Ukjent feltgruppe"

#: pro/fields/class-acf-field-clone.php:1081
#, php-format
msgid "All fields from %s field group"
msgstr "Alle felt fra %s feltgruppe"

#: pro/fields/class-acf-field-flexible-content.php:42
#: pro/fields/class-acf-field-repeater.php:230
#: pro/fields/class-acf-field-repeater.php:534
msgid "Add Row"
msgstr "Legg til rad"

#: pro/fields/class-acf-field-flexible-content.php:45
msgid "layout"
msgstr "oppsett"

#: pro/fields/class-acf-field-flexible-content.php:46
msgid "layouts"
msgstr "oppsett"

#: pro/fields/class-acf-field-flexible-content.php:47
msgid "remove {layout}?"
msgstr "fjern {oppsett}?"

#: pro/fields/class-acf-field-flexible-content.php:48
msgid "This field requires at least {min} {identifier}"
msgstr "Dette feltet krever minst {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:49
msgid "This field has a limit of {max} {identifier}"
msgstr "Dette feltet har en grense på {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:50
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Dette feltet krever minst {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:51
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Maksimalt {label} nådd ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:52
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} tilgjengelig (maks {max})"

#: pro/fields/class-acf-field-flexible-content.php:53
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} kreves (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:54
msgid "Flexible Content requires at least 1 layout"
msgstr "Fleksibelt innholdsfelt krever minst en layout"

#: pro/fields/class-acf-field-flexible-content.php:288
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klikk \"%s\"-knappen nedenfor for å begynne å lage oppsettet"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Legg til oppsett"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Remove layout"
msgstr "Fjern oppsett"

#: pro/fields/class-acf-field-flexible-content.php:425
#: pro/fields/class-acf-field-repeater.php:360
msgid "Click to toggle"
msgstr "Klikk for å veksle"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder Layout"
msgstr "Endre rekkefølge på oppsett"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder"
msgstr "Endre rekkefølge"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete Layout"
msgstr "Slett oppsett"

#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate Layout"
msgstr "Dupliser oppsett"

#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New Layout"
msgstr "Legg til nytt oppsett"

#: pro/fields/class-acf-field-flexible-content.php:645
msgid "Min"
msgstr "Minimum"

#: pro/fields/class-acf-field-flexible-content.php:658
msgid "Max"
msgstr "Maksimum"

#: pro/fields/class-acf-field-flexible-content.php:685
#: pro/fields/class-acf-field-repeater.php:530
msgid "Button Label"
msgstr "Knappetikett"

#: pro/fields/class-acf-field-flexible-content.php:694
msgid "Minimum Layouts"
msgstr "Minimum oppsett"

#: pro/fields/class-acf-field-flexible-content.php:703
msgid "Maximum Layouts"
msgstr "Maksimum oppsett"

#: pro/fields/class-acf-field-gallery.php:52
msgid "Add Image to Gallery"
msgstr "Legg bildet til galleri"

#: pro/fields/class-acf-field-gallery.php:56
msgid "Maximum selection reached"
msgstr "Maksimalt utvalg nådd"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Lengde"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "Bildetekst"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "Alternativ tekst"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Legg til galleri"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Massehandlinger"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "Sorter etter dato lastet opp"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "Sorter etter dato endret"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "Sorter etter tittel"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Snu gjeldende rekkefølge"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Lukk"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Minimum antall valg"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Maksimum antall valg"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "Sett inn"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "Angi hvor nye vedlegg er lagt"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "Tilføy til slutten"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "Sett inn foran"

#: pro/fields/class-acf-field-repeater.php:47
msgid "Minimum rows reached ({min} rows)"
msgstr "Minimum antall rader nådd ({min} rader)"

#: pro/fields/class-acf-field-repeater.php:48
msgid "Maximum rows reached ({max} rows)"
msgstr "Maksimum antall rader nådd ({max} rader)"

#: pro/fields/class-acf-field-repeater.php:405
msgid "Add row"
msgstr "Legg til rad"

#: pro/fields/class-acf-field-repeater.php:406
msgid "Remove row"
msgstr "Fjern rad"

#: pro/fields/class-acf-field-repeater.php:483
msgid "Collapsed"
msgstr "Sammenfoldet"

#: pro/fields/class-acf-field-repeater.php:484
msgid "Select a sub field to show when row is collapsed"
msgstr "Velg et underfelt å vise når raden er skjult"

#: pro/fields/class-acf-field-repeater.php:494
msgid "Minimum Rows"
msgstr "Minimum antall rader"

#: pro/fields/class-acf-field-repeater.php:504
msgid "Maximum Rows"
msgstr "Maksimum antall rader"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Ingen side for alternativer eksisterer"

#: pro/options-page.php:51
msgid "Options"
msgstr "Valg"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Alternativer er oppdatert"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"For å låse opp oppdateringer må lisensnøkkelen skrives inn på <a href=\"%s"
"\">oppdateringer</a>-siden. Se <a href=\"%s\" target=\"_blank\">detaljer og "
"priser</a> dersom du ikke har lisensnøkkel."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Getting Started"
#~ msgstr "Kom i gang"

#~ msgid "Field Types"
#~ msgstr "Felttyper"

#~ msgid "Functions"
#~ msgstr "Funksjoner"

#~ msgid "Actions"
#~ msgstr "Handlinger"

#~ msgid "Features"
#~ msgstr "Funksjoner"

#~ msgid "How to"
#~ msgstr "Veiledning"

#~ msgid "Tutorials"
#~ msgstr "Veiledninger"

#~ msgid "FAQ"
#~ msgstr "OSS"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr "Termmeta-oppgradering ikke mulig (termmeta-tabell finnes ikke)"

#~ msgid "Error"
#~ msgstr "Feil"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 felt må ses på"
#~ msgstr[1] "%d felter må ses på"

#~ msgid ""
#~ "Error validating ACF PRO license URL (website does not match). Please re-"
#~ "activate your license"
#~ msgstr ""
#~ "Feil under validering av ACF PRO-lisens URL (nettsted samsvarer ikke). "
#~ "Vennligst reaktiver lisensen"

#~ msgid "Disabled"
#~ msgstr "Deaktivert"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Deaktivert <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Deaktiverte <span class=\"count\">(%s)</span>"

#~ msgid "'How to' guides"
#~ msgstr "\"Hvordan\" -guider"

#~ msgid "Created by"
#~ msgstr "Laget av"

#~ msgid "No updates available"
#~ msgstr "Ingen oppdateringer tilgjengelige"

#~ msgid "Error loading update"
#~ msgstr "Feil ved lasting av oppdatering"

#~ msgid "Database Upgrade complete"
#~ msgstr "Databaseoppgradering fullført"

#~ msgid "Return to network dashboard"
#~ msgstr "Tilbake til nettverkskontrollpanel"

#~ msgid "See what's new"
#~ msgstr "Se hva som er nytt"

#~ msgid "No embed found for the given URL"
#~ msgstr "Ingen embed funnet for den gitte URL-en"

#~ msgid "eg. Show extra content"
#~ msgstr "f. eks. Vis ekstra innhold"

#~ msgid "No Custom Field Groups found for this options page"
#~ msgstr "Ingen egendefinerte feltgrupper funnet for dette valget"

#~ msgid "Create a Custom Field Group"
#~ msgstr "Opprett en egendefinert feltgruppe"

#~ msgid ""
#~ "Error validating license URL (website does not match). Please re-activate "
#~ "your license"
#~ msgstr ""
#~ "Feil ved validering av lisens-URL (nettsted samsvarer ikke). Vennligst "
#~ "reaktiver din lisens"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Suksess.</b> Importverktøyet la til %s feltgrupper: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Advarsel.</b> Importverktøyet oppdaget %s feltgrupper allerede "
#~ "eksisterer og har blitt ignorert: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Oppgrader ACF"

#~ msgid "Upgrade"
#~ msgstr "Oppgrader"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "Følgende områder krever en database-oppgradering. Sjekk de du vil "
#~ "oppdatere, og klikk deretter på \"Upgrade Database\"."

#~ msgid "Select"
#~ msgstr "Select"

#~ msgid "Done"
#~ msgstr "Fullført"

#~ msgid "Today"
#~ msgstr "Idag"

#~ msgid "Show a different month"
#~ msgstr "Vise en annen måned"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Tilkoblingsfeil</b>. Beklager, prøv på nytt"

#~ msgid "See what's new in"
#~ msgstr "Se hva som er nytt i"

#~ msgid "version"
#~ msgstr "versjon"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Dra og slipp for å endre rekkefølgen"

#~ msgid "Upgrading data to"
#~ msgstr "Oppgradere data til"

#~ msgid "Return format"
#~ msgstr "Format som skal returneres"

#~ msgid "uploaded to this post"
#~ msgstr "lastet opp til dette innlegget"

#~ msgid "File Name"
#~ msgstr "Filnavn"

#~ msgid "File Size"
#~ msgstr "Filstørrelse"

#~ msgid "No File selected"
#~ msgstr "Ingen fil valgt"

#~ msgid "Add new %s "
#~ msgstr "Legg til ny %s"

#~ msgid "Save Options"
#~ msgstr "Lagringsvalg"

#~ msgid "License"
#~ msgstr "Lisens"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Oppgi lisensnøkkelen nedenfor for å låse opp oppdateringer. Hvis du ikke "
#~ "har en lisensnøkkel, se"

#~ msgid "details & pricing"
#~ msgstr "detaljer og priser"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "For å aktivere oppdateringer, angi din lisensnøkkel på <a href=\"%s"
#~ "\">oppdateringer</a> -siden. Hvis du ikke har en lisensnøkkel, se <a href="
#~ "\"%s\">detaljer og priser</a>"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

#~ msgid "elliot condon"
#~ msgstr "elliot condon"
