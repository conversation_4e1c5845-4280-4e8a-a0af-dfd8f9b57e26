# Copyright (C) 2014
# This file is distributed under the same license as the  package.
msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields PRO\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2019-05-02 09:38+0100\n"
"PO-Revision-Date: 2019-05-02 09:52+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: pt_PT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.2.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;__ngettext:1,2;_n:1,2;__ngettext_noop:1,2;_n_noop:1,2;_c,_nc:4c,1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;_nx_noop:4c,1,2;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:80
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:363 includes/admin/admin.php:58
msgid "Field Groups"
msgstr "Grupos de campos"

#: acf.php:364
msgid "Field Group"
msgstr "Grupo de campos"

#: acf.php:365 acf.php:397 includes/admin/admin.php:59
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New"
msgstr "Adicionar novo"

#: acf.php:366
msgid "Add New Field Group"
msgstr "Adicionar novo grupo de campos"

#: acf.php:367
msgid "Edit Field Group"
msgstr "Editar grupo de campos"

#: acf.php:368
msgid "New Field Group"
msgstr "Novo grupo de campos"

#: acf.php:369
msgid "View Field Group"
msgstr "Ver grupo de campos"

#: acf.php:370
msgid "Search Field Groups"
msgstr "Pesquisar grupos de campos"

#: acf.php:371
msgid "No Field Groups found"
msgstr "Nenhum grupo de campos encontrado"

#: acf.php:372
msgid "No Field Groups found in Trash"
msgstr "Nenhum grupo de campos encontrado no lixo"

#: acf.php:395 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:530
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Campos"

#: acf.php:396
msgid "Field"
msgstr "Campo"

#: acf.php:398
msgid "Add New Field"
msgstr "Adicionar novo campo"

#: acf.php:399
msgid "Edit Field"
msgstr "Editar campo"

#: acf.php:400 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Novo campo"

#: acf.php:401
msgid "View Field"
msgstr "Ver campo"

#: acf.php:402
msgid "Search Fields"
msgstr "Pesquisar campos"

#: acf.php:403
msgid "No Fields found"
msgstr "Nenhum campo encontrado"

#: acf.php:404
msgid "No Fields found in Trash"
msgstr "Nenhum campo encontrado no lixo"

#: acf.php:443 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:587
msgid "Inactive"
msgstr "Inactivo"

#: acf.php:448
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactivo <span class=\"count\">(%s)</span>"
msgstr[1] "Inactivos <span class=\"count\">(%s)</span>"

#: includes/acf-field-functions.php:828
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr "(sem legenda)"

#: includes/acf-field-group-functions.php:816
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr "cópia"

#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "Grupo de campos actualizado."

#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "Grupo de campos eliminado."

#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "Grupo de campos guardado."

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "Grupo de campos agendado."

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "Rascunho de grupo de campos actualizado."

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "O prefixo \"field_\" não pode ser utilizado no início do nome do campo."

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "Este campo não pode ser movido até que as suas alterações sejam guardadas."

#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "O título do grupo de campos é obrigatório"

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "Mover para o lixo. Tem certeza?"

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "Nenhum campo de opções disponível"

#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "Mover campo personalizado"

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "Seleccionado"

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(este campo)"

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3862
msgid "or"
msgstr "ou"

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "Nulo"

#: includes/admin/admin-field-group.php:221
msgid "Location"
msgstr "Localização"

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Definições"

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr "Chaves dos campos"

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Activo"

#: includes/admin/admin-field-group.php:771
msgid "Move Complete."
msgstr "Movido com sucesso."

#: includes/admin/admin-field-group.php:772
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "O campo %s pode agora ser encontrado no grupo de campos %s"

#: includes/admin/admin-field-group.php:773
msgid "Close Window"
msgstr "Fechar janela"

#: includes/admin/admin-field-group.php:814
msgid "Please select the destination for this field"
msgstr "Por favor seleccione o destinho para este campo"

#: includes/admin/admin-field-group.php:821
msgid "Move Field"
msgstr "Mover campo"

#: includes/admin/admin-field-groups.php:89
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:156
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicados."

#: includes/admin/admin-field-groups.php:243
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Grupo de campos sincronizado."
msgstr[1] "%s grupos de campos sincronizados."

#: includes/admin/admin-field-groups.php:414
#: includes/admin/admin-field-groups.php:577
msgid "Sync available"
msgstr "Sincronização disponível"

#: includes/admin/admin-field-groups.php:527 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:372
msgid "Title"
msgstr "Título"

#: includes/admin/admin-field-groups.php:528
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:399
msgid "Description"
msgstr "Descrição"

#: includes/admin/admin-field-groups.php:529
msgid "Status"
msgstr "Estado"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:626
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Personalize o WordPress com campos intuitivos, poderosos e profissionais."

#: includes/admin/admin-field-groups.php:628
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Registo de alterações"

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Veja o que há de novo na <a href=\"%s\">versão %s</a>."

#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr "Recursos"

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr "Site"

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr "Documentação"

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr "Suporte"

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:84
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Obrigado por criar com o <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:686
msgid "Duplicate this item"
msgstr "Duplicar este item"

#: includes/admin/admin-field-groups.php:686
#: includes/admin/admin-field-groups.php:702
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-field-groups.php:719
#: includes/fields/class-acf-field-google-map.php:165
#: includes/fields/class-acf-field-relationship.php:593
msgid "Search"
msgstr "Pesquisa"

#: includes/admin/admin-field-groups.php:778
#, php-format
msgid "Select %s"
msgstr "Seleccionar %s"

#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr "Sincronizar grupo de campos"

#: includes/admin/admin-field-groups.php:786
#: includes/admin/admin-field-groups.php:816
msgid "Sync"
msgstr "Sincronizar"

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr "Aplicar"

#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr "Acções por lotes"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Ferramentas"

#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:94
#: includes/admin/admin-upgrade.php:156
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Actualizar base de dados"

#: includes/admin/admin-upgrade.php:180
msgid "Review sites & upgrade"
msgstr "Rever sites e actualizar"

#: includes/admin/admin.php:54 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Campos personalizados"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informações"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "O que há de novo"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Exportar grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Gerar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Nenhum grupo de campos seleccionado"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Foi exportado 1 grupo de campos."
msgstr[1] "Foram exportados %s grupos de campos."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Seleccione os grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Select the field groups you would like to export and then select your export method. Use the download button to export to a .json file which you can then import to another ACF installation. Use the generate button to export to PHP code which you can place in your theme."
msgstr "Seleccione os grupos de campos que deseja exportar e seleccione o método de exportação. Utilize o botão Descarregar para exportar um ficheiro .json que poderá depois importar para outra instalação do ACF. Utilize o botão Gerar para exportar o código PHP que poderá incorporar no seu tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exportar ficheiro"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid "The following code can be used to register a local version of the selected field group(s). A local field group can provide many benefits such as faster load times, version control & dynamic fields/settings. Simply copy and paste the following code to your theme's functions.php file or include it within an external file."
msgstr "O código abaixo pode ser usado para registar uma versão local do(s) grupo(s) de campos seleccionado(s). Um grupo de campos local tem alguns benefícios, tais como maior velocidade de carregamento, controlo de versão, definições e campos dinâmicos. Copie e cole o código abaixo no ficheiro functions.php do seu tema, ou inclua-o num ficheiro externo."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Copiar para a área de transferência"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Copiado"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importar grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid "Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the field groups."
msgstr "Seleccione o ficheiro JSON do Advanced Custom Fields que deseja importar. Ao clicar no botão Importar abaixo, o ACF irá importar os grupos de campos."

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Seleccionar ficheiro"

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "Importar ficheiro"

#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "Nenhum ficheiro seleccionado"

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr "Erro ao carregar ficheiro. Por favor tente de novo."

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr "Tipo de ficheiro incorrecto"

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr "Ficheiro de importação vazio"

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Foi importado 1 grupo de campos."
msgstr[1] "Foram importados %s grupos de campos."

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Lógica condicional"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Mostrar este campo se"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "e"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Adicionar grupo de regras"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:410
#: pro/fields/class-acf-field-repeater.php:299
msgid "Drag to reorder"
msgstr "Arraste para reordenar"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:359
msgid "Edit"
msgstr "Editar"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Mover campo para outro grupo"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Mover"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Eliminar campo"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete"
msgstr "Eliminar"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Legenda do campo"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Este é o nome que será mostrado na página EDITAR."

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Nome do campo"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Uma única palavra, sem espaços. São permitidos underscores (_) e traços (-)."

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Instruções"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instruções para os autores. São mostradas ao preencher e submeter dados."

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Obrigatório?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Atributos do wrapper"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "largura"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "classe"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Fechar campo"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ordem"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "Legenda"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Name"
msgstr "Nome"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Chave"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tipo"

#: includes/admin/views/field-group-fields.php:14
msgid "No fields. Click the <strong>+ Add Field</strong> button to create your first field."
msgstr "Nenhum campo. Clique no botão <strong>+ Adicionar campo</strong> para criar seu primeiro campo."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Adicionar campo"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regras"

#: includes/admin/views/field-group-locations.php:10
msgid "Create a set of rules to determine which edit screens will use these advanced custom fields"
msgstr "Crie um conjunto de regras para determinar em que ecrãs de edição serão utilizados estes campos personalizados avançados"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Predefinido (metabox do WP)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Simples (sem metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Posição"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Acima (depois do título)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (depois do conteúdo)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Posição da legenda"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Alinhado acima"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Alinhado à esquerda"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Posição das instruções"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Abaixo das legendas"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Abaixo dos campos"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Nº. de ordem"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Serão mostrados primeiro os grupos de campos com menor número de ordem."

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Mostrado na lista de grupos de campos"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Ligação permanente"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Editor de conteúdo"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Excerto"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Discussão"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Comentários"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Revisões"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Formato"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Atributos da página"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:607
msgid "Featured Image"
msgstr "Imagem de destaque"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Categorias"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Etiquetas"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Enviar trackbacks"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Esconder no ecrã"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Seleccione</b> os itens a <b>esconder</b> do ecrã de edição."

#: includes/admin/views/field-group-options.php:129
msgid "If multiple field groups appear on an edit screen, the first field group's options will be used (the one with the lowest order number)"
msgstr "Se forem mostrados vários grupos de campos num ecrã de edição, serão utilizadas as opções do primeiro grupo de campos. (o que tiver menor número de ordem)"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid "The following sites require a DB upgrade. Check the ones you want to update and then click %s."
msgstr "Os sites seguintes necessitam de actualização da BD. Seleccione os que quer actualizar e clique em %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Actualizar sites"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Site"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "O site necessita de actualizar a base de dados de %s para %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "O site está actualizado"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr "Actualização da base de dados concluída. <a href=\"%s\">Voltar ao painel da rede</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "Por favor, seleccione pelo menos um site para actualizar."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid "It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?"
msgstr "É recomendável que faça uma cópia de segurança da sua base de dados antes de continuar. Tem a certeza que quer actualizar agora?"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "A actualizar dados para a versão %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr "Actualização concluída."

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr "Falhou ao actualizar."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "A ler tarefas de actualização..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Actualização da base de dados concluída. <a href=\"%s\">Ver o que há de novo</a>"

#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:33
msgid "No updates available."
msgstr "Nenhuma actualização disponível."

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "Voltar para todas as ferramentas"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos se"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Repetidor"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Conteúdo flexível"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Página de opções"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Actualização da base de dados necessária"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Obrigado por actualizar para o %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid "This version contains improvements to your database and requires an upgrade."
msgstr "Esta versão inclui melhorias na base de dados e requer uma actualização."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid "Please also check all premium add-ons (%s) are updated to the latest version."
msgstr "Por favor, verifique se todos os add-ons premium (%s) estão actualizados para a última versão."

#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Add-ons"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Descarregar e instalar"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instalado"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Bem-vindo ao Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid "Thank you for updating! ACF %s is bigger and better than ever before. We hope you like it."
msgstr "Obrigado por actualizar! O ACF %s está maior e melhor do que nunca. Esperamos que goste."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Uma experiência mais fácil"

#: includes/admin/views/settings-info.php:19
msgid "Improved Usability"
msgstr "Usabilidade melhorada"

#: includes/admin/views/settings-info.php:20
msgid "Including the popular Select2 library has improved both usability and speed across a number of field types including post object, page link, taxonomy and select."
msgstr "A inclusão da popular biblioteca Select2 melhorou a usabilidade e a velocidade de tipos de campos como conteúdo, ligação de página, taxonomia e selecção."

#: includes/admin/views/settings-info.php:24
msgid "Improved Design"
msgstr "Design melhorado"

#: includes/admin/views/settings-info.php:25
msgid "Many fields have undergone a visual refresh to make ACF look better than ever! Noticeable changes are seen on the gallery, relationship and oEmbed (new) fields!"
msgstr "Muitos campos sofreram alterações visuais para que a aparência do ACF esteja melhor que nunca! Alterações notáveis nos campos de galeria, relação e oEmbed (novo)!"

#: includes/admin/views/settings-info.php:29
msgid "Improved Data"
msgstr "Dados melhorados"

#: includes/admin/views/settings-info.php:30
msgid "Redesigning the data architecture has allowed sub fields to live independently from their parents. This allows you to drag and drop fields in and out of parent fields!"
msgstr "A reformulação da arquitectura dos dados permite que os subcampos existam independentemente dos seus superiores. Isto permite-lhe arrastar e largar campos para dentro e para fora de campos superiores!"

#: includes/admin/views/settings-info.php:38
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Adeus add-ons. Olá PRO."

#: includes/admin/views/settings-info.php:41
msgid "Introducing ACF PRO"
msgstr "Introdução ao ACF PRO"

#: includes/admin/views/settings-info.php:42
msgid "We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Estamos a alterar o modo como as funcionalidades premium são distribuídas!"

#: includes/admin/views/settings-info.php:43
#, php-format
msgid "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro version of ACF</a>. With both personal and developer licenses available, premium functionality is more affordable and accessible than ever before!"
msgstr "Todos os 4 add-ons premium foram combinados numa única <a href=\"%s\">versão Pro do ACF</a>. Com licenças pessoais e para programadores, as funcionalidades premium estão agora mais acessíveis que nunca!"

#: includes/admin/views/settings-info.php:47
msgid "Powerful Features"
msgstr "Funcionalidades poderosas"

#: includes/admin/views/settings-info.php:48
msgid "ACF PRO contains powerful features such as repeatable data, flexible content layouts, a beautiful gallery field and the ability to create extra admin options pages!"
msgstr "O ACF PRO tem funcionalidades poderosas, tais como dados repetíveis, layouts de conteúdo flexível, um campo de galeria e a possibilidade de criar páginas de opções de administração adicionais!"

#: includes/admin/views/settings-info.php:49
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Mais informações sobre as <a href=\"%s\">funcionalidades do ACF PRO</a>."

#: includes/admin/views/settings-info.php:53
msgid "Easy Upgrading"
msgstr "Actualização fácil"

#: includes/admin/views/settings-info.php:54
msgid "Upgrading to ACF PRO is easy. Simply purchase a license online and download the plugin!"
msgstr "É fácil actualizar para o ACF PRO. Basta comprar uma licença online e descarregar o plugin!"

#: includes/admin/views/settings-info.php:55
#, php-format
msgid "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, but if you do have one, please contact our support team via the <a href=\"%s\">help desk</a>."
msgstr "Escrevemos um <a href=\"%s\">guia de actualização</a> para responder a todas as dúvidas, se tiver alguma questão, por favor contacte a nossa equipa de suporte através da <a href=\"%s\">central de ajuda</a>."

#: includes/admin/views/settings-info.php:64
msgid "New Features"
msgstr "Novas funcionalidades"

#: includes/admin/views/settings-info.php:69
msgid "Link Field"
msgstr "Campo de ligação"

#: includes/admin/views/settings-info.php:70
msgid "The Link field provides a simple way to select or define a link (url, title, target)."
msgstr "O campo de ligação permite facilmente seleccionar ou definir uma ligação (URL, título, destino)."

#: includes/admin/views/settings-info.php:74
msgid "Group Field"
msgstr "Campo de grupo"

#: includes/admin/views/settings-info.php:75
msgid "The Group field provides a simple way to create a group of fields."
msgstr "O campo de grupo permite facilmente criar um grupo de campos."

#: includes/admin/views/settings-info.php:79
msgid "oEmbed Field"
msgstr "Campo de oEmbed"

#: includes/admin/views/settings-info.php:80
msgid "The oEmbed field allows an easy way to embed videos, images, tweets, audio, and other content."
msgstr "O campo de oEmbed permite facilmente incorporar vídeos, imagens, tweets, áudio ou outros conteúdos."

#: includes/admin/views/settings-info.php:84
msgid "Clone Field"
msgstr "Campo de clone"

#: includes/admin/views/settings-info.php:85
msgid "The clone field allows you to select and display existing fields."
msgstr "O campo de clone permite seleccionar e mostrar campos existentes."

#: includes/admin/views/settings-info.php:89
msgid "More AJAX"
msgstr "Mais AJAX"

#: includes/admin/views/settings-info.php:90
msgid "More fields use AJAX powered search to speed up page loading."
msgstr "Mais campos utilizam pesquisa com AJAX para aumentar a velocidade de carregamento."

#: includes/admin/views/settings-info.php:94
msgid "Local JSON"
msgstr "JSON local"

#: includes/admin/views/settings-info.php:95
msgid "New auto export to JSON feature improves speed and allows for syncronisation."
msgstr "Nova funcionalidade de exportação automática para JSON melhora a velocidade e permite sincronização."

#: includes/admin/views/settings-info.php:99
msgid "Easy Import / Export"
msgstr "Fácil importação e exportação"

#: includes/admin/views/settings-info.php:100
msgid "Both import and export can easily be done through a new tools page."
msgstr "Pode facilmente importar e exportar a partir da nova página de ferramentas."

#: includes/admin/views/settings-info.php:104
msgid "New Form Locations"
msgstr "Novas localizações de formulários"

#: includes/admin/views/settings-info.php:105
msgid "Fields can now be mapped to menus, menu items, comments, widgets and all user forms!"
msgstr "Os campos agora podem ser mapeados para menus, itens de menu, comentários, widgets e formulários de utilizador!"

#: includes/admin/views/settings-info.php:109
msgid "More Customization"
msgstr "Maior personalização"

#: includes/admin/views/settings-info.php:110
msgid "New PHP (and JS) actions and filters have been added to allow for more customization."
msgstr "Foram adicionadas novas acções e filtros de PHP (e JS) para permitir maior personalização."

#: includes/admin/views/settings-info.php:114
msgid "Fresh UI"
msgstr "Nova interface"

#: includes/admin/views/settings-info.php:115
msgid "The entire plugin has had a design refresh including new field types, settings and design!"
msgstr "Toda a interface do plugin foi actualizada, incluindo novos tipos de campos, definições e design!"

#: includes/admin/views/settings-info.php:119
msgid "New Settings"
msgstr "Novas definições"

#: includes/admin/views/settings-info.php:120
msgid "Field group settings have been added for Active, Label Placement, Instructions Placement and Description."
msgstr "Foram adicionadas definições aos grupos de campos, tais como activação, posição da legenda, posição das instruções e descrição."

#: includes/admin/views/settings-info.php:124
msgid "Better Front End Forms"
msgstr "Melhores formulários para o seu site"

#: includes/admin/views/settings-info.php:125
msgid "acf_form() can now create a new post on submission with lots of new settings."
msgstr "Com acf_form() agora pode criar um novo conteúdo ao submeter, com muito mais definições."

#: includes/admin/views/settings-info.php:129
msgid "Better Validation"
msgstr "Melhor validação"

#: includes/admin/views/settings-info.php:130
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr "A validação de formulários agora é feita com PHP + AJAX em vez de apenas JS."

#: includes/admin/views/settings-info.php:134
msgid "Moving Fields"
msgstr "Mover campos"

#: includes/admin/views/settings-info.php:135
msgid "New field group functionality allows you to move a field between groups & parents."
msgstr "Nova funcionalidade de grupo de campos permite mover um campo entre grupos e superiores."

#: includes/admin/views/settings-info.php:146
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Pensamos que vai gostar das alterações na versão %s."

#: includes/api/api-helpers.php:1003
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/api/api-helpers.php:1004
msgid "Medium"
msgstr "Média"

#: includes/api/api-helpers.php:1005
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:1054
msgid "Full Size"
msgstr "Tamanho original"

#: includes/api/api-helpers.php:1775 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(sem título)"

#: includes/api/api-helpers.php:3783
#, php-format
msgid "Image width must be at least %dpx."
msgstr "A largura da imagem deve ser pelo menos de %dpx."

#: includes/api/api-helpers.php:3788
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "A largura da imagem não deve exceder os %dpx."

#: includes/api/api-helpers.php:3804
#, php-format
msgid "Image height must be at least %dpx."
msgstr "A altura da imagem deve ser pelo menos de %dpx."

#: includes/api/api-helpers.php:3809
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "A altura da imagem não deve exceder os %dpx."

#: includes/api/api-helpers.php:3827
#, php-format
msgid "File size must be at least %s."
msgstr "O tamanho do ficheiro deve ser pelo menos de %s."

#: includes/api/api-helpers.php:3832
#, php-format
msgid "File size must must not exceed %s."
msgstr "O tamanho do ficheiro não deve exceder %s."

#: includes/api/api-helpers.php:3866
#, php-format
msgid "File type must be %s."
msgstr "O tipo de ficheiro deve ser %s."

#: includes/assets.php:168
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "As alterações que fez serão ignoradas se navegar para fora desta página."

#: includes/assets.php:171 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Seleccionar"

#: includes/assets.php:172
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/assets.php:173
msgctxt "verb"
msgid "Update"
msgstr "Actualizar"

#: includes/assets.php:174
msgid "Uploaded to this post"
msgstr "Carregados neste artigo"

#: includes/assets.php:175
msgid "Expand Details"
msgstr "Expandir detalhes"

#: includes/assets.php:176
msgid "Collapse Details"
msgstr "Minimizar detalhes"

#: includes/assets.php:177
msgid "Restricted"
msgstr "Restrito"

#: includes/assets.php:178 includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Todas as imagens"

#: includes/assets.php:181
msgid "Validation successful"
msgstr "Validação bem sucedida"

#: includes/assets.php:182 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "A validação falhou"

#: includes/assets.php:183
msgid "1 field requires attention"
msgstr "1 campo requer a sua atenção"

#: includes/assets.php:184
#, php-format
msgid "%d fields require attention"
msgstr "%d campos requerem a sua atenção"

#: includes/assets.php:187
msgid "Are you sure?"
msgstr "Tem a certeza?"

#: includes/assets.php:188 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Sim"

#: includes/assets.php:189 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Não"

#: includes/assets.php:190 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:141
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:360
#: pro/fields/class-acf-field-gallery.php:549
msgid "Remove"
msgstr "Remover"

#: includes/assets.php:191
msgid "Cancel"
msgstr "Cancelar"

#: includes/assets.php:194
msgid "Has any value"
msgstr "Tem um valor qualquer"

#: includes/assets.php:195
msgid "Has no value"
msgstr "Não tem valor"

#: includes/assets.php:196
msgid "Value is equal to"
msgstr "O valor é igual a"

#: includes/assets.php:197
msgid "Value is not equal to"
msgstr "O valor é diferente de"

#: includes/assets.php:198
msgid "Value matches pattern"
msgstr "O valor corresponde ao padrão"

#: includes/assets.php:199
msgid "Value contains"
msgstr "O valor contém"

#: includes/assets.php:200
msgid "Value is greater than"
msgstr "O valor é maior do que"

#: includes/assets.php:201
msgid "Value is less than"
msgstr "O valor é menor do que"

#: includes/assets.php:202
msgid "Selection is greater than"
msgstr "A selecção é maior do que"

#: includes/assets.php:203
msgid "Selection is less than"
msgstr "A selecção é menor do que"

#: includes/assets.php:206 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "Editar grupo de campos"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Tipo de campo não existe"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Desconhecido"

#: includes/fields.php:349
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Conteúdo"

#: includes/fields.php:351
msgid "Choice"
msgstr "Opção"

#: includes/fields.php:352
msgid "Relational"
msgstr "Relacional"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:553
#: pro/fields/class-acf-field-flexible-content.php:602
#: pro/fields/class-acf-field-repeater.php:448
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Acordeão"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Aberto"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Mostrar este item de acordeão aberto ao carregar a página."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Expandir múltiplos"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Permite abrir este item de acordeão sem fechar os restantes."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Fim"

#: includes/fields/class-acf-field-accordion.php:120
msgid "Define an endpoint for the previous accordion to stop. This accordion will not be visible."
msgstr "Define o fim do acordeão anterior. Este item de acordeão não será visível."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grupo de botões"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Opções"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Insira cada opção numa linha separada."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr "Para maior controlo, pode especificar tanto os valores como as legendas:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "vermelho : Vermelho"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:393
msgid "Allow Null?"
msgstr "Permitir nulo?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "Valor por omissão"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "Mostrado ao criar um novo conteúdo"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "Valor devolvido"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Especifica o valor devolvido na frente do site."

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "Caixa de selecção"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Seleccionar tudo"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Adicionar nova opção"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Permitir personalização"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Permite adicionar valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Guardar personalização"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Guarda valores personalizados nas opções do campo"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "Insira cada valor por omissão numa linha separada"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Selecção"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Preceder com caixa de selecção adicional para seleccionar todas as opções"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Selecção de cor"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Limpar"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Por omissão"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Seleccionar cor"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Cor actual"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Selecção de data"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Concluído"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoje"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Seguinte"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Formato de visualização"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "O formato de visualização ao editar um conteúdo"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Personalizado:"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Formato guardado"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "O formato usado ao guardar um valor"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:634
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:412
msgid "Return Format"
msgstr "Formato devolvido"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "O formato devolvido através das <em>template functions</em>"

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Semana começa em"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Selecção de data e hora"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Escolha a hora"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milissegundo"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegundo"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuso horário"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Agora"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Concluído"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Texto predefinido"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Mostrado dentro do campo"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Preceder"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Mostrado antes do campo"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Suceder"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Mostrado depois do campo"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Ficheiro"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Editar ficheiro"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Actualizar ficheiro"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Nome do ficheiro"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:294
#: pro/fields/class-acf-field-gallery.php:708
#: pro/fields/class-acf-field-gallery.php:737
msgid "File size"
msgstr "Tamanho do ficheiro"

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Adicionar ficheiro"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Array do ficheiro"

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "URL do ficheiro"

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "ID do ficheiro"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:673
msgid "Library"
msgstr "Biblioteca"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:674
msgid "Limit the media library choice"
msgstr "Limita a escolha da biblioteca de media."

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:236
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:679
#: pro/locations/class-acf-location-block.php:79
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:237
#: pro/fields/class-acf-field-gallery.php:680
msgid "Uploaded to post"
msgstr "Carregados no artigo"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:244
#: pro/fields/class-acf-field-gallery.php:687
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "Restringe que ficheiros podem ser carregados."

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:716
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:745
msgid "Allowed file types"
msgstr "Tipos de ficheiros permitidos"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:746
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista separada por vírgulas. Deixe em branco para permitir todos os tipos."

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa do Google"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Desculpe, este navegador não suporta geolocalização."

#: includes/fields/class-acf-field-google-map.php:166
msgid "Clear location"
msgstr "Limpar localização"

#: includes/fields/class-acf-field-google-map.php:167
msgid "Find current location"
msgstr "Encontrar a localização actual"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Search for address..."
msgstr "Pesquisar endereço..."

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-google-map.php:211
msgid "Center"
msgstr "Centrar"

#: includes/fields/class-acf-field-google-map.php:201
#: includes/fields/class-acf-field-google-map.php:212
msgid "Center the initial map"
msgstr "Centrar o mapa inicial"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:224
msgid "Set the initial zoom level"
msgstr "Definir o nível de zoom inicial"

#: includes/fields/class-acf-field-google-map.php:233
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:699
#: pro/fields/class-acf-field-gallery.php:728
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:234
msgid "Customize the map height"
msgstr "Personalizar a altura do mapa"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:384
msgid "Sub Fields"
msgstr "Subcampos"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Especifica o estilo usado para mostrar os campos seleccionados."

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:456
#: pro/locations/class-acf-location-block.php:27
msgid "Block"
msgstr "Bloco"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:455
msgid "Table"
msgstr "Tabela"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:614
#: pro/fields/class-acf-field-repeater.php:457
msgid "Row"
msgstr "Linha"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Imagem"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Seleccionar imagem"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Editar imagem"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Actualizar imagem"

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Nenhuma imagem seleccionada"

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Adicionar imagem"

#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr "Array da imagem"

#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr "URL da imagem"

#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr "ID da imagem"

#: includes/fields/class-acf-field-image.php:220
msgid "Preview Size"
msgstr "Tamanho da pré-visualização"

#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr "Mostrado ao inserir dados"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Restrict which images can be uploaded"
msgstr "Restringir que imagens que ser carregadas"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:691
#: pro/fields/class-acf-field-gallery.php:720
msgid "Width"
msgstr "Largura"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Ligação"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Seleccionar ligação"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Abre numa nova janela/separador"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Array da ligação"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL da ligação"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Mensagem"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Novas linhas"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Controla como serão visualizadas novas linhas."

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Adicionar parágrafos automaticamente"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Adicionar &lt;br&gt; automaticamente"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Sem formatação"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Mostrar HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Permite visualizar o código HTML como texto visível, em vez de o processar."

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr "Valor mínimo"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr "Valor máximo"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr "Valor dos passos"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "O valor deve ser um número"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "O valor deve ser igual ou superior a %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "O valor deve ser igual ou inferior a %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Insira o URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Tamanho da incorporação"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Ligação de página"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Arquivo"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr "Superior"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Post Type"
msgstr "Filtrar por tipo de conteúdo"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:568
msgid "All post types"
msgstr "Todos os tipos de conteúdo"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:574
msgid "Filter by Taxonomy"
msgstr "Filtrar por taxonomia"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:582
msgid "All taxonomies"
msgstr "Todas as taxonomias"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Permitir URL do arquivo"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:403
msgid "Select multiple values?"
msgstr "Seleccionar valores múltiplos?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Senha"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:639
msgid "Post Object"
msgstr "Conteúdo"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:640
msgid "Post ID"
msgstr "ID do conteúdo"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Botão de opção"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Outro"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Adicionar opção 'outros' para permitir a inserção de valores personalizados"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Guardar outros"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Guardar 'outros' valores nas opções do campo"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Intervalo"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relação"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "Valor máximo alcançado ( valor {max} )"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "A carregar"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "Seleccione tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "Seleccione taxonomia"

#: includes/fields/class-acf-field-relationship.php:477
msgid "Search..."
msgstr "Pesquisar..."

#: includes/fields/class-acf-field-relationship.php:588
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-relationship.php:594
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:595
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/fields/class-acf-field-relationship.php:602
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:603
msgid "Selected elements will be displayed in each result"
msgstr "Os elementos seleccionados serão mostrados em cada resultado."

#: includes/fields/class-acf-field-relationship.php:614
msgid "Minimum posts"
msgstr "Mínimo de conteúdos"

#: includes/fields/class-acf-field-relationship.php:623
msgid "Maximum posts"
msgstr "Máximo de conteúdos"

#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:818
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s requer pelo menos %s selecção"
msgstr[1] "%s requer pelo menos %s selecções"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr "Selecção"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Um resultado encontrado, prima Enter para seleccioná-lo."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d resultados encontrados, use as setas para cima ou baixo para navegar."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Por favor insira 1 ou mais caracteres"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Por favor insira %d ou mais caracteres"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Por favor elimine 1 caractere"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Por favor elimine %d caracteres"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Só pode seleccionar 1 item"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Só pode seleccionar %d itens"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "A carregar mais resultados&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "A pesquisar&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Falhou ao carregar"

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Interface estilizada"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "Utilizar AJAX para carregar opções?"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "Especifica o valor devolvido."

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Divisória"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Separador"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Posição"

#: includes/fields/class-acf-field-tab.php:115
msgid "Define an endpoint for the previous tabs to stop. This will start a new group of tabs."
msgstr "Define o fim dos separadores anteriores. Isto será o início de um novo grupo de separadores."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Sem %s"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "Seleccione a taxonomia que será mostrada."

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "Apresentação"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "Seleccione a apresentação deste campo."

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "Valores múltiplos"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "Selecção múltipla"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "Valor único"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "Botões de opções"

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "Criar termos"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Permite a criação de novos termos durante a edição."

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "Guardar termos"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "Liga os termos seleccionados ao conteúdo."

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "Carregar termos"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Carrega os termos a partir dos termos dos conteúdos."

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "Termo"

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "ID do termo"

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "O utilizador não pôde adicionar novo(a) %s"

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s já existe"

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "%s adicionado(a)"

#: includes/fields/class-acf-field-taxonomy.php:973
msgid "Add"
msgstr "Adicionar"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limite de caracteres"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Deixe em branco para não limitar"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:213
#, php-format
msgid "Value must not exceed %d characters"
msgstr "O valor não deve exceder %d caracteres"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Área de texto"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Linhas"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Define a altura da área de texto"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Selecção de hora"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Verdadeiro / Falso"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Texto mostrado ao lado da caixa de selecção"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Texto ligado"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Texto mostrado quando activo"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Texto desligado"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Texto mostrado quando inactivo"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "O valor deve ser um URL válido"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Utilizador"

#: includes/fields/class-acf-field-user.php:378
msgid "Filter by role"
msgstr "Filtrar por papel"

#: includes/fields/class-acf-field-user.php:386
msgid "All user roles"
msgstr "Todos os papéis de utilizador"

#: includes/fields/class-acf-field-user.php:417
msgid "User Array"
msgstr "Array do utilizador"

#: includes/fields/class-acf-field-user.php:418
msgid "User Object"
msgstr "Objecto do utilizador"

#: includes/fields/class-acf-field-user.php:419
msgid "User ID"
msgstr "ID do utilizador"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor wysiwyg"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "HTML"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "Clique para inicializar o TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "Separadores"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "Visual e HTML"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "Apenas visual"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "Apenas HTML"

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "Barra de ferramentas"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Mostrar botões de carregar multimédia?"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Atrasar a inicialização?"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "O TinyMCE não será inicializado até que clique no campo"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validar email"

#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:591
#: pro/options-page.php:81
msgid "Update"
msgstr "Actualizar"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Artigo actualizado"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Spam detectado"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Artigo"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Página"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formulários"

#: includes/locations.php:243
msgid "is equal to"
msgstr "é igual a"

#: includes/locations.php:244
msgid "is not equal to"
msgstr "não é igual a"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Anexo"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Todos os formatos de %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Comentário"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Papel do utilizador actual"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super Administrador"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Utilizador actual"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Sessão iniciada"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "A visualizar a frente do site"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "A visualizar a administração do site"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Item de menu"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Localizações do menu"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Página superior"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Modelo de página"

#: includes/locations/class-acf-location-page-template.php:87
#: includes/locations/class-acf-location-post-template.php:134
msgid "Default Template"
msgstr "Modelo por omissão"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Tipo de página"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Página inicial"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Página de artigos"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Página de topo (sem superior)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Página superior (tem dependentes)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Página dependente (tem superior)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Categoria de artigo"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Formato de artigo"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Estado do conteúdo"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taxonomia do artigo"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Modelo de conteúdo"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Formulário de utilizador"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Adicionar / Editar"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Registar"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Papel de utilizador"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "O valor %s é obrigatório"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "Publicado"

#: pro/admin/admin-options-page.php:204
#, php-format
msgid "No Custom Field Groups found for this options page. <a href=\"%s\">Create a Custom Field Group</a>"
msgstr "Nenhum grupo de campos personalizado encontrado na página de opções. <a href=\"%s\">Criar um grupo de campos personalizado</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erro</b>. Não foi possível ligar ao servidor de actualização"

#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Actualizações"

#: pro/admin/admin-updates.php:191
msgid "<b>Error</b>. Could not authenticate update package. Please check again or deactivate and reactivate your ACF PRO license."
msgstr "<b>Erro</b>. Não foi possível autenticar o pacote de actualização. Por favor verifique de novo, ou desactive e reactive a sua licença do ACF PRO."

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Desactivar licença"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Activar licença"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informações da licença"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid "To unlock updates, please enter your license key below. If you don't have a licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</a>."
msgstr "Para desbloquear as actualizações, por favor insira a sua chave de licença. Se não tiver uma chave de licença, por favor consulte os <a href=\"%s\" target=\"_blank\">detalhes e preços</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Chave de licença"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informações de actualização"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versão actual"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Última versão"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Actualização disponível"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Actualizar plugin"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Por favor, insira acima a sua chave de licença para desbloquear as actualizações"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Verificar de novo"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Informações sobre a actualização"

#: pro/blocks.php:371
msgid "Switch to Edit"
msgstr "Mudar para o editor"

#: pro/blocks.php:372
msgid "Switch to Preview"
msgstr "Mudar para pré-visualização"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Seleccione um ou mais campos que deseje clonar."

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Visualização"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Especifica o estilo usado para mostrar o campo de clone."

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupo (mostra os campos seleccionados num grupo dentro deste campo)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Simples (substitui este campo pelos campos seleccionados)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "As legendas serão mostradas com %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Prefixo nas legendas dos campos"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Os valores serão guardados como %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Prefixos nos nomes dos campos"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Campo desconhecido"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Grupo de campos desconhecido"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Todos os campos do grupo de campos %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:468
msgid "Add Row"
msgstr "Adicionar linha"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:924
#: pro/fields/class-acf-field-flexible-content.php:1006
msgid "layout"
msgid_plural "layouts"
msgstr[0] "layout"
msgstr[1] "layouts"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "layouts"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:923
#: pro/fields/class-acf-field-flexible-content.php:1005
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Este campo requer pelo menos {min} {identifier} {label}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Este campo está limitado a {max} {identifier} {label}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {identifier} {label} disponível (máx {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {identifier} {label} em falta (mín {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "O conteúdo flexível requer pelo menos 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:287
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Clique no botão \"%s\" abaixo para começar a criar o seu layout"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Adicionar layout"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Remove layout"
msgstr "Remover layout"

#: pro/fields/class-acf-field-flexible-content.php:415
#: pro/fields/class-acf-field-repeater.php:301
msgid "Click to toggle"
msgstr "Clique para alternar"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder Layout"
msgstr "Reordenar layout"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder"
msgstr "Reordenar"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete Layout"
msgstr "Eliminar layout"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate Layout"
msgstr "Duplicar layout"

#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New Layout"
msgstr "Adicionar novo layout"

#: pro/fields/class-acf-field-flexible-content.php:629
msgid "Min"
msgstr "Mín"

#: pro/fields/class-acf-field-flexible-content.php:642
msgid "Max"
msgstr "Máx"

#: pro/fields/class-acf-field-flexible-content.php:669
#: pro/fields/class-acf-field-repeater.php:464
msgid "Button Label"
msgstr "Legenda do botão"

#: pro/fields/class-acf-field-flexible-content.php:678
msgid "Minimum Layouts"
msgstr "Mínimo de layouts"

#: pro/fields/class-acf-field-flexible-content.php:687
msgid "Maximum Layouts"
msgstr "Máximo de layouts"

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr "Adicionar imagem à galeria"

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr "Máximo de selecção alcançado"

#: pro/fields/class-acf-field-gallery.php:338
msgid "Length"
msgstr "Comprimento"

#: pro/fields/class-acf-field-gallery.php:381
msgid "Caption"
msgstr "Legenda"

#: pro/fields/class-acf-field-gallery.php:390
msgid "Alt Text"
msgstr "Texto alternativo"

#: pro/fields/class-acf-field-gallery.php:562
msgid "Add to gallery"
msgstr "Adicionar à galeria"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Bulk actions"
msgstr "Acções por lotes"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Sort by date uploaded"
msgstr "Ordenar por data de carregamento"

#: pro/fields/class-acf-field-gallery.php:568
msgid "Sort by date modified"
msgstr "Ordenar por data de modificação"

#: pro/fields/class-acf-field-gallery.php:569
msgid "Sort by title"
msgstr "Ordenar por título"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Reverse current order"
msgstr "Inverter ordem actual"

#: pro/fields/class-acf-field-gallery.php:588
msgid "Close"
msgstr "Fechar"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Minimum Selection"
msgstr "Selecção mínima"

#: pro/fields/class-acf-field-gallery.php:651
msgid "Maximum Selection"
msgstr "Selecção máxima"

#: pro/fields/class-acf-field-gallery.php:660
msgid "Insert"
msgstr "Inserir"

#: pro/fields/class-acf-field-gallery.php:661
msgid "Specify where new attachments are added"
msgstr "Especifica onde serão adicionados os novos anexos."

#: pro/fields/class-acf-field-gallery.php:665
msgid "Append to the end"
msgstr "No fim"

#: pro/fields/class-acf-field-gallery.php:666
msgid "Prepend to the beginning"
msgstr "No início"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:661
msgid "Minimum rows reached ({min} rows)"
msgstr "Mínimo de linhas alcançado ({min} linhas)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Máximo de linhas alcançado ({max} linhas)"

#: pro/fields/class-acf-field-repeater.php:338
msgid "Add row"
msgstr "Adicionar linha"

#: pro/fields/class-acf-field-repeater.php:339
msgid "Remove row"
msgstr "Remover linha"

#: pro/fields/class-acf-field-repeater.php:417
msgid "Collapsed"
msgstr "Minimizado"

#: pro/fields/class-acf-field-repeater.php:418
msgid "Select a sub field to show when row is collapsed"
msgstr "Seleccione o subcampo a mostrar ao minimizar a linha."

#: pro/fields/class-acf-field-repeater.php:428
msgid "Minimum Rows"
msgstr "Mínimo de linhas"

#: pro/fields/class-acf-field-repeater.php:438
msgid "Maximum Rows"
msgstr "Máximo de linhas"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Não existem páginas de opções"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opções"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Opções actualizadas"

#: pro/updates.php:97
#, php-format
msgid "To enable updates, please enter your license key on the <a href=\"%s\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s\">details & pricing</a>."
msgstr "Para permitir actualizações, por favor insira a sua chave de licença na página de <a href=\"%s\">Actualizações</a>. Se não tiver uma chave de licença, por favor veja os <a href=\"%s\">detalhes e preços</a>."

#: tests/basic/test-blocks.php:13
msgid "Testimonial"
msgstr "Testemunho"

#: tests/basic/test-blocks.php:14
msgid "A custom testimonial block."
msgstr "Um bloco personalizado de testemunho."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "<b>Error</b>. Could not connect to update server %s."
#~ msgstr "<b>Erro</b>. Não foi possível ligar ao servidor de actualização %s."
