msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro\n"
"POT-Creation-Date: 2017-06-27 15:37+1000\n"
"PO-Revision-Date: 2019-02-11 10:45+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:63
msgid "Advanced Custom Fields"
msgstr "الحقول المخصصة المتقدمة"

#: acf.php:355 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "مجموعات الحقول"

#: acf.php:356
msgid "Field Group"
msgstr "مجموعة الحقول"

#: acf.php:357 acf.php:389 includes/admin/admin.php:118 pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New"
msgstr "إضافة جديد"

#: acf.php:358
msgid "Add New Field Group"
msgstr "إضافة مجموعة حقول جديدة"

#: acf.php:359
msgid "Edit Field Group"
msgstr "تحرير مجموعة الحقول"

#: acf.php:360
msgid "New Field Group"
msgstr "مجموعة حقول جديدة"

#: acf.php:361
msgid "View Field Group"
msgstr "عرض مجموعة الحقول"

#: acf.php:362
msgid "Search Field Groups"
msgstr "بحث في مجموعات الحقول"

#: acf.php:363
msgid "No Field Groups found"
msgstr "لم يتم العثور على نتائج"

#: acf.php:364
msgid "No Field Groups found in Trash"
msgstr "لا توجد مجموعات حقول في سلة المهملات"

#: acf.php:387 includes/admin/admin-field-group.php:182 includes/admin/admin-field-group.php:275 includes/admin/admin-field-groups.php:510 pro/fields/class-acf-field-clone.php:857
msgid "Fields"
msgstr "حقول"

#: acf.php:388
msgid "Field"
msgstr "حقل"

#: acf.php:390
msgid "Add New Field"
msgstr "إضافة حقل جديد"

#: acf.php:391
msgid "Edit Field"
msgstr "تحرير الحقل"

#: acf.php:392 includes/admin/views/field-group-fields.php:41 includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "حقل جديد"

#: acf.php:393
msgid "View Field"
msgstr "عرض الحقل"

#: acf.php:394
msgid "Search Fields"
msgstr "بحث في الحقول"

#: acf.php:395
msgid "No Fields found"
msgstr "لم يتم العثور على أية حقول"

#: acf.php:396
msgid "No Fields found in Trash"
msgstr "لم يتم العثور على أية حقول في سلة المهملات"

#: acf.php:435 includes/admin/admin-field-group.php:390 includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "غير نشط"

#: acf.php:440
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[1] "غير نشط <span class=\"count\">(%s)</span>"
msgstr[2] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[3] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[4] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[5] "غير نشطة <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68 includes/admin/admin-field-group.php:69 includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "تم تحديث مجموعة الحقول"

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "تم حذف مجموعة الحقول."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "تم نشر مجموعة الحقول."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "تم حفظ مجموعة الحقول."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "تم تقديم مجموعة الحقول."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "تم جدولة مجموعة الحقول لـ."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "تم تحديث مسودة مجموعة الحقول."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "الموقع"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "الإعدادات"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "ارسال إلى سلة المهملات. هل أنت متأكد؟"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "مفحوص"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "تبديل الحقول غير متوفر"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "عنوان مجموعة الحقول مطلوب"

#: includes/admin/admin-field-group.php:273 includes/api/api-field-group.php:732
msgid "copy"
msgstr "انسخ"

#: includes/admin/admin-field-group.php:274 includes/admin/views/field-group-field-conditional-logic.php:54 includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29 includes/admin/views/html-location-group.php:3 includes/api/api-helpers.php:3970
msgid "or"
msgstr "او"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "الحقول الأصلية"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "الحقول الفرعية"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "نقل الحقل المخصص"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "لا يمكن نقل هذا الحقل حتى يتم حفظ تغييراته"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "لا شيء"

#: includes/admin/admin-field-group.php:281 includes/input.php:257
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "سيتم فقدان التغييرات التي أجريتها إذا غادرت هذه الصفحة"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "لا يجوز استخدام المقطع \"field_\" في بداية اسم الحقل"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "مفاتيح الحقل"

#: includes/admin/admin-field-group.php:390 includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "نشط"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "تم النقل."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "الحقل %s يمكن الآن إيجاده في مجموعة الحقول %s"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "إغلاق النافذة"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "الرجاء تحديد الوجهة لهذا الحقل"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "نقل الحقل"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "نشط <span class=\"count\">(%s)</span>"
msgstr[1] "نشط <span class=\"count\">(%s)</span>"
msgstr[2] "نشطة <span class=\"count\">(%s)</span>"
msgstr[3] "نشطة <span class=\"count\">(%s)</span>"
msgstr[4] "نشطة <span class=\"count\">(%s)</span>"
msgstr[5] "نشطة <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "تم تكرار مجموعة الحقول %s."

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "تم تكرار مجموعة الحقول. %s"
msgstr[1] "تم تكرار مجموعة الحقول. %s"
msgstr[2] "تم تكرار مجموعة الحقول. %s"
msgstr[3] "تم تكرار مجموعة الحقول. %s"
msgstr[4] "تم تكرار مجموعة الحقول. %s"
msgstr[5] "تم تكرار مجموعة الحقول. %s"

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "تمت مزامنة مجموعة الحقول %s."

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "تمت مزامنة مجموعة الحقول. %s"
msgstr[1] "تمت مزامنة مجموعة الحقول. %s"
msgstr[2] "تمت مزامنة مجموعة الحقول. %s"
msgstr[3] "تمت مزامنة مجموعة الحقول. %s"
msgstr[4] "تمت مزامنة مجموعة الحقول. %s"
msgstr[5] "تمت مزامنة مجموعة الحقول. %s"

#: includes/admin/admin-field-groups.php:394 includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "المزامنة متوفرة"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38 pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "العنوان"

#: includes/admin/admin-field-groups.php:508 includes/admin/views/field-group-options.php:96 includes/admin/views/install-network.php:21 includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "الوصف"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "الحالة"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "خصص ووردبرس بحقول قوية، مهنية، وبديهية‪.‬"

#: includes/admin/admin-field-groups.php:609 includes/admin/settings-info.php:76 pro/admin/views/html-settings-updates.php:111
msgid "Changelog"
msgstr "سجل التغييرات"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "اطلع على الجديد في <a href=\"%s\">النسخة %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "الموارد"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "الموقع الإليكتروني"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "التوثيق"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "الدعم"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "احترافي"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "شكرا لك لاستخدامك <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "تكرار هذا العنصر"

#: includes/admin/admin-field-groups.php:668 includes/admin/admin-field-groups.php:684 includes/admin/views/field-group-field.php:49 pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate"
msgstr "تكرار"

#: includes/admin/admin-field-groups.php:701 includes/fields/class-acf-field-google-map.php:132 includes/fields/class-acf-field-relationship.php:737
msgid "Search"
msgstr "بحث"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "اختيار %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "مزامنة مجموعة الحقول"

#: includes/admin/admin-field-groups.php:768 includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "مزامنة"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr "تطبيق"

#: includes/admin/admin-field-groups.php:798
msgid "Bulk Actions"
msgstr "اجراءات جماعية"

#: includes/admin/admin.php:113 includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "الحقول المخصصة"

#: includes/admin/install-network.php:88 includes/admin/install.php:70 includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "ترقية قاعدة البيانات"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "استعراض المواقع والترقية"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "حدث خطأ أثناء التحقق من صحة الطلب"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "لا توجد تحديثات متوفرة."

#: includes/admin/settings-addons.php:51 includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "الإضافات"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>خطأ.</b> لا يمكن تحميل قائمة الإضافات"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "معلومات"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "ما الجديد"

#: includes/admin/settings-tools.php:50 includes/admin/views/settings-tools-export.php:19 includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "أدوات"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "لم يتم تحديد مجموعات الحقول"

#: includes/admin/settings-tools.php:184 includes/fields/class-acf-field-file.php:174
msgid "No file selected"
msgstr "لم يتم إختيار ملف"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "خطأ في تحميل الملف . حاول مرة أخرى"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "نوع الملف غير صحيح"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "الملف المستورد فارغ"

#: includes/admin/settings-tools.php:331
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "تم استيراد مجموعة حقول واحدة"
msgstr[1] "تم استيراد مجموعة حقول واحدة"
msgstr[2] "تم استيراد مجموعتي حقول"
msgstr[3] "تم استيراد %s مجموعات حقول"
msgstr[4] "تم استيراد %s مجموعات حقول"
msgstr[5] "تم استيراد %s مجموعات حقول"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "المنطق الشرطي"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "إظهار هذا الحقل إذا"

#: includes/admin/views/field-group-field-conditional-logic.php:103 includes/locations.php:243
msgid "is equal to"
msgstr "يساوي"

#: includes/admin/views/field-group-field-conditional-logic.php:104 includes/locations.php:244
msgid "is not equal to"
msgstr "لا يساوي"

#: includes/admin/views/field-group-field-conditional-logic.php:141 includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "و"

#: includes/admin/views/field-group-field-conditional-logic.php:156 includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "إضافة مجموعة قاعدة"

#: includes/admin/views/field-group-field.php:41 pro/fields/class-acf-field-flexible-content.php:420 pro/fields/class-acf-field-repeater.php:358
msgid "Drag to reorder"
msgstr "اسحب لإعادة الترتيب"

#: includes/admin/views/field-group-field.php:45 includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "تحرير الحقل"

#: includes/admin/views/field-group-field.php:48 includes/fields/class-acf-field-image.php:140 includes/fields/class-acf-field-link.php:152 pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "تحرير"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "تكرار الحقل"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "نقل الحقل إلى مجموعة أخرى"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "نقل"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "حذف الحقل"

#: includes/admin/views/field-group-field.php:51 pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete"
msgstr "حذف"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "تسمية الحقل"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "هذا هو الاسم الذي سيظهر في صفحة التحرير"

#: includes/admin/views/field-group-field.php:78
msgid "Field Name"
msgstr "اسم الحقل"

#: includes/admin/views/field-group-field.php:79
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "كلمة واحدة، بدون مسافات. مسموح بالشرطات والشرطات السفلية"

#: includes/admin/views/field-group-field.php:89
msgid "Field Type"
msgstr "نوع الحقل"

#: includes/admin/views/field-group-field.php:101 includes/fields/class-acf-field-tab.php:102
msgid "Instructions"
msgstr "التعليمات"

#: includes/admin/views/field-group-field.php:102
msgid "Instructions for authors. Shown when submitting data"
msgstr "تعليمات للكتاب. سيظهر عند إرسال البيانات"

#: includes/admin/views/field-group-field.php:111
msgid "Required?"
msgstr "مطلوب؟"

#: includes/admin/views/field-group-field.php:134
msgid "Wrapper Attributes"
msgstr "سمات المجمع"

#: includes/admin/views/field-group-field.php:140
msgid "width"
msgstr "العرض"

#: includes/admin/views/field-group-field.php:155
msgid "class"
msgstr "class (الفئة)"

#: includes/admin/views/field-group-field.php:168
msgid "id"
msgstr "id (المعرف)"

#: includes/admin/views/field-group-field.php:180
msgid "Close Field"
msgstr "أغلق الحقل"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "ترتيب"

#: includes/admin/views/field-group-fields.php:5 includes/fields/class-acf-field-checkbox.php:317 includes/fields/class-acf-field-radio.php:321 includes/fields/class-acf-field-select.php:530
#: pro/fields/class-acf-field-flexible-content.php:599
msgid "Label"
msgstr "تسمية"

#: includes/admin/views/field-group-fields.php:6 includes/fields/class-acf-field-taxonomy.php:970 pro/fields/class-acf-field-flexible-content.php:612
msgid "Name"
msgstr "الاسم"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "المفتاح"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "النوع"

#: includes/admin/views/field-group-fields.php:14
msgid "No fields. Click the <strong>+ Add Field</strong> button to create your first field."
msgstr "لا توجد حقول. انقر على زر <strong>+ إضافة حقل</strong> لإنشاء أول حقل."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ اضف حقل"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "القواعد"

#: includes/admin/views/field-group-locations.php:10
msgid "Create a set of rules to determine which edit screens will use these advanced custom fields"
msgstr "إنشىء مجموعة من القواعد لتحديد أي شاشات التحرير ستستخدم هذه الحقول المخصصة"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "نمط"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "قياسي (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "سلس (بدون metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "الموضع"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "عالي (بعد العنوان)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "عادي (بعد المحتوى)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "الجانب"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "تعيين مكان التسمية"

#: includes/admin/views/field-group-options.php:62 includes/fields/class-acf-field-tab.php:116
msgid "Top aligned"
msgstr "محاذاة إلى الأعلى"

#: includes/admin/views/field-group-options.php:63 includes/fields/class-acf-field-tab.php:117
msgid "Left aligned"
msgstr "محاذاة لليسار"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "تعيين مكان التعليمات"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "أسفل التسميات"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "بعد الحقول"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "رقم الترتيب"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "مجموعات الحقول ذات الترتيب الأدنى ستظهر أولا"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "اظهار في قائمة مجموعة الحقول"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "إخفاء على الشاشة"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>تحديد</b> العناصر <b>لإخفائها</b> من شاشة التحرير."

#: includes/admin/views/field-group-options.php:108
msgid "If multiple field groups appear on an edit screen, the first field group's options will be used (the one with the lowest order number)"
msgstr "إذا ظهرت مجموعات حقول متعددة في شاشة التحرير. سيتم استخدام خيارات المجموعة الأولى (تلك التي تحتوي على أقل رقم ترتيب)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "الرابط الدائم"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "محرر المحتوى"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "مختصر الموضوع"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "النقاش"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "التعليقات"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "المراجعة"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "الاسم اللطيف"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "الكاتب"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "الشكل"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "سمات الصفحة"

#: includes/admin/views/field-group-options.php:126 includes/fields/class-acf-field-relationship.php:751
msgid "Featured Image"
msgstr "صورة بارزة"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "التصنيفات"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "الوسوم"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "إرسال Trackbacks"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "إظهار هذه المجموعة إذا"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "ترقية المواقع"

#: includes/admin/views/install-network.php:9 includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr " ترقية قاعدة بيانات الحقول المخصصة المتقدمة"

#: includes/admin/views/install-network.php:11
#, php-format
msgid "The following sites require a DB upgrade. Check the ones you want to update and then click %s."
msgstr "تتطلب المواقع التالية ترقية قاعدة البيانات. تحقق من تلك التي تحتاج إلى ترقيتها ومن ثم انقر على %s."

#: includes/admin/views/install-network.php:20 includes/admin/views/install-network.php:28
msgid "Site"
msgstr "الموقع"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "يتطلب الموقع ترقية قاعدة البيانات من %s إلى %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "الموقع محدث"

#: includes/admin/views/install-network.php:63
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr "تمت ترقية قاعدة البيانات. <a href=\"%s\">العودة إلى لوحة معلومات الشبكة</a>"

#: includes/admin/views/install-network.php:102 includes/admin/views/install-notice.php:42
msgid "It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?"
msgstr "يوصى بشدة أن تقوم بأخذ نسخة احتياطية من قاعدة البيانات قبل المتابعة. هل أنت متأكد أنك ترغب في تشغيل التحديث الآن؟"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "اكتملت عملية الترقية"

#: includes/admin/views/install-network.php:162 includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "ترقية البيانات إلى الإصدار %s"

#: includes/admin/views/install-notice.php:8 pro/fields/class-acf-field-repeater.php:36
msgid "Repeater"
msgstr "المكرر"

#: includes/admin/views/install-notice.php:9 pro/fields/class-acf-field-flexible-content.php:36
msgid "Flexible Content"
msgstr "المحتوى المرن"

#: includes/admin/views/install-notice.php:10 pro/fields/class-acf-field-gallery.php:36
msgid "Gallery"
msgstr "الالبوم"

#: includes/admin/views/install-notice.php:11 pro/locations/class-acf-location-options-page.php:13
msgid "Options Page"
msgstr "خيارات الصفحة"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "ترقية قاعدة البيانات مطلوبة"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "شكرا لك على تحديث %s إلى الإصدار %s!"

#: includes/admin/views/install-notice.php:28
msgid "Before you start using the new awesome features, please update your database to the newest version."
msgstr "قبل البدء باستخدام الميزات الجديدة، الرجاء تحديث قاعدة البيانات الخاصة بك إلى الإصدار الأحدث."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid "Please also ensure any premium add-ons (%s) have first been updated to the latest version."
msgstr "يرجى أيضا التأكد من تحديث أي إضافات مدفوعة (%s) أولا إلى أحدث إصدار."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "قراءة مهام الترقية..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "تمت ترقية قاعدة البيانات. <a href=\"%s\">اطلع على الجديد</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "تحميل وتثبيت"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "تم التثبيت"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "مرحبا بك في الحقول المخصصة المتقدمة"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid "Thank you for updating! ACF %s is bigger and better than ever before. We hope you like it."
msgstr "شكرا لك للتحديث! ACF %s أكبر وأفضل من أي وقت مضى."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "حقول مخصصة أكثر سلاسة"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "تحسين قابلية الاستخدام"

#: includes/admin/views/settings-info.php:23
msgid "Including the popular Select2 library has improved both usability and speed across a number of field types including post object, page link, taxonomy and select."
msgstr "دمج مكتبة Select2 حسن قابلية الاستخدام والسرعة عبر عدد من أنواع الحقول بما في ذلك موضوع المنشور، رابط الصفحة، التصنيف والتحديد"

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "تصميم محسّن"

#: includes/admin/views/settings-info.php:28
msgid "Many fields have undergone a visual refresh to make ACF look better than ever! Noticeable changes are seen on the gallery, relationship and oEmbed (new) fields!"
msgstr "شهدت العديد من الحقول تحديث مرئي جعل ACF تبدو أفضل من أي وقت مضى! تلاحظ التغييرات في المعرض، العلاقة وحقول oEmbed (جديد)!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "بيانات محسّنة"

#: includes/admin/views/settings-info.php:33
msgid "Redesigning the data architecture has allowed sub fields to live independently from their parents. This allows you to drag and drop fields in and out of parent fields!"
msgstr "إعادة تصميم هيكل البيانات سمحت للحقول الفرعية للعمل بشكل مستقل عن الحقول الأصلية. هذا يسمح لك بسحب وافلات الحقول داخل وخارج الحقول الأصلية!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "وداعا للوظائف الإضافية. مرحبا برو"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "نقدم ACF برو"

#: includes/admin/views/settings-info.php:45
msgid "We're changing the way premium functionality is delivered in an exciting way!"
msgstr "نحن نغير الطريقة التي يتم بها تقديم الأداء المتميز بطريقة مثيرة!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro version of ACF</a>. With both personal and developer licenses available, premium functionality is more affordable and "
"accessible than ever before!"
msgstr ""
"تم دمج الإضافات المدفوعة الأربعة في <a href=\"%s\">النسخة الإحترافية من ACF</a>. مع توفير رخص شخصية واخرى للمطورين، لتصبح الوظائف المميزة بأسعار معقولة ويمكن الوصول إليها أكثر من أي وقت مضى!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "ميزات قوية"

#: includes/admin/views/settings-info.php:51
msgid "ACF PRO contains powerful features such as repeatable data, flexible content layouts, a beautiful gallery field and the ability to create extra admin options pages!"
msgstr "يحتوي ACF PRO على ميزات قوية مثل البيانات القابلة للتكرار، والمحتوى المرن، وحقل المعرض الجميل والقدرة على إنشاء صفحات خيارات إضافية للمشرفين!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "اقرأ المزيد حول <a href=\"%s\">ميزات ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "ترقية سهلة"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid "To help make upgrading easy, <a href=\"%s\">login to your store account</a> and claim a free copy of ACF PRO!"
msgstr "للمساعدة في جعل الترقية سهلة، <a href=\"%s\">سجل الدخول إلى حسابك في المتجر</a> واحصل على نسخة مجانية من ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, but if you do have one, please contact our support team via the <a href=\"%s\">help desk</a>"
msgstr "نحن كتبنا أيضا <a href=\"%s\">دليل للتحديث</a> للرد على أية أسئلة، ولكن إذا كان إذا كان لديك اي سؤال، الرجاء الاتصال بفريق الدعم الخاص بنا عن طريق <a href=\"%s\">مكتب المساعدة</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "تحت الغطاء"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "إعدادات حقول أكثر ذكاء"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF الآن يحفظ إعدادات الحقول كـ post object منفصل"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "اجاكس أكثر"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr "حقول اكثر تستخدم بحث أجاكس لتسريع تحميل الصفحة"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "JSON محلي"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "تصدير اتوماتيكي الى JSON يحسن السرعة"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "تحكم أفضل في الإصدارات"

#: includes/admin/views/settings-info.php:89
msgid "New auto export to JSON feature allows field settings to be version controlled"
msgstr "يسمح التصدير الاتوماتيكي الجديدة إلى JSON لإعدادات الحقول بأن تكون قابلة لتحكم الإصدارات"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "استبدال XML بـ JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "الاستيراد والتصدير الآن يستخدم JSON عوضا عن XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "أشكال جديدة"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr "يمكن الآن تعيين الحقول إلى التعليقات، الودجات وجميع نماذج المستخدم!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "تم إضافة حقل جديد لتضمين المحتوى"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "معرض صور جديد"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "شهد حقل المعرض عملية تغيير جذرية"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "إعدادات جديدة"

#: includes/admin/views/settings-info.php:116
msgid "Field group settings have been added for label placement and instruction placement"
msgstr "تمت إضافة إعدادات لموضع التسمية والتعليمات بمجموعة الحقول "

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "نماذج افضل"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() يمكن الآن إنشاء مشاركة جديدة عند الإرسال"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "تحقق افضل"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "يتم الآن التحقق من صحة النموذج عن طريق PHP + AJAX بدلا من جافا سكريبت فقط"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "حقل العلاقة"

#: includes/admin/views/settings-info.php:133
msgid "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr "إعداد جديد لحقل العلاقة خاص بالفلاتر (البحث، نوع المقالة، التصنيف)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "نقل الحقول"

#: includes/admin/views/settings-info.php:140
msgid "New field group functionality allows you to move a field between groups & parents"
msgstr "يمكن الان نقل الحقل بين المجموعات و المجموعات الأصلية"

#: includes/admin/views/settings-info.php:144 includes/fields/class-acf-field-page_link.php:36
msgid "Page Link"
msgstr "رابط الصفحة"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "مجموعة المحفوظات الجديدة في تحديد الحقل page_link"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "صفحات خيارات أفضل"

#: includes/admin/views/settings-info.php:150
msgid "New functions for options page allow creation of both parent and child menu pages"
msgstr "مهام جديدة لصفحة الخيارات تسمح بإنشاء كل من صفحات القائمة الأصلية والفرعية"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "نعتقد أنك ستحب هذه التغييرات في %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "تصدير مجموعات الحقول لـ PHP"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected field group(s). A local field group can provide many benefits such as faster load times, version control & dynamic "
"fields/settings. Simply copy and paste the following code to your theme's functions.php file or include it within an external file."
msgstr ""
"يمكن استخدام الكود التالي لتسجيل نسخة محلية من مجموعة الحقول المحددة. مجموعة الحقول المحلية يمكن أن توفر العديد من المزايا مثل التحميل بشكل أسرع، والتحكم في الإصدار والإعدادات والحقول "
"الديناميكية. ببساطة أنسخ وألصق الكود التالي إلى ملف functions.php بالقالب الخاص بك أو إدراجه ضمن ملف خارجي."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "حدد مجموعات الحقول"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "تصدير مجموعات الحقول"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export method. Use the download button to export to a .json file which you can then import to another ACF installation. "
"Use the generate button to export to PHP code which you can place in your theme."
msgstr ""
"حدد مجموعات الحقول التي ترغب في تصديرها ومن ثم حدد طريقة التصدير. استخدام زر التحميل للتصدير إلى ملف .json الذي يمكنك من ثم استيراده إلى تثبيت ACF آخر. استخدم زر التوليد للتصدير بصيغة PHP "
"الذي يمكنك ادراجه في القالب الخاص بك."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "تنزيل ملف التصدير"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "توليد كود التصدير"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "استيراد مجموعات الحقول"

#: includes/admin/views/settings-tools.php:67
msgid "Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the field groups."
msgstr "حدد ملف JSON الذي ترغب في استيراده. عند النقر على زر استيراد أدناه، ACF ستقوم باستيراد مجموعات الحقول."

#: includes/admin/views/settings-tools.php:77 includes/fields/class-acf-field-file.php:46
msgid "Select File"
msgstr "إختر ملف"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "استيراد"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "الصورة المصغرة"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "متوسط"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "كبير"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "العرض الكامل"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1837 pro/fields/class-acf-field-clone.php:1042
msgid "(no title)"
msgstr "(بدون عنوان)"

#: includes/api/api-helpers.php:1874 includes/fields/class-acf-field-page_link.php:284 includes/fields/class-acf-field-post_object.php:283 includes/fields/class-acf-field-taxonomy.php:992
msgid "Parent"
msgstr "الأب"

#: includes/api/api-helpers.php:3891
#, php-format
msgid "Image width must be at least %dpx."
msgstr "يجب أن يكون عرض الصورة على الأقل %dpx."

#: includes/api/api-helpers.php:3896
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "يجب إلا يتجاوز عرض الصورة %dpx."

#: includes/api/api-helpers.php:3912
#, php-format
msgid "Image height must be at least %dpx."
msgstr "يجب أن يكون ارتفاع الصورة على الأقل %dpx."

#: includes/api/api-helpers.php:3917
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "يجب إلا يتجاوز ارتفاع الصورة %dpx."

#: includes/api/api-helpers.php:3935
#, php-format
msgid "File size must be at least %s."
msgstr "يجب إلا يقل حجم الملف عن %s."

#: includes/api/api-helpers.php:3940
#, php-format
msgid "File size must must not exceed %s."
msgstr "حجم الملف يجب يجب أن لا يتجاوز %s."

#: includes/api/api-helpers.php:3974
#, php-format
msgid "File type must be %s."
msgstr "يجب أن يكون نوع الملف %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "أساسية"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "المحتوى"

#: includes/fields.php:146
msgid "Choice"
msgstr "خيار"

#: includes/fields.php:147
msgid "Relational"
msgstr "ذو علاقة"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149 includes/fields/class-acf-field-checkbox.php:286 includes/fields/class-acf-field-group.php:485 includes/fields/class-acf-field-radio.php:300
#: pro/fields/class-acf-field-clone.php:889 pro/fields/class-acf-field-flexible-content.php:569 pro/fields/class-acf-field-flexible-content.php:618 pro/fields/class-acf-field-repeater.php:514
msgid "Layout"
msgstr "المخطط"

#: includes/fields.php:305
msgid "Field type does not exist"
msgstr "نوع الحقل غير موجود"

#: includes/fields.php:305
msgid "Unknown"
msgstr "غير معروف"

#: includes/fields/class-acf-field-checkbox.php:36 includes/fields/class-acf-field-taxonomy.php:786
msgid "Checkbox"
msgstr "مربع اختيار"

#: includes/fields/class-acf-field-checkbox.php:150
msgid "Toggle All"
msgstr "تبديل الكل"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "إضافة اختيار جديد"

#: includes/fields/class-acf-field-checkbox.php:246 includes/fields/class-acf-field-radio.php:250 includes/fields/class-acf-field-select.php:466
msgid "Choices"
msgstr "خيارات"

#: includes/fields/class-acf-field-checkbox.php:247 includes/fields/class-acf-field-radio.php:251 includes/fields/class-acf-field-select.php:467
msgid "Enter each choice on a new line."
msgstr "أدخل كل خيار في سطر جديد."

#: includes/fields/class-acf-field-checkbox.php:247 includes/fields/class-acf-field-radio.php:251 includes/fields/class-acf-field-select.php:467
msgid "For more control, you may specify both a value and label like this:"
msgstr "لمزيد من التحكم، يمكنك تحديد كل من القيمة والتسمية كما يلي:"

#: includes/fields/class-acf-field-checkbox.php:247 includes/fields/class-acf-field-radio.php:251 includes/fields/class-acf-field-select.php:467
msgid "red : Red"
msgstr "أحمر : أحمر"

#: includes/fields/class-acf-field-checkbox.php:255
msgid "Allow Custom"
msgstr "اسمح بالتخصيص"

#: includes/fields/class-acf-field-checkbox.php:260
msgid "Allow 'custom' values to be added"
msgstr "السماح بإضافة قيم \"مخصصة\""

#: includes/fields/class-acf-field-checkbox.php:266
msgid "Save Custom"
msgstr "حفظ المخصص"

#: includes/fields/class-acf-field-checkbox.php:271
msgid "Save 'custom' values to the field's choices"
msgstr "حفظ القيم \"المخصصة\" لخيارات الحقل"

#: includes/fields/class-acf-field-checkbox.php:277 includes/fields/class-acf-field-color_picker.php:146 includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:145 includes/fields/class-acf-field-radio.php:291 includes/fields/class-acf-field-select.php:475 includes/fields/class-acf-field-text.php:142
#: includes/fields/class-acf-field-textarea.php:139 includes/fields/class-acf-field-true_false.php:150 includes/fields/class-acf-field-url.php:114
#: includes/fields/class-acf-field-wysiwyg.php:436
msgid "Default Value"
msgstr "قيمة إفتراضية"

#: includes/fields/class-acf-field-checkbox.php:278 includes/fields/class-acf-field-select.php:476
msgid "Enter each default value on a new line"
msgstr "قم بإدخال كل قيمة افتراضية في سطر جديد"

#: includes/fields/class-acf-field-checkbox.php:292 includes/fields/class-acf-field-radio.php:306
msgid "Vertical"
msgstr "عمودي"

#: includes/fields/class-acf-field-checkbox.php:293 includes/fields/class-acf-field-radio.php:307
msgid "Horizontal"
msgstr "أفقي"

#: includes/fields/class-acf-field-checkbox.php:300
msgid "Toggle"
msgstr "تبديل"

#: includes/fields/class-acf-field-checkbox.php:301
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "أضف مربع اختيار إضافي في البداية لتبديل جميع الخيارات"

#: includes/fields/class-acf-field-checkbox.php:310 includes/fields/class-acf-field-file.php:219 includes/fields/class-acf-field-image.php:206 includes/fields/class-acf-field-link.php:180
#: includes/fields/class-acf-field-radio.php:314 includes/fields/class-acf-field-taxonomy.php:839
msgid "Return Value"
msgstr "القيمة المرجعة"

#: includes/fields/class-acf-field-checkbox.php:311 includes/fields/class-acf-field-file.php:220 includes/fields/class-acf-field-image.php:207 includes/fields/class-acf-field-link.php:181
#: includes/fields/class-acf-field-radio.php:315
msgid "Specify the returned value on front end"
msgstr "حدد القيمة التي سيتم إرجاعها في الواجهة الأمامية"

#: includes/fields/class-acf-field-checkbox.php:316 includes/fields/class-acf-field-radio.php:320 includes/fields/class-acf-field-select.php:529
msgid "Value"
msgstr "قيمة"

#: includes/fields/class-acf-field-checkbox.php:318 includes/fields/class-acf-field-radio.php:322 includes/fields/class-acf-field-select.php:531
msgid "Both (Array)"
msgstr "كلاهما (Array)"

#: includes/fields/class-acf-field-color_picker.php:36
msgid "Color Picker"
msgstr "محدد اللون"

#: includes/fields/class-acf-field-color_picker.php:83
msgid "Clear"
msgstr "مسح"

#: includes/fields/class-acf-field-color_picker.php:84
msgid "Default"
msgstr "الافتراضي"

#: includes/fields/class-acf-field-color_picker.php:85
msgid "Select Color"
msgstr "اختر اللون"

#: includes/fields/class-acf-field-color_picker.php:86
msgid "Current Color"
msgstr "اللون الحالي"

#: includes/fields/class-acf-field-date_picker.php:36
msgid "Date Picker"
msgstr "عنصر إختيار التاريخ"

#: includes/fields/class-acf-field-date_picker.php:44
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "تم"

#: includes/fields/class-acf-field-date_picker.php:45
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "اليوم"

#: includes/fields/class-acf-field-date_picker.php:46
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "التالي"

#: includes/fields/class-acf-field-date_picker.php:47
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "السابق"

#: includes/fields/class-acf-field-date_picker.php:48
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "اسبوع"

#: includes/fields/class-acf-field-date_picker.php:223 includes/fields/class-acf-field-date_time_picker.php:197 includes/fields/class-acf-field-time_picker.php:127
msgid "Display Format"
msgstr "تنسيق العرض"

#: includes/fields/class-acf-field-date_picker.php:224 includes/fields/class-acf-field-date_time_picker.php:198 includes/fields/class-acf-field-time_picker.php:128
msgid "The format displayed when editing a post"
msgstr "تنسيق العرض عند تحرير المقال"

#: includes/fields/class-acf-field-date_picker.php:232 includes/fields/class-acf-field-date_picker.php:263 includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:224 includes/fields/class-acf-field-time_picker.php:135 includes/fields/class-acf-field-time_picker.php:150
msgid "Custom:"
msgstr "مخصص:"

#: includes/fields/class-acf-field-date_picker.php:242
msgid "Save Format"
msgstr "حفظ التنسيق"

#: includes/fields/class-acf-field-date_picker.php:243
msgid "The format used when saving a value"
msgstr "التنسيق المستخدم عند حفظ القيمة"

#: includes/fields/class-acf-field-date_picker.php:253 includes/fields/class-acf-field-date_time_picker.php:214 includes/fields/class-acf-field-post_object.php:447
#: includes/fields/class-acf-field-relationship.php:778 includes/fields/class-acf-field-select.php:524 includes/fields/class-acf-field-time_picker.php:142
msgid "Return Format"
msgstr "التنسيق المسترجع"

#: includes/fields/class-acf-field-date_picker.php:254 includes/fields/class-acf-field-date_time_picker.php:215 includes/fields/class-acf-field-time_picker.php:143
msgid "The format returned via template functions"
msgstr "التنسيق عاد عن طريق وظائف القالب"

#: includes/fields/class-acf-field-date_picker.php:272 includes/fields/class-acf-field-date_time_picker.php:231
msgid "Week Starts On"
msgstr "يبدأ الأسبوع في"

#: includes/fields/class-acf-field-date_time_picker.php:36
msgid "Date Time Picker"
msgstr "عنصر إختيار التاريخ والوقت"

#: includes/fields/class-acf-field-date_time_picker.php:44
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "اختر الوقت"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "الوقت"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "الساعة"

#: includes/fields/class-acf-field-date_time_picker.php:47
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "الدقيقة"

#: includes/fields/class-acf-field-date_time_picker.php:48
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "الثانية"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "ميلي ثانية"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "ميكرو ثانية"

#: includes/fields/class-acf-field-date_time_picker.php:51
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "المنطقة الزمنية"

#: includes/fields/class-acf-field-date_time_picker.php:52
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "الان"

#: includes/fields/class-acf-field-date_time_picker.php:53
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "تم"

#: includes/fields/class-acf-field-date_time_picker.php:54
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "اختر"

#: includes/fields/class-acf-field-date_time_picker.php:56
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "صباحا"

#: includes/fields/class-acf-field-date_time_picker.php:57
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "ص"

#: includes/fields/class-acf-field-date_time_picker.php:60
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "مساء"

#: includes/fields/class-acf-field-date_time_picker.php:61
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "م"

#: includes/fields/class-acf-field-email.php:36
msgid "Email"
msgstr "البريد الإلكتروني"

#: includes/fields/class-acf-field-email.php:134 includes/fields/class-acf-field-number.php:146 includes/fields/class-acf-field-radio.php:292 includes/fields/class-acf-field-text.php:143
#: includes/fields/class-acf-field-textarea.php:140 includes/fields/class-acf-field-url.php:115 includes/fields/class-acf-field-wysiwyg.php:437
msgid "Appears when creating a new post"
msgstr "يظهر عند إنشاء مقالة جديدة"

#: includes/fields/class-acf-field-email.php:142 includes/fields/class-acf-field-number.php:154 includes/fields/class-acf-field-password.php:134 includes/fields/class-acf-field-text.php:151
#: includes/fields/class-acf-field-textarea.php:148 includes/fields/class-acf-field-url.php:123
msgid "Placeholder Text"
msgstr "نص الـ placeholder"

#: includes/fields/class-acf-field-email.php:143 includes/fields/class-acf-field-number.php:155 includes/fields/class-acf-field-password.php:135 includes/fields/class-acf-field-text.php:152
#: includes/fields/class-acf-field-textarea.php:149 includes/fields/class-acf-field-url.php:124
msgid "Appears within the input"
msgstr "سيظهر داخل مربع الإدخال."

#: includes/fields/class-acf-field-email.php:151 includes/fields/class-acf-field-number.php:163 includes/fields/class-acf-field-password.php:143 includes/fields/class-acf-field-text.php:160
msgid "Prepend"
msgstr "بادئة"

#: includes/fields/class-acf-field-email.php:152 includes/fields/class-acf-field-number.php:164 includes/fields/class-acf-field-password.php:144 includes/fields/class-acf-field-text.php:161
msgid "Appears before the input"
msgstr "يظهر قبل الإدخال"

#: includes/fields/class-acf-field-email.php:160 includes/fields/class-acf-field-number.php:172 includes/fields/class-acf-field-password.php:152 includes/fields/class-acf-field-text.php:169
msgid "Append"
msgstr "إلحاق"

#: includes/fields/class-acf-field-email.php:161 includes/fields/class-acf-field-number.php:173 includes/fields/class-acf-field-password.php:153 includes/fields/class-acf-field-text.php:170
msgid "Appears after the input"
msgstr "يظهر بعد الإدخال"

#: includes/fields/class-acf-field-file.php:36
msgid "File"
msgstr "ملف"

#: includes/fields/class-acf-field-file.php:47
msgid "Edit File"
msgstr "تعديل الملف"

#: includes/fields/class-acf-field-file.php:48
msgid "Update File"
msgstr "تحديث الملف"

#: includes/fields/class-acf-field-file.php:49 includes/fields/class-acf-field-image.php:54 includes/media.php:57 pro/fields/class-acf-field-gallery.php:55
msgid "Uploaded to this post"
msgstr "مرفوع الى هذه المقالة"

#: includes/fields/class-acf-field-file.php:145
msgid "File name"
msgstr "إسم الملف"

#: includes/fields/class-acf-field-file.php:149 includes/fields/class-acf-field-file.php:252 includes/fields/class-acf-field-file.php:263 includes/fields/class-acf-field-image.php:266
#: includes/fields/class-acf-field-image.php:295 pro/fields/class-acf-field-gallery.php:705 pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "حجم الملف"

#: includes/fields/class-acf-field-file.php:174
msgid "Add File"
msgstr "إضافة ملف"

#: includes/fields/class-acf-field-file.php:225
msgid "File Array"
msgstr "مصفوفة الملف"

#: includes/fields/class-acf-field-file.php:226
msgid "File URL"
msgstr "رابط الملف URL"

#: includes/fields/class-acf-field-file.php:227
msgid "File ID"
msgstr "معرف الملف"

#: includes/fields/class-acf-field-file.php:234 includes/fields/class-acf-field-image.php:231 pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "المكتبة"

#: includes/fields/class-acf-field-file.php:235 includes/fields/class-acf-field-image.php:232 pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "الحد من اختيار مكتبة الوسائط"

#: includes/fields/class-acf-field-file.php:240 includes/fields/class-acf-field-image.php:237 includes/locations/class-acf-location-attachment.php:105
#: includes/locations/class-acf-location-comment.php:83 includes/locations/class-acf-location-nav-menu.php:106 includes/locations/class-acf-location-taxonomy.php:83
#: includes/locations/class-acf-location-user-form.php:91 includes/locations/class-acf-location-user-role.php:108 includes/locations/class-acf-location-widget.php:87
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "الكل"

#: includes/fields/class-acf-field-file.php:241 includes/fields/class-acf-field-image.php:238 pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "مرفوع الى المقالة"

#: includes/fields/class-acf-field-file.php:248 includes/fields/class-acf-field-image.php:245 pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "الحد الأدنى"

#: includes/fields/class-acf-field-file.php:249 includes/fields/class-acf-field-file.php:260
msgid "Restrict which files can be uploaded"
msgstr "تقييد الملفات التي يمكن رفعها"

#: includes/fields/class-acf-field-file.php:259 includes/fields/class-acf-field-image.php:274 pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "الحد الأقصى"

#: includes/fields/class-acf-field-file.php:270 includes/fields/class-acf-field-image.php:303 pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "أنواع الملفات المسموح بها"

#: includes/fields/class-acf-field-file.php:271 includes/fields/class-acf-field-image.php:304 pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr "قائمة مفصولة بفواصل. اترك المساحة فارغة للسماح بالكل"

#: includes/fields/class-acf-field-google-map.php:36
msgid "Google Map"
msgstr "خرائط جوجل"

#: includes/fields/class-acf-field-google-map.php:51
msgid "Locating"
msgstr "تحديد الموقع"

#: includes/fields/class-acf-field-google-map.php:52
msgid "Sorry, this browser does not support geolocation"
msgstr "عذراً، هذا المتصفح لا يدعم تحديد الموقع الجغرافي"

#: includes/fields/class-acf-field-google-map.php:133
msgid "Clear location"
msgstr "مسح الموقع"

#: includes/fields/class-acf-field-google-map.php:134
msgid "Find current location"
msgstr "البحث عن الموقع الحالي"

#: includes/fields/class-acf-field-google-map.php:137
msgid "Search for address..."
msgstr "البحث عن عنوان..."

#: includes/fields/class-acf-field-google-map.php:167 includes/fields/class-acf-field-google-map.php:178
msgid "Center"
msgstr "منتصف"

#: includes/fields/class-acf-field-google-map.php:168 includes/fields/class-acf-field-google-map.php:179
msgid "Center the initial map"
msgstr "مركز الخريطة الأولية"

#: includes/fields/class-acf-field-google-map.php:190
msgid "Zoom"
msgstr "التكبير"

#: includes/fields/class-acf-field-google-map.php:191
msgid "Set the initial zoom level"
msgstr "ضبط مستوى التكبير"

#: includes/fields/class-acf-field-google-map.php:200 includes/fields/class-acf-field-image.php:257 includes/fields/class-acf-field-image.php:286
#: includes/fields/class-acf-field-oembed.php:297 pro/fields/class-acf-field-gallery.php:696 pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "الإرتفاع"

#: includes/fields/class-acf-field-google-map.php:201
msgid "Customise the map height"
msgstr "تخصيص ارتفاع الخريطة"

#: includes/fields/class-acf-field-group.php:36
msgid "Group"
msgstr "مجموعة"

#: includes/fields/class-acf-field-group.php:469 pro/fields/class-acf-field-repeater.php:453
msgid "Sub Fields"
msgstr "الحقول الفرعية"

#: includes/fields/class-acf-field-group.php:486 pro/fields/class-acf-field-clone.php:890
msgid "Specify the style used to render the selected fields"
msgstr "حدد النمط المستخدم لعرض الحقول المحددة"

#: includes/fields/class-acf-field-group.php:491 pro/fields/class-acf-field-clone.php:895 pro/fields/class-acf-field-flexible-content.php:629 pro/fields/class-acf-field-repeater.php:522
msgid "Block"
msgstr "كتلة"

#: includes/fields/class-acf-field-group.php:492 pro/fields/class-acf-field-clone.php:896 pro/fields/class-acf-field-flexible-content.php:628 pro/fields/class-acf-field-repeater.php:521
msgid "Table"
msgstr "جدول"

#: includes/fields/class-acf-field-group.php:493 pro/fields/class-acf-field-clone.php:897 pro/fields/class-acf-field-flexible-content.php:630 pro/fields/class-acf-field-repeater.php:523
msgid "Row"
msgstr "سطر"

#: includes/fields/class-acf-field-image.php:36
msgid "Image"
msgstr "صورة"

#: includes/fields/class-acf-field-image.php:51
msgid "Select Image"
msgstr "إختر صورة"

#: includes/fields/class-acf-field-image.php:52 pro/fields/class-acf-field-gallery.php:53
msgid "Edit Image"
msgstr "تحرير الصورة"

#: includes/fields/class-acf-field-image.php:53 pro/fields/class-acf-field-gallery.php:54
msgid "Update Image"
msgstr "تحديث الصورة"

#: includes/fields/class-acf-field-image.php:55
msgid "All images"
msgstr "جميع الصور"

#: includes/fields/class-acf-field-image.php:142 includes/fields/class-acf-field-link.php:153 includes/input.php:267 pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "ازالة"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "لم يتم اختيار صورة"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "اضافة صورة"

#: includes/fields/class-acf-field-image.php:212
msgid "Image Array"
msgstr "مصفوفة الصور"

#: includes/fields/class-acf-field-image.php:213
msgid "Image URL"
msgstr "رابط الصورة"

#: includes/fields/class-acf-field-image.php:214
msgid "Image ID"
msgstr "معرف الصورة"

#: includes/fields/class-acf-field-image.php:221
msgid "Preview Size"
msgstr "حجم المعاينة"

#: includes/fields/class-acf-field-image.php:222
msgid "Shown when entering data"
msgstr "تظهر عند إدخال البيانات"

#: includes/fields/class-acf-field-image.php:246 includes/fields/class-acf-field-image.php:275 pro/fields/class-acf-field-gallery.php:685 pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "تقييد الصور التي يمكن رفعها"

#: includes/fields/class-acf-field-image.php:249 includes/fields/class-acf-field-image.php:278 includes/fields/class-acf-field-oembed.php:286 pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "العرض"

#: includes/fields/class-acf-field-link.php:36
msgid "Link"
msgstr "الرابط"

#: includes/fields/class-acf-field-link.php:146
msgid "Select Link"
msgstr "إختر رابط"

#: includes/fields/class-acf-field-link.php:151
msgid "Opens in a new window/tab"
msgstr "فتح في نافذة / علامة تبويب جديدة"

#: includes/fields/class-acf-field-link.php:186
msgid "Link Array"
msgstr "مصفوفة الرابط"

#: includes/fields/class-acf-field-link.php:187
msgid "Link URL"
msgstr "رابط URL"

#: includes/fields/class-acf-field-message.php:36 includes/fields/class-acf-field-message.php:115 includes/fields/class-acf-field-true_false.php:141
msgid "Message"
msgstr "الرسالة"

#: includes/fields/class-acf-field-message.php:124 includes/fields/class-acf-field-textarea.php:176
msgid "New Lines"
msgstr "سطور جديدة"

#: includes/fields/class-acf-field-message.php:125 includes/fields/class-acf-field-textarea.php:177
msgid "Controls how new lines are rendered"
msgstr "تحكم في طريقة عرض السطور الجديدة"

#: includes/fields/class-acf-field-message.php:129 includes/fields/class-acf-field-textarea.php:181
msgid "Automatically add paragraphs"
msgstr "إضافة الفقرات تلقائيا"

#: includes/fields/class-acf-field-message.php:130 includes/fields/class-acf-field-textarea.php:182
msgid "Automatically add &lt;br&gt;"
msgstr "اضف  &lt;br&gt; تلقائياً"

#: includes/fields/class-acf-field-message.php:131 includes/fields/class-acf-field-textarea.php:183
msgid "No Formatting"
msgstr "بدون تنسيق"

#: includes/fields/class-acf-field-message.php:138
msgid "Escape HTML"
msgstr "استبعاد كود HTML"

#: includes/fields/class-acf-field-message.php:139
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "السماح بعرض كود HTML كنص"

#: includes/fields/class-acf-field-number.php:36
msgid "Number"
msgstr "رقم"

#: includes/fields/class-acf-field-number.php:181
msgid "Minimum Value"
msgstr "قيمة الحد الأدنى"

#: includes/fields/class-acf-field-number.php:190
msgid "Maximum Value"
msgstr "قيمة الحد الأقصى"

#: includes/fields/class-acf-field-number.php:199
msgid "Step Size"
msgstr "حجم الخطوة"

#: includes/fields/class-acf-field-number.php:237
msgid "Value must be a number"
msgstr "يجب أن تكون القيمة رقماً"

#: includes/fields/class-acf-field-number.php:255
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "يجب أن تكون القيمة مساوية أو أكبر من  %d"

#: includes/fields/class-acf-field-number.php:263
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "يجب أن تكون القيمة مساوية أو أقل من  %d"

#: includes/fields/class-acf-field-oembed.php:36
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:237
msgid "Enter URL"
msgstr "قم بإدخال عنوان URL"

#: includes/fields/class-acf-field-oembed.php:250 includes/fields/class-acf-field-taxonomy.php:904
msgid "Error."
msgstr "خطأ."

#: includes/fields/class-acf-field-oembed.php:250
msgid "No embed found for the given URL."
msgstr "لم يتم العثور على تضمين لعنوان URL المحدد."

#: includes/fields/class-acf-field-oembed.php:283 includes/fields/class-acf-field-oembed.php:294
msgid "Embed Size"
msgstr "حجم المضمن"

#: includes/fields/class-acf-field-page_link.php:192
msgid "Archives"
msgstr "الأرشيفات"

#: includes/fields/class-acf-field-page_link.php:500 includes/fields/class-acf-field-post_object.php:399 includes/fields/class-acf-field-relationship.php:704
msgid "Filter by Post Type"
msgstr "فرز حسب نوع المقالة"

#: includes/fields/class-acf-field-page_link.php:508 includes/fields/class-acf-field-post_object.php:407 includes/fields/class-acf-field-relationship.php:712
msgid "All post types"
msgstr "أنواع المقالات"

#: includes/fields/class-acf-field-page_link.php:514 includes/fields/class-acf-field-post_object.php:413 includes/fields/class-acf-field-relationship.php:718
msgid "Filter by Taxonomy"
msgstr "تصفية حسب التصنيف"

#: includes/fields/class-acf-field-page_link.php:522 includes/fields/class-acf-field-post_object.php:421 includes/fields/class-acf-field-relationship.php:726
msgid "All taxonomies"
msgstr "كافة التصنيفات"

#: includes/fields/class-acf-field-page_link.php:528 includes/fields/class-acf-field-post_object.php:427 includes/fields/class-acf-field-radio.php:259
#: includes/fields/class-acf-field-select.php:484 includes/fields/class-acf-field-taxonomy.php:799 includes/fields/class-acf-field-user.php:423
msgid "Allow Null?"
msgstr "السماح بالفارغ؟"

#: includes/fields/class-acf-field-page_link.php:538
msgid "Allow Archives URLs"
msgstr "السماح بالعناوين المؤرشفة"

#: includes/fields/class-acf-field-page_link.php:548 includes/fields/class-acf-field-post_object.php:437 includes/fields/class-acf-field-select.php:494
#: includes/fields/class-acf-field-user.php:433
msgid "Select multiple values?"
msgstr "تحديد قيم متعددة؟"

#: includes/fields/class-acf-field-password.php:36
msgid "Password"
msgstr "كلمة السر"

#: includes/fields/class-acf-field-post_object.php:36 includes/fields/class-acf-field-post_object.php:452 includes/fields/class-acf-field-relationship.php:783
msgid "Post Object"
msgstr "Post Object"

#: includes/fields/class-acf-field-post_object.php:453 includes/fields/class-acf-field-relationship.php:784
msgid "Post ID"
msgstr "معرف المقال"

#: includes/fields/class-acf-field-radio.php:36
msgid "Radio Button"
msgstr "زر الراديو"

#: includes/fields/class-acf-field-radio.php:269
msgid "Other"
msgstr "أخرى"

#: includes/fields/class-acf-field-radio.php:274
msgid "Add 'other' choice to allow for custom values"
msgstr "إضافة خيار 'آخر' للسماح بقيم مخصصة"

#: includes/fields/class-acf-field-radio.php:280
msgid "Save Other"
msgstr "حفظ الأخرى"

#: includes/fields/class-acf-field-radio.php:285
msgid "Save 'other' values to the field's choices"
msgstr "حفظ القيم الأخرى لخيارات الحقل"

#: includes/fields/class-acf-field-relationship.php:36
msgid "Relationship"
msgstr "علاقة"

#: includes/fields/class-acf-field-relationship.php:48
msgid "Minimum values reached ( {min} values )"
msgstr "تم الوصول الى الحد الأدنى من القيم ( {min} قيمة )"

#: includes/fields/class-acf-field-relationship.php:49
msgid "Maximum values reached ( {max} values )"
msgstr "وصلت إلى الحد الأقصى للقيم ( {max} قيمة )"

#: includes/fields/class-acf-field-relationship.php:50
msgid "Loading"
msgstr "تحميل"

#: includes/fields/class-acf-field-relationship.php:51
msgid "No matches found"
msgstr "لم يتم العثور على مطابقات"

#: includes/fields/class-acf-field-relationship.php:585
msgid "Search..."
msgstr "بحث..."

#: includes/fields/class-acf-field-relationship.php:594
msgid "Select post type"
msgstr "اختر نوع المقال"

#: includes/fields/class-acf-field-relationship.php:607
msgid "Select taxonomy"
msgstr "اختر التصنيف"

#: includes/fields/class-acf-field-relationship.php:732
msgid "Filters"
msgstr "فرز"

#: includes/fields/class-acf-field-relationship.php:738 includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "نوع المقال"

#: includes/fields/class-acf-field-relationship.php:739 includes/fields/class-acf-field-taxonomy.php:36 includes/fields/class-acf-field-taxonomy.php:769
msgid "Taxonomy"
msgstr "التصنيف"

#: includes/fields/class-acf-field-relationship.php:746
msgid "Elements"
msgstr "العناصر"

#: includes/fields/class-acf-field-relationship.php:747
msgid "Selected elements will be displayed in each result"
msgstr "سيتم عرض العناصر المحددة في كل نتيجة"

#: includes/fields/class-acf-field-relationship.php:758
msgid "Minimum posts"
msgstr "الحد الأدنى للمقالات"

#: includes/fields/class-acf-field-relationship.php:767
msgid "Maximum posts"
msgstr "الحد الأقصى للمقالات"

#: includes/fields/class-acf-field-relationship.php:871 pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s يتطلب على الأقل %s تحديد"
msgstr[1] "%s يتطلب على الأقل %s تحديد"
msgstr[2] "%s يتطلب على الأقل %s تحديدان"
msgstr[3] "%s يتطلب على الأقل %s تحديد"
msgstr[4] "%s يتطلب على الأقل %s تحديد"
msgstr[5] "%s يتطلب على الأقل %s تحديد"

#: includes/fields/class-acf-field-select.php:36 includes/fields/class-acf-field-taxonomy.php:791
msgctxt "noun"
msgid "Select"
msgstr "اختار"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "نتيجة واحدة متاحة، اضغط على زر الإدخال لتحديدها."

#: includes/fields/class-acf-field-select.php:50
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d نتيجة متاحة، استخدم مفاتيح الأسهم للتنقل."

#: includes/fields/class-acf-field-select.php:51
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "لم يتم العثور على مطابقات"

#: includes/fields/class-acf-field-select.php:52
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "الرجاء إدخال حرف واحد أو أكثر"

#: includes/fields/class-acf-field-select.php:53
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "الرجاء إدخال %d حرف أو أكثر"

#: includes/fields/class-acf-field-select.php:54
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "الرجاء حذف حرف واحد"

#: includes/fields/class-acf-field-select.php:55
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "الرجاء حذف %d حرف"

#: includes/fields/class-acf-field-select.php:56
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "يمكنك تحديد عنصر واحد فقط"

#: includes/fields/class-acf-field-select.php:57
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "يمكنك تحديد %d عنصر فقط"

#: includes/fields/class-acf-field-select.php:58
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "تحميل نتائج أكثر&hellip;"

#: includes/fields/class-acf-field-select.php:59
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "بحث &hellip;"

#: includes/fields/class-acf-field-select.php:60
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "عملية التحميل فشلت"

#: includes/fields/class-acf-field-select.php:270 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "اختار"

#: includes/fields/class-acf-field-select.php:504 includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "واجهة المستخدم الأنيقة"

#: includes/fields/class-acf-field-select.php:514
msgid "Use AJAX to lazy load choices?"
msgstr "استخدام AJAX لخيارات التحميل الكسول؟"

#: includes/fields/class-acf-field-select.php:525
msgid "Specify the value returned"
msgstr "حدد القيمة التي سيتم إرجاعها"

#: includes/fields/class-acf-field-separator.php:36
msgid "Separator"
msgstr "فاصل"

#: includes/fields/class-acf-field-tab.php:36
msgid "Tab"
msgstr "تبويب"

#: includes/fields/class-acf-field-tab.php:96
msgid "The tab field will display incorrectly when added to a Table style repeater field or flexible content field layout"
msgstr "سيتم عرض حقل علامة التبويب بشكل غير صحيح عند إضافته إلى حقل مكرر بتنسيق جدول أو محتوى مرن"

#: includes/fields/class-acf-field-tab.php:97
msgid "Use \"Tab Fields\" to better organize your edit screen by grouping fields together."
msgstr "استخدم \"حقل علامة التبويب\" لتنظيم أفضل لشاشة التحرير الخاصة بك عن طريق تجميع الحقول معا."

#: includes/fields/class-acf-field-tab.php:98
msgid "All fields following this \"tab field\" (or until another \"tab field\" is defined) will be grouped together using this field's label as the tab heading."
msgstr "كافة الحقول بعد \"حقل علامة التبويب\" هذة (أو حتى إضافة \"حقل علامة تبويب آخر\") سوف يتم تجميعها معا باستخدام تسمية هذا الحقل كعنوان للتبويب."

#: includes/fields/class-acf-field-tab.php:112
msgid "Placement"
msgstr "الوضع"

#: includes/fields/class-acf-field-tab.php:124
msgid "End-point"
msgstr "نقطة النهاية"

#: includes/fields/class-acf-field-tab.php:125
msgid "Use this field as an end-point and start a new group of tabs"
msgstr "استخدم هذا الحقل كنقطة نهاية وابدأ مجموعة جديدة من علامات التبويب"

#: includes/fields/class-acf-field-taxonomy.php:719 includes/fields/class-acf-field-true_false.php:95 includes/fields/class-acf-field-true_false.php:184 includes/input.php:266
#: pro/admin/views/html-settings-updates.php:103
msgid "No"
msgstr "لا"

#: includes/fields/class-acf-field-taxonomy.php:738
msgid "None"
msgstr "لا شيء"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Select the taxonomy to be displayed"
msgstr "حدد التصنيف الذي سيتم عرضه"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Appearance"
msgstr "المظهر"

#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Select the appearance of this field"
msgstr "حدد مظهر هذا الحقل"

#: includes/fields/class-acf-field-taxonomy.php:785
msgid "Multiple Values"
msgstr "قيم متعددة"

#: includes/fields/class-acf-field-taxonomy.php:787
msgid "Multi Select"
msgstr "متعددة الاختيار"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Single Value"
msgstr "قيمة مفردة"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Radio Buttons"
msgstr "ازرار الراديو"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Create Terms"
msgstr "إنشاء شروط"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Allow new terms to be created whilst editing"
msgstr "السماح بإنشاء شروط جديدة أثناء التحرير"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Save Terms"
msgstr "حفظ الشروط"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Connect selected terms to the post"
msgstr "وصل الشروط المحددة بالمقالة"

#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Load Terms"
msgstr "تحميل الشروط"

#: includes/fields/class-acf-field-taxonomy.php:830
msgid "Load value from posts terms"
msgstr "تحميل قيمة من شروط المقالة"

#: includes/fields/class-acf-field-taxonomy.php:844
msgid "Term Object"
msgstr "Term Object"

#: includes/fields/class-acf-field-taxonomy.php:845
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:904
#, php-format
msgid "User unable to add new %s"
msgstr "المستخدم غير قادر على إضافة %s جديد"

#: includes/fields/class-acf-field-taxonomy.php:917
#, php-format
msgid "%s already exists"
msgstr "%s موجود بالفعل"

#: includes/fields/class-acf-field-taxonomy.php:958
#, php-format
msgid "%s added"
msgstr "تمت اضافة %s"

#: includes/fields/class-acf-field-taxonomy.php:1003
msgid "Add"
msgstr "إضافة"

#: includes/fields/class-acf-field-text.php:36
msgid "Text"
msgstr "نص"

#: includes/fields/class-acf-field-text.php:178 includes/fields/class-acf-field-textarea.php:157
msgid "Character Limit"
msgstr "الحد الأقصى للحروف"

#: includes/fields/class-acf-field-text.php:179 includes/fields/class-acf-field-textarea.php:158
msgid "Leave blank for no limit"
msgstr "اتركه فارغا لبدون حد."

#: includes/fields/class-acf-field-textarea.php:36
msgid "Text Area"
msgstr "مربع النص"

#: includes/fields/class-acf-field-textarea.php:166
msgid "Rows"
msgstr "صفوف"

#: includes/fields/class-acf-field-textarea.php:167
msgid "Sets the textarea height"
msgstr "تعيين ارتفاع مربع النص"

#: includes/fields/class-acf-field-time_picker.php:36
#, fuzzy
msgid "Time Picker"
msgstr "عنصر إختيار التاريخ:"

#: includes/fields/class-acf-field-true_false.php:36
msgid "True / False"
msgstr "صح / خطأ"

#: includes/fields/class-acf-field-true_false.php:94 includes/fields/class-acf-field-true_false.php:174 includes/input.php:265 pro/admin/views/html-settings-updates.php:93
msgid "Yes"
msgstr "نعم"

#: includes/fields/class-acf-field-true_false.php:142
msgid "Displays text alongside the checkbox"
msgstr "عرض النص بجانب مربع الاختيار"

#: includes/fields/class-acf-field-true_false.php:170
msgid "On Text"
msgstr "النص اثناء التفعيل"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when active"
msgstr "النص المعروض عند التنشيط"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "النص اثناء عدم التفعيل"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "النص المعروض عند عدم النشاط"

#: includes/fields/class-acf-field-url.php:36
msgid "Url"
msgstr "الرابط"

#: includes/fields/class-acf-field-url.php:165
msgid "Value must be a valid URL"
msgstr "القيمة يجب أن تكون عنوان رابط صحيح"

#: includes/fields/class-acf-field-user.php:36 includes/locations.php:95
msgid "User"
msgstr "المستخدم"

#: includes/fields/class-acf-field-user.php:408
msgid "Filter by role"
msgstr "فرز حسب:"

#: includes/fields/class-acf-field-user.php:416
msgid "All user roles"
msgstr "جميع رتب المستخدم"

#: includes/fields/class-acf-field-wysiwyg.php:36
msgid "Wysiwyg Editor"
msgstr "محرر Wysiwyg"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Visual"
msgstr "مرئي"

#: includes/fields/class-acf-field-wysiwyg.php:386
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "نص"

#: includes/fields/class-acf-field-wysiwyg.php:392
msgid "Click to initialize TinyMCE"
msgstr "انقر لبدء تهيئة TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:445
msgid "Tabs"
msgstr "علامات التبويب"

#: includes/fields/class-acf-field-wysiwyg.php:450
msgid "Visual & Text"
msgstr "نص و مرئي"

#: includes/fields/class-acf-field-wysiwyg.php:451
msgid "Visual Only"
msgstr "المرئي فقط"

#: includes/fields/class-acf-field-wysiwyg.php:452
msgid "Text Only"
msgstr "النص فقط"

#: includes/fields/class-acf-field-wysiwyg.php:459
msgid "Toolbar"
msgstr "شريط الأدوات"

#: includes/fields/class-acf-field-wysiwyg.php:469
msgid "Show Media Upload Buttons?"
msgstr "اظهار زر إضافة ملفات الوسائط؟"

#: includes/fields/class-acf-field-wysiwyg.php:479
msgid "Delay initialization?"
msgstr "تأخير التهيئة؟"

#: includes/fields/class-acf-field-wysiwyg.php:480
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "لن يتم تهيئة TinyMCE حتى يتم النقر فوق الحقل"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303 pro/admin/admin-options-page.php:304
msgid "Edit field group"
msgstr "تحرير مجموعة الحقول"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "التحقق من البريد الإليكتروني"

#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:588 pro/options-page.php:81
msgid "Update"
msgstr "تحديث"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "تم تحديث المنشور ."

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr "تم الكشف عن البريد المزعج"

#: includes/input.php:258
msgid "Expand Details"
msgstr "توسيع التفاصيل"

#: includes/input.php:259
msgid "Collapse Details"
msgstr "طي التفاصيل"

#: includes/input.php:260
msgid "Validation successful"
msgstr "عملية التحقق تمت بنجاح"

#: includes/input.php:261 includes/validation.php:285 includes/validation.php:296
msgid "Validation failed"
msgstr "فشل في عملية التحقق"

#: includes/input.php:262
msgid "1 field requires attention"
msgstr "حقل واحد يتطلب الاهتمام"

#: includes/input.php:263
#, php-format
msgid "%d fields require attention"
msgstr "%d حقول تتطلب الاهتمام"

#: includes/input.php:264
msgid "Restricted"
msgstr "محظور"

#: includes/input.php:268
msgid "Cancel"
msgstr "الغاء"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "مقالة"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "صفحة"

#: includes/locations.php:96
msgid "Forms"
msgstr "نماذج"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "مرفقات"

#: includes/locations/class-acf-location-attachment.php:113
#, php-format
msgid "All %s formats"
msgstr "كل صيغ %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "تعليق"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "رتبة المستخدم الحالي"

#: includes/locations/class-acf-location-current-user-role.php:114
msgid "Super Admin"
msgstr "مدير"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "المستخدم الحالي"

#: includes/locations/class-acf-location-current-user.php:101
msgid "Logged in"
msgstr "مسجل الدخول"

#: includes/locations/class-acf-location-current-user.php:102
msgid "Viewing front end"
msgstr "عرض الواجهة الأمامية"

#: includes/locations/class-acf-location-current-user.php:103
msgid "Viewing back end"
msgstr "عرض الواجهة الخلفية"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "عنصر القائمة"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "القائمة"

#: includes/locations/class-acf-location-nav-menu.php:113
msgid "Menu Locations"
msgstr "مواقع القائمة"

#: includes/locations/class-acf-location-nav-menu.php:123
msgid "Menus"
msgstr "القوائم"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "أب الصفحة"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "قالب الصفحة:"

#: includes/locations/class-acf-location-page-template.php:102 includes/locations/class-acf-location-post-template.php:156
msgid "Default Template"
msgstr "قالب افتراضي"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "نوع الصفحة"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Front Page"
msgstr "الصفحة الرئسية"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Posts Page"
msgstr "صفحة المقالات"

#: includes/locations/class-acf-location-page-type.php:151
msgid "Top Level Page (no parent)"
msgstr "أعلى مستوى للصفحة (بدون أب)"

#: includes/locations/class-acf-location-page-type.php:152
msgid "Parent Page (has children)"
msgstr "صفحة أب (لديها فروع)"

#: includes/locations/class-acf-location-page-type.php:153
msgid "Child Page (has parent)"
msgstr "صفحة فرعية (لديها أب)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "تصنيف المقالة"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "تنسيق المقالة"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "حالة المقالة"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "تصنيف المقالة"

#: includes/locations/class-acf-location-post-template.php:29
#, fuzzy
msgid "Post Template"
msgstr "قالب الصفحة:"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "شروط التصنيف"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "نموذج المستخدم"

#: includes/locations/class-acf-location-user-form.php:92
msgid "Add / Edit"
msgstr "إضافة / تعديل"

#: includes/locations/class-acf-location-user-form.php:93
msgid "Register"
msgstr "التسجيل"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "رتبة المستخدم"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "ودجت"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "تحرير"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "تحديث"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "قيمة %s مطلوبة"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "الحقول المخصصة المتقدمة للمحترفين"

#: pro/admin/admin-options-page.php:196
msgid "Publish"
msgstr "نشر"

#: pro/admin/admin-options-page.php:202
#, php-format
msgid "No Custom Field Groups found for this options page. <a href=\"%s\">Create a Custom Field Group</a>"
msgstr "لم يتم العثور على أية \"مجموعات حقول مخصصة لصفحة الخيارات هذة. <a href=\"%s\">أنشئ مجموعة حقول مخصصة</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>خطأ</b>. تعذر الاتصال بخادم التحديث"

#: pro/admin/admin-settings-updates.php:162 pro/admin/views/html-settings-updates.php:17
msgid "Updates"
msgstr "تحديثات"

#: pro/admin/views/html-settings-updates.php:11
msgid "Deactivate License"
msgstr "تعطيل الترخيص"

#: pro/admin/views/html-settings-updates.php:11
msgid "Activate License"
msgstr "تفعيل الترخيص"

#: pro/admin/views/html-settings-updates.php:21
msgid "License Information"
msgstr "معلومات الترخيص"

#: pro/admin/views/html-settings-updates.php:24
#, fuzzy, php-format
msgid "To unlock updates, please enter your license key below. If you don't have a licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</a>."
msgstr "لتمكين التحديثات، الرجاء إدخال مفتاح الترخيص الخاص بك على صفحة <a href=\"%s\">التحديثات</a> . إذا لم يكن لديك مفتاح ترخيص، يرجى الاطلاع على <a href=\"%s\">تفاصيل & التسعير</a>"

#: pro/admin/views/html-settings-updates.php:33
msgid "License Key"
msgstr "مفتاح الترخيص"

#: pro/admin/views/html-settings-updates.php:65
msgid "Update Information"
msgstr "معلومات التحديث"

#: pro/admin/views/html-settings-updates.php:72
msgid "Current Version"
msgstr "النسخة الحالية"

#: pro/admin/views/html-settings-updates.php:80
msgid "Latest Version"
msgstr "آخر نسخة"

#: pro/admin/views/html-settings-updates.php:88
msgid "Update Available"
msgstr "هنالك تحديث متاح"

#: pro/admin/views/html-settings-updates.php:96
msgid "Update Plugin"
msgstr "تحديث الاضافة"

#: pro/admin/views/html-settings-updates.php:98
msgid "Please enter your license key above to unlock updates"
msgstr "يرجى إدخال مفتاح الترخيص أعلاه لإلغاء تأمين التحديثات"

#: pro/admin/views/html-settings-updates.php:104
msgid "Check Again"
msgstr "الاختيار مرة أخرى"

#: pro/admin/views/html-settings-updates.php:121
msgid "Upgrade Notice"
msgstr "إشعار الترقية"

#: pro/fields/class-acf-field-clone.php:36
msgctxt "noun"
msgid "Clone"
msgstr "استنساخ"

#: pro/fields/class-acf-field-clone.php:858
msgid "Select one or more fields you wish to clone"
msgstr "حدد حقل واحد أو أكثر ترغب في استنساخه"

#: pro/fields/class-acf-field-clone.php:875
msgid "Display"
msgstr "عرض"

#: pro/fields/class-acf-field-clone.php:876
msgid "Specify the style used to render the clone field"
msgstr "حدد النمط المستخدم لعرض حقل الاستنساخ"

#: pro/fields/class-acf-field-clone.php:881
msgid "Group (displays selected fields in a group within this field)"
msgstr "المجموعة (تعرض الحقول المحددة في مجموعة ضمن هذا الحقل)"

#: pro/fields/class-acf-field-clone.php:882
msgid "Seamless (replaces this field with selected fields)"
msgstr "سلس (يستبدل هذا الحقل بالحقول المحددة)"

#: pro/fields/class-acf-field-clone.php:903
#, php-format
msgid "Labels will be displayed as %s"
msgstr "سيتم عرض التسمية كـ %s"

#: pro/fields/class-acf-field-clone.php:906
msgid "Prefix Field Labels"
msgstr "بادئة تسمية الحقول"

#: pro/fields/class-acf-field-clone.php:917
#, php-format
msgid "Values will be saved as %s"
msgstr "سيتم حفظ القيم كـ %s"

#: pro/fields/class-acf-field-clone.php:920
msgid "Prefix Field Names"
msgstr "بادئة أسماء الحقول"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field"
msgstr "حقل غير معروف"

#: pro/fields/class-acf-field-clone.php:1077
msgid "Unknown field group"
msgstr "مجموعة حقول غير معروفة"

#: pro/fields/class-acf-field-clone.php:1081
#, php-format
msgid "All fields from %s field group"
msgstr "جميع الحقول من مجموعة الحقول %s"

#: pro/fields/class-acf-field-flexible-content.php:42 pro/fields/class-acf-field-repeater.php:230 pro/fields/class-acf-field-repeater.php:534
msgid "Add Row"
msgstr "إضافة صف"

#: pro/fields/class-acf-field-flexible-content.php:45
msgid "layout"
msgstr "التخطيط"

#: pro/fields/class-acf-field-flexible-content.php:46
msgid "layouts"
msgstr "التخطيطات"

#: pro/fields/class-acf-field-flexible-content.php:47
msgid "remove {layout}?"
msgstr "إزالة {layout}؟"

#: pro/fields/class-acf-field-flexible-content.php:48
msgid "This field requires at least {min} {identifier}"
msgstr "يتطلب هذا الحقل على الأقل {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:49
msgid "This field has a limit of {max} {identifier}"
msgstr "يحتوي هذا الحقل حد {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:50
msgid "This field requires at least {min} {label} {identifier}"
msgstr "يتطلب هذا الحقل على الأقل {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:51
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "تم الوصول إلى حد أقصى ({max} {identifier}) لـ {label}"

#: pro/fields/class-acf-field-flexible-content.php:52
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} متاح (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:53
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} مطلوب (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:54
msgid "Flexible Content requires at least 1 layout"
msgstr "يتطلب المحتوى المرن تخطيط واحد على الأقل"

#: pro/fields/class-acf-field-flexible-content.php:288
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "انقر فوق الزر \"%s\" أدناه لبدء إنشاء التخطيط الخاص بك"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "إضافة تنسيق جديد"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Remove layout"
msgstr "إزالة التنسيق"

#: pro/fields/class-acf-field-flexible-content.php:425 pro/fields/class-acf-field-repeater.php:360
msgid "Click to toggle"
msgstr "انقر للتبديل"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder Layout"
msgstr "إعادة ترتيب التخطيط"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder"
msgstr "إعادة ترتيب"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete Layout"
msgstr "حذف التخطيط"

#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate Layout"
msgstr "تكرار التخطيط "

#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New Layout"
msgstr "إضافة تخطيط جديد"

#: pro/fields/class-acf-field-flexible-content.php:645
msgid "Min"
msgstr "الحد الأدنى"

#: pro/fields/class-acf-field-flexible-content.php:658
msgid "Max"
msgstr "الحد أقصى"

#: pro/fields/class-acf-field-flexible-content.php:685 pro/fields/class-acf-field-repeater.php:530
msgid "Button Label"
msgstr "تسمية الزر"

#: pro/fields/class-acf-field-flexible-content.php:694
msgid "Minimum Layouts"
msgstr "الحد الأدنى للتخطيطات"

#: pro/fields/class-acf-field-flexible-content.php:703
msgid "Maximum Layouts"
msgstr "الحد الأقصى للتخطيطات"

#: pro/fields/class-acf-field-gallery.php:52
msgid "Add Image to Gallery"
msgstr "اضافة صورة للمعرض"

#: pro/fields/class-acf-field-gallery.php:56
msgid "Maximum selection reached"
msgstr "وصلت للحد الأقصى"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "الطول"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "كلمات توضيحية"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "النص البديل"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "اضافة الى المعرض"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "- اجراءات جماعية -"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "ترتيب حسب تاريخ الرفع"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "ترتيب حسب تاريخ التعديل"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "ترتيب فرز حسب العنوان"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "عكس الترتيب الحالي"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "إغلاق"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "الحد الأدنى للاختيار"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "الحد الأقصى للاختيار"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "إدراج"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "حدد مكان إضافة المرفقات الجديدة"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "إلحاق بالنهاية"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "إلحاق بالبداية"

#: pro/fields/class-acf-field-repeater.php:47
msgid "Minimum rows reached ({min} rows)"
msgstr "وصلت للحد الأدنى من الصفوف ({min} صف)"

#: pro/fields/class-acf-field-repeater.php:48
msgid "Maximum rows reached ({max} rows)"
msgstr "بلغت الحد الأقصى من الصفوف ({max} صف)"

#: pro/fields/class-acf-field-repeater.php:405
msgid "Add row"
msgstr "إضافة صف"

#: pro/fields/class-acf-field-repeater.php:406
msgid "Remove row"
msgstr "إزالة صف"

#: pro/fields/class-acf-field-repeater.php:483
msgid "Collapsed"
msgstr "طي"

#: pro/fields/class-acf-field-repeater.php:484
msgid "Select a sub field to show when row is collapsed"
msgstr "حدد حقل فرعي لإظهار عند طي الصف"

#: pro/fields/class-acf-field-repeater.php:494
msgid "Minimum Rows"
msgstr "الحد الأدنى من الصفوف"

#: pro/fields/class-acf-field-repeater.php:504
msgid "Maximum Rows"
msgstr "الحد الأقصى من الصفوف"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "لا توجد صفحة خيارات"

#: pro/options-page.php:51
msgid "Options"
msgstr "خيارات"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "تم تحديث الإعدادات"

#: pro/updates.php:97
#, fuzzy, php-format
msgid "To enable updates, please enter your license key on the <a href=\"%s\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s\">details & pricing</a>."
msgstr "لتمكين التحديثات، الرجاء إدخال مفتاح الترخيص الخاص بك على صفحة <a href=\"%s\">التحديثات</a> . إذا لم يكن لديك مفتاح ترخيص، يرجى الاطلاع على <a href=\"%s\">تفاصيل & التسعير</a>"

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "http://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "إليوت كوندون"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Disabled"
#~ msgstr "تعطيل"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[1] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[2] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[3] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[4] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[5] "تعطيل <span class=\"count\">(%s)</span>"

#~ msgid "See what's new in"
#~ msgstr "أنظر ما هو الجديد في"

#~ msgid "version"
#~ msgstr "النسخة"

#~ msgid "Getting Started"
#~ msgstr "بدء العمل"

#~ msgid "Field Types"
#~ msgstr "أنواع بيانات الحقول"

#~ msgid "Functions"
#~ msgstr "الدالات"

#~ msgid "Actions"
#~ msgstr "الإجراءات"

#~ msgid "'How to' guides"
#~ msgstr "'كيف' أدلة"

#~ msgid "Tutorials"
#~ msgstr "الدروس التعليمية"

#~ msgid "Created by"
#~ msgstr "أنشئ بواسطة"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>تم بنجاح</b> أداة استيراد أضافت  %s جماعات الحقل %s"

#~ msgid "<b>Warning</b>. Import tool detected %s field groups already exist and have been ignored: %s"
#~ msgstr "<b>تحذير.</b> الكشف عن أداة استيراد مجموعة الحقول  %s موجودة بالفعل، وتم تجاهل  %s"

#~ msgid "Upgrade ACF"
#~ msgstr "ترقية ACF"

#~ msgid "Upgrade"
#~ msgstr "ترقية"

#~ msgid "Error"
#~ msgstr "خطأ"

#~ msgid "Upgrading data to"
#~ msgstr "تحديث البيانات"

#~ msgid "See what's new"
#~ msgstr "أنظر ما هو الجديد في"

#~ msgid "Show a different month"
#~ msgstr "عرض شهر مختلف"

#~ msgid "Return format"
#~ msgstr "إعادة تنسيق"

#~ msgid "uploaded to this post"
#~ msgstr "اضافة للصفحة"

#~ msgid "File Size"
#~ msgstr "حجم الملف"

#~ msgid "No File selected"
#~ msgstr "لا يوجد ملف محدد."

#~ msgid "eg. Show extra content"
#~ msgstr "على سبيل المثال. إظهار محتوى إضافي"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>خطأ في الاتصال</b>. آسف، الرجاء المحاولة مرة أخرى"

#~ msgid "Save Options"
#~ msgstr "حفظ الإعدادات"

#~ msgid "License"
#~ msgstr "الترخيص"

#~ msgid "To unlock updates, please enter your license key below. If you don't have a licence key, please see"
#~ msgstr "لللحصول على التحديثات، الرجاء إدخال مفتاح الترخيص الخاص بك أدناه. إذا لم يكن لديك مفتاح ترخيص، الرجاء مراجعة"

#~ msgid "details & pricing"
#~ msgstr "التفاصيل & الأسعار"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "حقول مخصصة متقدمة برو"
