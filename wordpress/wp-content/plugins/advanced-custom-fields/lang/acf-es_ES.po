msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-06-27 15:35+1000\n"
"PO-Revision-Date: 2018-02-06 10:05+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:63
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:355 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Grupos de Campos"

#: acf.php:356
msgid "Field Group"
msgstr "Grupo de Campos"

#: acf.php:357 acf.php:389 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New"
msgstr "Añadir nuevo"

#: acf.php:358
msgid "Add New Field Group"
msgstr "Añadir nuevo Field Group"

#: acf.php:359
msgid "Edit Field Group"
msgstr "Editar Grupo de Campos"

#: acf.php:360
msgid "New Field Group"
msgstr "Nuevo Grupo de Campos"

#: acf.php:361
msgid "View Field Group"
msgstr "Ver Grupo de Campos"

#: acf.php:362
msgid "Search Field Groups"
msgstr "Buscar Grupo de Campos"

#: acf.php:363
msgid "No Field Groups found"
msgstr "No se han encontrado Grupos de Campos"

#: acf.php:364
msgid "No Field Groups found in Trash"
msgstr "No se han encontrado Grupos de Campos en la Papelera"

#: acf.php:387 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:857
msgid "Fields"
msgstr "Campos"

#: acf.php:388
msgid "Field"
msgstr "Campo"

#: acf.php:390
msgid "Add New Field"
msgstr "Agregar Nuevo Campo"

#: acf.php:391
msgid "Edit Field"
msgstr "Editar Campo"

#: acf.php:392 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Nuevo Campo"

#: acf.php:393
msgid "View Field"
msgstr "Ver Campo"

#: acf.php:394
msgid "Search Fields"
msgstr "Buscar Campos"

#: acf.php:395
msgid "No Fields found"
msgstr "No se encontraron campos"

#: acf.php:396
msgid "No Fields found in Trash"
msgstr "No se encontraron Campos en Papelera"

#: acf.php:435 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Inactivo"

#: acf.php:440
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Grupo de campos actualizado."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Grupo de campos eliminado."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Grupo de campos guardado."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Grupo de Campos programado."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Borrador del Grupo de Campos actualizado."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Ubicación"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "Ajustes"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Mover a papelera. Estás seguro?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "Chequeado"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "No hay campos de conmutación disponibles"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "El título del grupo de campos es requerido"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:732
msgid "copy"
msgstr "copiar"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3970
msgid "or"
msgstr "o"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Campos Padre"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Campos de mismo nivel"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Mover Campo Personalizado"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "Este campo no puede ser movido hasta que sus cambios se hayan guardado"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Vacío"

#: includes/admin/admin-field-group.php:281 includes/input.php:257
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Los cambios que has realizado se perderán si navegas hacia otra página"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La cadena \"_field\" no se debe utilizar al comienzo de un nombre de campo"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Claves de Campo"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Activo"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Movimiento Completo."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "El campo %s puede ser ahora encontrado en el grupo de campos %s"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Cerrar Ventana"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Por favor selecciona el destino para este campo"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Mover Campo"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Grupo de campos duplicado. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicado."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Grupo de campos sincronizado. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s grupo de campos sincronizado."
msgstr[1] "%s grupos de campos sincronizado."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Sincronización disponible"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "Título"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "Descripción"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Estado"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personaliza Wordpress con campos poderosos, profesionales e intuitivos."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:111
msgid "Changelog"
msgstr "Changelog"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Ver las novedades de la <a href=\"% s \">versión %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Recursos"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Sitio web"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Documentación"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Soporte"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Gracias por crear con <a href=\" %s \">ACF</a>."

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "Duplicar este ítem"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:132
#: includes/fields/class-acf-field-relationship.php:737
msgid "Search"
msgstr "Buscar"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "Sincronizar grupo de campos"

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "Sincronizar"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr "Aplicar"

#: includes/admin/admin-field-groups.php:798
msgid "Bulk Actions"
msgstr "Acciones en lote"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Campos Personalizados"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Actualizar Base de datos"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Revisar sitios y actualizar"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "¡Error al validar la solicitud!"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "No hay actualizaciones disponibles."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Agregados"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Error</b>. No se pudo cargar la lista de agregados"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Info"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Qué hay de nuevo"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "Herramientas"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "No se seleccionaron grupos de campos"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:174
msgid "No file selected"
msgstr "No se seleccionó archivo"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "Error subiendo archivo.  Por favor intente nuevamente"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "Tipo de campo incorrecto"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "Archivo de imporación vacío"

#: includes/admin/settings-tools.php:331
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importar un grupo de campos"
msgstr[1] "Importar %s grupos de campos"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Lógica Condicional"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Mostrar este campo si"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:243
msgid "is equal to"
msgstr "es igual a"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:244
msgid "is not equal to"
msgstr "no es igual a"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "y"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Agregar grupo de reglas"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:420
#: pro/fields/class-acf-field-repeater.php:358
msgid "Drag to reorder"
msgstr "Arrastra para reordenar"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:152
#: pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "Editar"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Mover campo a otro grupo"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Mover"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Borrar campo"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete"
msgstr "Borrar"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Etiqueta del campo"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "Este es el nombre que aparecerá en la página EDITAR"

#: includes/admin/views/field-group-field.php:78
msgid "Field Name"
msgstr "Nombre del campo"

#: includes/admin/views/field-group-field.php:79
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Una sola palabra, sin espacios. Guiones bajos y barras están permitidos."

#: includes/admin/views/field-group-field.php:89
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/admin/views/field-group-field.php:101
#: includes/fields/class-acf-field-tab.php:102
msgid "Instructions"
msgstr "Instrucciones"

#: includes/admin/views/field-group-field.php:102
msgid "Instructions for authors. Shown when submitting data"
msgstr ""
"Instrucciones para los autores. Se muestra a la hora de introducir los datos."

#: includes/admin/views/field-group-field.php:111
msgid "Required?"
msgstr "¿Requerido?"

#: includes/admin/views/field-group-field.php:134
msgid "Wrapper Attributes"
msgstr "Atributos del Contenedor"

#: includes/admin/views/field-group-field.php:140
msgid "width"
msgstr "ancho"

#: includes/admin/views/field-group-field.php:155
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:168
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:180
msgid "Close Field"
msgstr "Cerrar Campo"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Orden"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-checkbox.php:317
#: includes/fields/class-acf-field-radio.php:321
#: includes/fields/class-acf-field-select.php:530
#: pro/fields/class-acf-field-flexible-content.php:599
msgid "Label"
msgstr "Etiqueta"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:970
#: pro/fields/class-acf-field-flexible-content.php:612
msgid "Name"
msgstr "Nombre"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Clave"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tipo"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"No hay campos. Haz Click en el botón <strong>+ Añadir campo</strong> para "
"crear tu primer campo."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Añadir Campo"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Reglas"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un conjunto de reglas para determinar qué pantallas de edición "
"utilizarán estos campos personalizados"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Estándar (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Directo (sin metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Posición"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Alta (después del título)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (después del contenido)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Ubicación de la etiqueta"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:116
msgid "Top aligned"
msgstr "Alineada arriba"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:117
msgid "Left aligned"
msgstr "Alineada a la izquierda"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Ubicación de la instrucción"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Debajo de las etiquetas"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Debajo de los campos"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Número de orden"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Los grupos de campos con menor orden aparecerán primero"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Mostrado en lista de grupos de campos"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Esconder en pantalla"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selecciona</b> los items para <b>esconderlos</b> en la pantalla de "
"edición."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si múltiples grupos de campos aparecen en una pantalla de edición, las "
"opciones del primer grupo serán utilizadas (el que tiene el menor número de "
"orden)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Enlace Permanente"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Editor de Contenido"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Extracto"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Discusión"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Comentarios"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Revisiones"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Formato"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Atributos de Página"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:751
msgid "Featured Image"
msgstr "Imagen Destacada"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Categorías"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Etiquetas"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Enviar Trackbacks"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos si"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Mejorar los sitios"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Actualización de Base de Datos de Advanced Custom Fields"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Los siguientes sitios requieren una actualización de la base de datos. Marca "
"los que desea actualizar y haga clic en %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Sitio"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "El sitio requiere actualización de base de datos de %s a %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "El sitio está actualizado"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Actualización de base de datos completa. <a href=\"%s\">Regresar al "
"Escritorio de Red</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Es fuertemente recomendado que hagas un backup de tu base de datos antes de "
"continuar. Estás seguro que quieres ejecutar la actualización ahora?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Actualización completa"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Actualizando datos a la versión %s."

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:36
msgid "Repeater"
msgstr "Repeater"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:36
msgid "Flexible Content"
msgstr "Contenido Flexible"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:36
msgid "Gallery"
msgstr "Galería"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:13
msgid "Options Page"
msgstr "Página de Opciones"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Actualización de Base de Datos Requerida"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Gracias por actualizar a %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Antes de comenzar a utilizar las nuevas y excelentes características, por "
"favor actualizar tu base de datos a la versión más nueva."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"También asegúrate de que todas las extensiones premium (%s) se hayan "
"actualizado a la última versión."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Leyendo tareas de actualización..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Actualización de la base de datos completada. <a href=\"%s \">Vea las "
"novedades</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Descargar e Instalar"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instalado"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Bienvenido a Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Gracias por actualizar! ACF %s es más grande y poderoso que nunca.  "
"Esperamos que te guste."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "Una experiencia de campos personalizados más fluida"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Usabilidad Mejorada"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Incluir la popular librería Select2 ha mejorado tanto la usabilidad como la "
"velocidad a través de varios tipos de campos, incluyendo el objeto post , "
"link a página, taxonomía y selección."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Diseño Mejorado"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Muchos campos han experimentado un mejorado visual para hacer que ACF luzca "
"mejor que nunca! Hay cambios notables en los campos de galería, relación y "
"oEmbed (nuevo)!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Datos Mejorados"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Rediseñar la arquitectura de datos ha permitido que los sub campos vivan "
"independientemente de sus padres.  Esto permite arrastrar y soltar campos "
"desde y hacia otros campos padres!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Adiós Agregados.  Hola PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Presentando ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Estamos cambiando la manera en que las funcionalidades premium son brindadas "
"de un modo muy interesante!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Todos los 4 agregados premium han sido combinados en una nueva <a href=\"%s"
"\">version Pro de ACF</a>. Con lincencias personales y para desarrolladores "
"disponibles, la funcionalidad premium es más acequible que nunca!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Características Poderosas"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO contiene poderosas características como campo de repetición, "
"contenido con disposición flexible, un hermoso campo de galería y la "
"habilidad de crear páginas de opciones extra en el panel de administración."

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Lee más acerca de las <a href=\"%s\">características de ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Fácil Actualización"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Para facilitar la actualización, <a href=\"%s\">accede a tu cuenta en "
"nuestra tienda</a> y solicita una copia gratis de ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Nosotros también escribimos una <a href=\"%s\">guía de actualización</a> "
"para responder cualquier pregunta, pero si tienes una, por favor contacta "
"nuestro equipo de soporte via <a href=\"%s\">mesa de ayuda</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Debajo del Capó"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Ajustes de campos más inteligentes"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr ""
"ACF ahora guarda los ajustes de los campos como objetos post individuales"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Más AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Más campos utilizan búsqueda manejada por AJAX para acelerar la carga de "
"página"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "La nueva funcionalidad de exportar a JSON mejora la velocidad"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Mejor Control por Versiones"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"La nueva funcionalidad de exporta a JSON permite que los ajustes de los "
"campos se controlen por versiones"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "Reemplazamos XML por JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Importar / Exportar ahora utilizan JSON en vez de XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Nuevos Formularios"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Los campos ahora pueden ser mapeados a comentarios, widgets y todos los "
"formularios de usuario!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Se agregó un nuevo campo para embeber contenido."

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Nueva Galería"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "El campo galería ha experimentado un muy necesario lavado de cara"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Nuevos Ajustes"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Se agregaron ajustes de grupos de campos para posicionamiento de las "
"etiquetas y las instrucciones"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Mejores formularios para Front End"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() ahora puede crear nuevos post"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Mejor Validación"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"La validación de los formularios es ahora realizada via PHP + AJAX en vez de "
"sólo JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Campod de Relación"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Nuevos ajustes para 'Filtros' en camp de Relación (Búsqueda, Tipo de Post, "
"Taxonomía)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Moviendo Campos"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Nueva funcionalidad de grupos permite mover campos entre grupos y padres"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:36
msgid "Page Link"
msgstr "Link de página"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Nuevo grupo archivos en el campo de selección de page_link"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Mejores Páginas de Opciones"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Nuevas funciones para las páginas de opciones permiten crear tanto páginas "
"de menú hijas como superiores."

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Creemos que te encantarán los cambios en %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Exportar Field Groups a PHP"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"El siguiente código puede ser utilizado para registrar una versión local del "
"o los grupos seleccionados.  Un grupo de campos local puede brindar muchos "
"beneficios como tiempos de carga más cortos, control por versiones y campos/"
"ajustes dinámicos.  Simplemente copia y pega el siguiente código en el "
"archivo functions.php de tu tema o inclúyelo como un archivo externo."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "Selleciona Grupos de Campos"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "Exportar Grupos de Campos"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Selecciona los grupos de campos que te gustaría exportar y luego elige tu "
"método de exportación.  Utiliza el boton de descarga para exportar a un "
"archivo .json el cual luego puedes importar en otra instalación de ACF. "
"Utiliza el botón de generación para exportar a código PHP que puedes incluir "
"en tu tema."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Descargar archivo de exportación"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Generar código de exportación"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "Importar Field Group"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Selecciona el archivo JSON de Advanced Custom Fields que te gustaría "
"importar.  Cuando hagas click en el botón importar debajo, ACF importará los "
"grupos de campos."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:46
msgid "Select File"
msgstr "Seleccionar archivo"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "Importar"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "Medio"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "GRande"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "Tamaño Completo"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1837
#: pro/fields/class-acf-field-clone.php:1042
msgid "(no title)"
msgstr "(sin título)"

#: includes/api/api-helpers.php:1874
#: includes/fields/class-acf-field-page_link.php:284
#: includes/fields/class-acf-field-post_object.php:283
#: includes/fields/class-acf-field-taxonomy.php:992
msgid "Parent"
msgstr "Superior"

#: includes/api/api-helpers.php:3891
#, php-format
msgid "Image width must be at least %dpx."
msgstr "El ancho de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:3896
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "El ancho de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:3912
#, php-format
msgid "Image height must be at least %dpx."
msgstr "La altura de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:3917
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "La altura de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:3935
#, php-format
msgid "File size must be at least %s."
msgstr "El tamaño de archivo debe ser al menos %s."

#: includes/api/api-helpers.php:3940
#, php-format
msgid "File size must must not exceed %s."
msgstr "El tamaño de archivo no debe exceder %s."

#: includes/api/api-helpers.php:3974
#, php-format
msgid "File type must be %s."
msgstr "El tipo de archivo debe ser %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Contenido"

#: includes/fields.php:146
msgid "Choice"
msgstr "Elección"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relación"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149 includes/fields/class-acf-field-checkbox.php:286
#: includes/fields/class-acf-field-group.php:485
#: includes/fields/class-acf-field-radio.php:300
#: pro/fields/class-acf-field-clone.php:889
#: pro/fields/class-acf-field-flexible-content.php:569
#: pro/fields/class-acf-field-flexible-content.php:618
#: pro/fields/class-acf-field-repeater.php:514
msgid "Layout"
msgstr "Layout"

#: includes/fields.php:305
msgid "Field type does not exist"
msgstr "Tipo de campo inexistente"

#: includes/fields.php:305
msgid "Unknown"
msgstr "Desconocido"

#: includes/fields/class-acf-field-checkbox.php:36
#: includes/fields/class-acf-field-taxonomy.php:786
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-checkbox.php:150
msgid "Toggle All"
msgstr "Invertir Todos"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "Agregar nueva opción"

#: includes/fields/class-acf-field-checkbox.php:246
#: includes/fields/class-acf-field-radio.php:250
#: includes/fields/class-acf-field-select.php:466
msgid "Choices"
msgstr "Opciones"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "Enter each choice on a new line."
msgstr "Ingresa cada opción en una nueva línea"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Para más control, puedes especificar tanto un valor como una etiqueta, así:"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "red : Red"
msgstr "rojo : Rojo"

#: includes/fields/class-acf-field-checkbox.php:255
msgid "Allow Custom"
msgstr "Permitir personalización"

#: includes/fields/class-acf-field-checkbox.php:260
msgid "Allow 'custom' values to be added"
msgstr "Permite añadir valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:266
msgid "Save Custom"
msgstr "Guardar personalización"

#: includes/fields/class-acf-field-checkbox.php:271
msgid "Save 'custom' values to the field's choices"
msgstr "Guardar los valores \"personalizados\" a las opciones del campo"

#: includes/fields/class-acf-field-checkbox.php:277
#: includes/fields/class-acf-field-color_picker.php:146
#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-radio.php:291
#: includes/fields/class-acf-field-select.php:475
#: includes/fields/class-acf-field-text.php:142
#: includes/fields/class-acf-field-textarea.php:139
#: includes/fields/class-acf-field-true_false.php:150
#: includes/fields/class-acf-field-url.php:114
#: includes/fields/class-acf-field-wysiwyg.php:436
msgid "Default Value"
msgstr "Valor por defecto"

#: includes/fields/class-acf-field-checkbox.php:278
#: includes/fields/class-acf-field-select.php:476
msgid "Enter each default value on a new line"
msgstr "Ingresa cada valor en una nueva línea"

#: includes/fields/class-acf-field-checkbox.php:292
#: includes/fields/class-acf-field-radio.php:306
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-checkbox.php:293
#: includes/fields/class-acf-field-radio.php:307
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-checkbox.php:300
msgid "Toggle"
msgstr "Invertir"

#: includes/fields/class-acf-field-checkbox.php:301
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Anteponer un checkbox extra para invertir todas las opciones"

#: includes/fields/class-acf-field-checkbox.php:310
#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:180
#: includes/fields/class-acf-field-radio.php:314
#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Return Value"
msgstr "Retornar valor"

#: includes/fields/class-acf-field-checkbox.php:311
#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:207
#: includes/fields/class-acf-field-link.php:181
#: includes/fields/class-acf-field-radio.php:315
msgid "Specify the returned value on front end"
msgstr "Especifica el valor retornado en el front end"

#: includes/fields/class-acf-field-checkbox.php:316
#: includes/fields/class-acf-field-radio.php:320
#: includes/fields/class-acf-field-select.php:529
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-checkbox.php:318
#: includes/fields/class-acf-field-radio.php:322
#: includes/fields/class-acf-field-select.php:531
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/fields/class-acf-field-color_picker.php:36
msgid "Color Picker"
msgstr "Selector de color"

#: includes/fields/class-acf-field-color_picker.php:83
msgid "Clear"
msgstr "Limpiar"

#: includes/fields/class-acf-field-color_picker.php:84
msgid "Default"
msgstr "Por defecto"

#: includes/fields/class-acf-field-color_picker.php:85
msgid "Select Color"
msgstr "Selecciona Color"

#: includes/fields/class-acf-field-color_picker.php:86
msgid "Current Color"
msgstr "Color actual"

#: includes/fields/class-acf-field-date_picker.php:36
msgid "Date Picker"
msgstr "Selector de Fecha"

#: includes/fields/class-acf-field-date_picker.php:44
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Hecho"

#: includes/fields/class-acf-field-date_picker.php:45
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoy"

#: includes/fields/class-acf-field-date_picker.php:46
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Siguiente"

#: includes/fields/class-acf-field-date_picker.php:47
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:48
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Se"

#: includes/fields/class-acf-field-date_picker.php:223
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-time_picker.php:127
msgid "Display Format"
msgstr "Formato de Visualización"

#: includes/fields/class-acf-field-date_picker.php:224
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:128
msgid "The format displayed when editing a post"
msgstr "El formato mostrado cuando se edita un post"

#: includes/fields/class-acf-field-date_picker.php:232
#: includes/fields/class-acf-field-date_picker.php:263
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:224
#: includes/fields/class-acf-field-time_picker.php:135
#: includes/fields/class-acf-field-time_picker.php:150
msgid "Custom:"
msgstr "Personalizado:"

#: includes/fields/class-acf-field-date_picker.php:242
msgid "Save Format"
msgstr "Guardar formato"

#: includes/fields/class-acf-field-date_picker.php:243
msgid "The format used when saving a value"
msgstr "El formato utilizado cuando se guarda un valor"

#: includes/fields/class-acf-field-date_picker.php:253
#: includes/fields/class-acf-field-date_time_picker.php:214
#: includes/fields/class-acf-field-post_object.php:447
#: includes/fields/class-acf-field-relationship.php:778
#: includes/fields/class-acf-field-select.php:524
#: includes/fields/class-acf-field-time_picker.php:142
msgid "Return Format"
msgstr "Formato de Retorno"

#: includes/fields/class-acf-field-date_picker.php:254
#: includes/fields/class-acf-field-date_time_picker.php:215
#: includes/fields/class-acf-field-time_picker.php:143
msgid "The format returned via template functions"
msgstr "El formato retornado a través de las funciones del tema"

#: includes/fields/class-acf-field-date_picker.php:272
#: includes/fields/class-acf-field-date_time_picker.php:231
msgid "Week Starts On"
msgstr "La semana comenza en "

#: includes/fields/class-acf-field-date_time_picker.php:36
msgid "Date Time Picker"
msgstr "Selector de fecha y hora"

#: includes/fields/class-acf-field-date_time_picker.php:44
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Elegir tiempo"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Tiempo"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:47
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "minuto"

#: includes/fields/class-acf-field-date_time_picker.php:48
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisegundo"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegundo"

#: includes/fields/class-acf-field-date_time_picker.php:51
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zona horaria"

#: includes/fields/class-acf-field-date_time_picker.php:52
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ahora"

#: includes/fields/class-acf-field-date_time_picker.php:53
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Hecho"

#: includes/fields/class-acf-field-date_time_picker.php:54
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Elige"

#: includes/fields/class-acf-field-date_time_picker.php:56
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:57
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:60
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:61
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:36
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-radio.php:292
#: includes/fields/class-acf-field-text.php:143
#: includes/fields/class-acf-field-textarea.php:140
#: includes/fields/class-acf-field-url.php:115
#: includes/fields/class-acf-field-wysiwyg.php:437
msgid "Appears when creating a new post"
msgstr "Aparece cuando se está creando un nuevo post"

#: includes/fields/class-acf-field-email.php:142
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:134
#: includes/fields/class-acf-field-text.php:151
#: includes/fields/class-acf-field-textarea.php:148
#: includes/fields/class-acf-field-url.php:123
msgid "Placeholder Text"
msgstr "Marcador de Texto"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:135
#: includes/fields/class-acf-field-text.php:152
#: includes/fields/class-acf-field-textarea.php:149
#: includes/fields/class-acf-field-url.php:124
msgid "Appears within the input"
msgstr "Aparece en el campo"

#: includes/fields/class-acf-field-email.php:151
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:143
#: includes/fields/class-acf-field-text.php:160
msgid "Prepend"
msgstr "Anteponer"

#: includes/fields/class-acf-field-email.php:152
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:144
#: includes/fields/class-acf-field-text.php:161
msgid "Appears before the input"
msgstr "Aparece antes del campo"

#: includes/fields/class-acf-field-email.php:160
#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-password.php:152
#: includes/fields/class-acf-field-text.php:169
msgid "Append"
msgstr "Anexar"

#: includes/fields/class-acf-field-email.php:161
#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-password.php:153
#: includes/fields/class-acf-field-text.php:170
msgid "Appears after the input"
msgstr "Aparece luego del campo"

#: includes/fields/class-acf-field-file.php:36
msgid "File"
msgstr "Archivo"

#: includes/fields/class-acf-field-file.php:47
msgid "Edit File"
msgstr "Editar Archivo"

#: includes/fields/class-acf-field-file.php:48
msgid "Update File"
msgstr "Actualizar Archivo"

#: includes/fields/class-acf-field-file.php:49
#: includes/fields/class-acf-field-image.php:54 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:55
msgid "Uploaded to this post"
msgstr "Subidos a este post"

#: includes/fields/class-acf-field-file.php:145
msgid "File name"
msgstr "Nombre del archivo"

#: includes/fields/class-acf-field-file.php:149
#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:266
#: includes/fields/class-acf-field-image.php:295
#: pro/fields/class-acf-field-gallery.php:705
#: pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "Tamaño de Archivo"

#: includes/fields/class-acf-field-file.php:174
msgid "Add File"
msgstr "Añadir archivo"

#: includes/fields/class-acf-field-file.php:225
msgid "File Array"
msgstr "Array de Archivo"

#: includes/fields/class-acf-field-file.php:226
msgid "File URL"
msgstr "URL de Archivo"

#: includes/fields/class-acf-field-file.php:227
msgid "File ID"
msgstr "ID de Archivo"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "Librería"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:232
#: pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "Limitar las opciones de la librería de medios"

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:237
#: includes/locations/class-acf-location-attachment.php:105
#: includes/locations/class-acf-location-comment.php:83
#: includes/locations/class-acf-location-nav-menu.php:106
#: includes/locations/class-acf-location-taxonomy.php:83
#: includes/locations/class-acf-location-user-form.php:91
#: includes/locations/class-acf-location-user-role.php:108
#: includes/locations/class-acf-location-widget.php:87
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:241
#: includes/fields/class-acf-field-image.php:238
#: pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "Subidos al post"

#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-image.php:245
#: pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:249
#: includes/fields/class-acf-field-file.php:260
msgid "Restrict which files can be uploaded"
msgstr "Restringir qué archivos pueden ser subidos"

#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:270
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "Tipos de archivos permitidos"

#: includes/fields/class-acf-field-file.php:271
#: includes/fields/class-acf-field-image.php:304
#: pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista separada por comas.  Deja en blanco para todos los tipos"

#: includes/fields/class-acf-field-google-map.php:36
msgid "Google Map"
msgstr "Mapa de Google"

#: includes/fields/class-acf-field-google-map.php:51
msgid "Locating"
msgstr "Ubicando"

#: includes/fields/class-acf-field-google-map.php:52
msgid "Sorry, this browser does not support geolocation"
msgstr "Disculpas, este navegador no soporta geolocalización"

#: includes/fields/class-acf-field-google-map.php:133
msgid "Clear location"
msgstr "Borrar ubicación"

#: includes/fields/class-acf-field-google-map.php:134
msgid "Find current location"
msgstr "Encontrar ubicación actual"

#: includes/fields/class-acf-field-google-map.php:137
msgid "Search for address..."
msgstr "Buscar dirección..."

#: includes/fields/class-acf-field-google-map.php:167
#: includes/fields/class-acf-field-google-map.php:178
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:168
#: includes/fields/class-acf-field-google-map.php:179
msgid "Center the initial map"
msgstr "Centrar inicialmente el mapa"

#: includes/fields/class-acf-field-google-map.php:190
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:191
msgid "Set the initial zoom level"
msgstr "Setear el nivel inicial de zoom"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-image.php:257
#: includes/fields/class-acf-field-image.php:286
#: includes/fields/class-acf-field-oembed.php:297
#: pro/fields/class-acf-field-gallery.php:696
#: pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:201
msgid "Customise the map height"
msgstr "Personalizar altura de mapa"

#: includes/fields/class-acf-field-group.php:36
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-group.php:469
#: pro/fields/class-acf-field-repeater.php:453
msgid "Sub Fields"
msgstr "Sub Campos"

#: includes/fields/class-acf-field-group.php:486
#: pro/fields/class-acf-field-clone.php:890
msgid "Specify the style used to render the selected fields"
msgstr ""
"Especifique el estilo utilizado para representar los campos seleccionados"

#: includes/fields/class-acf-field-group.php:491
#: pro/fields/class-acf-field-clone.php:895
#: pro/fields/class-acf-field-flexible-content.php:629
#: pro/fields/class-acf-field-repeater.php:522
msgid "Block"
msgstr "Bloque"

#: includes/fields/class-acf-field-group.php:492
#: pro/fields/class-acf-field-clone.php:896
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:521
msgid "Table"
msgstr "Tabla"

#: includes/fields/class-acf-field-group.php:493
#: pro/fields/class-acf-field-clone.php:897
#: pro/fields/class-acf-field-flexible-content.php:630
#: pro/fields/class-acf-field-repeater.php:523
msgid "Row"
msgstr "Fila"

#: includes/fields/class-acf-field-image.php:36
msgid "Image"
msgstr "Imagen"

#: includes/fields/class-acf-field-image.php:51
msgid "Select Image"
msgstr "Seleccionar Imagen"

#: includes/fields/class-acf-field-image.php:52
#: pro/fields/class-acf-field-gallery.php:53
msgid "Edit Image"
msgstr "Editar Imagen"

#: includes/fields/class-acf-field-image.php:53
#: pro/fields/class-acf-field-gallery.php:54
msgid "Update Image"
msgstr "Actualizar Imagen"

#: includes/fields/class-acf-field-image.php:55
msgid "All images"
msgstr "Todas las imágenes"

#: includes/fields/class-acf-field-image.php:142
#: includes/fields/class-acf-field-link.php:153 includes/input.php:267
#: pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "Remover"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "No hay ninguna imagen seleccionada"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "Añadir Imagen"

#: includes/fields/class-acf-field-image.php:212
msgid "Image Array"
msgstr "Array de Imagen"

#: includes/fields/class-acf-field-image.php:213
msgid "Image URL"
msgstr "URL de Imagen"

#: includes/fields/class-acf-field-image.php:214
msgid "Image ID"
msgstr "ID de Imagen"

#: includes/fields/class-acf-field-image.php:221
msgid "Preview Size"
msgstr "Tamaño del Preview"

#: includes/fields/class-acf-field-image.php:222
msgid "Shown when entering data"
msgstr "Mostrado cuando se ingresan datos"

#: includes/fields/class-acf-field-image.php:246
#: includes/fields/class-acf-field-image.php:275
#: pro/fields/class-acf-field-gallery.php:685
#: pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "Restringir cuáles imágenes pueden ser subidas"

#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:278
#: includes/fields/class-acf-field-oembed.php:286
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "Ancho"

#: includes/fields/class-acf-field-link.php:36
msgid "Link"
msgstr "Enlace"

#: includes/fields/class-acf-field-link.php:146
msgid "Select Link"
msgstr "Elige el enlace"

#: includes/fields/class-acf-field-link.php:151
msgid "Opens in a new window/tab"
msgstr "Abrir en una nueva ventana/pestaña"

#: includes/fields/class-acf-field-link.php:186
msgid "Link Array"
msgstr "Matriz de enlace"

#: includes/fields/class-acf-field-link.php:187
msgid "Link URL"
msgstr "URL del enlace"

#: includes/fields/class-acf-field-message.php:36
#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-true_false.php:141
msgid "Message"
msgstr "Mensaje"

#: includes/fields/class-acf-field-message.php:124
#: includes/fields/class-acf-field-textarea.php:176
msgid "New Lines"
msgstr "Nuevas Líneas"

#: includes/fields/class-acf-field-message.php:125
#: includes/fields/class-acf-field-textarea.php:177
msgid "Controls how new lines are rendered"
msgstr "Controla cómo se muestran las nuevas líneas"

#: includes/fields/class-acf-field-message.php:129
#: includes/fields/class-acf-field-textarea.php:181
msgid "Automatically add paragraphs"
msgstr "Agregar párrafos automáticamente"

#: includes/fields/class-acf-field-message.php:130
#: includes/fields/class-acf-field-textarea.php:182
msgid "Automatically add &lt;br&gt;"
msgstr "Agregar &lt;br&gt; automáticamente"

#: includes/fields/class-acf-field-message.php:131
#: includes/fields/class-acf-field-textarea.php:183
msgid "No Formatting"
msgstr "Sin Formato"

#: includes/fields/class-acf-field-message.php:138
msgid "Escape HTML"
msgstr "Escapar HTML"

#: includes/fields/class-acf-field-message.php:139
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permitir que el maquetado HTML se muestre como texto visible en vez de "
"interpretarlo"

#: includes/fields/class-acf-field-number.php:36
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-number.php:181
msgid "Minimum Value"
msgstr "Valor Mínimo"

#: includes/fields/class-acf-field-number.php:190
msgid "Maximum Value"
msgstr "Valor Máximo"

#: includes/fields/class-acf-field-number.php:199
msgid "Step Size"
msgstr "Tamaño del Paso"

#: includes/fields/class-acf-field-number.php:237
msgid "Value must be a number"
msgstr "El valor debe ser un número"

#: includes/fields/class-acf-field-number.php:255
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "El valor debe ser mayor o igual a %d"

#: includes/fields/class-acf-field-number.php:263
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "El valor debe ser menor o igual a %d"

#: includes/fields/class-acf-field-oembed.php:36
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:237
msgid "Enter URL"
msgstr "Ingresa URL"

#: includes/fields/class-acf-field-oembed.php:250
#: includes/fields/class-acf-field-taxonomy.php:904
msgid "Error."
msgstr "Error."

#: includes/fields/class-acf-field-oembed.php:250
msgid "No embed found for the given URL."
msgstr "No se encontró embed para la URL proporcionada."

#: includes/fields/class-acf-field-oembed.php:283
#: includes/fields/class-acf-field-oembed.php:294
msgid "Embed Size"
msgstr "Tamaño del Embed"

#: includes/fields/class-acf-field-page_link.php:192
msgid "Archives"
msgstr "Archivos"

#: includes/fields/class-acf-field-page_link.php:500
#: includes/fields/class-acf-field-post_object.php:399
#: includes/fields/class-acf-field-relationship.php:704
msgid "Filter by Post Type"
msgstr "Filtrar por Tipo de Post"

#: includes/fields/class-acf-field-page_link.php:508
#: includes/fields/class-acf-field-post_object.php:407
#: includes/fields/class-acf-field-relationship.php:712
msgid "All post types"
msgstr "Todos los Tipos de Post"

#: includes/fields/class-acf-field-page_link.php:514
#: includes/fields/class-acf-field-post_object.php:413
#: includes/fields/class-acf-field-relationship.php:718
msgid "Filter by Taxonomy"
msgstr "Filtrar por Taxonomía"

#: includes/fields/class-acf-field-page_link.php:522
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-relationship.php:726
msgid "All taxonomies"
msgstr "Todas las taxonomías"

#: includes/fields/class-acf-field-page_link.php:528
#: includes/fields/class-acf-field-post_object.php:427
#: includes/fields/class-acf-field-radio.php:259
#: includes/fields/class-acf-field-select.php:484
#: includes/fields/class-acf-field-taxonomy.php:799
#: includes/fields/class-acf-field-user.php:423
msgid "Allow Null?"
msgstr "Permitir Vacío?"

#: includes/fields/class-acf-field-page_link.php:538
msgid "Allow Archives URLs"
msgstr "Permitir las URLs de los archivos"

#: includes/fields/class-acf-field-page_link.php:548
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-select.php:494
#: includes/fields/class-acf-field-user.php:433
msgid "Select multiple values?"
msgstr "¿Seleccionar valores múltiples?"

#: includes/fields/class-acf-field-password.php:36
msgid "Password"
msgstr "Contraseña"

#: includes/fields/class-acf-field-post_object.php:36
#: includes/fields/class-acf-field-post_object.php:452
#: includes/fields/class-acf-field-relationship.php:783
msgid "Post Object"
msgstr "Objecto Post"

#: includes/fields/class-acf-field-post_object.php:453
#: includes/fields/class-acf-field-relationship.php:784
msgid "Post ID"
msgstr "ID de Post"

#: includes/fields/class-acf-field-radio.php:36
msgid "Radio Button"
msgstr "Radio Button"

#: includes/fields/class-acf-field-radio.php:269
msgid "Other"
msgstr "Otro"

#: includes/fields/class-acf-field-radio.php:274
msgid "Add 'other' choice to allow for custom values"
msgstr "Agregar la opción 'otros' para permitir valores personalizados"

#: includes/fields/class-acf-field-radio.php:280
msgid "Save Other"
msgstr "Guardar Otro"

#: includes/fields/class-acf-field-radio.php:285
msgid "Save 'other' values to the field's choices"
msgstr "Guardar 'otros' valores a las opciones del campo"

#: includes/fields/class-acf-field-relationship.php:36
msgid "Relationship"
msgstr "Relación"

#: includes/fields/class-acf-field-relationship.php:48
msgid "Minimum values reached ( {min} values )"
msgstr "Valores mínimos alcanzados ( {min} valores )"

#: includes/fields/class-acf-field-relationship.php:49
msgid "Maximum values reached ( {max} values )"
msgstr "Valores máximos alcanzados ( {max} valores )"

#: includes/fields/class-acf-field-relationship.php:50
msgid "Loading"
msgstr "Cargando"

#: includes/fields/class-acf-field-relationship.php:51
msgid "No matches found"
msgstr "No se encontraron resultados"

#: includes/fields/class-acf-field-relationship.php:585
msgid "Search..."
msgstr "Buscar..."

#: includes/fields/class-acf-field-relationship.php:594
msgid "Select post type"
msgstr "Selecciona Tipo de Post"

#: includes/fields/class-acf-field-relationship.php:607
msgid "Select taxonomy"
msgstr "Selecciona Taxonomía"

#: includes/fields/class-acf-field-relationship.php:732
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-relationship.php:738
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Post Type"

#: includes/fields/class-acf-field-relationship.php:739
#: includes/fields/class-acf-field-taxonomy.php:36
#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Taxonomy"
msgstr "Taxonomía"

#: includes/fields/class-acf-field-relationship.php:746
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:747
msgid "Selected elements will be displayed in each result"
msgstr "Los elementos seleccionados serán mostrados en cada resultado"

#: includes/fields/class-acf-field-relationship.php:758
msgid "Minimum posts"
msgstr "Mínimos posts"

#: includes/fields/class-acf-field-relationship.php:767
msgid "Maximum posts"
msgstr "Máximos posts"

#: includes/fields/class-acf-field-relationship.php:871
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s requiere al menos %s selección"
msgstr[1] "%s requiere al menos %s selecciones"

#: includes/fields/class-acf-field-select.php:36
#: includes/fields/class-acf-field-taxonomy.php:791
msgctxt "noun"
msgid "Select"
msgstr "Elige"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Hay un resultado disponible, pulse Enter para seleccionarlo."

#: includes/fields/class-acf-field-select.php:50
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultados disponibles, utilice las flechas arriba y abajo para navegar "
"por los resultados."

#: includes/fields/class-acf-field-select.php:51
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No se encontraron coincidencias"

#: includes/fields/class-acf-field-select.php:52
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Por favor, introduce 1 o más caracteres"

#: includes/fields/class-acf-field-select.php:53
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Por favor escribe %d o más caracteres"

#: includes/fields/class-acf-field-select.php:54
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Por favor, borra 1 carácter"

#: includes/fields/class-acf-field-select.php:55
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Por favor, elimina %d caracteres"

#: includes/fields/class-acf-field-select.php:56
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Sólo puede seleccionar 1 elemento"

#: includes/fields/class-acf-field-select.php:57
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Sólo puede seleccionar %d elementos"

#: includes/fields/class-acf-field-select.php:58
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Cargando más resultados&hellip;"

#: includes/fields/class-acf-field-select.php:59
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Buscando&hellip;"

#: includes/fields/class-acf-field-select.php:60
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Error al cargar"

#: includes/fields/class-acf-field-select.php:270 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Elige"

#: includes/fields/class-acf-field-select.php:504
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "UI estilizada"

#: includes/fields/class-acf-field-select.php:514
msgid "Use AJAX to lazy load choices?"
msgstr "Usar AJAX para hacer lazy load de las opciones?"

#: includes/fields/class-acf-field-select.php:525
msgid "Specify the value returned"
msgstr "Especifique el valor devuelto"

#: includes/fields/class-acf-field-separator.php:36
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-tab.php:36
msgid "Tab"
msgstr "Pestaña"

#: includes/fields/class-acf-field-tab.php:96
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""
"El campo pestaña se visualizará incorrectamente cuando sea agregado a un "
"campo de repetición con estilo Tabla o a un layout de contenido flexible"

#: includes/fields/class-acf-field-tab.php:97
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr ""
"Usa \"Campos Pestaña\" para organizar mejor tu pantalla de edición agrupando "
"campos."

#: includes/fields/class-acf-field-tab.php:98
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"Todos los campos que siguen de este \"campo pestaña\" (o hasta que otro "
"\"campo pestaña\" sea definido) serán agrepados la etiqueta de este campo "
"como título de la pestaña."

#: includes/fields/class-acf-field-tab.php:112
msgid "Placement"
msgstr "Ubicación"

#: includes/fields/class-acf-field-tab.php:124
msgid "End-point"
msgstr "Punto de Terminación"

#: includes/fields/class-acf-field-tab.php:125
msgid "Use this field as an end-point and start a new group of tabs"
msgstr ""
"Usar este campo como un punto de terminación y comenzar un nuevo grupo de "
"pestañas"

#: includes/fields/class-acf-field-taxonomy.php:719
#: includes/fields/class-acf-field-true_false.php:95
#: includes/fields/class-acf-field-true_false.php:184 includes/input.php:266
#: pro/admin/views/html-settings-updates.php:103
msgid "No"
msgstr "No"

#: includes/fields/class-acf-field-taxonomy.php:738
msgid "None"
msgstr "Ninguno"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Select the taxonomy to be displayed"
msgstr "Selecciona taxonomía a ser mostrada"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Appearance"
msgstr "Apariencia"

#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Select the appearance of this field"
msgstr "Selecciona la apariencia de este campo"

#: includes/fields/class-acf-field-taxonomy.php:785
msgid "Multiple Values"
msgstr "Múltiples Valores"

#: includes/fields/class-acf-field-taxonomy.php:787
msgid "Multi Select"
msgstr "Selección Múltiple"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Single Value"
msgstr "Valor Individual"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Radio Buttons"
msgstr "Botones Radio"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Create Terms"
msgstr "Crear Términos"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Allow new terms to be created whilst editing"
msgstr "Permitir la creación de nuevos términos mientras se edita"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Save Terms"
msgstr "Guardar Términos"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Connect selected terms to the post"
msgstr "Conectar los términos seleccionados al post"

#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Load Terms"
msgstr "Cargar Términos"

#: includes/fields/class-acf-field-taxonomy.php:830
msgid "Load value from posts terms"
msgstr "Cargar valor de los términos del post"

#: includes/fields/class-acf-field-taxonomy.php:844
msgid "Term Object"
msgstr "Objeto de Término"

#: includes/fields/class-acf-field-taxonomy.php:845
msgid "Term ID"
msgstr "ID de Término"

#: includes/fields/class-acf-field-taxonomy.php:904
#, php-format
msgid "User unable to add new %s"
msgstr "El usuario no puede agregar nuevos %s"

#: includes/fields/class-acf-field-taxonomy.php:917
#, php-format
msgid "%s already exists"
msgstr "%s ya existe"

#: includes/fields/class-acf-field-taxonomy.php:958
#, php-format
msgid "%s added"
msgstr "%s agregados"

#: includes/fields/class-acf-field-taxonomy.php:1003
msgid "Add"
msgstr "Agregar"

#: includes/fields/class-acf-field-text.php:36
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-text.php:178
#: includes/fields/class-acf-field-textarea.php:157
msgid "Character Limit"
msgstr "Límite de Caractéres"

#: includes/fields/class-acf-field-text.php:179
#: includes/fields/class-acf-field-textarea.php:158
msgid "Leave blank for no limit"
msgstr "Deja en blanco para ilimitado"

#: includes/fields/class-acf-field-textarea.php:36
msgid "Text Area"
msgstr "Area de Texto"

#: includes/fields/class-acf-field-textarea.php:166
msgid "Rows"
msgstr "Filas"

#: includes/fields/class-acf-field-textarea.php:167
msgid "Sets the textarea height"
msgstr "Setea el alto del área de texto"

#: includes/fields/class-acf-field-time_picker.php:36
msgid "Time Picker"
msgstr "Selector de hora"

#: includes/fields/class-acf-field-true_false.php:36
msgid "True / False"
msgstr "Verdadero / Falso"

#: includes/fields/class-acf-field-true_false.php:94
#: includes/fields/class-acf-field-true_false.php:174 includes/input.php:265
#: pro/admin/views/html-settings-updates.php:93
msgid "Yes"
msgstr "Sí"

#: includes/fields/class-acf-field-true_false.php:142
msgid "Displays text alongside the checkbox"
msgstr "Muestra el texto junto a la casilla de verificación"

#: includes/fields/class-acf-field-true_false.php:170
msgid "On Text"
msgstr "Sobre texto"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when active"
msgstr "Texto mostrado cuando está activo"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Sin texto"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Texto mostrado cuando está inactivo"

#: includes/fields/class-acf-field-url.php:36
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:165
msgid "Value must be a valid URL"
msgstr "El valor debe ser una URL válida"

#: includes/fields/class-acf-field-user.php:36 includes/locations.php:95
msgid "User"
msgstr "Usuario"

#: includes/fields/class-acf-field-user.php:408
msgid "Filter by role"
msgstr "Filtrar por rol"

#: includes/fields/class-acf-field-user.php:416
msgid "All user roles"
msgstr "Todos los roles de usuario"

#: includes/fields/class-acf-field-wysiwyg.php:36
msgid "Wysiwyg Editor"
msgstr "Editor Wysiwyg"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:386
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-wysiwyg.php:392
msgid "Click to initialize TinyMCE"
msgstr "Haz clic para iniciar TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:445
msgid "Tabs"
msgstr "Pestañas"

#: includes/fields/class-acf-field-wysiwyg.php:450
msgid "Visual & Text"
msgstr "Visual y Texto"

#: includes/fields/class-acf-field-wysiwyg.php:451
msgid "Visual Only"
msgstr "Sólo Visual"

#: includes/fields/class-acf-field-wysiwyg.php:452
msgid "Text Only"
msgstr "Sólo Texto"

#: includes/fields/class-acf-field-wysiwyg.php:459
msgid "Toolbar"
msgstr "Barra de Herramientas"

#: includes/fields/class-acf-field-wysiwyg.php:469
msgid "Show Media Upload Buttons?"
msgstr "¿Mostrar el botón Media Upload?"

#: includes/fields/class-acf-field-wysiwyg.php:479
msgid "Delay initialization?"
msgstr "¿Inicialización retrasada?"

#: includes/fields/class-acf-field-wysiwyg.php:480
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE no se iniciará hasta que se haga clic en el campo"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:304
msgid "Edit field group"
msgstr "Editar grupo de campos"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validar correo electrónico"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:588 pro/options-page.php:81
msgid "Update"
msgstr "Actualizar"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Post actualizado"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr "Spam detectado"

#: includes/input.php:258
msgid "Expand Details"
msgstr "Expandir Detalles"

#: includes/input.php:259
msgid "Collapse Details"
msgstr "Colapsar Detalles"

#: includes/input.php:260
msgid "Validation successful"
msgstr "Validación exitosa"

#: includes/input.php:261 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Validación fallida"

#: includes/input.php:262
msgid "1 field requires attention"
msgstr "1 campo requiere atención"

#: includes/input.php:263
#, php-format
msgid "%d fields require attention"
msgstr "%d campos requieren atención"

#: includes/input.php:264
msgid "Restricted"
msgstr "Restringido"

#: includes/input.php:268
msgid "Cancel"
msgstr "Cancelar"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Post"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Página"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formularios"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Adjunto"

#: includes/locations/class-acf-location-attachment.php:113
#, php-format
msgid "All %s formats"
msgstr "%s formatos"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Comentario"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Rol del Usuario Actual"

#: includes/locations/class-acf-location-current-user-role.php:114
msgid "Super Admin"
msgstr "Super Administrador"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Usuario Actual"

#: includes/locations/class-acf-location-current-user.php:101
msgid "Logged in"
msgstr "Logueado"

#: includes/locations/class-acf-location-current-user.php:102
msgid "Viewing front end"
msgstr "Viendo front end"

#: includes/locations/class-acf-location-current-user.php:103
msgid "Viewing back end"
msgstr "Viendo back end"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Elemento del menú"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menú"

#: includes/locations/class-acf-location-nav-menu.php:113
msgid "Menu Locations"
msgstr "Localizaciones de menú"

#: includes/locations/class-acf-location-nav-menu.php:123
msgid "Menus"
msgstr "Menús"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Página Superior"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Plantilla de Página"

#: includes/locations/class-acf-location-page-template.php:102
#: includes/locations/class-acf-location-post-template.php:156
msgid "Default Template"
msgstr "Plantilla por Defecto"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Tipo de Página"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Front Page"
msgstr "Página Principal"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Posts Page"
msgstr "Página de Entradas"

#: includes/locations/class-acf-location-page-type.php:151
msgid "Top Level Page (no parent)"
msgstr "Página de Nivel Superior"

#: includes/locations/class-acf-location-page-type.php:152
msgid "Parent Page (has children)"
msgstr "Página Superior (tiene hijas)"

#: includes/locations/class-acf-location-page-type.php:153
msgid "Child Page (has parent)"
msgstr "Página hija (tiene superior)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Categoría de Post"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Formato de Post"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Estado del Post"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taxonomía de Post"

#: includes/locations/class-acf-location-post-template.php:29
msgid "Post Template"
msgstr "Plantilla de entrada:"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Término de Taxonomía"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Formulario de Usuario"

#: includes/locations/class-acf-location-user-form.php:92
msgid "Add / Edit"
msgstr "Agregar / Editar"

#: includes/locations/class-acf-location-user-form.php:93
msgid "Register"
msgstr "Registrar"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Rol de Usuario"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Actualizar"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "El valor %s es requerido"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:196
msgid "Publish"
msgstr "Publicar"

#: pro/admin/admin-options-page.php:202
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"No se encontraron grupos de campos personalizados para esta página de "
"opciones. <a href=\"%s\">Crear Grupo de Campos Personalizados</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""
"<b>Error</b>. No se ha podido conectar con el servidor de actualización"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:17
msgid "Updates"
msgstr "Actualizaciones"

#: pro/admin/views/html-settings-updates.php:11
msgid "Deactivate License"
msgstr "Desactivar Licencia"

#: pro/admin/views/html-settings-updates.php:11
msgid "Activate License"
msgstr "Activar Licencia"

#: pro/admin/views/html-settings-updates.php:21
msgid "License Information"
msgstr "Información de la licencia"

#: pro/admin/views/html-settings-updates.php:24
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Para desbloquear las actualizaciones, por favor a continuación introduce tu "
"clave de licencia. Si no tienes una clave de licencia, consulta <a href=\"%s"
"\" target=\"_blank\">detalles y precios</a>."

#: pro/admin/views/html-settings-updates.php:33
msgid "License Key"
msgstr "Clave de Licencia"

#: pro/admin/views/html-settings-updates.php:65
msgid "Update Information"
msgstr "Información de Actualización"

#: pro/admin/views/html-settings-updates.php:72
msgid "Current Version"
msgstr "Versión Actual"

#: pro/admin/views/html-settings-updates.php:80
msgid "Latest Version"
msgstr "Última Versión"

#: pro/admin/views/html-settings-updates.php:88
msgid "Update Available"
msgstr "Actualización Disponible"

#: pro/admin/views/html-settings-updates.php:96
msgid "Update Plugin"
msgstr "Actualizar Plugin"

#: pro/admin/views/html-settings-updates.php:98
msgid "Please enter your license key above to unlock updates"
msgstr "Por favor ingresa tu clave de licencia para habilitar actualizaciones"

#: pro/admin/views/html-settings-updates.php:104
msgid "Check Again"
msgstr "Chequear nuevamente"

#: pro/admin/views/html-settings-updates.php:121
msgid "Upgrade Notice"
msgstr "Notificación de Actualización"

#: pro/fields/class-acf-field-clone.php:36
msgctxt "noun"
msgid "Clone"
msgstr "Clonar"

#: pro/fields/class-acf-field-clone.php:858
msgid "Select one or more fields you wish to clone"
msgstr "Elige uno o más campos que quieras clonar"

#: pro/fields/class-acf-field-clone.php:875
msgid "Display"
msgstr "Mostrar"

#: pro/fields/class-acf-field-clone.php:876
msgid "Specify the style used to render the clone field"
msgstr "Especifique el estilo utilizado para procesar el campo de clonación"

#: pro/fields/class-acf-field-clone.php:881
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Grupo (muestra los campos seleccionados en un grupo dentro de este campo)"

#: pro/fields/class-acf-field-clone.php:882
msgid "Seamless (replaces this field with selected fields)"
msgstr "Transparente (reemplaza este campo con los campos seleccionados)"

#: pro/fields/class-acf-field-clone.php:903
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Las etiquetas se mostrarán como %s"

#: pro/fields/class-acf-field-clone.php:906
msgid "Prefix Field Labels"
msgstr "Etiquetas del prefijo de campo"

#: pro/fields/class-acf-field-clone.php:917
#, php-format
msgid "Values will be saved as %s"
msgstr "Los valores se guardarán como %s"

#: pro/fields/class-acf-field-clone.php:920
msgid "Prefix Field Names"
msgstr "Nombres de prefijos de campos"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field"
msgstr "Campo desconocido"

#: pro/fields/class-acf-field-clone.php:1077
msgid "Unknown field group"
msgstr "Grupo de campos desconocido"

#: pro/fields/class-acf-field-clone.php:1081
#, php-format
msgid "All fields from %s field group"
msgstr "Todos los campos del grupo de campo %s"

#: pro/fields/class-acf-field-flexible-content.php:42
#: pro/fields/class-acf-field-repeater.php:230
#: pro/fields/class-acf-field-repeater.php:534
msgid "Add Row"
msgstr "Agregar Fila"

#: pro/fields/class-acf-field-flexible-content.php:45
msgid "layout"
msgstr "esquema"

#: pro/fields/class-acf-field-flexible-content.php:46
msgid "layouts"
msgstr "esquemas"

#: pro/fields/class-acf-field-flexible-content.php:47
msgid "remove {layout}?"
msgstr "remover {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:48
msgid "This field requires at least {min} {identifier}"
msgstr "Este campo requiere al menos {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:49
msgid "This field has a limit of {max} {identifier}"
msgstr "Este campo tiene un límite de  {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:50
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Este campo requiere al menos {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:51
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Límite máximo de {label} alcanzado. ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:52
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponible (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:53
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} requerido (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:54
msgid "Flexible Content requires at least 1 layout"
msgstr "El Contenido Flexible requiere por lo menos 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:288
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Haz click en el botón \"%s\" debajo para comenzar a crear tu esquema"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Agregar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Remove layout"
msgstr "Remover esquema"

#: pro/fields/class-acf-field-flexible-content.php:425
#: pro/fields/class-acf-field-repeater.php:360
msgid "Click to toggle"
msgstr "Clic para mostrar"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder Layout"
msgstr "Reordenar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder"
msgstr "Reordenar"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete Layout"
msgstr "Eliminar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate Layout"
msgstr "Duplicar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New Layout"
msgstr "Agregar Nuevo Esquema"

#: pro/fields/class-acf-field-flexible-content.php:645
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:658
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:685
#: pro/fields/class-acf-field-repeater.php:530
msgid "Button Label"
msgstr "Etiqueta del Botón"

#: pro/fields/class-acf-field-flexible-content.php:694
msgid "Minimum Layouts"
msgstr "Esquemas Mínimos"

#: pro/fields/class-acf-field-flexible-content.php:703
msgid "Maximum Layouts"
msgstr "Esquemas Máximos"

#: pro/fields/class-acf-field-gallery.php:52
msgid "Add Image to Gallery"
msgstr "Agregar Imagen a Galería"

#: pro/fields/class-acf-field-gallery.php:56
msgid "Maximum selection reached"
msgstr "Selección máxima alcanzada"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Longitud"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "Leyenda"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "Texto Alt"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Agregar a galería"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Acciones en lote"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "Ordenar por fecha de subida"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "Ordenar por fecha de modificación"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "Ordenar por título"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Invertir orden actual"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Cerrar"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Selección Mínima"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Selección Máxima"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "Insertar"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "Especificar dónde se agregan nuevos adjuntos"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "Añadir al final"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "Adelantar hasta el principio"

#: pro/fields/class-acf-field-repeater.php:47
msgid "Minimum rows reached ({min} rows)"
msgstr "Mínimo de filas alcanzado ({min} rows)"

#: pro/fields/class-acf-field-repeater.php:48
msgid "Maximum rows reached ({max} rows)"
msgstr "Máximo de filas alcanzado ({max} rows)"

#: pro/fields/class-acf-field-repeater.php:405
msgid "Add row"
msgstr "Agregar fila"

#: pro/fields/class-acf-field-repeater.php:406
msgid "Remove row"
msgstr "Remover fila"

#: pro/fields/class-acf-field-repeater.php:483
msgid "Collapsed"
msgstr "Colapsado"

#: pro/fields/class-acf-field-repeater.php:484
msgid "Select a sub field to show when row is collapsed"
msgstr "Elige un subcampo para indicar cuándo se colapsa la fila"

#: pro/fields/class-acf-field-repeater.php:494
msgid "Minimum Rows"
msgstr "Mínimo de Filas"

#: pro/fields/class-acf-field-repeater.php:504
msgid "Maximum Rows"
msgstr "Máximo de Filas"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "No existen páginas de opciones"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opciones"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Opciones Actualizadas"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Para habilitar actualizaciones, por favor, introduzca su llave de licencia "
"en la <a href=\"%s\">página de actualizaciones</a>. Si no tiene una llave de "
"licencia, por favor, consulta <a href=\"%s\">detalles y precios</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Disabled"
#~ msgstr "Deshabilitado"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Deshabilitado <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Deshabilitados <span class=\"count\">(%s)</span>"

#~ msgid "See what's new in"
#~ msgstr "Que hay de nuevo en"

#~ msgid "version"
#~ msgstr "versión"

#~ msgid "Getting Started"
#~ msgstr "Comenzando"

#~ msgid "Field Types"
#~ msgstr "Tipos de Campos"

#~ msgid "Functions"
#~ msgstr "Funciones"

#~ msgid "Actions"
#~ msgstr "Acciones"

#~ msgid "'How to' guides"
#~ msgstr "Guías 'Cómo hacer'"

#~ msgid "Tutorials"
#~ msgstr "Tutoriales"

#~ msgid "Created by"
#~ msgstr "Creado por"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr ""
#~ "<b>Perfecto</b>. La herramienta de importación agregó %s grupos de "
#~ "campos: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Alerta</b>. La herramienta de importación detectó que %s grupos de "
#~ "campos ya existen y han sido ignorados: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Actualizar ACF"

#~ msgid "Upgrade"
#~ msgstr "Actualizar"

#~ msgid "Error"
#~ msgstr "Error"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Arrastra y suelta para reordenar"

#~ msgid "Upgrading data to"
#~ msgstr "Actualizando datos a"

#~ msgid "See what's new"
#~ msgstr "Mira qué hay de nuevo"

#~ msgid "Show a different month"
#~ msgstr "Mostrar un mes diferente"

#~ msgid "Return format"
#~ msgstr "Formato de Retorno"

#~ msgid "uploaded to this post"
#~ msgstr "subidos a este post"

#~ msgid "File Size"
#~ msgstr "Tamaño de Archivo"

#~ msgid "No File selected"
#~ msgstr "No hay ningún archivo seleccionado"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Por favor toma en cuenta que todo el texto será pasado primero por la "
#~ "función wp"

#~ msgid "Warning"
#~ msgstr "Alerta"

#~ msgid "Add new %s "
#~ msgstr "Agregar nuevo %s"

#~ msgid "eg. Show extra content"
#~ msgstr "ej. Mostrar contenido extra"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Error de Conección</b>. Disculpa, por favor intenta nuevamente"

#~ msgid "Save Options"
#~ msgstr "Guardar Opciones"

#~ msgid "License"
#~ msgstr "Licencia"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Para desbloquear las actualizaciones, por favor ingresa tu clabe de "
#~ "licencia debajo.  Si no tienes una clave de licencia, por favor mira"

#~ msgid "details & pricing"
#~ msgstr "detalles y precios"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Fallo en la validación. Uno o más campos son requeridos."

#~ msgid "Error: Field Type does not exist!"
#~ msgstr "Error: El tipo de campo no existe!"

#~ msgid "No ACF groups selected"
#~ msgstr "No hay grupos de ACF seleccionados"

#~ msgid "Field Order"
#~ msgstr "Orden de los campos"

#~ msgid "Docs"
#~ msgstr "Docs"

#~ msgid "Field Instructions"
#~ msgstr "Instrucciones del campo"

#~ msgid "Save Field"
#~ msgstr "Guardar Field"

#~ msgid "Hide this edit screen"
#~ msgstr "Ocultar esta pantalla de edición"

#~ msgid "continue editing ACF"
#~ msgstr "continuar editando ACF"

#~ msgid "match"
#~ msgstr "coincide"

#~ msgid "of the above"
#~ msgstr "de los superiores"

#~ msgid "Field groups are created in order <br />from lowest to highest."
#~ msgstr "Los Field Groups son creados en orden <br /> de menor a mayor."

#~ msgid "Show on page"
#~ msgstr "Mostrar en página"

#~ msgid "Deselect items to hide them on the edit page"
#~ msgstr "Deselecciona items para esconderlos en la página de edición"

#~ msgid ""
#~ "If multiple ACF groups appear on an edit page, the first ACF group's "
#~ "options will be used. The first ACF group is the one with the lowest "
#~ "order number."
#~ msgstr ""
#~ "Si aparecen multiples grupos de ACF en una página de edición, se usarán "
#~ "las opciones del primer grupo. Se considera primer grupo de ACF al que "
#~ "cuenta con el número de orden más bajo."

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "Lee la documentación, aprende sobre las funciones y encuentra algunos "
#~ "trucos y consejos para tu siguiente proyecto web."

#~ msgid "View the ACF website"
#~ msgstr "Ver la web de ACF"

#~ msgid "Vote"
#~ msgstr "Vota"

#~ msgid "Follow"
#~ msgstr "Sígueme"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "Ajustes de Advanced Custom Fields"

#~ msgid "Activate Add-ons."
#~ msgstr "Activar Add-ons."

#~ msgid "Activation Code"
#~ msgstr "Código de activación"

#~ msgid "Repeater Field"
#~ msgstr "Repeater Field"

#~ msgid "Flexible Content Field"
#~ msgstr "Flexible Content Field"

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr ""
#~ "Las Add-ons pueden desbloquearse comprando una clave de licencia. Cada "
#~ "clave puede usarse en multiple sites."

#~ msgid "Find Add-ons"
#~ msgstr "Buscar Add-ons"

#~ msgid "Export Field Groups to XML"
#~ msgstr "Exportar Field Groups a XML"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "ACF creará un archivo .xml que es compatible con el plugin de importación "
#~ "nativo de WP."

#~ msgid "Export XML"
#~ msgstr "Exportar XML"

#~ msgid "Navigate to the"
#~ msgstr "Navegar a"

#~ msgid "Import Tool"
#~ msgstr "Utilidad de importación"

#~ msgid "and select WordPress"
#~ msgstr "y selecciona WordPress"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Instalar el plugin de importación de WP si se pide"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Subir e importar tu archivo .xml exportado"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Selecciona tu usuario e ignora Import Attachments"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "¡Eso es todo! Feliz WordPressing"

#~ msgid "ACF will create the PHP code to include in your theme"
#~ msgstr "ACF creará el código PHP para incluir en tu tema"

#~ msgid "Create PHP"
#~ msgstr "Crear PHP"

#~ msgid "Register Field Groups with PHP"
#~ msgstr "Registrar Field Groups con PHP"

#~ msgid "Copy the PHP code generated"
#~ msgstr "Copia el código PHP generado"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Pegalo en tu archivo functions.php"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "Para activar cualquier Add-on, edita y usa el código en las primeras "
#~ "pocas lineas."

#~ msgid "Back to settings"
#~ msgstr "Volver a los ajustes"

#~ msgid ""
#~ "/**\n"
#~ " * Activate Add-ons\n"
#~ " * Here you can enter your activation codes to unlock Add-ons to use in "
#~ "your theme. \n"
#~ " * Since all activation codes are multi-site licenses, you are allowed to "
#~ "include your key in premium themes. \n"
#~ " * Use the commented out code to update the database with your activation "
#~ "code. \n"
#~ " * You may place this code inside an IF statement that only runs on theme "
#~ "activation.\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Activar Add-ons\n"
#~ " * Aquí puedes introducir tus códigos de activación para desbloquear Add-"
#~ "ons y utilizarlos en tu tema. \n"
#~ " * Ya que todos los códigos de activación tiene licencia multi-site, se "
#~ "te permite incluir tu clave en temas premium. \n"
#~ " * Utiliza el código comentado para actualizar la base de datos con tu "
#~ "código de activación. \n"
#~ " * Puedes colocar este código dentro de una instrucción IF para que sólo "
#~ "funcione en la activación del tema.\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " * Register field groups\n"
#~ " * The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " * You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " * This code must run every time the functions.php file is read\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Registrar field groups\n"
#~ " * La función register_field_group acepta un 1 array que contiene los "
#~ "datos pertinentes para registrar un Field Group\n"
#~ " * Puedes editar el array como mejor te parezca. Sin embargo, esto puede "
#~ "dar lugar a errores si la matriz no es compatible con ACF\n"
#~ " * Este código debe ejecutarse cada vez que se lee el archivo functions."
#~ "php\n"
#~ " */"

#~ msgid "No field groups were selected"
#~ msgstr "No hay ningún Field Group seleccionado"

#~ msgid "No choices to choose from"
#~ msgstr "No hay opciones para escojer"

#~ msgid ""
#~ "Enter your choices one per line<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tRed<br />\n"
#~ "\t\t\t\tBlue<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tor<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tred : Red<br />\n"
#~ "\t\t\t\tblue : Blue"
#~ msgstr ""
#~ "Introduce tus opciones, una por línea<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tRojo<br />\n"
#~ "\t\t\t\tAzul<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\to<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tred : Rojo<br />\n"
#~ "\t\t\t\tblue : Azul"

#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "ej. dd/mm/yy. leer más sobre"

#~ msgid "Remove File"
#~ msgstr "Eliminar Archivo"

#~ msgid "Click the \"add row\" button below to start creating your layout"
#~ msgstr ""
#~ "Haz click sobre el botón \"añadir fila\" para empezar a crear tu Layout"

#~ msgid "+ Add Row"
#~ msgstr "+ Añadir fila"

#~ msgid ""
#~ "No fields. Click the \"+ Add Field button\" to create your first field."
#~ msgstr ""
#~ "No hay campos. Haz click en el botón \"+ Añadir Campo\" para crear tu "
#~ "primer campo."

#~ msgid ""
#~ "Filter posts by selecting a post type<br />\n"
#~ "\t\t\t\tTip: deselect all post types to show all post type's posts"
#~ msgstr ""
#~ "Filtrar posts seleccionando un post type<br />\n"
#~ "\t\t\t\tConsejo: deselecciona todos los post type para mostrar todos los "
#~ "tipos de post"

#~ msgid "Filter from Taxonomy"
#~ msgstr "Filtrar por Taxonomía"

#~ msgid "Set to -1 for inifinit"
#~ msgstr "Se establece en -1 para inifinito"

#~ msgid "Repeater Fields"
#~ msgstr "Repeater Fields"

#~ msgid "Row Limit"
#~ msgstr "Limite de filas"

#~ msgid "Formatting"
#~ msgstr "Formato"

#~ msgid "Define how to render html tags"
#~ msgstr "Define como renderizar las etiquetas html"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Define como renderizar los tags html / nuevas lineas"

#~ msgid "Save"
#~ msgstr "Guardar"
