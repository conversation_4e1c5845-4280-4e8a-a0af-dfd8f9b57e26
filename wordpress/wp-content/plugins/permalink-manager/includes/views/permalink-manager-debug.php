<?php

/**
* Display the page where the slugs could be regenerated or replaced
*/
class Permalink_Manager_Debug extends Permalink_Manager_Class {

	public function __construct() {
		add_filter( 'permalink_manager_sections', array($this, 'add_debug_section'), 4 );
	}

	public function add_debug_section($admin_sections) {
		$admin_sections['debug'] = array(
			'name'				=>	__('Debug', 'permalink-manager'),
			'function'    => array('class' => 'Permalink_Manager_Debug', 'method' => 'output')
		);

		return $admin_sections;
	}

	public function output() {
		global $permalink_manager_options, $permalink_manager_uris, $permalink_manager_permastructs, $permalink_manager_redirects, $permalink_manager_external_redirects, $wp_filter;

		$debug_section_url = Permalink_Manager_Admin_Functions::get_admin_url('&section=debug');

		$sections_and_fields = apply_filters('permalink_manager_debug_fields', array(
			'debug-data' => array(
				'section_name' => __('Debug data', 'permalink-manager'),
				'fields' => array(
					'uris' => array(
						'type' => 'textarea',
						'description' => sprintf('%s<br /><strong><a class="pm-confirm-action" href="%s&remove-permalink-manager-settings=uris">%s</a></strong>',
							__('List of the URIs generated by this plugin.', 'permalink-manager'),
							$debug_section_url,
							__('Remove all custom permalinks', 'permalink-manager')
						),
						'label' => __('Array with URIs', 'permalink-manager'),
						'input_class' => 'short-textarea widefat',
						'value' => ($permalink_manager_uris) ? print_r($permalink_manager_uris, true) : ''
					),
					'custom-redirects' => array(
						'type' => 'textarea',
						'description' => sprintf('%s<br /><strong><a class="pm-confirm-action" href="%s&remove-permalink-manager-settings=redirects">%s</a></strong>',
							__('List of custom redirects set-up by this plugin.', 'permalink-manager'),
							$debug_section_url,
							__('Remove all custom redirects', 'permalink-manager')
						),
						'label' => __('Array with redirects', 'permalink-manager'),
						'input_class' => 'short-textarea widefat',
						'value' => ($permalink_manager_redirects) ? print_r($permalink_manager_redirects, true) : ''
					),
					'external-redirects' => array(
						'type' => 'textarea',
						'description' => sprintf('%s<br /><strong><a class="pm-confirm-action" href="%s&remove-permalink-manager-settings=external-redirects">%s</a></strong>',
							__('List of external redirects set-up by this plugin.', 'permalink-manager'),
							$debug_section_url, __('Remove all external redirects', 'permalink-manager')
						),
						'label' => __('Array with external redirects', 'permalink-manager'),
						'input_class' => 'short-textarea widefat',
						'value' => ($permalink_manager_external_redirects) ? print_r(array_filter($permalink_manager_external_redirects), true) : ''
					),
					'permastructs' => array(
						'type' => 'textarea',
						'description' => sprintf('%s<br /><strong><a class="pm-confirm-action" href="%s&remove-permalink-manager-settings=permastructs">%s</a></strong>',
							__('List of permastructures set-up by this plugin.', 'permalink-manager'),
							$debug_section_url,
							__('Remove all permastructures settings', 'permalink-manager')
						),
						'label' => __('Array with permastructures', 'permalink-manager'),
						'input_class' => 'short-textarea widefat',
						'value' => ($permalink_manager_permastructs) ? print_r($permalink_manager_permastructs, true) : ''
					),
					'settings' => array(
						'type' => 'textarea',
						'description' => sprintf('%s<br /><strong><a class="pm-confirm-action" href="%s&remove-permalink-manager-settings=settings">%s</a></strong>',
							__('List of plugin settings.', 'permalink-manager'),
							$debug_section_url,
							__('Remove all plugin settings', 'permalink-manager')
						),
						'label' => __('Array with settings used in this plugin.', 'permalink-manager'),
						'input_class' => 'short-textarea widefat',
						'value' => print_r($permalink_manager_options, true)
					)
				)
			)
		));

		// Now get the HTML output
		$output = '';
		foreach($sections_and_fields as $section_id => $section) {
			$output .= (isset($section['section_name'])) ? "<h3>{$section['section_name']}</h3>" : "";
			$output .= (isset($section['description'])) ? "<p class=\"description\">{$section['description']}</p>" : "";
			$output .= "<table class=\"form-table fixed-table\">";

			// Loop through all fields assigned to this section
			foreach($section['fields'] as $field_id => $field) {
				$field_name = "{$section_id}[$field_id]";
				$field['container'] = 'row';

				$output .= Permalink_Manager_Admin_Functions::generate_option_field($field_name, $field);
			}

			// End the section
			$output .= "</table>";

		}

		return $output;
	}

}
