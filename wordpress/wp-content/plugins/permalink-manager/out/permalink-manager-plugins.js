/**!
* popper.js
* https://popper.js.org/
* Copyright (c) atomiks
* @license MIT
* @version v2.10.2
*/
"use strict";!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Popper={})}(this,(function(e){function t(e,t){return{width:(e=e.getBoundingClientRect()).width/1,height:e.height/1,top:e.top/1,right:e.right/1,bottom:e.bottom/1,left:e.left/1,x:e.left/1,y:e.top/1}}function n(e){return null==e?window:"[object Window]"!==e.toString()?(e=e.ownerDocument)&&e.defaultView||window:e}function o(e){return{scrollLeft:(e=n(e)).pageXOffset,scrollTop:e.pageYOffset}}function r(e){return e instanceof n(e).Element||e instanceof Element}function i(e){return e instanceof n(e).HTMLElement||e instanceof HTMLElement}function a(e){return"undefined"!=typeof ShadowRoot&&(e instanceof n(e).ShadowRoot||e instanceof ShadowRoot)}function s(e){return e?(e.nodeName||"").toLowerCase():null}function f(e){return((r(e)?e.ownerDocument:e.document)||window.document).documentElement}function p(e){return t(f(e)).left+o(e).scrollLeft}function c(e){return n(e).getComputedStyle(e)}function l(e){return e=c(e),/auto|scroll|overlay|hidden/.test(e.overflow+e.overflowY+e.overflowX)}function u(e,r,a){void 0===a&&(a=!1);var c=i(r);i(r)&&r.getBoundingClientRect();var u=f(r);e=t(e);var d={scrollLeft:0,scrollTop:0},m={x:0,y:0};return(c||!c&&!a)&&(("body"!==s(r)||l(u))&&(d=r!==n(r)&&i(r)?{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop}:o(r)),i(r)?((m=t(r)).x+=r.clientLeft,m.y+=r.clientTop):u&&(m.x=p(u))),{x:e.left+d.scrollLeft-m.x,y:e.top+d.scrollTop-m.y,width:e.width,height:e.height}}function d(e){var n=t(e),o=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(n.width-o)&&(o=n.width),1>=Math.abs(n.height-r)&&(r=n.height),{x:e.offsetLeft,y:e.offsetTop,width:o,height:r}}function m(e){return"html"===s(e)?e:e.assignedSlot||e.parentNode||(a(e)?e.host:null)||f(e)}function h(e){return 0<=["html","body","#document"].indexOf(s(e))?e.ownerDocument.body:i(e)&&l(e)?e:h(m(e))}function v(e,t){var o;void 0===t&&(t=[]);var r=h(e);return e=r===(null==(o=e.ownerDocument)?void 0:o.body),o=n(r),r=e?[o].concat(o.visualViewport||[],l(r)?r:[]):r,t=t.concat(r),e?t:t.concat(v(m(r)))}function g(e){return i(e)&&"fixed"!==c(e).position?e.offsetParent:null}function b(e){for(var t=n(e),o=g(e);o&&0<=["table","td","th"].indexOf(s(o))&&"static"===c(o).position;)o=g(o);if(o&&("html"===s(o)||"body"===s(o)&&"static"===c(o).position))return t;if(!o)e:{if(o=-1!==navigator.userAgent.toLowerCase().indexOf("firefox"),-1===navigator.userAgent.indexOf("Trident")||!i(e)||"fixed"!==c(e).position)for(e=m(e);i(e)&&0>["html","body"].indexOf(s(e));){var r=c(e);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||o&&"filter"===r.willChange||o&&r.filter&&"none"!==r.filter){o=e;break e}e=e.parentNode}o=null}return o||t}function y(e){function t(e){o.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){o.has(e)||(e=n.get(e))&&t(e)})),r.push(e)}var n=new Map,o=new Set,r=[];return e.forEach((function(e){n.set(e.name,e)})),e.forEach((function(e){o.has(e.name)||t(e)})),r}function w(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}function x(e){return e.split("-")[0]}function O(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&a(n))do{if(t&&e.isSameNode(t))return!0;t=t.parentNode||t.host}while(t);return!1}function j(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function E(e,r){if("viewport"===r){r=n(e);var a=f(e);r=r.visualViewport;var s=a.clientWidth;a=a.clientHeight;var l=0,u=0;r&&(s=r.width,a=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(l=r.offsetLeft,u=r.offsetTop)),e=j(e={width:s,height:a,x:l+p(e),y:u})}else i(r)?((e=t(r)).top+=r.clientTop,e.left+=r.clientLeft,e.bottom=e.top+r.clientHeight,e.right=e.left+r.clientWidth,e.width=r.clientWidth,e.height=r.clientHeight,e.x=e.left,e.y=e.top):(u=f(e),e=f(u),s=o(u),r=null==(a=u.ownerDocument)?void 0:a.body,a=U(e.scrollWidth,e.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),l=U(e.scrollHeight,e.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),u=-s.scrollLeft+p(u),s=-s.scrollTop,"rtl"===c(r||e).direction&&(u+=U(e.clientWidth,r?r.clientWidth:0)-a),e=j({width:a,height:l,x:u,y:s}));return e}function D(e,t,n){return t="clippingParents"===t?function(e){var t=v(m(e)),n=0<=["absolute","fixed"].indexOf(c(e).position)&&i(e)?b(e):e;return r(n)?t.filter((function(e){return r(e)&&O(e,n)&&"body"!==s(e)})):[]}(e):[].concat(t),(n=(n=[].concat(t,[n])).reduce((function(t,n){return n=E(e,n),t.top=U(n.top,t.top),t.right=z(n.right,t.right),t.bottom=z(n.bottom,t.bottom),t.left=U(n.left,t.left),t}),E(e,n[0]))).width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}function L(e){return e.split("-")[1]}function P(e){return 0<=["top","bottom"].indexOf(e)?"x":"y"}function M(e){var t=e.reference,n=e.element,o=(e=e.placement)?x(e):null;e=e?L(e):null;var r=t.x+t.width/2-n.width/2,i=t.y+t.height/2-n.height/2;switch(o){case"top":r={x:r,y:t.y-n.height};break;case"bottom":r={x:r,y:t.y+t.height};break;case"right":r={x:t.x+t.width,y:i};break;case"left":r={x:t.x-n.width,y:i};break;default:r={x:t.x,y:t.y}}if(null!=(o=o?P(o):null))switch(i="y"===o?"height":"width",e){case"start":r[o]-=t[i]/2-n[i]/2;break;case"end":r[o]+=t[i]/2-n[i]/2}return r}function k(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function A(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function B(e,n){void 0===n&&(n={});var o=n;n=void 0===(n=o.placement)?e.placement:n;var i=o.boundary,a=void 0===i?"clippingParents":i,s=void 0===(i=o.rootBoundary)?"viewport":i;i=void 0===(i=o.elementContext)?"popper":i;var p=o.altBoundary,c=void 0!==p&&p;o=k("number"!=typeof(o=void 0===(o=o.padding)?0:o)?o:A(o,N)),p=e.rects.popper,a=D(r(c=e.elements[c?"popper"===i?"reference":"popper":i])?c:c.contextElement||f(e.elements.popper),a,s),c=M({reference:s=t(e.elements.reference),element:p,strategy:"absolute",placement:n}),p=j(Object.assign({},p,c)),s="popper"===i?p:s;var l={top:a.top-s.top+o.top,bottom:s.bottom-a.bottom+o.bottom,left:a.left-s.left+o.left,right:s.right-a.right+o.right};if(e=e.modifiersData.offset,"popper"===i&&e){var u=e[n];Object.keys(l).forEach((function(e){var t=0<=["right","bottom"].indexOf(e)?1:-1,n=0<=["top","bottom"].indexOf(e)?"y":"x";l[e]+=u[n]*t}))}return l}function W(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function T(e){void 0===e&&(e={});var t=e.defaultModifiers,n=void 0===t?[]:t,o=void 0===(e=e.defaultOptions)?X:e;return function(e,t,i){function a(){f.forEach((function(e){return e()})),f=[]}void 0===i&&(i=o);var s={placement:"bottom",orderedModifiers:[],options:Object.assign({},X,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},f=[],p=!1,c={state:s,setOptions:function(i){return i="function"==typeof i?i(s.options):i,a(),s.options=Object.assign({},o,s.options,i),s.scrollParents={reference:r(e)?v(e):e.contextElement?v(e.contextElement):[],popper:v(t)},i=function(e){var t=y(e);return _.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(n,s.options.modifiers))),s.orderedModifiers=i.filter((function(e){return e.enabled})),s.orderedModifiers.forEach((function(e){var t=e.name,n=e.options;n=void 0===n?{}:n,"function"==typeof(e=e.effect)&&(t=e({state:s,name:t,instance:c,options:n}),f.push(t||function(){}))})),c.update()},forceUpdate:function(){if(!p){var e=s.elements,t=e.reference;if(W(t,e=e.popper))for(s.rects={reference:u(t,b(e),"fixed"===s.options.strategy),popper:d(e)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(e){return s.modifiersData[e.name]=Object.assign({},e.data)})),t=0;t<s.orderedModifiers.length;t++)if(!0===s.reset)s.reset=!1,t=-1;else{var n=s.orderedModifiers[t];e=n.fn;var o=n.options;o=void 0===o?{}:o,n=n.name,"function"==typeof e&&(s=e({state:s,options:o,name:n,instance:c})||s)}}},update:w((function(){return new Promise((function(e){c.forceUpdate(),e(s)}))})),destroy:function(){a(),p=!0}};return W(e,t)?(c.setOptions(i).then((function(e){!p&&i.onFirstUpdate&&i.onFirstUpdate(e)})),c):c}}function R(e){var t,o=e.popper,r=e.popperRect,i=e.placement,a=e.variation,s=e.offsets,p=e.position,l=e.gpuAcceleration,u=e.adaptive;if(!0===(e=e.roundOffsets)){e=s.y;var d=window.devicePixelRatio||1;e={x:F(F(s.x*d)/d)||0,y:F(F(e*d)/d)||0}}else e="function"==typeof e?e(s):s;e=void 0===(e=(d=e).x)?0:e,d=void 0===(d=d.y)?0:d;var m=s.hasOwnProperty("x");s=s.hasOwnProperty("y");var h,v="left",g="top",y=window;if(u){var w=b(o),x="clientHeight",O="clientWidth";w===n(o)&&("static"!==c(w=f(o)).position&&"absolute"===p&&(x="scrollHeight",O="scrollWidth")),"top"!==i&&("left"!==i&&"right"!==i||"end"!==a)||(g="bottom",d-=w[x]-r.height,d*=l?1:-1),"left"!==i&&("top"!==i&&"bottom"!==i||"end"!==a)||(v="right",e-=w[O]-r.width,e*=l?1:-1)}return o=Object.assign({position:p},u&&K),l?Object.assign({},o,((h={})[g]=s?"0":"",h[v]=m?"0":"",h.transform=1>=(y.devicePixelRatio||1)?"translate("+e+"px, "+d+"px)":"translate3d("+e+"px, "+d+"px, 0)",h)):Object.assign({},o,((t={})[g]=s?d+"px":"",t[v]=m?e+"px":"",t.transform="",t))}function H(e){return e.replace(/left|right|bottom|top/g,(function(e){return ee[e]}))}function S(e){return e.replace(/start|end/g,(function(e){return te[e]}))}function C(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function q(e){return["top","right","bottom","left"].some((function(t){return 0<=e[t]}))}var N=["top","bottom","right","left"],V=N.reduce((function(e,t){return e.concat([t+"-start",t+"-end"])}),[]),I=[].concat(N,["auto"]).reduce((function(e,t){return e.concat([t,t+"-start",t+"-end"])}),[]),_="beforeRead read afterRead beforeMain main afterMain beforeWrite write afterWrite".split(" "),U=Math.max,z=Math.min,F=Math.round,X={placement:"bottom",modifiers:[],strategy:"absolute"},Y={passive:!0},G={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,o=e.instance,r=(e=e.options).scroll,i=void 0===r||r,a=void 0===(e=e.resize)||e,s=n(t.elements.popper),f=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&f.forEach((function(e){e.addEventListener("scroll",o.update,Y)})),a&&s.addEventListener("resize",o.update,Y),function(){i&&f.forEach((function(e){e.removeEventListener("scroll",o.update,Y)})),a&&s.removeEventListener("resize",o.update,Y)}},data:{}},J={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state;t.modifiersData[e.name]=M({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},K={top:"auto",right:"auto",bottom:"auto",left:"auto"},Q={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options;e=void 0===(e=n.gpuAcceleration)||e;var o=n.adaptive;o=void 0===o||o,n=void 0===(n=n.roundOffsets)||n,e={placement:x(t.placement),variation:L(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:e},null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,R(Object.assign({},e,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:n})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,R(Object.assign({},e,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:n})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Z={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},o=t.attributes[e]||{},r=t.elements[e];i(r)&&s(r)&&(Object.assign(r.style,n),Object.keys(o).forEach((function(e){var t=o[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var o=t.elements[e],r=t.attributes[e]||{};e=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{}),i(o)&&s(o)&&(Object.assign(o.style,e),Object.keys(r).forEach((function(e){o.removeAttribute(e)})))}))}},requires:["computeStyles"]},$={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.name,o=void 0===(e=e.options.offset)?[0,0]:e,r=(e=I.reduce((function(e,n){var r=t.rects,i=x(n),a=0<=["left","top"].indexOf(i)?-1:1,s="function"==typeof o?o(Object.assign({},r,{placement:n})):o;return r=(r=s[0])||0,s=((s=s[1])||0)*a,i=0<=["left","right"].indexOf(i)?{x:s,y:r}:{x:r,y:s},e[n]=i,e}),{}))[t.placement],i=r.x;r=r.y,null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=i,t.modifiersData.popperOffsets.y+=r),t.modifiersData[n]=e}},ee={left:"right",right:"left",bottom:"top",top:"bottom"},te={start:"end",end:"start"},ne={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options;if(e=e.name,!t.modifiersData[e]._skip){var o=n.mainAxis;o=void 0===o||o;var r=n.altAxis;r=void 0===r||r;var i=n.fallbackPlacements,a=n.padding,s=n.boundary,f=n.rootBoundary,p=n.altBoundary,c=n.flipVariations,l=void 0===c||c,u=n.allowedAutoPlacements;c=x(n=t.options.placement),i=i||(c!==n&&l?function(e){if("auto"===x(e))return[];var t=H(e);return[S(e),t,S(t)]}(n):[H(n)]);var d=[n].concat(i).reduce((function(e,n){return e.concat("auto"===x(n)?function(e,t){void 0===t&&(t={});var n=t.boundary,o=t.rootBoundary,r=t.padding,i=t.flipVariations,a=t.allowedAutoPlacements,s=void 0===a?I:a,f=L(t.placement);0===(i=(t=f?i?V:V.filter((function(e){return L(e)===f})):N).filter((function(e){return 0<=s.indexOf(e)}))).length&&(i=t);var p=i.reduce((function(t,i){return t[i]=B(e,{placement:i,boundary:n,rootBoundary:o,padding:r})[x(i)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:s,rootBoundary:f,padding:a,flipVariations:l,allowedAutoPlacements:u}):n)}),[]);n=t.rects.reference,i=t.rects.popper;var m=new Map;c=!0;for(var h=d[0],v=0;v<d.length;v++){var g=d[v],b=x(g),y="start"===L(g),w=0<=["top","bottom"].indexOf(b),O=w?"width":"height",j=B(t,{placement:g,boundary:s,rootBoundary:f,altBoundary:p,padding:a});if(y=w?y?"right":"left":y?"bottom":"top",n[O]>i[O]&&(y=H(y)),O=H(y),w=[],o&&w.push(0>=j[b]),r&&w.push(0>=j[y],0>=j[O]),w.every((function(e){return e}))){h=g,c=!1;break}m.set(g,w)}if(c)for(o=function(e){var t=d.find((function(t){if(t=m.get(t))return t.slice(0,e).every((function(e){return e}))}));if(t)return h=t,"break"},r=l?3:1;0<r&&"break"!==o(r);r--);t.placement!==h&&(t.modifiersData[e]._skip=!0,t.placement=h,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},oe={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options;e=e.name;var o=n.mainAxis,r=void 0===o||o,i=void 0!==(o=n.altAxis)&&o;o=void 0===(o=n.tether)||o;var a=n.tetherOffset,s=void 0===a?0:a,f=B(t,{boundary:n.boundary,rootBoundary:n.rootBoundary,padding:n.padding,altBoundary:n.altBoundary});n=x(t.placement);var p=L(t.placement),c=!p,l=P(n);n="x"===l?"y":"x",a=t.modifiersData.popperOffsets;var u=t.rects.reference,m=t.rects.popper,h="function"==typeof s?s(Object.assign({},t.rects,{placement:t.placement})):s;if(s={x:0,y:0},a){if(r||i){var v="y"===l?"top":"left",g="y"===l?"bottom":"right",y="y"===l?"height":"width",w=a[l],O=a[l]+f[v],j=a[l]-f[g],E=o?-m[y]/2:0,D="start"===p?u[y]:m[y];p="start"===p?-m[y]:-u[y],m=t.elements.arrow,m=o&&m?d(m):{width:0,height:0};var M=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0};v=M[v],g=M[g],m=U(0,z(u[y],m[y])),D=c?u[y]/2-E-m-v-h:D-m-v-h,u=c?-u[y]/2+E+m+g+h:p+m+g+h,c=t.elements.arrow&&b(t.elements.arrow),h=t.modifiersData.offset?t.modifiersData.offset[t.placement][l]:0,c=a[l]+D-h-(c?"y"===l?c.clientTop||0:c.clientLeft||0:0),u=a[l]+u-h,r&&(r=o?z(O,c):O,j=o?U(j,u):j,r=U(r,z(w,j)),a[l]=r,s[l]=r-w),i&&(r=(i=a[n])+f["x"===l?"top":"left"],f=i-f["x"===l?"bottom":"right"],r=o?z(r,c):r,o=o?U(f,u):f,o=U(r,z(i,o)),a[n]=o,s[n]=o-i)}t.modifiersData[e]=s}},requiresIfExists:["offset"]},re={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,r=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=x(n.placement);if(e=P(s),s=0<=["left","right"].indexOf(s)?"height":"width",i&&a){r=k("number"!=typeof(r="function"==typeof(r=r.padding)?r(Object.assign({},n.rects,{placement:n.placement})):r)?r:A(r,N));var f=d(i),p="y"===e?"top":"left",c="y"===e?"bottom":"right",l=n.rects.reference[s]+n.rects.reference[e]-a[e]-n.rects.popper[s];a=a[e]-n.rects.reference[e],a=(i=(i=b(i))?"y"===e?i.clientHeight||0:i.clientWidth||0:0)/2-f[s]/2+(l/2-a/2),s=U(r[p],z(a,i-f[s]-r[c])),n.modifiersData[o]=((t={})[e]=s,t.centerOffset=s-a,t)}},effect:function(e){var t=e.state;if(null!=(e=void 0===(e=e.options.element)?"[data-popper-arrow]":e)){if("string"==typeof e&&!(e=t.elements.popper.querySelector(e)))return;O(t.elements.popper,e)&&(t.elements.arrow=e)}},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},ie={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state;e=e.name;var n=t.rects.reference,o=t.rects.popper,r=t.modifiersData.preventOverflow,i=B(t,{elementContext:"reference"}),a=B(t,{altBoundary:!0});n=C(i,n),o=C(a,o,r),r=q(n),a=q(o),t.modifiersData[e]={referenceClippingOffsets:n,popperEscapeOffsets:o,isReferenceHidden:r,hasPopperEscaped:a},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":r,"data-popper-escaped":a})}},ae=T({defaultModifiers:[G,J,Q,Z]}),se=[G,J,Q,Z,$,ne,oe,re,ie],fe=T({defaultModifiers:se});e.applyStyles=Z,e.arrow=re,e.computeStyles=Q,e.createPopper=fe,e.createPopperLite=ae,e.defaultModifiers=se,e.detectOverflow=B,e.eventListeners=G,e.flip=ne,e.hide=ie,e.offset=$,e.popperGenerator=T,e.popperOffsets=J,e.preventOverflow=oe,Object.defineProperty(e,"__esModule",{value:!0})}));

/**!
* tippy.js
* https://atomiks.github.io/tippyjs/
* Copyright (c) atomiks
* @license MIT
* @version v6.3.2
*/
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],e):(t=t||self).tippy=e(t.Popper)}(this,(function(t){"use strict";var e="undefined"!=typeof window&&"undefined"!=typeof document,n=!!e&&!!window.msCrypto,r={passive:!0,capture:!0},o=function(){return document.body};function i(t,e,n){if(Array.isArray(t)){var r=t[e];return null==r?Array.isArray(n)?n[e]:n:r}return t}function a(t,e){var n={}.toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function s(t,e){return"function"==typeof t?t.apply(void 0,e):t}function u(t,e){return 0===e?t:function(r){clearTimeout(n),n=setTimeout((function(){t(r)}),e)};var n}function p(t,e){var n=Object.assign({},t);return e.forEach((function(t){delete n[t]})),n}function c(t){return[].concat(t)}function f(t,e){-1===t.indexOf(e)&&t.push(e)}function l(t){return t.split("-")[0]}function d(t){return[].slice.call(t)}function v(){return document.createElement("div")}function m(t){return["Element","Fragment"].some((function(e){return a(t,e)}))}function h(t){return a(t,"MouseEvent")}function g(t){return!(!t||!t._tippy||t._tippy.reference!==t)}function b(t){return m(t)?[t]:function(t){return a(t,"NodeList")}(t)?d(t):Array.isArray(t)?t:d(document.querySelectorAll(t))}function y(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function w(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function x(t){var e,n=c(t)[0];return(null==n||null==(e=n.ownerDocument)?void 0:e.body)?n.ownerDocument:document}function E(t,e,n){var r=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[r](e,n)}))}function O(t,e){for(var n=e;n;){var r;if(t.contains(n))return!0;n=null==(r=null==n.getRootNode?void 0:n.getRootNode())?void 0:r.host}return!1}var C={isTouch:!1},T=0;function A(){C.isTouch||(C.isTouch=!0,window.performance&&document.addEventListener("mousemove",L))}function L(){var t=performance.now();t-T<20&&(C.isTouch=!1,document.removeEventListener("mousemove",L)),T=t}function D(){var t=document.activeElement;if(g(t)){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}var k=Object.assign({appendTo:o,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),R=Object.keys(k);function P(t){var e=(t.plugins||[]).reduce((function(e,n){var r,o=n.name,i=n.defaultValue;o&&(e[o]=void 0!==t[o]?t[o]:null!=(r=k[o])?r:i);return e}),{});return Object.assign({},t,{},e)}function j(t,e){var n=Object.assign({},e,{content:s(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(P(Object.assign({},k,{plugins:e}))):R).reduce((function(e,n){var r=(t.getAttribute("data-tippy-"+n)||"").trim();if(!r)return e;if("content"===n)e[n]=r;else try{e[n]=JSON.parse(r)}catch(t){e[n]=r}return e}),{})}(t,e.plugins));return n.aria=Object.assign({},k.aria,{},n.aria),n.aria={expanded:"auto"===n.aria.expanded?e.interactive:n.aria.expanded,content:"auto"===n.aria.content?e.interactive?null:"describedby":n.aria.content},n}function M(t,e){t.innerHTML=e}function V(t){var e=v();return!0===t?e.className="tippy-arrow":(e.className="tippy-svg-arrow",m(t)?e.appendChild(t):M(e,t)),e}function I(t,e){m(e.content)?(M(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?M(t,e.content):t.textContent=e.content)}function S(t){var e=t.firstElementChild,n=d(e.children);return{box:e,content:n.find((function(t){return t.classList.contains("tippy-content")})),arrow:n.find((function(t){return t.classList.contains("tippy-arrow")||t.classList.contains("tippy-svg-arrow")})),backdrop:n.find((function(t){return t.classList.contains("tippy-backdrop")}))}}function B(t){var e=v(),n=v();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=v();function o(n,r){var o=S(e),i=o.box,a=o.content,s=o.arrow;r.theme?i.setAttribute("data-theme",r.theme):i.removeAttribute("data-theme"),"string"==typeof r.animation?i.setAttribute("data-animation",r.animation):i.removeAttribute("data-animation"),r.inertia?i.setAttribute("data-inertia",""):i.removeAttribute("data-inertia"),i.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?i.setAttribute("role",r.role):i.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||I(a,t.props),r.arrow?s?n.arrow!==r.arrow&&(i.removeChild(s),i.appendChild(V(r.arrow))):i.appendChild(V(r.arrow)):s&&i.removeChild(s)}return r.className="tippy-content",r.setAttribute("data-state","hidden"),I(r,t.props),e.appendChild(n),n.appendChild(r),o(t.props,t.props),{popper:e,onUpdate:o}}B.$$tippy=!0;var N=1,H=[],U=[];function _(e,a){var p,m,g,b,T,A,L,D,R,M=j(e,Object.assign({},k,{},P((p=a,Object.keys(p).reduce((function(t,e){return void 0!==p[e]&&(t[e]=p[e]),t}),{}))))),V=!1,I=!1,B=!1,_=!1,z=[],F=u(wt,M.interactiveDebounce),W=N++,X=(R=M.plugins).filter((function(t,e){return R.indexOf(t)===e})),Y={id:W,reference:e,popper:v(),popperInstance:null,props:M,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:X,clearDelayTimeouts:function(){clearTimeout(m),clearTimeout(g),cancelAnimationFrame(b)},setProps:function(t){if(Y.state.isDestroyed)return;at("onBeforeUpdate",[Y,t]),bt();var n=Y.props,r=j(e,Object.assign({},Y.props,{},t,{ignoreAttributes:!0}));Y.props=r,gt(),n.interactiveDebounce!==r.interactiveDebounce&&(pt(),F=u(wt,r.interactiveDebounce));n.triggerTarget&&!r.triggerTarget?c(n.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):r.triggerTarget&&e.removeAttribute("aria-expanded");ut(),it(),J&&J(n,r);Y.popperInstance&&(Ct(),At().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})));at("onAfterUpdate",[Y,t])},setContent:function(t){Y.setProps({content:t})},show:function(){var t=Y.state.isVisible,e=Y.state.isDestroyed,n=!Y.state.isEnabled,r=C.isTouch&&!Y.props.touch,a=i(Y.props.duration,0,k.duration);if(t||e||n||r)return;if(et().hasAttribute("disabled"))return;if(at("onShow",[Y],!1),!1===Y.props.onShow(Y))return;Y.state.isVisible=!0,tt()&&($.style.visibility="visible");it(),dt(),Y.state.isMounted||($.style.transition="none");if(tt()){var u=rt(),p=u.box,c=u.content;y([p,c],0)}L=function(){var t;if(Y.state.isVisible&&!_){if(_=!0,$.offsetHeight,$.style.transition=Y.props.moveTransition,tt()&&Y.props.animation){var e=rt(),n=e.box,r=e.content;y([n,r],a),w([n,r],"visible")}st(),ut(),f(U,Y),null==(t=Y.popperInstance)||t.forceUpdate(),Y.state.isMounted=!0,at("onMount",[Y]),Y.props.animation&&tt()&&function(t,e){mt(t,e)}(a,(function(){Y.state.isShown=!0,at("onShown",[Y])}))}},function(){var t,e=Y.props.appendTo,n=et();t=Y.props.interactive&&e===o||"parent"===e?n.parentNode:s(e,[n]);t.contains($)||t.appendChild($);Ct()}()},hide:function(){var t=!Y.state.isVisible,e=Y.state.isDestroyed,n=!Y.state.isEnabled,r=i(Y.props.duration,1,k.duration);if(t||e||n)return;if(at("onHide",[Y],!1),!1===Y.props.onHide(Y))return;Y.state.isVisible=!1,Y.state.isShown=!1,_=!1,V=!1,tt()&&($.style.visibility="hidden");if(pt(),vt(),it(),tt()){var o=rt(),a=o.box,s=o.content;Y.props.animation&&(y([a,s],r),w([a,s],"hidden"))}st(),ut(),Y.props.animation?tt()&&function(t,e){mt(t,(function(){!Y.state.isVisible&&$.parentNode&&$.parentNode.contains($)&&e()}))}(r,Y.unmount):Y.unmount()},hideWithInteractivity:function(t){nt().addEventListener("mousemove",F),f(H,F),F(t)},enable:function(){Y.state.isEnabled=!0},disable:function(){Y.hide(),Y.state.isEnabled=!1},unmount:function(){Y.state.isVisible&&Y.hide();if(!Y.state.isMounted)return;Tt(),At().forEach((function(t){t._tippy.unmount()})),$.parentNode&&$.parentNode.removeChild($);U=U.filter((function(t){return t!==Y})),Y.state.isMounted=!1,at("onHidden",[Y])},destroy:function(){if(Y.state.isDestroyed)return;Y.clearDelayTimeouts(),Y.unmount(),bt(),delete e._tippy,Y.state.isDestroyed=!0,at("onDestroy",[Y])}};if(!M.render)return Y;var q=M.render(Y),$=q.popper,J=q.onUpdate;$.setAttribute("data-tippy-root",""),$.id="tippy-"+Y.id,Y.popper=$,e._tippy=Y,$._tippy=Y;var G=X.map((function(t){return t.fn(Y)})),K=e.hasAttribute("aria-expanded");return gt(),ut(),it(),at("onCreate",[Y]),M.showOnCreate&&Lt(),$.addEventListener("mouseenter",(function(){Y.props.interactive&&Y.state.isVisible&&Y.clearDelayTimeouts()})),$.addEventListener("mouseleave",(function(t){Y.props.interactive&&Y.props.trigger.indexOf("mouseenter")>=0&&(nt().addEventListener("mousemove",F),F(t))})),Y;function Q(){var t=Y.props.touch;return Array.isArray(t)?t:[t,0]}function Z(){return"hold"===Q()[0]}function tt(){var t;return!!(null==(t=Y.props.render)?void 0:t.$$tippy)}function et(){return D||e}function nt(){var t=et().parentNode;return t?x(t):document}function rt(){return S($)}function ot(t){return Y.state.isMounted&&!Y.state.isVisible||C.isTouch||T&&"focus"===T.type?0:i(Y.props.delay,t?0:1,k.delay)}function it(){$.style.pointerEvents=Y.props.interactive&&Y.state.isVisible?"":"none",$.style.zIndex=""+Y.props.zIndex}function at(t,e,n){var r;(void 0===n&&(n=!0),G.forEach((function(n){n[t]&&n[t].apply(void 0,e)})),n)&&(r=Y.props)[t].apply(r,e)}function st(){var t=Y.props.aria;if(t.content){var n="aria-"+t.content,r=$.id;c(Y.props.triggerTarget||e).forEach((function(t){var e=t.getAttribute(n);if(Y.state.isVisible)t.setAttribute(n,e?e+" "+r:r);else{var o=e&&e.replace(r,"").trim();o?t.setAttribute(n,o):t.removeAttribute(n)}}))}}function ut(){!K&&Y.props.aria.expanded&&c(Y.props.triggerTarget||e).forEach((function(t){Y.props.interactive?t.setAttribute("aria-expanded",Y.state.isVisible&&t===et()?"true":"false"):t.removeAttribute("aria-expanded")}))}function pt(){nt().removeEventListener("mousemove",F),H=H.filter((function(t){return t!==F}))}function ct(t){if(!C.isTouch||!B&&"mousedown"!==t.type){var e=t.composedPath&&t.composedPath()[0]||t.target;if(!Y.props.interactive||!O($,e)){if(O(et(),e)){if(C.isTouch)return;if(Y.state.isVisible&&Y.props.trigger.indexOf("click")>=0)return}else at("onClickOutside",[Y,t]);!0===Y.props.hideOnClick&&(Y.clearDelayTimeouts(),Y.hide(),I=!0,setTimeout((function(){I=!1})),Y.state.isMounted||vt())}}}function ft(){B=!0}function lt(){B=!1}function dt(){var t=nt();t.addEventListener("mousedown",ct,!0),t.addEventListener("touchend",ct,r),t.addEventListener("touchstart",lt,r),t.addEventListener("touchmove",ft,r)}function vt(){var t=nt();t.removeEventListener("mousedown",ct,!0),t.removeEventListener("touchend",ct,r),t.removeEventListener("touchstart",lt,r),t.removeEventListener("touchmove",ft,r)}function mt(t,e){var n=rt().box;function r(t){t.target===n&&(E(n,"remove",r),e())}if(0===t)return e();E(n,"remove",A),E(n,"add",r),A=r}function ht(t,n,r){void 0===r&&(r=!1),c(Y.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,r),z.push({node:e,eventType:t,handler:n,options:r})}))}function gt(){var t;Z()&&(ht("touchstart",yt,{passive:!0}),ht("touchend",xt,{passive:!0})),(t=Y.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(ht(t,yt),t){case"mouseenter":ht("mouseleave",xt);break;case"focus":ht(n?"focusout":"blur",Et);break;case"focusin":ht("focusout",Et)}}))}function bt(){z.forEach((function(t){var e=t.node,n=t.eventType,r=t.handler,o=t.options;e.removeEventListener(n,r,o)})),z=[]}function yt(t){var e,n=!1;if(Y.state.isEnabled&&!Ot(t)&&!I){var r="focus"===(null==(e=T)?void 0:e.type);T=t,D=t.currentTarget,ut(),!Y.state.isVisible&&h(t)&&H.forEach((function(e){return e(t)})),"click"===t.type&&(Y.props.trigger.indexOf("mouseenter")<0||V)&&!1!==Y.props.hideOnClick&&Y.state.isVisible?n=!0:Lt(t),"click"===t.type&&(V=!n),n&&!r&&Dt(t)}}function wt(t){var e=t.target,n=et().contains(e)||$.contains(e);"mousemove"===t.type&&n||function(t,e){var n=e.clientX,r=e.clientY;return t.every((function(t){var e=t.popperRect,o=t.popperState,i=t.props.interactiveBorder,a=l(o.placement),s=o.modifiersData.offset;if(!s)return!0;var u="bottom"===a?s.top.y:0,p="top"===a?s.bottom.y:0,c="right"===a?s.left.x:0,f="left"===a?s.right.x:0,d=e.top-r+u>i,v=r-e.bottom-p>i,m=e.left-n+c>i,h=n-e.right-f>i;return d||v||m||h}))}(At().concat($).map((function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:M}:null})).filter(Boolean),t)&&(pt(),Dt(t))}function xt(t){Ot(t)||Y.props.trigger.indexOf("click")>=0&&V||(Y.props.interactive?Y.hideWithInteractivity(t):Dt(t))}function Et(t){Y.props.trigger.indexOf("focusin")<0&&t.target!==et()||Y.props.interactive&&t.relatedTarget&&$.contains(t.relatedTarget)||Dt(t)}function Ot(t){return!!C.isTouch&&Z()!==t.type.indexOf("touch")>=0}function Ct(){Tt();var n=Y.props,r=n.popperOptions,o=n.placement,i=n.offset,a=n.getReferenceClientRect,s=n.moveTransition,u=tt()?S($).arrow:null,p=a?{getBoundingClientRect:a,contextElement:a.contextElement||et()}:e,c=[{name:"offset",options:{offset:i}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(tt()){var n=rt().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)})),e.attributes.popper={}}}}];tt()&&u&&c.push({name:"arrow",options:{element:u,padding:3}}),c.push.apply(c,(null==r?void 0:r.modifiers)||[]),Y.popperInstance=t.createPopper(p,$,Object.assign({},r,{placement:o,onFirstUpdate:L,modifiers:c}))}function Tt(){Y.popperInstance&&(Y.popperInstance.destroy(),Y.popperInstance=null)}function At(){return d($.querySelectorAll("[data-tippy-root]"))}function Lt(t){Y.clearDelayTimeouts(),t&&at("onTrigger",[Y,t]),dt();var e=ot(!0),n=Q(),r=n[0],o=n[1];C.isTouch&&"hold"===r&&o&&(e=o),e?m=setTimeout((function(){Y.show()}),e):Y.show()}function Dt(t){if(Y.clearDelayTimeouts(),at("onUntrigger",[Y,t]),Y.state.isVisible){if(!(Y.props.trigger.indexOf("mouseenter")>=0&&Y.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&V)){var e=ot(!1);e?g=setTimeout((function(){Y.state.isVisible&&Y.hide()}),e):b=requestAnimationFrame((function(){Y.hide()}))}}else vt()}}function z(t,e){void 0===e&&(e={});var n=k.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",A,r),window.addEventListener("blur",D);var o=Object.assign({},e,{plugins:n}),i=b(t).reduce((function(t,e){var n=e&&_(e,o);return n&&t.push(n),t}),[]);return m(t)?i[0]:i}z.defaultProps=k,z.setDefaultProps=function(t){Object.keys(t).forEach((function(e){k[e]=t[e]}))},z.currentInput=C;var F=Object.assign({},t.applyStyles,{effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow)}}),W={mouseover:"mouseenter",focusin:"focus",click:"click"};var X={name:"animateFill",defaultValue:!1,fn:function(t){var e;if(!(null==(e=t.props.render)?void 0:e.$$tippy))return{};var n=S(t.popper),r=n.box,o=n.content,i=t.props.animateFill?function(){var t=v();return t.className="tippy-backdrop",w([t],"hidden"),t}():null;return{onCreate:function(){i&&(r.insertBefore(i,r.firstElementChild),r.setAttribute("data-animatefill",""),r.style.overflow="hidden",t.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(i){var t=r.style.transitionDuration,e=Number(t.replace("ms",""));o.style.transitionDelay=Math.round(e/10)+"ms",i.style.transitionDuration=t,w([i],"visible")}},onShow:function(){i&&(i.style.transitionDuration="0ms")},onHide:function(){i&&w([i],"hidden")}}}};var Y={clientX:0,clientY:0},q=[];function $(t){var e=t.clientX,n=t.clientY;Y={clientX:e,clientY:n}}var J={name:"followCursor",defaultValue:!1,fn:function(t){var e=t.reference,n=x(t.props.triggerTarget||e),r=!1,o=!1,i=!0,a=t.props;function s(){return"initial"===t.props.followCursor&&t.state.isVisible}function u(){n.addEventListener("mousemove",f)}function p(){n.removeEventListener("mousemove",f)}function c(){r=!0,t.setProps({getReferenceClientRect:null}),r=!1}function f(n){var r=!n.target||e.contains(n.target),o=t.props.followCursor,i=n.clientX,a=n.clientY,s=e.getBoundingClientRect(),u=i-s.left,p=a-s.top;!r&&t.props.interactive||t.setProps({getReferenceClientRect:function(){var t=e.getBoundingClientRect(),n=i,r=a;"initial"===o&&(n=t.left+u,r=t.top+p);var s="horizontal"===o?t.top:r,c="vertical"===o?t.right:n,f="horizontal"===o?t.bottom:r,l="vertical"===o?t.left:n;return{width:c-l,height:f-s,top:s,right:c,bottom:f,left:l}}})}function l(){t.props.followCursor&&(q.push({instance:t,doc:n}),function(t){t.addEventListener("mousemove",$)}(n))}function d(){0===(q=q.filter((function(e){return e.instance!==t}))).filter((function(t){return t.doc===n})).length&&function(t){t.removeEventListener("mousemove",$)}(n)}return{onCreate:l,onDestroy:d,onBeforeUpdate:function(){a=t.props},onAfterUpdate:function(e,n){var i=n.followCursor;r||void 0!==i&&a.followCursor!==i&&(d(),i?(l(),!t.state.isMounted||o||s()||u()):(p(),c()))},onMount:function(){t.props.followCursor&&!o&&(i&&(f(Y),i=!1),s()||u())},onTrigger:function(t,e){h(e)&&(Y={clientX:e.clientX,clientY:e.clientY}),o="focus"===e.type},onHidden:function(){t.props.followCursor&&(c(),p(),i=!0)}}}};var G={name:"inlinePositioning",defaultValue:!1,fn:function(t){var e,n=t.reference;var r=-1,o=!1,i={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(o){var i=o.state;t.props.inlinePositioning&&(e!==i.placement&&t.setProps({getReferenceClientRect:function(){return function(t){return function(t,e,n,r){if(n.length<2||null===t)return e;if(2===n.length&&r>=0&&n[0].left>n[1].right)return n[r]||e;switch(t){case"top":case"bottom":var o=n[0],i=n[n.length-1],a="top"===t,s=o.top,u=i.bottom,p=a?o.left:i.left,c=a?o.right:i.right;return{top:s,bottom:u,left:p,right:c,width:c-p,height:u-s};case"left":case"right":var f=Math.min.apply(Math,n.map((function(t){return t.left}))),l=Math.max.apply(Math,n.map((function(t){return t.right}))),d=n.filter((function(e){return"left"===t?e.left===f:e.right===l})),v=d[0].top,m=d[d.length-1].bottom;return{top:v,bottom:m,left:f,right:l,width:l-f,height:m-v};default:return e}}(l(t),n.getBoundingClientRect(),d(n.getClientRects()),r)}(i.placement)}}),e=i.placement)}};function a(){var e;o||(e=function(t,e){var n;return{popperOptions:Object.assign({},t.popperOptions,{modifiers:[].concat(((null==(n=t.popperOptions)?void 0:n.modifiers)||[]).filter((function(t){return t.name!==e.name})),[e])})}}(t.props,i),o=!0,t.setProps(e),o=!1)}return{onCreate:a,onAfterUpdate:a,onTrigger:function(e,n){if(h(n)){var o=d(t.reference.getClientRects()),i=o.find((function(t){return t.left-2<=n.clientX&&t.right+2>=n.clientX&&t.top-2<=n.clientY&&t.bottom+2>=n.clientY})),a=o.indexOf(i);r=a>-1?a:r}},onHidden:function(){r=-1}}}};var K={name:"sticky",defaultValue:!1,fn:function(t){var e=t.reference,n=t.popper;function r(e){return!0===t.props.sticky||t.props.sticky===e}var o=null,i=null;function a(){var s=r("reference")?(t.popperInstance?t.popperInstance.state.elements.reference:e).getBoundingClientRect():null,u=r("popper")?n.getBoundingClientRect():null;(s&&Q(o,s)||u&&Q(i,u))&&t.popperInstance&&t.popperInstance.update(),o=s,i=u,t.state.isMounted&&requestAnimationFrame(a)}return{onMount:function(){t.props.sticky&&a()}}}};function Q(t,e){return!t||!e||(t.top!==e.top||t.right!==e.right||t.bottom!==e.bottom||t.left!==e.left)}return e&&function(t){var e=document.createElement("style");e.textContent=t,e.setAttribute("data-tippy-stylesheet","");var n=document.head,r=document.querySelector("head>style,head>link");r?n.insertBefore(e,r):n.appendChild(e)}('.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;white-space:normal;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}'),z.setDefaultProps({plugins:[X,J,G,K],render:B}),z.createSingleton=function(t,e){var n;void 0===e&&(e={});var r,o=t,i=[],a=e.overrides,s=[],u=!1;function c(){i=o.map((function(t){return t.reference}))}function f(t){o.forEach((function(e){t?e.enable():e.disable()}))}function l(t){return o.map((function(e){var n=e.setProps;return e.setProps=function(o){n(o),e.reference===r&&t.setProps(o)},function(){e.setProps=n}}))}function d(t,e){var n=i.indexOf(e);if(e!==r){r=e;var s=(a||[]).concat("content").reduce((function(t,e){return t[e]=o[n].props[e],t}),{});t.setProps(Object.assign({},s,{getReferenceClientRect:"function"==typeof s.getReferenceClientRect?s.getReferenceClientRect:function(){return e.getBoundingClientRect()}}))}}f(!1),c();var m={fn:function(){return{onDestroy:function(){f(!0)},onHidden:function(){r=null},onClickOutside:function(t){t.props.showOnCreate&&!u&&(u=!0,r=null)},onShow:function(t){t.props.showOnCreate&&!u&&(u=!0,d(t,i[0]))},onTrigger:function(t,e){d(t,e.currentTarget)}}}},h=z(v(),Object.assign({},p(e,["overrides"]),{plugins:[m].concat(e.plugins||[]),triggerTarget:i,popperOptions:Object.assign({},e.popperOptions,{modifiers:[].concat((null==(n=e.popperOptions)?void 0:n.modifiers)||[],[F])})})),g=h.show;h.show=function(t){if(g(),!r&&null==t)return d(h,i[0]);if(!r||null!=t){if("number"==typeof t)return i[t]&&d(h,i[t]);if(o.indexOf(t)>=0){var e=t.reference;return d(h,e)}return i.indexOf(t)>=0?d(h,t):void 0}},h.showNext=function(){var t=i[0];if(!r)return h.show(0);var e=i.indexOf(r);h.show(i[e+1]||t)},h.showPrevious=function(){var t=i[i.length-1];if(!r)return h.show(t);var e=i.indexOf(r),n=i[e-1]||t;h.show(n)};var b=h.setProps;return h.setProps=function(t){a=t.overrides||a,b(t)},h.setInstances=function(t){f(!0),s.forEach((function(t){return t()})),o=t,f(!1),c(),l(h),h.setProps({triggerTarget:i})},s=l(h),h},z.delegate=function(t,e){var n=[],o=[],i=!1,a=e.target,s=p(e,["target"]),u=Object.assign({},s,{trigger:"manual",touch:!1}),f=Object.assign({},s,{showOnCreate:!0}),l=z(t,u);function d(t){if(t.target&&!i){var n=t.target.closest(a);if(n){var r=n.getAttribute("data-tippy-trigger")||e.trigger||k.trigger;if(!n._tippy&&!("touchstart"===t.type&&"boolean"==typeof f.touch||"touchstart"!==t.type&&r.indexOf(W[t.type])<0)){var s=z(n,f);s&&(o=o.concat(s))}}}}function v(t,e,r,o){void 0===o&&(o=!1),t.addEventListener(e,r,o),n.push({node:t,eventType:e,handler:r,options:o})}return c(l).forEach((function(t){var e=t.destroy,a=t.enable,s=t.disable;t.destroy=function(t){void 0===t&&(t=!0),t&&o.forEach((function(t){t.destroy()})),o=[],n.forEach((function(t){var e=t.node,n=t.eventType,r=t.handler,o=t.options;e.removeEventListener(n,r,o)})),n=[],e()},t.enable=function(){a(),o.forEach((function(t){return t.enable()})),i=!1},t.disable=function(){s(),o.forEach((function(t){return t.disable()})),i=!0},function(t){var e=t.reference;v(e,"touchstart",d,r),v(e,"mouseover",d),v(e,"focusin",d),v(e,"click",d)}(t)})),l},z.hideAll=function(t){var e=void 0===t?{}:t,n=e.exclude,r=e.duration;U.forEach((function(t){var e=!1;if(n&&(e=g(n)?t.reference===n:t.popper===n.popper),!e){var o=t.props.duration;t.setProps({duration:r}),t.hide(),t.state.isDestroyed||t.setProps({duration:o})}}))},z.roundArrow='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',z}));

/**
 * Simplest Tags Input Beautifier
 * Copyright (c) Taufik Nurrohman
 * https://github.com/tovic
 * @license MIT
 * @version 2.1.3
 */
!function(e,t,n){function y(e){return t.createElement(e)}function x(e){return Object.keys(e)}function C(e){return x(e).length}var i="__instance__",r=setTimeout,s="innerHTML",a="textContent",o="className",f="classes",u="toLowerCase",l="replace",c="firstChild",p="parentNode",g="nextSibling",d="previousSibling",v="appendChild",h="insertBefore",b="removeChild",m="setAttribute",w="getAttribute",j="preventDefault",k="addEventListener";!function(e){e.version="2.1.3",e[i]={},e.each=function(t,n){return r(function(){var r,n=e[i];for(r in n)t(n[r],r,n)},0===n?0:n||1),e}}(e[n]=function(t,D){function H(){B.focus()}function I(e){var n,t=e?this:B;r(function(){n=t[a].split(N.join);for(K in n)_.set(n[K])},1)}var B,K,O,_=this,E=Date.now(),L=t.placeholder||"",N={join:", ",max:9999,escape:[",","\n"],alert:!0,text:["Delete “%s”","Duplicate “%s”"],classes:["tags","tag","tags-input","tags-output","tags-view"],update:function(){}},S=y("span"),T=y("span"),A="data-tag";e[n][i][t.id||t.name||C(e[n][i])]=_,D="string"==typeof D?{join:D}:D||{};for(K in N)"undefined"!=typeof D[K]&&(N[K]=D[K]);t[o]=N[f][3],S[o]=N[f][0]+" "+N[f][0]+"-"+E,S[k]("click",H,!1),S.id=N[f][0]+":"+(t.id||E),S[s]='<span class="'+N[f][4]+'"></span>',T[o]=N[f][2],T[s]='<span contenteditable spellcheck="false" style="white-space:nowrap;outline:none;"></span><span>'+L+"</span>",t[p][h](S,t[g]||null),S[c][v](T),B=T[c],_.tags={},_.error=0,_.filter=function(e){return(e+"")[l](new RegExp("["+N.join[l](/\s/g,"")+"]|\\s{2,}|^\\s+|\\s+$","g"),"")[u]()},_.update=function(e,n){for(t.value="";(K=S[c][c])&&K[w](A);)S[c][b](K);if(0===e)e=x(_.tags);else{for(K in e)O=_.filter(e[K]),O&&(_.tags[O]=1);e=x(_.tags)}_.tags={};for(K in e)_.set(e[K],n);return N.update(_),_},_.reset=function(e){return e=_.filter(e||""),e?delete _.tags[e]:_.tags={},_.update(0,1)},_.set=function(e,n){if(e=_.filter(e),""===e||C(_.tags)===N.max)return B[s]="",_;var i=y("span"),r=y("a");if(i[o]=N[f][1],i[m](A,e),r.href="javascript:;",r.title=(N.text[0]||"")[l](/%s/g,e),r[k]("click",function(e){var t=this,n=t[p],i=t[p][w](A);n[p][b](n),_.reset(i),H(),e[j]()},!1),i[v](r),B[s]="",B[g][s]=L,_.tags[e]){if(n)S[c][h](i,T);else if(N.alert){_.error=1;var a=(N.text[1]||e)[l](/%s/g,e);"function"==typeof N.alert?N.alert(a,e,_):alert(a)}}else _.tags[e]=1,S[c][h](i,T);return t.value=x(_.tags).join(N.join),!n&&N.update(_),_},function(){return B[k]("blur",function(){_.error=0,_.set(this[a])},!1),B[k]("paste",I,!1),B[k]("keydown",function(e){_.error=0;var y,t=this,n=e.keyCode,i=S,o=(e.key||String.fromCharCode(n))[u](),f=e.ctrlKey,l=e.shiftKey,c=T[d]&&T[d][w](A),v=B[g],h="tab"===o||!l&&9===n,b="enter"===o||!l&&13===n,m=" "===o||!l&&32===n,k="backspace"===o||!l&&8===n;if(!f&&b){for(;i=i[p];)if("form"===i.nodeName[u]()){y=i;break}_.set(t[a]),0===_.error&&y&&y.submit()}else if(f&&("v"===o||!l&&86===n))I();else if(!t[a]&&k)_.reset(c),H();else{var C,D,x=N.escape;for(K in x)if(C=x[K],D="s"===C,(D||"	"===C)&&h||(D||"\n"===C)&&b||(D||" "===C)&&m)return r(function(){_.set(t[a]),H()},1),void e[j]();r(function(){var e=t[a];for(v[s]=e?"":L,K=0,O=x.length;O>K;++K)if(x[K]&&-1!==e.indexOf(x[K])){_.set(e.split(x[K]).join(""));break}},1)}},!1),_}(),_.update(t.value.split(N.join),1),_.config=N,_.input=T,_.view=S,_.target=_.output=t})}(window,document,"TIB");

/**
 * LoadingOverlay
 * Copyright (c) Gaspare Sganga
 * https://gasparesganga.com/labs/jquery-loading-overlay/
 * @license MIT
 * @version 2.1.5
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?e(require("jquery")):e(jQuery)}(function(e,s){"use strict";var t={background:"rgba(255, 255, 255, 0.8)",backgroundClass:"",image:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'><circle r='80' cx='500' cy='90'/><circle r='80' cx='500' cy='910'/><circle r='80' cx='90' cy='500'/><circle r='80' cx='910' cy='500'/><circle r='80' cx='212' cy='212'/><circle r='80' cx='788' cy='212'/><circle r='80' cx='212' cy='788'/><circle r='80' cx='788' cy='788'/></svg>",imageAnimation:"2000ms rotate_right",imageAutoResize:!0,imageResizeFactor:1,imageColor:"#202020",imageClass:"",imageOrder:1,fontawesome:"",fontawesomeAnimation:"",fontawesomeAutoResize:!0,fontawesomeResizeFactor:1,fontawesomeColor:"#202020",fontawesomeOrder:2,custom:"",customAnimation:"",customAutoResize:!0,customResizeFactor:1,customOrder:3,text:"",textAnimation:"",textAutoResize:!0,textResizeFactor:.5,textColor:"#202020",textClass:"",textOrder:4,progress:!1,progressAutoResize:!0,progressResizeFactor:.25,progressColor:"#a0a0a0",progressClass:"",progressOrder:5,progressFixedPosition:"",progressSpeed:200,progressMin:0,progressMax:100,size:50,maxSize:120,minSize:20,direction:"column",fade:!0,resizeInterval:50,zIndex:2147483647},a={overlay:{"box-sizing":"border-box",position:"relative",display:"flex","flex-wrap":"nowrap","align-items":"center","justify-content":"space-around"},element:{"box-sizing":"border-box",overflow:"visible",flex:"0 0 auto",display:"flex","justify-content":"center","align-items":"center"},element_svg:{width:"100%",height:"100%"},progress_fixed:{position:"absolute",left:"0",width:"100%"},progress_wrapper:{position:"absolute",top:"0",left:"0",width:"100%",height:"100%"},progress_bar:{position:"absolute",left:"0"}},o={count:0,container:s,settings:s,wholePage:s,resizeIntervalId:s,text:s,progress:s},r={animations:["rotate_right","rotate_left","fadein","pulse"],progressPosition:["top","bottom"]},i={animations:{name:"rotate_right",time:"2000ms"},fade:[400,200]};function n(s,t){s=e(s),t.size=y(t.size),t.maxSize=parseInt(t.maxSize,10)||0,t.minSize=parseInt(t.minSize,10)||0,t.resizeInterval=parseInt(t.resizeInterval,10)||0;var r=f(s),n=p(s);if(!1===n){if((n=e.extend({},o)).container=s,n.wholePage=s.is("body"),r=e("<div>",{class:"loadingoverlay"}).css(a.overlay).css("flex-direction","row"===t.direction.toLowerCase()?"row":"column"),t.backgroundClass?r.addClass(t.backgroundClass):r.css("background",t.background),n.wholePage&&r.css({position:"fixed",top:0,left:0,width:"100%",height:"100%"}),void 0!==t.zIndex&&r.css("z-index",t.zIndex),t.image){e.isArray(t.imageColor)?0===t.imageColor.length?t.imageColor=!1:1===t.imageColor.length?t.imageColor={fill:t.imageColor[0]}:t.imageColor={fill:t.imageColor[0],stroke:t.imageColor[1]}:t.imageColor&&(t.imageColor={fill:t.imageColor});var l=u(r,t.imageOrder,t.imageAutoResize,t.imageResizeFactor,t.imageAnimation);"<svg"===t.image.slice(0,4).toLowerCase()&&"</svg>"===t.image.slice(-6).toLowerCase()?(l.append(t.image),l.children().css(a.element_svg),!t.imageClass&&t.imageColor&&l.find("*").css(t.imageColor)):".svg"===t.image.slice(-4).toLowerCase()||"data:image/svg"===t.image.slice(0,14).toLowerCase()?e.ajax({url:t.image,type:"GET",dataType:"html",global:!1}).done(function(e){l.html(e),l.children().css(a.element_svg),!t.imageClass&&t.imageColor&&l.find("*").css(t.imageColor)}):l.css({"background-image":"url("+t.image+")","background-position":"center","background-repeat":"no-repeat","background-size":"cover"}),t.imageClass&&l.addClass(t.imageClass)}if(t.fontawesome){l=u(r,t.fontawesomeOrder,t.fontawesomeAutoResize,t.fontawesomeResizeFactor,t.fontawesomeAnimation).addClass("loadingoverlay_fa");e("<div>",{class:t.fontawesome}).appendTo(l),t.fontawesomeColor&&l.css("color",t.fontawesomeColor)}if(t.custom)l=u(r,t.customOrder,t.customAutoResize,t.customResizeFactor,t.customAnimation).append(t.custom);if(t.text&&(n.text=u(r,t.textOrder,t.textAutoResize,t.textResizeFactor,t.textAnimation).addClass("loadingoverlay_text").text(t.text),t.textClass?n.text.addClass(t.textClass):t.textColor&&n.text.css("color",t.textColor)),t.progress){l=u(r,t.progressOrder,t.progressAutoResize,t.progressResizeFactor,!1).addClass("loadingoverlay_progress");var g=e("<div>").css(a.progress_wrapper).appendTo(l);n.progress={bar:e("<div>").css(a.progress_bar).appendTo(g),fixed:!1,margin:0,min:parseFloat(t.progressMin),max:parseFloat(t.progressMax),speed:parseInt(t.progressSpeed,10)};var c=(t.progressFixedPosition+"").replace(/\s\s+/g," ").toLowerCase().split(" ");2===c.length&&h(c[0])?(n.progress.fixed=c[0],n.progress.margin=y(c[1])):2===c.length&&h(c[1])?(n.progress.fixed=c[1],n.progress.margin=y(c[0])):1===c.length&&h(c[0])&&(n.progress.fixed=c[0],n.progress.margin=0),"top"===n.progress.fixed?l.css(a.progress_fixed).css("top",n.progress.margin?n.progress.margin.value+(n.progress.margin.fixed?n.progress.margin.units:"%"):0):"bottom"===n.progress.fixed&&l.css(a.progress_fixed).css("top","auto"),t.progressClass?n.progress.bar.addClass(t.progressClass):t.progressColor&&n.progress.bar.css("background",t.progressColor)}t.fade?!0===t.fade?t.fade=i.fade:"string"==typeof t.fade||"number"==typeof t.fade?t.fade=[t.fade,t.fade]:e.isArray(t.fade)&&t.fade.length<2&&(t.fade=[t.fade[0],t.fade[0]]):t.fade=[0,0],t.fade=[parseInt(t.fade[0],10),parseInt(t.fade[1],10)],n.settings=t,r.data("loadingoverlay_data",n),s.data("loadingoverlay",r),r.fadeTo(0,.01).appendTo("body"),m(s,!0),t.resizeInterval>0&&(n.resizeIntervalId=setInterval(function(){m(s,!1)},t.resizeInterval)),r.fadeTo(t.fade[0],1)}n.count++}function l(s,t){var a=f(s=e(s)),o=p(s);!1!==o&&(o.count--,(t||o.count<=0)&&(o.resizeIntervalId&&clearInterval(o.resizeIntervalId),a.fadeOut(o.settings.fade[1],function(){e(this).remove()}),s.removeData("loadingoverlay")))}function g(s){m(e(s),!0)}function c(s,t){var a=p(s=e(s));!1!==a&&a.text&&(!1===t?a.text.hide():a.text.show().text(t))}function d(s,t){var a=p(s=e(s));if(!1!==a&&a.progress)if(!1===t)a.progress.bar.hide();else{var o=100*((parseFloat(t)||0)-a.progress.min)/(a.progress.max-a.progress.min);o<0&&(o=0),o>100&&(o=100),a.progress.bar.show().animate({width:o+"%"},a.progress.speed)}}function m(s,t){var a=f(s),o=p(s);if(!1!==o){if(!o.wholePage){var r="fixed"===s.css("position"),i=r?s[0].getBoundingClientRect():s.offset();a.css({position:r?"fixed":"absolute",top:i.top+parseInt(s.css("border-top-width"),10),left:i.left+parseInt(s.css("border-left-width"),10),width:s.innerWidth(),height:s.innerHeight()})}if(o.settings.size){var n=o.wholePage?e(window):s,l=o.settings.size.value;o.settings.size.fixed||(l=Math.min(n.innerWidth(),n.innerHeight())*l/100,o.settings.maxSize&&l>o.settings.maxSize&&(l=o.settings.maxSize),o.settings.minSize&&l<o.settings.minSize&&(l=o.settings.minSize)),a.children(".loadingoverlay_element").each(function(){var s=e(this);if(t||s.data("loadingoverlay_autoresize")){var a=s.data("loadingoverlay_resizefactor");s.hasClass("loadingoverlay_fa")||s.hasClass("loadingoverlay_text")?s.css("font-size",l*a+o.settings.size.units):s.hasClass("loadingoverlay_progress")?(o.progress.bar.css("height",l*a+o.settings.size.units),o.progress.fixed?"bottom"===o.progress.fixed&&s.css("bottom",o.progress.margin?o.progress.margin.value+(o.progress.margin.fixed?o.progress.margin.units:"%"):0).css("bottom","+="+l*a+o.settings.size.units):o.progress.bar.css("top",s.position().top).css("top","-="+l*a*.5+o.settings.size.units)):s.css({width:l*a+o.settings.size.units,height:l*a+o.settings.size.units})}})}}}function f(e){return e.data("loadingoverlay")}function p(t){var a=f(t),o=void 0===a?s:a.data("loadingoverlay_data");return void 0===o?(e(".loadingoverlay").each(function(){var s=e(this),t=s.data("loadingoverlay_data");document.body.contains(t.container[0])||(t.resizeIntervalId&&clearInterval(t.resizeIntervalId),s.remove())}),!1):(a.toggle(t.is(":visible")),o)}function u(s,t,o,r,n){var l=e("<div>",{class:"loadingoverlay_element",css:{order:t}}).css(a.element).data({loadingoverlay_autoresize:o,loadingoverlay_resizefactor:r}).appendTo(s);if(!0===n&&(n=i.animations.time+" "+i.animations.name),"string"==typeof n){var g,c,d=n.replace(/\s\s+/g," ").toLowerCase().split(" ");2===d.length&&v(d[0])&&x(d[1])?(g=d[1],c=d[0]):2===d.length&&v(d[1])&&x(d[0])?(g=d[0],c=d[1]):1===d.length&&v(d[0])?(g=i.animations.name,c=d[0]):1===d.length&&x(d[0])&&(g=d[0],c=i.animations.time),l.css({"animation-name":"loadingoverlay_animation__"+g,"animation-duration":c,"animation-timing-function":"linear","animation-iteration-count":"infinite"})}return l}function v(e){return!isNaN(parseFloat(e))&&("s"===e.slice(-1)||"ms"===e.slice(-2))}function x(e){return r.animations.indexOf(e)>-1}function h(e){return r.progressPosition.indexOf(e)>-1}function y(e){return!(!e||e<0)&&("string"==typeof e&&["vmin","vmax"].indexOf(e.slice(-4))>-1?{fixed:!0,units:e.slice(-4),value:e.slice(0,-4)}:"string"==typeof e&&["rem"].indexOf(e.slice(-3))>-1?{fixed:!0,units:e.slice(-3),value:e.slice(0,-3)}:"string"==typeof e&&["px","em","cm","mm","in","pt","pc","vh","vw"].indexOf(e.slice(-2))>-1?{fixed:!0,units:e.slice(-2),value:e.slice(0,-2)}:{fixed:!1,units:"px",value:parseFloat(e)})}e.LoadingOverlaySetup=function(s){e.extend(!0,t,s)},e.LoadingOverlay=function(s,a){switch(s.toLowerCase()){case"show":n("body",e.extend(!0,{},t,a));break;case"hide":l("body",a);break;case"resize":g("body");break;case"text":c("body",a);break;case"progress":d("body",a)}},e.fn.LoadingOverlay=function(s,a){switch(s.toLowerCase()){case"show":var o=e.extend(!0,{},t,a);return this.each(function(){n(this,o)});case"hide":return this.each(function(){l(this,a)});case"resize":return this.each(function(){g(this)});case"text":return this.each(function(){c(this,a)});case"progress":return this.each(function(){d(this,a)})}},e(function(){e("head").append(["<style>","@-webkit-keyframes loadingoverlay_animation__rotate_right {","to {","-webkit-transform : rotate(360deg);","transform : rotate(360deg);","}","}","@keyframes loadingoverlay_animation__rotate_right {","to {","-webkit-transform : rotate(360deg);","transform : rotate(360deg);","}","}","@-webkit-keyframes loadingoverlay_animation__rotate_left {","to {","-webkit-transform : rotate(-360deg);","transform : rotate(-360deg);","}","}","@keyframes loadingoverlay_animation__rotate_left {","to {","-webkit-transform : rotate(-360deg);","transform : rotate(-360deg);","}","}","@-webkit-keyframes loadingoverlay_animation__fadein {","0% {","opacity : 0;","-webkit-transform : scale(0.1, 0.1);","transform : scale(0.1, 0.1);","}","50% {","opacity : 1;","}","100% {","opacity : 0;","-webkit-transform : scale(1, 1);","transform : scale(1, 1);","}","}","@keyframes loadingoverlay_animation__fadein {","0% {","opacity : 0;","-webkit-transform : scale(0.1, 0.1);","transform : scale(0.1, 0.1);","}","50% {","opacity : 1;","}","100% {","opacity : 0;","-webkit-transform : scale(1, 1);","transform : scale(1, 1);","}","}","@-webkit-keyframes loadingoverlay_animation__pulse {","0% {","-webkit-transform : scale(0, 0);","transform : scale(0, 0);","}","50% {","-webkit-transform : scale(1, 1);","transform : scale(1, 1);","}","100% {","-webkit-transform : scale(0, 0);","transform : scale(0, 0);","}","}","@keyframes loadingoverlay_animation__pulse {","0% {","-webkit-transform : scale(0, 0);","transform : scale(0, 0);","}","50% {","-webkit-transform : scale(1, 1);","transform : scale(1, 1);","}","100% {","-webkit-transform : scale(0, 0);","transform : scale(0, 0);","}","}","</style>"].join(" "))})});
