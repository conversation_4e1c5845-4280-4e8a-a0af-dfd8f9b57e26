#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-24 17:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/"

#: includes/views/permalink-manager-pro-addons.php:257
msgid ""
" Please send your remarks to <a href=\"mailto:<EMAIL>\">"
"<EMAIL></a>."
msgstr ""

#: includes/core/permalink-manager-third-parties.php:344
msgid "\"Custom Permalinks\" URIs were imported!"
msgstr ""

#: includes/views/permalink-manager-settings.php:207
msgid "\"Primary category\" support"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:180
msgid "\"Stop words\" list"
msgstr ""

#: includes/core/permalink-manager-actions.php:400
#, php-format
msgid "%d Custom URIs and %d Custom Redirects were removed!"
msgstr ""

#: includes/core/permalink-manager-actions.php:447
#, php-format
msgid "%s were removed!"
msgstr ""

#: includes/views/permalink-manager-tools.php:97
msgid "(Removed post)"
msgstr ""

#: includes/views/permalink-manager-tools.php:84
msgid "(Removed term)"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:161
msgid "-- Use predefined words list --"
msgstr ""

#: includes/views/permalink-manager-settings.php:165
msgid "301 redirect"
msgstr ""

#: includes/views/permalink-manager-settings.php:165
msgid "302 redirect"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:747
#, php-format
msgid "<a %s>Click here</a> to go to the list of updated slugs"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:746
#, php-format
msgid "<strong class=\"updated_count\">%d</strong> slug was updated!"
msgid_plural "<strong class=\"updated_count\">%d</strong> slugs were updated!"
msgstr[0] ""
msgstr[1] ""

#: includes/views/permalink-manager-tools.php:44
msgid ""
"<strong>A MySQL backup is highly recommended before using \"<em>Native "
"slugs</em>\" mode!</strong>"
msgstr ""

#: includes/views/permalink-manager-settings.php:91
msgid ""
"<strong>Canonical redirect allows WordPress to \"correct\" the requested URL "
"and redirect visitor to the canonical permalink.</strong>"
msgstr ""

#: includes/core/permalink-manager-actions.php:129
msgid "<strong>No content type</strong> selected!"
msgstr ""

#: includes/core/permalink-manager-actions.php:149
msgid "<strong>No post status</strong> selected!"
msgstr ""

#: includes/core/permalink-manager-actions.php:143
msgid "<strong>No post type</strong> selected!"
msgstr ""

#: includes/core/permalink-manager-actions.php:115
#: includes/core/permalink-manager-admin-functions.php:751
msgid "<strong>No slugs</strong> were updated!"
msgstr ""

#: includes/core/permalink-manager-actions.php:135
msgid "<strong>No taxonomy</strong> selected!"
msgstr ""

#: includes/views/permalink-manager-settings.php:108
msgid ""
"<strong>Old slug redirect is used by WordPress to provide a fallback for old "
"version of slugs after they are changed.</strong>"
msgstr ""

#: includes/views/permalink-manager-settings.php:42
msgid ""
"<strong>Permalink Manager can automatically update the custom permalink "
"after post or term is saved/updated.</strong>"
msgstr ""

#: includes/views/permalink-manager-settings.php:52
msgid ""
"<strong>Permalink Manager can use either native slugs or actual titles for "
"custom permalinks.</strong>"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:324
msgid ""
"<strong>Please use full URLs!</strong><br />For instance, <code>http:"
"//another-website.com/final-target-url</code>."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:306
#, php-format
msgid ""
"<strong>Please use URIs only!</strong><br />For instance, to set-up a "
"redirect for <code>%s/old-uri</code> please use <code>old-uri</code>."
msgstr ""

#: includes/views/permalink-manager-settings.php:157
msgid ""
"<strong>You can use Permalink Manager to force SSL or \"www\" prefix in "
"WordPress permalinks.</strong>"
msgstr ""

#: includes/views/permalink-manager-settings.php:64
msgid ""
"<strong>You can use this feature to either add or remove the slases from end "
"of WordPress permalinks.</strong>"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:301
msgid "Add new redirect"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:166
msgid "Add the words from the list"
msgstr ""

#: includes/views/permalink-manager-settings.php:61
msgid "Add trailing slashes"
msgstr ""

#: includes/views/permalink-manager-settings.php:244
msgid "Administrator (edit_theme_options)"
msgstr ""

#. Description of the plugin
msgid ""
"Advanced plugin that allows to set-up custom permalinks (bulk editors "
"included), slugs and permastructures (WooCommerce compatible)."
msgstr ""

#: includes/views/permalink-manager-settings.php:214
msgid "Advanced settings"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:83
#, php-format
msgid ""
"All allowed <a href=\"%s\" target=\"_blank\">permastructure tags</a> are "
"listed below. Please note that some of them can be used only for particular "
"post types or taxonomies."
msgstr ""

#: includes/views/permalink-manager-uri-editor-post.php:174
msgid "All dates"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:278
msgid ""
"All URIs specified below will redirect the visitors to the custom URI "
"defined above in \"Current URI\" field."
msgstr ""

#: includes/views/permalink-manager-uri-editor.php:83
msgid "Apply"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:196
msgid "Arabic"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:135
msgid "Are you sure? This action cannot be undone!"
msgstr ""

#: includes/views/permalink-manager-debug.php:58
msgid "Array with external redirects"
msgstr ""

#: includes/views/permalink-manager-debug.php:69
msgid "Array with permastructures"
msgstr ""

#: includes/views/permalink-manager-debug.php:48
msgid "Array with redirects"
msgstr ""

#: includes/views/permalink-manager-debug.php:80
msgid "Array with settings used in this plugin."
msgstr ""

#: includes/views/permalink-manager-debug.php:37
msgid "Array with URIs"
msgstr ""

#: includes/views/permalink-manager-settings.php:244
msgid "Author (publish_posts)"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:902
msgid "Auto-update \"Current URI\""
msgstr ""

#: includes/views/permalink-manager-settings.php:39
msgid "Auto-update permalinks"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:935
msgid "Automatic redirect for native URI enabled:"
msgstr ""

#: includes/views/permalink-manager-settings.php:249
msgid "Automatically fix broken URIs"
msgstr ""

#: includes/views/permalink-manager-settings.php:201
msgid "Breadcrumbs support"
msgstr ""

#: includes/core/permalink-manager-actions.php:596
msgid "Broken redirects were removed successfully!"
msgstr ""

#: includes/views/permalink-manager-settings.php:251
msgid "Bulk fix all URIs (once a day, in the background)"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:173
msgid "Buy Permalink Manager Pro"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:614
msgid "by Maciej Bis"
msgstr ""

#: includes/views/permalink-manager-settings.php:88
msgid "Canonical redirect"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:197
msgid "Chinese"
msgstr ""

#: includes/core/permalink-manager-uri-functions-tax.php:504
msgid "Clear/leave the field empty to use the default permalink."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:874
msgid "Close: "
msgstr ""

#: includes/views/permalink-manager-tools.php:120
msgid "Congratulations! No duplicated URIs or Redirects found!"
msgstr ""

#: includes/views/permalink-manager-settings.php:244
msgid "Contributor (edit_posts)"
msgstr ""

#: includes/views/permalink-manager-settings.php:236
msgid "Convert accented letters"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:53
msgid "Count"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:541
msgid "Coupon Full URL"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:508
msgid "Coupon Link"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:529
msgid "Coupon URI"
msgstr ""

#: includes/core/permalink-manager-uri-functions-post.php:700
#: includes/core/permalink-manager-uri-functions-tax.php:536
#: includes/core/permalink-manager-admin-functions.php:793
#: includes/core/permalink-manager-admin-functions.php:893
msgid "Current URI"
msgstr ""

#: includes/views/permalink-manager-tools.php:34
#: includes/views/permalink-manager-pro-addons.php:216
msgid "Custom Permalinks"
msgstr ""

#: includes/core/permalink-manager-actions.php:420
msgid "Custom permalinks"
msgstr ""

#: includes/core/permalink-manager-actions.php:424
msgid "Custom redirects"
msgstr ""

#: includes/views/permalink-manager-tools.php:70
#: includes/core/permalink-manager-third-parties.php:476
#: includes/core/permalink-manager-third-parties.php:813
#: includes/core/permalink-manager-uri-functions-tax.php:503
msgid "Custom URI"
msgstr ""

#: includes/views/permalink-manager-tools.php:150
msgid "Custom URIs"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:198
msgid "Danish"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:217
msgid "Deactivate after import"
msgstr ""

#: includes/views/permalink-manager-debug.php:14
msgid "Debug"
msgstr ""

#: includes/views/permalink-manager-debug.php:28
msgid "Debug data"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:419
msgid "Default permastructure"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:924
msgid "Default URI"
msgstr ""

#: includes/views/permalink-manager-settings.php:251
msgid "Disable"
msgstr ""

#: includes/views/permalink-manager-settings.php:165
msgid "Disable (Permalink Manager redirect functions)"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:427
msgid "Do not automatically append the slug"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:169
msgid "Documentation"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:613
msgid "Donate"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:199
msgid "Dutch"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:194
msgid ""
"Each of the words can be removed and any new words can be added to the list. "
"You can also use a predefined list (available in 21 languages)."
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:100
#: includes/views/permalink-manager-uri-editor-post.php:120
#: includes/views/permalink-manager-uri-editor-post.php:120
msgid "Edit"
msgstr ""

#: includes/views/permalink-manager-tools.php:94
msgid "Edit post"
msgstr ""

#: includes/views/permalink-manager-tools.php:81
msgid "Edit term"
msgstr ""

#: includes/views/permalink-manager-settings.php:244
msgid "Editor (publish_pages)"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:174
msgid "Enable \"stop words\""
msgstr ""

#: includes/views/permalink-manager-settings.php:253
msgid ""
"Enable this option if you would like to automatically remove redundant "
"permalinks & duplicated redirects."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:200
msgid "English"
msgstr ""

#: includes/views/permalink-manager-settings.php:149
msgid ""
"Example: <em>https://example.com/product/old-product-url/<strong>?discount-"
"code=blackfriday</strong></em> => <em>https://example.com/new-product-"
"url/<strong>?discount-code=blackfriday</strong></em>"
msgstr ""

#: includes/views/permalink-manager-settings.php:70
msgid "Exclude content types"
msgstr ""

#: includes/views/permalink-manager-settings.php:76
msgid "Exclude drafts"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:159
msgid ""
"Expiration date could not be downloaded at this moment. Please try again in "
"a few minutes."
msgstr ""

#: includes/core/permalink-manager-actions.php:428
msgid "External redirects"
msgstr ""

#: includes/views/permalink-manager-tools.php:70
msgid "Extra Redirect"
msgstr ""

#: includes/views/permalink-manager-settings.php:114
#: includes/views/permalink-manager-pro-addons.php:277
msgid "Extra redirects (aliases)"
msgstr ""

#: includes/views/permalink-manager-uri-editor-post.php:183
msgid "Filter"
msgstr ""

#: includes/views/permalink-manager-tools.php:22
msgid "Find & Replace"
msgstr ""

#: includes/views/permalink-manager-tools.php:134
msgid "Find ..."
msgstr ""

#: includes/views/permalink-manager-tools.php:209
msgid "Find and replace"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:201
msgid "Finnish"
msgstr ""

#: includes/views/permalink-manager-tools.php:55
msgid "Fix custom permalinks & redirects"
msgstr ""

#: includes/views/permalink-manager-settings.php:251
msgid "Fix URIs individually (during page load)"
msgstr ""

#: includes/views/permalink-manager-settings.php:224
msgid "Force 404 on non-existing pagination pages"
msgstr ""

#: includes/views/permalink-manager-settings.php:154
msgid "Force HTTPS/WWW"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:202
msgid "French"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:52
#: includes/views/permalink-manager-uri-editor-post.php:56
msgid "Full URI & Permalink"
msgstr ""

#: includes/views/permalink-manager-settings.php:33
msgid "General settings"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:203
msgid "German"
msgstr ""

#: permalink-manager.php:212
#, php-format
msgid ""
"Get access to extra features: full taxonomy and WooCommerce support, "
"possibility to use custom fields inside the permalinks and more!<br />"
"<strong>Buy Permalink Manager Pro <a href=\"%s\" target=\"_blank\">here</a> "
"and save %s using \"%s\" coupon code!</strong> Valid until %s!"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:134
msgid "Get license information"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:204
msgid "Hebrew"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:205
msgid "Hindi"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:320
msgid "http://another-website.com/final-target-url"
msgstr ""

#. Author URI of the plugin
msgid "http://maciejbis.net/"
msgstr ""

#. URI of the plugin
msgid "https://permalinkmanager.pro?utm_source=plugin"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:894
msgid ""
"If custom URI is not defined, a default URI will be set (see below). The "
"custom URI can be edited only if 'Auto-update the URI' feature is not "
"enabled."
msgstr ""

#: includes/views/permalink-manager-settings.php:190
msgid ""
"If disabled, the custom permalinks <strong>will not be saved</strong> for "
"the posts imported with WP All Import plugin."
msgstr ""

#: includes/core/permalink-manager-third-parties.php:814
#, php-format
msgid ""
"If empty, a default permalink based on your current <a href=\"%s\" "
"target=\"_blank\">permastructure settings</a> will be used."
msgstr ""

#: includes/views/permalink-manager-settings.php:232
msgid ""
"If enabled only alphanumeric characters, underscores and dashes will be "
"allowed for post/term slugs."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:193
msgid ""
"If enabled, all selected \"stop words\" will be automatically removed from "
"default URIs."
msgstr ""

#: includes/views/permalink-manager-settings.php:239
msgid ""
"If enabled, all the accented letters will be replaced with their non-"
"accented equivalent (eg. Å => A, Æ => AE, Ø => O, Ć => C)."
msgstr ""

#: includes/views/permalink-manager-settings.php:43
msgid ""
"If enabled, Permalink Manager will always force the default custom permalink "
"format (based on current <strong>Permastructure</strong> settings)."
msgstr ""

#: includes/views/permalink-manager-settings.php:197
msgid ""
"If enabled, Permalink Manager will detect the additional Ultimate Member "
"pages (eg. \"account\" sections)."
msgstr ""

#: includes/views/permalink-manager-settings.php:130
msgid ""
"If enabled, Permalink Manager will save the \"extra redirect\" for earlier "
"version of custom permalink after you change it (eg. with URI Editor or "
"Regenerate/reset tool)."
msgstr ""

#: includes/views/permalink-manager-settings.php:209
msgid ""
"If enabled, Permalink Manager will use the \"primary category\" for the "
"default post permalinks.<br />Works with: <strong>Yoast SEO, The SEO "
"Framework, RankMath and SEOPress</strong>."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:903
msgid ""
"If enabled, the 'Current URI' field will be automatically changed to "
"'Default URI' (displayed below) after the post is saved or updated."
msgstr ""

#: includes/views/permalink-manager-settings.php:77
msgid "If enabled, the custom permalinks for post drafts will not be saved."
msgstr ""

#: includes/views/permalink-manager-settings.php:203
msgid ""
"If enabled, the HTML breadcrumbs will be filtered by Permalink Manager to "
"mimic the current URL structure.<br />Works with: <strong>WooCommerce, Yoast "
"SEO, Slim Seo, RankMath and SEOPress</strong> breadcrumbs."
msgstr ""

#: includes/views/permalink-manager-settings.php:225
msgid ""
"If enabled, the non-existing pagination pages (for single posts) will return "
"404 (\"Not Found\") error.<br /><strong>Please disable it, if you encounter "
"any problems with pagination pages or use custom pagination system.</strong>"
msgstr ""

#: includes/views/permalink-manager-settings.php:183
msgid ""
"If enabled, the plugin will load the adjacent translation of post when the "
"custom permalink is detected, but the language code in the URL does not "
"match the language code assigned to the post/term."
msgstr ""

#: includes/views/permalink-manager-settings.php:148
msgid ""
"If enabled, the query parameters will be copied to the target URL when the "
"redirect is triggered."
msgstr ""

#: includes/views/permalink-manager-settings.php:109
msgid ""
"If enabled, the visitors trying to access the URL with the old slug will be "
"redirected to the canonical permalink."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:316
msgid ""
"If not empty, the visitors trying to access this page will be redirected to "
"the URL specified below."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:220
msgid ""
"If selected, \"Custom Permalinks\" plugin will be deactivated after its "
"custom URIs are imported."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:252
msgid ""
"If you still did not find the answer to your question, please send us your "
"question or a detailed description of your problem/issue to <a href=\"mailto:"
"<EMAIL>\"><EMAIL></a>."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:256
msgid ""
"If you would like to suggest a new functionality or leave us feedback, we "
"are open to all new ideas and would be grateful for all your comments!"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:402
msgid ""
"If you would like to translate the permastructures and set-up different "
"permalink structure per language, please fill in the fields below. Otherwise "
"the permastructure set for default language (see field above) will be "
"applied."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:232
#, php-format
msgid "Import %d URIs"
msgstr ""

#: includes/views/permalink-manager-tools.php:206
#: includes/views/permalink-manager-tools.php:283
msgid "Important notices"
msgstr ""

#: includes/views/permalink-manager-uri-editor-post.php:89
msgid "Inherit (Attachment)"
msgstr ""

#: includes/views/permalink-manager-settings.php:50
msgid "Inherit parents' slugs"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:75
#: includes/views/permalink-manager-pro-addons.php:192
#: includes/views/permalink-manager-pro-addons.php:225
msgid "Instructions"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:206
msgid "Italian"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:207
msgid "Japanese"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:208
msgid "Korean"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:121
msgid "Licence"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:127
msgid "Licence key"
msgstr ""

#: includes/views/permalink-manager-debug.php:44
msgid "List of custom redirects set-up by this plugin."
msgstr ""

#: includes/views/permalink-manager-tools.php:54
msgid "List of duplicated permalinks"
msgstr ""

#: includes/views/permalink-manager-debug.php:55
msgid "List of external redirects set-up by this plugin."
msgstr ""

#: includes/views/permalink-manager-debug.php:65
msgid "List of permastructures set-up by this plugin."
msgstr ""

#: includes/views/permalink-manager-debug.php:76
msgid "List of plugin settings."
msgstr ""

#: includes/views/permalink-manager-debug.php:33
msgid "List of the URIs generated by this plugin."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:733
msgid "List of updated items"
msgstr ""

#. Author of the plugin
msgid "Maciej Bis"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:976
msgid "Manage redirects"
msgstr ""

#: includes/views/permalink-manager-tools.php:146
#: includes/views/permalink-manager-tools.php:222
msgid "Mode"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:914
msgid "Native slug"
msgstr ""

#: includes/views/permalink-manager-tools.php:151
msgid "Native slugs"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:696
#: includes/core/permalink-manager-admin-functions.php:723
msgid "New Slug"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:699
#: includes/core/permalink-manager-admin-functions.php:726
msgid "New URI"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:855
#: includes/core/permalink-manager-admin-functions.php:859
msgid "No"
msgstr ""

#: includes/core/permalink-manager-third-parties.php:347
msgid "No \"Custom Permalinks\" URIs were imported!"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:860
msgid "No (ignore this URI in bulk tools)"
msgstr ""

#: includes/core/permalink-manager-actions.php:402
msgid "No Custom URIs or Custom Redirects were removed!"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:237
msgid "No custom URIs to import"
msgstr ""

#: includes/views/permalink-manager-settings.php:238
msgid "No, keep accented letters in the slugs"
msgstr ""

#: includes/views/permalink-manager-settings.php:231
msgid "No, keep special characters (.,|_+) in the slugs"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:209
msgid "Norwegian"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:695
#: includes/core/permalink-manager-admin-functions.php:722
msgid "Old Slug"
msgstr ""

#: includes/views/permalink-manager-settings.php:105
msgid "Old slug redirect"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:698
#: includes/core/permalink-manager-admin-functions.php:725
msgid "Old URI"
msgstr ""

#: includes/views/permalink-manager-settings.php:245
#, php-format
msgid ""
"Only the users who have selected capability will be able to access URI "
"Editor.<br />The list of capabilities <a href=\"%s\" target=\"_blank\">can "
"be found here</a>."
msgstr ""

#: includes/views/permalink-manager-uri-editor.php:89
msgid "Per page"
msgstr ""

#: includes/views/permalink-manager-tools.php:18
msgid "Permalink Duplicates"
msgstr ""

#: includes/core/permalink-manager-third-parties.php:809
#: includes/core/permalink-manager-third-parties.php:954
#: includes/core/permalink-manager-gutenberg.php:33
#: includes/core/permalink-manager-admin-functions.php:111
#: includes/core/permalink-manager-admin-functions.php:111
#: includes/core/permalink-manager-admin-functions.php:789
#: includes/core/permalink-manager-admin-functions.php:869
#: includes/core/permalink-manager-admin-functions.php:874
#: includes/core/permalink-manager-admin-functions.php:875
msgid "Permalink Manager"
msgstr ""

#: includes/views/permalink-manager-settings.php:139
msgid ""
"Permalink Manager can force the trailing slashes settings in the custom "
"permalinks with redirect."
msgstr ""

#: includes/views/permalink-manager-settings.php:167
msgid ""
"Permalink Manager includes a set of hooks that allow to extend the redirect "
"functions used natively by WordPress to avoid 404 errors."
msgstr ""

#. Name of the plugin
msgid "Permalink Manager Pro"
msgstr ""

#: includes/views/permalink-manager-settings.php:72
msgid ""
"Permalink Manager will ignore and not filter the custom permalinks of all "
"selected above post types & taxonomies."
msgstr ""

#: includes/core/permalink-manager-actions.php:432
#: includes/core/permalink-manager-actions.php:436
#: includes/core/permalink-manager-admin-functions.php:426
msgid "Permastructure settings"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:82
msgid "Permastructure tags"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:401
msgid "Permastructure translations"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:15
msgid "Permastructures"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:210
msgid "Persian"
msgstr ""

#: includes/views/permalink-manager-settings.php:158
msgid "Please disable it if you encounter any redirect loop issues."
msgstr ""

#: includes/views/permalink-manager-settings.php:119
msgid ""
"Please enable this option if you would like to manage additional custom "
"redirects (aliasees) in URI Editor for individual posts & terms."
msgstr ""

#: includes/views/permalink-manager-settings.php:140
msgid ""
"Please go to \"<em>General settings -> Trailing slashes</em>\" to choose if "
"trailing slashes should be added or removed from WordPress permalinks."
msgstr ""

#: includes/views/permalink-manager-settings.php:65
msgid ""
"Please go to \"<em>Redirect settings -> Trailing slashes redirect</em>\" to "
"force the trailing slashes mode with redirect."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:226
msgid ""
"Please note that \"Custom Permalinks\" (if activated) may break the behavior "
"of this plugin."
msgstr ""

#: includes/views/permalink-manager-settings.php:131
msgid ""
"Please note that the new redirects will be saved only if \"Extra redirects "
"(aliases)\" option is turned on above."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:134
#, php-format
msgid ""
"Please paste the licence key to access all Permalink Manager Pro updates & "
"features <a href=\"%s\" target=\"_blank\">on this page</a>."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:211
msgid "Polish"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:212
msgid "Portuguese"
msgstr ""

#: includes/views/permalink-manager-uri-editor-post.php:115
msgid "Post status"
msgstr ""

#: includes/views/permalink-manager-uri-editor.php:94
msgid "Post statuses"
msgstr ""

#: includes/views/permalink-manager-uri-editor-post.php:55
msgid "Post title"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:31
#: includes/views/permalink-manager-tools.php:162
#: includes/views/permalink-manager-tools.php:239
#: includes/core/permalink-manager-admin-functions.php:262
msgid "Post types"
msgstr ""

#: includes/views/permalink-manager-settings.php:163
msgid "Redirect mode"
msgstr ""

#: includes/views/permalink-manager-settings.php:82
msgid "Redirect settings"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:315
msgid "Redirect this page to external URL"
msgstr ""

#: includes/views/permalink-manager-settings.php:145
msgid "Redirect with query parameters"
msgstr ""

#: includes/views/permalink-manager-tools.php:286
msgid "Regenerate"
msgstr ""

#: includes/views/permalink-manager-tools.php:226
msgid "Regenerate custom permalinks"
msgstr ""

#: includes/views/permalink-manager-tools.php:227
msgid "Regenerate native slugs"
msgstr ""

#: includes/views/permalink-manager-tools.php:26
msgid "Regenerate/Reset"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:132
msgid "Reload the expiration date"
msgstr ""

#: includes/views/permalink-manager-debug.php:35
msgid "Remove all custom permalinks"
msgstr ""

#: includes/views/permalink-manager-debug.php:46
msgid "Remove all custom redirects"
msgstr ""

#: includes/views/permalink-manager-debug.php:56
msgid "Remove all external redirects"
msgstr ""

#: includes/views/permalink-manager-debug.php:67
msgid "Remove all permastructures settings"
msgstr ""

#: includes/views/permalink-manager-debug.php:78
msgid "Remove all plugin settings"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:164
msgid "Remove all words"
msgstr ""

#: includes/views/permalink-manager-tools.php:85
#: includes/views/permalink-manager-tools.php:98
msgid "Remove broken URI"
msgstr ""

#: includes/views/permalink-manager-settings.php:61
msgid "Remove trailing slashes"
msgstr ""

#: includes/views/permalink-manager-tools.php:140
msgid "Replace with ..."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:420
msgid "Restore default permastructure"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:925
msgid "Restore Default URI"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:213
msgid "Russian"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:284
msgid "sample/custom-uri"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:196
msgid "Save"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:147
#: includes/views/permalink-manager-uri-editor-post.php:150
msgid "Save all the URIs above"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:146
#: includes/views/permalink-manager-uri-editor-post.php:149
msgid "Save all the URIs below"
msgstr ""

#: includes/views/permalink-manager-settings.php:125
msgid "Save old custom permalinks as extra redirects"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:949
msgid "Save permalink"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:86
msgid "Save permastructures"
msgstr ""

#: includes/views/permalink-manager-settings.php:260
msgid "Save settings"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:155
#: includes/views/permalink-manager-uri-editor-post.php:158
msgid "Search"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:285
msgid "Select all"
msgstr ""

#: includes/views/permalink-manager-tools.php:155
#: includes/views/permalink-manager-tools.php:232
msgid "Select content type"
msgstr ""

#: includes/views/permalink-manager-tools.php:196
#: includes/views/permalink-manager-tools.php:273
msgid "Select IDs"
msgstr ""

#: includes/views/permalink-manager-tools.php:187
#: includes/views/permalink-manager-tools.php:264
msgid "Select post statuses"
msgstr ""

#: includes/views/permalink-manager-tools.php:167
#: includes/views/permalink-manager-tools.php:244
msgid "Select post types"
msgstr ""

#: includes/views/permalink-manager-tools.php:176
#: includes/views/permalink-manager-tools.php:253
msgid "Select taxonomies"
msgstr ""

#: includes/views/permalink-manager-settings.php:14
#: includes/core/permalink-manager-admin-functions.php:157
msgid "Settings"
msgstr ""

#: includes/views/permalink-manager-settings.php:220
msgid "Show \"Native slug\" field in URI Editor"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:435
msgid "Show additional settings"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:720
msgid "Show more details"
msgstr ""

#: includes/core/permalink-manager-actions.php:682
msgid "Sitemaps were updated!"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:95
#: includes/views/permalink-manager-uri-editor-post.php:114
msgid "Slug"
msgstr ""

#: includes/views/permalink-manager-settings.php:48
msgid "Slugs mode"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:214
msgid "Spanish"
msgstr ""

#: includes/views/permalink-manager-tools.php:30
msgid "Stop Words"
msgstr ""

#: includes/views/permalink-manager-settings.php:229
msgid "Strip special characters"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:255
msgid "Suggestions/feedback"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:101
msgid "Support"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:215
msgid "Swedish"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:36
#: includes/views/permalink-manager-tools.php:163
#: includes/views/permalink-manager-tools.php:240
#: includes/core/permalink-manager-admin-functions.php:262
msgid "Taxonomies"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:250
msgid "Technical support"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:51
msgid "Term title"
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:112
#: includes/views/permalink-manager-uri-editor-post.php:101
msgid ""
"The above permalink will be automatically updated and is locked for editing."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:889
msgid ""
"The above permalink will be automatically updated to \"Default URI\" and is "
"locked for editing."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:886
msgid "The custom URI cannot be edited on frontpage."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:915
msgid ""
"The native slug is by default automatically used in native permalinks (when "
"Permalink Manager is disabled)."
msgstr ""

#: includes/views/permalink-manager-settings.php:53
msgid ""
"The native slug is generated from the initial title after the post or term "
"is published."
msgstr ""

#: includes/core/permalink-manager-actions.php:620
msgid "The redirect was removed successfully!"
msgstr ""

#: includes/core/permalink-manager-actions.php:105
msgid "The settings are saved!"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:530
msgid ""
"The URIs are case-insensitive, eg. <strong>BLACKFRIDAY</strong> and <strong>"
"blackfriday</strong> are equivalent."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:227
msgid ""
"Therefore, it is recommended to disable \"Custom Permalink\" and import old "
"permalinks before using Permalink Manager Pro."
msgstr ""

#: includes/views/permalink-manager-settings.php:174
msgid "Third party plugins"
msgstr ""

#: includes/views/permalink-manager-settings.php:92
msgid ""
"This feature will be also used to redirect (old) original permalinks to (new)"
" custom permalinks set with Permalink Manager."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:1094
#, php-format
msgid ""
"This functionality is available only in <a href=\"%s\" target=\"_blank\">"
"Permalink Manager Pro</a>."
msgstr ""

#: includes/views/permalink-manager-settings.php:62
msgid ""
"This option can be used to alter the native settings and control if trailing "
"slash should be added or removed from the end of posts & terms permalinks."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:693
#: includes/core/permalink-manager-admin-functions.php:720
msgid "Title"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:79
#, php-format
msgid ""
"To apply the <strong>new format to existing posts and terms</strong>, please "
"use \"<a href=\"%s\">Regenerate/reset</a>\" tool after you update the "
"permastructure settings below."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:251
#, php-format
msgid ""
"To find the answers on frequently asked questions and information about how "
"to deal with the most common issues please go to the <strong>Knowledge "
"Base</strong> using <a target=\"_blank\" href=\"%s\">this link</a>."
msgstr ""

#: includes/views/permalink-manager-tools.php:200
#: includes/views/permalink-manager-tools.php:277
msgid ""
"To narrow the above filters you can type the post IDs (or ranges) here. Eg. "
"<strong>1-8, 10, 25</strong>."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:253
msgid ""
"To reduce the response time, please attach your licence key and if possible "
"also: URL address of your website and screenshots explaining the issue."
msgstr ""

#: includes/views/permalink-manager-tools.php:15
msgid "Tools"
msgstr ""

#: includes/views/permalink-manager-settings.php:59
msgid "Trailing slashes"
msgstr ""

#: includes/views/permalink-manager-settings.php:136
msgid "Trailing slashes redirect"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:216
msgid "Turkish"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:270
msgid ""
"Turn on \"<strong>Extra redirects (aliases)</strong>\" in Permalink Manager "
"settings to enable this feature."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:184
msgid "Type comma to separate the words."
msgstr ""

#: includes/views/permalink-manager-settings.php:194
msgid "Ultimate Member support"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:286
msgid "Unselect all"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:626
msgid "Upgrade to PRO"
msgstr ""

#: includes/core/permalink-manager-actions.php:588
#, php-format
msgid "URI \"%s\" was removed successfully!"
msgstr ""

#: includes/core/permalink-manager-actions.php:600
msgid "URI and/or custom redirects does not exist or were already removed!"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:156
msgid "URI Editor"
msgstr ""

#: includes/views/permalink-manager-uri-editor.php:28
msgid "URI editor"
msgstr ""

#: includes/views/permalink-manager-settings.php:243
msgid "URI Editor role capability"
msgstr ""

#: includes/core/permalink-manager-actions.php:650
msgid "URI is already in use, please select another one!"
msgstr ""

#: includes/views/permalink-manager-settings.php:50
msgid "Use actual titles as slugs"
msgstr ""

#: includes/views/permalink-manager-settings.php:61
msgid "Use default settings"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:857
#, php-format
msgid "Use global settings [%s]"
msgstr ""

#: includes/views/permalink-manager-settings.php:50
msgid "Use native slugs"
msgstr ""

#: includes/views/permalink-manager-tools.php:228
msgid "Use original URLs as custom permalinks"
msgstr ""

#: includes/views/permalink-manager-settings.php:54
msgid ""
"Use this field if you would like Permalink Manager to use the actual titles "
"instead of native slugs."
msgstr ""

#: includes/views/permalink-manager-uri-editor-tax.php:101
#: includes/views/permalink-manager-uri-editor-post.php:121
#: includes/views/permalink-manager-uri-editor-post.php:121
msgid "View"
msgstr ""

#: includes/views/permalink-manager-permastructs.php:46
msgid "WooCommerce"
msgstr ""

#: includes/views/permalink-manager-settings.php:187
msgid "WP All Import support"
msgstr ""

#: includes/views/permalink-manager-settings.php:180
msgid "WPML/Polylang language mismatch"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:855
#: includes/core/permalink-manager-admin-functions.php:858
msgid "Yes"
msgstr ""

#: includes/views/permalink-manager-settings.php:231
#: includes/views/permalink-manager-settings.php:238
msgid "Yes, use native settings"
msgstr ""

#: includes/core/permalink-manager-actions.php:414
msgid "You are not allowed to remove Permalink Manager data!"
msgstr ""

#: includes/views/permalink-manager-settings.php:168
msgid ""
"You can disable this feature if you do not want Permalink Manager to trigger "
"any additional redirect functions at all."
msgstr ""

#: includes/views/permalink-manager-settings.php:120
msgid ""
"You can disable this feature if you use another plugin to control the "
"redirects, eg. Yoast SEO Premium or Redirection."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:149
msgid "You own a lifetime licence key."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:154
#, php-format
msgid ""
"Your licence key is valid until %s.<br />To prolong it please go to <a "
"href=\"%s\" target=\"_blank\">this page</a> for more information."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:144
#, php-format
msgid ""
"Your Permalink Manager Pro licence key expired! To restore access to plugin "
"updates & technical support please go to <a href=\"%s\" target=\"_blank\">"
"this page</a>."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:139
msgid "Your Permalink Manager Pro licence key is invalid!"
msgstr ""
