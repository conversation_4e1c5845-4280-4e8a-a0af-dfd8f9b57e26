msgid ""
msgstr ""
"Project-Id-Version: Permalink Manager Lite\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-23 20:44+0000\n"
"PO-Revision-Date: 2021-10-24 17:15+0000\n"
"Last-Translator: admin\n"
"Language-Team: Japanese\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.4.4; wp-5.5.3"

#: includes/views/permalink-manager-pro-addons.php:257
msgid ""
" Please send your remarks to <a href=\"mailto:<EMAIL>\">"
"<EMAIL></a>."
msgstr ""
"<a href=\"mailto:<EMAIL>\">contact@permalinkmanager."
"pro</a>まで、ご意見をお送り下さい。"

#: includes/core/permalink-manager-third-parties.php:344
msgid "\"Custom Permalinks\" URIs were imported!"
msgstr "「カスタム パーマリンク」URIは、インポートされました！"

#: includes/views/permalink-manager-settings.php:207
msgid "\"Primary category\" support"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:180
msgid "\"Stop words\" list"
msgstr "「使用禁止の単語」リスト"

#: includes/core/permalink-manager-actions.php:400
#, php-format
msgid "%d Custom URIs and %d Custom Redirects were removed!"
msgstr "%d カスタムURIと、%d カスタム リダイレクトは削除されました。"

#: includes/core/permalink-manager-actions.php:447
#, php-format
msgid "%s were removed!"
msgstr "%s は削除されました！"

#: includes/views/permalink-manager-tools.php:97
msgid "(Removed post)"
msgstr "（削除された投稿）"

#: includes/views/permalink-manager-tools.php:84
msgid "(Removed term)"
msgstr "（削除されたターム）"

#: includes/views/permalink-manager-pro-addons.php:161
msgid "-- Use predefined words list --"
msgstr "-- 定義済みの単語リストを使用する --"

#: includes/views/permalink-manager-settings.php:165
msgid "301 redirect"
msgstr "301 リダイレクト"

#: includes/views/permalink-manager-settings.php:165
msgid "302 redirect"
msgstr "302 リダイレクト"

#: includes/core/permalink-manager-admin-functions.php:747
#, php-format
msgid "<a %s>Click here</a> to go to the list of updated slugs"
msgstr "更新されたスラッグのリストは、<a %s>ここをクリック</a> "

#: includes/core/permalink-manager-admin-functions.php:746
#, php-format
msgid "<strong class=\"updated_count\">%d</strong> slug was updated!"
msgid_plural "<strong class=\"updated_count\">%d</strong> slugs were updated!"
msgstr[0] "<strong class=\"updated_count\">%d</strong> スラッグは、更新されました！"
msgstr[1] ""

#: includes/views/permalink-manager-tools.php:44
msgid ""
"<strong>A MySQL backup is highly recommended before using \"<em>Native "
"slugs</em>\" mode!</strong>"
msgstr ""
"<strong>「<em>ネイティブ スラッグ</em>」モード使用される前に、MY SQLのバックアップを強く推奨します！</strong>"

#: includes/views/permalink-manager-settings.php:91
msgid ""
"<strong>Canonical redirect allows WordPress to \"correct\" the requested URL "
"and redirect visitor to the canonical permalink.</strong>"
msgstr ""
"<strong>"
"正規リダイレクトにより、WordPressは要求されたURLを「修正」し、訪問者を正規パーマリンクにリダイレクトできます。</strong>"

#: includes/core/permalink-manager-actions.php:129
msgid "<strong>No content type</strong> selected!"
msgstr ""

#: includes/core/permalink-manager-actions.php:149
msgid "<strong>No post status</strong> selected!"
msgstr ""

#: includes/core/permalink-manager-actions.php:143
msgid "<strong>No post type</strong> selected!"
msgstr ""

#: includes/core/permalink-manager-actions.php:115
#: includes/core/permalink-manager-admin-functions.php:751
msgid "<strong>No slugs</strong> were updated!"
msgstr "スラッグは、更新されませんでした。"

#: includes/core/permalink-manager-actions.php:135
msgid "<strong>No taxonomy</strong> selected!"
msgstr ""

#: includes/views/permalink-manager-settings.php:108
msgid ""
"<strong>Old slug redirect is used by WordPress to provide a fallback for old "
"version of slugs after they are changed.</strong>"
msgstr ""
"<strong>古いスラッグ "
"リダイレクトは、WordPressが古いバージョンのスラッグを変更した後の代替を提供するために使用されます。</strong>"

#: includes/views/permalink-manager-settings.php:42
msgid ""
"<strong>Permalink Manager can automatically update the custom permalink "
"after post or term is saved/updated.</strong>"
msgstr ""
"<strong>Permalink Managerは、投稿またはタームが保存・更新された後、カスタム "
"パーマリンクを自動的に更新できます。</strong>"

#: includes/views/permalink-manager-settings.php:52
msgid ""
"<strong>Permalink Manager can use either native slugs or actual titles for "
"custom permalinks.</strong>"
msgstr ""
"<strong>Permalink Managerでは、カスタム パーマリンクにネイティブ スラッグまたは実際のタイトルを使用できます。</strong>"

#: includes/views/permalink-manager-pro-addons.php:324
msgid ""
"<strong>Please use full URLs!</strong><br />For instance, <code>http:"
"//another-website.com/final-target-url</code>."
msgstr ""
"<strong>全てのURLをお使い下さい！</strong><br />例：<code>http://another-website."
"com/final-target-url</code>."

#: includes/views/permalink-manager-pro-addons.php:306
#, php-format
msgid ""
"<strong>Please use URIs only!</strong><br />For instance, to set-up a "
"redirect for <code>%s/old-uri</code> please use <code>old-uri</code>."
msgstr ""
"<strong>URIのみご利用下さい！</strong><br />例えば、<code>%s/old-uri</code> "
"のリダイレクトを設定する場合は、<code>old-uri</code> をお使い下さい。"

#: includes/views/permalink-manager-settings.php:157
msgid ""
"<strong>You can use Permalink Manager to force SSL or \"www\" prefix in "
"WordPress permalinks.</strong>"
msgstr ""
"<strong>Permalink "
"Managerを使って、WordPressのパーマリンク内にSSLもしくはwwwの接頭辞を強制的に付与できます。</strong>"

#: includes/views/permalink-manager-settings.php:64
msgid ""
"<strong>You can use this feature to either add or remove the slases from end "
"of WordPress permalinks.</strong>"
msgstr "<strong>この機能で、WordPressパーマリンクの末尾にスラッシュを追加または削除できます。</strong>"

#: includes/views/permalink-manager-pro-addons.php:301
msgid "Add new redirect"
msgstr "新しいリダイレクトを追加する"

#: includes/views/permalink-manager-pro-addons.php:166
msgid "Add the words from the list"
msgstr "リストから単語を追加する"

#: includes/views/permalink-manager-settings.php:61
msgid "Add trailing slashes"
msgstr "末尾のスラッシュを追加する"

#: includes/views/permalink-manager-settings.php:244
msgid "Administrator (edit_theme_options)"
msgstr "管理者 (edit_theme_options)"

#. Description of the plugin
msgid ""
"Advanced plugin that allows to set-up custom permalinks (bulk editors "
"included), slugs and permastructures (WooCommerce compatible)."
msgstr "有償版では、スラッグやパーマ構造（WooCommerceとの互換性あり）、カスタム パーマリンク（一括編集も含む）の設定が可能です。"

#: includes/views/permalink-manager-settings.php:214
msgid "Advanced settings"
msgstr "詳細設定"

#: includes/views/permalink-manager-permastructs.php:83
#, php-format
msgid ""
"All allowed <a href=\"%s\" target=\"_blank\">permastructure tags</a> are "
"listed below. Please note that some of them can be used only for particular "
"post types or taxonomies."
msgstr ""
"以下は、全て使用可能な<a href=\"%s\" target=\"_blank\">パーマ構造 タグ</a>"
"です。いくつかのタグは、特定の投稿タイプやタクソノミーでのみ使用可能ですので、ご注意下さい。"

#: includes/views/permalink-manager-uri-editor-post.php:174
msgid "All dates"
msgstr "全日付"

#: includes/views/permalink-manager-pro-addons.php:278
msgid ""
"All URIs specified below will redirect the visitors to the custom URI "
"defined above in \"Current URI\" field."
msgstr "以下で指定された全てのURIは、訪問者を上記の「現在のURI」フィールドで定義されたカスタムURIにリダイレクトします。"

#: includes/views/permalink-manager-uri-editor.php:83
msgid "Apply"
msgstr "適用"

#: includes/core/permalink-manager-pro-functions.php:196
msgid "Arabic"
msgstr "アラビア語"

#: includes/core/permalink-manager-admin-functions.php:135
msgid "Are you sure? This action cannot be undone!"
msgstr "本当によろしいですか？この操作は、元に戻せません！"

#: includes/views/permalink-manager-debug.php:58
msgid "Array with external redirects"
msgstr "外部リダイレクトでの配列"

#: includes/views/permalink-manager-debug.php:69
msgid "Array with permastructures"
msgstr "パーマ構造での配列"

#: includes/views/permalink-manager-debug.php:48
msgid "Array with redirects"
msgstr "リダイレクトでの配列"

#: includes/views/permalink-manager-debug.php:80
msgid "Array with settings used in this plugin."
msgstr "本プラグイン内で使用中の設定での配列"

#: includes/views/permalink-manager-debug.php:37
msgid "Array with URIs"
msgstr "URIでの配列"

#: includes/views/permalink-manager-settings.php:244
msgid "Author (publish_posts)"
msgstr "投稿者 (publish_posts)"

#: includes/core/permalink-manager-admin-functions.php:902
msgid "Auto-update \"Current URI\""
msgstr ""

#: includes/views/permalink-manager-settings.php:39
msgid "Auto-update permalinks"
msgstr "パーマリンクの自動更新"

#: includes/core/permalink-manager-admin-functions.php:935
msgid "Automatic redirect for native URI enabled:"
msgstr "ネイティブURIの自動リダイレクトを有効にする："

#: includes/views/permalink-manager-settings.php:249
msgid "Automatically fix broken URIs"
msgstr "壊れたURIを自動的に修正する"

#: includes/views/permalink-manager-settings.php:201
msgid "Breadcrumbs support"
msgstr "パンくずリスト サポート"

#: includes/core/permalink-manager-actions.php:596
msgid "Broken redirects were removed successfully!"
msgstr "リンク切れリダイレクトは、無事削除されました！"

#: includes/views/permalink-manager-settings.php:251
msgid "Bulk fix all URIs (once a day, in the background)"
msgstr "全てのURIを一括修正する（1日1回、バックグラウンド）"

#: includes/core/permalink-manager-admin-functions.php:173
msgid "Buy Permalink Manager Pro"
msgstr "Permalink Manager Proを購入"

#: includes/core/permalink-manager-admin-functions.php:614
msgid "by Maciej Bis"
msgstr "by Maciej Bis"

#: includes/views/permalink-manager-settings.php:88
msgid "Canonical redirect"
msgstr "正規リダイレクト"

#: includes/core/permalink-manager-pro-functions.php:197
msgid "Chinese"
msgstr "中国語"

#: includes/core/permalink-manager-uri-functions-tax.php:504
msgid "Clear/leave the field empty to use the default permalink."
msgstr "デフォルトのパーマリンクを使用するため、フィールドを空のままにする / クリアする"

#: includes/core/permalink-manager-admin-functions.php:874
msgid "Close: "
msgstr "閉じる："

#: includes/views/permalink-manager-tools.php:120
msgid "Congratulations! No duplicated URIs or Redirects found!"
msgstr "おめでとうございます！重複したURI・リダイレクトは、見つかりませんでした！"

#: includes/views/permalink-manager-settings.php:244
msgid "Contributor (edit_posts)"
msgstr "寄稿者 (edit_posts)"

#: includes/views/permalink-manager-settings.php:236
msgid "Convert accented letters"
msgstr "アクセント付き文字の変換"

#: includes/views/permalink-manager-uri-editor-tax.php:53
msgid "Count"
msgstr "カウント"

#: includes/core/permalink-manager-pro-functions.php:541
msgid "Coupon Full URL"
msgstr "クーポン フル URL"

#: includes/core/permalink-manager-pro-functions.php:508
msgid "Coupon Link"
msgstr "クーポンリンク"

#: includes/core/permalink-manager-pro-functions.php:529
msgid "Coupon URI"
msgstr "クーポンURI"

#: includes/core/permalink-manager-uri-functions-post.php:700
#: includes/core/permalink-manager-uri-functions-tax.php:536
#: includes/core/permalink-manager-admin-functions.php:793
#: includes/core/permalink-manager-admin-functions.php:893
msgid "Current URI"
msgstr "現在のURI"

#: includes/views/permalink-manager-tools.php:34
#: includes/views/permalink-manager-pro-addons.php:216
msgid "Custom Permalinks"
msgstr "カスタム パーマリンク"

#: includes/core/permalink-manager-actions.php:420
msgid "Custom permalinks"
msgstr "カスタム パーマリンク"

#: includes/core/permalink-manager-actions.php:424
msgid "Custom redirects"
msgstr "カスタム リダイレクト"

#: includes/views/permalink-manager-tools.php:70
#: includes/core/permalink-manager-third-parties.php:476
#: includes/core/permalink-manager-third-parties.php:813
#: includes/core/permalink-manager-uri-functions-tax.php:503
msgid "Custom URI"
msgstr "カスタム URI"

#: includes/views/permalink-manager-tools.php:150
msgid "Custom URIs"
msgstr "カスタム URI"

#: includes/core/permalink-manager-pro-functions.php:198
msgid "Danish"
msgstr "デンマーク語"

#: includes/views/permalink-manager-pro-addons.php:217
msgid "Deactivate after import"
msgstr "インポート後、非アクティブにする"

#: includes/views/permalink-manager-debug.php:14
msgid "Debug"
msgstr "デバッグ"

#: includes/views/permalink-manager-debug.php:28
msgid "Debug data"
msgstr "デバッグデータ"

#: includes/core/permalink-manager-admin-functions.php:419
msgid "Default permastructure"
msgstr "デフォルト パーマ構造"

#: includes/core/permalink-manager-admin-functions.php:924
msgid "Default URI"
msgstr "デフォルト URI"

#: includes/views/permalink-manager-settings.php:251
msgid "Disable"
msgstr "無効"

#: includes/views/permalink-manager-settings.php:165
msgid "Disable (Permalink Manager redirect functions)"
msgstr "無効（Permalink Manager リダイレクト 機能）"

#: includes/core/permalink-manager-admin-functions.php:427
msgid "Do not automatically append the slug"
msgstr "スラッグを自動的に追加しない"

#: includes/core/permalink-manager-admin-functions.php:169
msgid "Documentation"
msgstr "ドキュメント"

#: includes/core/permalink-manager-admin-functions.php:613
msgid "Donate"
msgstr "寄付"

#: includes/core/permalink-manager-pro-functions.php:199
msgid "Dutch"
msgstr "オランダ語"

#: includes/views/permalink-manager-pro-addons.php:194
msgid ""
"Each of the words can be removed and any new words can be added to the list. "
"You can also use a predefined list (available in 21 languages)."
msgstr "各単語は、リストへ追加/削除できます。また、定義済みのリスト（21の言語）も使用可能です。"

#: includes/views/permalink-manager-uri-editor-tax.php:100
#: includes/views/permalink-manager-uri-editor-post.php:120
#: includes/views/permalink-manager-uri-editor-post.php:120
msgid "Edit"
msgstr "編集"

#: includes/views/permalink-manager-tools.php:94
msgid "Edit post"
msgstr "投稿の編集"

#: includes/views/permalink-manager-tools.php:81
msgid "Edit term"
msgstr "タームの編集"

#: includes/views/permalink-manager-settings.php:244
msgid "Editor (publish_pages)"
msgstr "編集者 (publish_pages)"

#: includes/views/permalink-manager-pro-addons.php:174
msgid "Enable \"stop words\""
msgstr "「使用禁止の単語」を有効にする"

#: includes/views/permalink-manager-settings.php:253
msgid ""
"Enable this option if you would like to automatically remove redundant "
"permalinks & duplicated redirects."
msgstr "冗長なパーマリンクと重複したリダイレクトを自動的に削除したい場合は、このオプションを有効にします。"

#: includes/core/permalink-manager-pro-functions.php:200
msgid "English"
msgstr "英語"

#: includes/views/permalink-manager-settings.php:149
msgid ""
"Example: <em>https://example.com/product/old-product-url/<strong>?discount-"
"code=blackfriday</strong></em> => <em>https://example.com/new-product-"
"url/<strong>?discount-code=blackfriday</strong></em>"
msgstr ""

#: includes/views/permalink-manager-settings.php:70
#| msgid "Excluded content types"
msgid "Exclude content types"
msgstr "除外する投稿タイプ"

#: includes/views/permalink-manager-settings.php:76
msgid "Exclude drafts"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:159
msgid ""
"Expiration date could not be downloaded at this moment. Please try again in "
"a few minutes."
msgstr "現在、有効期限の情報をダウンロードできない可能性があります。数分後に、再度お試し下さい。"

#: includes/core/permalink-manager-actions.php:428
msgid "External redirects"
msgstr "外部のリダイレクト"

#: includes/views/permalink-manager-tools.php:70
msgid "Extra Redirect"
msgstr "追加のリダイレクト"

#: includes/views/permalink-manager-settings.php:114
#: includes/views/permalink-manager-pro-addons.php:277
msgid "Extra redirects (aliases)"
msgstr "追加のリダイレクト（エイリアス）"

#: includes/views/permalink-manager-uri-editor-post.php:183
msgid "Filter"
msgstr "フィルター"

#: includes/views/permalink-manager-tools.php:22
msgid "Find & Replace"
msgstr "検索＆置換"

#: includes/views/permalink-manager-tools.php:134
msgid "Find ..."
msgstr "検索"

#: includes/views/permalink-manager-tools.php:209
msgid "Find and replace"
msgstr "検索＆置換"

#: includes/core/permalink-manager-pro-functions.php:201
msgid "Finnish"
msgstr "フィンランド語"

#: includes/views/permalink-manager-tools.php:55
msgid "Fix custom permalinks & redirects"
msgstr "カスタム パーマリンク＆リダイレクトを修正する"

#: includes/views/permalink-manager-settings.php:251
msgid "Fix URIs individually (during page load)"
msgstr "個々にURLを修正する（ページ読込時）"

#: includes/views/permalink-manager-settings.php:224
msgid "Force 404 on non-existing pagination pages"
msgstr "実在しないページネーションのページに404を強制表示する"

#: includes/views/permalink-manager-settings.php:154
msgid "Force HTTPS/WWW"
msgstr "HTTPS/WWW 強制"

#: includes/core/permalink-manager-pro-functions.php:202
msgid "French"
msgstr "フランス語"

#: includes/views/permalink-manager-uri-editor-tax.php:52
#: includes/views/permalink-manager-uri-editor-post.php:56
msgid "Full URI & Permalink"
msgstr "全ての URIとパーマリンク"

#: includes/views/permalink-manager-settings.php:33
msgid "General settings"
msgstr "一般設定"

#: includes/core/permalink-manager-pro-functions.php:203
msgid "German"
msgstr "ドイツ語"

#: permalink-manager.php:212
#, php-format
msgid ""
"Get access to extra features: full taxonomy and WooCommerce support, "
"possibility to use custom fields inside the permalinks and more!<br />"
"<strong>Buy Permalink Manager Pro <a href=\"%s\" target=\"_blank\">here</a> "
"and save %s using \"%s\" coupon code!</strong> Valid until %s!"
msgstr ""
"様々な機能へアクセス：タクソノミーとWooCommerceへのフルサポート、パーマリンク内のカスタムフィールドの使用など、盛り沢山！<br />"
"<strong>パーマリンク マネージャー Proの購入は<a href=\"%s\" target=\"_blank\">コチラ</a>！ %s "
"の割引 「%s」をご利用下さい。 </strong>　%s まで！"

#: includes/views/permalink-manager-pro-addons.php:134
msgid "Get license information"
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:204
msgid "Hebrew"
msgstr "ヘブライ語"

#: includes/core/permalink-manager-pro-functions.php:205
msgid "Hindi"
msgstr "ヒンディー語"

#: includes/views/permalink-manager-pro-addons.php:320
msgid "http://another-website.com/final-target-url"
msgstr "http://another-website.com/final-target-url"

#. Author URI of the plugin
msgid "http://maciejbis.net/"
msgstr "http://maciejbis.net/"

#. URI of the plugin
msgid "https://permalinkmanager.pro?utm_source=plugin"
msgstr "https://permalinkmanager.pro?utm_source=plugin"

#: includes/core/permalink-manager-admin-functions.php:894
msgid ""
"If custom URI is not defined, a default URI will be set (see below). The "
"custom URI can be edited only if 'Auto-update the URI' feature is not "
"enabled."
msgstr ""
"カスタムURIが定義されていない場合、デフォルトのURIが設定されます。（以下を参照）カスタムURIは、「URIの自動更新」機能が有効になっていない場合のみ編集できます。"

#: includes/views/permalink-manager-settings.php:190
msgid ""
"If disabled, the custom permalinks <strong>will not be saved</strong> for "
"the posts imported with WP All Import plugin."
msgstr ""

#: includes/core/permalink-manager-third-parties.php:814
#, php-format
msgid ""
"If empty, a default permalink based on your current <a href=\"%s\" "
"target=\"_blank\">permastructure settings</a> will be used."
msgstr ""
"空の場合、現在の<a href=\"%s\" target=\"_blank\">パーマ構造 設定</a>"
"に基づいたデフォルトのパーマリンクが適用されます。"

#: includes/views/permalink-manager-settings.php:232
msgid ""
"If enabled only alphanumeric characters, underscores and dashes will be "
"allowed for post/term slugs."
msgstr "英数字のみを有効にすると、投稿とターム スラッグにて下線とダッシュの使用が許可されます。"

#: includes/views/permalink-manager-pro-addons.php:193
msgid ""
"If enabled, all selected \"stop words\" will be automatically removed from "
"default URIs."
msgstr "有効にすると、選択された全ての「使用禁止の単語」はデフォルトのURIから、自動的に削除されます。"

#: includes/views/permalink-manager-settings.php:239
msgid ""
"If enabled, all the accented letters will be replaced with their non-"
"accented equivalent (eg. Å => A, Æ => AE, Ø => O, Ć => C)."
msgstr ""
"有効にすると、全てのアクセント付きの文字が、アクセントなしの文字に置換されます。（例：Å => A, Æ => AE, Ø => O, Ć => C）"

#: includes/views/permalink-manager-settings.php:43
msgid ""
"If enabled, Permalink Manager will always force the default custom permalink "
"format (based on current <strong>Permastructure</strong> settings)."
msgstr ""
"有効にすると、Permalink Managerは常にデフォルトのカスタム パーマリンク形式を強制します。（現在の<strong>"
"パーマ構造</strong>設定に基づく）"

#: includes/views/permalink-manager-settings.php:197
msgid ""
"If enabled, Permalink Manager will detect the additional Ultimate Member "
"pages (eg. \"account\" sections)."
msgstr ""

#: includes/views/permalink-manager-settings.php:130
#| msgid ""
#| "If enabled, Permalink Manage will save the \"extra redirect\" for earlier "
#| "version of custom permalink after you change it (eg. with URI Editor or "
#| "Regenerate/reset tool)."
msgid ""
"If enabled, Permalink Manager will save the \"extra redirect\" for earlier "
"version of custom permalink after you change it (eg. with URI Editor or "
"Regenerate/reset tool)."
msgstr ""
"有効にすると、Permalink Managerは、変更後（例：URIエディターまたは再生成・リセットツールを使用）に、以前のバージョンのカスタム "
"パーマリンクのリダイレクトを追加します。"

#: includes/views/permalink-manager-settings.php:209
msgid ""
"If enabled, Permalink Manager will use the \"primary category\" for the "
"default post permalinks.<br />Works with: <strong>Yoast SEO, The SEO "
"Framework, RankMath and SEOPress</strong>."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:903
msgid ""
"If enabled, the 'Current URI' field will be automatically changed to "
"'Default URI' (displayed below) after the post is saved or updated."
msgstr "有効にすると、「現在のURI」項目は投稿が更新又は保存された後に、（下記に表示された）「デフォルトURI」に自動的に変更されます。"

#: includes/views/permalink-manager-settings.php:77
msgid "If enabled, the custom permalinks for post drafts will not be saved."
msgstr ""

#: includes/views/permalink-manager-settings.php:203
msgid ""
"If enabled, the HTML breadcrumbs will be filtered by Permalink Manager to "
"mimic the current URL structure.<br />Works with: <strong>WooCommerce, Yoast "
"SEO, Slim Seo, RankMath and SEOPress</strong> breadcrumbs."
msgstr ""

#: includes/views/permalink-manager-settings.php:225
msgid ""
"If enabled, the non-existing pagination pages (for single posts) will return "
"404 (\"Not Found\") error.<br /><strong>Please disable it, if you encounter "
"any problems with pagination pages or use custom pagination system.</strong>"
msgstr ""
"有効にすると、実在しないページネーションのページ（単一投稿用）に対して、404（ \"Not Found\"）エラーを返します。<br />"
"<strong>ページネーションのページ、もしくは独自のページネーション "
"システムをご利用で、なんらかの問題が生じた場合は、無効にしてください。</strong>"

#: includes/views/permalink-manager-settings.php:183
msgid ""
"If enabled, the plugin will load the adjacent translation of post when the "
"custom permalink is detected, but the language code in the URL does not "
"match the language code assigned to the post/term."
msgstr ""
"有効にすると、本プラグインはカスタムパーマリンクを検出した際、投稿に用いた言語に近い翻訳を読み込みますが、投稿やタームに割当られた言語コードとURL内言語コードと一致しない場合は読み込みません。"

#: includes/views/permalink-manager-settings.php:148
msgid ""
"If enabled, the query parameters will be copied to the target URL when the "
"redirect is triggered."
msgstr ""

#: includes/views/permalink-manager-settings.php:109
msgid ""
"If enabled, the visitors trying to access the URL with the old slug will be "
"redirected to the canonical permalink."
msgstr "有効にすると、古いスラッグでURLにアクセスした訪問者を、正規パーマリンクにリダイレクトします。"

#: includes/views/permalink-manager-pro-addons.php:316
msgid ""
"If not empty, the visitors trying to access this page will be redirected to "
"the URL specified below."
msgstr "空白でない場合、このページにアクセスしようと試みた訪問者は、下記のURLにリダイレクトされます。"

#: includes/views/permalink-manager-pro-addons.php:220
msgid ""
"If selected, \"Custom Permalinks\" plugin will be deactivated after its "
"custom URIs are imported."
msgstr "選択すると、カスタムURIのインポート後、「カスタムパーマリンク」プラグインが無効になります。"

#: includes/views/permalink-manager-pro-addons.php:252
msgid ""
"If you still did not find the answer to your question, please send us your "
"question or a detailed description of your problem/issue to <a href=\"mailto:"
"<EMAIL>\"><EMAIL></a>."
msgstr ""
"ご質問に対して答えが見つからない場合は、問題の詳細又はご質問を<a href=\"mailto:support@permalinkmanager."
"pro\"><EMAIL></a>までお送り下さい。（英語のみ）"

#: includes/views/permalink-manager-pro-addons.php:256
msgid ""
"If you would like to suggest a new functionality or leave us feedback, we "
"are open to all new ideas and would be grateful for all your comments!"
msgstr "新たな機能の提案やフィードバックをお送り下さい。あらゆる新しいアイデアは大歓迎であり、あなたのコメントをお送り頂ければ幸いです。"

#: includes/core/permalink-manager-admin-functions.php:402
msgid ""
"If you would like to translate the permastructures and set-up different "
"permalink structure per language, please fill in the fields below. Otherwise "
"the permastructure set for default language (see field above) will be "
"applied."
msgstr ""
"パーマ構造を翻訳し、言語ごとに異なるパーマリンク構造を設定する場合は、以下のフィールドに入力して下さい。 それ以外は、デフォルト言語のパーマ構造 "
"セット（上記のフィールドを参照）が適用されます。"

#: includes/views/permalink-manager-pro-addons.php:232
#, php-format
msgid "Import %d URIs"
msgstr "%d URIをインポートする"

#: includes/views/permalink-manager-tools.php:206
#: includes/views/permalink-manager-tools.php:283
msgid "Important notices"
msgstr "重要なお知らせ"

#: includes/views/permalink-manager-uri-editor-post.php:89
msgid "Inherit (Attachment)"
msgstr "継承（添付）"

#: includes/views/permalink-manager-settings.php:50
msgid "Inherit parents' slugs"
msgstr "親のスラッグを継承する"

#: includes/views/permalink-manager-permastructs.php:75
#: includes/views/permalink-manager-pro-addons.php:192
#: includes/views/permalink-manager-pro-addons.php:225
msgid "Instructions"
msgstr "手順"

#: includes/core/permalink-manager-pro-functions.php:206
msgid "Italian"
msgstr "イタリア語"

#: includes/core/permalink-manager-pro-functions.php:207
msgid "Japanese"
msgstr "日本語"

#: includes/core/permalink-manager-pro-functions.php:208
msgid "Korean"
msgstr "韓国語"

#: includes/views/permalink-manager-pro-addons.php:121
msgid "Licence"
msgstr "ライセンス"

#: includes/views/permalink-manager-pro-addons.php:127
msgid "Licence key"
msgstr "ライセンスキー"

#: includes/views/permalink-manager-debug.php:44
msgid "List of custom redirects set-up by this plugin."
msgstr "本プラグインによって設定されたカスタム リダイレクトのリスト"

#: includes/views/permalink-manager-tools.php:54
msgid "List of duplicated permalinks"
msgstr "重複したパーマリンクのリスト"

#: includes/views/permalink-manager-debug.php:55
msgid "List of external redirects set-up by this plugin."
msgstr "本プラグインにてセットアップされた外部リダイレクトのリスト"

#: includes/views/permalink-manager-debug.php:65
msgid "List of permastructures set-up by this plugin."
msgstr "本プラグインにてセットアップしたパーマ構造のリスト"

#: includes/views/permalink-manager-debug.php:76
msgid "List of plugin settings."
msgstr "プラグイン設定 リスト"

#: includes/views/permalink-manager-debug.php:33
msgid "List of the URIs generated by this plugin."
msgstr "本プラグインで生成されたURIのリスト"

#: includes/core/permalink-manager-admin-functions.php:733
msgid "List of updated items"
msgstr "更新済みのアイテムのリスト"

#. Author of the plugin
msgid "Maciej Bis"
msgstr "Maciej Bis"

#: includes/core/permalink-manager-admin-functions.php:976
msgid "Manage redirects"
msgstr "リダイレクトを管理"

#: includes/views/permalink-manager-tools.php:146
#: includes/views/permalink-manager-tools.php:222
msgid "Mode"
msgstr "モード"

#: includes/core/permalink-manager-admin-functions.php:914
msgid "Native slug"
msgstr "ネイティブ スラッグ"

#: includes/views/permalink-manager-tools.php:151
msgid "Native slugs"
msgstr "ネイティブ スラッグ"

#: includes/core/permalink-manager-admin-functions.php:696
#: includes/core/permalink-manager-admin-functions.php:723
msgid "New Slug"
msgstr "新しいスラッグ"

#: includes/core/permalink-manager-admin-functions.php:699
#: includes/core/permalink-manager-admin-functions.php:726
msgid "New URI"
msgstr "新しいURI"

#: includes/core/permalink-manager-admin-functions.php:855
#: includes/core/permalink-manager-admin-functions.php:859
msgid "No"
msgstr "いいえ"

#: includes/core/permalink-manager-third-parties.php:347
msgid "No \"Custom Permalinks\" URIs were imported!"
msgstr "「カスタム パーマリンク」 URIはインポートされませんでした！"

#: includes/core/permalink-manager-admin-functions.php:860
msgid "No (ignore this URI in bulk tools)"
msgstr "いいえ（一括ツールでは、このURIを無視します。）"

#: includes/core/permalink-manager-actions.php:402
msgid "No Custom URIs or Custom Redirects were removed!"
msgstr "カスタムURIもしくはカスタム リダイレクトは、削除されませんでした！"

#: includes/views/permalink-manager-pro-addons.php:237
msgid "No custom URIs to import"
msgstr "インポートするカスタムURIはありません"

#: includes/views/permalink-manager-settings.php:238
msgid "No, keep accented letters in the slugs"
msgstr "いいえ、スラッグにアクセント付き文字を保持します。"

#: includes/views/permalink-manager-settings.php:231
msgid "No, keep special characters (.,|_+) in the slugs"
msgstr "いいえ、スラッグに特殊文字(.,|_+)を保持します。"

#: includes/core/permalink-manager-pro-functions.php:209
msgid "Norwegian"
msgstr "ノルウェー語"

#: includes/core/permalink-manager-admin-functions.php:695
#: includes/core/permalink-manager-admin-functions.php:722
msgid "Old Slug"
msgstr "古いスラッグ"

#: includes/views/permalink-manager-settings.php:105
msgid "Old slug redirect"
msgstr "古いスラッグ リダイレクト"

#: includes/core/permalink-manager-admin-functions.php:698
#: includes/core/permalink-manager-admin-functions.php:725
msgid "Old URI"
msgstr "古いURI"

#: includes/views/permalink-manager-settings.php:245
#, php-format
msgid ""
"Only the users who have selected capability will be able to access URI "
"Editor.<br />The list of capabilities <a href=\"%s\" target=\"_blank\">can "
"be found here</a>."
msgstr ""
"権限を持つユーザーのみがURIエディターにアクセスできます。<br />権限のリストは<a href=\"%s\" target=\"_blank\">"
"コチラ</a>で確認できます。"

#: includes/views/permalink-manager-uri-editor.php:89
msgid "Per page"
msgstr "ページ毎"

#: includes/views/permalink-manager-tools.php:18
msgid "Permalink Duplicates"
msgstr "重複したパーマリンク"

#: includes/core/permalink-manager-third-parties.php:809
#: includes/core/permalink-manager-third-parties.php:954
#: includes/core/permalink-manager-gutenberg.php:33
#: includes/core/permalink-manager-admin-functions.php:111
#: includes/core/permalink-manager-admin-functions.php:111
#: includes/core/permalink-manager-admin-functions.php:789
#: includes/core/permalink-manager-admin-functions.php:869
#: includes/core/permalink-manager-admin-functions.php:874
#: includes/core/permalink-manager-admin-functions.php:875
msgid "Permalink Manager"
msgstr "Permalink Manager"

#: includes/views/permalink-manager-settings.php:139
#| msgid ""
#| "<strong>Permalink Manager can force the trailing slashes settings in the "
#| "custom permalinks with redirect.</strong>"
msgid ""
"Permalink Manager can force the trailing slashes settings in the custom "
"permalinks with redirect."
msgstr "Permalink Managerは、リダイレクトのあるカスタム パーマリンクの末尾のスラッシュ設定を強制できます。"

#: includes/views/permalink-manager-settings.php:167
#| msgid ""
#| "<strong>Permalink Manager includes a set of hooks that allow to extend "
#| "the redirect functions used natively by WordPress to avoid 404 errors."
#| "</strong>"
msgid ""
"Permalink Manager includes a set of hooks that allow to extend the redirect "
"functions used natively by WordPress to avoid 404 errors."
msgstr ""
"Permalink Managerには、404エラー回避のためにWordPressが従来使用するリダイレクト機能を拡張できる一連のフックが含まれています。"

#. Name of the plugin
msgid "Permalink Manager Pro"
msgstr "Permalink Manager Pro"

#: includes/views/permalink-manager-settings.php:72
msgid ""
"Permalink Manager will ignore and not filter the custom permalinks of all "
"selected above post types & taxonomies."
msgstr "Permalink Managerは、上記で選択した全ての投稿タイプとタクソノミーのカスタム パーマリンクを無視し、フィルタリングしません。"

#: includes/core/permalink-manager-actions.php:432
#: includes/core/permalink-manager-actions.php:436
#: includes/core/permalink-manager-admin-functions.php:426
msgid "Permastructure settings"
msgstr "パーマ構造 設定"

#: includes/views/permalink-manager-permastructs.php:82
msgid "Permastructure tags"
msgstr "パーマ構造 タグ"

#: includes/core/permalink-manager-admin-functions.php:401
msgid "Permastructure translations"
msgstr "パーマ構造 翻訳"

#: includes/views/permalink-manager-permastructs.php:15
msgid "Permastructures"
msgstr "パーマ構造"

#: includes/core/permalink-manager-pro-functions.php:210
msgid "Persian"
msgstr "ペルシア語"

#: includes/views/permalink-manager-settings.php:158
msgid "Please disable it if you encounter any redirect loop issues."
msgstr "リダイレクトのループ問題が発生した場合は、このオプションを無効にして下さい。"

#: includes/views/permalink-manager-settings.php:119
msgid ""
"Please enable this option if you would like to manage additional custom "
"redirects (aliasees) in URI Editor for individual posts & terms."
msgstr ""

#: includes/views/permalink-manager-settings.php:140
msgid ""
"Please go to \"<em>General settings -> Trailing slashes</em>\" to choose if "
"trailing slashes should be added or removed from WordPress permalinks."
msgstr ""

#: includes/views/permalink-manager-settings.php:65
msgid ""
"Please go to \"<em>Redirect settings -> Trailing slashes redirect</em>\" to "
"force the trailing slashes mode with redirect."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:226
msgid ""
"Please note that \"Custom Permalinks\" (if activated) may break the behavior "
"of this plugin."
msgstr "「カスタム パーマリンク」（有効にしている場合）により、本プラグインの動作が損なわれる場合があります。"

#: includes/views/permalink-manager-settings.php:131
msgid ""
"Please note that the new redirects will be saved only if \"Extra redirects "
"(aliases)\" option is turned on above."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:134
#, php-format
msgid ""
"Please paste the licence key to access all Permalink Manager Pro updates & "
"features <a href=\"%s\" target=\"_blank\">on this page</a>."
msgstr ""
"<a href=\"%s\" target=\"_blank\">コチラのページ</a>にて、ライセンスキーを設定して全てのPermalink "
"Manager Pro のアップデート＆機能をご利用下さい。"

#: includes/core/permalink-manager-pro-functions.php:211
msgid "Polish"
msgstr "ポーランド語"

#: includes/core/permalink-manager-pro-functions.php:212
msgid "Portuguese"
msgstr "ポルトガル語"

#: includes/views/permalink-manager-uri-editor-post.php:115
msgid "Post status"
msgstr "投稿状態"

#: includes/views/permalink-manager-uri-editor.php:94
msgid "Post statuses"
msgstr "投稿状態"

#: includes/views/permalink-manager-uri-editor-post.php:55
msgid "Post title"
msgstr "投稿タイトル"

#: includes/views/permalink-manager-permastructs.php:31
#: includes/views/permalink-manager-tools.php:162
#: includes/views/permalink-manager-tools.php:239
#: includes/core/permalink-manager-admin-functions.php:262
msgid "Post types"
msgstr "投稿タイプ"

#: includes/views/permalink-manager-settings.php:163
msgid "Redirect mode"
msgstr "リダイレクト モード"

#: includes/views/permalink-manager-settings.php:82
msgid "Redirect settings"
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:315
msgid "Redirect this page to external URL"
msgstr "このページを外部URLにリダイレクトする"

#: includes/views/permalink-manager-settings.php:145
msgid "Redirect with query parameters"
msgstr ""

#: includes/views/permalink-manager-tools.php:286
msgid "Regenerate"
msgstr "再作成"

#: includes/views/permalink-manager-tools.php:226
msgid "Regenerate custom permalinks"
msgstr "カスタム パーマリンクを再生成する"

#: includes/views/permalink-manager-tools.php:227
msgid "Regenerate native slugs"
msgstr "ネイティブ スラッグを再生成する"

#: includes/views/permalink-manager-tools.php:26
msgid "Regenerate/Reset"
msgstr "再作成 / リセット"

#: includes/views/permalink-manager-pro-addons.php:132
msgid "Reload the expiration date"
msgstr "有効期限を確認する"

#: includes/views/permalink-manager-debug.php:35
msgid "Remove all custom permalinks"
msgstr "全てのカスタム パーマリンクを削除する"

#: includes/views/permalink-manager-debug.php:46
msgid "Remove all custom redirects"
msgstr "全てのカスタム リダイレクトを削除する"

#: includes/views/permalink-manager-debug.php:56
msgid "Remove all external redirects"
msgstr "全ての外部リダイレクトを削除する"

#: includes/views/permalink-manager-debug.php:67
msgid "Remove all permastructures settings"
msgstr "全てのパーマ構造 設定を削除する"

#: includes/views/permalink-manager-debug.php:78
msgid "Remove all plugin settings"
msgstr "全てのプラグイン設定を削除する"

#: includes/views/permalink-manager-pro-addons.php:164
msgid "Remove all words"
msgstr "全ての単語を削除する"

#: includes/views/permalink-manager-tools.php:85
#: includes/views/permalink-manager-tools.php:98
msgid "Remove broken URI"
msgstr "リンク切れURLを削除する"

#: includes/views/permalink-manager-settings.php:61
msgid "Remove trailing slashes"
msgstr "末尾のスラッシュを削除する"

#: includes/views/permalink-manager-tools.php:140
msgid "Replace with ..."
msgstr "置換"

#: includes/core/permalink-manager-admin-functions.php:420
msgid "Restore default permastructure"
msgstr "デフォルトのパーマ構造を復元する"

#: includes/core/permalink-manager-admin-functions.php:925
msgid "Restore Default URI"
msgstr "デフォルトのURIを復元する"

#: includes/core/permalink-manager-pro-functions.php:213
msgid "Russian"
msgstr "ロシア語"

#: includes/views/permalink-manager-pro-addons.php:284
msgid "sample/custom-uri"
msgstr "sample/custom-uri"

#: includes/views/permalink-manager-pro-addons.php:196
msgid "Save"
msgstr "保存する"

#: includes/views/permalink-manager-uri-editor-tax.php:147
#: includes/views/permalink-manager-uri-editor-post.php:150
msgid "Save all the URIs above"
msgstr "上記の全てのURIを保存する"

#: includes/views/permalink-manager-uri-editor-tax.php:146
#: includes/views/permalink-manager-uri-editor-post.php:149
msgid "Save all the URIs below"
msgstr "以下の全てのURIを保存する"

#: includes/views/permalink-manager-settings.php:125
msgid "Save old custom permalinks as extra redirects"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:949
msgid "Save permalink"
msgstr "パーマリンクを保存する"

#: includes/views/permalink-manager-permastructs.php:86
msgid "Save permastructures"
msgstr "パーマ構造を保存する"

#: includes/views/permalink-manager-settings.php:260
msgid "Save settings"
msgstr "設定を保存する"

#: includes/views/permalink-manager-uri-editor-tax.php:155
#: includes/views/permalink-manager-uri-editor-post.php:158
msgid "Search"
msgstr "検索"

#: includes/core/permalink-manager-admin-functions.php:285
msgid "Select all"
msgstr "全てを選択する"

#: includes/views/permalink-manager-tools.php:155
#: includes/views/permalink-manager-tools.php:232
msgid "Select content type"
msgstr "コンテンツタイプを選択する"

#: includes/views/permalink-manager-tools.php:196
#: includes/views/permalink-manager-tools.php:273
msgid "Select IDs"
msgstr "IDを選択する"

#: includes/views/permalink-manager-tools.php:187
#: includes/views/permalink-manager-tools.php:264
msgid "Select post statuses"
msgstr "投稿状態を選択する"

#: includes/views/permalink-manager-tools.php:167
#: includes/views/permalink-manager-tools.php:244
msgid "Select post types"
msgstr "投稿タイプを選択する"

#: includes/views/permalink-manager-tools.php:176
#: includes/views/permalink-manager-tools.php:253
msgid "Select taxonomies"
msgstr "タクソノミーを選択する"

#: includes/views/permalink-manager-settings.php:14
#: includes/core/permalink-manager-admin-functions.php:157
msgid "Settings"
msgstr "設定"

#: includes/views/permalink-manager-settings.php:220
msgid "Show \"Native slug\" field in URI Editor"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:435
msgid "Show additional settings"
msgstr "追加設定を表示する"

#: includes/core/permalink-manager-admin-functions.php:720
msgid "Show more details"
msgstr "さらなる詳細を表示する"

#: includes/core/permalink-manager-actions.php:682
msgid "Sitemaps were updated!"
msgstr "サイトマップは、更新されました！"

#: includes/views/permalink-manager-uri-editor-tax.php:95
#: includes/views/permalink-manager-uri-editor-post.php:114
msgid "Slug"
msgstr "スラッグ"

#: includes/views/permalink-manager-settings.php:48
msgid "Slugs mode"
msgstr "スラッグ モード"

#: includes/core/permalink-manager-pro-functions.php:214
msgid "Spanish"
msgstr "スペイン語"

#: includes/views/permalink-manager-tools.php:30
msgid "Stop Words"
msgstr "使用禁止の単語"

#: includes/views/permalink-manager-settings.php:229
msgid "Strip special characters"
msgstr "特殊文字を取り除く"

#: includes/views/permalink-manager-pro-addons.php:255
msgid "Suggestions/feedback"
msgstr "ご意見 / フィードバック"

#: includes/views/permalink-manager-pro-addons.php:101
msgid "Support"
msgstr "サポート"

#: includes/core/permalink-manager-pro-functions.php:215
msgid "Swedish"
msgstr "スウェーデン語"

#: includes/views/permalink-manager-permastructs.php:36
#: includes/views/permalink-manager-tools.php:163
#: includes/views/permalink-manager-tools.php:240
#: includes/core/permalink-manager-admin-functions.php:262
msgid "Taxonomies"
msgstr "タクソノミー"

#: includes/views/permalink-manager-pro-addons.php:250
msgid "Technical support"
msgstr "技術的サポート"

#: includes/views/permalink-manager-uri-editor-tax.php:51
msgid "Term title"
msgstr "ターム タイトル"

#: includes/views/permalink-manager-uri-editor-tax.php:112
#: includes/views/permalink-manager-uri-editor-post.php:101
msgid ""
"The above permalink will be automatically updated and is locked for editing."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:889
msgid ""
"The above permalink will be automatically updated to \"Default URI\" and is "
"locked for editing."
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:886
msgid "The custom URI cannot be edited on frontpage."
msgstr "カスタムURIは、フロントページでは編集不可です。"

#: includes/core/permalink-manager-admin-functions.php:915
msgid ""
"The native slug is by default automatically used in native permalinks (when "
"Permalink Manager is disabled)."
msgstr ""
"ネイティブ スラッグは、（Permalink Managerが無効になっている場合）デフォルトでは自動的にネイティブパーマリンクが使用されます。"

#: includes/views/permalink-manager-settings.php:53
msgid ""
"The native slug is generated from the initial title after the post or term "
"is published."
msgstr "ネイティブ スラッグは、投稿またはタームが公開された後、最初のタイトルから生成されます。"

#: includes/core/permalink-manager-actions.php:620
msgid "The redirect was removed successfully!"
msgstr "リダイレクトは、無事削除されました！"

#: includes/core/permalink-manager-actions.php:105
msgid "The settings are saved!"
msgstr "設定は、保存されました！"

#: includes/core/permalink-manager-pro-functions.php:530
msgid ""
"The URIs are case-insensitive, eg. <strong>BLACKFRIDAY</strong> and <strong>"
"blackfriday</strong> are equivalent."
msgstr ""
"URIは、大文字・小文字は区別しません。例：<strong>BLACKFRIDAY</strong> と<strong>"
"blackfriday</strong>は、同じです。"

#: includes/views/permalink-manager-pro-addons.php:227
msgid ""
"Therefore, it is recommended to disable \"Custom Permalink\" and import old "
"permalinks before using Permalink Manager Pro."
msgstr ""
"したがって、「カスタム パーマリンク」を無効にして、Permalink Manager Proを使用する前の古いパーマリンクのインポートを推奨します。"

#: includes/views/permalink-manager-settings.php:174
msgid "Third party plugins"
msgstr "サードパーティー プラグイン"

#: includes/views/permalink-manager-settings.php:92
msgid ""
"This feature will be also used to redirect (old) original permalinks to (new)"
" custom permalinks set with Permalink Manager."
msgstr ""
"この機能は、（古い）オリジナルのパーマリンクをPermalink Managerで設定された（新しい）カスタム "
"パーマリンクにリダイレクトするために使用されます。"

#: includes/core/permalink-manager-admin-functions.php:1094
#, php-format
msgid ""
"This functionality is available only in <a href=\"%s\" target=\"_blank\">"
"Permalink Manager Pro</a>."
msgstr "この機能は、Permalink Manager Proのみ利用可能です。"

#: includes/views/permalink-manager-settings.php:62
msgid ""
"This option can be used to alter the native settings and control if trailing "
"slash should be added or removed from the end of posts & terms permalinks."
msgstr ""
"このオプションは、ネイティブの設定を変更し、末尾のスラッシュを投稿・タームのパーマリンクの末尾に、追加・削除するかを制御するために使用できます。"

#: includes/core/permalink-manager-admin-functions.php:693
#: includes/core/permalink-manager-admin-functions.php:720
msgid "Title"
msgstr "タイトル"

#: includes/views/permalink-manager-permastructs.php:79
#, php-format
msgid ""
"To apply the <strong>new format to existing posts and terms</strong>, please "
"use \"<a href=\"%s\">Regenerate/reset</a>\" tool after you update the "
"permastructure settings below."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:251
#, php-format
msgid ""
"To find the answers on frequently asked questions and information about how "
"to deal with the most common issues please go to the <strong>Knowledge "
"Base</strong> using <a target=\"_blank\" href=\"%s\">this link</a>."
msgstr ""
"よくある質問と一般的な問題に対する対処方法についての情報は、 <a target=\"_blank\" href=\"%s\">コチラ</a>"
"の<strong>Knowledge Base</strong> をご利用下さい。（英語のみ）"

#: includes/views/permalink-manager-tools.php:200
#: includes/views/permalink-manager-tools.php:277
msgid ""
"To narrow the above filters you can type the post IDs (or ranges) here. Eg. "
"<strong>1-8, 10, 25</strong>."
msgstr ""
"上記のフィルターに絞りこむために、ここに投稿ID（または範囲）を入力してください。例：<strong>1-8, 10, 25</strong>."

#: includes/views/permalink-manager-pro-addons.php:253
msgid ""
"To reduce the response time, please attach your licence key and if possible "
"also: URL address of your website and screenshots explaining the issue."
msgstr ""
"ご返答までの時間を短縮するため、ライセンスキーを添付し、可能であればWebサイトのURLと問題箇所のスクリーンショットもお送り下さい。（英語のみ）"

#: includes/views/permalink-manager-tools.php:15
msgid "Tools"
msgstr "ツール"

#: includes/views/permalink-manager-settings.php:59
msgid "Trailing slashes"
msgstr "末尾のスラッシュ"

#: includes/views/permalink-manager-settings.php:136
msgid "Trailing slashes redirect"
msgstr "末尾のスラッシュリダイレクト"

#: includes/core/permalink-manager-pro-functions.php:216
msgid "Turkish"
msgstr "トルコ語"

#: includes/views/permalink-manager-pro-addons.php:270
msgid ""
"Turn on \"<strong>Extra redirects (aliases)</strong>\" in Permalink Manager "
"settings to enable this feature."
msgstr ""

#: includes/views/permalink-manager-pro-addons.php:184
msgid "Type comma to separate the words."
msgstr "単語の区切りにカンマを入力下さい。"

#: includes/views/permalink-manager-settings.php:194
msgid "Ultimate Member support"
msgstr ""

#: includes/core/permalink-manager-admin-functions.php:286
msgid "Unselect all"
msgstr "選択を全て解除する"

#: includes/core/permalink-manager-admin-functions.php:626
msgid "Upgrade to PRO"
msgstr "Pro版にアップグレード"

#: includes/core/permalink-manager-actions.php:588
#, php-format
msgid "URI \"%s\" was removed successfully!"
msgstr "「%s」のURIは、無事削除されました！"

#: includes/core/permalink-manager-actions.php:600
msgid "URI and/or custom redirects does not exist or were already removed!"
msgstr "URIもしくはカスタムリダイレクトは、実在しないか既に削除されています！"

#: includes/core/permalink-manager-admin-functions.php:156
msgid "URI Editor"
msgstr "URIエディター"

#: includes/views/permalink-manager-uri-editor.php:28
msgid "URI editor"
msgstr "URIエディター"

#: includes/views/permalink-manager-settings.php:243
msgid "URI Editor role capability"
msgstr "URIエディター 種類と権限"

#: includes/core/permalink-manager-actions.php:650
msgid "URI is already in use, please select another one!"
msgstr "URIは既に使用済みです。他をお選び下さい。"

#: includes/views/permalink-manager-settings.php:50
msgid "Use actual titles as slugs"
msgstr "スラッグとして実際のタイトルを使用する"

#: includes/views/permalink-manager-settings.php:61
msgid "Use default settings"
msgstr "デフォルトの設定を使用する"

#: includes/core/permalink-manager-admin-functions.php:857
#, php-format
msgid "Use global settings [%s]"
msgstr "グローバル設定を使用する [%s]"

#: includes/views/permalink-manager-settings.php:50
msgid "Use native slugs"
msgstr "ネイティブ スラッグを使用する"

#: includes/views/permalink-manager-tools.php:228
msgid "Use original URLs as custom permalinks"
msgstr "カスタム パーマリンクをオリジナルのURLとして使用する"

#: includes/views/permalink-manager-settings.php:54
msgid ""
"Use this field if you would like Permalink Manager to use the actual titles "
"instead of native slugs."
msgstr "Permalink Managerで、ネイティブ スラッグの代わりに実際のタイトルを使用する場合は、このフィールドを使用します。"

#: includes/views/permalink-manager-uri-editor-tax.php:101
#: includes/views/permalink-manager-uri-editor-post.php:121
#: includes/views/permalink-manager-uri-editor-post.php:121
msgid "View"
msgstr "見る"

#: includes/views/permalink-manager-permastructs.php:46
msgid "WooCommerce"
msgstr "WooCommerce"

#: includes/views/permalink-manager-settings.php:187
msgid "WP All Import support"
msgstr "WP All Import サポート"

#: includes/views/permalink-manager-settings.php:180
msgid "WPML/Polylang language mismatch"
msgstr "WPML / Polylang 言語 不一致"

#: includes/core/permalink-manager-admin-functions.php:855
#: includes/core/permalink-manager-admin-functions.php:858
msgid "Yes"
msgstr "はい"

#: includes/views/permalink-manager-settings.php:231
#: includes/views/permalink-manager-settings.php:238
msgid "Yes, use native settings"
msgstr "はい、ネイティブ 設定を使います。"

#: includes/core/permalink-manager-actions.php:414
msgid "You are not allowed to remove Permalink Manager data!"
msgstr "Permalink Manager データを削除できません！"

#: includes/views/permalink-manager-settings.php:168
msgid ""
"You can disable this feature if you do not want Permalink Manager to trigger "
"any additional redirect functions at all."
msgstr "Permalink Managerに、追加のリダイレクト機能を一切トリガーさせないようにする場合は、この機能を無効にします。"

#: includes/views/permalink-manager-settings.php:120
msgid ""
"You can disable this feature if you use another plugin to control the "
"redirects, eg. Yoast SEO Premium or Redirection."
msgstr ""

#: includes/core/permalink-manager-pro-functions.php:149
msgid "You own a lifetime licence key."
msgstr "あなたは、永久ライセンスキーをお持ちです。"

#: includes/core/permalink-manager-pro-functions.php:154
#, php-format
msgid ""
"Your licence key is valid until %s.<br />To prolong it please go to <a "
"href=\"%s\" target=\"_blank\">this page</a> for more information."
msgstr ""
"あなたのライセンスキーは、%s まで有効です。<br />延長するには、<a href=\"%s\" target=\"_blank\">"
"コチラのページ</a>にアクセスして詳細を確認して下さい。"

#: includes/core/permalink-manager-pro-functions.php:144
#, php-format
msgid ""
"Your Permalink Manager Pro licence key expired! To restore access to plugin "
"updates & technical support please go to <a href=\"%s\" target=\"_blank\">"
"this page</a>."
msgstr ""
"あなたのライセンスキーは有効期限が切れました！プラグインのアップデートのアクセスやテクニカルサポートを受けたい場合は、<a href=\"%s\" "
"target=\"_blank\">コチラのページ</a>にアクセスして、復元して下さい。"

#: includes/core/permalink-manager-pro-functions.php:139
msgid "Your Permalink Manager Pro licence key is invalid!"
msgstr "あなたのライセンスキーは、無効です！"
