=== FileBird - Wordpress Media Library Folders ===
Contributors: ninjateam
Tags: wordpress media library folders, wp media folder, wordpress media folder, wordpress media folders, wordpress media library, wordpress media library plugin, wordpress file manager, wordpress media manager
Requires at least: 3.0
Tested up to: 5.2.2
Stable tag: trunk
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Organize thousands of WordPress media files into folders/ categories at ease.


== Description ==

WordPress media library folders.
Organize thousands of WordPress media files into folders/ categories at ease.

[TEST DRIVE](https://media-folder.ninjateam.org/create/) | [DOCUMENTATION](https://filebird.gitbook.io/) 

Video Demo:
[youtube https://www.youtube.com/watch?v=z0-nC-gVNIA]

== FEATURES ==

**Friendly User Interface**
Drag and drop to upload/move files into folders. Drag and drop to rearrange folders.

**Smart Context Menu**
Right click your mouse to quickly create, rename or delete folders like what you do on your computer.

**Full Control In One Toolbar**
The toolbar on the left allows you control all of your folders/ files. You can create, rearrange, rename or delete folders.

**Unlimited Folders/ Subfolders (PRO)**
You can create unlimited folders/subfolders for all your media files. Lite version support 10 folders/subfolders.

**Many File Types Supported**
As WordPress default, you are allowed to only upload the most commonly used file types. With this plugin, you can manually manage most of allowed file types.

**RTL Supported**
FileBird supports RTL languages including Hebrew, Arabic, etc.

**Many Languages Supported**
This plugin supports most popular languages such as English, Spanish, Italian, French, etc.

== COMPATIBLE WITH ==

* Elementor – Top #1 WordPress page builder.

* Beaver Builder – One of the popular page builder plugins for WordPress.

* Visual Composer Page Builder.

* Divi Theme from ElegantThemes (PRO).

* Divi Builder plugin from ElegantThemes (PRO).

* WPBakery Page Builder - Top #1 page builder selling on CodeCanyon. (PRO)

* WPML - Best WP plugin to translate and display your website in any languages.

* Polylang - A plugin allows you to create a bilingual or multilingual WordPress site.

* Gutenberg - New editor Gutenberg from WordPress 5.0.

* Classic Editor - Old WordPress editor to display tree view folders.

* Most of 3rd party themes or plugins.

== HOW IT WORKS ==
Are you a WordPress website administrator?

Developing a site means that you’ll be adding tons of new media files such as images, videos, audios, PDF, txt, docx, .ect into your site database day by day.

As a result, your site will have to store thousands of files. In the default WordPress media database, you are not able to arrange files in a logical manner and you wish you could do that like what you do with your files in your own computer, right? When you want to look for a particular uploaded file on your WordPress site, don’t you know how to find it?

FileBird was born to help you overcome the obstacle. This plugin gives you the ability to easily organize your files in media library of your site. With this tool in hand, you can manage and arrange thousands of images, audios, videos, and other files in your media library.

Managing files in WordPress has never been easier. You are able to just drag and drop to upload or move files/ folders. This will help you save a ton of time that goes into putting your media files in place. The hierarchical view of your folders will also make things easier for you to quickly find specific folders in just a blink of an eye.

In somes cases, your WordPress media library not loading, please chat with our support at [https://m.me/ninjateam.org/](https://m.me/ninjateam.org) to get help.


== Installation ==
Manual installation is easy and takes fewer than one minute.

1. Download the plugin from wordpress.org, unpack it and upload the **[FileBird]** folder to your **wp-content/plugins/** directory.
2. Activate the plugin through the ‘Plugins‘ menu in WordPress.
3. Go to your main **WordPress menu > Media** to start create folder to manage your files

You’re done. Enjoy.

== Frequently Asked Questions ==
= Is it compatible with WordPress 5? = 
Definitely yes! We’re always up-to-date to give you the best experience in using our plugin.

= When I create a new post, how can I choose an uploaded file in a specific folder? = 
Yes, you can choose the folder in the dropdown list of the filter box beside the search box.

= How can I bulk select multiple files and put them into a folder? =
You can use the bulk select Button in media grid, bulk select files you want and just drag them into a folder.

= Do all of my uploaded files remain the same after I first install this plugin? =
Yes, they do.

= When I move an image to another folder, does this action affect the image appearance on my frontend? =
No, it does not. The image is still displayed correctly on the site frontend.

= Is FileBird free? =
Yes, FileBird is free to use with 10 folders/categories

= Should I purchase a paid plan? =
If you have a large files and want to use more than 10 folders/categories, you can [upgrade to PRO version here](https://codecanyon.net/item/media-folders-manager-for-wordpress/21715379)

== Screenshots ==
1. Full control of your files
2. Drag and drop to upload files
3. Right click to create, rename, refresh, edit or delete folder
4. Bulk selectto move / delete files

== Changelog ==
= 2.3 =
Jul 24, 2019 - Version 2.3
* Improved: Fast append treeview
* Improved: Prevent public folders link
* Improved: CSS UI
* Fixed: Prevent plublic taxonomy folder
* Fixed: Query database callback alltime
* Fixed: FileBird treeview show on top left in product editor in WooCommerce
* Fixed: Can't drop file when create first folder
* Fixed: Work with WPML when Post Types Translation: Media tick not translatable
* Fixed: 10 folders create in Lite version
* Fixed: Bulk select moving images into folders by itself
* Fixed: Move folder with length folder text
* Fixed: Resize bar not save in min minimum width
* Fixed: Remove map file library
* Fixed: Conflict photo gallery

= 2.2 =
Jun 13, 2019 - Version 2.2
* Added: RTL display
* Added: Hebrew language
* Added: Support Advanced Custom Field
* Fixed: Create new folders in edit attachment page
* Fixed: RTL languages in Beaver and Divi builder

= 2.1 =
Jun 04, 2019 - Version 2.1
* Improved: Change drag-drop library
* Improved: Optimize drag-drop
* Improved: Change drag-drop to trigger folders, list view
* Improved: Change syntax jQuery
* Fixed: CSS not show in all folders treeview from classic editor
* Fixed: Move many files
* Fixed: Move all categories
* Fixed: Auto Deselect bulk when none file
* Fixed: Count file selected in bulk select
* Fixed: Conflict with Avada theme
* Updated: CSS 
* Removed: Unused files

= 2.0 =
May 29, 2019 - Version 2.0
* Added: Treeview interface in page, post, custom post type
* Added: Treeview interface in Divi Builder, Beaver Builder, Visual Composer, WPBakery Page Builder, Elementor Builder
* Added: Treeview interface for Gutenberg
* Added: Bulk auto deselect when change folder
* Added: Compatible with WPML 4.x version
* Fixed: WPML 3.9 conflict
* Fixed: Move one file not click image
* Fixed: CSS over when edit folder 
* Fixed: Load too much database
* Fixed: Treeview interface Elementor Builder
* Fixed: Upload images
* Improved: No load FileBird files on frontend if not use builder
* Improved: Optimize FileBird
* Improved: UI/UX
* Removed: Unused files

= 1.8 =
Mar 13, 2019 – Version 1.8
* Added: Compatible with Elementor
* Added: Compatible with Visual Composer
* Added: Compatible with Beaver Builder
* Added: Refresh folder
* Added: Save position for resizable sidebar
* Added: Russian Translation
* Improved: Languages and Documentation
Improved: Doesn't jump to destination folder when moving files
* Improved: CSS
* Improved: Clean code
* Fixed: Removed WordPress notification
* Fixed: Categories dropdown from Media Library popup
* Fixed: Folder structure when insert files in page/post
* Fixed: Press double clicks for OK/cancel button
* Fixed: Bulk select error
* Fixed: Scroll to view folders
* Fixed: Many small bugs

= 1.7 =
Dec 14, 2018 – Version 1.7
* Added: Drag and drop files to folders for List mode
* Added: Count items after dragging
* Added: Load effect while dragging
* Added: Hover effect (List mode and gird mode)
* Added: Show loading progress bar when upload file
* Improved: CSS
* Improved: Clean code
* Improved: Loading effect smoother
* Fixed: Resize bug when dragging back
* Fixed: Hide list attachments after load attachment
* Fixed: Conflict with 404 to 301 plugin
* Fixed: Error in Theme Customize Tab
* Fixed: Some small bugs

= 1.6 =
Aug 15, 2018 – Version 1.6
* Fixed: Some small bugs

= 1.5 =
July 2, 2018 – Version 1.5
* Added: Display location latest folder place use.
* Improved: New UI/UX
* Improved: Change icons
* Improved: CSS styles

= 1.4 =
March 28, 2018 – Version 1.4
* Improved: New drag and drop Javascript
* Fixed: JS conflicts

= 1.3 =
March 18, 2018 – Version 1.3
* Fixed: Load files in folder
* Fixed: More bugs

= 1.2 =
March 10, 2018 – Version 1.2
* Fixed: Drag and drop files
* Fixed: More bugs

= 1.1 =
March 5, 2018 – Version 1.1
* Fixed: JS conflicts
* Fixed: Drag and drop folders

= 1.0 =
May 1, 2016 – Version 1.0
* Version 1.0 Initial Release