.njt-filebird-container{padding: 0 10px;}

.njt-filebird-container ul li{

    margin-bottom: 15px;
    display: inline-block;
    width: calc(16.666666666666668% - 20px);
    margin-right: 50px;
}
.njt-filebird-container .item{
    width: 100%;
    webkit-align-items: center;
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
   -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.07), 0 3px 1px -2px rgba(0, 0, 0, 0), 0 1px 5px 0 rgba(0, 0, 0, 0);
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.07), 0 3px 1px -2px rgba(0, 0, 0, 0), 0 1px 5px 0 rgba(0, 0, 0, 0);
    height: 48px;
    padding: 0 16px;
    background: #fff;

}
.njt-filebird-container .icon{
    background: #fff;
    border-radius: 50%;
    padding: 3px;
    margin-left: 0;
    display: flex;
}
.njt-filebird-container .item:hover{
    background: #0073aa;
    color: #fff;
}
.njt-filebird-container .folder-name{
    margin-left: 16px;
    font-size: 13px;
    white-space: nowrap;
    cursor: default;
}
.njt-filebird-container .item-containt{
    margin-left: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    align-items: center;
}