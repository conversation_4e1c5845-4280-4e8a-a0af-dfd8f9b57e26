jQuery(window).load(function(){const e="https://preview.ninjateam.org/filebird/wp-json/filebird/v1/add";var i="";let n="Unselected",t="none";function a(n,t){jQuery.ajax({method:"POST",contentType:"application/json",url:e,data:JSON.stringify({key:n,feed:t}),dataType:"json"}).done(function(e){window.location.href=i}).fail(function(e){console.log("Feedback can't submit"),console.log(e.responseText),window.location.href=i})}document.querySelector('[data-slug="filebird"] a, [data-slug="filebird-lite"] a').addEventListener("click",function(e){e.preventDefault(),i=document.querySelector('[data-slug="filebird"] a, [data-slug="filebird-lite"] a').getAttribute("href"),tb_show("Quick Feedback - FileBird","#TB_inline?height=370&amp;width=470&amp;inlineId=filebird-feedback-window"),jQuery("#TB_window").css({marginLeft:"-"+parseInt(250,10)+"px",width:"500px",height:"330px",marginTop:parseInt((jQuery(window).height()-330)/2,10)+"px"}),jQuery(window).resize(function(){jQuery("#TB_window").css({marginLeft:"-"+parseInt(250,10)+"px",width:"500px",height:"330px",marginTop:parseInt((jQuery(window).height()-330)/2,10)+"px"})}),jQuery("#TB_ajaxContent input[type=radio]").on("change",function(){n=jQuery("input[name=reasons]:checked","#TB_ajaxContent").val(),"other"===(t=jQuery("input[name=reasons]:checked","#TB_ajaxContent").attr("id"))?(jQuery("#feedback-suggest-plugin").hide(),jQuery("#feedback-description").show(),jQuery("#feedback-description").on("change",function(){n=jQuery("#feedback-description").val()}),jQuery("#TB_window").css({marginLeft:"-"+parseInt(250,10)+"px",width:"500px",height:"400px"})):"found_better_plugin"===t?(jQuery("#feedback-description").hide(),jQuery("#feedback-suggest-plugin").show(),jQuery("#feedback-suggest-plugin").on("change",function(){n=jQuery("#feedback-suggest-plugin").val()}),jQuery("#TB_window").css({marginLeft:"-"+parseInt(250,10)+"px",width:"500px",height:"360px"})):(jQuery("#feedback-description").hide(),jQuery("#feedback-suggest-plugin").hide(),jQuery("#TB_window").css({marginLeft:"-"+parseInt(250,10)+"px",width:"500px",height:"330px"}))}),jQuery("#feedback-submit").click(function(){a(t,n)}),jQuery("#feedback-skip").click(function(){a("skip","skip")})})});