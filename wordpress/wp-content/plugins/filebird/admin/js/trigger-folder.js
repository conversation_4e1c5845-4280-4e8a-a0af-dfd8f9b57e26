var njt_trigger_folder={};!function(e){"use strict";njt_trigger_folder.jQueryExtensions=void e.fn.extend({move_folder:function(){return this.each(function(){var t=e(this),r=parseInt(t.menuItemDepth(),10),i=r-1,n=t.prevAll(".menu-item-depth-"+i).first(),l=0;0!==r&&(l=n.find(".menu-item-data-db-id").val());var a=t.find(".menu-item-data-db-id").val();njt_trigger_folder.updateFolderList(a,l,"move")})}}),njt_trigger_folder.rename=function(t,r){ntWMC.filebird_begin_loading();var i={action:"filebird_ajax_update_folder_list",current:t,new_name:r,type:"rename"};e.post(ajaxurl,i,function(e){"error"==e&&swal({title:filebird_translate.oops,text:filebird_translate.this_folder_is_already_exists,type:"error"}).then(function(){location.reload()})}).fail(function(){swal({title:filebird_translate.error,text:filebird_translate.error_occurred,type:"error"}).then(function(){location.reload()})}).complete(function(){ntWMC.filebird_finish_loading()})},njt_trigger_folder.delete=function(t){ntWMC.filebird_begin_loading();var r={action:"filebird_ajax_delete_folder_list",current:t};e.post(ajaxurl,r,function(e){"error"==e&&swal({title:filebird_translate.error,text:filebird_translate.folder_cannot_be_delete,type:"error"}).then(function(){location.reload()})}).fail(function(){swal({title:filebird_translate.error,text:filebird_translate.error_occurred,type:"error"}).then(function(){location.reload()})}).success(function(r){ntWMC.updateCountAfternDeleteFolder(r),e(".menu-item.uncategory .jstree-anchor").addClass("need-refresh"),t==localStorage.getItem("current_folder")&&localStorage.removeItem("current_folder");var i=e("#menu-item-"+t).find(".menu-item-data-parent-id").val();i&&(e(".menu-item .menu-item-data-parent-id").filter(function(){return e(this).val()==i}).length||e("#menu-item-"+i+" .dh-tree-icon").removeClass("open close")),e("#menu-item-"+t).remove(),ntWMC.filebird_finish_loading()})},njt_trigger_folder.new=function(t,r){ntWMC.filebird_begin_loading();var i={action:"filebird_ajax_update_folder_list",new_name:t,parent:r,folder_type:"default",type:"new"};e.post(ajaxurl,i,function(e){"error"==e&&swal({title:filebird_translate.error,text:filebird_translate.folder_cannot_be_delete,type:"error"}).then(function(){location.reload()})}).fail(function(){swal({title:filebird_translate.error,text:filebird_translate.error_occurred,type:"error"}).then(function(){location.reload()})}).success(function(t){filebird_taxonomies.folder.term_list.push({term_id:t.data.term_id,term_name:"new tmp folder"});var r=e(".filebird_sidebar"),i=ntWMC.ntWMCgetBackboneOfMedia(r);if("object"==typeof i.view){var n=i.view.toolbar.get("folder-filter");"object"==typeof i.view&&n.createFilters()}var l=e("<option></option>").attr("value",t.data.term_id).text("new tmp folder");e(".wpmediacategory-filter").append(l),e(".jstree-anchor.jstree-clicked").removeClass("jstree-clicked"),njt_trigger_folder.update_folder_position(),ntWMC.filebird_finish_loading()})},njt_trigger_folder.updateFolderList=function(t,r,i){var n={action:"filebird_ajax_update_folder_list",current:t,new_name:0,parent:r,type:i,folder_type:"folder"};e.post(ajaxurl,n,function(t){"error"==t?swal({title:filebird_translate.oops,text:filebird_translate.this_folder_is_already_exists,type:"error"}).then(function(){location.reload()}):(njt_trigger_folder.update_folder_position(),e(".need-refresh").trigger("click"))}).fail(function(){swal({title:filebird_translate.error,text:filebird_translate.error_occurred,type:"error"}).then(function(){location.reload()})})},njt_trigger_folder.update_folder_position=function(){ntWMC.filebird_begin_loading();var t="",r="";e("#njt-filebird-folderTree .menu-item-data-db-id").each(function(){r+="0",""!=t&&(t+="|"),t=t+e(this).val()+","+r});var i={action:"filebird_ajax_update_folder_position",result:t};e.post(ajaxurl,i,function(e){"error"==e&&swal({title:filebird_translate.oops,text:filebird_translate.something_not_correct+filebird_translate.this_page_will_reload,type:"error"}).then(function(){location.reload()}),ntWMC.filebird_finish_loading()}).fail(function(){ntWMC.filebird_finish_loading(),swal({title:filebird_translate.error,text:filebird_translate.error_occurred,type:"error"}).then(function(){location.reload()})}).success(function(t){var r=e(".wpmediacategory-filter").val();e("#menu-item-"+r+" .jstree-anchor").addClass("need-load-children"),e("#menu-item-"+r+" .jstree-anchor").trigger("click")})},njt_trigger_folder.filter_media=function(t){if(null==t);else{var r=t.closest(".menu-item").data("id");if(e(".need-refresh").length||e(".filebird-treeview").length){var i=e(".filebird_sidebar"),n=ntWMC.ntWMCgetBackboneOfMedia(i);if(n.browser.length>0&&"object"==typeof n.view)try{n.view.collection.props.set({ignore:+new Date})}catch(e){console.log(e)}e(".need-refresh").removeClass("need-refresh")}e(".wpmediacategory-filter").val(r),e(".wpmediacategory-filter").trigger("change"),e(".attachments").css("height","auto")}},njt_trigger_folder.getChildFolder=function(t){e(".njt-filebird-container").length&&e(".njt-filebird-container").remove();var r={action:"filebird_ajax_get_child_folders",folder_id:t};e.post(ajaxurl,r,function(e){}).fail(function(){}).success(function(e){njt_folder_in_content.render(e.data)})},e("#njt-filebird-folderTree .jstree-anchor").dblclick(function(e){e.preventDefault()});var t=0,r=null;function i(t,r){e(".wrap").html(e(t).find(".wrap").html()),e("#folders-to-edit li").removeClass("current_folder"),e("ul.jstree-container-ul li").removeClass("current-dir current_folder"),""==r||null==r?e("#menu-item-all").addClass("current-dir"):"-1"==r?e("#menu-item--1").addClass("current-dir"):e("#menu-item-"+r).addClass("current_folder"),e.each(filebird_taxonomies.folder.term_list,function(t,r){e(".wpmediacategory-filter").append('<option value="'+r.term_id+'">'+r.term_name+"</option>")}),e(".wpmediacategory-filter").val(r);var i=e("#njt-filebird-attachment"),n=filebird_translate.move_1_file;e.each(e("table.wp-list-table tr"),function(t,r){e(r).draggable({cursorAt:{top:0,left:0},helper:function(t){return e("<span></span>")},start:function(t,r){var l=e('.wp-list-table input[name="media[]"]:checked');l.length>0&&(n=filebird_translate.Move+" "+l.length+" "+filebird_translate.files),i.html(n),i.show(),e("body").addClass("njt-draging")},stop:function(t,r){i.hide(),e("body").removeClass("njt-draging")},drag:function(t,r){var n=e(this).attr("id");n=n.match(/post-([\d]+)/),i.data("id",n[1]),i.css({top:t.clientY-15,left:t.clientX-15})}})}),ntWMC.filebird_finish_loading()}e(document).on("click",".filebird_sidebar .jstree-anchor",function(n){var l=e(this),a=l.closest(".menu-item").data("id");if(1===++t)if(e('select[name="njt_filebird_folder"]').length){if(e('select[name="njt_filebird_folder"]').val(a),e(".njt-filebird-loader").hasClass("loading"))return;ntWMC.filebird_begin_loading(),r=setTimeout(function(){var r=e("#posts-filter").serialize();e.ajax({url:njt_filebird_dh.upload_url,type:"GET",data:r}).done(function(e){window.history.pushState({},"",njt_filebird_dh.upload_url+"?"+r),i(e,a)}).fail(function(){ntWMC.filebird_finish_loading(),console.log("error")}),oldCurrentFolder=localStorage.getItem("current_folder"),t=0},200)}else r=setTimeout(function(){njt_trigger_folder.filter_media(l),(oldCurrentFolder!=localStorage.getItem("current_folder")||l.hasClass("need-load-children"))&&(e("body.wp-admin.upload-php").length&&njt_trigger_folder.getChildFolder(a),l.removeClass("need-load-children")),oldCurrentFolder=localStorage.getItem("current_folder"),t=0},200);else clearTimeout(r),e(".js__nt_rename").trigger("click"),t=0}),e(document).on("click","body.wp-admin.upload-php .pagination-links a",function(t){t.preventDefault();var r=e(this);if(!e(".njt-filebird-loader").hasClass("loading"))return ntWMC.filebird_begin_loading(),e.ajax({url:r.attr("href"),type:"GET",data:{}}).done(function(t){window.history.pushState({},"",r.attr("href")),i(t,e('select[name="njt_filebird_folder"]').val())}).fail(function(){ntWMC.filebird_finish_loading(),console.log("error")}),!1}),e("body.wp-admin.upload-php").length&&e(document).on("submit","#posts-filter",function(t){t.preventDefault();e(this);if(!e(".njt-filebird-loader").hasClass("loading")){ntWMC.filebird_begin_loading();var r=e("#posts-filter").serialize();return e.ajax({url:njt_filebird_dh.upload_url,type:"GET",data:r}).done(function(t){window.history.pushState({},"",njt_filebird_dh.upload_url+"?"+r),i(t,e('select[name="njt_filebird_folder"]').val())}).fail(function(){ntWMC.filebird_finish_loading(),console.log("error")}),!1}})}(jQuery);