!function(t){"use strict";t(document).ready(function(){wp.media&&function(i){var e=i.media.view.Attachment.Library,n=filebird_translate.move_1_file;t("body").append('<div id="njt-filebird-attachment" data-id="">'+n+"</div>"),i.media.view.Attachment.Library=i.media.view.Attachment.Library.extend({initialize:function(){e.prototype.initialize.apply(this,arguments),this.on("ready",function(){ntWMC.dragFile(t(this.el))})}}),t("body.wp-admin.upload-php").length&&ntWMC.dropFile(),setTimeout(function(){var i=localStorage.getItem("current_folder")||"all";t("#menu-item-"+i+" .jstree-anchor").trigger("click")},1e3),t(".menu-item-bar").bind({mouseenter:function(){var i=t(this),e=i.find(".item-title").innerWidth(),n=i.find(".menu-item-title").innerWidth(),a=i.find(".menu-item-title").text();e<n+10&&(i.tooltip({title:a,placement:"bottom"}),i.tooltip("show"))},mouseleave:function(){t(this).tooltip("hide")}})}(wp)})}(jQuery);