!function(e){"use strict";var t=-1;setInterval(function(){e(".njt-filebird-editcategory-filter").change(function(n){void 0!==n.originalEvent&&(t=Number(e(".njt-filebird-editcategory-filter").val()))});var n=e(".media-toolbar-secondary .wpmediacategory-filter"),i=e(".njt-filebird-editcategory-filter");n.length&&i.length&&n.on("change",function(){i.val(n.val()),i.trigger("change"),t=Number(n.val())})},1e3),e(document).ready(function(){var n=window.wp;void 0!==n&&"function"==typeof n.Uploader&&e.extend(n.Uploader.prototype,{init:function(){this.uploader&&(this.uploader.bind("FileFiltered",function(e,t){}),this.uploader.bind("FilesAdded",function(e,t){}),this.uploader.bind("BeforeUpload",function(e,n){var i=e.settings.multipart_params;console.log("nt_wcm_post_folder_id",t),Number.isInteger(t)&&t>0&&(i.ntWMCFolder=t)}),this.uploader.bind("UploadProgress",function(t,n){e(".uploader-window").hide().css("opacity",0),ntWMC.filebird_begin_loading()}),this.uploader.bind("UploadComplete",function(t,n){ntWMC.filebird_finish_loading();var i=e(".media-toolbar-secondary"),o=ntWMC.ntWMCgetBackboneOfMedia(i);if(o.browser.length>0&&"object"==typeof o.view)try{o.view.collection.props.set({ignore:+new Date})}catch(e){console.log(e)}else console.log("test-err")}),this.uploader.bind("FilesAdded",function(t,n){var i=e(".wpmediacategory-filter").val(),o=jQuery('.menu-item[data-id="'+i+'"] .jstree-anchor'),r=jQuery(".menu-item[data-id=-1] .jstree-anchor"),a=jQuery('.menu-item[data-id="all"] .jstree-anchor');n.forEach(function(e){ntWMC.updateCount(null,i),"all"===i?(r.addClass("need-refresh"),ntWMC.updateCount(null,-1)):-1===Number(i)?(a.addClass("need-refresh"),ntWMC.updateCount(null,"all")):(o.addClass("need-refresh"),ntWMC.updateCount(null,"all"))})}))}})})}(jQuery);