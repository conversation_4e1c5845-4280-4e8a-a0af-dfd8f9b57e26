var FileBird_Popup={init:function(){jQuery(".media-button-reverse").is(":visible")||(jQuery("#filebird_sidebar").length?setTimeout(()=>{var e=jQuery("div[id^='__wp-uploader-id-'].supports-drag-drop:visible").attr("id");if(e){jQuery("#filebird_sidebar").appendTo("#"+e+" .media-menu");var r=localStorage.getItem("current_folder")||"all";jQuery("#menu-item-"+r+" .jstree-anchor").trigger("click"),jQuery("#filebird_sidebar").show(),jQuery(".fb-treeview-loading").hide()}},300):jQuery.ajax({url:ajaxurl,type:"post",dataType:"text",data:{action:"filebird_ajax_treeview_folder"},success:function(e){var r=jQuery("div[id^='__wp-uploader-id-'].supports-drag-drop:visible");if(r.length){var t="#"+jQuery(r).attr("id")+" .media-menu";jQuery(e).appendTo(t),jQuery(".fb-treeview-loading").hide(),DhTreeFolder.init(),ntWMC.dropFile();var i=localStorage.getItem("current_folder")||"all";jQuery("#menu-item-"+i+" .jstree-anchor").trigger("click"),jQuery("#filebird_sidebar").show(),setTimeout(()=>{jQuery("#filebird_sidebar").bind("remove",function(){FileBird_Popup.init()})},100),FileBird_Popup.jstree.init();var l=jQuery(t+" .media-menu-item:not(.fb-treeview-loading)").length;jQuery("#njt-filebird-folderTree").mCustomScrollbar({autoHideScrollbar:!0,setHeight:jQuery(t).height()-34*l-240}),FileBird_Popup.toolbar.init()}}}))},jstree:{init:function(){FileBird_Popup.jstree.default(),"all"!==localStorage.getItem("current_folder")&&"undefined"!==localStorage.getItem("current_folder")&&null!=localStorage.getItem("current_folder")||jQuery("#menu-item-all .menu-item-bar").trigger("click")},default:function(){jQuery("#njt-filebird-defaultTree").length&&(jQuery("#njt-filebird-defaultTree").jstree({core:{themes:{responsive:!1,icons:!1}}}),jQuery("#njt-filebird-defaultTree").on("changed.jstree",function(e,r){if(r.node){var t=r.node.li_attr["data-id"];if(localStorage.setItem("current_folder",t),jQuery(".jstree-anchor.jstree-clicked").removeClass("jstree-clicked"),jQuery(".jstree-node.current-dir").removeClass("current-dir"),jQuery(".jstree-node[data-id='"+t+"']").addClass("current-dir"),jQuery(".jstree-node[data-id='"+t+"']").children(".jstree-anchor").addClass("jstree-clicked"),jQuery(".jstree-anchor.need-refresh").length){var i=jQuery(".filebird_sidebar"),l=ntWMC.ntWMCgetBackboneOfMedia(i);if(l.browser.length>0&&"object"==typeof l.view)try{l.view.collection.props.set({ignore:+new Date})}catch(e){console.log(e)}else window.location.reload();jQuery(".jstree-anchor.need-refresh").removeClass("need-refresh")}jQuery(".wpmediacategory-filter").val(t),jQuery(".wpmediacategory-filter").trigger("change")}jQuery(".menu-item.current_folder").length&&(jQuery('select[name="njt_filebird_folder"]').length||jQuery(".menu-item.current_folder").removeClass("current_folder"))}))}},sweetAlert:{delete:function(e){var r=0;r=Array.isArray(e)?e[0].data("id"):e.data("id");var t=jQuery("menu-item-"+r);jQuery(t).next().find(".menu-item-data-parent-id").length&&jQuery(t).next().find(".menu-item-data-parent-id").val()==r?swal({title:filebird_translate.oops,text:filebird_translate.folder_are_sub_directories,type:"error"}):swal({title:filebird_translate.are_you_sure,text:filebird_translate.not_able_recover_folder,type:"warning",confirmButtonText:filebird_translate.yes_delete_it,showCancelButton:!0,cancelButtonText:filebird_translate.cancel}).then(function(e){e.value&&(njt_trigger_folder.delete(r),swal(filebird_translate.deleted+"!",filebird_translate.folder_deleted,"success"))})}},toolbar:{init:function(){FileBird_Popup.toolbar.create(),FileBird_Popup.toolbar.delete()},create:function(){jQuery(".js__nt_create").length&&jQuery(".js__nt_create").on("click",function(){var e,r=jQuery("#njt-filebird-folderTree").jstree(!0),t=jQuery(this).data("type");(e=r.create_node(null,{type:t}))&&r.edit(e)})},delete:function(){jQuery(".js__nt_delete").length&&jQuery(".js__nt_delete").on("click",function(){var e=jQuery("#njt-filebird-folderTree .current_folder");if(!e.length)return!1;FileBird_Popup.sweetAlert.delete(e)})}},tooltip:{init:function(){jQuery(".js__nt_tipped").length&&Tipped.create(".js__nt_tipped",function(e){return{title:jQuery(e).data("title"),content:jQuery(e).data("content")}},{skin:"blue",maxWidth:250})}}};