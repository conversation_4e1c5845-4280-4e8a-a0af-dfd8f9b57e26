!function(t){"use strict";t(document).ready(function(){wp.media.view.Attachment.Library;var e=filebird_translate.move_1_file;t("body").append('<div id="njt-filebird-attachment" data-id="">'+e+"</div>");var i=t("#njt-filebird-attachment");t.each(filebird_taxonomies.folder.term_list,function(e,i){t(".wpmediacategory-filter").append('<option value="'+i.term_id+'">'+i.term_name+"</option>")}),t(".wpmediacategory-filter").val(njt_filebird_dh.current_folder),t.each(t("table.wp-list-table tr"),function(a,n){t(n).draggable({cursorAt:{top:0,left:0},helper:function(e){return t("<span></span>")},start:function(a,n){var d=t('.wp-list-table input[name="media[]"]:checked');d.length>0&&(e=filebird_translate.Move+" "+d.length+" "+filebird_translate.files),i.html(e),i.show(),t("body").addClass("njt-draging")},stop:function(e,a){i.hide(),t("body").removeClass("njt-draging")},drag:function(e,a){var n=t(this).attr("id");n=n.match(/post-([\d]+)/),i.data("id",n[1]),i.css({top:e.clientY-15,left:e.clientX-15})}})}),t("#wpcontent .jstree-anchor").droppable({drop:function(e,a){var n=t(this).parent().attr("data-id"),d=function(){var e=t('.wp-list-table input[name="media[]"]:checked'),i=[];if(e.length)return e.each(function(e,a){i.push(t(a).val())}),i;return!1}();d.length?function(e,i,a){t(a.target).addClass("need-refresh");var n={};n.ids=e,n.folder_id=i,n.action="filebird_save_multi_attachments",ntWMC.filebird_begin_loading(),t.each(n.ids,function(e,i){t("#post-"+i).addClass("njt-opacity")}),jQuery.ajax({type:"POST",dataType:"json",data:n,url:ajaxurl,success:function(e){if(e.success&&(e.data.forEach(function(t){ntWMC.updateCount(t.from,t.to)}),t(".jstree-anchor").addClass("need-refresh"),null!=t(".wpmediacategory-filter").val())){t.each(n.ids,function(e,i){t("#post-"+i).remove()});var i=t(".wp-list-table tbody tr").length;0==i?(t(".wp-list-table tbody").append(njt_filebird_dh.no_item_html),t(".displaying-num").hide()):t(".displaying-num").text(i+" "+(1==i?njt_filebird_dh.item:njt_filebird_dh.items))}t(".wp-list-table tbody tr").removeClass("njt-opacity"),ntWMC.filebird_finish_loading()}})}(d,n,e):function(e,a){var n=i.data("id"),d=t('.attachment[data-id="'+n+'"]'),r=t(".wpmediacategory-filter").val();if("all"===a||a==r)return void t(".wp-list-table tbody tr").removeClass("njt-opacity");ntWMC.filebird_begin_loading(),t("#post-"+n).addClass("njt-opacity"),jQuery.ajax({type:"POST",dataType:"json",data:{id:n,action:"nt_wcm_get_terms_by_attachment"},url:ajaxurl,success:function(i){var l=Array.from(i.data,t=>t.term_id);if(l.includes(Number(a)))return ntWMC.filebird_finish_loading(),void t(".wp-list-table tbody tr").removeClass("njt-opacity");t(e.target).addClass("need-refresh");var o={};o.id=n,o.attachments={},o.attachments[n]={menu_order:0},o.folder_id=a,o.action="filebird_save_attachment",jQuery.ajax({type:"POST",dataType:"json",data:o,url:ajaxurl,success:function(e){if(e.success&&(t.each(l,function(t,e){ntWMC.updateCount(e,a)}),console.log(r,l.length),"all"!==r||l.length||ntWMC.updateCount(-1,a),-1==r&&ntWMC.updateCount(-1,a),"all"!=r&&d.detach()),ntWMC.filebird_finish_loading(),t(".wp-list-table tbody tr").removeClass("njt-opacity"),null!=t(".wpmediacategory-filter").val()){t("#post-"+o.id).remove();var i=t(".wp-list-table tbody tr").length;0==i?(t(".wp-list-table tbody").append(njt_filebird_dh.no_item_html),t(".displaying-num").hide()):t(".displaying-num").text(i+" "+(1==i?njt_filebird_dh.item:njt_filebird_dh.items))}}})}})}(e,n)}}),t(".menu-item-bar").bind({mouseenter:function(){var e=t(this),i=e.find(".item-title").innerWidth(),a=e.find(".menu-item-title").innerWidth(),n=e.find(".menu-item-title").text();i<a+10&&(e.tooltip({title:n,placement:"bottom"}),e.tooltip("show"))},mouseleave:function(){t(this).tooltip("hide")}})})}(jQuery);