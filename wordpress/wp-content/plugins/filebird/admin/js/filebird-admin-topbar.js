window.wp=window.wp||{},function(e){"use strict";var t=wp.media;t.view.AttachmentFilters.Taxonomy=t.view.AttachmentFilters.extend({tagName:"select",createFilters:function(){var t={};_.each(filebird_taxonomies.folder.term_list||{},function(i,r){var a=i.term_id,n=e("<div/>").html(i.term_name).text();t[a]={text:n,priority:r+2},t[a].props={},t[a].props[filebird_folder]=a}),t.all={text:filebird_taxonomies.folder.list_title,priority:1},t.all.props={},t.all.props[filebird_folder]=null,this.filters=t}});var i=t.view.AttachmentsBrowser;t.view.AttachmentsBrowser=t.view.AttachmentsBrowser.extend({createToolbar:function(){var t=jQuery.Deferred();this.$el.data("backboneView",this),this._treeLoaded=t,i.prototype.createToolbar.apply(this,arguments);var r=new wp.media.view.AttachmentFilters.Taxonomy({className:"wpmediacategory-filter attachment-filters",controller:this.controller,model:this.collection.props,priority:-75}).render();setTimeout(()=>{if(!e("body.wp-admin.upload-php").length){var t=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var i=[],r=!0,a=!1,n=void 0;try{for(var l,o=e[Symbol.iterator]();!(r=(l=o.next()).done)&&(i.push(l.value),!t||i.length!==t);r=!0);}catch(e){a=!0,n=e}finally{try{!r&&o.return&&o.return()}finally{if(a)throw n}}return i}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),i=this.views.parent,r=t(i.views.get(".media-frame-menu"),1)[0];i.$el.find(".fb-treeview-loading").length||r.views.add(new wp.media.View({className:"fb-treeview",el:'<a href="#" class="fb-treeview-loading media-menu-item">Filebird is loading <span class="spinner"></span></a>'}));var a=e(i.$el).attr("id");e("#"+a).find("#filebird_sidebar").length&&e(".fb-treeview-loading").hide(),i.$el.addClass("filebird-treeview").removeClass("hide-menu not-show")}},50),this.toolbar.set("folder-filter",r),r.initialize()}}),t.view.UploaderInline=t.view.UploaderInline.extend({prepare:function(){setTimeout(()=>{if(!e("body.wp-admin.upload-php").length){this.views.parent.$el.addClass("filebird-treeview not-show").removeClass("hide-menu")}},50)}})}(jQuery);