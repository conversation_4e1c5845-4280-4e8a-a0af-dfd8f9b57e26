var DhTreeFolder,oldCurrentFolder=null,njt_filebird_free="11"!==njtFBV,njt_filebird_pro_link="https://1.envato.market/FileBird2";function editFolderCancel(){var e=jQuery(".edit-folder-cancel.button");jQuery("#menu-item-"+e.closest(".edit-folder-wrap").data("id")).show(),jQuery(".edit-folder-wrap").remove()}function editFolderNow(){var e=jQuery(".edit-folder-now.button"),t=jQuery(".edit-folder-name").val();if(""==t)return alert("Please enter your folder name."),!1;var n=e.closest(".edit-folder-wrap").data("id"),r=jQuery("#menu-item-"+n);r.find(".menu-item-title").text(t),r.show(),jQuery(".edit-folder-wrap").remove(),njt_trigger_folder.rename(n,t),jQuery.event.trigger({type:"DhTreeFolder_renamed",id:n,new_name:t})}!function(e){var t;t=DhTreeFolder={options:{menuItemDepthPerLevel:30,globalMaxDepth:11,sortableItems:"> *",targetTolerance:0},menuList:void 0,menusChanged:!1,isRTL:!("undefined"==typeof isRtl||!isRtl),negateIfRTL:"undefined"!=typeof isRtl&&isRtl?-1:1,menus:{moveUp:"Move up one",moveDown:"Move down one",moveToTop:"Move to the top",moveUnder:"Move under %s",moveOutFrom:"Move out from under %s",under:"Under %s",outFrom:"Out from under %s",menuFocus:"%1$s. Menu item %2$d of %3$d.",subMenuFocus:"%1$s. Sub item number %2$d under %3$s."},current_folder:localStorage.getItem("current_folder")||"all",current_parent:null,old_parent:null,state:localStorage.getItem("tree_state")?localStorage.getItem("tree_state").split(","):[],init:function(){t.menuList=e("#folders-to-edit"),this.jQueryExtensions(),t.menuList.length&&this.initSortables(),this.setIcons(),this.newBehavior()},njt_filebird_upgrade_options:function(){return{title:filebird_translate.notice,html:filebird_translate.limit_folder,type:"warning",showCancelButton:!0,confirmButtonText:filebird_translate.ok,cancelButtonText:filebird_translate.no_thank,confirmButtonClass:"bnt-upgrade",cancelButtonClass:"btn-text"}},setIcons:function(){var n=t.menuList.find("li.menu-item");e.each(n,function(n,r){var i=e(r).menuItemDepth(),l=e(r).next();l.hasClass("menu-item")&&(l.menuItemDepth()>i&&(t.state.indexOf(e(r).data("id").toString())<0?(e(r).childMenuItems().wrapAll('<li class="new-wrapper children_of_'+e(r).attr("id")+'"><ul></ul></li>'),e(r).find(".dh-tree-icon").addClass("has_children").addClass("close")):e(r).find(".dh-tree-icon").addClass("has_children").addClass("open")))}),e(document).off("click",".dh-tree-icon.has_children").on("click",".dh-tree-icon.has_children",function(n){n.preventDefault();var r=e(this),i=r.closest("li.menu-item"),l=i.data("id");if(r.hasClass("open"))i.childMenuItems().wrapAll('<li class="new-wrapper children_of_'+i.attr("id")+'"><ul></ul></li>'),r.removeClass("open").addClass("close"),t.state.splice(t.state.indexOf(l.toString()),1),localStorage.setItem("tree_state",t.state);else if(r.hasClass("close")){e(".children_of_"+i.attr("id")+" >ul>li.menu-item").unwrap().unwrap(),r.removeClass("close").addClass("open"),t.state.indexOf(l.toString())<0&&(t.state.push(l),localStorage.setItem("tree_state",t.state))}oldCurrentFolder=localStorage.getItem("current_folder"),localStorage.setItem("current_folder",l),i.find(".jstree-anchor").trigger("click")})},newBehavior:function(){e(document).on("click",".menu-item-bar",function(n){n.preventDefault();var r=e(this);t.doSetCurrentFolder(r)}),e.contextMenu({selector:".menu-item-bar",animation:{duration:200,show:"fadeIn",hide:"fadeOut"},zIndex:999,callback:function(n,r){if("new"==n){if(e("#folders-to-edit li").length>=11&&njt_filebird_free)return swal(t.njt_filebird_upgrade_options()).then(e=>{switch(e.value){case!0:window.open(njt_filebird_pro_link,"_blank")}}),!1;t.doSetCurrentFolder(r.$trigger),t.doInsertFolder()}else if("rename"==n){if(e(".folder-input").length)return e(".folder-input").focus(),void(i=e(".folder-input")).putCursorAtEnd().on("focus",function(){i.putCursorAtEnd()});var i,l=(s=r.$trigger.closest(".menu-item")).find(".menu-item-data-db-id").val(),o=s.find(".menu-item-title").text(),a=s.menuItemDepth(),d=t.editFolderFormTemplate(l,o,"menu-item-depth-"+a);e(d).insertAfter(s),(i=e(".edit-folder-name")).putCursorAtEnd().on("focus",function(){i.putCursorAtEnd()}),s.hide()}else if("delete"==n){l=(s=r.$trigger.closest(".menu-item")).find(".menu-item-data-db-id").val();e(s).next().find(".menu-item-data-parent-id").length&&e(s).next().find(".menu-item-data-parent-id").val()==l?swal({title:filebird_translate.oops,text:filebird_translate.folder_are_sub_directories,type:"error"}):swal({title:filebird_translate.are_you_sure,text:filebird_translate.not_able_recover_folder,type:"warning",confirmButtonText:filebird_translate.yes_delete_it,showCancelButton:!0,cancelButtonText:filebird_translate.cancel}).then(function(e){e.value&&(njt_trigger_folder.delete(l),swal(filebird_translate.deleted+"!",filebird_translate.folder_deleted,"success"))})}else if("refresh"==n){var s;l=(s=r.$trigger.closest(".menu-item")).find(".menu-item-data-db-id").val();t.doRefreshFolder(l)}},items:{new:{name:filebird_translate.new_folder,icon:"context-menu-icon context-menu-icon-new"},refresh:{name:filebird_translate.refresh,icon:"context-menu-icon context-menu-icon-refresh"},rename:{name:filebird_translate.rename,icon:"context-menu-icon context-menu-icon-rename"},delete:{name:filebird_translate.delete,icon:"context-menu-icon context-menu-icon-delete"}}}),e(document).on("keypress",".edit-folder-name",function(t){13==t.which&&(t.preventDefault(),e(".edit-folder-now").trigger("click"))}),e(document).on("keypress",".add-new-folder-name",function(t){13==t.which&&(t.preventDefault(),e(".add-new-folder-now").trigger("click"))}),e(document).on("click",".add-new-folder-now",function(n){if(n.preventDefault(),e("#folders-to-edit li").length>=11&&njt_filebird_free){e("div");return swal(t.njt_filebird_upgrade_options()).then(e=>{switch(e.value){case!0:window.open(njt_filebird_pro_link,"_blank")}}),!1}e(this);var r=e(".add-new-folder-name").val();if(""==r)alert("Please enter folder name!");else{var i=0,l=0,o=e("#menu-item-"+i),a=0;null!=t.current_folder&&(i=t.current_folder,o=e("#menu-item-"+i),l=o.menuItemDepth(),a=l,l=parseInt(l)+1),ntWMC.filebird_begin_loading();var d={action:"filebird_ajax_update_folder_list",new_name:r,parent:i,folder_type:"default",type:"new"};e.post(ajaxurl,d,function(e){"error"==e&&swal({title:filebird_translate.error,text:filebird_translate.error_occurred,type:"error"}).then(function(){location.reload()})}).fail(function(){swal({title:filebird_translate.error,text:filebird_translate.error_occurred,type:"error"}).then(function(){location.reload()})}).success(function(n){var r=t.newFolderTemplate(n.data.term_id,n.data.term_name,i,l);if(e(".new-folder-wrap").remove(),null==t.current_folder)e("#folders-to-edit").append(r);else if(0==e('[class="menu-item-data-parent-id"][value="'+t.current_folder+'"]').length)e(r).insertAfter(e("#menu-item-"+t.current_folder)),e("#menu-item-"+t.current_folder).find(".dh-tree-icon").addClass("has_children open");else{var o=e("#menu-item-"+t.current_folder).nextAll();e.each(o,function(t,n){if(e(n).menuItemDepth()<=a)return e(r).insertAfter(e(n).prev()),!1;t==o.length-1&&e(r).insertAfter(e(n))})}filebird_taxonomies.folder.term_list.push({term_id:n.data.term_id,term_name:"new tmp folder"});var d=e(".filebird_sidebar"),s=ntWMC.ntWMCgetBackboneOfMedia(d);if("object"==typeof s.view){var u=s.view.toolbar.get("folder-filter");"object"==typeof s.view&&u.createFilters()}var m=e("<option></option>").attr("value",n.data.term_id).text("new tmp folder");e(".wpmediacategory-filter").append(m),e(".jstree-anchor.jstree-clicked").removeClass("jstree-clicked"),njt_trigger_folder.update_folder_position(),i&&t.state.indexOf(i.toString())<0&&(t.state.push(i),localStorage.setItem("tree_state",t.state)),ntWMC.dropFile(),ntWMC.filebird_finish_loading()})}}),e(document).on("click",".add-new-folder-cancel",function(t){t.preventDefault(),e(".new-folder-wrap").remove()}),e(".new-folder").click(function(n){if(e("#folders-to-edit li").length>=11&&njt_filebird_free)return swal(t.njt_filebird_upgrade_options()).then(e=>{switch(e.value){case!0:window.open(njt_filebird_pro_link,"_blank")}}),!1;t.doInsertFolder()}),e(document).on("click","#njt-filebird-defaultTree .jstree-anchor",function(e){e.preventDefault(),t.doSetCurrentFolder()}),e("#njt-filebird-folderTree .jstree-anchor").dblclick(function(t){t.preventDefault(),setTimeout(function(){e(".js__nt_rename").trigger("click")},100)}),e(".js__nt_rename").click(function(n){if(e(".folder-input").length)return e(".folder-input").focus(),(i=e(".folder-input")).putCursorAtEnd().on("focus",function(){i.putCursorAtEnd()}),!1;var r=e(".menu-item.current_folder");if(r.length){var i,l=r.find(".menu-item-data-db-id").val(),o=r.find(".menu-item-title").text(),a=r.menuItemDepth(),d=t.editFolderFormTemplate(l,o,"menu-item-depth-"+a);e(d).insertAfter(r),r.hide(),e(".edit-folder-name").focus(),(i=e(".edit-folder-name")).putCursorAtEnd().on("focus",function(){i.putCursorAtEnd()})}})},doSetCurrentFolder:function(n){if(null==n)t.menuList.find("li.menu-item").removeClass("current_folder"),t.current_folder=null;else{t.menuList.find("li.menu-item").removeClass("current_folder"),n.closest(".menu-item").addClass("current_folder"),e(".jstree-anchor.jstree-clicked").removeClass("jstree-clicked"),e(".jstree-node.current-dir").removeClass("current-dir"),t.current_folder=n.closest(".menu-item").find(".menu-item-data-db-id").val(),localStorage.setItem("current_folder",t.current_folder);var r=".wp-admin.upload-php .media-toolbar.wp-filter .media-toolbar-secondary";e(r+" .media-button.delete-selected-button").hasClass("hidden")||e(r+" .media-button.select-mode-toggle-button").trigger("click")}},doRefreshFolder:function(t){if(e.trim(t)){var n={action:"filebird_ajax_refresh_folder",current_folder:t};ntWMC.filebird_begin_loading(),e.post(ajaxurl,n,function(e){}).success(function(n){if(1==n.success){var r=e("#menu-item-"+t);if(e(r).length){var i=e(r).data("number"),l=n.data.rowChanged;i>=l&&e(r).attr("data-number",i-l)}ntWMC.filebird_finish_loading()}else console.log("Error: "+n)})}},doInsertFolder:function(){if(e(".folder-input").length){e(".folder-input").focus();var n=e(".folder-input");return n.putCursorAtEnd().on("focus",function(){n.putCursorAtEnd()}),!1}if(e("#menu-item-"+t.current_folder).length||(t.current_folder=null,localStorage.removeItem("current_folder")),null==t.current_folder)e("#folders-to-edit").append(t.newFolderFormTemplate());else{var r=e("#menu-item-"+t.current_folder).menuItemDepth(),i=r;if((r=parseInt(r)+1)>=t.options.globalMaxDepth+1)alert("The max Depth is: "+t.options.globalMaxDepth);else{var l=e("#menu-item-"+t.current_folder).find(".dh-tree-icon");if(l.hasClass("close")&&l.trigger("click"),0==e('[class="menu-item-data-parent-id"][value="'+t.current_folder+'"]').length)e(t.newFolderFormTemplate("menu-item-depth-"+r)).insertAfter(e("#menu-item-"+t.current_folder));else{var o=e("#menu-item-"+t.current_folder).nextAll();e.each(o,function(n,l){return e(l).menuItemDepth()<=i?(e(t.newFolderFormTemplate("menu-item-depth-"+r)).insertAfter(e(l).prev()),!1):n==o.length-1?(e(t.newFolderFormTemplate("menu-item-depth-"+r)).insertAfter(e(l)),!1):void 0})}}}e(".add-new-folder-name").focus()},newFolderFormTemplate:function(e){return void 0===e&&(e=""),'<li class="new-folder-wrap '+e+'"><input type="text" name="add-new-folder-name" value="" class="folder-input add-new-folder-name" id="" autocomplete="off" /><div class="action"><span class="add-new-folder-cancel button button-default button-small">Cancel</span>&nbsp<span class="add-new-folder-now button button-primary button-small">Ok</span></div></li>'},editFolderFormTemplate:function(e,t,n){return void 0===n&&(n=""),void 0===t&&(t=""),'<li data-id="'+e+'" class="edit-folder-wrap '+n+'"><input type="text" name="edit-folder-name" value="'+t+'" class="folder-input edit-folder-name" id="" autocomplete="off" /><div class="action"><span class="edit-folder-cancel  button button-default button-small" onClick="editFolderCancel()">Cancel</span>&nbsp;<span class="edit-folder-now  button button-primary button-small" onClick="editFolderNow()">Ok</span></div></li>'},newFolderTemplate:function(e,t,n,r){return'<li id="menu-item-'+e+'" data-id= "'+e+'" class="menu-item menu-item-depth-'+r+'"><i class="dh-tree-icon"></i><div class="menu-item-bar jstree-anchor"><div class="menu-item-handle ui-sortable-handle"><span class="item-title"><span class="menu-item-title">'+t+'</span></span></div></div><ul class="menu-item-transport"></ul><input class="menu-item-data-db-id" type="hidden" name="menu-item-db-id['+e+']" value="'+e+'"><input class="menu-item-data-parent-id" type="hidden" name="menu-item-parent-id['+e+']" value="'+n+'"></li>'},jQueryExtensions:function(){e.fn.extend({menuItemDepth:function(){var e=t.isRTL?this.eq(0).css("margin-right"):this.eq(0).css("margin-left");return t.pxToDepth(e&&-1!=e.indexOf("px")?e.slice(0,-2):0)},updateDepthClass:function(t,n){return this.each(function(){var r=e(this);n=n||r.menuItemDepth(),e(this).removeClass("menu-item-depth-"+n).addClass("menu-item-depth-"+t)})},shiftDepthClass:function(t){return this.each(function(){var n=e(this),r=n.menuItemDepth(),i=r+t;n.removeClass("menu-item-depth-"+r).addClass("menu-item-depth-"+i),0===i&&n.find(".is-submenu").hide()})},childMenuItems_bk:function(){var t=e();return this.each(function(){for(var n=e(this),r=n.menuItemDepth(),i=n.next(".menu-item");i.length&&i.menuItemDepth()>r;)t=t.add(i),i=i.next(".menu-item")}),t},childMenuItems:function(){var t=e();return this.each(function(){for(var n=e(this),r=n.menuItemDepth(),i=n.next("li");i.length&&i.menuItemDepth()>r||i.hasClass("new-wrapper");)t=t.add(i),i=i.next("li")}),t},updateParentMenuItemDBId:function(){return this.each(function(){var n=e(this),r=n.find(".menu-item-data-parent-id"),i=parseInt(n.menuItemDepth(),10),l=i-1,o=n.prevAll(".menu-item-depth-"+l).first(),a=0;0!==i&&(a=o.find(".menu-item-data-db-id").val()),r.val(a),a!=t.current_parent&&(e.event.trigger({type:"DhTreeFolder_parent_changed",new_parent:a,id:n.find(".menu-item-data-db-id").val()}),console.log("parent changed"),t.state.push(a),localStorage.setItem("tree_state",t.state)),t.current_parent=null})},putCursorAtEnd:function(){return this.each(function(){var t=e(this),n=this;if(t.is(":focus")||t.focus(),n.setSelectionRange){var r=2*t.val().length;setTimeout(function(){n.setSelectionRange(r,r)},1)}else t.val(t.val());this.scrollTop=999999})}})},initSortables:function(){var n,r,i,l,o,a,d,s,u,m,c=0,f=t.menuList.offset().left,p=e("body"),h=function(){if(!p[0].className)return 0;var e=p[0].className.match(/menu-max-depth-(\d+)/);return e&&e[1]?parseInt(e[1],10):0}();function _(e){var n;l=e.placeholder.prev(".menu-item"),o=e.placeholder.next(".menu-item"),l[0]==e.item[0]&&(l=l.prev(".menu-item")),o[0]==e.item[0]&&(o=o.next(".menu-item")),a=l.length?l.offset().top+l.height():0,d=o.length?o.offset().top+o.height()/3:0,r=o.length?o.menuItemDepth():0,i=l.length?(n=l.menuItemDepth()+1)>t.options.globalMaxDepth?t.options.globalMaxDepth:n:0}function v(e,t){e.placeholder.updateDepthClass(t,c),c=t}0!==e("#folders-to-edit li").length&&e(".drag-instructions").show(),f+=t.isRTL?t.menuList.width():0,t.menuList.sortable({handle:".menu-item-handle",placeholder:"sortable-placeholder",items:t.options.sortableItems,start:function(r,i){var l,o,a,d;t.isRTL&&(i.item[0].style.right="auto"),u=i.item.children(".menu-item-transport"),n=i.item.menuItemDepth(),v(i,n),a=(i.item.next()[0]==i.placeholder[0]?i.item.next():i.item).childMenuItems(),u.append(a),l=u.outerHeight(),l+=l>0?1*i.placeholder.css("margin-top").slice(0,-2):0,l+=i.helper.outerHeight(),s=l,l-=2,i.placeholder.height(l),m=n,a.each(function(){var t=e(this).menuItemDepth();m=t>m?t:m}),o=i.helper.find(".menu-item-handle").outerWidth(),o+=t.depthToPx(m-n),o-=2,i.placeholder.width(o),(d=i.placeholder.next(".menu-item")).css("margin-top",s+"px"),i.placeholder.detach(),e(this).sortable("refresh"),i.item.after(i.placeholder),d.css("margin-top",0),_(i),t.current_parent=i.item.find(".menu-item-data-parent-id").val(),t.old_parent=i.item.prev()},stop:function(r,i){var l,o,a=c-n;e(".children_of_"+i.item.attr("id")).length?e(".children_of_"+i.item.attr("id")).insertAfter(i.item):l=u.children().insertAfter(i.item),e.each(e(".new-wrapper"),function(t,n){e(n).insertAfter("#menu-item-"+e(n).attr("class").match(/children_of_menu-item-(\d)+/)[1])}),o=i.item.find(".item-title .is-submenu"),0<c?o.show():o.hide(),0!==a&&(i.item.updateDepthClass(c),e(".children_of_"+i.item.attr("id")).length?(l=e(".children_of_"+i.item.attr("id")).find(".menu-item")).shiftDepthClass(a):l.shiftDepthClass(a),function(n){var r,i=h;if(0===n)return;if(n>0)(r=m+n)>h&&(i=r);else if(n<0&&m==h)for(;!e(".menu-item-depth-"+i,t.menuList).length&&i>0;)i--;p.removeClass("menu-max-depth-"+h).addClass("menu-max-depth-"+i),h=i}(a)),t.registerChange(),i.item.updateParentMenuItemDBId(),i.item[0].style.top=0,t.isRTL&&(i.item[0].style.left="auto",i.item[0].style.right=0),0==t.old_parent.childMenuItems().length?t.old_parent.find(".dh-tree-icon").removeClass("has_children open"):t.old_parent.find(".dh-tree-icon").addClass("has_children open");var d=e("#menu-item-"+i.item.find(".menu-item-data-parent-id").val());d.childMenuItems().length>0&&d.find(".dh-tree-icon").addClass("has_children open"),d.find(".dh-tree-icon").hasClass("close")&&d.find(".dh-tree-icon").trigger("click"),i.item.move_folder()},change:function(e,n){n.placeholder.parent().hasClass("menu")||(l.length?l.after(n.placeholder):t.menuList.prepend(n.placeholder)),_(n)},sort:function(n,l){var u=l.helper.offset(),m=t.isRTL?u.left+l.helper.width():u.left,p=t.negateIfRTL*t.pxToDepth(m-f);p>i||u.top<a-t.options.targetTolerance?p=i:p<r&&(p=r),p!=c&&v(l,p),d&&u.top+s>d&&(o.after(l.placeholder),_(l),e(this).sortable("refreshPositions"))}})},registerChange:function(){t.menusChanged=!0},depthToPx:function(e){return e*t.options.menuItemDepthPerLevel},pxToDepth:function(e){return Math.floor(e/t.options.menuItemDepthPerLevel)}}}(jQuery);