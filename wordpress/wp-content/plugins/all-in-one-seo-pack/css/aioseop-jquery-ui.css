/**
 * jQ<PERSON>y UI - v1.11.4
 * Targets AIOSEOP instead of whole page to avoid plugin conflicts.
 *
 * @since 3.0.0
 *
 * ©2015 jQuery Foundation and other contributors; Licensed MIT.
 */
#aiosp_tabbed .ui-helper-clearfix:before,
#aiosp_tabbed .ui-helper-clearfix:after {
    content: "";
    display: table;
    border-collapse: collapse
}

#aiosp_tabbed .ui-helper-clearfix:after {
    clear: both
}

#aiosp_tabbed .ui-tabs {
    position: relative;
    padding: .2em
}

#aiosp_tabbed .ui-tabs .ui-tabs-nav {
    margin: 0;
    padding: .2em .2em 0
}

#aiosp_tabbed .ui-tabs .ui-tabs-nav li {
    list-style: none;
    float: left;
    position: relative;
    top: 0;
    margin: 1px .2em 0 0;
    border-bottom-width: 0;
    padding: 0;
    white-space: nowrap
}

#aiosp_tabbed .ui-tabs .ui-tabs-nav .ui-tabs-anchor {
    float: left;
    padding: .5em 1em;
    text-decoration: none
}

#aiosp_tabbed .ui-tabs .ui-tabs-nav li.ui-tabs-active {
    margin-bottom: -1px;
    padding-bottom: 1px
}

#aiosp_tabbed .ui-state-default,
#aiosp_tabbed .ui-widget-content .ui-state-default,
#aiosp_tabbed .ui-widget-header .ui-state-default {
    border: 1px solid #d3d3d3;
    background: #e6e6e6 url("images/ui-bg_glass_75_e6e6e6_1x400.png") 50% 50% repeat-x;
    font-weight: normal;
    color: #555
}

#aiosp_tabbed .ui-state-active,
#aiosp_tabbed .ui-widget-content .ui-state-active,
#aiosp_tabbed .ui-widget-header .ui-state-active {
    border: 1px solid #aaa;
    background: #fff url("images/ui-bg_glass_65_ffffff_1x400.png") 50% 50% repeat-x;
    font-weight: normal;
    color: #212121
}

#aiosp_tabbed .ui-corner-all,
#aiosp_tabbed .ui-corner-top,
#aiosp_tabbed .ui-corner-left,
#aiosp_tabbed .ui-corner-tl {
    border-top-left-radius: 4px
}

#aiosp_tabbed .ui-corner-all,
#aiosp_tabbed .ui-corner-top,
#aiosp_tabbed .ui-corner-right,
#aiosp_tabbed .ui-corner-tr {
    border-top-right-radius: 4px
}

#aiosp_tabbed .ui-corner-all,
#aiosp_tabbed .ui-corner-bottom,
#aiosp_tabbed .ui-corner-left,
#aiosp_tabbed .ui-corner-bl {
    border-bottom-left-radius: 4px
}

#aiosp_tabbed .ui-corner-all,
#aiosp_tabbed .ui-corner-bottom,
#aiosp_tabbed .ui-corner-right,
#aiosp_tabbed .ui-corner-br {
    border-bottom-right-radius: 4px
}

.aioseop-ui-tooltip.ui-tooltip {
    padding: 8px;
    position: absolute;
    z-index: 9999;
    max-width: 300px;
    -webkit-box-shadow: 0 0 5px #aaa;
    box-shadow: 0 0 5px #aaa
}
