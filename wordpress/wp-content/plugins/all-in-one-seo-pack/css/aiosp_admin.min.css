#aioseop_settings_header #message{padding:5px 0 5px 50px;background-image:url(../images/update32.png);background-repeat:no-repeat;background-position:10px;font-size:14px;min-height:32px;clear:none}@media only screen and (-webkit-min-device-pixel-ratio:1.5),only screen and (min--moz-device-pixel-ratio:1.5),only screen and (-o-min-device-pixel-ratio:3/2),only screen and (min-device-pixel-ratio:1.5),only screen and (min-resolution:1.5dppx){#aioseop_settings_header #message{background-image:url(../images/update64.png)!important;-webkit-background-size:32px 32px!important;-moz-background-size:32px 32px!important;background-size:32px 32px!important}}.proupgrade a{font-weight:900;color:#d54e21;font-size:105%}li#wp-admin-bar-aioseop-pro-upgrade a.ab-item{font-weight:900;color:#d54e21!important;font-size:110%}#aio-pro-update{font-weight:900;color:#cc4b1f!important;font-size:110%}.upgrade_menu_link{font-weight:900;color:#d54e21;font-size:105%}label[for=aioseop_edit_profile_header]{font-size:1.3em}#aioseop_edit_profile_header{display:none}.ui-tooltip.ui-widget.ui-corner-all.ui-widget-content.aioseop-ui-tooltip{font-family:'Open Sans',sans-serif;-webkit-box-shadow:0 1px 6px -2px #0073aa;box-shadow:0 1px 6px -2px #0073aa;border:0;border-radius:0;background:#fefefe}.ui-tooltip.ui-widget.ui-corner-all.ui-widget-content.aioseop-ui-tooltip a{color:#0073aa;text-decoration:none}.ui-tooltip.ui-widget.ui-corner-all.ui-widget-content.aioseop-ui-tooltip dt{font-weight:700}