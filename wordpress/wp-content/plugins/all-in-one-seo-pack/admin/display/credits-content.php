<?php
/**
 * Credits Content
 *
 * Displays AIOSEOP's team and additional individuals who have contributed.
 *
 * @link https://wordpress.org/plugins/all-in-one-seo-pack/
 *
 * @package All_in_One_SEO_Pack
 * @since ?
 */

?>
<div class="wrap credits-wrap">

<p class="about-description">
<?php
	/* translators: %s is a placeholder so it should not be translated. It will be replaced with the name of the plugin, All in One SEO Pack. */
	printf( __( '%s is created by a worldwide network of friendly folks like these.', 'all-in-one-seo-pack' ), AIOSEOP_PLUGIN_NAME );
?>
	</p>

<h3 class="wp-people-group"><?php _e( 'Project Leaders', 'all-in-one-seo-pack' ); ?></h3>
<ul class="wp-people-group " id="wp-people-group-project-leaders">
	<li class="wp-person" id="wp-person-micha<PERSON><PERSON><PERSON>">
		<a class="web" href="https://twitter.com/michaeltorbert" target="_blank"><img alt="" class="gravatar" src="https://s.gravatar.com/avatar/f41419cf5cfdbb071a8d591ac9976bf3?s=60">
			<PERSON> Torbert</a>
		<span class="title">
		<?php
		/* translators: This is our CEO and founder Michael Torbert who oversees everything that is related to All in One SEO Pack. Feel free to use a different term if this doesn't translate well. */
		_e( 'Project Lead', 'all-in-one-seo-pack' );
		?>
		</span>
	</li>
	<li class="wp-person" id="wp-person-stevemortiboy">
		<a class="web" target="_blank" href="https://twitter.com/wpsmort"><img alt="" class="gravatar" src="https://www.gravatar.com/avatar/40e33d813c16a63500675d851b0cbf3a?s=60">
			Steve Mortiboy</a>
		<span class="title"><?php _e( 'Project Manager', 'all-in-one-seo-pack' ); ?></span>
	</li>
	<li class="wp-person" id="wp-person-yuqianliu">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/yuqianl"><img alt="" class="gravatar" src="https://www.gravatar.com/avatar/8f971bea2b6c483fd1099e558013a7d0?s=60">
			Yuqian Liu</a>
		<span class="title"><?php _e( 'Project Manager', 'all-in-one-seo-pack' ); ?></span>
	</li>
</ul>

<h3 class="wp-people-group"><?php printf( __( 'Core Team', 'all-in-one-seo-pack' ) ); ?></h3>
<ul class="wp-people-group " id="wp-people-group-contributors">
	<li class="wp-person" id="wp-person-arnaudbroes">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/arnaudbroes"><img alt="" class="gravatar" src="https://www.gravatar.com/avatar/0ce0d554c2b0bd61d326e15c8dcde756?s=60">
			Arnaud Broes</a>
		<span class="title"><?php _e( 'Team Lead', 'all-in-one-seo-pack' ); ?></span>
	</li>

	<li class="wp-person" id="rozroz">
		<a class="web" target="_blank" href="https://github.com/contactashish13"><img alt="" class="gravatar" src="https://avatars2.githubusercontent.com/u/12953439?s=60">
			Ashish Ravi</a>
		<span class="title"><?php _e( 'Development Team', 'all-in-one-seo-pack' ); ?></span>
	</li>
	<li class="wp-person" id="EkoJR">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/EkoJR/"><img alt="" class="gravatar" src="https://secure.gravatar.com/avatar/bb4c78fe944b58bd5f127d836500c30a?s=200&d=mm&r=g">
			Ben Reames</a>
		<span class="title"><?php _e( 'Development Team', 'all-in-one-seo-pack' ); ?></span>
	</li>

</ul>

<h3 class="wp-people-group">&#x1f31f;
<?php
/* translators: These are people who made an awesome contribution to All in One SEO Pack. Feel free to replace "Rockstar" with whatever works best in your language. */
_e( 'Recent Rockstar Contributors', 'all-in-one-seo-pack' );
?>
&#x1f31f;</h3>
	<ul class="wp-people-group " id="wp-people-group-rockstars">
		<li>
		<?php
		/* translators: %1$s and %2$s are used as placeholders and should not be transalted. These are used to transform the text in between them into a clickable link. */
			printf(
				__( 'Want to see your name and picture here as a community developer? %1$sClick here%2$s to open an issue on GitHub to report a bug, request a feature or find an issue and submit code!', 'all-in-one-seo-pack' ),
				'<a href="https://github.com/semperfiwebdesign/all-in-one-seo-pack" target="_blank">',
				'</a>'
			);
			?>
		</li>
	<li class="wp-person" id="wp-person-mayukojpn">
		<a class="web" target="_blank" href="https://github.com/rebeccahum"><img alt="" class="gravatar" src="https://2.gravatar.com/avatar/b57b5efcabe3e01833849390ad7d3129?s=60">
			Rebecca Hum</a>
	</li>
	<li class="wp-person" id="wp-person-mayukojpn">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/mayukojpn/"><img alt="" class="gravatar" src="https://secure.gravatar.com/avatar/79294868a241e80ea4fda34c618b8a11?s=60">
			Mayo Moriyama</a>
	</li>
	<li class="wp-person" id="wp-person-dougalcampbell">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/dougal/"><img alt="" class="gravatar" src="https://www.gravatar.com/avatar/81717a172b6918071fbea1a52483294b?s=60">
			Dougal Campbell</a>
	</li>
	<li class="wp-person" id="wp-person-alejandromostajo">
		<a class="web" target="_blank" href="https://github.com/amostajo"><img alt="" class="gravatar" src="https://avatars1.githubusercontent.com/u/1645908?s=60">
			Alejandro Mostajo</a>
	</li>
	<li class="wp-person" id="wp-person-aaronbrodney">
		<a class="web" target="_blank" href="https://github.com/theycalledmetaz"><img alt="" class="gravatar" src="https://avatars3.githubusercontent.com/u/8225725?v=3&s=60">
			Aaron Brodney</a>
	</li>
	<li class="wp-person" id="rozroz">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/yummy-wp/"><img alt="" class="gravatar" src="https://avatars0.githubusercontent.com/u/22232968?v=3&s=460">
			Stanislav Samoilenko</a>
	</li>
	<li class="wp-person" id="shoheitanaka">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/shoheitanaka"><img alt="" class="gravatar" src="https://secure.gravatar.com/avatar/677e512c803c40c0180d4514f876a21f?s=200&d=mm&r=g">
			Shohei Tanaka</a>
	</li>
	<li class="wp-person" id="webaware">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/webaware/"><img alt="" class="gravatar" src="https://secure.gravatar.com/avatar/aee800bc3644d9ebfa33c1ed9df5d958?s=200&d=mm&r=g">
			Ross McKay</a>
	</li>
	<li class="wp-person" id="webaware">
		<a class="web" target="_blank" href="https://github.com/jWright-Freelance/"><img alt="" class="gravatar" src="https://avatars1.githubusercontent.com/u/11382307?s=200&v=4">
			John Wright</a>
	</li>
	<li class="wp-person" id="webaware">
		<a class="web" target="_blank" href="https://wordpress.org/support/users/onetarek/"><img alt="" class="gravatar" src="https://secure.gravatar.com/avatar/dc4d0f0561009cc18c8d1cdfc760b2f1?s=200&d=retro&r=g">
			Md Jahidul Islam</a>
	</li>
	<li class="wp-person" id="adamsilverstein">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/adamsilverstein/"><img alt=""class="gravatar" src="https://secure.gravatar.com/avatar/fddbd6c3e1c3d971aa732b9346aeb433?s=200&d=mm&r=g">
			Adam Silverstein</a>
	</li>
	<li class="wp-person" id="vschettino">
		<a class="web" target="_blank" href="https://github.com/vschettino/"><img alt="" class="gravatar" src="https://avatars2.githubusercontent.com/u/7289698?s=460&v=4">
			Vinicius Schettino</a>
	</li>
	<li class="wp-person" id="vschettino">
		<a class="web" target="_blank" href="https://github.com/srdjan-jcc"><img alt="" class="gravatar" src="https://avatars2.githubusercontent.com/u/3109112?s=460&v=4">
			Srdjan Jocic</a>
	</li>
	<li class="wp-person" id="vschettino">
		<a class="web" target="_blank" href="https://profiles.wordpress.org/soulseekah/"><img alt="" class="gravatar" src="https://avatars0.githubusercontent.com/u/685880?s=460&v=4">
			Gennady Kovshenin</a>
	</li>
</ul>

<h3 class="wp-people-group dashicons-before dashicons-translation">
<?php
/* translators: In this context, "translation contributors" are translators who submit strings on translate.wordpress.org and "translation editors" are those who proofread and approve them (also known as PTEs - Project Translation Editors).*/
printf( _e( 'Translation contributors and translation editors', 'all-in-one-seo-pack' ), '1.2' );
?>
</h3>
<p class="wp-credits-list">
	<a href="https://profiles.wordpress.org/pierrelannoy/" target="_blank">Pierre Lannoy</a>,
	<a href="https://profiles.wordpress.org/sonjanyc/" target="_blank">Sonja Leix</a>,
	<a href="https://profiles.wordpress.org/dev-ide/" target="_blank">Adil El hallaoui</a>,
	<a href="https://profiles.wordpress.org/simonie/" target="_blank">simonie</a>,
	<a href="https://profiles.wordpress.org/lenasterg/" target="_blank">lenasterg</a>,
	<a href="https://profiles.wordpress.org/arnaudbroes/" target="_blank">Arnaud Broes</a>,
	<a href="https://profiles.wordpress.org/pixolin/" target="_blank">Bego Mario Garde</a>,
	<a href="https://profiles.wordpress.org/wp-yogi/" target="_blank">wp-yogi</a>,
	<a href="https://profiles.wordpress.org/wpsmort/" target="_blank">Steve Mortiboy</a>,
	<a href="https://profiles.wordpress.org/webaware/" target="_blank">webaware</a>,
	<a href="https://profiles.wordpress.org/escribirelmundo/" target="_blank">escribirelmundo</a>,
	<a href="https://profiles.wordpress.org/casiepa/" target="_blank">Pascal Casier</a>,
	<a href="https://profiles.wordpress.org/shoheitanaka/" target="_blank">Shohei Tanaka</a>,
	<a href="https://profiles.wordpress.org/nurron/" target="_blank">Nurron Shodiqin</a>,
	<a href="https://profiles.wordpress.org/aprmndr/" target="_blank">Alyssa Primandaru</a>,
	<a href="https://profiles.wordpress.org/facestoro/" target="_blank">facestoro</a>,
	<a href="https://profiles.wordpress.org/yuqianl/" target="_blank">Dawa Torbert</a>,
	<a href="https://profiles.wordpress.org/hallsofmontezuma/" target="_blank">Michael Torbert</a>,
	<a href="https://profiles.wordpress.org/istvanzseller/" target="_blank">Istvan Zseller</a>,
	<a href="https://profiles.wordpress.org/paaljoachim" target="_blank">Paal Joachim Romdahl</a>,
	<a href="https://profiles.wordpress.org/almaz/" target="_blank">Almaz Mannanov</a>,
	<a href="https://profiles.wordpress.org/vide13 /" target="_blank">vide13</a>,
	<a href="https://profiles.wordpress.org/yuraz/" target="_blank">Jurica Zuanovic</a>,
	<a href="https://profiles.wordpress.org/arhipaiva/" target="_blank">arhipaiva</a>,
	<a href="https://profiles.wordpress.org/maximanikin/" target="_blank">Maxim Anikin</a>,
	<a href="https://profiles.wordpress.org/petya/" target="_blank">Petya Raykovska</a>,
	<a href="https://profiles.wordpress.org/hathanh0809/" target="_blank">hathanh0809</a>,
	<a href="https://profiles.wordpress.org/cedric3131/" target="_blank">Cédric Valmary</a>,
	<a href="https://profiles.wordpress.org/smitka/" target="_blank">Vladimir Smitka</a>,
	<a href="https://profiles.wordpress.org/brewtal/" target="_blank">Paul P.</a>,
	<a href="https://profiles.wordpress.org/wpaleks/" target="_blank">Aleksander Savkovic</a>,
	<a href="https://profiles.wordpress.org/diogosanches/" target="_blank">Diogo Sanches</a>,
	<a href="https://profiles.wordpress.org/klemenfajs/" target="_blank">Klemen Fajs</a>,
	<a href="https://profiles.wordpress.org/adriancastellanos/" target="_blank">Adrian Castellanos</a>,
	<a href="https://profiles.wordpress.org/exilhamburger/" target="_blank">exilhamburger</a>,
	<a href="https://profiles.wordpress.org/garyj/" target="_blank">Gary Jones</a>,
	<a href="https://profiles.wordpress.org/fernandot/" target="_blank">Fernando Tellado</a>,
	<a href="https://profiles.wordpress.org/hiwhatsup/" target="_blank">Carlos Zuniga</a>,
	<a href="https://profiles.wordpress.org/fxbenard/" target="_blank">François Bernard</a>,
	<a href="https://profiles.wordpress.org/jack0falltrades/" target="_blank">jack0falltrades</a>,
	<a href="https://profiles.wordpress.org/dancaragea/" target="_blank">Dan Caragea</a>,
	<a href="https://profiles.wordpress.org/kyla81975/" target="_blank">kyla81975</a>,
	<a href="https://profiles.wordpress.org/arildknudsen1/" target="_blank">Arild Knudsen</a>.
</p>

</div>
