!function(i){var a=aioseop_notice_data.notice_actions;i.each(a,function(n,a){var o,t;i.each(a,function(a,e){var o,t,c;o=n,t=e,c=aioseop_notice_data.notice_nonce,i("#aioseop-notice-delay-"+o+"-"+t).on("click",function(a){var e=i(this).attr("href");"#"!==e&&""!==e||(a.stopPropagation(),a.preventDefault());var n=new FormData;n.append("notice_slug",o),n.append("action_index",t),n.append("action","aioseop_notice"),n.append("_ajax_nonce",c),i.ajax({url:ajaxurl,type:"POST",data:n,cache:!1,dataType:"json",processData:!1,contentType:!1,success:function(a,e,n){i(".aioseop-notice-"+o).remove()}})})}),o=n,t=aioseop_notice_data.notice_nonce,i(".aioseop-notice-"+o).on("click","button.notice-dismiss ",function(a){a.stopPropagation(),a.preventDefault();var e=new FormData;e.append("notice_slug",o),e.append("action_index","default"),e.append("action","aioseop_notice"),e.append("_ajax_nonce",t),i.ajax({url:ajaxurl,type:"POST",data:e,cache:!1,dataType:"json",processData:!1,contentType:!1})})})}(jQuery);