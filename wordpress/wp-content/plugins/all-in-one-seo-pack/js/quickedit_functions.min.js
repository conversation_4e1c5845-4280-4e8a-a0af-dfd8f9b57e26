function aioseop_ajax_edit_meta_form(e,i,t){var a,o=jQuery("#aioseop_"+i+"_"+e),s=jQuery("#aioseop_label_"+i+"_"+e).text(),_=o.html();a='<textarea id="aioseop_new_'+i+"_"+e+'" style="font-size:13px;width:100%;float:left;position:relative;z-index:1;" rows=4 cols=32>'+s+"</textarea>",a+='<label style="float:left">',a+='<a class="aioseop-icon-qedit aioseop-icon-qedit-accept" href="javascript:void(0);" id="aioseop_'+i+"_save_"+e+'" title="Accept" >',a+='<a class="aioseop-icon-qedit aioseop-icon-qedit-delete" href="javascript:void(0);" id="aioseop_'+i+"_cancel_"+e+'" title="Decline" >',a+="</label>",o.html(a),o.attr("class","aioseop_mpc_admin_meta_options aio_editing"),jQuery("#aioseop_"+i+"_cancel_"+e).click(function(){o.html(_),o.attr("class","aioseop_mpc_admin_meta_options")}),jQuery("#aioseop_"+i+"_save_"+e).click(function(){var a=jQuery("#aioseop_new_"+i+"_"+e).val();handle_post_meta(e,a,i,t)})}function handle_post_meta(e,i,t,o){jQuery("div#aioseop_"+t+"_"+e).fadeOut("fast",function(){var a='<label class="aioseop_'+t+'_loading">';a+='<img style="width:20px;margin-right:5px;float:left" align="absmiddle" ',a+='src="'+aioseopadmin.imgUrl+'activity.gif" border="0" alt="" title="'+t+'" /></a>',a+='</label><div style="float:left">Please wait…</div>',jQuery("div#aioseop_"+t+"_"+e).fadeIn("fast",function(){var a=new sack(aioseopadmin.requestUrl);a.execute=1,a.method="POST",a.setVar("action","aioseop_ajax_save_meta"),a.setVar("post_id",e),a.setVar("new_meta",i),a.setVar("target_meta",t),a.setVar("_inline_edit",jQuery("input#_inline_edit").val()),a.setVar("_nonce",o),a.onError=function(){alert("Ajax error on saving title")},a.runAJAX()}),jQuery("div#aioseop_"+t+"_"+e).html(a),jQuery("div#aioseop_"+t+"_"+e).attr("class","aioseop_mpc_admin_meta_options")})}jQuery(document).on("click",".visibility-notice",function(){jQuery.ajax({url:ajaxurl,data:{action:"aioseo_dismiss_visibility_notice"}})}),jQuery(document).on("click",".yst_notice",function(){jQuery.ajax({url:ajaxurl,data:{action:"aioseo_dismiss_yst_notice"}})}),jQuery(document).on("click",".woo-upgrade-notice",function(){jQuery.ajax({url:ajaxurl,data:{action:"aioseo_dismiss_woo_upgrade_notice"}})}),jQuery(document).on("click",".sitemap_max_urls_notice",function(){jQuery.ajax({url:ajaxurl,data:{action:"aioseo_dismiss_sitemap_max_url_notice"}})});