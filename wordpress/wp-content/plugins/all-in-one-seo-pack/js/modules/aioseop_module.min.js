function toggleVisibility(e){var a=document.getElementById(e);"block"===a.style.display?a.style.display="none":a.style.display="block"}function aioseop_get_field_value(e){if(0===e.length)return e;var a=jQuery("[name="+e+"]");if(0===a.length)return e;var o=a.attr("type");return"checkbox"!==o&&"radio"!==o||(a=jQuery("input[name="+e+"]:checked")),a.val()}function aioseop_get_field_values(e){var a=[],o=jQuery("[name="+e+"]");if(0===o.length)return e;var t=o.attr("type");return"checkbox"!==t&&"radio"!==t||jQuery("input[name="+e+"]:checked").each(function(){a.push(jQuery(this).val())}),a.length<=0&&a.push(o.val()),a}function aioseop_eval_condshow_logic(e){var a,o;if("object"==typeof e)switch(a=e.lhs,o=e.rhs,null!==a&&"object"==typeof a&&(a=aioseop_eval_condshow_logic(a)),null!==o&&"object"==typeof o&&(o=aioseop_eval_condshow_logic(o)),a=aioseop_get_field_value(a),o=aioseop_get_field_value(o),e.op){case"NOT":return!a;case"AND":return a&&o;case"OR":return a||o;case"==":return a===o;case"!=":return a!==o;default:return null}return e}function aioseop_do_condshow_match(e,a){if(void 0===a)return!1;var t=!0;return jQuery.each(a,function(e,a){if("object"==typeof a)aioseop_eval_condshow_logic(a)||(t=!1);else{var o=[];e.match(/\\\[\\\]/)?(o=aioseop_get_field_values(e),jQuery.inArray(a,o,0)<0&&(t=!1)):(o=aioseop_get_field_value(e))!=a&&(t=!1)}}),t?jQuery("#"+e+"_wrapper").show():jQuery("#"+e+"_wrapper").hide(),t}function aioseop_add_condshow_handlers(o,t){void 0!==t&&jQuery.each(t,function(e,a){jQuery("[name="+e+"]").bind("change keyup",function(){aioseop_do_condshow_match(o,t)})})}function aioseop_do_condshow(e){void 0!==aiosp_data.condshow&&jQuery.each(aiosp_data.condshow,function(e,a){aioseop_do_condshow_match(e,a),aioseop_add_condshow_handlers(e,a)})}function aiosp_store_radio(){var e={};jQuery('input[type="radio"]').each(function(){jQuery(this).is(":checked")&&(e[jQuery(this).attr("name")]=jQuery(this).val()),jQuery(document).data("radioshack",e)})}function aiosp_reclick_radio(){var e=jQuery(document).data("radioshack");for(var a in e)jQuery('input[name="'+a+'"]').filter('[value="'+e[a]+'"]').trigger("click");jQuery(".wrap").unbind("mouseup")}function aioseop_handle_ajax_call(e,a,o,t){var i=new sack(ajaxurl);i.execute=1,i.method="POST",i.setVar("action",e),i.setVar("settings",a),i.setVar("options",o),void 0!==t&&(i.onCompletion=t),i.setVar("nonce-aioseop",jQuery('input[name="nonce-aioseop"]').val()),i.setVar("nonce-aioseop-edit",jQuery('input[name="nonce-aioseop-edit"]').val()),i.onError=function(){alert("Ajax error on saving.")},i.runAJAX()}function aioseop_handle_post_url(a,o,t,i,s){jQuery("div#aiosp_"+o).fadeOut("fast",function(){var e='<label class="aioseop_loading aioseop_'+o+'_loading"></label> Please wait...';jQuery("div#aiosp_"+o).fadeIn("fast",function(){s?jQuery.ajax({url:ajaxurl,method:"POST",dataType:"json",data:{action:a,options:t,settings:o,"nonce-aioseop":jQuery('input[name="nonce-aioseop"]').val(),"nonce-aioseop-edit":jQuery('input[name="nonce-aioseop-edit"]').val()},success:function(e){i&&i(e)}}):aioseop_handle_ajax_call(a,o,t,i)}),jQuery("div#aiosp_"+o).html(e)})}function aioseop_is_overflowed(e){return e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth}function aioseop_overflow_border(e){aioseop_is_overflowed(e)?e.className="aioseop_option_div aioseop_overflowed":e.className="aioseop_option_div"}function aiospinitAll(){aiospinitSocialMetaInPosts(jQuery),aiospinitCalendar()}function aiospinitCalendar(){0<jQuery(".aiseop-date").length&&"text"===jQuery(".aiseop-date").eq(0).prop("type").toLowerCase()&&jQuery(".aiseop-date").datepicker({dateFormat:"yy-mm-dd"})}function aiospinitSocialMetaInPosts(a){a('input[name="aioseop_opengraph_settings_customimg_checker"] ~ .aioseop_upload_image_button').on("click",function(e){a('input[name="aioseop_opengraph_settings_image"]').attr("checked",!1)})}"undefined"!=typeof aiosp_data&&(jQuery.each(aiosp_data,function(e,a){0===e?void 0===a.condshow&&(aiosp_data[e].condshow=[]):void 0!==a.condshow&&(aiosp_data[0].condshow=jQuery.merge(aiosp_data[0].condshow,a.condshow))}),aiosp_data=aiosp_data[0]),jQuery(document).ready(function(){"undefined"!=typeof aiosp_data&&void 0!==aiosp_data.condshow&&aioseop_do_condshow(aiosp_data.condshow),jQuery(".aioseop_upload_image_label").on("change",function(){this.checker=jQuery(this).parent().find(".aioseop_upload_image_checker"),0<this.checker.length&&this.checker.val(1)}),jQuery(document).ready(function(e){jQuery(".aioseop_upload_image_button").each(function(){jQuery(this).aioseopImageUploader({success:function(e,a){0<jQuery(a).prev().length&&jQuery(a).prev().val(1)}})})}),jQuery(document).ready(function(){jQuery("#poststuff .aioseop_radio_type input[type='radio']").on("click",function(){var e=jQuery(this).attr("previousValue"),a=jQuery(this).attr("name");void 0!==e?"checked"===e?(jQuery(this).prop("checked",!1),jQuery(this).attr("previousValue",!1)):(jQuery("input[name="+a+"]:radio").attr("previousValue",!1),jQuery(this).attr("previousValue","checked")):jQuery(this).prop("checked")?(jQuery(this).prop("checked",!0),jQuery(this).attr("previousValue","checked")):(jQuery(this).prop("checked",!1),jQuery(this).attr("previousValue",!1))})}),void 0!==aiosp_data.pointers&&jQuery.each(aiosp_data.pointers,function(e,a){"undefined"!==a&&""!==a.pointer_text&&aioseop_show_pointer(e,a)}),jQuery(".all-in-one-seo_page_all-in-one-seo-pack-modules-aioseop_feature_manager #aiosp_settings_form .aioseop_settings_left").delegate("input[name='Submit']","click",function(e){return e.preventDefault(),!1}),jQuery(".all-in-one-seo_page_all-in-one-seo-pack-modules-aioseop_feature_manager #aiosp_settings_form").delegate("input[name='Submit']","click",function(e){return e.preventDefault(),aioseop_handle_post_url("aioseop_ajax_save_settings","ajax_settings_message",jQuery("form#aiosp_settings_form").serialize(),function(){jQuery(".wp-has-current-submenu").fadeIn("fast",function(){aioseop_handle_ajax_call("aioseop_ajax_get_menu_links","ajax_settings_message",jQuery.param({target:".wp-has-current-submenu > ul"}))})}),!1}),jQuery(".all-in-one-seo_page_all-in-one-seo-pack-pro-modules-aioseop_feature_manager #aiosp_settings_form .aioseop_settings_left").delegate("input[name='Submit']","click",function(e){return e.preventDefault(),!1}),jQuery(".all-in-one-seo_page_all-in-one-seo-pack-pro-modules-aioseop_feature_manager #aiosp_settings_form").delegate("input[name='Submit']","click",function(e){return e.preventDefault(),aioseop_handle_post_url("aioseop_ajax_save_settings","ajax_settings_message",jQuery("form#aiosp_settings_form").serialize(),function(){jQuery(".wp-has-current-submenu").fadeIn("fast",function(){aioseop_handle_ajax_call("aioseop_ajax_get_menu_links","ajax_settings_message",jQuery.param({target:".wp-has-current-submenu > ul"}))})}),!1});jQuery("div#aiosp_sitemap_addl_pages_metabox").delegate("input[name='Submit']","click",function(){return aioseop_handle_post_url("aioseop_ajax_save_url","sitemap_addl_pages",jQuery("div#aiosp_sitemap_addl_pages_metabox input, div#aiosp_sitemap_addl_pages_metabox select").serialize()),!1}),jQuery("div#aiosp_video_sitemap_addl_pages_metabox").delegate("input[name='Submit']","click",function(){return aioseop_handle_post_url("aioseop_ajax_save_url","video_sitemap_addl_pages",jQuery("div#aiosp_video_sitemap_addl_pages_metabox input, div#aiosp_video_sitemap_addl_pages_metabox select").serialize()),!1}),jQuery("div#aiosp_sitemap_addl_pages_metabox").delegate("a.aiosp_delete_url","click",function(e){return e.preventDefault(),aioseop_handle_post_url("aioseop_ajax_delete_url","sitemap_addl_pages",jQuery(this).attr("title")),!1}),jQuery("div#aiosp_video_sitemap_addl_pages_metabox").delegate("a.aiosp_delete_url","click",function(e){return e.preventDefault(),aioseop_handle_post_url("aioseop_ajax_delete_url","video_sitemap_addl_pages",jQuery(this).attr("title")),!1}),jQuery("div#aiosp_opengraph_scan_header").delegate("input[name='aiosp_opengraph_scan_header']","click",function(e){return e.preventDefault(),aioseop_handle_post_url("aioseop_ajax_scan_header","opengraph_scan_header",jQuery("div#aiosp_opengraph_scan_header").serialize()),!1}),jQuery('input[name="aiosp_sitemap_posttypes[]"][value="all"], input[name="aiosp_video_sitemap_posttypes[]"][value="all"], input[name="aiosp_sitemap_taxonomies[]"][value="all"], input[name="aiosp_video_sitemap_taxonomies[]"][value="all"]').click(function(){jQuery(this).parents("div:eq(0)").find(":checkbox").prop("checked",this.checked)}),jQuery('input[name="aiosp_sitemap_posttypes[]"][value!="all"], input[name="aiosp_video_sitemap_posttypes[]"][value!="all"], input[name="aiosp_sitemap_taxonomies[]"][value!="all"], input[name="aiosp_video_sitemap_taxonomies[]"][value!="all"]').click(function(){this.checked||jQuery(this).parents("div:eq(0)").find('input[value="all"]:checkbox').prop("checked",this.checked)}),jQuery(".aioseop_tabs").tabs({hide:!0,show:!0}),jQuery("div#aiosp_robots_default_metabox").delegate("a.aiosp_robots_delete_rule","click",function(e){return e.preventDefault(),aioseop_handle_post_url("aioseop_ajax_delete_rule","robots_rules",jQuery(this).attr("data-id"),function(){window.location.reload()}),!1}),jQuery("div#aiosp_robots_default_metabox").delegate("a.aiosp_robots_edit_rule","click",function(e){return e.preventDefault(),jQuery('input[name="aiosp_robots_agent"]').val(jQuery(this).attr("data-agent")),jQuery('select[name="aiosp_robots_type"]').val(jQuery(this).attr("data-type")),jQuery('input[name="aiosp_robots_path"]').val(jQuery(this).attr("data-path")),jQuery("input.add-edit-rule").val(jQuery(".aioseop_table").attr("data-edit-label")),jQuery("input.edit-rule-id").val(jQuery(this).attr("data-id")),!1}),jQuery("a.aiosp_robots_physical").on("click",function(e){return e.preventDefault(),aioseop_handle_post_url("aioseop_ajax_robots_physical","robots_physical_import_delete",jQuery(this).attr("data-action"),function(e){e.data&&e.data.message&&alert(e.data.message),window.location.reload()},!0),!1}),aiospinitAll()}),jQuery.fn.aioseopImageUploader=function(e){var a=this;a.options=jQuery.extend({success:void 0},e),a.target=jQuery(a).next(),a.uploader=wp.media({title:"Choose Image",button:{text:"Choose Image"},multiple:!1}),a.onSelect=function(){var e=a.uploader.state().get("selection").first().toJSON().url;0<=a.target.length&&jQuery(a.target).val(e),void 0!==a.options.success&&a.options.success(e,a)},a.onClick=function(e){e.preventDefault(),a.uploader.open()},a.uploader.on("select",a.onSelect),jQuery(a).click(a.onClick)};