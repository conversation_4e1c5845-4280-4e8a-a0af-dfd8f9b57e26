var aiosp_title_extra=parseInt(aioseop_count_chars.aiosp_title_extra,10);function aioseopInitCounting(){jQuery(".aioseop_count_chars").on("keyup keydown",function(){aioseopCountChars(jQuery(this).eq(0),jQuery(this).parent().find('[name="'+jQuery(this).attr("data-length-field")+'"]').eq(0))}),jQuery(".aioseop_count_chars").each(function(){aioseopCountChars(jQuery(this).eq(0),jQuery(this).parent().find('[name="'+jQuery(this).attr("data-length-field")+'"]').eq(0))})}function aioseopCountChars(e,t){var a,o=0;"aiosp_title"===e.attr("name")&&void 0!==aiosp_title_extra&&(o=aiosp_title_extra),t.val(e.val().length+o),a=void 0!==e.attr("size")?e.attr("size"):e.attr("rows")*e.attr("cols"),(a=parseInt(a,10))<10||(t.val()>a?t.removeClass().addClass("aioseop_count_ugly"):"aiosp_title"===e.attr("name")||"aiosp_home_title"===e.attr("name")?t.val()>a-6?t.removeClass().addClass("aioseop_count_bad"):t.removeClass().addClass("aioseop_count_good"):t.val()>a-10?t.removeClass().addClass("aioseop_count_bad"):t.removeClass().addClass("aioseop_count_good"))}jQuery(document).ready(function(){aioseopInitCounting()}),jQuery(document).ready(function(){jQuery("#aiosp_title_wrapper").bind("input",function(){jQuery("#aiosp_snippet_title").text(jQuery("#aiosp_title_wrapper input").val().replace(/<(?:.|\n)*?>/gm,""))}),jQuery("#aiosp_description_wrapper").bind("input",function(){jQuery("#aioseop_snippet_description").text(jQuery("#aiosp_description_wrapper textarea").val().replace(/<(?:.|\n)*?>/gm,""))})});