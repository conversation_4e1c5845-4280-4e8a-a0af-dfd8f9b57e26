<?php
/**
 * Compatability with <PERSON><PERSON><PERSON> (WordPress Multilingual)
 *
 * @package All_in_One_SEO_Pack
 * @since ?
 */

if ( ! class_exists( 'All_in_One_SEO_Pack_Wpml' ) ) {
	/**
	 * Compatibility with WPML - WordPress Multilingual Plugin
	 *
	 * @link https://wpml.org/
	 * @package All-in-One-SEO-Pack
	 * <AUTHOR>
	 * @copyright Semperfi Web Design <https://semperplugins.com/>
	 * @version 2.3.13
	 */
	class All_in_One_SEO_Pack_Wpml extends All_in_One_SEO_Pack_Compatible {
		/**
		 * Returns flag indicating if <PERSON><PERSON><PERSON> is present.
		 *
		 * @since 2.3.12.3
		 *
		 * @return bool
		 */
		public function exists() {
			return function_exists( 'icl_object_id' );
		}

		/**
		 * Declares compatibility hooks.
		 *
		 * @since 2.3.12.3
		 */
		public function hooks() {
			add_filter( 'aioseop_home_url', array( &$this, 'aioseop_home_url' ) );
			add_filter( 'aioseop_sitemap_xsl_url', array( &$this, 'aioseop_sitemap_xsl_url' ) );
			add_action( 'init', 'aioseop_get_options' ); // #1761 Options are otherwise called too early to work with W<PERSON>L.
		}

		/**
		 * Returns specified url filtered by wpml.
		 * This is needed to obtain the correct domain in which WordPress is running on.
		 * AIOSEOP would have ran first expecting the return of home_url().
		 *
		 * @since 2.3.12.3
		 *
		 * @param string $path Relative path or url.
		 *
		 * @param string filtered url.
		 */
		public function aioseop_home_url( $path ) {
			$url = apply_filters( 'wpml_home_url', home_url( '/' ) );
			// Remove query string.
			preg_match_all( '/\?[\s\S]+/', $url, $matches );
			// Get base.
			$url  = preg_replace( '/\?[\s\S]+/', '', $url );
			$url  = trailingslashit( $url );
			$url .= preg_replace( '/\//', '', $path, 1 );
			// Add query string.
			if ( count( $matches ) > 0 && count( $matches[0] ) > 0 ) {
				$url .= $matches[0][0];
			}
			return $url;
		}
		/**
		 * Returns XSL url without query string.
		 *
		 * @since 2.3.12.3
		 *
		 * @param string $url XSL url.
		 *
		 * @param string filtered url.
		 */
		public function aioseop_sitemap_xsl_url( $url ) {
			return preg_replace( '/\?[\s\S]+/', '', $url );
		}
	}
}
