.wp-admin.jetpack_page_akismet-key-config, .wp-admin.settings_page_akismet-key-config {
	background-color:#f3f6f8;
}

#submitted-on {
    position: relative;
}
#the-comment-list .author .akismet-user-comment-count {
    display: inline;
}
#the-comment-list .author a span {
    text-decoration: none;
    color: #999;
}
#the-comment-list .author a span.akismet-span-link {
	text-decoration: inherit;
	color: inherit;
}
#the-comment-list .akismet_remove_url {
    margin-left: 3px;
    color: #999;
    padding: 2px 3px 2px 0;
}
#the-comment-list .akismet_remove_url:hover {
    color: #A7301F;
    font-weight: bold;
    padding: 2px 2px 2px 0;
}
#dashboard_recent_comments .akismet-status {
    display: none;
}
.akismet-status {
    float: right;
}
.akismet-status a {
    color: #AAA;
    font-style: italic;
}
table.comments td.comment p a {
    text-decoration: underline;
}
table.comments td.comment p a:after {
    content: attr(href);
    color: #aaa;
    display: inline-block; /* Show the URL without the link's underline extending under it. */
    padding: 0 1ex; /* Because it's inline block, we can't just use spaces in the content: attribute to separate it from the link text. */
}
.mshot-arrow {
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid #5C5C5C;
    position: absolute;
    left: -6px;
    top: 91px;
}
.mshot-container {
    background: #5C5C5C;
    position: absolute;
    top: -94px;
    padding: 7px;
    width: 450px;
    height: 338px;
    z-index: 20000;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-border-radius: 6px;
}
.akismet-mshot {
    position: absolute;
    z-index: 100;
}
.akismet-mshot .mshot-image {
    margin: 0;
    height: 338px;
    width: 450px;
}
.checkforspam {
    display: inline-block !important;
}
.checkforspam-progress {
	padding-left: 1ex;
	display: none;
}
.checkforspam.button-disabled .checkforspam-progress {
	display: inline;
}

.checkforspam-spinner {
    display: inline-block;
    margin-top: 7px;
}

.akismet-right {
	float: right;
}

.akismet-card .akismet-right {
	margin: 1em 0;
}

.akismet-alert-text {
	color: #dd3d36;
	font-weight: bold;
	font-size: 120%;
	margin-top: .5rem;
}
.akismet-alert {
	border: 1px solid #e5e5e5;
	padding: 0.4em 1em 1.4em 1em;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    border-width: 1px;
    border-style: solid;
}

.akismet-alert h3.akismet-key-status {
	color: #fff;
	margin: 1em 0 0.5em 0;
}

.akismet-alert.akismet-critical {
	background-color: #993300;
}

.akismet-alert.akismet-active {
	background-color: #649316;
}

.akismet-alert p.akismet-key-status {
	font-size: 24px;
}

.akismet-alert p.akismet-description {
	color:#fff;
	font-size: 14px;
    margin: 0 0;
	font-style: normal;
}

.akismet-alert p.akismet-description a,
.akismet-alert p.akismet-description a,
.akismet-alert p.akismet-description a,
.akismet-alert p.akismet-description a {
	color: #fff;
}

.akismet-new-snapshot {
	margin-top: 1em;
	padding: 1em;
	text-align: center;
	background: #fff;
}

.akismet-new-snapshot h3 {
    background: #f5f5f5;
	color: #888;
	font-size: 11px;
    margin: 0;
    padding: 3px;
}

.new-snapspot ul {
	font-size: 12px;
	width: 100%;
}

.akismet-new-snapshot ul li {
    color: #999;
	float: left;
    font-size: 11px;
	padding: 0 20px;
    text-transform: uppercase;
	width: 33%;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-ms-box-sizing: border-box;
}

.akismet-new-snapshot ul li:first-child,
.akismet-new-snapshot ul li:nth-child(2) {
	border-right:1px dotted #ccc;
}

.akismet-new-snapshot ul li span {
    color: #52accc;
	display: block;
	font-size: 32px;
	font-weight: lighter;
	line-height: 1.5em;
}

.akismet-settings th:first-child {
	vertical-align: top;
	padding-top: 15px;
}

.akismet-settings th.akismet-api-key {
	vertical-align: middle;
	padding-top: 0;
}

.akismet-settings input[type=text] {
	width: 75%;
}

.akismet-settings span.akismet-note{
	float: left;
	padding-left: 23px;
	font-size: 75%;
	margin-top: -10px;
}

/**
 * For the activation notice on the plugins page.
 */

#akismet_setup_prompt {
	background: none;
	border: none;
	margin: 0;
	padding: 0;
	width: 100%;
}

.akismet_activate {
	border: 1px solid #4F800D;
	padding: 5px;
	margin: 15px 0;
	background: #83AF24;
	background-image: -webkit-gradient(linear, 0% 0, 80% 100%, from(#83AF24), to(#4F800D));
	background-image: -moz-linear-gradient(80% 100% 120deg, #4F800D, #83AF24);
	-moz-border-radius: 3px;
	border-radius: 3px;
	-webkit-border-radius: 3px;
	position: relative;
	overflow: hidden;
}

.akismet_activate .aa_a {
	position: absolute;
	top: -5px;
	right: 10px;
	font-size: 140px;
	color: #769F33;
	font-family: Georgia, "Times New Roman", Times, serif;
	z-index: 1;
}

.akismet_activate .aa_button {
	font-weight: bold;
	border: 1px solid #029DD6;
	border-top: 1px solid #06B9FD;
	font-size: 15px;
	text-align: center;
	padding: 9px 0 8px 0;
	color: #FFF;
	background: #029DD6;
	background-image: -webkit-gradient(linear, 0% 0, 0% 100%, from(#029DD6), to(#0079B1));
	background-image: -moz-linear-gradient(0% 100% 90deg, #0079B1, #029DD6);
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-border-radius: 2px;
	width: 100%;
	cursor: pointer;
	margin: 0;
}

.akismet_activate .aa_button:hover {
	text-decoration: none !important;
	border: 1px solid #029DD6;
	border-bottom: 1px solid #00A8EF;
	font-size: 15px;
	text-align: center;
	padding: 9px 0 8px 0;
	color: #F0F8FB;
	background: #0079B1;
	background-image: -webkit-gradient(linear, 0% 0, 0% 100%, from(#0079B1), to(#0092BF));
	background-image: -moz-linear-gradient(0% 100% 90deg, #0092BF, #0079B1);
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-border-radius: 2px;
}

.akismet_activate .aa_button_border {
	border: 1px solid #006699;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-border-radius: 2px;
	background: #029DD6;
	background-image: -webkit-gradient(linear, 0% 0, 0% 100%, from(#029DD6), to(#0079B1));
	background-image: -moz-linear-gradient(0% 100% 90deg, #0079B1, #029DD6);
}

.akismet_activate .aa_button_container {
	box-sizing: border-box;
	display: inline-block;
	background: #DEF1B8;
	padding: 5px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-border-radius: 2px;
	width: 266px;
}

.akismet_activate .aa_description {
	position: absolute;
	top: 22px;
	left: 285px;
	margin-left: 25px;
	color: #E5F2B1;
	font-size: 15px;
	z-index: 1000;
}

.akismet_activate .aa_description strong {
	color: #FFF;
	font-weight: normal;
}

@media (max-width: 550px) {
	.akismet_activate .aa_a {
		display: none;
	}
	
	.akismet_activate .aa_button_container {
		width: 100%;
	}
}

@media (max-width: 782px) {
	.akismet_activate {
		min-width: 0;
	}
}

@media (max-width: 850px) {
	#akismet_setup_prompt .aa_description {
		display: none;
	}
	
	.akismet_activate {
		min-width: 0;
	}
}

.jetpack_page_akismet-key-config #wpcontent, .settings_page_akismet-key-config #wpcontent {
	padding-left: 0;
}

.akismet-masthead {
	background-color:#fff;
	text-align:center;
	box-shadow:0 1px 0 rgba(200,215,225,0.5),0 1px 2px #e9eff3
}
@media (max-width: 45rem) {
	.akismet-masthead {
		padding:0 1.25rem
	}
}

.akismet-masthead__inside-container {
	padding:.375rem 0;
	margin:0 auto;
	width:100%;
	max-width:45rem;
	text-align: left;
}
.akismet-masthead__logo-container {
	padding:.3125rem 0 0
}
.akismet-masthead__logo {
	width:10.375rem;
	height:1.8125rem;
}
.akismet-masthead__logo-link {
	display:inline-block;
	outline:none;
	vertical-align:middle
}
.akismet-masthead__logo-link:focus {
	line-height:0;
	box-shadow:0 0 0 2px #78dcfa
}
.akismet-masthead__logo-link+code {
	margin:0 10px;
	padding:5px 9px;
	border-radius:2px;
	background:#e6ecf1;
	color:#647a88
}
.akismet-masthead__links {
	display:-ms-flexbox;
	display:flex;
	-ms-flex-flow:row wrap;
	flex-flow:row wrap;
	-ms-flex:2 50%;
	flex:2 50%;
	-ms-flex-pack:end;
	justify-content:flex-end;
	margin:0
}
@media (max-width: 480px) {
	.akismet-masthead__links {
		padding-right:.625rem
	}
}
.akismet-masthead__link-li {
	margin:0;
	padding:0
}
.akismet-masthead__link {
	font-style:normal;
	color:#0087be;
	padding:.625rem;
	display:inline-block
}
.akismet-masthead__link:visited {
	color:#0087be
}
.akismet-masthead__link:active,.akismet-masthead__link:hover {
	color:#00aadc
}
.akismet-masthead__link:hover {
	text-decoration:underline
}
.akismet-masthead__link .dashicons {
	display:none
}
@media (max-width: 480px) {
	.akismet-masthead__link:hover,.akismet-masthead__link:active {
		text-decoration:none
	}
	.akismet-masthead__link .dashicons {
		display:block;
		font-size:1.75rem
	}
	.akismet-masthead__link span+span {
		display:none
	}
}
.akismet-masthead__link-li:last-of-type .akismet-masthead__link {
	padding-right:0
}

.akismet-lower {
	margin: 0 auto;
	text-align: left;
	max-width: 45rem;
	padding: 1.5rem;
}

.akismet-lower .notice {
	margin-bottom: 2rem;
}

.akismet-card {
	margin-top: 1rem;
	margin-bottom: 0;
	position: relative;
	margin: 0 auto 0.625rem auto;
	box-sizing: border-box;
	background: white;
	box-shadow: 0 0 0 1px rgba(200, 215, 225, 0.5), 0 1px 2px #e9eff3;
}

.akismet-card:after, .akismet-card .inside:after, .akismet-masthead__logo-container:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}

.akismet-card .inside {
	padding: 1.5rem;
	padding-top: 1rem;
}

.akismet-card .akismet-card-actions {
	margin-top: 1rem;
}

.jetpack_page_akismet-key-config .update-nag, .settings_page_akismet-key-config .update-nag {
    display: none;
}

.akismet-masthead .akismet-right {
	line-height: 2.125rem;
	font-size: 0.9rem;
}

.akismet-box {
	box-sizing: border-box;
	background: white;
	border: 1px solid rgba(200, 215, 225, 0.5);
}

.akismet-box h2, .akismet-box h3 {
	padding: 1.5rem 1.5rem .5rem 1.5rem;
	margin: 0;
}

.akismet-box p {
	padding: 0 1.5rem 1.5rem 1.5rem;
	margin: 0;
}

.akismet-jetpack-email {
	font-style: oblique;
}

.akismet-jetpack-gravatar {
	padding: 0 0 0 1.5rem;
	float: left;
	margin-right: 1rem;
	width: 54px;
	height: 54px;
}
	
.akismet-box p:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}

.akismet-box .akismet-right {
	padding-right: 1.5rem;
}

.akismet-boxes .akismet-box {
	margin-bottom: 0;
	padding: 0;
	margin-top: -1px;
}

.akismet-boxes .akismet-box:last-child {
	margin-bottom: 1.5rem;
}

.akismet-boxes .akismet-box:first-child {
	margin-top: 1.5rem;
}

.akismet-button, .akismet-button:hover, .akismet-button:visited {
	background: white;
	border-color: #c8d7e1;
	border-style: solid;
	border-width: 1px 1px 2px;
	color: #2e4453;
	cursor: pointer;
	display: inline-block;
	margin: 0;
	outline: 0;
	overflow: hidden;
	font-size: 14px;
	font-weight: 500;
	text-overflow: ellipsis;
	text-decoration: none;
	vertical-align: top;
	box-sizing: border-box;
	font-size: 14px;
	line-height: 21px;
	border-radius: 4px;
	padding: 7px 14px 9px;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

.akismet-button:hover {
	border-color: #a8bece;
}

.akismet-button:active {
	border-width: 2px 1px 1px;
}

.akismet-is-primary, .akismet-is-primary:hover, .akismet-is-primary:visited {
	background: #00aadc;
	border-color: #0087be;
	color: white;
}

.akismet-is-primary:hover, .akismet-is-primary:focus {
    border-color: #005082;
}

.akismet-is-primary:hover {
	border-color: #005082;
}

.akismet-section-header {
	position: relative;
	margin: 0 auto 0.625rem auto;
	padding: 1rem;
	box-sizing: border-box;
	box-shadow: 0 0 0 1px rgba(200, 215, 225, 0.5), 0 1px 2px #e9eff3;
	background: #ffffff;
	width: 100%;
	padding-top: 0.6875rem;
	padding-bottom: 0.6875rem;
	display: flex;
}

.akismet-section-header__label {
	display: -ms-flexbox;
	display: flex;
	-ms-flex-align: center;
	align-items: center;
	-ms-flex-positive: 1;
	flex-grow: 1;
	line-height: 1.75rem;
	position: relative;
	font-size: 0.875rem;
	color: #4f748e;
}

.akismet-section-header__actions {
	line-height: 1.75rem;
}
