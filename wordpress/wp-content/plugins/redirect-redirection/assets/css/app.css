.page__heading {
  font-family: <PERSON>ser<PERSON>;
  font-style: normal !important;
  font-weight: 600 !important;
  font-size: 20px !important;
  line-height: 27px !important;
  color: #373737 !important;
  margin: 0 !important;
  padding: 0 !important;
}

@media(min-width: 700px) {
  .page__heading {
    font-size: 30px !important;
    line-height: 37px !important;
  }
}

.page__block {
  background: white;
  padding: 10px;
  border-radius: var(--border-radius);
}

@media(min-width: 700px) {
  .page__block {
    padding: 40px;
  }
}

.automatic-redirects-block {
  padding: 30px 20px;
}

@media(min-width: 700px) {
  .automatic-redirects-block {
    padding: 40px;
  }
}

.page__paragraph {
  font-size: 18px;
  line-height: 26px;
  margin: 0;
}

.custom-body__heading {
  margin: 40px 0 35px 0;
  padding: 0;
  text-align: center;
  font-style: normal;
  font-weight: normal;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.custom-body__heading-primary {
  font-weight: 600;
  font-size: 30px;
  line-height: 37px;
  color: #373737;
  margin-bottom: 5px;
}

.custom-body__heading-secondary {
  font-size: 22px;
  line-height: 27px;
  color: #373737;
}

.custom-body__note {
  font-size: 16px;
  line-height: 26px;
  color: #8E8E8E;
  margin: 40px 0 10px 0;
}

.page__note {
  font-size: 18px;
  line-height: 26px;
  color: #8E8E8E;
  margin: 0;
}

.page__custom-flex {
  display: flex;
  flex-wrap: wrap;
  margin: 40px 0;
}

@media(min-width: 500px) {
  .page__custom-flex {
    width: 300px;
    margin: 40px auto;
  }
}

@media(min-width: 850px) {
  .page__custom-flex {
    width: 100%;
  }
}

.page__custom-col {
  width: 100%;
  margin-bottom: 25px;
}

@media(min-width: 850px) {
  .page__custom-col {
    flex: 0 0 0 auto;
    width: 33.33%;
    margin-bottom: 0;
  }
}

.page-switch-group {
  display: flex;
  align-items: center;
}

.page-switch-group__label {
  flex-grow: 1;
  font-weight: bold;
  font-size: 18px;
  line-height: 26px;
  color: #000000;
  margin-right: 20px;
}

@media(min-width: 700px) {
  .page-switch-group__label {
    font-size: 20px;
  }
}

@media(min-width: 850px) {
  .page-switch-group__label {
    flex-grow: 0;
  }
}
