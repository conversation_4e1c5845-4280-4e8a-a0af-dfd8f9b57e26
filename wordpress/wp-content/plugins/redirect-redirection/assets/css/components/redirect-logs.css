.redirect-logs__start-banner {
  max-width: 1170px;
  margin: 0 auto;
}

.redirect-start-banner {
  overflow: hidden;
  border-radius: 8px;
  text-align: center;
  font-size: 18px;
  line-height: 26px;
  color: #373737;
  padding: 39px 121px 30px;
  background: url("../assets/images/banner-background-icons.png"), linear-gradient(74.14deg, rgba(255, 255, 255, 0.9) 11.48%, rgba(255, 255, 255, 0) 46.22%), #e6f7eb;
  background-repeat: no-repeat;
  background-position: 71px 42px, 100% 100%;
}

@media (max-width: 1170px) {
  .redirect-start-banner {
    padding: 35px calc(121 / 1170 * 100vw);
  }
}

.redirect-start-banner__title {
  font-weight: bold;
  font-size: 21px;
  line-height: 26px;
  margin: 0 0 25px;
}

.redirect-start-banner__choise {
  display: grid;
  grid-template-columns: auto min-content auto;
  width: 100%;
  justify-items: center;
  justify-content: center;
  align-items: center;
  gap: 30px;
}

.choise-item {
  display: grid;
  grid-template-columns: min-content auto;
  gap: 15px;
  align-items: center;
  justify-content: start;
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  padding-right: 20px;
}
