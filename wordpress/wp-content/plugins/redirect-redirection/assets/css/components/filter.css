.filter {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 35px;
  white-space: nowrap;
}

@media(min-width: 500px) {
  .filter {
    flex-direction: row;
  }
}

.filter__select-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-weight: normal;
  font-size: 14px;
  line-height: 26px;
  background: white;
  box-shadow: 0px 0px 30px rgb(0, 0, 0, 0.19);
  border: 1px solid #d4d3d3;
  border-top: none;
  padding: 12px 15px;
  border-radius: 8px;
}

@media(min-width: 370px) and (max-width: 599px) {
  .filter__select-panel {
    max-width: 350px;
    margin: auto;
    margin-bottom: 1rem;
  }
}

@media(min-width: 600px) {
  .filter__select-panel {
    width: auto;
    left: auto;
    right: auto;
    bottom: 15px;
    padding: 12px 25px;
  }
}

@media(min-width: 1200px) {
  .filter__select-panel {
    font-size: 16px;
  }
}

@media(min-width: 1366px) {
  .filter__select-panel {
    position: static;
    bottom: 0;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.07);
    border: none;
  }
}

@media(min-width: 1518px) {
  .filter__select-panel {
    font-size: 18px;
  }
}

.filter__group-label {
  width: 100%;
  margin-bottom: 10px;
}

@media(min-width: 600px) {
  .filter__group-label {
    width: auto;
    margin-bottom: 0;
  }
}

.filter__radio {
  flex: 0 0 auto;
  width: 33%;
  display: flex;
  align-items: center;
  cursor: pointer;
}

@media(min-width: 600px) {
  .filter__radio {
    flex: 0 0 auto;
    width: auto;
    margin-right: 20px;
  }
}

.filter__radio:last-child {
  margin-right: 0;
}

.filter-input-group__radio {
  position: relative;
  width: 22.5px;
  height: 22.5px;
  margin: 0;
  margin-right: 10px;
  cursor: pointer;
}

@media(min-width: 600px) {
  .filter-input-group__radio {
    width: 30px;
    height: 30px;
    margin: 0 10px;
  }
}

.filter-input-group__radio::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: white;
  border: 1px solid #E1DADA;
  border-radius: 10rem;
  transition: .2s all;
}

.filter-input-group__radio:checked::before {
  border-color: var(--primary);
}

.filter-input-group__radio:checked::after {
  content: '';
  width: 12.5px;
  height: 12.5px;
  background: var(--primary);
  border-radius: 10rem;
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(48%, 49%);
  transition: .2s all;
}

@media(min-width: 600px) {
  .filter-input-group__radio:checked::after {
    width: 16px;
    height: 16px;
  }
}

.filter-input-group__radio:checked ~ * {
  color: var(--primary);
}

.filter-input-group__label {
  cursor: pointer;
  color: #8E8E8E;
}

.filter-input-group__label--danger {
  color: #D0302D;
}

.filter__submit-btn-container {
  width: 100%;
  margin-top: 10px;
}

@media(min-width: 600px) {
  .filter__submit-btn-container {
    width: auto;
    margin: 0;
  }
}

.filter-input-group__submit-btn {
  width: 100%;
  padding: 9px;
  font-size: 15px;
  font-weight: bold;
  line-height: 22px;
  background: var(--primary);
  color: var(--primary-invert);
  box-shadow: 0px 3px 13px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
}

@media(min-width: 600px) {
  .filter-input-group__submit-btn {
    width: auto;
    padding: 12px 25px;
    font-size: 18px;
  }
}

.filter__links {
  display: flex;
  font-size: 16px;
  font-style: normal;
  font-weight: normal;
  line-height: 26px;
  color: var(--primary);
  flex-grow: 1;
}

@media(min-width: 1366px) {
  .filter__links {
    justify-content: center;
  }
}

@media(min-width: 1518px) {
  .filter__links {
    font-size: 18px;
  }
}

.filter__search-input {
  width: 250px;
  display: flex;
  align-items: center;
}

@media(min-width: 1366px) {
  .filter__search-input {
    width: 180px;
  }
}

.filter-search-input__submit-btn {
  width: 45px;
  min-width: 45px;
  height: 45px;
  min-height: 45px;
  background: #D5D5D5;
  border-radius: 0px 7px 7px 0px;
  transition: .2s all;
}

.filter-search-input__input {
  flex-grow: 1;
  min-width: 0;
  width: auto;
  height: 45px;
  padding-left: 18px;
  border: 1px solid #d5d5d5;
  border-right: none;
  border-radius: 7px 0 0 7px;
  outline: none;
  box-shadow: none;
  transition: .2s all;
}

.filter-search-input__input:focus {
  border-color: var(--primary);
}

.filter-search-input__input:focus ~ .filter-search-input__submit-btn {
  background: var(--primary);
}