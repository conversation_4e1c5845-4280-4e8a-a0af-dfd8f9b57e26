=== Redirection ===
Contributors: Socialdude
Tags: Redirect, Redirection, 301, 307, 404, Redirects
Requires at least: 4.6
Tested up to: 6.1
Stable tag: 1.1.1
License: GPLv3
Requires PHP: 5.6

Redirection

== Description ==

**Try it out on your free dummy site: Click here => [https://tastewp.com/plugins/redirect-redirection](https://demo.tastewp.com/redirection).**
(this trick works for all plugins in the WP repo - just replace "wordpress" with "tastewp" in the URL)

Create specific URL redirections and redirection rules super-easily on a beautiful, user-friendly interface of the Redirection plugin.

Creating even conditional redirections has never been easier and quicker than with the Redirection plugin.

Creating redirection has never been simpler, but for fine-tuning, you can use a wide choice of advanced options:

- Redirect HTTP codes can be 301, 302, 303, 304, 307 and 308;
- Inclusion and exclusion rules: logged in/out users, specific user role, user’s referrer link, user’s agent, user’s cookie, user’s IP, server, and language;
- Redirection rules: Simplified redirection rules as well as advanced RegEx matches - URLs that contain specific string to new or removed string, URLs that start with X, specific permalink structures, RegEx matches, and 404s. Setting redirection rules with several matching conditions is also possible;
- Managing all redirects comfortably - enable/disable, edit and delete redirects neatly listed in the plugin menu;
- ⭐️**NEW!** 404 page redirection to a Specific URL (e.g. homepage) or to a Random Similar Post;
- ⭐️**NEW!** Import/Export redirection lists (both Specific URL Redirections and Redirection Rules);
- ⭐️**NEW!** Redirection & 404 Logs - chronological log of all redirections and 404s with a one-click solution to add the redirection and fix the dead links;
- Automatic redirects, Change URL -  these features are coming soon!

This redirect plugin is part of the Inisev product family. Have a look at our [other projects](https://inisev.com) too please! :)

== Frequently Asked Questions ==

= Can I limit who can create redirections on my site? =

By default, only Administrators can access the plugin and create and edit redirections.

= Can I bulk disable/delete redirections created by this plugin?  =

Yes, you can easily disable single or bulk redirection rules or specific redirections.

= Are redirections disabled if I uninstall the plugin?  =

All redirection rules and specific redirects will be disabled when the Redirection plugin is disabled/uninstalled. But, you can disable redirects without disabling the plugin.

= Can I redirect 404 to homepage? =

Yes! You can also create redirection 404s to any other page if you like. And there is a brand new feature of redirection 404 to a Random Similar Post.

= How to find/track dead links on my site? =

With the brand new feature “Redirection and 404 Logs” you can easily spot if there are any invalid links that your visitors are trying to reach, and best of all, you can fix them with one click on the button. Also, if you set up a Redirection Rule “Redirect all URLs which: are 404s to Random Similar Post” - you will basically fix all 404s on your site with one rule.

= How are you better than other redirection plugins? =

Redirection plugin is designed to be newbie-friendly, with a professionally designed interface, and simplified functions available to help users that are not familiar with, e.g. RegEx. Upcoming features will also be handy for not-too-technical users and users who changed their links (and permalinks).

= Can I redirect all pages on my site to a homepage (or another specific page)? =

Yes, you can. Re how to do it, you simply navigate to Redirection Rules (second tab from the left), then add new redir, under the first dropdown select “All URLs” and on the right define a destination. Note that adding this redirection will *not* cause red alert of infinite loop error, and /wp-admin section of your site is also exempted from this rule.

= Is the plugin also available in my language? =

So far we have translated the plugin into these languages:

Arabic: [قم بتعيين 301 ، 307 ، 404 ، إعادة توجيه أخرى.](https://ar.wordpress.org/plugins/redirect-redirection/)
Chinese (China): [设置 301、307、404、其他重定向。](https://cn.wordpress.org/plugins/redirect-redirection/)
Croatian: [Postavite 301, 307, 404 i druga preusmjeravanja.](https://hr.wordpress.org/plugins/redirect-redirection/)
Dutch: [Stel 301, 307, 404 in, een andere omleiding.](https://nl.wordpress.org/plugins/redirect-redirection/)
English: [Setup 301, 307, 404 and other redirections.](https://wordpress.org/plugins/redirect-redirection/)
Finnish: [Aseta 301, 307, 404 ja muut uudelleenohjaukset.](https://fi.wordpress.org/plugins/redirect-redirection/)
French (France): [Définissez 301, 307, 404, une autre redirection.](https://fr.wordpress.org/plugins/redirect-redirection/)
German: [Stellen Sie 301, 307, 404 und andere Umleitungen ein.](https://de.wordpress.org/plugins/redirect-redirection/)
Greek: [Ορίστε 301, 307, 404, άλλες ανακατευθύνσεις.](https://el.wordpress.org/plugins/redirect-redirection/)
Hungarian: [Állítsa be a 301, 307, 404 és egyéb átirányításokat.](https://hu.wordpress.org/plugins/redirect-redirection/)
Indonesian: [Setel 301, 307, 404, pengalihan lainnya.](https://id.wordpress.org/plugins/redirect-redirection/)
Italian: [Impostare 301, 307, 404, un altro reindirizzamento.](https://it.wordpress.org/plugins/redirect-redirection/)
Persian: [301، 307، 404، یک تغییر مسیر دیگر را تنظیم کنید.](https://fa.wordpress.org/plugins/redirect-redirection/)
Polish: [Ustaw 301, 307, 404, inne przekierowania.](https://pl.wordpress.org/plugins/redirect-redirection/)
Portuguese (Brazil): [Defina 301, 307, 404, outros redirecionamentos.](https://br.wordpress.org/plugins/redirect-redirection/)
Russian: [Установите 301, 307, 404, другие редиректы.](https://ru.wordpress.org/plugins/redirect-redirection/)
Spanish: [Configure 301, 307, 404 y otras redirecciones.](https://es.wordpress.org/plugins/redirect-redirection/)
Turkish: [301'e, 307'e, 404'e ve diğerlerine yönelik yönlendirme kurun.](https://tr.wordpress.org/plugins/redirect-redirection/)
Vietnamese: [Đặt 301, 307, 404, một chuyển hướng khác.](https://vi.wordpress.org/plugins/redirect-redirection/)

== Screenshots ==

1. Specific URL Redirections
2. Redirection Rules
3. Redirection rules explained
4. Advanced options
5. HTTP codes explanation

== Changelog ==
= 1.1.1 =
* Reverted change of template redirection priority

= 1.1.0 =
* Tested up to WP v6.1-RC5
* Added optional opt-in module
* Updated try it option in readme
* Fixed issue with logs clean-up - database errors PHP 8+
* Removed unnecessary files from codebase
* Adjusted redirection, it won't redirect twice before destination site

= 1.0.9 =
* Fixed issues with version 1.0.8 where template redirection support was required

= 1.0.8 =
* Fixed local redirect loops (e.g. homepage -> post)
* Adjusted our URL validator
* Changed hook of redirect execution
* Removed unwanted error_logging
* Tested with WordPress 6.0.1

= 1.0.7 =
* Tested with WordPress Beta 6.0
* Improved database management
* Added new notifications and notices
* Allowed redirections inside wp-admin
* Added all-urls rule for redirection
* Added support for PHP 5.6 (older sites)
* Added carrousel

= 1.0.6 =
* Added Redirection & 404 Logs feature; fixed an issue with redirection to another website and the issue with the set cookie
* Tested with PHP 8.0 and 8.1
* Tested with WordPress 5.9

= 1.0.5 =
* Added redirection by browser language
* Fixed bugs with language detection

= 1.0.4 =
* Added redirection rule 404s to Random Similar Post

= 1.0.3 =
* Added redirection rule 404s to Specific URL

= 1.0.2 =
* Make plugin translatable ready
* Tested up to WordPress 5.8

= 1.0.1 =
* Fixed mb functions on servers without mbstring extenstion
* Added auto redirection on activation
* Added screenshots

= 1.0.0 =
* Initial release

== Upgrade Notice ==
= 1.1.1 =
* Tested up to WP v6.1-RC5
* Added optional opt-in module
* Updated try it option in readme
* Fixed issue with logs clean-up - database errors PHP 8+
* Removed unnecessary files from codebase
* Adjusted redirection, it won't redirect twice before destination site
* Reverted change of template redirection priority
