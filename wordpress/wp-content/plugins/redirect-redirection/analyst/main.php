<?php

require_once 'sdk_resolver.php';


if (!function_exists('analyst_init')) {
	/**
	 * Initialize analyst
	 *
	 * @param array $options
	 */
	function analyst_init ($options) {
		// Try resolve latest supported SDK
		// In case resolving is failed exit the execution
		try {
			analyst_resolve_sdk($options['base-dir']);
		} catch (Exception $exception) {
			// error_log('[ANALYST] Cannot resolve any supported SDK');
			return;
		}

		try {
			global /** @var Analyst\Analyst $analyst */
			$analyst;

			// Set global instance of analyst
			if (!$analyst) {
				$analyst = Analyst\Analyst::getInstance();
			}

			$analyst->registerAccount(new Account\Account($options['client-id'], $options['client-secret'], $options['base-dir']));
		} catch (Exception $e) {
			// error_log('Analyst SDK receive an error: [' . $e->getMessage() . '] Please contact our <NAME_EMAIL>');
		}
	}
}
