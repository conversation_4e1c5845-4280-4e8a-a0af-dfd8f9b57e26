(function ($) {
  $(document).on('click', '.analyst-notice-dismiss', function () {
    var id = $(this).attr('analyst-notice-id');
    var self = this;
    
    $.post(ajaxurl, {action: 'analyst_notification_dismiss', id: id})
      .done(function () {
        $(self).parent().fadeOut()
      })
  })

  var url = new URL(window.location.href)
  
  if (url.searchParams.has('verify')) {
    var pluginId = url.searchParams.get('plugin_id')
  
    $.ajax({
      url: ajaxurl,
      method: 'POST',
      data: {
        action: 'analyst_install_verified_' + pluginId,
      },
      success: function () {
        // Refresh page without query params
        window.location.href = window.location.origin + window.location.pathname
      }
    })
  }
})(jQuery)
