.analyst-action-opt {
    cursor: pointer;
}

.analyst-modal {
    color: #000000;
    display: none;
    position: fixed;
    z-index: 1000;
    padding-top: 100px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
}

.analyst-modal-content {
    font-family: Helvetica, serif;
    position: relative;
    background-color: #fefefe;
    margin: auto;
    padding: 35px 35px 20px;
    border: 1px solid #F2F2F2;
    width: 40%;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
    -webkit-animation-name: analyst-animatetop;
    -webkit-animation-duration: 0.4s;
    animation-name: analyst-animatetop;
    animation-duration: 0.4s
}

.analyst-btn-success {
    cursor: pointer;
    color: #ffffff;
    background-color: #00AF5E;
    border: none;
    width: 100%;
    font-size: 18px;
    padding: 8px;
    font-weight: bold;
}

.analyst-btn-grey {
    cursor: pointer;
    color: #2D2D2D;
    background-color: #D8D8D8;
    border: none;
    width: 100%;
    font-size: 18px;
    padding: 8px;
    font-weight: bold;
}

.analyst-btn-secondary-ghost {
    cursor: pointer;
    background: transparent;
    border: none;
    color: #898686;
    font-size: 18px;
}

.analyst-modal-def-top-padding {
    padding-top: 20px;
}

.analyst-modal-header {
    font-size: 20px;
    font-weight: bold;
}

/*INSTALL STYLES*/
.analyst-install-footer {
    padding-top: 10px;
    text-align: center;
}

.analyst-install-image-block {
    width: 140px;
}

.analyst-install-image-block img {
    width: inherit;
}

.analyst-install-description-block {
    padding-left: 40px;
    padding-top: 5px
}

.analyst-install-description-text {
    font-size: 16px;
    color: #000000;
}

.analyst-install-permissions-list {
    list-style: disc inside;
}

.analyst-install-permissions-list li {
    padding-left: 15px;
    margin-bottom: 2px;
}

.analyst-install-footer span {
    color: #8a8787;
    padding-right: 10px;
    padding-left: 10px;
}

.analyst-install-footer span:not(:last-child) {
    border-right: 1px solid #8a8787;
}

/*INSTALL STYLES*/

.reason-answer {
    padding: 7px;
    margin-left: 23px;
    border: 1px solid #F2F2F2;
}

.analyst-link {
    color: #00AF5E;
    text-decoration: none;
}

.analyst-action-text {
    cursor: pointer;
}

.analyst-action-text:hover {
    color: #9d9a9a;
}

.analyst-disable-modal-mask {
    width: 100%;
    height: 100%;
    opacity: 0.5;
    position: absolute;
    background: white;
    top: 0;
    left: 0;
}

.analyst-smile-image {
    vertical-align: middle;
    padding-bottom: 3px;
    width: 24px;
}

#analyst-deactivation-reasons li {
    padding-bottom: 3px;
    font-size: 16px;
    color: #000000;
}

@-webkit-keyframes analyst-animatetop {
    from {top:-300px; opacity:0}
    to {top:0; opacity:1}
}

@keyframes analyst-animatetop {
    from {top:-300px; opacity:0}
    to {top:0; opacity:1}
}

.analyst-modal-close {
    color: #48036F;
    font-size: 28px;
    font-weight: bold;
    top: 12px;
    position: absolute;
    right: 15px;
}

.analyst-modal-close:hover,
.analyst-modal-close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

.analyst-modal-body {padding: 2px 16px;}

.analyst-modal-footer {
    padding: 6px 16px;
    background-color: #FFE773;
    color: white;
}

#analyst-deactivate-modal .question-answer input, textarea {
    margin-top: 5px;
    width: 100%;
}

.analyst-btn-primary {
    cursor: pointer;
    border: none;
    display:inline-block;
    padding:0.7em 1.4em;
    margin:0 0.3em 0.3em 0;
    border-radius:0.15em;
    box-sizing: border-box;
    text-decoration:none;
    font-family:'Roboto',sans-serif;
    text-transform:uppercase;
    font-weight:400;
    color:#FFFFFF;
    background-color:#9F3ED5;
    box-shadow:inset 0 -0.6em 0 -0.35em rgba(0,0,0,0.17);
    text-align:center;
    position:relative;
}

.analyst-btn-primary:disabled {
    background-color: #AD66D5;
    cursor: not-allowed;
}

.analyst-btn-primary:active{
    top:0.1em;
}
@media all and (max-width:30em){
    .analyst-btn-primary {
        display:block;
        margin:0.4em auto;
    }
}

.analyst-btn-secondary {
    cursor: pointer;
    border: none;
    display:inline-block;
    padding:0.7em 1.4em;
    margin:0 0.3em 0.3em 0;
    border-radius:0.15em;
    box-sizing: border-box;
    text-decoration:none;
    font-family:'Roboto',sans-serif;
    text-transform:uppercase;
    font-weight:400;
    color:#FFFFFF;
    background-color:#6C8CD5;
    box-shadow:inset 0 -0.6em 0 -0.35em rgba(0,0,0,0.17);
    text-align:center;
    position:relative;
}

.analyst-btn-secondary:disabled {
    background-color: #6C8CD5;
    cursor: not-allowed;
}

.analyst-btn-secondary:active{
    top:0.1em;
}
@media all and (max-width:30em){
    .analyst-btn-secondary {
        display:block;
        margin:0.4em auto;
    }
}

.analyst-notice {
    padding-right: 38px;
    position: relative;
    margin-bottom: 30px !important;
}

.analyst-notice .analyst-plugin-name {
    background-color: #00000024;
    padding-left: 7px;
    padding-right: 7px;
    position: absolute;
    top: 100%;
    border-radius: 0 0 5px 5px;
}
