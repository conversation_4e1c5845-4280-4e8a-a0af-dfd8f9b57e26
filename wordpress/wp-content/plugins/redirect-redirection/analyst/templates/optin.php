<script type="text/javascript">

  (function ($) {
    var isOptingIn = false

    $('#analyst-opt-in-modal').appendTo($('body'))

	var makeOptIn = function (pluginId) {
      if (isOptingIn) return

      isOptingIn = true

      $.ajax({
        url: ajaxurl,
        method: 'POST',
        data: {
          action: 'analyst_opt_in_' + pluginId,
        },
        success: function () {
          $('#analyst-opt-in-modal').hide()

          isOptingIn = false

          var optOutAction = $('<a />').attr({
              class: 'analyst-action-opt analyst-opt-out',
              'analyst-plugin-id': pluginId,
              'analyst-plugin-signed': '1'
            })
            .text('Opt Out')
          $('.analyst-opt-in[analyst-plugin-id="'+ pluginId +'"').replaceWith(optOutAction)

          $('[analyst-plugin-id="' + pluginId + '"').attr('analyst-plugin-opted-in', 1)
        }
      })
	}

    $(document).on('click', '.analyst-opt-in:not([loading])', function() {
	  var pluginId = $(this).attr('analyst-plugin-id')
	  var isSigned = $(this).attr('analyst-plugin-signed') === '1'

	  if (!isSigned) {
		$('#analyst-install-modal')
		  .attr('analyst-plugin-id', pluginId)
		  .show()

	    return;
	  }

      $('#analyst-install-modal').attr({'analyst-plugin-id': pluginId})

	  $(this).attr('loading', true).text('Opting In...')

	  makeOptIn(pluginId);
    })

    $('.opt-in-modal-close').click(function () {
      $('#analyst-opt-in-modal').hide()
    })
  })(jQuery)
</script>
