<div id="analyst-opt-out-modal" class="analyst-modal" style="display: none">
	<div class="analyst-modal-content" style="width: 600px">
		<div class="analyst-disable-modal-mask" id="analyst-disable-opt-out-modal-mask" style="display: none"></div>
		<div style="display: flex">
			<div class="analyst-install-image-block" style="width: 120px">
				<img src="<?=$shieldImage?>"/>
			</div>
			<div class="analyst-install-description-block">
				<strong class="analyst-modal-header">By opting out, we cannot alert you anymore  in case of important security updates.</strong>
				<p class="analyst-install-description-text">
					In addition, we won’t get pointers how to further improve the plugin based on your integration with our plugin.
				</p>
			</div>
		</div>
		<div class="analyst-modal-def-top-padding">
			<button class="analyst-btn-success opt-out-modal-close">Ok, don't opt out</button>
		</div>
		<div class="analyst-modal-def-top-padding" style="text-align: center;">
			<button class="analyst-btn-secondary-ghost" id="opt-out-action">Opt out</button>
		</div>
		<div id="analyst-opt-out-error" class="analyst-modal-def-top-padding" style="display: none;">
			<span style="color: #dc3232; font-size: 16px">Service unavailable. Please try again later</span>
		</div>
	</div>
	</div>
</div>

<script type="text/javascript">

	(function ($) {
	  var isOptingOut = false

	  $('#analyst-opt-out-modal').appendTo($('body'))

      $(document).on('click', '.analyst-opt-out', function() {
        var pluginId = $(this).attr('analyst-plugin-id')

        $('#analyst-opt-out-modal')
		  .attr({'analyst-plugin-id': pluginId})
		  .show()
      })

	  $('.opt-out-modal-close').click(function () {
        $('#analyst-opt-out-modal').hide()
      })

	  $('#opt-out-action').click(function () {
		if (isOptingOut) return

        var $mask = $('#analyst-disable-opt-out-modal-mask')
        var $error = $('#analyst-opt-out-error')

		var pluginId = $('#analyst-opt-out-modal').attr('analyst-plugin-id')

        $mask.show()
		$error.hide()

		var self = this

	    isOptingOut = true

        $(self).text('Opting out...')

        $.ajax({
          url: ajaxurl,
          method: 'POST',
          data: {
            action: 'analyst_opt_out_' + pluginId,
          },
          success: function (data) {
            $(self).text('Opt out')

			if (data && !data.success) {
			    $('#analyst-opt-out-modal').hide()

			    return
			}

            $error.hide()

            $('#analyst-opt-out-modal').hide()

			isOptingOut = false

            var optInAction = $('<a />').attr({
			  class: 'analyst-action-opt analyst-opt-in',
			  'analyst-plugin-id': pluginId,
                'analyst-plugin-signed': '1'
            })
			  .text('Opt In')
            $('.analyst-opt-out[analyst-plugin-id="'+ pluginId +'"').replaceWith(optInAction)

            $('[analyst-plugin-id="' + pluginId + '"').attr('analyst-plugin-opted-in', 0)

            $mask.hide()
          },
		  error: function () {
            $('#analyst-opt-out-error').show()

            $(self).text('Opt out')
          }
        }).done(function () {
		  $mask.hide()

          isOptingOut = false
        })
      })
    })(jQuery)
</script>
