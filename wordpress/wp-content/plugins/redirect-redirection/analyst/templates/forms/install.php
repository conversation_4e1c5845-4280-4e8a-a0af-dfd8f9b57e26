<div id="analyst-install-modal" class="analyst-modal" style="display: none" analyst-plugin-id="<?=$pluginToInstall?>">
	<div class="analyst-modal-content" style="width: 450px">
		<div class="analyst-disable-modal-mask" id="analyst-disable-install-modal-mask" style="display: none"></div>
		<div style="display: flex">
			<div class="analyst-install-image-block">
				<img src="<?=$shieldImage?>"/>
			</div>
			<div class="analyst-install-description-block">
				<strong class="analyst-modal-header">Stay on the safe side</strong>
				<p class="analyst-install-description-text">Receive our plugin’s alerts in
					case of <strong>critical security</strong> & feature
					updates and allow non-sensitive
					diagnostic tracking.</p>
			</div>
		</div>
		<div class="analyst-modal-def-top-padding">
			<button class="analyst-btn-success" id="analyst-install-action">Allow & Continue ></button>
		</div>
		<div class="analyst-modal-def-top-padding" id="analyst-permissions-block" style="display: none">
			<span>You’re granting these permissions:</span>
			<ul class="analyst-install-permissions-list">
				<li><strong>Your profile information</strong> (name and email) ​</li>
				<li><strong>Your site information</strong> (URL, WP version, PHP info, plugins & themes)</li>
				<li><strong>Plugin notices</strong> (updates, announcements, marketing, no spam)</li>
				<li><strong>Plugin events</strong> (activation, deactivation and uninstall)​</li>
			</ul>
		</div>
		<div class="analyst-install-footer analyst-modal-def-top-padding">
			<span class="analyst-action-text" id="analyst-permissions-toggle">Learn more</span>
			<span id="analyst-powered-by" style="display: none;">Powered by <a href="https://sellcodes.com/blog/wordpress-feedback-system-for-plugin-creators/?utm_source=optin_screen" target="_blank" class="analyst-link">Sellcodes.com</a></span>
			<span class="analyst-action-text analyst-install-modal-close" id="analyst-install-skip">Skip</span>
		</div>
		<div id="analyst-install-error" class="analyst-modal-def-top-padding" style="display: none; text-align: center">
			<span style="color: #dc3232; font-size: 16px">Service unavailable. Please try again later</span>
		</div>
	</div>
</div>

<script type="text/javascript">
  (function ($) {

    var installPlugin = function (pluginId) {
      var $error = $('#analyst-install-error')

	  $error.hide()

      $.ajax({
        url: ajaxurl,
        method: 'POST',
        data: {
          action: 'analyst_install_' + pluginId
        },
        success: function (data) {
		  if (data && !data.success) {
		    //error
            $('#analyst-install-modal').hide()

			return
		  }

          window.location.reload()
        },
		error: function () {
		  $('#analyst-install-modal').hide()
        }
      }).done(function () {
        $('#analyst-disable-install-modal-mask').hide()

        $('#analyst-install-action')
          .attr('disabled', false)
          .text('Allow & Continue >')
      })
	}

	if ($('#analyst-install-modal').attr('analyst-plugin-id')) {
	  $('#analyst-install-modal').show()
	}


	$('.analyst-install-modal-close').click(function () {
      $('#analyst-install-modal').hide()
    })

	$('#analyst-install-action').click(function () {
	  var pluginId = $('#analyst-install-modal').attr('analyst-plugin-id')

      $('#analyst-install-action')
		.attr('disabled', true)
		.text('Please wait...')

	  $('#analyst-disable-install-modal-mask').show()

      installPlugin(pluginId)
    })

	$('#analyst-permissions-toggle').click(function () {
	  var isVisible = $('#analyst-permissions-block').toggle().is(':visible')

	  isVisible ? $(this).text('Close section') : $(this).text('Learn more')

	  var poweredBy = $('#analyst-powered-by')
	  isVisible ? poweredBy.show() : poweredBy.hide()
    })

	$('#analyst-install-skip').click(function () {
      var pluginId = $('#analyst-install-modal').attr('analyst-plugin-id')

	  $.post(ajaxurl, {action: 'analyst_skip_install_' + pluginId}).done(function () {
		$('#analyst-install-modal').hide()
      })
    })
  })(jQuery)
</script>
