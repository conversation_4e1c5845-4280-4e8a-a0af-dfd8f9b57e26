<div id="analyst-deactivate-modal" class="analyst-modal" style="display: none">
	<div class="analyst-modal-content" style="width: 500px">
		<div class="analyst-disable-modal-mask" id="analyst-disable-deactivate-modal-mask" style="display: none"></div>
		<div style="display: flex">
			<div class="analyst-install-image-block" style="width: 80px">
				<img src="<?=$pencilImage?>"/>
			</div>
			<div class="analyst-install-description-block" style="padding-left: 20px">
				<strong class="analyst-modal-header">Why do you deactivate?</strong>
				<div class="analyst-install-description-text" style="padding-top: 2px">
					Please let us know, so we can improve it! Thank you <img class="analyst-smile-image" src="<?=$smileImage?>" alt="">
				</div>
			</div>
		</div>
		<div>
			<ul id="analyst-deactivation-reasons">
				<li>
					<label>
						<span>
							<input type="radio" name="deactivation-reason">
						</span>
						<span class="question" data-question="I couldn't understand how to make it work">I couldn't understand how to make it work</span>
					</label>
				</li>
				<li data-input-type="textarea" data-input-placeholder="What should have worked, but didn’t?">
					<label>
						<span>
							<input type="radio" name="deactivation-reason">
						</span>
						<span class="question" data-question="The plugin didn't work as expected">The plugin didn't work as expected</span>
					</label>
					<div class="question-answer"></div>
				</li>
				<li data-input-type="input" data-input-placeholder="What is the plugin name?">
					<label>
						<span>
							<input type="radio" name="deactivation-reason">
						</span>
						<span class="question" data-question="I found a better plugin">I found a better plugin</span>
					</label>
					<div class="question-answer"></div>
				</li>
				<li>
					<label>
						<span>
							<input type="radio" name="deactivation-reason">
						</span>
						<span class="question" data-question="It's a temporary deactivation">It's a temporary deactivation</span>
					</label>
					<div class="question-answer"></div>
				</li>
				<li data-input-type="textarea" data-input-placeholder="Please provide the reason of deactivation">
					<label>
						<span>
							<input type="radio" name="deactivation-reason">
						</span>
						<span class="question" data-question="Other">Other</span>
					</label>
					<div class="question-answer"></div>
				</li>
			</ul>
			<p id="analyst-deactivation-error" style="color: #dc3232; font-size: 16px; display: none">Please let us know the reason for de-activation. Thank you!</p>
		</div>
		<div>
			<button class="analyst-btn-grey" id="analyst-disabled-plugin-action">Deactivate</button>
		</div>
		<div class="" style="text-align: center; font-size: 18px; padding-top: 10px">
			<button class="analyst-btn-secondary-ghost analyst-deactivate-modal-close" style="color: #cccccc">Cancel</button>
		</div>
	</div>
</div>

<script type="text/javascript">
  (function ($) {
	$('.deactivate').click(function (e) {
      var anchor = $(this).find('[analyst-plugin-id]')
	  var pluginId = anchor.attr('analyst-plugin-id')
	  var isOptedIn = anchor.attr('analyst-plugin-opted-in') === '1'

	  // Do not ask for reason if not opted in
	  if (!isOptedIn) {
	    return
	  }

	  e.preventDefault()

	  $('#analyst-deactivate-modal')
		.attr({
		  'analyst-plugin-id': pluginId,
		  'analyst-redirect-url': $(this).find('a').attr('href')
		})
		.show()
    })

	$('.analyst-deactivate-modal-close').click(function () {
	  $('#analyst-deactivate-modal').hide()
    })

    $('#analyst-deactivation-reasons input[name="deactivation-reason"]').change(function () {
	  $('.question-answer').empty()

      var root = $('#analyst-deactivation-reasons input[name="deactivation-reason"]:checked').parents('li')

      $('#analyst-deactivation-error').hide()

	  if (!root.attr('data-input-type')) return

	  var reasonInput = $('<' + root.attr('data-input-type') + '/>').attr({placeholder: root.attr('data-input-placeholder'), class: 'reason-answer'})

	  root.find('.question-answer').append(reasonInput)
    })

	$('#analyst-disabled-plugin-action').click(function () {
	  var pluginId = $('#analyst-deactivate-modal').attr('analyst-plugin-id')
	  var pluginDeactivationUrl = $('#analyst-deactivate-modal').attr('analyst-redirect-url')

	  var root = $('#analyst-deactivation-reasons input[name="deactivation-reason"]:checked').parents('li');

      var reason = root.find('.question-answer .reason-answer').val();

      var question = root.find('.question').attr('data-question').trim()

	  var $errorBlock = $('#analyst-deactivation-error')

      if (!question) {
		return $errorBlock.show()
	  }

      $errorBlock.hide()

      var data = {
        action: 'analyst_plugin_deactivate_' + pluginId,
		question: question
      }

	  if (reason) {
	    data['reason'] = reason.trim();
	  }

      $(this).attr('disabled', true).text('Deactivating...');

      $('#analyst-disable-deactivate-modal-mask').show();

      $.ajax({
        url: ajaxurl,
        method: 'POST',
        data: data
      }).done(function () {
        window.location.href = pluginDeactivationUrl

        $('#analyst-disable-deactivate-modal-mask').hide();
      })
    })

  })(jQuery)
</script>
