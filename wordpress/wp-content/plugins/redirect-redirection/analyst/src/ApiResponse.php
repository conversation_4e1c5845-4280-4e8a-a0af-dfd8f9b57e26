<?php

namespace Analyst;

class ApiResponse
{
	/**
	 * Response headers
	 *
	 * @var array
	 */
	public $headers;

	/**
	 * Response body
	 *
	 * @var mixed
	 */
	public $body;

	/**
	 * Status code
	 *
	 * @var string
	 */
	public $code;

	public function __construct($body, $code, $headers)
	{
		$this->body = $body;
		$this->code = $code;
		$this->headers = $headers;
	}

	/**
	 * Whether status code is successful
	 *
	 * @return bool
	 */
	public function isSuccess()
	{
		return $this->code >= 200 && $this->code < 300;
	}
}
