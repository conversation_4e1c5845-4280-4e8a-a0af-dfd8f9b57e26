<?php

namespace Analyst;

use Analyst\Contracts\AnalystContract;

/**
 * Class Collector is a set of getters
 * to retrieve some data from wp site
 */
class Collector
{
	/**
	 * @var AnalystContract
	 */
	protected $sdk;

	/**
	 * @var \WP_User
	 */
	protected $user;

	public function __construct(AnalystContract $sdk)
	{
		$this->sdk = $sdk;
	}

	/**
	 * Load current user into memory
	 */
	public function loadCurrentUser()
	{
		$this->user = wp_get_current_user();
	}

	/**
	 * Get site url
	 *
	 * @return string
	 */
	public function getSiteUrl()
	{
		return get_option('siteurl');
	}

	/**
	 * Get current user email
	 *
	 * @return string
	 */
	public function getCurrentUserEmail()
	{
		return $this->user->user_email;
	}

	/**
	 * Get's email from general settings
	 *
	 * @return string
	 */
	public function getGeneralEmailAddress()
	{
		return get_option('admin_email');
	}

	/**
	 * Is this user administrator
	 *
	 * @return bool
	 */
	public function isUserAdministrator()
	{
		return in_array('administrator', $this->user->roles);
	}

	/**
	 * User name
	 *
	 * @return string
	 */
	public function getCurrentUserName()
	{
		return $this->user ? $this->user->user_nicename : 'unknown';
	}

	/**
	 * WP version
	 *
	 * @return string
	 */
	public function getWordPressVersion()
	{
		global $wp_version;

		return $wp_version;
	}

	/**
	 * PHP version
	 *
	 * @return string
	 */
	public function getPHPVersion()
	{
		return phpversion();
	}

	/**
	 * Resolves plugin information
	 *
	 * @param string $path Absolute path to plugin
	 * @return array
	 */
	public function resolvePluginData($path)
	{
        if( !function_exists('get_plugin_data') ){
            require_once( ABSPATH . 'wp-admin/includes/plugin.php' );
        }

		return get_plugin_data($path);
	}

	/**
	 * Get plugin name by path
	 *
	 * @param $path
	 * @return string
	 */
	public function getPluginName($path)
	{
		$data = $this->resolvePluginData($path);

		return $data['Name'];
	}

	/**
	 * Get plugin version
	 *
	 * @param $path
	 * @return string
	 */
	public function getPluginVersion($path)
	{
		$data = $this->resolvePluginData($path);

		return $data['Version'] ? $data['Version'] : null;
	}

	/**
	 * Get server ip
	 *
	 * @return string
	 */
	public function getServerIp()
	{
		return sanitize_text_field($_SERVER['SERVER_ADDR']);
	}

	/**
	 * @return string
	 */
	public function getSDKVersion()
	{
		return $this->sdk->version();
	}

	/**
	 * @return string
	 */
	public function getMysqlVersion()
	{
		$conn = mysqli_connect(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);

		$version = mysqli_get_server_info($conn);

		return $version ? $version : 'unknown';
	}

	/**
	 * @return string
	 */
	public function getSiteLanguage()
	{
		return get_locale();
	}


	/**
	 * Current WP theme
	 *
	 * @return false|string
	 */
	public function getCurrentThemeName()
	{
		return wp_get_theme()->get('Name');
	}

	/**
	 * Get active plugins list
	 *
	 * @return array
	 */
	public function getActivePluginsList()
	{
		if (!function_exists('get_plugins')) {
			require_once ABSPATH . 'wp-admin/includes/plugin.php';
		}

		$allPlugins = get_plugins();

		$activePluginsNames = array_map(function ($path) use ($allPlugins) {
			return $allPlugins[$path]['Name'];
		}, get_option('active_plugins'));

		return $activePluginsNames;
	}
}
