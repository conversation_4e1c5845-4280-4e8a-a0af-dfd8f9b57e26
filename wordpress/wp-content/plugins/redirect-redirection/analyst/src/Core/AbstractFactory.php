<?php

namespace Analyst\Core;

abstract class AbstractFactory
{
	/**
	 * Unserialize to static::class instance
	 *
	 * @param $raw
	 * @return static
	 */
	protected static function unserialize($raw)
	{
		$instance = maybe_unserialize($raw);

		$isProperObject = is_object($instance) && $instance instanceof static;

		// In case for some reason unserialized object is not
		// static::class we make sure it is static::class
		if (!$isProperObject) {
			$instance = new static();
		}

		return $instance;
	}
}
