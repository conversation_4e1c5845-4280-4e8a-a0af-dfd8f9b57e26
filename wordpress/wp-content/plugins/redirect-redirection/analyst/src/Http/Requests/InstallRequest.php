<?php

namespace Analyst\Http\Requests;

use Analyst\ApiResponse;
use Analyst\Collector;
use Analyst\Contracts\RequestorContract;

/**
 * Class InstallRequest
 *
 * @since 0.9.4
 */
class InstallRequest extends AbstractLoggerRequest
{
	/**
	 * Execute the request
	 * @param RequestorContract $requestor
	 * @return ApiResponse
	 */
	public function execute(RequestorContract $requestor)
	{
		return $requestor->post('logger/install', $this->toArray());
	}

	/**
	 * Make request instance
	 *
	 * @param Collector $collector
	 * @param $pluginId
	 * @param $path
	 * @return static
	 */
	public static function make(Collector $collector, $pluginId, $path)
	{
		return new static($collector, $pluginId, $path);
	}
}
