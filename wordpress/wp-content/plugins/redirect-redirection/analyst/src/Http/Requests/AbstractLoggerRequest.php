<?php

namespace Analyst\Http\Requests;

use Analyst\ApiResponse;
use Analyst\Collector;
use Analyst\Contracts\RequestContract;
use Analyst\Contracts\RequestorContract;

abstract class AbstractLoggerRequest implements RequestContract
{
	/**
	 * @var Collector
	 */
	protected $collector;

	/**
	 * @var integer
	 */
	protected $id;

	/**
	 * @var string
	 */
	protected $path;

	public function __construct(Collector $collector, $pluginId, $path)
	{
		$this->collector = $collector;
		$this->id = $pluginId;
		$this->path = $path;
	}

	/**
	 * Cast request data to array
	 *
	 * @return array
	 */
	public function toArray()
	{
		return [
			'plugin_id' => $this->id,
			'php_version' => $this->collector->getPHPVersion(),
			'wp_version' => $this->collector->getWordPressVersion(),
			'plugin_version' => $this->collector->getPluginVersion($this->path),
			'url' => $this->collector->getSiteUrl(),
			'sdk_version' => $this->collector->getSDKVersion(),
			'ip' => $this->collector->getServerIp(),
			'mysql_version' => $this->collector->getMysqlVersion(),
			'locale' => $this->collector->getSiteLanguage(),
			'current_theme' => $this->collector->getCurrentThemeName(),
			'active_plugins_list' => implode(', ', $this->collector->getActivePluginsList()),
			'email' => $this->collector->getGeneralEmailAddress(),
			'name' => $this->collector->getCurrentUserName()
		];
	}

	/**
	 * Execute the request
	 * @param RequestorContract $requestor
	 * @return ApiResponse
	 */
	public abstract function execute(RequestorContract $requestor);
}
