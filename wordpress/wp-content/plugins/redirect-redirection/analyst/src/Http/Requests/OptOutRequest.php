<?php

namespace Analyst\Http\Requests;

use Analyst\ApiResponse;
use Analyst\Collector;
use Analyst\Contracts\RequestContract;
use Analyst\Contracts\RequestorContract;

/**
 * Class OptOutRequest
 *
 * Is is very similar to install request
 * but with different path
 *
 * @since 0.9.9
 */
class OptOutRequest extends AbstractLoggerRequest
{
	/**
	 * @param Collector $collector
	 * @param $pluginId
	 * @param $path
	 * @return static
	 */
	public static function make(Collector $collector, $pluginId, $path)
	{
		return new static($collector, $pluginId, $path);
	}

	/**
	 * Execute the request
	 * @param RequestorContract $requestor
	 * @return ApiResponse
	 */
	public function execute(RequestorContract $requestor)
	{
		return $requestor->post('logger/opt-out', $this->toArray());
	}
}
