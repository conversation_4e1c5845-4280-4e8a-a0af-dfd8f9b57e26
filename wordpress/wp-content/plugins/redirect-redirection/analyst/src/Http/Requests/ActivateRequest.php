<?php

namespace Analyst\Http\Requests;

use Analyst\ApiResponse;
use Analyst\Collector;
use Analyst\Contracts\RequestContract;
use Analyst\Contracts\RequestorContract;

/**
 * Class ActivateRequest
 *
 * Is is very similar to install request
 * but with different path
 *
 * @since 0.9.12
 */
class ActivateRequest extends AbstractLoggerRequest
{
	/**
	 * Execute the request
	 * @param RequestorContract $requestor
	 * @return ApiResponse
	 */
	public function execute(RequestorContract $requestor)
	{
		return $requestor->post('logger/activate', $this->toArray());
	}

	/**
	 * Make request instance
	 *
	 * @param Collector $collector
	 * @param $pluginId
	 * @param $path
	 * @return static
	 */
	public static function make(Collector $collector, $pluginId, $path)
	{
		return new static($collector, $pluginId, $path);
	}
}
