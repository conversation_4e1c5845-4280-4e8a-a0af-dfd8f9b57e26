<?php

namespace Analyst\Http\Requests;

use Analyst\ApiResponse;
use Analyst\Collector;
use Analyst\Contracts\RequestorContract;

/**
 * Class DeactivateRequest
 *
 * @since 0.9.10
 */
class DeactivateRequest extends AbstractLoggerRequest
{
	/**
	 * @var string
	 */
	protected $question;

	/**
	 * @var string
	 */
	protected $answer;

	/**
	 * @param Collector $collector
	 * @param $pluginId
	 * @param $path
	 * @param $question
	 * @param $answer
	 * @return static
	 */
	public static function make(Collector $collector, $pluginId, $path, $question, $answer)
	{
		return new static($collector, $pluginId, $path, $question, $answer);
	}

	public function __construct(Collector $collector, $pluginId, $path, $question, $answer)
	{
		parent::__construct($collector, $pluginId, $path);

		$this->question = $question;
		$this->answer = $answer;
	}

	public function toArray()
	{
		return array_merge(parent::toArray(), [
			'question' => $this->question,
			'answer' => $this->answer,
		]);
	}

	/**
	 * Execute the request
	 * @param RequestorContract $requestor
	 * @return ApiResponse
	 */
	public function execute(RequestorContract $requestor)
	{
		return $requestor->post('logger/deactivate', $this->toArray());
	}
}
