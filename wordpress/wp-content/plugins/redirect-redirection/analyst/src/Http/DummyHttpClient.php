<?php

namespace Analyst\Http;

use Analyst\ApiResponse;
use Analyst\Contracts\HttpClientContract;

class DummyHttpClient implements HttpClientContract
{
	/**
	 * Make an http request
	 *
	 * @param $method
	 * @param $url
	 * @param $body
	 * @param $headers
	 * @return ApiResponse
	 */
	public function request($method, $url, $body, $headers)
	{
		return new ApiResponse('Dummy response', 200, []);
	}

	/**
	 * Must return `true` if client is supported
	 *
	 * @return bool
	 */
	public static function hasSupport()
	{
		return true;
	}
}
