<?php

namespace Analyst\Http;

use WP_Error;
use Analyst\ApiResponse;
use Analyst\Contracts\HttpClientContract;
use Requests_Utility_CaseInsensitiveDictionary;

class WordPressHttpClient implements HttpClientContract
{
	/**
	 * Make an http request
	 *
	 * @param $method
	 * @param $url
	 * @param $body
	 * @param $headers
	 * @return ApiResponse
	 */
	public function request($method, $url, $body, $headers)
	{
		$options = [
			'body' => json_encode($body),
			'headers' => $headers,
			'method' => $method,
			'timeout' => 30,
		];

		$response = wp_remote_request($url, $options);

		$body = [];
		$responseHeaders = [];

		if ($response instanceof WP_Error) {
			$code = $response->get_error_code();
		} else {
			/** @var Requests_Utility_CaseInsensitiveDictionary $headers */
			$responseHeaders = $response['headers']->getAll();
			$body = json_decode($response['body'], true);
			$code = $response['response']['code'];
		}


		return new ApiResponse(
			$body,
			$code,
			$responseHeaders
		);
	}

	/**
	 * Must return `true` if client is supported
	 *
	 * @return bool
	 */
	public static function hasSupport()
	{
		return function_exists('wp_remote_request');
	}
}
