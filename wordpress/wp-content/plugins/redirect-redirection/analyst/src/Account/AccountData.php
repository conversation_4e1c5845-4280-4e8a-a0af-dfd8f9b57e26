<?php

namespace Account;

/**
 * Class AccountData is the data holder
 * for Analyst\Account\Account class
 * which is unserialized from database
 */
class AccountData
{
	/**
	 * Account id
	 *
	 * @var string
	 */
	protected $id;

	/**
	 * Account secret key
	 *
	 * @var string
	 */
	protected $secret;

	/**
	 * Basename of plugin
	 *
	 * @var string
	 */
	protected $path;

	/**
	 * Whether admin accepted opt in
	 * terms and permissions
	 *
	 * @var bool
	 */
	protected $isInstalled = false;

	/**
	 * Is user sign in for data tracking
	 *
	 * @var bool
	 */
	protected $isOptedIn = false;

	/**
	 * Is user accepted permissions grant
	 * for collection site data
	 *
	 * @var bool
	 */
	protected $isSigned = false;

	/**
	 * Is user ever resolved install modal window?
	 *
	 * @var bool
	 */
	protected $isInstallResolved;

	/**
	 * @return string
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @param string $id
	 */
	public function setId($id)
	{
		$this->id = $id;
	}

	/**
	 * @param string $path
	 * @return AccountData
	 */
	public function setPath($path)
	{
		$this->path = $path;
		return $this;
	}

	/**
	 * @return bool
	 */
	public function isInstalled()
	{
		return $this->isInstalled;
	}

	/**
	 * @param bool $isInstalled
	 */
	public function setIsInstalled($isInstalled)
	{
		$this->isInstalled = $isInstalled;
	}

	/**
	 * @return bool
	 */
	public function isOptedIn()
	{
		return $this->isOptedIn;
	}

	/**
	 * @param bool $isOptedIn
	 */
	public function setIsOptedIn($isOptedIn)
	{
		$this->isOptedIn = $isOptedIn;
	}

	/**
	 * @return bool
	 */
	public function isSigned()
	{
		return $this->isSigned;
	}

	/**
	 * @param bool $isSigned
	 */
	public function setIsSigned($isSigned)
	{
		$this->isSigned = $isSigned;
	}

	/**
	 * @return string
	 */
	public function getPath()
	{
		return $this->path;
	}

	/**
	 * @return string
	 */
	public function getSecret()
	{
		return $this->secret;
	}

	/**
	 * @param string $secret
	 */
	public function setSecret($secret)
	{
		$this->secret = $secret;
	}

	/**
	 * @return bool
	 */
	public function isInstallResolved()
	{
		return $this->isInstallResolved;
	}

	/**
	 * @param bool $isInstallResolved
	 */
	public function setIsInstallResolved($isInstallResolved)
	{
		$this->isInstallResolved = $isInstallResolved;
	}
}
