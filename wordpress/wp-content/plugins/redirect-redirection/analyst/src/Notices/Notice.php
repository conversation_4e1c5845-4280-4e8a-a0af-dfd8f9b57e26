<?php

namespace Analyst\Notices;

class Notice
{
	/**
	 * Id of notice
	 *
	 * @var string
	 */
	protected $id;

	/**
	 * Body of notice
	 *
	 * @var string
	 */
	protected $body;

	/**
	 * Account id
	 *
	 * @var string
	 */
	protected $accountId;

	/**
	 * The plugin name
	 *
	 * @var string
	 */
	protected $pluginName;

	/**
	 * New notice
	 *
	 * @param $id
	 * @param $accountId
	 * @param $body
	 * @param null $pluginName
	 *
	 * @return Notice
	 */
	public static function make($id, $accountId, $body, $pluginName = null)
	{
		return new Notice($id, $accountId, $body, $pluginName);
	}

	public function __construct($id, $accountId, $body, $pluginName)
	{
		$this->setId($id);
		$this->setBody($body);
		$this->setAccountId($accountId);
		$this->setPluginName($pluginName);
	}

	/**
	 * @return string
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @param string $id
	 */
	public function setId($id)
	{
		$this->id = $id;
	}

	/**
	 * @return string
	 */
	public function getBody()
	{
		return $this->body;
	}

	/**
	 * @param string $body
	 */
	public function setBody($body)
	{
		$this->body = $body;
	}

	/**
	 * @return string
	 */
	public function getAccountId()
	{
		return $this->accountId;
	}

	/**
	 * @param string $accountId
	 */
	public function setAccountId($accountId)
	{
		$this->accountId = $accountId;
	}

	/**
	 * @return string|null
	 */
	public function getPluginName()
	{
		return $this->pluginName;
	}

	/**
	 * @param string $pluginName
	 */
	public function setPluginName($pluginName)
	{
		$this->pluginName = $pluginName;
	}
}
