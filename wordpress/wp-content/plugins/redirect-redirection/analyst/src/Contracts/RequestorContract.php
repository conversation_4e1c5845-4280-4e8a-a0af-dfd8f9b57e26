<?php

namespace Analyst\Contracts;

interface RequestorContract
{
	/**
	 * Get request
	 *
	 * @param $url
	 * @param array $headers
	 * @return mixed
	 */
	public function get($url, $headers = []);

	/**
	 * Post request
	 *
	 * @param $url
	 * @param $body
	 * @param array $headers
	 * @return mixed
	 */
	public function post($url, $body = [], $headers = []);

	/**
	 * Put request
	 *
	 * @param $url
	 * @param $body
	 * @param array $headers
	 * @return mixed
	 */
	public function put($url, $body = [], $headers = []);

	/**
	 * Delete request
	 *
	 * @param $url
	 * @param array $headers
	 * @return mixed
	 */
	public function delete($url, $headers = []);
}
