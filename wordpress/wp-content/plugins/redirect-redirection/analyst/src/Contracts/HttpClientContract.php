<?php
namespace Analyst\Contracts;

use Analyst\ApiResponse;

interface HttpClientContract
{
	/**
	 * Make an http request
	 *
	 * @param $method
	 * @param $url
	 * @param $body
	 * @param $headers
	 * @return ApiResponse
	 */
	public function request($method, $url, $body, $headers);

	/**
	 * Must return `true` if client is supported
	 *
	 * @return bool
	 */
	public static function hasSupport();
}
