<?php

namespace Analyst\Contracts;

interface TrackerContract
{
	/**
	 * Should register activation and deactivation
	 * event hooks
	 *
	 * @return void
	 */
	public function registerHooks();

	/**
	 * Will fire when admin activates plugin
	 *
	 * @return void
	 */
	public function onActivePluginListener();

	/**
	 * Will fire when admin deactivates plugin
	 *
	 * @return void
	 */
	public function onDeactivatePluginListener();

	/**
	 * Will fire when user opted in
	 *
	 * @return void
	 */
	public function onOptInListener();

	/**
	 * Will fire when user opted out
	 *
	 * @return void
	 */
	public function onOptOutListener();

	/**
	 * Will fire when user accept opt/in at first time
	 *
	 * @return void
	 */
	public function onInstallListener();

	/**
	 * Will fire when user skipped installation
	 *
	 * @return void
	 */
	public function onSkipInstallListener();

	/**
	 * Will fire when user delete plugin through admin panel.
	 * This action will happen if admin at least once
	 * activated the plugin.
	 *
	 * The register_uninstall_hook function accepts only static
	 * function or global function to be executed, so this is
	 * why this method is static
	 *
	 * @return void
	 */
	public static function onUninstallPluginListener();
}
