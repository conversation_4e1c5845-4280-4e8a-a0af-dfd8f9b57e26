<?php

namespace Analyst\Contracts;

/**
 * Interface CacheContract
 *
 * @since 1.1.5
 */
interface CacheContract
{
	/**
	 * Save value with given key
	 *
	 * @param string $key
	 * @param string $value
	 *
	 * @return static
	 */
	public function put($key, $value);

	/**
	 * Get value by given key
	 *
	 * @param $key
	 *
	 * @param null $default
	 * @return string
	 */
	public function get($key, $default = null);

	/**
	 * @param $key
	 *
	 * @return static
	 */
	public function delete($key);

	/**
	 * Should get value and remove it from cache
	 *
	 * @param $key
	 * @param null $default
	 * @return mixed
	 */
	public function pop($key, $default = null);
}
