<?php

if (!function_exists('analyst_resolve_sdk')) {

	/**
	 * Resolve supported sdk versions and load latest supported one
	 * also bootstrap sdk with autoloader
	 *
	 * @since 1.1.3
	 *
	 * @param null $thisPluginPath
	 * @return void
	 * @throws Exception
	 */
    function analyst_resolve_sdk($thisPluginPath = null) {
    	static $loaded = false;

    	// Exit if we already resolved SDK
    	if ($loaded) return;

        $plugins = get_option('active_plugins');

		if ($thisPluginPath) {
			array_push($plugins, plugin_basename($thisPluginPath));
		}

        $pluginsFolder = WP_PLUGIN_DIR;

        $possibleSDKs = array_map(function ($path) use ($pluginsFolder) {
            $sdkFolder = sprintf('%s/%s/analyst/', $pluginsFolder, dirname($path));

            $sdkFolder = str_replace('\\', '/', $sdkFolder);

            $versionPath = $sdkFolder . 'version.php';

            if (file_exists($versionPath)) {
                return require $versionPath;
            }

            return false;
        }, $plugins);

        global $wp_version;

        // Filter out plugins which has no SDK
        $SDKs = array_filter($possibleSDKs, function ($s) {return is_array($s);});

        // Filter SDKs which is supported by PHP and WP
        $supported = array_values(array_filter($SDKs, function ($sdk) use($wp_version) {
           $phpSupported = version_compare(PHP_VERSION, $sdk['php']) >= 0;
           $wpSupported = version_compare($wp_version, $sdk['wp']) >= 0;

           return $phpSupported && $wpSupported;
        }));

        // Sort SDK by version in descending order
        uasort($supported, function ($x, $y) {
           return version_compare($y['sdk'], $x['sdk']);
        });

	    // Reset sorted values keys
	    $supported = array_values($supported);

        if (!isset($supported[0])) {
            throw new Exception('There is no SDK which is support current PHP version and WP version');
        }

        // Autoload files for supported SDK
        $autoloaderPath = str_replace(
            '\\',
            '/',
            sprintf('%s/autoload.php', $supported[0]['path'])
        );

        require_once $autoloaderPath;

        $loaded = true;
    }
}
