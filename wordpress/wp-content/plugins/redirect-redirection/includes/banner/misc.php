<?php

  /**
   * File for our cool Carousel in the footer
   *
   * @category Child Plugin
   * @version v0.2.0
   * @since v0.1.0
   * <AUTHOR> <<EMAIL>>
   */

  // Namespace
  namespace Inisev\Subs;

  // Disallow direct access
  if (defined('ABSPATH')) {

    /**
     * Main class for handling the Carousel
     */
    if (!class_exists('Inisev\Subs\Inisev_Carousel')) {
      class Inisev_Carousel {

        // Should hide it for good i.e. styles may be broken?
        private $error = 0;

        // Slugs of plugins
        private $usm_premium = 'usm-premium/usm_premium_icons.php';
        private $usm_slug = 'ultimate-social-media-icons/ultimate_social_media_icons.php';
        private $bmi_premium = 'backup-backup-pro/backup-backup-pro.php';
        private $bmi_slug = 'backup-backup/backup-backup.php';
        private $cdp_premium = 'copy-delete-posts-premium/copy-delete-posts-premium.php';
        private $cdp_slug = 'copy-delete-posts/copy-delete-posts.php';
        private $mpu_slug = 'pop-up-pop-up/pop-up-pop-up.php';
        private $redi_slug = 'redirect-redirection/redirect-redirection.php';

        /*
        * Compile some variables for "future us"
        * Such as slug of current plugin, root dir of plugin
        */
        function __construct($root_file, $root_dir) {

          // This roots
          $this->_root_file = $root_file;
          $this->_root_dir = $root_dir;

          // Add handler for Ajax request
          if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'POST') {

            // Check if slug is defined
            if (isset($_POST['slug']) && !empty($_POST['slug'])) {

              // Handle the request
              add_action('wp_ajax_inisev_installation', [&$this, 'handle_installation']);

            }

            // Stop for POST
            return;

          }

          // WordPress globals
          global $menu;

          // Make sure WP_PLUGIN_DIR is defined
          if (!defined('WP_PLUGIN_DIR')) return $this->fail(1);
          if (!function_exists('trailingslashit')) return $this->fail(2);
          if (!defined('DIRECTORY_SEPARATOR')) define('DIRECTORY_SEPARATOR', '/');

          // That's in case the developer moved this file somewhere else
          $tmp_slug = trailingslashit($this->_root_dir);
          $tmp_root = trailingslashit(WP_PLUGIN_DIR);
          $tmp_name = explode(DIRECTORY_SEPARATOR, substr($tmp_slug, strlen($tmp_root)));

          // Make the "probably" slug name
          $this->page = sanitize_text_field($_GET['page']);
          $this->slug = $tmp_name[0];
          $this->root = $tmp_root . $this->slug;

          // Make lowercase slug
          $this->slug_low = $this->makelower($this->slug);

          // We don't need those anymore
          unset($tmp_slug, $tmp_root, $tmp_name);

          // Check if the guess is correct enough
          if (!is_dir($this->root)) return $this->fail(3);

          // Check if the script requires to be in hook
          if (!function_exists('current_action')) return $this->fail(4);
          $this->hooked = (current_action() == '' ? false : true);

          // Add hook if it's required
          if (!$this->hooked) {

            // Hook the script to init
            add_action('admin_menu', [&$this, 'setup'], PHP_INT_MAX);

          } else {

            // The child plugin is already hooked, check if correctly
            if (current_action() == 'admin_menu' || isset($menu)) {

              // If the hook is correct continue
              $this->setup();

            } else {

              // Hook the script to init if it's not hooked to it already
              add_action('admin_menu', [&$this, 'setup'], PHP_INT_MAX);

            }

          }

        }

        /*
        * Main setup of this child plugin
        */
        public function setup() {

          // WordPress Global Variables
          global $menu;

          // Make sure $menu exists
          if (!isset($menu) || !is_array($menu)) return $this->fail(5);

          // Get menu slug name
          if (!$this->menu_name($menu)) return false;

          if (/*$this->page === $this->menu && */!defined('INISEV_CAROUSEL')) {

            // Initialize Carousel constant
            define('INISEV_CAROUSEL', true);

            // Root URL for assets
            $this->url = trailingslashit(plugins_url(null, $this->_root_file));

            // Load styles
            wp_enqueue_script('inisev-carousel-script', ($this->url . 'assets/index.min.js'), [], filemtime($this->_root_dir . '/assets/index.min.js'), true);
            wp_enqueue_style('inisev-carousel-style', ($this->url . 'assets/style.min.css'), [], filemtime($this->_root_dir . '/assets/style.min.css'));

            // Print the footer
            if (!has_action('ins_global_print_carrousel')) {
              add_action('ins_global_print_carrousel', [&$this, '_print'], 1);
            }

          }

        }

        /*
        * This function may be used for debugging purposes
        */
        private function fail($code = false) {

          if ($code === false) {

            // Return error code if specified as request ($code === false)
            return $this->error;

          } else {

            // Set the error code and return
            // error_log($code);
            $this->error = $code;
            return false;

          }

        }

        /*
        * Helper function remove _ -/ characters and make lowercase
        */
        private function makelower($str) {

          $str = str_replace('_', '', $str);
          $str = str_replace('-', '', $str);
          $str = str_replace('/', '', $str);
          $str = str_replace('\/', '', $str);
          $str = str_replace(' ', '', $str);
          $str = strtolower($str);

          return $str;

        }

        /*
        * This function will find slug of menu page
        */
        private function menu_name(&$menu) {

          // Find the menu slug
          // IMPORTANT: It requires the plugin to use own icon (own assets)
          foreach ($menu as $priority => $details) {
            if (is_array($details) && sizeof($details) >= 6) {
              for ($i = 0; $i < sizeof($details); ++$i) {
                if ($this->makelower($details[$i]) == $this->slug_low) {
                  $this->menu = $details[2];
                  break;
                }
              }
              if (isset($this->menu)) break;
            }
          }

          // MyPopUps exception
          if (!isset($this->menu)) {
            $mpu = ['wpmypopups', 'mypopups', 'popuppopup'];
            if (in_array($this->slug_low, $mpu)) {
              $this->menu = 'wp-mypopups';
            }
          }

          if (!isset($this->menu)) {
            $bmi = ['backupbackup', 'backup-backup', 'backup-migration', 'backupmigration'];
            if (in_array($this->slug_low, $bmi)) {
              $this->menu = 'backup-migration';
            }
          }

          if (!isset($this->menu)) {
            $hhr = ['httpsremover', 'httphttpsremover'];
            if (in_array($this->slug_low, $hhr)) {
              $this->menu = 'httphttpsRemoval';
            }
          }

          if (!isset($this->menu)) {
            $wpc = ['wp-clone', 'wp-clone', 'wpclonebywpacademy'];
            if (in_array($this->slug_low, $wpc)) {
              $this->menu = 'wp-clone';
            }
          }

          // Make sure it found something
          if (isset($this->menu)) return true;
          else return true;
          // else return $this->fail(6);

        }

        /*
        * Helper: Include file
        */
        private function _include($path) {

          include_once trailingslashit($this->_root_dir) . 'views/' . $path . '.php';

        }

        /*
        * Helper: Get asset URL
        */
        private function get_asset($file) {

          return $this->url . $file;

        }

        /*
        * Helper: Get asset and print URL
        */
        private function _asset($file) {

          echo $this->get_asset('views/' . $file);

        }

        /*
        * Upgrade plugin, this function probably will never be fired
        */
        private function upgrade_plugin($plugin_slug) {

          // Include upgrader
          include_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';
          wp_cache_flush();

          // Initialize & upgrade the plugin
          $upgrader = new \Plugin_Upgrader();
          $upgraded = $upgrader->upgrade($plugin_slug);

          // Return status or WP Error
          return $upgraded;

        }

        /*
        * Check if plugin is installed by slug
        */
        private function is_plugin_installed($slug) {

          // Get all plugins
          $all_plugins = get_plugins();

          // Make sure all slugs are in lowercase.
          foreach ($all_plugins as $plug => $v) {

            // Once something match return success
            if (strtolower($plug) == strtolower($slug)) return true;

          }

          // If nothing just fail
          return false;

          // When I exactly know the letter case...
          // if (!empty($all_plugins[$slug])) return true;
          // else return false;

        }

        /*
        * Install the plugin by slug
        */
        private function install_plugin($plugin_zip) {

          // Include upgrader
          include_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';
          wp_cache_flush();

          // Initialize WP upgrader & install the plugin
          $upgrader = new \Plugin_Upgrader();
          $installed = $upgrader->install($plugin_zip);

          // Return status or WP Error
          return $installed;

        }

        /*
        * Install file
        */
        private function install($slug, $directory_slug) {

          // Prepare the URLs and full slug
          $plugin_slug = $slug;
          $plugin_zip = 'https://downloads.wordpress.org/plugin/' . $directory_slug . '.latest-stable.zip';

          // Make sure the plugin is not installed
          if ($this->is_plugin_installed($plugin_slug)) {

            // Upgrade the plugin if it's installed somehow
            $this->upgrade_plugin($plugin_slug);
            $installed = true;

            // Install instead
          } else $installed = $this->install_plugin($plugin_zip);

          // Check if there was any error
          if (!is_wp_error($installed) && $installed) {
            $activate = activate_plugin($plugin_slug);

            if (is_null($activate)) {

              $url = admin_url('', 'admin');

              // CDP has special alert when installed with quick-install module
              if ($_POST['slug'] === 'cdp') {
                update_option('_cdp_cool_installation', true);
                update_option('_cdp_redirect', true);
                $url = admin_url() . 'admin.php?page=copy-delete-posts';
              }

              // Redirection for MPU
              if ($_POST['slug'] === 'mpu') {
                update_option('wp_mypopups_do_activation_redirect', true);
                $url = admin_url() . 'admin.php?page=wp-mypopups';
              }

              // Redirection for USM
              if ($_POST['slug'] === 'usm') {
                update_option('sfsi_plugin_do_activation_redirect', true);
                $url = admin_url() . 'admin.php?page=sfsi-options';
              }

              // Redirection for BMI
              if ($_POST['slug'] === 'bmi') {
                update_option('_bmi_redirect', true);
                $url = admin_url() . 'admin.php?page=backup-migration';
              }

              // Redirection for RED
              if ($_POST['slug'] === 'redi') {
                update_option('irrp_activation_redirect', true);
                $url = admin_url() . 'admin.php?page=irrp-redirection';
              }

              // Send success
              wp_send_json_success([ 'installed' => true, 'url' => $url ]);

              // I don't know what happened here and if it's even possible
            } else wp_send_json_error();

            // Send fail
          } else wp_send_json_error();

        }

        /*
        * Add/print the Carousel
        */
        public function _print() {

          try {

            include_once trailingslashit($this->_root_dir) . 'views/index.php';

          } catch (\Exception $e) {

            return $this->fail(7);

          } catch (\Exception $e) {

            return $this->fail(8);

          }

        }

        /*
        * Handle ajax request
        */
        public function handle_installation() {

          // Handle the slug and install the plugin
          $slug = sanitize_text_field($_POST['slug']);
          if ($slug === 'usm') {

            $this->install($this->usm_slug, 'ultimate-social-media-icons');

          } elseif ($slug === 'bmi') {

            $this->install($this->bmi_slug, 'backup-backup');

          } elseif ($slug === 'cdp') {

            $this->install($this->cdp_slug, 'copy-delete-posts');

          } elseif ($slug === 'mpu') {

            $this->install($this->mpu_slug, 'pop-up-pop-up');

          } elseif ($slug == 'redi') {

            $this->install($this->redi_slug, 'redirect-redirection');

            // Anything else error
          } else wp_send_json_error();

        }

      }
    }

    // Disallow usage of multiple Carousels + allow only GET requests
    if (!defined('INISEV_CAROUSEL')) {

      // Make sure settings/menu page slug exsits
      if (!empty($_GET['page']) || (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST')) {

        // Initialize the Carousel
        $carousel = new Inisev_Carousel(__FILE__, __DIR__);

      }

    }

  }
