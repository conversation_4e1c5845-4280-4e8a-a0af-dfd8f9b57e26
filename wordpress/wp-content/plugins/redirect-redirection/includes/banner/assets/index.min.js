!function(){let e=[],t={name:null,trigger:null,element:null},n=null,r=null,i=!1;const s=e=>{o(),c(e),l()},c=e=>{t.name&&document.querySelector(".ci-project-content").classList.remove(`ci-${t.name}-visible`),t.name=e.id.replace("-trigger",""),document.querySelector(".ci-project-content").classList.add(`ci-${t.name}-visible`),t.trigger&&t.trigger.classList.remove("ci-selected-project"),t.trigger=e,t.trigger.classList.add("ci-selected-project"),t.element=document.querySelector(`.ci-project-${t.name}`)},l=()=>{n||(n=setInterval(()=>{const n=e.findIndex(e=>e.name===t.name),r=n===e.length-1?0:n+1;c(e[r].trigger)},1e4))},o=()=>{clearInterval(n),n=null},a=()=>{const e=(e=>{const t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)})(r);!n&&e?l():n&&!e&&o()},u=e=>{const t=e.target,r=t.dataset.slug,s=t.innerText;!0!==i&&(i=!0,t.innerText="Installing, please wait...",t.classList.add("ci-inisev-prepare"),clearInterval(n),setTimeout(()=>{t.classList.add("ci-inisev-install");let e=new XMLHttpRequest;e.open("POST",ajaxurl,!0),e.setRequestHeader("Content-type","application/x-www-form-urlencoded"),e.onload=(()=>{if(l(),200===e.status||e.status<400)try{let n=e.responseText;g(n)&&(n=v(n)),void 0!==n.success&&!0===n.success?(t.classList.remove("ci-inisev-install"),t.innerText="Plugin installed successfully :)",setTimeout(()=>{window.location.href=n.data.url},300)):d(t,s)}catch(e){d(t,s)}else d(t,s)}),e.send("action=inisev_installation&slug="+r)},7e3))},d=(e,t)=>{e.innerText="Installation failed...",setTimeout(()=>{e.classList.remove("ci-inisev-install"),setTimeout(()=>{e.classList.remove("ci-inisev-prepare"),e.innerText=t,i=!1})},2e3)},m=e=>{e&&e.addEventListener("click",u)},g=e=>{try{JSON.parse(e)}catch(t){if("string"==typeof e){let t=p(e).indexOf("}");t=0==t?e.length:-t,e=e.slice(e.indexOf("{"),t);try{JSON.parse(e)}catch(e){return!1}return!0}return!1}return!0},p=e=>"string"==typeof e?""===e?"":p(e.substr(1))+e.charAt(0):e,v=e=>{try{JSON.parse(e)}catch(t){if("string"==typeof e){let t=p(e).indexOf("}");t=0==t?e.length:-t,e=e.slice(e.indexOf("{"),t);try{JSON.parse(e)}catch(e){return!1}return JSON.parse(e)}return!1}return JSON.parse(e)};document.addEventListener("DOMContentLoaded",()=>{(()=>{const t=document.querySelectorAll(".ci-project-list-element"),n=document.querySelectorAll(".ci-project");e=Array.from(t).map((e,t)=>({trigger:e,element:n[t],name:e.id.replace("-trigger","")}))})(),c(document.querySelector("#BackupMigration-trigger")),document.querySelectorAll(".ci-project-list-element").forEach(e=>{e.addEventListener("mouseover",t=>(e=>{e.classList.contains("ci-selected-project")?o():s(e)})(e)),e.addEventListener("mouseout",l)}),r=document.querySelector(".ci-carrinis .ci-carousel"),document.addEventListener("scroll",a),document.querySelectorAll(".ci-carrinis .ci-project").forEach(e=>{e.addEventListener("mouseover",o),e.addEventListener("mouseout",l)}),(()=>{const e=document.getElementsByClassName("ci-inisev-install-plugin");for(let t=0;t<e.length;++t)m(e[t])})()})}();
