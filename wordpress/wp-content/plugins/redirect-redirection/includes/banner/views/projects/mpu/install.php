<?php

  // Namespace
  namespace Inisev\Subs;

  // Disallow direct access
  if (!defined('ABSPATH')) exit;

?>

<div class="ci-left-part ci-install-state">
  <div class="ci-project-logo">
    <a href="https://mypoups.com" target="_blank">
      <img src="<?php $this->_asset('/projects/mpu/imgs/big-colored-logo.png'); ?>">
    </a>
    <img src="<?php $this->_asset('/static/imgs/rating.svg'); ?>" class="ci-rating" >
  </div>
  <div class="ci-install-column">
    <ul class="ci-checkmark-list ci-checkmark-list-type-1">
      <li>Create cool pop-ups with ease</li>
      <li>Increase conversions, show discount codes & cookie notices, stop ad-blockers etc.</li>
      <li><b>Free</b> <span class="ci-light-font">on <a href="https://mypopups.com" target="_blank" >basic plan</a></span></li>
    </ul>
    <div class="ci-install-button">
      <button class="ci-inisev-install-plugin" data-slug="mpu">Install plugin now</button>
      <span>(from <a href="https://wordpress.org/plugins/pop-up-pop-up/" target="_blank">WP directory</a>)</span>
    </div>
  </div>
</div>
