<?php

  // Namespace
  namespace Inisev\Subs;

  // Disallow direct access
  if (!defined('ABSPATH')) exit;

?>

<div class="ci-left-part ci-install-state">
  <div class="ci-project-logo">
    <a href="https://wordpress.org/plugins/copy-delete-posts/" target="_blank">
      <div class="ci-project-logo-element">
        <img src="<?php $this->_asset('/projects/cdp/imgs/big-colored-logo.png'); ?>">
        <span>Copy & Delete<br><b>posts</b></span>
      </div>
    </a>
    <img src="<?php $this->_asset('/static/imgs/rating.svg'); ?>" class="ci-rating">
  </div>
  <div class="ci-install-column">
    <ul class="ci-checkmark-list ci-checkmark-list-type-1">
      <li>Copy posts & pages with one click</li>
      <li>Save a lot of time!</li>
      <li><b>Free</b> <span class="ci-light-font">(optional upgrade to <a href="https://sellcodes.com/CylMIdJD" target="_blank">premium</a>)</span></li>
    </ul>
    <div class="ci-install-button">
      <button class="ci-inisev-install-plugin" data-slug="cdp">Install plugin now</button>
      <span>(from <a href="https://wordpress.org/plugins/copy-delete-posts/" target="_blank">WP directory</a>)</span>
    </div>
  </div>
</div>
