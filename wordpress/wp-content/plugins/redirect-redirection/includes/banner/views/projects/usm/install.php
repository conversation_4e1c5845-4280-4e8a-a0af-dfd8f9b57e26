<?php

  // Namespace
  namespace Inisev\Subs;

  // Disallow direct access
  if (!defined('ABSPATH')) exit;

?>

<div class="ci-left-part ci-install-state">
  <div class="ci-project-logo">
    <a href="https://www.ultimatelysocial.com/usm-premium/" target="_blank">
      <img src="<?php $this->_asset('/projects/usm/imgs/big-colored-logo.png'); ?>">
    </a>
    <img src="<?php $this->_asset('/static/imgs/rating.svg'); ?>">
  </div>
  <div class="ci-install-column">
    <ul class="ci-checkmark-list ci-checkmark-list-type-1">
      <li>Show social media & sharing icons</li>
      <li>Maximize shares for more traffic</li>
      <li><b>Free</b> <span class="ci-light-font">(optional upgrade to <a href="https://www.ultimatelysocial.com/usm-premium/" target="_blank">premium</a>)</span></li>
    </ul>
    <div class="ci-install-button">
      <button class="ci-inisev-install-plugin" data-slug="usm">Install plugin now</button>
      <span>(from <a href="https://wordpress.org/plugins/ultimate-social-media-icons/" target="_blank">WP directory</a>)</span>
    </div>
  </div>
</div>
