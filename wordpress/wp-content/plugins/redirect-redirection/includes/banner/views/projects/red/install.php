<?php

  // Namespace
  namespace Inisev\Subs;

  // Disallow direct access
  if (!defined('ABSPATH')) exit;

?>

<img src="<?php $this->_asset('/projects/red/imgs/background-top-left.svg'); ?>" class="ci-background-top-left">
<div class="ci-left-part ci-install-state">

  <div class="ci-project-logo">
     <a href="https://wordpress.org/plugins/redirect-redirection/" target="_blank">
        <img class="ci-redi-logo-p" alt="Redirect Logo" src="<?php $this->_asset('/projects/red/imgs/big-colored-logo-rr.png'); ?>">
     </a>
     <img src="<?php $this->_asset('/static/imgs/rating.svg'); ?>">
  </div>

  <div class="ci-install-column">
     <ul class="ci-checkmark-list ci-checkmark-list-type-1">
        <li>Redirect URLs with one click</li>
        <li>Define redirection rules</li>
        <li><b>100% Free</b></li>
     </ul>
     <div class="ci-install-button">
        <button class="ci-inisev-install-plugin" data-slug="redi">Install plugin now</button>
        <span>(from <a href="https://wordpress.org/plugins/redirect-redirection/" target="_blank">WP directory</a>)</span>
     </div>
  </div>

</div>

<div class="ci-right-part">
  <img src="<?php $this->_asset('/projects/red/imgs/main-background-image.png'); ?>" class="ci-background-top-right">
</div>
