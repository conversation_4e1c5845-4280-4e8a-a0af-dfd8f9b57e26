<?php

  /**
   * Main renderer for the Carousel
   *
   * @category Child Plugin
   * <AUTHOR> <<EMAIL>>
   */

  // Namespace
  namespace Inisev\Subs;

  // Disallow direct access
  if (!defined('ABSPATH')) exit;
?>

<section class="ci-carrinis-wrapper">
  <section class="ci-carrinis" id="carrinis">
    <h1>Like this plugin?</h1>
    <h1>Then you'll LOVE our others too :)</h1>
    <div class="ci-carousel">
      <?php $this->_include('static/tabs'); ?>
      <div class="ci-project-content">
        <?php

          $bmi_free = $this->is_plugin_installed($this->bmi_slug);
          $bmi_pro = $this->is_plugin_installed($this->bmi_premium);
          $bmi_state = (!$bmi_free || (!$bmi_free && $bmi_pro)) ? 'install' : (($bmi_free && !$bmi_pro) ? 'upgrade' : 'already-installed')

        ?>
        <div class="ci-project ci-project-BackupMigration <?php echo 'ci-'.$bmi_state.'-state-visible'; ?>">
          <?php

            if (!$bmi_free || (!$bmi_free && $bmi_pro)) {
              $this->_include('projects/bmi/install');
            } elseif ($bmi_free && !$bmi_pro) {
              $this->_include('projects/bmi/upgrade');
            } elseif ($bmi_free && $bmi_pro) {
              $this->_include('projects/bmi/installed');
            }

          ?>
          <div class="ci-right-part">
            <img src="<?php $this->_asset('/projects/bmi/imgs/background-images.png'); ?>" class="ci-main-image">
          </div>
        </div>
        <?php

          $mpu_plugin = $this->is_plugin_installed($this->mpu_slug);
          $mpu_state = $mpu_plugin ? 'already-installed' : 'install';

        ?>
        <div class="ci-project ci-project-MyPopups <?php echo 'ci-'.$mpu_state.'-state-visible'; ?>">
          <?php

            if ($mpu_plugin) {
              $this->_include('projects/mpu/installed');
            } else {
              $this->_include('projects/mpu/install');
            }

          ?>
          <div class="ci-right-part">
            <img src="<?php $this->_asset('/projects/mpu/imgs/background-images.png'); ?>" class="ci-main-image">
            <img src="<?php $this->_asset('/projects/mpu/imgs/background-texture-green.png'); ?>" class="ci-secondary-image">
          </div>
        </div>
        <?php

          $cdp_free = $this->is_plugin_installed($this->cdp_slug);
          $cdp_pro = $this->is_plugin_installed($this->cdp_premium);
          $cdp_state = (!$cdp_free || (!$cdp_free && $cdp_pro)) ? 'install' : (($cdp_free && !$cdp_pro) ? 'upgrade' : 'already-installed');

        ?>
        <div class="ci-project ci-project-CopyDeletePosts <?php echo 'ci-'.$cdp_state.'-state-visible'; ?>">
          <?php

            if (!$cdp_free || (!$cdp_free && $cdp_pro)) {
              $this->_include('projects/cdp/install');
            } elseif ($cdp_free && !$cdp_pro) {
              $this->_include('projects/cdp/upgrade');
            } elseif ($cdp_free && $cdp_pro) {
              $this->_include('projects/cdp/installed');
            }

          ?>
          <div class="ci-right-part">
            <img src="<?php $this->_asset('/projects/cdp/imgs/secondary-background-image.svg'); ?>" class="ci-secondary-image">
            <img src="<?php $this->_asset('/projects/cdp/imgs/main-background-image.png'); ?>" class="ci-main-image">
          </div>
        </div>
        <?php

          $redi_plugin = $this->is_plugin_installed($this->redi_slug);
          $redi_state = $redi_plugin ? 'already-installed' : 'install';

        ?>
        <div class="ci-project ci-project-redRed ci-<?php echo $redi_state; ?>-state-visible">
          <?php
            if ($redi_state == 'install') {
              $this->_include('projects/red/install');
            } else {
              $this->_include('projects/red/installed');
            }
          ?>
        </div>
        <div class="ci-project ci-project-TasteWP ci-install-state-visible">
          <?php $this->_include('projects/twp/install'); ?>
          <div class="ci-right-part">
            <img src="<?php $this->_asset('/projects/twp/imgs/background-image-1.svg'); ?>">
            <img src="<?php $this->_asset('/projects/twp/imgs/background-image-2.svg'); ?>">
            <img src="<?php $this->_asset('/projects/twp/imgs/background-image-3.svg'); ?>">
          </div>
        </div>
        <?php

          $usm_free = $this->is_plugin_installed($this->usm_slug);
          $usm_pro = $this->is_plugin_installed($this->usm_premium);
          $usm_state = (!$usm_free || (!$usm_free && $usm_pro)) ? 'install' : (($usm_free && !$usm_pro) ? 'upgrade' : 'already-installed');

        ?>
        <div class="ci-project ci-project-SocialShare <?php echo 'ci-'.$usm_state.'-state-visible'; ?>">
          <?php

            if (!$usm_free || (!$usm_free && $usm_pro)) {
              $this->_include('projects/usm/install');
              $this->_include('projects/usm/part-install');
            } elseif ($usm_free && !$usm_pro) {
              $this->_include('projects/usm/upgrade');
              $this->_include('projects/usm/part-upgrade');
            } elseif ($usm_free && $usm_pro) {
              $this->_include('projects/usm/part-install');
              $this->_include('projects/usm/installed');
            }

          ?>
        </div>
        <div class="ci-project ci-project-followIt ci-install-state-visible">
          <?php $this->_include('projects/fit/install'); ?>
        </div>
      </div>
    </div>
    <div class="ci-all-projects">
      <a class="ci-see-all-projects" href="https://inisev.com/?utm_source=plugin_footer&utm_campaign=CDP&utm_medium=carrousel" target="_blank">See all projects</a>
    </div>
  </section>
</section>
